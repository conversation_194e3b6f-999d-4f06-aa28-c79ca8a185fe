package com.ruoyi.app.leave.service.impl;

import java.io.FileInputStream;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.google.common.collect.Lists;
import com.ruoyi.app.crane.domain.CraneDept;
import com.ruoyi.app.crane.domain.CraneUserDept;
import com.ruoyi.app.crane.enums.CraneRoleExcelEnum;
import com.ruoyi.app.leave.domain.LeaveDepartment;
import com.ruoyi.app.leave.domain.LeaveUserImport;
import com.ruoyi.app.leave.dto.LeaveUserPermitDto;
import com.ruoyi.app.leave.dto.LeaveUserQueryDto;
import com.ruoyi.app.leave.dto.LeaveUserSaveDto;
import com.ruoyi.app.leave.enums.LeaveFactoryRoleEnum;
import com.ruoyi.app.leave.enums.LeaveRoleEnum;
import com.ruoyi.app.leave.mapper.LeaveDepartmentMapper;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.domain.server.Sys;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeaveDeptAssignmentMapper;
import com.ruoyi.app.leave.domain.LeaveDeptAssignment;
import com.ruoyi.app.leave.service.ILeaveDeptAssignmentService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 出门证部门归属Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeaveDeptAssignmentServiceImpl implements ILeaveDeptAssignmentService {
    private static final Logger log = LoggerFactory.getLogger(LeaveDeptAssignmentServiceImpl.class);

    @Autowired
    private LeaveDeptAssignmentMapper leaveDeptAssignmentMapper;

    @Autowired
    private LeaveDepartmentMapper leaveDepartmentMapper;

    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private SysUserRoleMapper userRoleMapper;
    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    /**
     * 查询出门证部门归属
     *
     * @param id 出门证部门归属ID
     * @return 出门证部门归属
     */
    @Override
    public LeaveDeptAssignment selectLeaveDeptAssignmentById(Long id) {
        return leaveDeptAssignmentMapper.selectLeaveDeptAssignmentById(id);
    }

    /**
     * 查询出门证部门归属列表
     *
     * @param leaveDeptAssignment 出门证部门归属
     * @return 出门证部门归属
     */
    @Override
    public List<LeaveDeptAssignment> selectLeaveDeptAssignmentList(LeaveDeptAssignment leaveDeptAssignment) {
        return leaveDeptAssignmentMapper.selectLeaveDeptAssignmentList(leaveDeptAssignment);
    }

    /**
     * 新增出门证部门归属
     *
     * @param leaveDeptAssignment 出门证部门归属
     * @return 结果
     */
    @Override
    public int insertLeaveDeptAssignment(LeaveDeptAssignment leaveDeptAssignment) {
        leaveDeptAssignment.setCreateTime(DateUtils.getNowDate());
        return leaveDeptAssignmentMapper.insertLeaveDeptAssignment(leaveDeptAssignment);
    }

    /**
     * 修改出门证部门归属
     *
     * @param leaveDeptAssignment 出门证部门归属
     * @return 结果
     */
    @Override
    public int updateLeaveDeptAssignment(LeaveDeptAssignment leaveDeptAssignment) {
        leaveDeptAssignment.setUpdateTime(DateUtils.getNowDate());
        return leaveDeptAssignmentMapper.updateLeaveDeptAssignment(leaveDeptAssignment);
    }

    /**
     * 批量删除出门证部门归属
     *
     * @param ids 需要删除的出门证部门归属ID
     * @return 结果
     */
    @Override
    public int deleteLeaveDeptAssignmentByIds(Long[] ids) {
        return leaveDeptAssignmentMapper.deleteLeaveDeptAssignmentByIds(ids);
    }

    /**
     * 删除出门证部门归属信息
     *
     * @param id 出门证部门归属ID
     * @return 结果
     */
    @Override
    public int deleteLeaveDeptAssignmentById(Long id) {
        return leaveDeptAssignmentMapper.deleteLeaveDeptAssignmentById(id);
    }

    @Override
    public List<LeaveUserPermitDto> getHasPermitUserList(LeaveUserQueryDto queryDto) {
        List<LeaveUserPermitDto> list = leaveDeptAssignmentMapper.selectLeaveUserList(queryDto);
        List<String> roleList = LeaveFactoryRoleEnum.getCodeList();
        //各用户角色描述
        for (String roleKey : roleList) {
            List<String> hasPermitUserNos = sysUserRoleMapper.selectUserNamesByRoleKeyList(Lists.newArrayList(roleKey));
            for (LeaveUserPermitDto userPermitDto : list) {
                if (hasPermitUserNos.contains(userPermitDto.getUserName())) {
                    userPermitDto.setRoleNamesDesc(StringUtils.defaultString(userPermitDto.getRoleNamesDesc()) + LeaveFactoryRoleEnum.getDescByCode(roleKey) + "、");
                }
            }
        }
        //去除多余的分隔符
        for (LeaveUserPermitDto handleUser : list) {
            if (handleUser.getRoleNamesDesc() != null && !handleUser.getRoleNamesDesc().isEmpty()) {
                handleUser.setRoleNamesDesc(handleUser.getRoleNamesDesc().substring(0, handleUser.getRoleNamesDesc().length() - 1));
            }
        }
        return list;
    }

    @Override
    public LeaveUserPermitDto getUserInfo(LeaveUserQueryDto queryDto) {
        List<LeaveUserPermitDto> list = leaveDeptAssignmentMapper.selectLeaveUserList(queryDto);
        if (CollectionUtils.isEmpty(list) || list.size() != 1) {
            return null;
        }
        LeaveUserPermitDto userInfo = list.get(0);
        List<String> userRoleKeys = Lists.newArrayList();
        List<String> roleList = LeaveFactoryRoleEnum.getCodeList();
        //各用户角色描述
        for (String roleKey : roleList) {
            List<String> hasPermitUserNos = sysUserRoleMapper.selectUserNamesByRoleKeyList(Lists.newArrayList(roleKey));
            if (hasPermitUserNos.contains(userInfo.getUserName())) {
                userRoleKeys.add(roleKey);
            }
        }
        userInfo.setRoleKeys(userRoleKeys);
        return userInfo;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(LeaveUserSaveDto userDto) {
        Date nowTime = DateUtils.getNowDate();
        //1.用户部门
        //1.1删除用户与部门绑定关系
        LeaveDeptAssignment deleteDto = new LeaveDeptAssignment();
        deleteDto.setUserId(userDto.getUserId());
        deleteDto.setDelFlag(1);
        deleteDto.setUpdateTime(nowTime);
        deleteDto.setUpdateBy(userDto.getHandleNo());
        leaveDeptAssignmentMapper.deleteLeaveDeptAssignmentByUserId(deleteDto);
        //1.2添加用户所属部门
        LeaveDeptAssignment saveDto = new LeaveDeptAssignment();
        saveDto.setUserId(userDto.getUserId());
        saveDto.setDeptId(userDto.getDeptId());
        saveDto.setCreateTime(nowTime);
        saveDto.setCreateBy(userDto.getHandleNo());
        leaveDeptAssignmentMapper.insertLeaveDeptAssignment(saveDto);

        //2.用户角色
        //2.1删除用户与角色绑定关系
        roleService.deleteAuthUsers(Lists.newArrayList(LeaveFactoryRoleEnum.getCodeList()), Lists.newArrayList(userDto.getUserId()).toArray(new Long[1]));
        //2.2添加用户角色
        roleService.insertAuthUsers(userDto.getRoleKeys(), Lists.newArrayList(userDto.getUserId()).toArray(new Long[1]));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(LeaveUserSaveDto userDto) {
        Date nowTime = DateUtils.getNowDate();
        //1.删除用户与部门绑定关系
        LeaveDeptAssignment deleteDto = new LeaveDeptAssignment();
        deleteDto.setUserId(userDto.getUserId());
        deleteDto.setDelFlag(1);
        deleteDto.setUpdateTime(nowTime);
        deleteDto.setUpdateBy(userDto.getHandleNo());
        leaveDeptAssignmentMapper.deleteLeaveDeptAssignmentByUserId(deleteDto);
        //2.删除用户与角色绑定关系
        roleService.deleteAuthUsers(Lists.newArrayList(LeaveFactoryRoleEnum.getCodeList()), Lists.newArrayList(userDto.getUserId()).toArray(new Long[1]));
    }

    @Override
    public void assignUserRole() throws Exception {
        ExcelUtil<LeaveUserImport> util = new ExcelUtil<>(LeaveUserImport.class);

        List<LeaveUserImport> leaveUserList = util.importExcel(new FileInputStream("D:\\export\\test.xlsx"));
        //预处理
        this.preHandle(leaveUserList);

        List<String> errorList = Lists.newArrayList();
        List<String> successList = Lists.newArrayList();
        for (LeaveUserImport leaveUser : leaveUserList) {
            this.initUser(leaveUser, errorList, successList);
        }

        if (!CollectionUtils.isEmpty(successList)) {
            for (String msg : successList) {
                log.info(msg);
            }
        }

        if (!CollectionUtils.isEmpty(errorList)) {
            for (String error : errorList) {
                log.error(error);
            }
        }
    }

    private void preHandle(List<LeaveUserImport> leaveUserList) {
        for (LeaveUserImport leaveUser : leaveUserList) {
            if (!leaveUser.getWorkNo().startsWith("X") && !leaveUser.getWorkNo().startsWith("G") && !leaveUser.getWorkNo().startsWith("S")) {
                leaveUser.setWorkNo("X" + leaveUser.getWorkNo());
            }
        }
    }

    private void initUser(LeaveUserImport leaveUser, List<String> errorList, List<String> successList) {
        try {
            SysUser sysUser = userMapper.selectUserByUserName(leaveUser.getWorkNo());
            if (Objects.isNull(sysUser)) {
                errorList.add(leaveUser.getWorkNo() + " " + leaveUser.getNickName() + "工号不存在");
                return;
            }

            //更新账号状态
            if (!"0".equals(sysUser.getStatus())) {
                sysUser.setStatus("0");
                userMapper.updateUser(sysUser);
            }

            //绑定角色
            SysRole sysRole = roleMapper.checkRoleKeyUnique(LeaveRoleEnum.getCodeByDesc(leaveUser.getRoleKey()));
            if (Objects.isNull(sysRole)) {
                errorList.add(leaveUser.getWorkNo() + " " + leaveUser.getNickName() + "权限不存在");
                return;
            } else {
                //授权
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUser.getUserId());
                sysUserRole.setRoleId(sysRole.getRoleId());
                userRoleMapper.deleteUserRoleInfo(sysUserRole);
                userRoleMapper.batchUserRole(Lists.newArrayList(sysUserRole));
            }

            //绑定部门
            Date nowTime = DateUtils.getNowDate();
            LeaveDepartment leaveDepartmentCondition = new LeaveDepartment();
            leaveDepartmentCondition.setStoreName(leaveUser.getDeptName());
            List<LeaveDepartment> departmentList = leaveDepartmentMapper.selectLeaveDepartmentByDeptName(leaveDepartmentCondition);
            if (!CollectionUtils.isEmpty(departmentList)) {
                if (departmentList.size() > 1) {
                    errorList.add(leaveUser.getWorkNo() + " " + leaveUser.getNickName() + " 存在相同名称部门 " + leaveUser.getDeptName());
                    return;
                }
                //删除用户与部门绑定关系
                LeaveDeptAssignment deleteDto = new LeaveDeptAssignment();
                deleteDto.setUserId(sysUser.getUserId());
                deleteDto.setDelFlag(1);
                deleteDto.setUpdateTime(nowTime);
                deleteDto.setUpdateBy("admin");
                leaveDeptAssignmentMapper.deleteLeaveDeptAssignmentByUserId(deleteDto);

                LeaveDepartment department = departmentList.get(0);
                LeaveDeptAssignment leaveDeptAssignment = new LeaveDeptAssignment();
                leaveDeptAssignment.setUserId(sysUser.getUserId());
                leaveDeptAssignment.setDeptId(department.getId());
                leaveDeptAssignment.setCreateTime(nowTime);
                leaveDeptAssignment.setCreateBy("admin");
                leaveDeptAssignmentMapper.insertLeaveDeptAssignment(leaveDeptAssignment);
            } else {
                errorList.add(leaveUser.getWorkNo() + " " + leaveUser.getNickName() + "部门不存在");
                return;
            }
            successList.add(leaveUser.getWorkNo() + " " + leaveUser.getNickName() + "绑定成功");

        } catch (Exception e) {
            e.printStackTrace();
            errorList.add(leaveUser.getWorkNo() + " " + leaveUser.getNickName() + "发生异常");
        }
    }
}
