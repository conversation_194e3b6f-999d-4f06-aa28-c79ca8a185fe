package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LeavePushRecord;

/**
 * 出门证推送记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface ILeavePushRecordService 
{
    /**
     * 查询出门证推送记录
     * 
     * @param id 出门证推送记录ID
     * @return 出门证推送记录
     */
    public LeavePushRecord selectLeavePushRecordById(Long id);

    /**
     * 查询出门证推送记录列表
     * 
     * @param leavePushRecord 出门证推送记录
     * @return 出门证推送记录集合
     */
    public List<LeavePushRecord> selectLeavePushRecordList(LeavePushRecord leavePushRecord);

    /**
     * 新增出门证推送记录
     * 
     * @param leavePushRecord 出门证推送记录
     * @return 结果
     */
    public int insertLeavePushRecord(LeavePushRecord leavePushRecord);

    /**
     * 修改出门证推送记录
     * 
     * @param leavePushRecord 出门证推送记录
     * @return 结果
     */
    public int updateLeavePushRecord(LeavePushRecord leavePushRecord);

    /**
     * 批量删除出门证推送记录
     * 
     * @param ids 需要删除的出门证推送记录ID
     * @return 结果
     */
    public int deleteLeavePushRecordByIds(Long[] ids);

    /**
     * 删除出门证推送记录信息
     * 
     * @param id 出门证推送记录ID
     * @return 结果
     */
    public int deleteLeavePushRecordById(Long id);

    /**
     * 查询待重试的推送记录
     * 
     * @return 待重试的推送记录列表
     */
    public List<LeavePushRecord> selectRetryPushRecords();

    /**
     * 记录推送失败
     * 
     * @param businessNo 业务单号
     * @param type 业务类型
     * @param failReason 失败原因
     * @return 结果
     */
    public int recordPushFailure(String businessNo, Integer type, String failReason);

    /**
     * 更新推送状态
     * 
     * @param id 记录ID
     * @param pushStatus 推送状态
     * @return 结果
     */
    public int updatePushStatus(Long id, Integer pushStatus);
} 