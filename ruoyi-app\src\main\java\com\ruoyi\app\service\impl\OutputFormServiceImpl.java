package com.ruoyi.app.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.app.domain.XcerpForm;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.mapper.OutputFormMapper;
import com.ruoyi.app.domain.OutputForm;
import com.ruoyi.app.service.IOutputFormService;

/**
 * 产量生产报表（生产指挥中心）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-04-21
 */
@Service
public class OutputFormServiceImpl implements IOutputFormService 
{
    @Autowired
    private OutputFormMapper outputFormMapper;

    /**
     * 查询产量生产报表（生产指挥中心）
     * 

     * @return 产量生产报表（生产指挥中心）
     */
    @Override
    @DataSource(value = DataSourceType.XCPRODNEW)
    public List<Map<String, Object>> selectOutputFormByItemName(OutputForm outputForm)
    {

        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        List<OutputForm>  stocklist= outputFormMapper.selectOutputFormByItemName(outputForm);

        for (OutputForm item: stocklist) {
            Map<String, Object> map = new HashMap<>();
            item.setFinishPer(item.getFinishPer().setScale(2, BigDecimal.ROUND_HALF_UP));
            map.put("itemName", item.getItemName());
            map.put("monthPlanProd", item.getMonthPlanProd());
            map.put("dayWeight", item.getDayWeight());
            map.put("totalWeight", item.getTotalWeight());
            map.put("finishPer", item.getFinishPer());
            map.put("dateCode", item.getDateCode());


            result.add(map);
        }
        return result;
    }

    /**
     * 查询产量生产报表（生产指挥中心）列表
     * 
     * @param outputForm 产量生产报表（生产指挥中心）
     * @return 产量生产报表（生产指挥中心）
     */
    @Override
    @DataSource(value = DataSourceType.XCPRODNEW)
    public List<Map<String, Object>>  selectOutputFormList(OutputForm outputForm)
    {

        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        List<OutputForm>  stocklist= outputFormMapper.selectOutputFormList(outputForm);

        for (OutputForm item: stocklist) {
            Map<String, Object> map = new HashMap<>();
            item.setFinishPer(item.getFinishPer().setScale(2, BigDecimal.ROUND_HALF_UP));
            map.put("itemName", item.getItemName());
            map.put("monthPlanProd", item.getMonthPlanProd());
            map.put("dayWeight", item.getDayWeight());
            map.put("totalWeight", item.getTotalWeight());
            map.put("finishPer", item.getFinishPer());
            map.put("dateCode", item.getDateCode());


            result.add(map);
        }
        return result;
    }


}
