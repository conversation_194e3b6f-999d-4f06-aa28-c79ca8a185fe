package com.ruoyi.web.controller.leave;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LPassItemT;
import com.ruoyi.app.leave.service.ILPassItemTService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 不返回物资明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@RestController
@RequestMapping("/web/leave/passItem")
public class WebLPassItemTController extends BaseController
{
    @Autowired
    private ILPassItemTService lPassItemTService;

    /**
     * 查询不返回物资明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LPassItemT lPassItemT)
    {
        startPage();
        List<LPassItemT> list = lPassItemTService.selectLPassItemTList(lPassItemT);
        return getDataTable(list);
    }

    /**
     * 导出不返回物资明细列表
     */
    @Log(title = "不返回物资明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LPassItemT lPassItemT)
    {
        List<LPassItemT> list = lPassItemTService.selectLPassItemTList(lPassItemT);
        ExcelUtil<LPassItemT> util = new ExcelUtil<LPassItemT>(LPassItemT.class);
        return util.exportExcel(list, "passItem");
    }

    /**
     * 获取不返回物资明细详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lPassItemTService.selectLPassItemTById(id));
    }

    /**
     * 新增不返回物资明细
     */
    @Log(title = "不返回物资明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LPassItemT lPassItemT)
    {
        return toAjax(lPassItemTService.insertLPassItemT(lPassItemT));
    }

    /**
     * 修改不返回物资明细
     */
    @Log(title = "不返回物资明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LPassItemT lPassItemT)
    {
        return toAjax(lPassItemTService.updateLPassItemT(lPassItemT));
    }

    /**
     * 删除不返回物资明细
     */
    @Log(title = "不返回物资明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lPassItemTService.deleteLPassItemTByIds(ids));
    }
}
