package com.ruoyi.app.leave.enums;

/**
 * 类型 1-同步计量 2-同步智慧物流 3-同步智慧安保
 * 属性 code desc
 */
public enum LeavePushTaskTypeEnum {
    SYNC_METRIC(1, "同步计量"),
    SYNC_LOGISTICS(2, "同步智慧物流"),
    SYNC_SECURITY(3, "同步智慧安保");

    private final int code;
    private final String desc;

    LeavePushTaskTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static LeavePushTaskTypeEnum getByCode(int code) {
        for (LeavePushTaskTypeEnum type : LeavePushTaskTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

    public static String getDescByCode(int code) {
        LeavePushTaskTypeEnum type = getByCode(code);
        return type != null ? type.getDesc() : null;
    }
}
