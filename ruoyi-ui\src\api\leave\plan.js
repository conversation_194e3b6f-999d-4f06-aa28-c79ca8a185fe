import request from '@/utils/request'

// 查询出门证计划申请列表
export function listPlan(query) {
  return request({
    url: '/web/leave/plan/list',
    method: 'get',
    params: query
  })
}

// 查询出门证计划申请详细
export function getPlan(id) {
  return request({
    url: '/web/leave/plan/' + id,
    method: 'get'
  })
}

// 查询出门证计划申请详情
export function detailPlan(applyNo) {
  return request({
    url: '/web/leave/plan/detail/' + applyNo,
    method: 'get'
  })
}

// 新增出门证计划申请
export function addPlan(data) {
  return request({
    url: '/web/leave/plan',
    method: 'post',
    data: data
  })
}

// 修改出门证计划申请
export function updatePlan(data) {
  return request({
    url: '/web/leave/plan',
    method: 'put',
    data: data
  })
}

// 删除出门证计划申请
export function delPlan(id) {
  return request({
    url: '/web/leave/plan/' + id,
    method: 'delete'
  })
}

// 导出出门证计划申请
export function exportPlan(query) {
  return request({
    url: '/web/leave/plan/export',
    method: 'get',
    params: query
  })
}

// 通过（驳回）审核
export function approve(data) {
  return request({
    url: '/web/leave/plan/approve',
    method: 'post',
    data: data
  })
}

// 废弃
export function discard(data) {
  return request({
    url: '/web/leave/plan/discard',
    method: 'post',
    data: data
  })
}

/** 导入物资列表 */
export function importMaterialList(data) {
  return request({
    url: '/web/leave/plan/importMaterialList',
    method: 'post',
    data: data
  })
}

// 导出物资模板
export function exportMaterialTemplate() {
  return request({
    url: '/web/leave/plan/exportMaterialTemplate',
    method: 'get'
  })
}

// 查询任务物资列表
export function listTaskMaterial(query) {
  return request({
    url: '/web/leave/plan/listTaskMaterial',
    method: 'get',
    params: query
  })
}

// 物资确认
export function confirmMaterial(data) {
  return request({
    url: '/web/leave/plan/confirmMaterial',
    method: 'post',
    data: data
  })
}

// // 是否允许派车
// export function isAllowPatch(data) {
//   return request({
//     url: '/web/leave/plan/isAllowPatch',
//     method: 'post',
//     data: data
//   })
// }

