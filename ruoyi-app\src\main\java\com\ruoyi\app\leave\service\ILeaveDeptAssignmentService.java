package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LeaveDeptAssignment;
import com.ruoyi.app.leave.dto.LeaveUserPermitDto;
import com.ruoyi.app.leave.dto.LeaveUserQueryDto;
import com.ruoyi.app.leave.dto.LeaveUserSaveDto;
import com.ruoyi.common.core.domain.entity.SysUser;

/**
 * 出门证部门归属Service接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ILeaveDeptAssignmentService
{
    /**
     * 查询出门证部门归属
     *
     * @param id 出门证部门归属ID
     * @return 出门证部门归属
     */
    public LeaveDeptAssignment selectLeaveDeptAssignmentById(Long id);

    /**
     * 查询出门证部门归属列表
     *
     * @param leaveDeptAssignment 出门证部门归属
     * @return 出门证部门归属集合
     */
    public List<LeaveDeptAssignment> selectLeaveDeptAssignmentList(LeaveDeptAssignment leaveDeptAssignment);

    /**
     * 新增出门证部门归属
     *
     * @param leaveDeptAssignment 出门证部门归属
     * @return 结果
     */
    public int insertLeaveDeptAssignment(LeaveDeptAssignment leaveDeptAssignment);

    /**
     * 修改出门证部门归属
     *
     * @param leaveDeptAssignment 出门证部门归属
     * @return 结果
     */
    public int updateLeaveDeptAssignment(LeaveDeptAssignment leaveDeptAssignment);

    /**
     * 批量删除出门证部门归属
     *
     * @param ids 需要删除的出门证部门归属ID
     * @return 结果
     */
    public int deleteLeaveDeptAssignmentByIds(Long[] ids);

    /**
     * 删除出门证部门归属信息
     *
     * @param id 出门证部门归属ID
     * @return 结果
     */
    public int deleteLeaveDeptAssignmentById(Long id);

    /**
     * 获取已分配权限用户
     *
     * @param
     * @return
     */
    List<LeaveUserPermitDto> getHasPermitUserList(LeaveUserQueryDto queryDto);


    /**
     *
     *
     * @param
     * @return
     */
    LeaveUserPermitDto getUserInfo(LeaveUserQueryDto queryDto);

    /**
     * 用户部门权限分配
     *
     * @param userDto
     */
    public void save(LeaveUserSaveDto userDto);

    /**
     * 用户部门权限删除
     *
     * @param userDto
     */
    public void delete(LeaveUserSaveDto userDto);

    /**
     * 批量分配人员权限
     */
    void assignUserRole() throws Exception;
}
