package com.ruoyi.app.leave.service.impl;

import java.util.Date;
import java.util.List;

import com.ruoyi.app.leave.domain.*;
import com.ruoyi.app.leave.mapper.DicMapper;
import com.ruoyi.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.StoreoutBfhCkMapper;
import com.ruoyi.app.leave.service.IStoreoutBfhCkService;

/**
 * 备发货出库Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class StoreoutBfhCkServiceImpl implements IStoreoutBfhCkService 
{
    @Autowired
    private StoreoutBfhCkMapper storeoutBfhCkMapper;

    @Autowired
    private DicMapper dicMapper;

    private static final Logger log = LoggerFactory.getLogger(StoreoutBfhCkServiceImpl.class);

    /**
     * 查询备发货出库
     * 
     * @param id 备发货出库主键
     * @return 备发货出库
     */
    @Override
    public StoreoutBfhCkMeasure selectStoreoutBfhCkById(Long id)
    {
        return storeoutBfhCkMapper.selectStoreoutBfhCkById(id);
    }

    /**
     * 查询备发货出库列表
     * 
     * @param storeoutBfhCkMeasure 备发货出库
     * @return 备发货出库
     */
    @Override
    public List<StoreoutBfhCkMeasure> selectStoreoutBfhCkList(StoreoutBfhCkMeasure storeoutBfhCkMeasure)
    {
        return storeoutBfhCkMapper.selectStoreoutBfhCkList(storeoutBfhCkMeasure);
    }

    /**
     * 新增备发货出库
     * 
     * @param storeoutBfhCkMeasure 备发货出库
     * @return 结果
     */
    @Override
    public int insertStoreoutBfhCk(StoreoutBfhCkMeasure storeoutBfhCkMeasure)
    {
        return storeoutBfhCkMapper.insertStoreoutBfhCk(storeoutBfhCkMeasure);
    }

    /**
     * 修改备发货出库
     * 
     * @param storeoutBfhCkMeasure 备发货出库
     * @return 结果
     */
    @Override
    public int updateStoreoutBfhCk(StoreoutBfhCkMeasure storeoutBfhCkMeasure)
    {
        return storeoutBfhCkMapper.updateStoreoutBfhCk(storeoutBfhCkMeasure);
    }

    /**
     * 批量删除备发货出库
     * 
     * @param ids 需要删除的备发货出库主键
     * @return 结果
     */
    @Override
    public int deleteStoreoutBfhCkByIds(Long[] ids)
    {
        return storeoutBfhCkMapper.deleteStoreoutBfhCkByIds(ids);
    }

    /**
     * 删除备发货出库信息
     * 
     * @param id 备发货出库主键
     * @return 结果
     */
    @Override
    public int deleteStoreoutBfhCkById(Long id)
    {
        return storeoutBfhCkMapper.deleteStoreoutBfhCkById(id);
    }

    /**
     * 根据匹配ID删除备发货出库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @Override
    public int deleteStoreoutBfhCkByMatchid(String matchid)
    {
        return storeoutBfhCkMapper.deleteStoreoutBfhCkByMatchid(matchid);
    }

    @Override
    public void handleunReturnStockOut(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial) {
        DicMeasure dicMeasure = new DicMeasure();

        dicMeasure.setCarno(leaveTask.getCarNum());
        dicMeasure.setOperatype(12L);
        dicMeasure.setPlanid(leavePlan.getPlanNo());
        dicMeasure.setMaterialname(leaveTaskMaterial.getMaterialName());
        dicMeasure.setMaterialspec(leaveTaskMaterial.getMaterialSpec());
        dicMeasure.setSourcename(leavePlan.getSourceCompany());
        dicMeasure.setTargetname(leavePlan.getReceiveCompany());
        dicMeasure.setSourcetime(nowDate);
        dicMeasure.setMflag("2");
        dicMeasure.setShflag("0");
        dicMeasure.setPlancount(leavePlan.getPlannedAmount());
        dicMeasure.setMsrmemo("炉号：" + leaveTask.getStockOutHeatNo() + "钢种：" + leaveTask.getStockOutSteelGrade() + "规格：" + leaveTask.getStockOutSpec1Length() + "数量：" + leaveTask.getStockOutTotals());
        dicMeasure.setMatno(leaveTask.getStockOutTotals());
        dicMeasure.setZoushu(leaveTask.getStockOutAxles());
        dicMapper.updateDicByCarNo(dicMeasure);

        StoreoutBfhCkMeasure storeoutBfhCkMeasure = new StoreoutBfhCkMeasure();
        storeoutBfhCkMeasure.setValidflag(1L);
        storeoutBfhCkMeasure.setCarno(leaveTask.getCarNum());
        storeoutBfhCkMeasure.setOperatype("12");
        storeoutBfhCkMeasure.setPlanid(leavePlan.getPlanNo());
        storeoutBfhCkMeasure.setStorename(leavePlan.getSourceCompany());
        storeoutBfhCkMeasure.setTargetname(leavePlan.getReceiveCompany());
        storeoutBfhCkMeasure.setMaterialname(leaveTaskMaterial.getMaterialName());
        storeoutBfhCkMeasure.setMaterialspec(leaveTaskMaterial.getMaterialSpec());
        storeoutBfhCkMeasure.setCreateman(SecurityUtils.getLoginUser().getUser().getNickName());
        storeoutBfhCkMeasure.setCreatedate(nowDate);
        storeoutBfhCkMeasure.setHeatno(leaveTask.getStockOutHeatNo());
        storeoutBfhCkMeasure.setSgsign(leaveTask.getStockOutSteelGrade());
        storeoutBfhCkMeasure.setSpec(leaveTask.getStockOutSpec1Length() != null ? leaveTask.getStockOutSpec1Length().toString() : null); // 规格
        storeoutBfhCkMeasure.setMatno(leaveTask.getStockOutTotals());

        // 根据车号获取matchid
        DicMeasure dicQuery = new DicMeasure();
        dicQuery.setCarno(leaveTask.getCarNum());
        List<DicMeasure> dicMeasures = dicMapper.selectDicList(dicQuery);
        if (dicMeasures.size() == 1 && dicMeasures.get(0) != null) {
            storeoutBfhCkMeasure.setMatchid(dicMeasures.get(0).getMatchid());
        } else {
            log.error("StoreoutBfhCkServiceImpl.handleunReturnStockOut  不返回出库对接计量系统异常");
        }

        int i = storeoutBfhCkMapper.insertStoreoutBfhCk(storeoutBfhCkMeasure);
        if (i < 0) {
            log.error("StoreoutWwjgServiceImpl.handleExternalProcessingStockOut 委外加工出库对接计量系统异常");
        }


    }
} 