package com.ruoyi.web.controller.leave;

import com.ruoyi.app.leave.domain.LeaveTaskMaterial;
import com.ruoyi.app.leave.service.ILeaveTaskMaterialService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 出门证任务物资Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/web/leave/taskMaterial")
public class WebLeaveTaskMaterialController extends BaseController
{
    @Autowired
    private ILeaveTaskMaterialService leaveTaskMaterialService;

    /**
     * 查询出门证任务物资列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeaveTaskMaterial leaveTaskMaterial)
    {
        startPage();
        List<LeaveTaskMaterial> list = leaveTaskMaterialService.selectLeaveTaskMaterialList(leaveTaskMaterial);
        return getDataTable(list);
    }

    /**
     * 导出出门证任务物资列表
     */
    @Log(title = "出门证任务物资", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveTaskMaterial leaveTaskMaterial)
    {
        List<LeaveTaskMaterial> list = leaveTaskMaterialService.selectLeaveTaskMaterialList(leaveTaskMaterial);
        ExcelUtil<LeaveTaskMaterial> util = new ExcelUtil<LeaveTaskMaterial>(LeaveTaskMaterial.class);
        return util.exportExcel(list, "material");
    }

    /**
     * 获取出门证任务物资详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveTaskMaterialService.selectLeaveTaskMaterialById(id));
    }

    /**
     * 新增出门证任务物资
     */
    @Log(title = "出门证任务物资", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveTaskMaterial leaveTaskMaterial)
    {
        return toAjax(leaveTaskMaterialService.insertLeaveTaskMaterial(leaveTaskMaterial));
    }

    /**
     * 修改出门证任务物资
     */
    @Log(title = "出门证任务物资", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveTaskMaterial leaveTaskMaterial)
    {
        return toAjax(leaveTaskMaterialService.updateLeaveTaskMaterial(leaveTaskMaterial));
    }

    /**
     * 删除出门证任务物资
     */
    @Log(title = "出门证任务物资", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveTaskMaterialService.deleteLeaveTaskMaterialByIds(ids));
    }
}
