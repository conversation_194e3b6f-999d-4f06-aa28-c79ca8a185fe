package com.ruoyi.app.leave.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.StoreinWwjgMeasure;
import com.ruoyi.app.leave.service.IStoreinWwjgService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 外委加工入库Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/leave/storeinwwjg")
public class StoreinWwjgController extends BaseController
{
    @Autowired
    private IStoreinWwjgService storeinWwjgService;

    /**
     * 查询外委加工入库列表
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinwwjg:list')")
    @GetMapping("/list")
    public TableDataInfo list(StoreinWwjgMeasure storeinWwjgMeasure)
    {
        startPage();
        List<StoreinWwjgMeasure> list = storeinWwjgService.selectStoreinWwjgList(storeinWwjgMeasure);
        return getDataTable(list);
    }

    /**
     * 导出外委加工入库列表
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinwwjg:export')")
    @Log(title = "外委加工入库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StoreinWwjgMeasure storeinWwjgMeasure)
    {
        List<StoreinWwjgMeasure> list = storeinWwjgService.selectStoreinWwjgList(storeinWwjgMeasure);
        ExcelUtil<StoreinWwjgMeasure> util = new ExcelUtil<StoreinWwjgMeasure>(StoreinWwjgMeasure.class);
        util.exportEasyExcel(list, "外委加工入库数据", StoreinWwjgMeasure.class);
    }

    /**
     * 获取外委加工入库详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinwwjg:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeinWwjgService.selectStoreinWwjgById(id));
    }

    /**
     * 新增外委加工入库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinwwjg:add')")
    @Log(title = "外委加工入库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StoreinWwjgMeasure storeinWwjgMeasure)
    {
        return toAjax(storeinWwjgService.insertStoreinWwjg(storeinWwjgMeasure));
    }

    /**
     * 修改外委加工入库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinwwjg:edit')")
    @Log(title = "外委加工入库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StoreinWwjgMeasure storeinWwjgMeasure)
    {
        return toAjax(storeinWwjgService.updateStoreinWwjg(storeinWwjgMeasure));
    }

    /**
     * 删除外委加工入库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinwwjg:remove')")
    @Log(title = "外委加工入库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(storeinWwjgService.deleteStoreinWwjgByIds(ids));
    }

    /**
     * 根据匹配ID删除外委加工入库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinwwjg:remove')")
    @Log(title = "外委加工入库", businessType = BusinessType.DELETE)
    @DeleteMapping("/matchid/{matchid}")
    public AjaxResult removeByMatchid(@PathVariable String matchid)
    {
        return toAjax(storeinWwjgService.deleteStoreinWwjgByMatchid(matchid));
    }
} 