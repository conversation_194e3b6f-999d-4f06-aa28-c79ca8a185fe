<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.StoreoutBfhCkMapper">
    
    <resultMap type="StoreoutBfhCkMeasure" id="StoreoutBfhCkResult">
        <result property="id"    column="ID"    />
        <result property="validflag"    column="VALIDFLAG"    />
        <result property="matchid"    column="MATCHID"    />
        <result property="carno"    column="CARNO"    />
        <result property="icno"    column="ICNO"    />
        <result property="operatype"    column="OPERATYPE"    />
        <result property="planid"    column="PLANID"    />
        <result property="storecode"    column="STORECODE"    />
        <result property="storename"    column="STORENAME"    />
        <result property="storepos"    column="STOREPOS"    />
        <result property="targetcode"    column="TARGETCODE"    />
        <result property="targetname"    column="TARGETNAME"    />
        <result property="materialcode"    column="MATERIALCODE"    />
        <result property="materialname"    column="MATERIALNAME"    />
        <result property="materialspeccode"    column="MATERIALSPECCODE"    />
        <result property="materialspec"    column="MATERIALSPEC"    />
        <result property="weight"    column="WEIGHT"    />
        <result property="counts"    column="COUNTS"    />
        <result property="transitway"    column="TRANSITWAY"    />
        <result property="memo"    column="MEMO"    />
        <result property="createman"    column="CREATEMAN"    />
        <result property="createdate"    column="CREATEDATE"    />
        <result property="updatedate"    column="UPDATEDATE"    />
        <result property="updateman"    column="UPDATEMAN"    />
        <result property="heatno"    column="HEATNO"    />
        <result property="sgsign"    column="SGSIGN"    />
        <result property="spec"    column="SPEC"    />
        <result property="matno"    column="MATNO"    />
        <result property="gbhao"    column="GBHAO"    />
    </resultMap>

    <sql id="selectStoreoutBfhCkVo">
        select ID, VALIDFLAG, MATCHID, CARNO, ICNO, OPERATYPE, PLANID, STORECODE, STORENAME, STOREPOS, TARGETCODE, TARGETNAME, MATERIALCODE, MATERIALNAME, MATERIALSPECCODE, MATERIALSPEC, WEIGHT, COUNTS, TRANSITWAY, MEMO, CREATEMAN, CREATEDATE, UPDATEDATE, UPDATEMAN, HEATNO, SGSIGN, SPEC, MATNO, GBHAO from L_STOREOUT_BFH_CK_T
    </sql>

    <select id="selectStoreoutBfhCkList" parameterType="StoreoutBfhCkMeasure" resultMap="StoreoutBfhCkResult">
        <include refid="selectStoreoutBfhCkVo"/>
        <where>  
            <if test="validflag != null "> and VALIDFLAG = #{validflag}</if>
            <if test="matchid != null  and matchid != ''"> and MATCHID = #{matchid}</if>
            <if test="carno != null  and carno != ''"> and CARNO = #{carno}</if>
            <if test="icno != null  and icno != ''"> and ICNO = #{icno}</if>
            <if test="operatype != null  and operatype != ''"> and OPERATYPE = #{operatype}</if>
            <if test="planid != null  and planid != ''"> and PLANID = #{planid}</if>
            <if test="storecode != null  and storecode != ''"> and STORECODE = #{storecode}</if>
            <if test="storename != null  and storename != ''"> and STORENAME like concat('%', #{storename}, '%')</if>
            <if test="storepos != null  and storepos != ''"> and STOREPOS = #{storepos}</if>
            <if test="targetcode != null  and targetcode != ''"> and TARGETCODE = #{targetcode}</if>
            <if test="targetname != null  and targetname != ''"> and TARGETNAME like concat('%', #{targetname}, '%')</if>
            <if test="materialcode != null  and materialcode != ''"> and MATERIALCODE = #{materialcode}</if>
            <if test="materialname != null  and materialname != ''"> and MATERIALNAME like concat('%', #{materialname}, '%')</if>
            <if test="materialspeccode != null  and materialspeccode != ''"> and MATERIALSPECCODE = #{materialspeccode}</if>
            <if test="materialspec != null  and materialspec != ''"> and MATERIALSPEC = #{materialspec}</if>
            <if test="weight != null "> and WEIGHT = #{weight}</if>
            <if test="counts != null "> and COUNTS = #{counts}</if>
            <if test="transitway != null  and transitway != ''"> and TRANSITWAY = #{transitway}</if>
            <if test="memo != null  and memo != ''"> and MEMO = #{memo}</if>
            <if test="createman != null  and createman != ''"> and CREATEMAN = #{createman}</if>
            <if test="createdate != null "> and CREATEDATE = #{createdate}</if>
            <if test="updatedate != null "> and UPDATEDATE = #{updatedate}</if>
            <if test="updateman != null  and updateman != ''"> and UPDATEMAN = #{updateman}</if>
            <if test="heatno != null  and heatno != ''"> and HEATNO = #{heatno}</if>
            <if test="sgsign != null  and sgsign != ''"> and SGSIGN = #{sgsign}</if>
            <if test="spec != null  and spec != ''"> and SPEC = #{spec}</if>
            <if test="matno != null  and matno != ''"> and MATNO = #{matno}</if>
            <if test="gbhao != null  and gbhao != ''"> and GBHAO = #{gbhao}</if>
        </where>
    </select>
    
    <select id="selectStoreoutBfhCkById" parameterType="Long" resultMap="StoreoutBfhCkResult">
        <include refid="selectStoreoutBfhCkVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertStoreoutBfhCk" parameterType="StoreoutBfhCkMeasure">
        insert into L_STOREOUT_BFH_CK_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="validflag != null">VALIDFLAG,</if>
            <if test="matchid != null">MATCHID,</if>
            <if test="carno != null">CARNO,</if>
            <if test="icno != null">ICNO,</if>
            <if test="operatype != null">OPERATYPE,</if>
            <if test="planid != null">PLANID,</if>
            <if test="storecode != null">STORECODE,</if>
            <if test="storename != null">STORENAME,</if>
            <if test="storepos != null">STOREPOS,</if>
            <if test="targetcode != null">TARGETCODE,</if>
            <if test="targetname != null">TARGETNAME,</if>
            <if test="materialcode != null">MATERIALCODE,</if>
            <if test="materialname != null">MATERIALNAME,</if>
            <if test="materialspeccode != null">MATERIALSPECCODE,</if>
            <if test="materialspec != null">MATERIALSPEC,</if>
            <if test="weight != null">WEIGHT,</if>
            <if test="counts != null">COUNTS,</if>
            <if test="transitway != null">TRANSITWAY,</if>
            <if test="memo != null">MEMO,</if>
            <if test="createman != null">CREATEMAN,</if>
            <if test="createdate != null">CREATEDATE,</if>
            <if test="updatedate != null">UPDATEDATE,</if>
            <if test="updateman != null">UPDATEMAN,</if>
            <if test="heatno != null">HEATNO,</if>
            <if test="sgsign != null">SGSIGN,</if>
            <if test="spec != null">SPEC,</if>
            <if test="matno != null">MATNO,</if>
            <if test="gbhao != null">GBHAO,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="validflag != null">#{validflag},</if>
            <if test="matchid != null">#{matchid},</if>
            <if test="carno != null">#{carno},</if>
            <if test="icno != null">#{icno},</if>
            <if test="operatype != null">#{operatype},</if>
            <if test="planid != null">#{planid},</if>
            <if test="storecode != null">#{storecode},</if>
            <if test="storename != null">#{storename},</if>
            <if test="storepos != null">#{storepos},</if>
            <if test="targetcode != null">#{targetcode},</if>
            <if test="targetname != null">#{targetname},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspeccode != null">#{materialspeccode},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="weight != null">#{weight},</if>
            <if test="counts != null">#{counts},</if>
            <if test="transitway != null">#{transitway},</if>
            <if test="memo != null">#{memo},</if>
            <if test="createman != null">#{createman},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="updatedate != null">#{updatedate},</if>
            <if test="updateman != null">#{updateman},</if>
            <if test="heatno != null">#{heatno},</if>
            <if test="sgsign != null">#{sgsign},</if>
            <if test="spec != null">#{spec},</if>
            <if test="matno != null">#{matno},</if>
            <if test="gbhao != null">#{gbhao},</if>
         </trim>
    </insert>

    <update id="updateStoreoutBfhCk" parameterType="StoreoutBfhCkMeasure">
        update L_STOREOUT_BFH_CK_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="validflag != null">VALIDFLAG = #{validflag},</if>
            <if test="matchid != null">MATCHID = #{matchid},</if>
            <if test="carno != null">CARNO = #{carno},</if>
            <if test="icno != null">ICNO = #{icno},</if>
            <if test="operatype != null">OPERATYPE = #{operatype},</if>
            <if test="planid != null">PLANID = #{planid},</if>
            <if test="storecode != null">STORECODE = #{storecode},</if>
            <if test="storename != null">STORENAME = #{storename},</if>
            <if test="storepos != null">STOREPOS = #{storepos},</if>
            <if test="targetcode != null">TARGETCODE = #{targetcode},</if>
            <if test="targetname != null">TARGETNAME = #{targetname},</if>
            <if test="materialcode != null">MATERIALCODE = #{materialcode},</if>
            <if test="materialname != null">MATERIALNAME = #{materialname},</if>
            <if test="materialspeccode != null">MATERIALSPECCODE = #{materialspeccode},</if>
            <if test="materialspec != null">MATERIALSPEC = #{materialspec},</if>
            <if test="weight != null">WEIGHT = #{weight},</if>
            <if test="counts != null">COUNTS = #{counts},</if>
            <if test="transitway != null">TRANSITWAY = #{transitway},</if>
            <if test="memo != null">MEMO = #{memo},</if>
            <if test="createman != null">CREATEMAN = #{createman},</if>
            <if test="createdate != null">CREATEDATE = #{createdate},</if>
            <if test="updatedate != null">UPDATEDATE = #{updatedate},</if>
            <if test="updateman != null">UPDATEMAN = #{updateman},</if>
            <if test="heatno != null">HEATNO = #{heatno},</if>
            <if test="sgsign != null">SGSIGN = #{sgsign},</if>
            <if test="spec != null">SPEC = #{spec},</if>
            <if test="matno != null">MATNO = #{matno},</if>
            <if test="gbhao != null">GBHAO = #{gbhao},</if>
        </trim>
        where ID = #{id}
    </update>



    <delete id="deleteStoreoutBfhCkById" parameterType="Long">
        delete from L_STOREOUT_BFH_CK_T where ID = #{id}
    </delete>

    <delete id="deleteStoreoutBfhCkByIds" parameterType="String">
        delete from L_STOREOUT_BFH_CK_T where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteStoreoutBfhCkByMatchid" parameterType="String">
        delete from L_STOREOUT_BFH_CK_T where MATCHID = #{matchid}
    </delete>
</mapper> 