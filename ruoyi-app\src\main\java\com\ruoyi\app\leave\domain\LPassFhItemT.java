package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出厂返回物资明细对象 L_PASS_FH_ITEM_T
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public class LPassFhItemT extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 本表主键 */
    private Long id;

    /** 头表主键 */
    @Excel(name = "头表主键")
    private Long pid;

    /** 物资编码 */
    @Excel(name = "物资编码")
    private String materialcode;

    /** 物资名称 */
    @Excel(name = "物资名称")
    private String materialname;

    /** 规格 */
    @Excel(name = "规格")
    private String materialspec;

    /** 型号 */
    @Excel(name = "型号")
    private String materialtype;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 数量 */
    @Excel(name = "数量")
    private String count;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 返回数量 */
    @Excel(name = "返回数量")
    private BigDecimal returncount;

    /** 1 正常 2 确认出厂 3 确认进厂 4收货完成，5门卫接收部分，6分厂接收部分 */
    @Excel(name = "1 正常 2 确认出厂 3 确认进厂 4收货完成，5门卫接收部分，6分厂接收部分")
    private Long fpflag;

    /** 单个物资出厂时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "单个物资出厂时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date outdate;

    /** 单个物资进厂时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "单个物资进厂时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date indate;

    /** 单个物资收货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "单个物资收货时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date shdate;

    /** 几号门（出门） */
    @Excel(name = "几号门", readConverterExp = "出=门")
    private String outdoor;

    /** 几号门（进门） */
    @Excel(name = "几号门", readConverterExp = "进=门")
    private String indoor;

    /** 单个物资收货确认 */
    @Excel(name = "单个物资收货确认")
    private String shren;

    /** 返回时候，门卫每次接收的数量 */
    @Excel(name = "返回时候，门卫每次接收的数量")
    private BigDecimal mwshCount;

    /** 返回时候，门卫已经接收的数量 */
    @Excel(name = "返回时候，门卫已经接收的数量")
    private BigDecimal mwshCounted;

    /** 临时字段 */
    @Excel(name = "临时字段")
    private Long counttmp;

    /** 物流物资名称 */
    @Excel(name = "物流物资名称")
    private String wlmaterialname;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPid(Long pid) 
    {
        this.pid = pid;
    }

    public Long getPid() 
    {
        return pid;
    }
    public void setMaterialcode(String materialcode) 
    {
        this.materialcode = materialcode;
    }

    public String getMaterialcode() 
    {
        return materialcode;
    }
    public void setMaterialname(String materialname) 
    {
        this.materialname = materialname;
    }

    public String getMaterialname() 
    {
        return materialname;
    }
    public void setMaterialspec(String materialspec) 
    {
        this.materialspec = materialspec;
    }

    public String getMaterialspec() 
    {
        return materialspec;
    }
    public void setMaterialtype(String materialtype) 
    {
        this.materialtype = materialtype;
    }

    public String getMaterialtype() 
    {
        return materialtype;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setCount(String count) 
    {
        this.count = count;
    }

    public String getCount() 
    {
        return count;
    }
    public void setWeight(BigDecimal weight) 
    {
        this.weight = weight;
    }

    public BigDecimal getWeight() 
    {
        return weight;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setReturncount(BigDecimal returncount) 
    {
        this.returncount = returncount;
    }

    public BigDecimal getReturncount() 
    {
        return returncount;
    }
    public void setFpflag(Long fpflag) 
    {
        this.fpflag = fpflag;
    }

    public Long getFpflag() 
    {
        return fpflag;
    }
    public void setOutdate(Date outdate) 
    {
        this.outdate = outdate;
    }

    public Date getOutdate() 
    {
        return outdate;
    }
    public void setIndate(Date indate) 
    {
        this.indate = indate;
    }

    public Date getIndate() 
    {
        return indate;
    }
    public void setShdate(Date shdate) 
    {
        this.shdate = shdate;
    }

    public Date getShdate() 
    {
        return shdate;
    }
    public void setOutdoor(String outdoor) 
    {
        this.outdoor = outdoor;
    }

    public String getOutdoor() 
    {
        return outdoor;
    }
    public void setIndoor(String indoor) 
    {
        this.indoor = indoor;
    }

    public String getIndoor() 
    {
        return indoor;
    }
    public void setShren(String shren) 
    {
        this.shren = shren;
    }

    public String getShren() 
    {
        return shren;
    }
    public void setMwshCount(BigDecimal mwshCount) 
    {
        this.mwshCount = mwshCount;
    }

    public BigDecimal getMwshCount() 
    {
        return mwshCount;
    }
    public void setMwshCounted(BigDecimal mwshCounted) 
    {
        this.mwshCounted = mwshCounted;
    }

    public BigDecimal getMwshCounted() 
    {
        return mwshCounted;
    }
    public void setCounttmp(Long counttmp) 
    {
        this.counttmp = counttmp;
    }

    public Long getCounttmp() 
    {
        return counttmp;
    }
    public void setWlmaterialname(String wlmaterialname) 
    {
        this.wlmaterialname = wlmaterialname;
    }

    public String getWlmaterialname() 
    {
        return wlmaterialname;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pid", getPid())
            .append("materialcode", getMaterialcode())
            .append("materialname", getMaterialname())
            .append("materialspec", getMaterialspec())
            .append("materialtype", getMaterialtype())
            .append("unit", getUnit())
            .append("count", getCount())
            .append("weight", getWeight())
            .append("memo", getMemo())
            .append("returncount", getReturncount())
            .append("fpflag", getFpflag())
            .append("outdate", getOutdate())
            .append("indate", getIndate())
            .append("shdate", getShdate())
            .append("outdoor", getOutdoor())
            .append("indoor", getIndoor())
            .append("shren", getShren())
            .append("mwshCount", getMwshCount())
            .append("mwshCounted", getMwshCounted())
            .append("counttmp", getCounttmp())
            .append("wlmaterialname", getWlmaterialname())
            .toString();
    }
}
