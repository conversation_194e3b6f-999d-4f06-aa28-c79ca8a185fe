package com.ruoyi.app.supply.service;

import com.ruoyi.app.supply.domain.SupplyUserInfo;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.List;

/**
 * 相关方人员Service接口
 * 
 * <AUTHOR>
 * @date 2023-3-22
 */
public interface ISupplyUserInfoService
{
    /**
     * 查询相关方人员信息
     *
     * @param id 人员ID
     * @return 人员信息
     */
    public SupplyUserInfo selectSupplyUserInfoById(Integer id);

    /**
     * 查询人员信息列表
     *
     * @param supplyUserInfo 人员信息
     * @return 人员信息集合
     */
    public List<SupplyUserInfo> selectSupplyUserInfoList(SupplyUserInfo supplyUserInfo);

    /**
     * 查询人员信息分页列表
     *
     * @param supplyUserInfo 人员信息
     * @return 人员信息分页数据
     */
    public TableDataInfo selectSupplyUserInfoTable(SupplyUserInfo supplyUserInfo);

    /**
     * 新增人员信息
     *
     * @param supplyUserInfo 人员信息
     * @return 结果
     */
    public int insertSupplyUserInfo(SupplyUserInfo supplyUserInfo);

    /**
     * 修改人员信息
     *
     * @param supplyUserInfo 人员信息
     * @return 结果
     */
    public int updateSupplyUserInfo(SupplyUserInfo supplyUserInfo);

    /**
     * 批量删除人员信息
     *
     * @param ids 需要删除的人员信息主键集合
     * @return 结果
     */
    public int deleteSupplyUserInfoByIds(Integer[] ids);

    /**
     * 删除人员信息信息
     *
     * @param id 人员信息主键
     * @return 结果
     */
    public int deleteSupplyUserInfoById(Integer id);
} 