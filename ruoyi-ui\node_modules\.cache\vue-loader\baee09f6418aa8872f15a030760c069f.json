{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\report.vue?vue&type=style&index=0&id=e34d0486&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\report.vue", "mtime": 1756169504037}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoudGFibGUtc3RyaXBlZHsKICBtYXJnaW4tdG9wOiAxMHB4OwogIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgd2lkdGg6IDEwMCU7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGJvcmRlcjogMXB4ICM4ODg7CiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsKfQoudGFibGUtc3RyaXBlZCB0aHsKICBoZWlnaHQ6IDMycHg7CiAgYm9yZGVyOiAxcHggc29saWQgIzg4ODsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGVkZWRlOwp9Ci50YWJsZS1zdHJpcGVkIHRkewogIG1pbi1oZWlnaHQ6IDMycHg7CiAgYm9yZGVyOiAxcHggc29saWQgIzg4ODsKfQoudGFibGUtaW5wdXQgLmVsLXRleHRhcmVhX19pbm5lcnsKICBib3JkZXI6IDAgIWltcG9ydGFudDsKICByZXNpemU6IG5vbmUgIWltcG9ydGFudDsKfQoudGFibGUtaW5wdXQgLmVsLWlucHV0X19pbm5lcnsKICBib3JkZXI6IDAgIWltcG9ydGFudDsKfQovKiAubXlVcGxvYWQgLmVsLXVwbG9hZC0tcGljdHVyZS1jYXJkewogIGRpc3BsYXk6bm9uZSAhaW1wb3J0YW50OyAKfSAqLwo="}, {"version": 3, "sources": ["report.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "report.vue", "sourceRoot": "src/views/assess/self/config/user", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            :clearable=\"false\"\r\n            @change=\"handleQuery\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\" disabled>\r\n            <el-option\r\n              v-for=\"item in deptOptions\"\r\n              :key=\"item.deptId\"\r\n              :label=\"item.deptName\"\r\n              :value=\"item.deptId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-s-tools\" size=\"mini\" @click=\"handleConfig\">指标配置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row>\r\n        <el-form-item label=\"当前状态\">\r\n          <el-tag v-if=\"status == '0' && rejectReason\" type=\"danger\">退 回</el-tag>\r\n          <el-tag v-if=\"status == '0' && !rejectReason\" type=\"info\">未提交</el-tag>\r\n            <el-tag v-else-if=\"status === '1'\" type=\"warning\">部门评分</el-tag>\r\n            <el-tag v-else-if=\"status === '2'\" type=\"warning\">事业部评分</el-tag>\r\n            <el-tag v-else-if=\"status === '3'\" type=\"warning\">运改/组织部评分</el-tag>\r\n            <el-tag v-else-if=\"status === '4' || status === '5'\" type=\"success\">已完成</el-tag>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row v-if=\"rejectReason\">\r\n        <el-form-item label=\"退回原因\" prop=\"rejectReason\">\r\n            <span class=\"el-icon-warning\" style=\"color: #f56c6c;\"></span>\r\n            {{ rejectReason }}\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n      <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n      <el-descriptions class=\"margin-top\" :column=\"3\">\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            姓名\r\n          </template>\r\n          {{ userInfo.name }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            部门\r\n          </template>\r\n          {{ deptName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            考核年月\r\n          </template>\r\n          {{ assessDateText }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-table v-loading=\"loading\" :data=\"list\"\r\n        :span-method=\"objectSpanMethod\" border>\r\n        <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n        <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"140\"/>\r\n        <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"150\" />\r\n        <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n        <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.performance }}</span>\r\n                <div v-else style=\"display: flex\">\r\n                  <!-- <el-button icon=\"el-icon-search\" size=\"small\"></el-button> -->\r\n                  <el-popover\r\n                    placement=\"left\"\r\n                    width=\"636\"\r\n                    trigger=\"click\"\r\n                    :ref=\"'popover' + scope.$index\">\r\n                    <el-table :data=\"beAssessedList\">\r\n                      <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                      <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                      <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      <el-table-column width=\"80\" label=\"操作\">\r\n                        <template slot-scope=\"beAssessed\">\r\n                          <el-button\r\n                            size=\"mini\"\r\n                            type=\"text\"\r\n                            icon=\"el-icon-edit\"\r\n                            @click=\"handleBeAssessedClick(scope.row,beAssessed.row,scope.$index)\"\r\n                          >填入</el-button>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                    <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                  </el-popover>\r\n                  <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.performance\" placeholder=\"请输入完成实绩\" />\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"108\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.dePoints }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"number\" autosize v-model=\"scope.row.dePoints\" placeholder=\"请输入加减分\" @input=\"scoreInput(scope.row)\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\"  width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.pointsReason }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.pointsReason\" placeholder=\"请输入加减分原因\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" style=\"margin-top: 10px;\" label-position=\"left\">\r\n            <el-form-item v-if=\"readOnly\" label=\"自评分数：\" prop=\"deptId\">\r\n              <span>{{ selfScore + \" 分\" }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-else label=\"自评分数：\" prop=\"selfScore\">\r\n              <div style=\"display: flex;width: 180px;\">\r\n                <el-input type=\"number\" autosize v-model=\"selfScore\" placeholder=\"请输入分数\" :readonly=\"readOnly\" />\r\n                <span style=\"margin-left: 8px;\">分</span>\r\n              </div>\r\n            </el-form-item>\r\n          </el-form>\r\n      <div v-if=\"!readOnly && status !== '1'\" style=\"text-align: center;\">\r\n        <el-button v-if=\"resetShow\" plain type=\"info\" @click=\"resetInfo\">重 置 </el-button>\r\n        <el-button type=\"success\" @click=\"save\">保 存</el-button>\r\n      </div>\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { getInfoByDate, saveInfo, delInfo, listBeAssessed } from \"@/api/assess/self/info\";\r\n  import { getDept } from \"@/api/assess/lateral/dept\";\r\n  import { getByWorkNoDeptId } from \"@/api/assess/self/user\";\r\n\r\n  export default {\r\n    name: \"SelfAssessReport\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 绩效考核-自评指标配置表格数据\r\n        list: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          userId:null,\r\n          workNo: null,\r\n          deptId:null,\r\n          assessDate: null,\r\n        },\r\n        // 考核年月文本显示\r\n        assessDateText:null,\r\n        // 部门显示\r\n        deptName:null,\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 用户信息\r\n        userInfo:{},\r\n        // 合并单元格信息\r\n        spanList:{\r\n          itemList:[],\r\n          standardList:[]\r\n        },\r\n        // 是否显示重置按钮\r\n        resetShow:false,\r\n        // 是否只读\r\n        readOnly:false,\r\n        // 部门选项\r\n        deptOptions:[],\r\n        // 自评信息Id\r\n        id:null,\r\n        // 自评分数\r\n        selfScore:100,\r\n        // 状态\r\n        status:\"0\",\r\n        // 自评信息\r\n        info:{},\r\n        // 横向被考评信息\r\n        beAssessedList:[],\r\n        // 退回理由\r\n        rejectReason:\"\",\r\n      };\r\n    },\r\n    created() {\r\n      this.initPageData();\r\n    },\r\n\r\n    // 监听路由变化，确保每次进入页面都重新获取数据\r\n    watch: {\r\n      '$route'(to) {\r\n        // 当路由发生变化时，重新初始化页面数据\r\n        if (to.path === '/assess/self/user/report') {\r\n          this.initPageData();\r\n        }\r\n      }\r\n    },\r\n\r\n    // 路由更新时的钩子\r\n    beforeRouteUpdate(to, from, next) {\r\n      // 在当前路由改变，但是该组件被复用时调用\r\n      this.initPageData();\r\n      next();\r\n    },\r\n    methods: {\r\n      // 初始化页面数据\r\n      initPageData() {\r\n        // 从路由中获取参数\r\n        this.queryParams.workNo = this.$route.query.workNo;\r\n        this.queryParams.deptId = this.$route.query.deptId;\r\n\r\n        // 验证必要参数\r\n        if (!this.queryParams.workNo || !this.queryParams.deptId) {\r\n          this.$message.error('缺少必要参数：用户工号或部门ID');\r\n          this.$router.go(-1);\r\n          return;\r\n        }\r\n\r\n        // 设置默认考核年月\r\n        this.queryParams.assessDate = this.getDefaultAssessDate();\r\n        this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n\r\n        // 重置数据\r\n        this.list = [];\r\n        this.userInfo = {};\r\n        this.deptOptions = [];\r\n        this.beAssessedList = [];\r\n        this.id = null;\r\n        this.selfScore = 100;\r\n        this.status = \"0\";\r\n        this.info = {};\r\n        this.rejectReason = \"\";\r\n        this.readOnly = false;\r\n        this.resetShow = false;\r\n\r\n        // 根据路由参数直接获取用户信息\r\n        this.getDeptInfo();\r\n        this.getUserInfoByParams();\r\n      },\r\n\r\n      // 获取默认考核日期\r\n      getDefaultAssessDate() {\r\n        const now = new Date();\r\n        const currentDay = now.getDate();\r\n\r\n        let targetDate;\r\n        if (currentDay < 10) {\r\n          // 当前日期小于10日，默认为上个月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n        } else {\r\n          // 当前日期大于等于10日，默认为当月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n        }\r\n\r\n        // 格式化为 YYYY-M 格式\r\n        const year = targetDate.getFullYear();\r\n        const month = targetDate.getMonth() + 1;\r\n        return `${year}-${month}`;\r\n      },\r\n\r\n        getDeptInfo(){\r\n            getDept(this.queryParams.deptId).then(res => {\r\n                // 设置部门名称\r\n                this.deptName = res.data.deptName || '未知部门';\r\n\r\n                // 设置部门选项（只包含当前部门）\r\n                this.deptOptions = [{\r\n                    deptId: this.queryParams.deptId,\r\n                    deptName: this.deptName\r\n                }];\r\n            });\r\n        },\r\n\r\n      // 根据路由参数直接获取用户信息\r\n      getUserInfoByParams() {\r\n        // 根据工号和部门ID获取用户信息\r\n        getByWorkNoDeptId({\r\n          workNo: this.queryParams.workNo,\r\n          deptId: this.queryParams.deptId\r\n        }).then(res => {\r\n          console.log('获取用户信息:', res);\r\n          if(res.code == 200 && res.data) {\r\n            this.queryParams.userId = res.data.id;\r\n            this.userInfo = res.data;\r\n\r\n\r\n            // 获取考核数据和被考核信息\r\n            this.getList();\r\n            this.getBeAssessedList();\r\n          } else {\r\n            this.$message.error('获取用户信息失败');\r\n            this.$router.go(-1);\r\n          }\r\n        }).catch(error => {\r\n          console.error('获取用户信息失败:', error);\r\n          this.$message.error('获取用户信息失败');\r\n          this.$router.go(-1);\r\n        });\r\n      },\r\n\r\n\r\n      \r\n      // 获取被考核信息\r\n      getBeAssessedList(){\r\n        listBeAssessed({deptId:this.queryParams.deptId,assessDate:this.queryParams.assessDate}).then(res =>{\r\n          let beAssessedList = [];\r\n          if(res.code == 200){\r\n            if(res.data.length > 0){\r\n              res.data.forEach(item => {\r\n                beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n              })\r\n              this.beAssessedList = beAssessedList;\r\n            }\r\n          }\r\n          console.log(beAssessedList)\r\n        })\r\n      },\r\n      /** 查询绩效考核-自评指标配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        getInfoByDate(this.queryParams).then(response => {\r\n          console.log(response.data);\r\n          // console.log(typeof response.data);\r\n          if (Array.isArray(response.data)) {\r\n            // 指标配置数据\r\n            this.handleSpanList(response.data);\r\n            this.list = response.data.map(item => {\r\n              item.performance = \"\";\r\n              // 根据项目类型设置默认分数\r\n              let noSpaceStr = item.item.replace(/\\s+/g, '');\r\n              if(noSpaceStr.includes(\"月度重点工作\")){\r\n                item.dePoints = 5; // 月度重点工作默认5分\r\n              } else {\r\n                item.dePoints = 0; // 其他项目默认0分\r\n              }\r\n              return item;\r\n            });\r\n            this.status = \"0\";\r\n            this.readOnly = false;\r\n            this.resetShow = false;\r\n            this.rejectReason = null;\r\n            this.calculateSelfScore();\r\n          } else {\r\n            // 自评信息\r\n            let info = response.data;\r\n            let list = JSON.parse(info.content);\r\n            this.handleSpanList(list);\r\n            this.list = list;\r\n            this.id = info.id;\r\n            this.selfScore = info.selfScore;\r\n            this.rejectReason = info.rejectReason;\r\n            this.status = info.status;\r\n            this.info = info;\r\n            if(info.status == \"0\"){\r\n              this.readOnly = false;\r\n              this.resetShow = true;\r\n            }else{\r\n              this.readOnly = true;\r\n              this.resetShow = false;\r\n            }\r\n          }\r\n          this.loading = false;\r\n        });\r\n      },\r\n\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let itemList = [];\r\n        let standardList = [];\r\n        let itemFlag = 0;\r\n        let standardFlag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项、评分标准合并\r\n          if(i == 0){\r\n            itemList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            standardList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            // 考核项\r\n            if(data[i - 1].item == data[i].item){\r\n              itemList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              itemList[itemFlag].rowspan += 1;\r\n            }else{\r\n              itemList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              itemFlag = i;\r\n            }\r\n            // 评分标准\r\n            if(data[i - 1].standard == data[i].standard){\r\n              standardList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              standardList[standardFlag].rowspan += 1;\r\n            }else{\r\n              standardList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              standardFlag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList.itemList = itemList;\r\n        this.spanList.standardList = standardList;\r\n      },\r\n\r\n\r\n\r\n\r\n\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        // 验证必要参数\r\n        if (!this.queryParams.workNo || !this.queryParams.deptId) {\r\n          this.$message.error('缺少必要参数：用户工号或部门ID');\r\n          return;\r\n        }\r\n\r\n        this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n\r\n        // 重置相关数据\r\n        this.id = null;\r\n        this.info = {};\r\n        this.selfScore = 100;\r\n        this.list = [];\r\n        this.beAssessedList = [];\r\n        this.rejectReason = \"\";\r\n        this.readOnly = false;\r\n        this.resetShow = false;\r\n\r\n        // 重新获取数据\r\n        this.getList();\r\n        this.getBeAssessedList();\r\n      },\r\n\r\n      // 保存\r\n      save(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"0\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType\r\n        }\r\n        saveInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.$message({\r\n              type: 'success',\r\n              message: '保存成功!'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n\r\n\r\n      // 保存重置\r\n      resetInfo(){\r\n        this.$confirm('重置后将清空所有已填写的内容，是否确认重置?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 删除保存信息\r\n          delInfo({id:this.id}).then(res => {\r\n            if(res.code == 200){\r\n              this.id = null;\r\n              this.selfScore = null;\r\n              // 获取配置信息\r\n              this.getList();\r\n              this.$message({\r\n                type: 'success',\r\n                message: '重置成功!'\r\n              });\r\n            }\r\n          }).catch(error => {\r\n            console.error('重置失败:', error);\r\n            this.$message({\r\n              type: 'error',\r\n              message: '重置失败，请重试'\r\n            });\r\n          });\r\n        }).catch(() => {\r\n          // 用户取消重置\r\n        });\r\n      },\r\n\r\n      // 处理提交数据\r\n      handleData(data){\r\n        let result = []\r\n        data.forEach(item => {\r\n          let form = {\r\n            item: item.item,\r\n            category: item.category,\r\n            target: item.target,\r\n            standard: item.standard,\r\n            performance: item.performance,\r\n            dePoints: item.dePoints,\r\n            pointsReason:item.pointsReason\r\n          };\r\n          result.push(form);\r\n        })\r\n        return result\r\n      },\r\n\r\n      /** 标准配置跳转 */\r\n      handleConfig(){\r\n        if (this.queryParams.userId) {\r\n          // 直接使用当前用户ID跳转\r\n          this.$router.push({\r\n            path:\"/assess/self/user/detail\",\r\n            query:{\r\n              userId: this.queryParams.userId\r\n            }\r\n          });\r\n        } else {\r\n          // 如果没有用户ID，通过工号和部门ID获取\r\n          getByWorkNoDeptId({\r\n            workNo: this.queryParams.workNo,\r\n            deptId: this.queryParams.deptId\r\n          }).then(res => {\r\n            console.log(res)\r\n            if(res.code == 200 && res.data && res.data.id){\r\n              this.$router.push({\r\n                path:\"/assess/self/user/detail\",\r\n                query:{\r\n                  userId: res.data.id\r\n                }\r\n              });\r\n            } else {\r\n              this.$message.error('获取用户配置信息失败');\r\n            }\r\n          }).catch(error => {\r\n            console.error('获取用户配置信息失败:', error);\r\n            this.$message.error('获取用户配置信息失败');\r\n          });\r\n        }\r\n      },\r\n\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList.itemList[rowIndex];\r\n        }\r\n        // 评分标准相同合并\r\n        if(columnIndex === 3){\r\n          return this.spanList.standardList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      // 被考核事项点击事件\r\n      handleBeAssessedClick(row,optionRow,index){\r\n        console.log(row)\r\n        // 将事项填入完成实绩列（弃用）\r\n        // if(row.performance){\r\n        //   this.$set(row, 'performance', row.performance + \"；\" + optionRow.assessContent);\r\n        // }else{\r\n        //   this.$set(row, 'performance', optionRow.assessContent);\r\n        // }\r\n        \r\n        // 将分数填入加减分列\r\n        if(row.dePoints){\r\n          this.$set(row, 'dePoints', Number(row.dePoints) + Number(optionRow.deductionOfPoint));\r\n        }else{\r\n          this.$set(row, 'dePoints', Number(optionRow.deductionOfPoint));\r\n        }\r\n        \r\n        // 将事项+分数填入加减分理由列\r\n        let reasonContent = optionRow.assessContent + \"(\" + optionRow.deductionOfPoint + \"分)\";\r\n        if(row.pointsReason){\r\n          this.$set(row, 'pointsReason', row.pointsReason + \"；\" + reasonContent);\r\n        }else{\r\n          this.$set(row, 'pointsReason', reasonContent);\r\n        }\r\n        \r\n        this.$refs[`popover${index}`].showPopper = false;\r\n        // 重新计算自评分数\r\n        this.scoreInput();\r\n      },\r\n\r\n      // 加减分输入\r\n      scoreInput(row = null){\r\n        // 验证加减分规则（仅当传入row参数时进行验证）\r\n        if (row && row.item) {\r\n          let noSpaceStr = row.item.replace(/\\s+/g, '');\r\n          let value = row.dePoints;\r\n\r\n          if (value !== null && value !== undefined && value !== '') {\r\n            let numValue = Number(value);\r\n\r\n            // 月度重点工作：只能为1、3或5分\r\n            if (noSpaceStr.includes(\"月度重点工作\")) {\r\n              if (![1, 3, 5].includes(numValue)) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '月度重点工作为得分制，只能为1分、3分或5分'\r\n                });\r\n                this.$set(row, 'dePoints', null);\r\n                return;\r\n              }\r\n            }\r\n            // 加分项：除月度重点工作外，只能填0\r\n            else if (noSpaceStr.includes(\"加分项\")) {\r\n              if (numValue !== 0) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '加分项只能填0分'\r\n                });\r\n                this.$set(row, 'dePoints', 0);\r\n                return;\r\n              }\r\n            }\r\n            // 其他类型项：只能填0或负数\r\n            else {\r\n              if (numValue > 0) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '该项目只能填0分或负数'\r\n                });\r\n                this.$set(row, 'dePoints', 0);\r\n                return;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 重新计算自评分数\r\n        this.calculateSelfScore();\r\n      },\r\n\r\n      /** 计算自评分数 */\r\n      calculateSelfScore() {\r\n        let points = 0;   // 加减分\r\n\r\n        this.list.forEach(item => {\r\n              points += Number(item.dePoints);\r\n        });\r\n        // 计算总分：基础分85 + 加减分\r\n        this.selfScore = 85 + points;\r\n      },\r\n\r\n\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .table-striped{\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n    text-align: center;\r\n    border: 1px #888;\r\n    border-collapse: collapse;\r\n  }\r\n  .table-striped th{\r\n    height: 32px;\r\n    border: 1px solid #888;\r\n    background-color: #dedede;\r\n  }\r\n  .table-striped td{\r\n    min-height: 32px;\r\n    border: 1px solid #888;\r\n  }\r\n  .table-input .el-textarea__inner{\r\n    border: 0 !important;\r\n    resize: none !important;\r\n  }\r\n  .table-input .el-input__inner{\r\n    border: 0 !important;\r\n  }\r\n  /* .myUpload .el-upload--picture-card{\r\n    display:none !important; \r\n  } */\r\n  </style>\r\n"]}]}