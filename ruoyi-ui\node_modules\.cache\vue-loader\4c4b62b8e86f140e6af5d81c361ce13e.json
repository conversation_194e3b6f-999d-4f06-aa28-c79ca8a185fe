{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\collect\\index.vue?vue&type=template&id=b19288ea", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\collect\\index.vue", "mtime": 1756170476769}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}