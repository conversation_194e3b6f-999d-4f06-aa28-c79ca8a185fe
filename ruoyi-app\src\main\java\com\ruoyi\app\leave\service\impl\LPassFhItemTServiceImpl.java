package com.ruoyi.app.leave.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LPassFhItemTMapper;
import com.ruoyi.app.leave.domain.LPassFhItemT;
import com.ruoyi.app.leave.service.ILPassFhItemTService;

/**
 * 出厂返回物资明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class LPassFhItemTServiceImpl implements ILPassFhItemTService 
{
    @Autowired
    private LPassFhItemTMapper lPassFhItemTMapper;

    /**
     * 查询出厂返回物资明细
     * 
     * @param id 出厂返回物资明细ID
     * @return 出厂返回物资明细
     */
    @Override
    public LPassFhItemT selectLPassFhItemTById(Long id)
    {
        return lPassFhItemTMapper.selectLPassFhItemTById(id);
    }

    /**
     * 查询出厂返回物资明细列表
     * 
     * @param lPassFhItemT 出厂返回物资明细
     * @return 出厂返回物资明细
     */
    @Override
    public List<LPassFhItemT> selectLPassFhItemTList(LPassFhItemT lPassFhItemT)
    {
        return lPassFhItemTMapper.selectLPassFhItemTList(lPassFhItemT);
    }

    /**
     * 新增出厂返回物资明细
     * 
     * @param lPassFhItemT 出厂返回物资明细
     * @return 结果
     */
    @Override
    public int insertLPassFhItemT(LPassFhItemT lPassFhItemT)
    {
        return lPassFhItemTMapper.insertLPassFhItemT(lPassFhItemT);
    }

    /**
     * 修改出厂返回物资明细
     * 
     * @param lPassFhItemT 出厂返回物资明细
     * @return 结果
     */
    @Override
    public int updateLPassFhItemT(LPassFhItemT lPassFhItemT)
    {
        return lPassFhItemTMapper.updateLPassFhItemT(lPassFhItemT);
    }

    /**
     * 批量删除出厂返回物资明细
     * 
     * @param ids 需要删除的出厂返回物资明细ID
     * @return 结果
     */
    @Override
    public int deleteLPassFhItemTByIds(Long[] ids)
    {
        return lPassFhItemTMapper.deleteLPassFhItemTByIds(ids);
    }

    /**
     * 删除出厂返回物资明细信息
     * 
     * @param id 出厂返回物资明细ID
     * @return 结果
     */
    @Override
    public int deleteLPassFhItemTById(Long id)
    {
        return lPassFhItemTMapper.deleteLPassFhItemTById(id);
    }
}
