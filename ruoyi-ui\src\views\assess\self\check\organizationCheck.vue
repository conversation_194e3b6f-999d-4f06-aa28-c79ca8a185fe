<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-row>
        <el-form-item label="考核年月" prop="assessDate">
          <el-date-picker
            v-model="queryParams.assessDate"
            type="month"
            value-format="yyyy-M"
            format="yyyy 年 M 月"
            placeholder="选择考核年月"
            :clearable="false">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入姓名"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="部门" prop="deptId">
          <treeselect style="width: 200px;" v-model="queryParams.deptId" :multiple="false" :options="deptOptions" :normalizer="normalizer" :disable-branch-nodes="true" placeholder="请选择部门" />
        </el-form-item>
        <el-form-item label="岗位类型" prop="postType">
          <el-select v-model="queryParams.postType" placeholder="请选择岗位类型" clearable style="width: 150px;">
            <el-option label="技术" value="0"></el-option>
            <el-option label="行政" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    
    <!-- 待评分列表 -->
    <el-card class="box-card" style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-size: 16px; font-weight: bold; color: #409EFF;">
          <i class="el-icon-s-order"></i>
          {{ toCheckLabel }}
        </span>
      </div>
      
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            @click="handleBatchQuickScore"
          >批量快速评分</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
        <el-table v-loading="loading" :data="listToCheck" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="工号" align="center" prop="workNo" width="120"/>
          <el-table-column label="姓名" align="center" prop="name" width="120"/>
          <el-table-column label="部门" align="center" prop="deptName" ></el-table-column>
          <el-table-column label="岗位类型" align="center" prop="postType" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.postType == '0' ? 'primary' : 'success'" size="small">
                {{ scope.row.postType == '0' ? '技术' : '行政' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="自评分" align="center" prop="selfScore"></el-table-column>
          <el-table-column label="部门领导评分" align="center" prop="deptScore"></el-table-column>
          <el-table-column label="事业部评分" align="center" prop="businessScore">
            <template slot-scope="scope">
              <span v-if="scope.row.businessScore">{{ scope.row.businessScore }}</span>
              <span v-else>无</span>
            </template>
          </el-table-column>
          <el-table-column label="是否100%挂钩公司效益" align="center" prop="benefitLinkFlag">
            <template slot-scope="scope">
              <el-tag :type="scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'">{{ scope.row.benefitLinkFlag == "Y" ? "是" : " 否" }}</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="运改组织部评分" align="center" prop="organizationScore">
            <template slot-scope="scope">
              <el-input-number 
                v-model="scope.row.quickScore" 
                :min="0" 
                :max="100" 
                size="mini"
                style="width: 120px"
                placeholder="请输入分数">
              </el-input-number>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="加减分原因" align="center">
            <template slot-scope="scope">
              <el-input 
                v-model="scope.row.quickReason"
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 4}"
                size="mini"
                style="width: 150px"
                placeholder="请输入加减分原因">
              </el-input>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleCheckDetail(scope.row)"
              >详细评分</el-button>
            </template>
          </el-table-column>
        </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 评分记录 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 16px; font-weight: bold; color: #67C23A;">
          <i class="el-icon-document"></i>
          评分记录({{ checkedTotal }})
        </span>
      </div>
      
      <el-table v-loading="loading" :data="listChecked">
        <el-table-column label="工号" align="center" prop="workNo" width="120"/>
        <el-table-column label="姓名" align="center" prop="name" width="120"/>
        <el-table-column label="部门" align="center" prop="deptName" />
        <el-table-column label="职务" align="center" prop="job" width="150"/>
        <el-table-column label="岗位类型" align="center" prop="postType" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.postType == '0' ? 'primary' : 'success'" size="small">
              {{ scope.row.postType == '0' ? '技术' : '行政' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="评分类型" align="center" prop="type" >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type == '1'" type="primary" size="small">部门领导评分</el-tag>
            <el-tag v-if="scope.row.type == '2'" type="warning" size="small">事业部领导评分</el-tag>
            <el-tag v-if="scope.row.type == '3'" type="success" size="small">运改组织部审核</el-tag>
            <el-tag v-if="scope.row.type == '4'" type="info" size="small">条线领导评分</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="评分时间" align="center" prop="createTime" width="160"/>
        <el-table-column label="评分" align="center" prop="score" width="100"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleCheckedDetail(scope.row)"
            >查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="checkedTotal>0"
        :total="checkedTotal"
        :page.sync="checkedQueryParams.pageNum"
        :limit.sync="checkedQueryParams.pageSize"
        @pagination="getCheckedList"
        style="margin-top: 20px;"
      />
    </el-card>

    <el-dialog
      :visible.sync="open"
      fullscreen
      class="assessment-detail-dialog">
      <div class="detail-container">
        <div class="detail-header">
          <h2 style="text-align: center; color: #303133; margin-bottom: 20px;">
            <i class="el-icon-document"></i>
            月度业绩考核表
          </h2>
          <el-card shadow="never" style="margin-bottom: 20px;">
            <el-descriptions class="margin-top" :column="3" border>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-user"></i> 姓名
                </template>
                {{ checkInfo.name }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-office-building"></i> 部门
                </template>
                {{ checkInfo.deptName }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-date"></i> 考核年月
                </template>
                {{ checkInfo.assessDate }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </div>
        
        <el-card shadow="never" class="assessment-table-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 16px; font-weight: bold; color: #409EFF;">
              <i class="el-icon-s-data"></i>
              考核详情
            </span>
          </div>
          <el-table v-loading="loading" :data="checkInfo.list"
            :span-method="objectSpanMethod" border stripe>
            <el-table-column label="类型" align="center" prop="item" width="120"/>
            <el-table-column label="指标" align="center" prop="category" width="150"/>
            <el-table-column label="目标" align="center" prop="target" width="180"/>
            <el-table-column label="评分标准" align="center" prop="standard" />
            <el-table-column label="完成实绩（若扣分，写明原因）" align="center" prop="performance" >
              <template slot-scope="scope">
                  <div style="display: flex">
                    <el-popover
                      placement="left"
                      width="636"
                      trigger="click"
                      :ref="'popover' + scope.$index">
                      <el-table :data="beAssessedList">
                        <el-table-column width="150" property="assessDeptName" label="提出考核单位"></el-table-column>
                        <el-table-column width="300" property="assessContent" label="事项"></el-table-column>
                        <el-table-column width="80" property="deductionOfPoint" label="加减分"></el-table-column>
                      </el-table>
                      <el-button slot="reference" icon="el-icon-search" size="small"></el-button>
                    </el-popover>
                    <span style="margin-left: 10px;">{{ scope.row.performance }}</span>
                  </div>
              </template>
            </el-table-column>
            <el-table-column label="加减分" align="center" prop="dePoints" width="150" />
            <el-table-column label="加减分理由" align="center" prop="pointsReason" width="180" />
          </el-table>
        </el-card>
        
        <el-card shadow="never" class="signature-card" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span style="font-size: 16px; font-weight: bold; color: #67C23A;">
              <i class="el-icon-edit-outline"></i>
              评分记录
            </span>
          </div>
          <el-form size="small" :inline="false" label-width="200px" label-position="left">
            <!-- 自评分 -->
            <el-form-item>
              <template slot="label">
                <span style="color: #606266;">
                  自评分数 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ checkInfo.selfScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ checkInfo.name }}</span>
              </div>
            </el-form-item>
            
            <!-- 部门领导评分 -->
            <el-form-item v-if="checkInfo.deptScore && checkInfo.deptUserName">
              <template slot="label">
                <span style="color: #606266;">
                  部门领导评分 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ checkInfo.deptScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ checkInfo.deptUserName }}</span>
                <div v-if="checkInfo.deptScoreReason" class="reason-text">
                  <span class="reason-label">加减分理由：</span>
                  <span class="reason-content">{{ checkInfo.deptScoreReason }}</span>
                </div>
              </div>
            </el-form-item>
            
            <!-- 事业部领导评分 -->
            <el-form-item v-if="checkInfo.businessUserName && checkInfo.businessScore">
              <template slot="label">
                <span style="color: #606266;">
                  事业部领导评分 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ checkInfo.businessScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ checkInfo.businessUserName }}</span>
                <div v-if="checkInfo.businessScoreReason" class="reason-text">
                  <span class="reason-label">加减分理由：</span>
                  <span class="reason-content">{{ checkInfo.businessScoreReason }}</span>
                </div>
              </div>
            </el-form-item>
            
            <!-- 运改组织部评分 -->
            <el-form-item v-if="checkInfo.organizationScore && checkInfo.organizationUserName">
              <template slot="label">
                <span style="color: #606266;">
                  运改组织部评分 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ checkInfo.organizationScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ checkInfo.organizationUserName }}</span>
                <div v-if="checkInfo.organizationScoreReason" class="reason-text">
                  <span class="reason-label">加减分理由：</span>
                  <span class="reason-content">{{ checkInfo.organizationScoreReason }}</span>
                </div>
              </div>
            </el-form-item>
            
            <!-- 公司效益信息 -->
            <el-form-item label="是否100%挂钩公司效益：">
              <el-tag :type="userInfo.benefitLinkFlag == 'Y' ? 'success' : 'info'">{{ userInfo.benefitLinkFlag == "Y" ? "是" : " 否" }}</el-tag>
            </el-form-item>
            
            <!-- 当前状态评分输入 -->
            <el-form-item v-if="checkInfo.status == '3'" label="公司效益加减分：">
              <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                <el-input-number v-model="benefit" placeholder="请输入公司效益加减" style="width: 200px;" />
                <span>分</span>
                <el-button type="primary" size="mini" @click="calScore">计 算</el-button>
              </div>
              <div v-if="benefitDetail" class="benefit-detail">
                <i class="el-icon-info"></i>
                {{ benefitDetail }}
              </div>
            </el-form-item>
            <el-form-item v-if="checkInfo.status == '3'" label="运改部/组织部审核：">
              <div style="display: flex; align-items: center; gap: 10px;">
                <el-input-number v-model="form.organizationScore" :min="0" :max="100" placeholder="请输入评分" style="width: 150px;" />
                <span>分</span>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
        
        <div class="dialog-footer" style="text-align: center; margin-top: 30px; padding: 20px;">
          <el-button type="primary" size="medium" @click="checkSubmit">
            <i class="el-icon-check"></i> 提 交
          </el-button>
          <el-button plain type="danger" size="medium" @click="rejectClick">
            <i class="el-icon-close"></i> 退 回
          </el-button>
          <el-button plain type="info" size="medium" @click="cancel">
            <i class="el-icon-back"></i> 取 消
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 驳回弹出框 -->
    <el-dialog title="退回" :visible.sync="rejectOpen" append-to-body center width="40%">
        <el-form label-width="150px">
            <el-form-item label="退回原因:">
                <el-input type="textarea"
                :autosize="{ minRows: 5}" 
                v-model="checkInfo.rejectReason" 
                placeholder="请输入内容"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center;">
            <el-button type="success" plain @click="rejectSubmit">提 交</el-button>
            <el-button @click="rejectCancel">取 消</el-button>
        </div>
    </el-dialog>

      <!-- 批量快速评分对话框 -->
      <el-dialog :title="'批量快速评分确认'" :visible.sync="batchQuickScoreOpen" width="1400px" append-to-body>
        <el-row>
          <el-col :span="12">
            <div class="benefit-score-container" style="display: flex; align-items: center; justify-content: flex-start; margin-bottom: 8px;">
              <div class="benefit-input-group" style="display: flex; align-items: center; height: 32px;">
                <span style="margin-right: 10px; white-space: nowrap; line-height: 32px;">公司效益加减分:</span>
                <el-input 
                  type="number" 
                  v-model="benefit" 
                  placeholder="请输入公司效益加减" 
                  style="width: 180px; margin-right: 5px;"
                />
                <span style="margin-right: 15px; line-height: 32px;">分</span>
                <el-button type="primary" size="mini" @click="batchCalScore" style="height: 28px;">计 算</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-alert
          title="请确认以下人员的评分信息"
          type="warning"
          :closable="false"
          show-icon
          class="mb20"
        />
        <el-table :data="selectedRows" size="small" border>
          <el-table-column label="工号" align="center" prop="workNo" />
          <el-table-column label="姓名" align="center" prop="name" />
          <el-table-column label="部门" align="center" prop="deptName" />
          <el-table-column label="岗位" align="center" prop="job" />
          <el-table-column label="岗位类型" align="center" prop="postType" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.postType == '0' ? 'primary' : 'success'" size="small">
                {{ scope.row.postType == '0' ? '技术' : '行政' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否100%挂钩公司效益" align="center" prop="benefitLinkFlag">
            <template slot-scope="scope">
              <el-tag :type="scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'">{{ scope.row.benefitLinkFlag == "Y" ? "是" : " 否" }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="自评分" align="center" prop="selfScore" width="90px"/>
          <el-table-column label="部门评分" align="center" prop="deptScore" width="90px">
            <template slot-scope="scope">
              <span v-if="scope.row.deptScore">{{ scope.row.deptScore }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="事业部评分" align="center" prop="businessScore" width="90px">
            <template slot-scope="scope">
              <span v-if="scope.row.businessScore">{{ scope.row.businessScore }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="效益加减分" align="center" prop="benefitScore" width="90px"/>
          <el-table-column label="运改/组织部评分" align="center" prop="quickScore" width="160px">
            <template slot-scope="scope">
              <!-- <span :class="{'text-red': !scope.row.quickScore}">{{ scope.row.quickScore || '未计算' }}</span> -->
               <el-input-number 
                v-model="scope.row.quickScore" 
                :min="0" 
                :max="100" 
                size="mini"
                style="width: 120px"
                placeholder="请输入分数">
              </el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="加减分理由" align="center" prop="quickReason" width="180px">
            <template slot-scope="scope">
              <el-input 
                v-model="scope.row.quickReason"
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 4}"
                size="mini"
                style="width: 150px"
                placeholder="请输入加减分原因">
              </el-input>
            </template>
          </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitBatchQuickScore" :disabled="!canSubmitBatchScore">确 定</el-button>
          <el-button @click="cancelBatchQuickScore">取 消</el-button>
          </div>
      </el-dialog>

  </div>
</template>

<script>
import { listInfo, listChecked, getInfo, check, listBeAssessed, rejectInfo, batchQuickScore, batchWithBenefitByIds} from "@/api/assess/self/info";
import { getSelfAssessUser} from "@/api/assess/self/user";
import { formatDateYm } from "@/utils/index";
import { listDept } from "@/api/assess/lateral/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "OrganizationCheck",
  components: {
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 退回原因输入框
      rejectOpen:false,
      // 总条数
      total: 0,
      checkedTotal: 0,
      // 绩效考核-干部自评人员配置表格数据
      listToCheck: [],
      listChecked: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workNo: null,
        name:null,
        deptId:null,
        assessDate:null,
        status:"3",
        postType:null
      },
      // 评分记录查询参数
      checkedQueryParams: {
        pageNum: 1,
        pageSize: 10,
        workNo: null,
        name:null,
        deptId:null,
        assessDate:null,
        postType:null
      },
      // 表单参数
      form: {
        id:null,
        // 部门领导评分
        deptScore:null,
        // 事业部评分
        businessScore:null,
        // 条线领导评分
        leaderScore:null,
      },
      // 表单校验
      rules: {
      },
      deptOptions:[],
      openCheck:false,
      checkInfo:{},
      // 合并单元格
      spanList:[],
      // 待评分标签
      toCheckLabel:"待评分(0)",
      // 横向被考评信息
      beAssessedList:[],
      benefit:null,  // 公司效益分
      userInfo:{
        benefitLinkFlag:null
      },
      benefitDetail:"",  // 效益详细
      // 选中数组
      multipleSelection: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 批量快速评分对话框显示状态
      quickScoreDialogVisible: false,
      // 批量快速评分表单参数
      batchQuickScoreForm: {
        score: undefined,
        ids: []
      },
      // 批量快速评分表单验证规则
      batchQuickScoreRules: {
        score: [
          { required: true, message: "评分不能为空", trigger: "blur" },
          { type: 'number', message: "评分必须为数字", trigger: "blur" }
        ]
      },
      // 批量快速评分对话框
      batchQuickScoreOpen: false,
      // 选中数组
      ids: [],
      // 选中的行数据
      selectedRows: [],
    };
  },
  computed: {
    // 是否可以提交批量评分
    canSubmitBatchScore() {
      if (this.selectedRows.length === 0) return false;
      
      // 简单检查是否所有行都填写了评分
      for (let row of this.selectedRows) {
        if (!row.quickScore && row.quickScore !== 0) {
          return false;
        }
      }
      
      return true;
    }
  },
  created() {
    this.queryParams.assessDate = formatDateYm(new Date().getTime())
    this.checkedQueryParams.assessDate = formatDateYm(new Date().getTime())
    // this.getSelfAssessUser();
    // this.getCheckDeptList();
    this.getTreeselect();
    this.getList();
    this.getCheckedList();
  },
  methods: {
    // 获取被考核信息
    getBeAssessedList(param){
      listBeAssessed(param).then(res =>{
        let beAssessedList = [];
        if(res.code == 200){
          if(res.data.length > 0){
            res.data.forEach(item => {
              beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]
            })
            this.beAssessedList = beAssessedList;
          }
        }
        console.log(beAssessedList)
      })
    },
    // 获取被评分人员信息
    getSelfAssessUser(param){
      getSelfAssessUser(param).then(res =>{
        if(res.code == 200){
          this.userInfo = res.data;
        }
      })
    },
    /** 转换横向评价部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      };
    },
	  /** 查询横向评价部门下拉树结构 */
    getTreeselect() {
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, "deptId", "parentId");
      });
    },
    /** 查询绩效考核-干部自评待审核列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.listToCheck = response.rows;
        this.total = response.total;
        this.toCheckLabel = `待评分(${response.total})`
        this.loading = false;
      });
    },
    /** 获取已审核列表 */
    getCheckedList(){
      this.loading = true;
      listChecked(this.checkedQueryParams).then(res => {
        this.listChecked = res.rows;
        this.checkedTotal = res.total;
        this.loading = false;
      })
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        organizationScore: null,
      };
      this.benefit = null;
      this.benefitDetail = "";
      this.userInfo = {
        benefitLinkFlag:null
      }
      // this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.checkedQueryParams.pageNum = 1;
      // 同步搜索条件
      this.checkedQueryParams.name = this.queryParams.name;
      this.checkedQueryParams.deptId = this.queryParams.deptId;
      this.checkedQueryParams.assessDate = this.queryParams.assessDate;
      this.checkedQueryParams.postType = this.queryParams.postType;
      this.getList();
      this.getCheckedList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 审批详情
    handleCheckDetail(row){
      getInfo({id:row.id}).then(res => {
        console.log(res);
        this.reset();
        if(res.code == 200){
          this.checkInfo = res.data;
          let list = JSON.parse(res.data.content);
          this.handleSpanList(list);
          let param ={
            deptId:res.data.deptId,
            assessDate:res.data.assessDate
          }
          this.getBeAssessedList(param);  // 获取横向评分被考核数据
          this.getSelfAssessUser({id:res.data.userId});  // 获取用户信息
          this.checkInfo.list = list;
        }
        this.open = true
      })
    },

    // 审批提交
    checkSubmit(){
      if(this.verify()){
        this.form.id = this.checkInfo.id;
        this.form.status = this.checkInfo.status;
        check(this.form).then(res => {
          console.log(res)
          if(res.code == 200){
            this.$message({
              type: 'success',
              message: '提交成功!'
            });
            this.reset();
            this.open = false;
            this.getList();
            this.getCheckedList();
          }else{
            this.$message({
              type: 'warning',
              message: '操作失败，无权限或当前审批状态不匹配'
            });
          }
        })
      }else{
        this.$message({
          type: 'warning',
          message: '请填写评分'
        });
        return false;
      }
    },

    // 退回点击事件
    rejectClick(){
      this.rejectOpen = true;
    },

    rejectCancel(){
      this.checkInfo.rejectReason = null;
      this.rejectOpen = false;
    },

    // 退回
    rejectSubmit(){
      if(!this.checkInfo.rejectReason){
        this.$message({
          type: 'warning',
          message: '请填写退回原因'
        });
        return;
      }
      this.$confirm('确认后, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.reject();
      }).catch(() => {
          
      });
    },

    reject(){
      rejectInfo(this.checkInfo).then(res => {
          this.reset();
          this.checkInfo.rejectReason = null;
          this.rejectOpen = false;
          this.open = false;
          this.getList();
          this.getCheckedList();
      })
    },


    // 数据验证
    verify(){
      if(!this.form.organizationScore) return false;
      return true;
    },

    handleListChange(type){
      console.log(type)
    },
    // 处理列表
    handleSpanList(data){
      let spanList = [];
      let flag = 0;
      for(let i = 0; i < data.length; i++){
        // 相同考核项合并
        if(i == 0){
          spanList.push({
            rowspan: 1,
            colspan: 1
          })
        }else{
          if(data[i - 1].item == data[i].item){
            spanList.push({
              rowspan: 0,
              colspan: 0
            })
            spanList[flag].rowspan += 1;
          }else{
            spanList.push({
              rowspan: 1,
              colspan: 1
            })
            flag = i;
          }
        }
      }
      this.spanList = spanList;
    },

    // 合并单元格方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 第一列相同项合并
      if (columnIndex === 0) {
        return this.spanList[rowIndex];
      }
      // 类别无内容 合并
      if(columnIndex === 1){
        if(!row.category){
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if(columnIndex === 2){
        if(!row.category){
          return {
            rowspan: 1,
            colspan: 2
          }
        }
      }
    },
    // 计算效益
    calScore(){
      if(this.benefit || this.benefit == 0){
        let benefit = Number(this.benefit);
        let categoryScore = 0;
        if(this.userInfo.benefitLinkFlag == "N"){
          this.checkInfo.list.forEach(row => {
            console.log(row)
            if(row.category == "效益"){
              categoryScore += Number(row.dePoints);
            }
          })
          this.form.organizationScore = parseFloat((this.checkInfo.selfScore + (benefit / 2) - (categoryScore / 2)).toFixed(1));
          // this.benefitDetail = "计算：" + this.checkInfo.selfScore + " + " + (benefit / 2) + " + " + -(categoryScore / 2);
          this.benefitDetail = `计算：${this.checkInfo.selfScore} ${benefit > 0 ? "+" : "-"} ${Math.abs(benefit / 2)} ${categoryScore > 0 ? "-" : "+"} ${Math.abs(categoryScore / 2)} = ${this.form.organizationScore}`
        }else{
          this.form.organizationScore = parseFloat((this.checkInfo.selfScore + benefit).toFixed(1));
          this.benefitDetail = `计算：${this.checkInfo.selfScore} ${benefit > 0 ? "+" : "-"} ${Math.abs(benefit)} = ${this.form.organizationScore}`
        }
      }else{
        this.$message({
          type: 'warning',
          message: '请填写公司效益加减分'
        });
        return;
      }
    },
    // 批量计算
    batchCalScore(){
      if(this.benefit || this.benefit == 0){
        let benefit = Number(this.benefit);
        this.selectedRows.forEach(row => {
          // 确定前一步评分：优先事业部评分，其次部门评分，最后自评分
          let previousScore = 0;
          if (row.businessScore !== null && row.businessScore !== undefined && row.businessScore !== '') {
            // 有事业部评分，以事业部评分为基础
            previousScore = Number(row.businessScore);
          } else if (row.deptScore !== null && row.deptScore !== undefined && row.deptScore !== '') {
            // 没有事业部评分，以部门评分为基础
            previousScore = Number(row.deptScore);
          } else {
            // 都没有，以自评分为基础
            previousScore = Number(row.selfScore);
          }
          
          if(row.benefitLinkFlag == "N"){
            row.quickScore = parseFloat((previousScore + (benefit / 2) - (Number(row.benefitScore) / 2)).toFixed(1));
          }else{
            row.quickScore = parseFloat((previousScore + benefit).toFixed(1));
          }
        })
      }else{
        this.$message({
          type: 'warning',
          message: '请填写公司效益加减分'
        });
        return;
      }
    },

    /** 选择条数改变 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.selectedRows = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 批量快速评分按钮操作 */
    handleBatchQuickScore() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择需要评分的数据");
        return;
      }
      batchWithBenefitByIds(this.ids).then(res => {
        if(res.code == 200){
          this.selectedRows = res.data;
          this.batchQuickScoreOpen = true;
        }
      })
      // // 检查是否有未填写评分的记录
      // const emptyScores = this.selectedRows.filter(row => !row.quickScore);
      // if (emptyScores.length > 0) {
      //   this.$modal.msgError(`有${emptyScores.length}条记录未填写快速评分，请先填写评分`);
      //   return;
      // }
    },

    /** 取消批量快速评分操作 */
    cancelBatchQuickScore() {
      this.batchQuickScoreOpen = false;
    },

    /** 提交批量快速评分 */
    submitBatchQuickScore() {
      // 验证评分一致性和理由必填
      const validationResult = this.validateBatchQuickScore();
      if (!validationResult.isValid) {
        this.$modal.msgError(validationResult.message);
        return;
      }

      // 准备提交数据
      const submitData = this.selectedRows.map(row => ({
        id: row.id,
        quickScore: row.quickScore,
        quickReason: row.quickReason
      }));

      this.$modal.confirm('是否确认提交选中人员的快速评分？').then(() => {
        return batchQuickScore(submitData);
      }).then(() => {
        this.$modal.msgSuccess("批量评分成功");
        this.batchQuickScoreOpen = false;
        this.getList();
        this.getCheckedList();
      }).catch(() => {});
    },

    /** 验证批量快速评分 */
    validateBatchQuickScore() {
      for (let i = 0; i < this.selectedRows.length; i++) {
        const row = this.selectedRows[i];
        
        // 检查是否填写了评分
        if (!row.quickScore && row.quickScore !== 0) {
          return {
            isValid: false,
            message: `第${i + 1}行 ${row.name} 未填写评分，请先填写评分`
          };
        }

        // 运改组织部评分时的验证
        if (row.status == '3') {
          let previousScore = null;
          let previousScoreName = '';
          
          // 判断上一环节评分
          if (row.businessScore !== null && row.businessScore !== undefined && row.businessScore !== '') {
            // 有事业部评分，以事业部评分为准
            previousScore = parseFloat(row.businessScore);
            previousScoreName = '事业部领导评分';
          } else if (row.deptScore !== null && row.deptScore !== undefined && row.deptScore !== '') {
            // 没有事业部评分，以部门评分为准
            previousScore = parseFloat(row.deptScore);
            previousScoreName = '部门领导评分';
          } else {
            // 都没有，以自评分为准
            previousScore = parseFloat(row.selfScore);
            previousScoreName = '自评分';
          }
          
          // 运改组织部评分与上一环节评分不一致时，加减分理由必填
          if (parseFloat(row.quickScore) !== previousScore && !row.quickReason) {
            return {
              isValid: false,
              message: `第${i + 1}行 ${row.name} 运改组织部评分(${row.quickScore}分)与${previousScoreName}(${previousScore}分)不一致，请填写加减分理由`
            };
          }
        }
      }

      return { isValid: true };
    },

    /** 查看评分记录详情 */
    handleCheckedDetail(row) {
      getInfo({id: row.infoId}).then(res => {
        console.log(res);
        this.reset();
        if(res.code == 200){
          this.checkInfo = res.data;
          let list = JSON.parse(res.data.content);
          this.handleSpanList(list);
          let param ={
            deptId:res.data.deptId,
            assessDate:res.data.assessDate
          }
          this.getBeAssessedList(param);  // 获取横向评分被考核数据
          this.getSelfAssessUser({id:res.data.userId});  // 获取用户信息
          this.checkInfo.list = list;
          this.open = true;
        }
      }).catch(error => {
        this.$message.error('获取详情失败');
      });
    }
  }
};
</script>

<style scoped>
.assessment-detail-dialog .detail-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.assessment-detail-dialog .detail-header h2 {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  font-weight: bold;
}

.assessment-detail-dialog .assessment-table-card {
  margin-bottom: 20px;
}

.assessment-detail-dialog .signature-card {
  background: #ffffff;
}

.signature-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.score-text {
  font-weight: 500;
  color: #303133;
}

.separator {
  color: #909399;
  margin: 0 4px;
}

.signature-name {
  color: #303133;
}

.reason-text {
  width: 100%;
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-left: 3px solid #409EFF;
  border-radius: 4px;
}

.reason-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.reason-content {
  color: #303133;
  line-height: 1.6;
}

.benefit-detail {
  color: #909399;
  font-size: 13px;
  padding: 8px 12px;
  background-color: #f4f4f5;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

.dialog-footer {
  border-top: 1px solid #e4e7ed;
  background-color: #ffffff;
  border-radius: 0 0 6px 6px;
}

.assessment-detail-dialog .el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.assessment-detail-dialog .el-descriptions {
  background-color: #ffffff;
}

.assessment-detail-dialog .el-table {
  border-radius: 6px;
  overflow: hidden;
}

.text-red {
  color: #F56C6C;
}
</style>
