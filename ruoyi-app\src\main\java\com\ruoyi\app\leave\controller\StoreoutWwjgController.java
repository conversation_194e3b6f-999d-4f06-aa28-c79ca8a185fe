package com.ruoyi.app.leave.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.StoreoutWwjgMeasure;
import com.ruoyi.app.leave.service.IStoreoutWwjgService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 外委加工出库Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/leave/storeout/wwjg")
public class StoreoutWwjgController extends BaseController
{
    @Autowired
    private IStoreoutWwjgService storeoutWwjgService;

    /**
     * 查询外委加工出库列表
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:wwjg:list')")
    @GetMapping("/list")
    public TableDataInfo list(StoreoutWwjgMeasure storeoutWwjgMeasure)
    {
        startPage();
        List<StoreoutWwjgMeasure> list = storeoutWwjgService.selectStoreoutWwjgList(storeoutWwjgMeasure);
        return getDataTable(list);
    }

    /**
     * 导出外委加工出库列表
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:wwjg:export')")
    @Log(title = "外委加工出库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StoreoutWwjgMeasure storeoutWwjgMeasure)
    {
        List<StoreoutWwjgMeasure> list = storeoutWwjgService.selectStoreoutWwjgList(storeoutWwjgMeasure);
        ExcelUtil<StoreoutWwjgMeasure> util = new ExcelUtil<StoreoutWwjgMeasure>(StoreoutWwjgMeasure.class);
        util.exportExcel(list, "外委加工出库数据");
    }

    /**
     * 获取外委加工出库详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:wwjg:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeoutWwjgService.selectStoreoutWwjgById(id));
    }

    /**
     * 新增外委加工出库
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:wwjg:add')")
    @Log(title = "外委加工出库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StoreoutWwjgMeasure storeoutWwjgMeasure)
    {
        return toAjax(storeoutWwjgService.insertStoreoutWwjg(storeoutWwjgMeasure));
    }

    /**
     * 修改外委加工出库
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:wwjg:edit')")
    @Log(title = "外委加工出库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StoreoutWwjgMeasure storeoutWwjgMeasure)
    {
        return toAjax(storeoutWwjgService.updateStoreoutWwjg(storeoutWwjgMeasure));
    }

    /**
     * 删除外委加工出库
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:wwjg:remove')")
    @Log(title = "外委加工出库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(storeoutWwjgService.deleteStoreoutWwjgByIds(ids));
    }

    /**
     * 根据匹配ID删除外委加工出库
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:wwjg:remove')")
    @Log(title = "外委加工出库", businessType = BusinessType.DELETE)
    @DeleteMapping("/matchid/{matchid}")
    public AjaxResult removeByMatchid(@PathVariable String matchid)
    {
        return toAjax(storeoutWwjgService.deleteStoreoutWwjgByMatchid(matchid));
    }
} 