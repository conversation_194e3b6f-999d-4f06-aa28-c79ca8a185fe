<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
    <el-form-item label="编号" prop="serialNo">
        <el-input
          v-model="queryParams.serialNo"
          placeholder="请输入编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请部门" prop="deptNo">
        <el-select
          v-model="queryParams.deptNo"
          placeholder="请选择申请部门"
          clearable
          size="small"
        >
          <el-option
            v-for="item in getDepNameList"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="填报人" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入填报人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="确认人" prop="confirmName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入确认人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="确认部门" prop="companyCode">
        <el-input
          v-model="queryParams.companyCode"
          placeholder="请输入确认部门"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>     
      <el-form-item label="供应商代码" prop="suppId">
        <el-input
          v-model="queryParams.suppId"
          placeholder="请输入供应商代码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <i slot="suffix" class="el-icon-search search-icon" @click="showSuppInfoDialogForQuery"></i>
        </el-input>
      </el-form-item>
      <el-form-item label="供应商名称" prop="suppName">
        <el-input
          v-model="queryParams.suppName"
          placeholder="请输入供应商名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处罚类型" prop="punishmentType">
        <el-select v-model="queryParams.punishmentType" placeholder="请选择处罚类型" clearable size="small">
          <el-option
            v-for="dict in punishmentTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="供应类型" prop="suppType">
        <el-select v-model="queryParams.suppType" placeholder="请选择物资、服务或工程" clearable size="small" @change="handleQueryMaterialOrServiceChange">
          <el-option
            v-for="option in suppTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="物料小类编码" prop="itemNo" v-if="queryParams.suppType === 'M'">
        <el-input
          v-model="queryParams.itemNo"
          placeholder="请输入物料小类编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <i slot="suffix" class="el-icon-search search-icon" @click="showMaterialInfoDialogForQuery"></i>
        </el-input>
      </el-form-item>
      <el-form-item label="物料小类名称" prop="itemName" v-if="queryParams.suppType === 'M'">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入物料小类名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务小类编码" prop="itemNo" v-if="queryParams.suppType === 'S'">
        <el-input
          v-model="queryParams.itemNo"
          placeholder="请输入服务小类编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <i slot="suffix" class="el-icon-search search-icon" @click="showServiceDialogForQuery"></i>
        </el-input>
      </el-form-item>
      <el-form-item label="服务小类名称" prop="itemName" v-if="queryParams.suppType === 'S'">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入服务小类名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工程小类编码" prop="itemNo" v-if="queryParams.suppType === 'P'">
        <el-input
          v-model="queryParams.itemNo"
          placeholder="请输入工程小类编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <i slot="suffix" class="el-icon-search search-icon" @click="showProjectDialogForQuery"></i>
        </el-input>
      </el-form-item>
      <el-form-item label="工程小类名称" prop="itemName" v-if="queryParams.suppType === 'P'">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入工程小类名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="stateId">
        <div style="display: flex; align-items: center;">
          <el-select
            v-model="queryParams.stateId"
            placeholder="请选择状态"
            :clearable="userGroup !== 'query'"
            :disabled="userGroup === 'query'"
            size="small"
            style="width: 180px;"
          >
            <el-option
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="事件发生时间">
        <el-date-picker
          v-model="happenedTimeRange"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy/MM/dd"
          value-format="yyyy/MM/dd"
          size="small"
          clearable>
        </el-date-picker>
      </el-form-item>
      <el-form-item label="处罚执行时间">
        <el-date-picker
          v-model="punishmentTimeRange"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy/MM/dd"
          value-format="yyyy/MM/dd"
          size="small"
          clearable>
        </el-date-picker>
      </el-form-item>
      
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" v-if="permissions.canAdd">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5" v-if="permissions.canEdit">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5" v-if="permissions.canConfirm">
        <el-button
          type="primary"
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleConfirm"
        >确认</el-button>
      </el-col>
      <el-col :span="1.5" v-if="permissions.canDelete">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5" v-if="permissions.canExport">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>

	  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="punishmentList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="状态" width="70" align="center" prop="stateId">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.stateId == 1" type="info">草稿</el-tag>
            <el-tag v-if="scope.row.stateId == 2" type="success">确认</el-tag>         
          </template>
        </el-table-column>
        
        <el-table-column label="编号" width="140" align="center" prop="serialNo" />
        <el-table-column label="申请部门" width="150" align="center" prop="deptNo" />
        <el-table-column label="填报人" width="120" align="center" prop="userName" />
        <el-table-column label="确认人" width="120" align="center" prop="confirmName" />
        <el-table-column label="确认部门" width="150" align="center" prop="companyCode" />
        <el-table-column label="供应商代码" width="120" align="center" prop="suppId" />
        <el-table-column label="供应商名称" min-width="230" align="center" prop="suppName" />
        <el-table-column label="供应类型" width="80" align="center" prop="suppType">
          <template slot-scope="scope">
            <span v-if="scope.row.suppType == 'M'">物资</span>
            <span v-if="scope.row.suppType == 'S'">服务</span>
            <span v-if="scope.row.suppType == 'P'">工程</span>
          </template>
        </el-table-column>
        <el-table-column label="物资/服务/工程代码" width="150" align="center" prop="itemNo" />
        <el-table-column label="物资/服务/工程小类名称" width="200" align="center" prop="itemName" />
        <el-table-column label="处罚类型" width="80" align="center" prop="punishmentType" :formatter="punishmentTypeFormat" />
        <el-table-column label="处罚事由" min-width="200" align="center" prop="punishmentReason" />
        <el-table-column label="处罚依据" min-width="300" align="center" prop="punishmentBasis" />
        <el-table-column label="处罚措施" min-width="150" align="center" prop="punishmentMeasure" />
        <el-table-column label="事件发生时间" width="100" align="center" prop="happenedTime" :formatter="dateFormat" />
        <el-table-column label="处罚执行时间" width="100" align="center" prop="punishmentTime" :formatter="dateFormat" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          fixed="right"
          width="180"
        >
        <template slot-scope="scope">
          <!-- 修改按钮：只有草稿状态(1)可以修改，且有修改权限 -->
          <el-button
            v-if="scope.row.stateId == 1 && permissions.canEdit"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <!-- 确认按钮：只有草稿状态(1)可以确认，且有确认权限 -->
          <el-button
            v-if="scope.row.stateId == 1 && permissions.canConfirm"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleConfirm(scope.row)"
            >确认</el-button
          >
          <!-- 删除按钮：只有草稿状态(1)可以删除，且有删除权限 -->
          <el-button
            v-if="scope.row.stateId == 1 && permissions.canDelete"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
          <!-- 查看按钮：确认状态(2)只能查看，或者没有修改权限时显示查看 -->
          <el-button
            v-if="scope.row.stateId == 2 || (scope.row.stateId == 1 && !permissions.canEdit)"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
        </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改供应商处罚记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="填报人" prop="userName">
              <el-input
                v-model="form.userName"
                placeholder="请输入填报人"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="确认部门" prop="companyCode">
              <el-input
                v-model="form.companyCode"
                placeholder="请输入确认部门"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请部门" prop="deptNo">
              <el-select
                v-model="form.deptNo"
                clearable
                placeholder="请选择申请部门"
                style="width: 100%"
              >
                <el-option
                  v-for="item in getDepNameList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商代码" prop="suppId">
              <el-input
                v-model="form.suppId"
                placeholder="请输入供应商代码"
              >
                <i slot="suffix" class="el-icon-search search-icon" @click="showSuppInfoDialog"></i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商名称" prop="suppName">
              <el-input v-model="form.suppName" placeholder="请输入供应商名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="处罚类型" prop="punishmentType">
              <el-select v-model="form.punishmentType" placeholder="请选择处罚类型" style="width: 100%">
                <el-option
                  v-for="dict in punishmentTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应类型" prop="suppType">
              <el-select v-model="form.suppType" placeholder="请选择涉及物资、服务或工程" style="width: 100%" @change="handleMaterialOrServiceChange">
                <el-option
                  v-for="option in suppTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          
        </el-row>
        <el-row :gutter="20" v-if="form.suppType === 'M'">
          <el-col :span="12">
            <el-form-item label="物料小类编码" prop="itemNo" :required="true">
              <el-input v-model="form.itemNo" placeholder="请输入物料小类编码">
                <i slot="suffix" class="el-icon-search search-icon" @click="showMaterialInfoDialogForForm"></i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料小类名称" prop="itemName" :required="true">
              <el-input v-model="form.itemName" placeholder="请输入物料小类名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.suppType === 'S'">
          <el-col :span="12">
            <el-form-item label="服务小类编码" prop="itemNo">
              <el-input v-model="form.itemNo" placeholder="请输入服务小类编码">
                <i slot="suffix" class="el-icon-search search-icon" @click="showServiceDialogForForm"></i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务小类名称" prop="itemName">
              <el-input v-model="form.itemName" placeholder="请输入服务小类名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="form.suppType === 'P'">
          <el-col :span="12">
            <el-form-item label="工程小类编码" prop="itemNo">
              <el-input v-model="form.itemNo" placeholder="请输入工程小类编码">
                <i slot="suffix" class="el-icon-search search-icon" @click="showProjectDialogForForm"></i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程小类名称" prop="itemName">
              <el-input v-model="form.itemName" placeholder="请输入工程小类名称" />
            </el-form-item>
          </el-col>
        </el-row>

        

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事件发生时间" prop="happenedTime">
              <el-date-picker
                v-model="form.happenedTime"
                type="date"
                placeholder="请选择事件发生时间"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处罚执行时间" prop="punishmentTime">
              <el-date-picker
                v-model="form.punishmentTime"
                type="date"
                placeholder="请选择处罚执行时间"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="处罚事由" prop="punishmentReason">
              <el-input v-model="form.punishmentReason" type="textarea" placeholder="请输入处罚事由" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="处罚依据" prop="punishmentBasis">
              <div class="basis-input-wrapper">
                <el-input
                  v-model="form.punishmentBasis"
                  type="textarea"
                  placeholder="请选择处罚依据"
                  :rows="3"
                  class="basis-textarea"
                  readonly
                />
                <div class="basis-buttons">
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    @click="showPunishmentBasisDialog"
                    class="basis-btn"
                    size="small"
                    title="选择处罚依据"
                  >
                    选择
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="处罚措施" prop="punishmentMeasure">
              <div class="measure-input-wrapper">
                <!-- 标签显示区域 -->
                <div class="measure-tags-container" v-if="punishmentMeasureTags.length > 0">
                  <el-tag
                    v-for="(tag, index) in punishmentMeasureTags"
                    :key="index"
                    :type="getTagType(tag.type)"
                    closable
                    @close="removeMeasureTag(index)"
                    class="measure-tag"
                  >
                    {{ tag.text }}
                  </el-tag>
                </div>

                <!-- 隐藏的输入框用于存储完整文本 -->
                <el-input
                  v-model="form.punishmentMeasure"
                  type="hidden"
                />

                <!-- 占位符显示区域 -->
                <div
                  v-if="punishmentMeasureTags.length === 0"
                  class="measure-placeholder"
                  @click="showPunishmentMeasureDialog"
                >
                  请选择处罚措施（至少选一个）
                </div>

                <div class="measure-buttons">
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    @click="showPunishmentMeasureDialog"
                    class="measure-btn"
                    size="small"
                    title="选择处罚措施"
                  >
                    选择
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!isViewMode" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>
      </div>
    </el-dialog>

    <!-- 供应商信息查询弹窗 -->
    <supp-info-dialog ref="suppInfoDialog" @select="handleSuppSelect" />

    <!-- 物料信息查询弹窗 -->
    <material-info-dialog ref="materialInfoDialog" @select="handleMaterialSelect" />

    <!-- 服务查询弹窗 -->
    <service-project-dialog ref="serviceDialog" @select="handleServiceSelect" />
    <!-- 项目查询弹窗 -->
    <project-dialog ref="projectDialog" @select="handleProjectSelect" />

    <!-- 处罚措施选择弹窗 -->
    <punishment-measure-dialog ref="punishmentMeasureDialog" @select="handlePunishmentMeasureSelect" />

    <!-- 处罚依据选择弹窗 -->
    <punishment-basis-dialog ref="punishmentBasisDialog" @select="handlePunishmentBasisSelect" />
  </div>
</template>

<script>
import { listPunishment, getPunishment, delPunishment, addPunishment, updatePunishment, exportPunishment, confirmPunishment, getUserCompany, getUserGroup } from "@/api/suppPunishment/punishment";
import { getDepNameList } from "@/api/purchase/purdchaseFactoryStock";
import { addDateRange } from "@/utils/ruoyi";
import SuppInfoDialog from "./suppInfo-module.vue";
import MaterialInfoDialog from "./materialInfo-module.vue";
import ServiceProjectDialog from "./service-module.vue";
import ProjectDialog from "./project-module.vue";
import PunishmentMeasureDialog from "./punishmentMeasure-module.vue";
import PunishmentBasisDialog from "./punishmentBasis-module.vue";


export default {
  name: "Punishment",
  components: {
    SuppInfoDialog,
    MaterialInfoDialog,
    ServiceProjectDialog,
    ProjectDialog,
    PunishmentMeasureDialog,
    PunishmentBasisDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商处罚记录表格数据
      punishmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否为查看模式
      isViewMode: false,
      // 当前选择类型：form-表单选择，query-查询条件选择
      currentSelectType: 'form',
      // 用户分组权限
      userGroup: '',
      // 权限控制
      permissions: {
        canAdd: true,      // 新增权限
        canEdit: true,     // 修改权限
        canDelete: true,   // 删除权限
        canConfirm: true,  // 确认权限
        canExport: true    // 导出权限
      },
      // 处罚类型数据字典
      punishmentTypeOptions: [],
      // 状态数据字典
      statusOptions: [],
      // 物资或服务选项
      suppTypeOptions: [
        { value: 'M', label: '物资' },
        { value: 'S', label: '服务' },
        { value: 'P', label: '工程' }
      ],
      // 服务部门列表
      getDepNameList: [],
      // 事件发生时间范围
      happenedTimeRange: [],
      // 处罚执行时间范围
      punishmentTimeRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        recCreator: null,
        recCreateTime: null,
        recRevisor: null,
        recReviseTime: null,
        userName: null,
        serialNo: null,
        companyCode: null,
        deptNo: null,
        suppId: null,
        suppName: null,
        itemNo: null,
        itemName: null,
        suppType: null,
        punishmentType: null,
        stateId: null,
        punishmentReason: null,
        punishmentBasis: null,
        punishmentMeasure: null
      },
      // 表单参数
      form: {},
      // 处罚措施标签数组
      punishmentMeasureTags: [],
      // 表单校验
      rules: {
        suppId: [
          { required: true, trigger: "blur", message: "请输入供应商代码" }
        ],
        suppName: [
          { required: true, trigger: "blur", message: "请输入供应商名称" }
        ],
        suppType: [
          { required: true, trigger: "change", message: "请选择物资或服务" }
        ],
        punishmentType: [
          { required: true, trigger: "change", message: "请选择处罚类型" }
        ],
        deptNo: [
          { required: true, trigger: "change", message: "请选择申请部门" }
        ],
        itemNo: [
          {
            required: false,
            trigger: "blur",
            validator: (rule, value, callback) => {
              // 只有物资类型才必填
              if (this.form.suppType === 'M') {
                if (!value || value.trim() === '') {
                  callback(new Error('请输入物料小类编码'));
                } else {
                  callback();
                }
              } else {
                // 服务和工程类型不校验必填
                callback();
              }
            }
          }
        ],
        itemName: [
          {
            required: false,
            trigger: "blur",
            validator: (rule, value, callback) => {
              // 只有物资类型才必填
              if (this.form.suppType === 'M') {
                if (!value || value.trim() === '') {
                  callback(new Error('请输入物料小类名称'));
                } else {
                  callback();
                }
              } else {
                // 服务和工程类型不校验必填
                callback();
              }
            }
          }
        ],
        happenedTime: [
          { required: true, trigger: "change", message: "请选择事件发生时间" }
        ],
        punishmentTime: [
          { required: true, trigger: "change", message: "请选择处罚执行时间" }
        ],
        punishmentReason: [
          { required: true, trigger: "blur", message: "请输入处罚事由" }
        ],
        punishmentBasis: [
          { required: true, trigger: "blur", message: "请选择处罚依据" }
        ],
        punishmentMeasure: [
          { required: true, trigger: "blur", message: "请选择处罚措施" }
        ]
      }
    };
  },
  created() {
    this.getDictData();
    this.getDeptList();
    this.getUserGroupPermissions();
    // 不在这里调用getList()，而是在设置默认值后调用
  },
  methods: {
    // 添加日期范围
    addDateRange,
    // 获取字典数据
    getDictData() {
      // 获取处罚类型
      this.getDicts("supp_punishment_type").then((response) => {
        this.punishmentTypeOptions = response.data;
      });
      // 获取状态 - 如果字典不存在，使用手动定义的状态
      this.getDicts("supp_punishment_status").then((response) => {
        this.statusOptions = response.data;
      });
    },
    /** 查询供应商处罚记录列表 */
    getList() {
      this.loading = true;

      console.log('事件发生时间范围:', this.happenedTimeRange);
      console.log('处罚执行时间范围:', this.punishmentTimeRange);

      // 手动构建时间范围参数
      let params = { ...this.queryParams };
      if (!params.params) {
        params.params = {};
      }

      // 处理事件发生时间范围
      if (this.happenedTimeRange && this.happenedTimeRange.length === 2) {
        params.params["happenedTimeBeginTime"] = this.happenedTimeRange[0];
        params.params["happenedTimeEndTime"] = this.happenedTimeRange[1];
      }

      // 处理处罚执行时间范围
      if (this.punishmentTimeRange && this.punishmentTimeRange.length === 2) {
        params.params["punishmentTimeBeginTime"] = this.punishmentTimeRange[0];
        params.params["punishmentTimeEndTime"] = this.punishmentTimeRange[1];
      }

      console.log('最终参数:', params);

      listPunishment(params).then(response => {
        this.punishmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 查询服务部门列表 */
    getDeptList() {
      getDepNameList().then(response => {
        this.getDepNameList = response.data;
        // 直接执行查询，不设置默认申请部门
        this.getList();
      });
    },

    /** 获取用户分组权限 */
    getUserGroupPermissions() {
      getUserGroup().then(response => {
        if (response.code === 200 && response.data) {
          this.userGroup = response.data.userGroup;
          this.setPermissions();
        } else {
          // 默认设置为查阅组权限（最严格）
          this.userGroup = 'query';
          this.setPermissions();
        }
      }).catch(error => {
        console.error('获取用户分组失败:', error);
        // 默认设置为查阅组权限（最严格）
        this.userGroup = 'query';
        this.setPermissions();
      });
    },

    /** 根据用户分组设置权限 */
    setPermissions() {
      switch (this.userGroup) {
        case 'input':
          // 填报组：隐藏确认、导出按钮
          this.permissions = {
            canAdd: true,
            canEdit: true,
            canDelete: true,
            canConfirm: false,
            canExport: false
          };
          break;
        case 'confirm':
          // 确认组：隐藏导出按钮
          this.permissions = {
            canAdd: true,
            canEdit: true,
            canDelete: true,
            canConfirm: true,
            canExport: false
          };
          break;
        case 'query':
          // 查阅组：隐藏新增、修改、确认、删除按钮
          this.permissions = {
            canAdd: false,
            canEdit: false,
            canDelete: false,
            canConfirm: false,
            canExport: true
          };
          // 设置查阅组默认状态为"确认"
          this.setQueryGroupDefaults();
          break;
        case 'manage':
          // 管理组：具备所有功能
          this.permissions = {
            canAdd: true,
            canEdit: true,
            canDelete: true,
            canConfirm: true,
            canExport: true
          };
          break;
        default:
          // 默认为查阅组权限
          this.permissions = {
            canAdd: false,
            canEdit: false,
            canDelete: false,
            canConfirm: false,
            canExport: true
          };
          // 设置查阅组默认状态为"确认"
          this.setQueryGroupDefaults();
      }
    },

    /** 设置查阅组默认值 */
    setQueryGroupDefaults() {
      // 设置状态默认为"确认"（值为2）
      this.queryParams.stateId = '2';
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        recCreator: null,
        recCreateTime: null,
        recRevisor: null,
        recReviseTime: null,
        userName: null,
        serialNo: null,
        companyCode: null,
        deptNo: null,
        suppId: null,
        suppName: null,
        itemNo: null,
        itemName: null,
        punishmentType: null,
        suppType: null,
        happenedTime: null,
        punishmentTime: null,
        punishmentReason: null,
        punishmentBasis: null,
        punishmentMeasure: null
      };
      // 重置处罚措施标签
      this.punishmentMeasureTags = [];
      this.resetForm("form");
    },
    // 处罚类型字典翻译
    punishmentTypeFormat(row, column) {
      return this.selectDictLabel(this.punishmentTypeOptions, row.punishmentType);
    },
    // 状态字典翻译
    stateFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.stateId);
    },
    // 日期格式化：将20250803转换为2025-08-03
    dateFormat(row, column, cellValue) {
      if (!cellValue) return '';
      // 如果已经是正确格式，直接返回
      if (cellValue.includes('-')) return cellValue;
      // 将20250803格式转换为2025-08-03
      if (cellValue.length === 8) {
        const year = cellValue.substring(0, 4);
        const month = cellValue.substring(4, 6);
        const day = cellValue.substring(6, 8);
        return `${year}-${month}-${day}`;
      }
      return cellValue;
    },
    // 日期格式转换：将20250803转换为2025/08/03（用于表单回显）
    convertDateFormat(dateValue) {
      if (!dateValue) return '';
      // 如果已经是正确格式，直接返回
      if (dateValue.includes('/') || dateValue.includes('-')) return dateValue;
      // 将20250803格式转换为2025/08/03
      if (dateValue.length === 8) {
        const year = dateValue.substring(0, 4);
        const month = dateValue.substring(4, 6);
        const day = dateValue.substring(6, 8);
        return `${year}/${month}/${day}`;
      }
      return dateValue;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.happenedTimeRange = [];
      this.punishmentTimeRange = [];
      this.resetForm("queryForm");

      // 如果是查阅组，重置后需要重新设置默认状态
      if (this.userGroup === 'query') {
        this.setQueryGroupDefaults();
      }

      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.isViewMode = false;
      // 自动填入当前登录用户
      if (this.$store.getters.name) {
        // 立即获取登录人信息
        this.getUserCompanyInfo(this.$store.getters.name);
      }
      this.open = true;
      this.title = "添加供应商处罚记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let id;

      if (row && row.id) {
        // 单行修改：从表格行操作按钮调用
        if (row.stateId != 1) {
          this.msgError("只有草稿状态的记录才能修改");
          return;
        }
        id = row.id;
      } else {
        // 批量修改：从顶部按钮调用
        if (!this.ids || this.ids.length === 0) {
          this.msgError("请选择要修改的记录");
          return;
        }
        if (this.ids.length > 1) {
          this.msgError("修改操作只能选择一条记录");
          return;
        }

        // 根据选中的ID查找对应的记录
        const selectedRow = this.punishmentList.find(item => item.id === this.ids[0]);
        if (!selectedRow) {
          this.msgError("无法找到选中的记录");
          return;
        }
        if (selectedRow.stateId != 1) {
          this.msgError("只有草稿状态的记录才能修改");
          return;
        }
        id = selectedRow.id;
      }
      console.log('修改操作 - id:', id);
      console.log('修改操作 - row:', row);

      if (!id) {
        this.msgError("无法获取记录标识，请重新选择");
        return;
      }

      getPunishment(id).then(response => {
        this.form = response.data;
        // 转换日期格式：将20250803转换为2025/08/03
        this.form.happenedTime = this.convertDateFormat(this.form.happenedTime);
        this.form.punishmentTime = this.convertDateFormat(this.form.punishmentTime);
        // 解析处罚措施为标签
        this.parseMeasureTextToTags(this.form.punishmentMeasure);
        this.isViewMode = false;
        this.open = true;
        this.title = "修改供应商处罚记录";
      }).catch(error => {
        console.error('获取详情失败:', error);
        this.msgError("获取记录详情失败");
      });
    },

    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id;
      console.log('查看操作 - id:', id);

      if (!id) {
        this.msgError("无法获取记录标识");
        return;
      }

      getPunishment(id).then(response => {
        this.form = response.data;
        // 转换日期格式：将20250803转换为2025/08/03
        this.form.happenedTime = this.convertDateFormat(this.form.happenedTime);
        this.form.punishmentTime = this.convertDateFormat(this.form.punishmentTime);
        // 解析处罚措施为标签
        this.parseMeasureTextToTags(this.form.punishmentMeasure);
        this.isViewMode = true;
        this.open = true;
        this.title = "查看供应商处罚记录";
      }).catch(error => {
        console.error('获取详情失败:', error);
        this.msgError("获取记录详情失败");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePunishment(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPunishment(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 确认按钮操作 */
    handleConfirm(row) {
      let ids;

      if (row && row.id) {
        // 单行确认：从表格行操作按钮调用
        if (row.stateId != 1) {
          this.msgError("只有草稿状态的记录才能确认");
          return;
        }
        ids = row.id;
      } else {
        // 批量确认：从顶部按钮调用
        if (!this.ids || this.ids.length === 0) {
          this.msgError("请选择要确认的记录");
          return;
        }

        // 检查所有选中记录的状态，只有草稿状态才能确认
        const selectedRows = this.punishmentList.filter(item => this.ids.includes(item.id));
        const hasNonDraftRecord = selectedRows.some(item => item.stateId != 1);
        if (hasNonDraftRecord) {
          this.msgError("只有草稿状态的记录才能确认");
          return;
        }

        ids = this.ids;
      }
      if (!ids || (Array.isArray(ids) && ids.length === 0)) {
        this.msgError("请选择要确认的记录");
        return;
      }

      const confirmIds = Array.isArray(ids) ? ids.join(',') : ids;
      this.$confirm('是否确认选中的处罚记录?', "提示", {
          cancelButtonText: "取消",
          confirmButtonText: "确定",
          type: "warning"
        }).then(() => {
          return confirmPunishment(confirmIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("确认成功");
        })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let ids;

      if (row && row.id) {
        // 单行删除：从表格行操作按钮调用
        if (row.stateId != 1) {
          this.msgError("只有草稿状态的记录才能删除");
          return;
        }
        ids = row.id;
      } else {
        // 批量删除：从顶部按钮调用
        if (!this.ids || this.ids.length === 0) {
          this.msgError("请选择要删除的记录");
          return;
        }

        // 检查所有选中记录的状态，只有草稿状态才能删除
        const selectedRows = this.punishmentList.filter(item => this.ids.includes(item.id));
        const hasNonDraftRecord = selectedRows.some(item => item.stateId != 1);
        if (hasNonDraftRecord) {
          this.msgError("只有草稿状态的记录才能删除");
          return;
        }

        ids = this.ids;
      }
      if (!ids) {
        this.msgError("无法获取记录标识，请重新选择");
        return;
      }

      this.$confirm('是否确认删除?', "警告", {
          cancelButtonText: "取消",
          confirmButtonText: "确定",
          type: "warning"
        }).then(function() {
          return delPunishment(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 显示供应商信息查询弹窗（表单用） */
    showSuppInfoDialog() {
      this.currentSelectType = 'form';
      this.$refs.suppInfoDialog.show();
    },
    /** 显示供应商信息查询弹窗（查询条件用） */
    showSuppInfoDialogForQuery() {
      this.currentSelectType = 'query';
      this.$refs.suppInfoDialog.show();
    },
    /** 处理供应商选择 */
    handleSuppSelect(suppInfo) {
      if (this.currentSelectType === 'form') {
        // 表单中的供应商选择
        this.form.suppId = suppInfo.suppId;
        this.form.suppName = suppInfo.suppName;
      } else if (this.currentSelectType === 'query') {
        // 查询条件中的供应商选择
        this.queryParams.suppId = suppInfo.suppId;
        this.queryParams.suppName = suppInfo.suppName;
      }
    },
    /** 显示物料信息查询弹窗（表单用） */
    showMaterialInfoDialogForForm() {
      this.currentSelectType = 'form';
      this.$refs.materialInfoDialog.show();
    },
    /** 显示物料信息查询弹窗（查询条件用） */
    showMaterialInfoDialogForQuery() {
      this.currentSelectType = 'query';
      this.$refs.materialInfoDialog.show();
    },
    /** 处理物料选择 */
    handleMaterialSelect(materialInfo) {
      if (this.currentSelectType === 'form') {
        // 表单中的物料选择
        this.form.itemNo = materialInfo.itemId;
        this.form.itemName = materialInfo.itemName;
      } else if (this.currentSelectType === 'query') {
        // 查询条件中的物料选择
        this.queryParams.itemNo = materialInfo.itemId;
        this.queryParams.itemName = materialInfo.itemName;
      }
    },
    /** 显示服务查询弹窗（表单用） */
    showServiceDialogForForm() {
      this.currentSelectType = 'form';
      this.$refs.serviceDialog.show();
    },
    /** 显示服务项目查询弹窗（查询条件用） */
    showServiceDialogForQuery() {
      this.currentSelectType = 'query';
      this.$refs.serviceDialog.show();
    },
    /** 处理服务选择 */
    handleServiceSelect(serviceInfo) {
      if (this.currentSelectType === 'form') {
        // 表单中的服务选择
        this.form.itemNo = serviceInfo.serviceNo;
        this.form.itemName = serviceInfo.serviceName;
      } else if (this.currentSelectType === 'query') {
        // 查询条件中的服务选择
        this.queryParams.itemNo = serviceInfo.serviceNo;
        this.queryParams.itemName = serviceInfo.serviceName;
      }
    },
    /** 显示项目查询弹窗（表单用） */
    showProjectDialogForForm() {
      this.currentSelectType = 'form';
      this.$refs.projectDialog.show();
    },
    /** 显示项目查询弹窗（查询条件用） */
    showProjectDialogForQuery() {
      this.currentSelectType = 'query';
      this.$refs.projectDialog.show();
    },
    /** 处理项目选择 */
    handleProjectSelect(projectInfo) {
      if (this.currentSelectType === 'form') {
        // 表单中的项目选择
        this.form.itemNo = projectInfo.projectNo;
        this.form.itemName = projectInfo.projectName;
      } else if (this.currentSelectType === 'query') {
        // 查询条件中的项目选择
        this.queryParams.itemNo = projectInfo.projectNo;
        this.queryParams.itemName = projectInfo.projectName;
      }
    },
    /** 处理服务项目选择 */
    handleServiceProjectSelect(serviceInfo) {
      if (this.currentSelectType === 'form') {
        // 表单中的服务项目选择
        this.form.itemNo = serviceInfo.serviceNo;
        this.form.itemName = serviceInfo.serviceName;
      } else if (this.currentSelectType === 'query') {
        // 查询条件中的服务项目选择
        this.queryParams.itemNo = serviceInfo.serviceNo;
        this.queryParams.itemName = serviceInfo.serviceName;
      }
    },
    /** 处理物资或服务选择变化 */
    handleMaterialOrServiceChange(value) {
      // 清空相关字段
      if (value === 'M' || value === 'S' || value === 'P') {
        // 选择任何类型时，都清空itemNo和itemName，让用户重新选择
        this.form.itemNo = null;
        this.form.itemName = null;
      } else {
        // 未选择时，清空所有相关字段
        this.form.itemNo = null;
        this.form.itemName = null;
      }

      // 切换类型后，清除之前的验证错误信息
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['itemNo', 'itemName']);
        }
      });
    },
    /** 处理查询条件中物资或服务选择变化 */
    handleQueryMaterialOrServiceChange(value) {
      // 清空查询条件中的相关字段
      if (value === 'M' || value === 'S' || value === 'P') {
        // 选择任何类型时，都清空itemNo和itemName，让用户重新选择
        this.queryParams.itemNo = null;
        this.queryParams.itemName = null;
      } else {
        // 未选择时，清空所有相关字段
        this.queryParams.itemNo = null;
        this.queryParams.itemName = null;
      }
    },
    /** 显示处罚措施选择弹窗 */
    showPunishmentMeasureDialog() {
      this.$refs.punishmentMeasureDialog.show(this.form.punishmentMeasure);
    },
    /** 处理处罚措施选择 */
    handlePunishmentMeasureSelect(measureText) {
      this.form.punishmentMeasure = measureText;
      this.parseMeasureTextToTags(measureText);
    },

    /** 显示处罚依据选择弹窗 */
    showPunishmentBasisDialog() {
      console.log('显示处罚依据弹窗，当前值：', this.form.punishmentBasis);
      this.$refs.punishmentBasisDialog.show(this.form.punishmentBasis);
    },

    /** 处理处罚依据选择 */
    handlePunishmentBasisSelect(basisText) {
      this.form.punishmentBasis = basisText;
    },

    /** 解析处罚措施文本为标签 */
    parseMeasureTextToTags(measureText) {
      this.punishmentMeasureTags = [];
      if (!measureText) return;

      const measures = measureText.split('；').filter(item => item.trim());
      measures.forEach(measure => {
        const tag = this.createMeasureTag(measure.trim());
        if (tag) {
          this.punishmentMeasureTags.push(tag);
        }
      });
    },

    /** 创建处罚措施标签 */
    createMeasureTag(measureText) {
      if (measureText.includes('处罚') && measureText.includes('元')) {
        return { text: measureText, type: 'penalty' };
      } else if (measureText === '降级') {
        return { text: measureText, type: 'downgrade' };
      } else if (measureText === '淘汰（禁用）') {
        return { text: measureText, type: 'eliminate' };
      } else if (measureText.includes('暂缓') && measureText.includes('月')) {
        return { text: measureText, type: 'suspend' };
      }
      return null;
    },

    /** 获取标签类型对应的颜色 */
    getTagType(type) {
      const typeMap = {
        'penalty': 'danger',
        'downgrade': 'danger',
        'eliminate': 'danger',
        'suspend': 'danger'
      };
      return typeMap[type] || '';
    },

    /** 删除处罚措施标签 */
    removeMeasureTag(index) {
      this.punishmentMeasureTags.splice(index, 1);
      this.updateMeasureText();
    },

    /** 更新处罚措施文本 */
    updateMeasureText() {
      const measureTexts = this.punishmentMeasureTags.map(tag => tag.text);
      this.form.punishmentMeasure = measureTexts.join('；');
    },


    /** 根据填报人工号获取名字、单位信息 */
    getUserCompanyInfo(userName) {
      getUserCompany(userName).then(response => {
        if (response.code === 200 && response.data) {
          // 从SysUser对象中取rsDeptName作为确认部门和申请部门
          const deptName = response.data.rsDeptName || '';
          this.form.companyCode = deptName;  // 确认部门
          this.form.deptNo = deptName;       // 申请部门，默认与确认部门一致
          this.form.userName = response.data.nickName || '';

        }
      }).catch(error => {
        console.warn('获取单位信息失败:', error);
        // 不显示错误提示，避免影响用户体验
      });
    },



    /** 导出按钮操作 */
    handleExport() {
      this.$confirm('是否确认导出所有供应商处罚记录数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 手动构建时间范围参数
          let params = { ...this.queryParams };
          if (!params.params) {
            params.params = {};
          }

          // 处理事件发生时间范围
          if (this.happenedTimeRange && this.happenedTimeRange.length === 2) {
            params.params["happenedTimeBeginTime"] = this.happenedTimeRange[0];
            params.params["happenedTimeEndTime"] = this.happenedTimeRange[1];
          }

          // 处理处罚执行时间范围
          if (this.punishmentTimeRange && this.punishmentTimeRange.length === 2) {
            params.params["punishmentTimeBeginTime"] = this.punishmentTimeRange[0];
            params.params["punishmentTimeEndTime"] = this.punishmentTimeRange[1];
          }

          return exportPunishment(params);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>

<style scoped>
/* 日期范围选择器宽度与其他输入框一致 */
.el-date-editor.el-range-editor {
  width: 205px !important;
  font-size: 12px !important;
}

/* 调整日期范围选择器内部样式 */
.el-date-editor.el-range-editor .el-range-input {
  width: 32% !important;
  font-size: 12px !important;
  text-align: center;
}

.el-date-editor.el-range-editor .el-range-separator {
  width: 20% !important;
  text-align: center;
  font-size: 12px !important;
  color: #C0C4CC;
}

/* 确保日期范围选择器的高度与其他输入框一致 */
.el-date-editor.el-range-editor.el-input__inner {
  height: 28px !important;
  line-height: 28px !important;
}

/* 缩短右侧留白 */
.app-container {
  padding-right: 5px !important;
}

/* 调整表单容器宽度 */
.el-form--inline {
  max-width: calc(100% - 10px) !important;
}

/* 调整输入框上下间距相等 */
.el-form--inline .el-form-item {
  margin-bottom: 18px !important;
  margin-top: 0 !important;
}

/* 确保第一行没有额外的上边距 */
.el-form--inline .el-form-item:first-child {
  margin-top: 0 !important;
}

/* 缩小输入框标题字体 */
.el-form--inline .el-form-item__label {
  font-size: 10px !important;
}

/* 更强的选择器确保字体样式生效 */
.app-container .el-form--inline .el-form-item .el-form-item__label {
  font-size: 10px !important;
  font-weight: normal !important;
}

/* 弹窗标题居中 */
.el-dialog__header {
  text-align: center !important;
}

.el-dialog__header .el-dialog__title {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
}

/* 更强的选择器确保弹窗标题居中 */
.el-dialog .el-dialog__header .el-dialog__title {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
  margin: 0 auto !important;
}

/* 使用深度选择器确保样式穿透 */
::v-deep .el-dialog__header {
  text-align: center !important;
}

::v-deep .el-dialog__title {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
}

/* 搜索图标样式 */
.search-icon {
  cursor: pointer;
  color: #909399;
  padding: 0 8px;
  transition: color 0.3s;
}

.search-icon:hover {
  color: #409EFF;
}

/* 操作列固定宽度 */
.el-table .fixed-width {
  width: 200px;
  min-width: 200px;
}

/* 小间距 */
.el-table .small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}

/* 处罚措施输入框样式 */
.measure-input-wrapper {
  position: relative;
  min-height: 78px;
  height: 78px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
}

.measure-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 62px;
  max-height: 62px;
  overflow-y: auto;
  align-items: flex-start;
  align-content: flex-start;
}

.measure-tag {
  margin: 0;
  font-size: 14px;
  height: 28px;
  line-height: 26px;
  border-radius: 14px;
  padding: 0 12px;
  cursor: default;
  font-weight: 500;
}

.measure-tag .el-icon-close {
  margin-left: 6px;
  font-size: 12px;
}

.measure-placeholder {
  color: #c0c4cc;
  font-size: 14px;
  line-height: 1.5;
  position: absolute;
  top: 8px;
  left: 12px;
  cursor: pointer;
  user-select: none;
}

.measure-placeholder:hover {
  color: #409EFF;
}

.measure-textarea {
  width: 100%;
}

.measure-buttons {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  display: flex;
  gap: 5px;
}

.measure-btn {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  min-width: 50px;
  height: 28px;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.measure-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 选择按钮样式 */
.measure-btn.el-button--primary {
  background: rgba(64, 158, 255, 0.9);
  border-color: #409EFF;
  color: white;
}

.measure-btn.el-button--primary:hover {
  background: #409EFF;
  border-color: #409EFF;
}

/* 清空按钮样式 */
.measure-btn.el-button--danger {
  background: rgba(245, 108, 108, 0.9);
  border-color: #F56C6C;
  color: white;
}

.measure-btn.el-button--danger:hover {
  background: #F56C6C;
  border-color: #F56C6C;
}

/* 只读处罚措施输入框样式 */
.measure-textarea.el-textarea.is-disabled .el-textarea__inner,
.measure-textarea .el-textarea__inner[readonly] {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: not-allowed;
}

/* 查询表单样式优化 - 每行4个输入框 */
.el-form--inline {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.el-form--inline .el-form-item {
  width: calc(25% - 15px);
  margin-right: 20px;
  margin-bottom: 15px;
  flex: 0 0 auto;
}

/* 每行第4个元素不需要右边距 */
.el-form--inline .el-form-item:nth-child(4n) {
  margin-right: 0;
}

/* 搜索按钮区域单独处理 */
.el-form--inline .el-form-item:last-child {
  width: auto;
  margin-left: auto;
  margin-right: 0;
}

/* 统一输入框宽度 */
.el-form--inline .el-form-item .el-input {
  width: 100%;
}

/* 统一选择框宽度 */
.el-form--inline .el-form-item .el-select {
  width: 100%;
}

/* 统一日期选择器宽度 */
.el-form--inline .el-form-item .el-date-editor {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .el-form--inline .el-form-item {
    width: calc(33.33% - 13px);
  }
  .el-form--inline .el-form-item:nth-child(4n) {
    margin-right: 20px;
  }
  .el-form--inline .el-form-item:nth-child(3n) {
    margin-right: 0;
  }
}

@media (max-width: 1000px) {
  .el-form--inline .el-form-item {
    width: calc(50% - 10px);
  }
  .el-form--inline .el-form-item:nth-child(3n) {
    margin-right: 20px;
  }
  .el-form--inline .el-form-item:nth-child(2n) {
    margin-right: 0;
  }
}

/* 表格容器样式 */
.table-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
}

/* 处罚依据输入框样式 */
.basis-input-wrapper {
  position: relative;
}

.basis-textarea {
  width: 100%;
}

.basis-buttons {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
}

.basis-btn {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  min-width: 50px;
  height: 28px;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.basis-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 选择按钮样式 */
.basis-btn.el-button--primary {
  background: rgba(64, 158, 255, 0.9);
  border-color: #409EFF;
  color: white;
}

.basis-btn.el-button--primary:hover {
  background: #409EFF;
  border-color: #409EFF;
}

/* 滚动条样式优化 */
.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}



/* Element UI 固定列样式优化 */
::v-deep .el-table__fixed-right {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 只读输入框样式 */
.readonly-input ::v-deep .el-input__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #909399 !important;
  cursor: not-allowed !important;
}

.readonly-input ::v-deep .el-input__inner:hover {
  border-color: #e4e7ed !important;
}

.readonly-input ::v-deep .el-input__inner:focus {
  border-color: #e4e7ed !important;
  box-shadow: none !important;
}
</style>
