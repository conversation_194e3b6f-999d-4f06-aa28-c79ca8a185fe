{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dgcb\\supplier\\addSupplyInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dgcb\\supplier\\addSupplyInfo\\index.vue", "mtime": 1756170476826}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Q29udHJhY3QsIGFkZEJhdGNoIH0gZnJvbSAiQC9hcGkvZGdjYi9zdXBwbGllci9zdXBwbHkiOw0KaW1wb3J0IHsgbGlzdEFsbERyaXZlciwgZ2V0WGN0Z0RyaXZlclVzZXJMaXN0LCBnZXRYY3RnRHJpdmVyQ2FyTGlzdCB9IGZyb20gIkAvYXBpL2RnY2IvZHJpdmVyL2RyaXZlciI7DQppbXBvcnQgeyBpc05vdGlmeSwgYWRkTm90aWZ5IH0gZnJvbSAiQC9hcGkvdHJ1Y2svbm90aWZ5L25vdGlmeSI7DQppbXBvcnQgVXBsb2FkSW1hZ2UgZnJvbSAnQC9jb21wb25lbnRzL01vcmVVcGxvYWRJbWFnZSc7Ly/lvJXnlKjnu4Tku7YNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7DQogICAgVXBsb2FkSW1hZ2UsDQogIH0sDQogIG5hbWU6ICJhZGRTdXBwbHlJbmZvIiwNCiAgcHJvcHM6IHsNCiAgICBzdWJtaXRDYW5jZWw6IHsNCiAgICAgIHR5cGU6IEZ1bmN0aW9uLA0KICAgICAgZGVmYXVsdDogbnVsbA0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2VhcmNoRHJpdmVyUXVlcnk6ICcnLA0KICAgICAgZmlsdGVyZWREcml2ZXJPcHRpb25zOiBbXSwNCg0KICAgICAgc2VhcmNoQ2FyUXVlcnk6ICcnLA0KICAgICAgZmlsdGVyZWRDYXJPcHRpb25zOiBbXSwNCg0KICAgICAgaXRlbUhlYWRlclN0eWxlOiB7DQogICAgICAgICJiYWNrZ3JvdW5kLWNvbG9yIjogIiNmZmYiDQogICAgICB9LA0KICAgICAgZHJpdmVyTGlzdDogW10sDQogICAgICBjYXJMaXN0OiBbXSwNCiAgICAgIC8vIOeJqei1hOS/oeaBr+makOiXj+W6j+WIlw0KICAgICAgb3BlbkluZGV4OiBbXSwNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOWQiOWQjOW+heehruiupOmAieaLqemhuQ0KICAgICAgY29udHJhY3RTZWxlY3Rpb246IFtdLA0KICAgICAgLy8g5bey6YCJ5oup5ZCI5ZCM5YiX6KGoDQogICAgICBjb250cmFjdExpc3Q6IFtdLA0KICAgICAgLy8g54mp6LWE5aSa6YCJ5L+h5oGvDQogICAgICBzZWxlY3REYXRhOiB7fSwNCiAgICAgIC8vIOaYvuekuueJqei1hOWIl+ihqA0KICAgICAgLy8gc2hvd1RhYmxlTGlzdDpbXSwNCiAgICAgIC8vIOa6kOWQiOWQjOaVsOaNrg0KICAgICAgY29udHJhY3REYXRhOiBbXSwNCiAgICAgIC8vIOi/h+a7pOaVsOaNrg0KICAgICAgc2hvd0NvbnRyYWN0RGF0YTogW10sDQogICAgICAvLyDliKDpmaTmlbDmja4NCiAgICAgIGRlbEl0ZW1EYXRhOiBbXSwNCiAgICAgIGZvcm06IHsNCiAgICAgICAgY2FyTnVtOiBudWxsLA0KICAgICAgICBzdXBwbHlUaW1lOiBudWxsLA0KICAgICAgICBtZWFzdXJlRmxhZzogMCwNCiAgICAgICAgc3RhdHVzOiAxLA0KICAgICAgICB0ZGdjYjA1TGlzdDogW10NCiAgICAgIH0sDQogICAgICAvLyDlkIjlkIzmkJzntKLmnaHku7YNCiAgICAgIGNvbnRyYWN0U2VhcmNoUGFyYW06IHsNCiAgICAgICAgY29udHJhY3RObzogIiINCiAgICAgIH0sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIC8qKg0KICAgICAgICAgY2FyTnVtOiBbDQogICAgICAgICB7DQogICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgIG1lc3NhZ2U6ICLovabniYzlj7fkuI3og73kuLrnqboiLA0KICAgICAgICAgdHJpZ2dlcjogImJsdXIiDQogICAgICAgICB9LA0KICAgICAgICAgew0KICAgICAgICAgcGF0dGVybjogL14oKFvkuqzmtKXmsqrmuJ3lhoDosavkupHovr3pu5HmuZjnmpbpsoHmlrDoi4/mtZnotaPphILmoYLnlJjmmYvokpnpmZXlkInpl73otLXnsqTpnZLol4/lt53lroHnkLzkvb/pooZdW0EtWl0oKFswLTldezV9W0RGXSl8KFtERl0oW0EtSEotTlAtWjAtOV0pWzAtOV17NH0pKSl8KFvkuqzmtKXmsqrmuJ3lhoDosavkupHovr3pu5HmuZjnmpbpsoHmlrDoi4/mtZnotaPphILmoYLnlJjmmYvokpnpmZXlkInpl73otLXnsqTpnZLol4/lt53lroHnkLzkvb/pooZdW0EtWl1bQS1ISi1OUC1aMC05XXs0fVtBLUhKLU5QLVowLTnmjILlraborabmuK/mvrPkvb/pooZdKSkkLywNCiAgICAgICAgIG1lc3NhZ2U6ICLovabniYzlj7fmoLzlvI/kuI3mraPnoa4iDQogICAgICAgICB9DQogICAgICAgICBdLCovDQogICAgICAgIHN1cHBseVRpbWU6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkvpvotKfml7bpl7TkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIC8vIOa1i+ivleWQiOWQjOaVsOaNrg0KICAgICAgcmVzcG9uc2VEYXRhOiBbew0KICAgICAgICBjb250cmFjdE5vOiAnMScsDQogICAgICAgIGNvbnRyYWN0TmFtZTogJ+WQiOWQjOWQjeensDEnLA0KICAgICAgICB2YWxpZFRpbWU6ICcyMDE2LTA1LTAyJywNCiAgICAgICAgdGRnY2IwM0xpc3Q6IFtdDQogICAgICB9XSwNCiAgICAgIHNob3dEaWFsb2c6IGZhbHNlLA0KICAgICAgaXNBZ3JlZUVuYWJsZWQ6IGZhbHNlLA0KICAgICAgY291bnREb3duOiA1LA0KICAgICAgdGltZXI6IG51bGwsDQogICAgfQ0KICB9LA0KDQogIGNvbXB1dGVkOiB7DQogICAgLy8g6buY6K6k5pi+56S65YmNNTDmnaHvvIzoi6XmnInmkJzntKLvvIzliJnmmL7npLrmkJzntKLlkI7nmoTmlbDmja4NCiAgICBkaXNwbGF5RHJpdmVyTGlzdE9wdGlvbnMoKSB7DQogICAgICByZXR1cm4gdGhpcy5zZWFyY2hEcml2ZXJRdWVyeSA/IHRoaXMuZmlsdGVyZWREcml2ZXJPcHRpb25zIDogdGhpcy5kcml2ZXJMaXN0LnNsaWNlKDAsIDUwKTsNCiAgICB9LA0KICAgIGRpc3BsYXlDYXJMaXN0T3B0aW9ucygpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlYXJjaENhclF1ZXJ5ID8gdGhpcy5maWx0ZXJlZENhck9wdGlvbnMgOiB0aGlzLmNhckxpc3Quc2xpY2UoMCwgNTApOw0KICAgIH0NCiAgfSwNCg0KICBjcmVhdGVkKCkgew0KICAgIGNvbnNvbGUubG9nKCdzaG93RGlhbG9nOicsIHRoaXMuc2hvd0RpYWxvZyk7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogICAgdGhpcy5nZXREcml2ZXJMaXN0KCk7DQogICAgdGhpcy5nZXRDYXJMaXN0KCk7DQoNCiAgICBsZXQgcGFyYW0gPSB7fQ0KICAgIHBhcmFtLmJ1c2luZXNzVHlwZSA9IDE7DQogICAgaXNOb3RpZnkocGFyYW0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsgLy8g5YGH6K6+5o6l5Y+j6L+U5Zue55qE5pWw5o2u5Li6IHRydWUg5oiWIGZhbHNlDQogICAgICAgIHRoaXMuc2hvd0RpYWxvZyA9IHRydWU7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNob3dEaWFsb2cgPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gY2FsbCBpc05vdGlmeTonLCBlcnJvcik7DQogICAgICB0aGlzLnNob3dEaWFsb2cgPSBmYWxzZTsgLy8g5aaC5p6c5o6l5Y+j6LCD55So5aSx6LSl77yM5LiN5pi+56S65by55qGGDQogICAgfSk7DQoNCg0KICAgIHRoaXMudGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICBpZiAodGhpcy5jb3VudERvd24gPiAwKSB7DQogICAgICAgIHRoaXMuY291bnREb3duLS07DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmlzQWdyZWVFbmFibGVkID0gdHJ1ZTsNCiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRpbWVyKTsNCiAgICAgIH0NCiAgICB9LCAxMDAwKTsNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICBjbGVhckludGVydmFsKHRoaXMudGltZXIpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5pCc57Si6L+H5ruk6YC76L6RDQogICAgZmlsdGVyRHJpdmVyRGF0YShxdWVyeSkgew0KICAgICAgdGhpcy5zZWFyY2hEcml2ZXJRdWVyeSA9IHF1ZXJ5Ow0KDQogICAgICBpZiAodGhpcy5zZWFyY2hEcml2ZXJRdWVyeSkgew0KDQogICAgICAgIHRoaXMuZmlsdGVyZWREcml2ZXJPcHRpb25zID0gdGhpcy5kcml2ZXJMaXN0LmZpbHRlcihpdGVtID0+DQogICAgICAgICAgaXRlbS5kcml2ZXJJbmZvLmluY2x1ZGVzKHF1ZXJ5KQ0KICAgICAgICApOw0KICAgICAgfSBlbHNlIHsNCg0KICAgICAgICB0aGlzLmZpbHRlcmVkRHJpdmVyT3B0aW9ucyA9IHRoaXMuZHJpdmVyTGlzdC5zbGljZSgwLCA1MCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaQnOe0oui/h+a7pOmAu+i+kQ0KICAgIGZpbHRlckNhckRhdGEocXVlcnkpIHsNCiAgICAgIHRoaXMuc2VhcmNoQ2FyUXVlcnkgPSBxdWVyeTsNCg0KICAgICAgaWYgKHRoaXMuc2VhcmNoQ2FyUXVlcnkpIHsNCg0KICAgICAgICB0aGlzLmZpbHRlcmVkQ2FyT3B0aW9ucyA9IHRoaXMuY2FyTGlzdC5maWx0ZXIoaXRlbSA9Pg0KICAgICAgICAgIGl0ZW0uY2FyTnVtYmVyLmluY2x1ZGVzKHF1ZXJ5KQ0KICAgICAgICApOw0KICAgICAgfSBlbHNlIHsNCg0KICAgICAgICB0aGlzLmZpbHRlcmVkQ2FyT3B0aW9ucyA9IHRoaXMuY2FyTGlzdC5zbGljZSgwLCA1MCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIG9uQWdyZWVDbGljaygpIHsNCiAgICAgIGlmICh0aGlzLmlzQWdyZWVFbmFibGVkKSB7DQogICAgICAgIHRoaXMuc2hvd0RpYWxvZyA9IGZhbHNlOw0KICAgICAgICAvLyDlnKjov5nph4zlj6/ku6Xmt7vliqDnlKjmiLflkIzmhI/lkI7nmoTpgLvovpENCiAgICAgICAgbGV0IHBhcmFtID0ge307DQogICAgICAgIHBhcmFtLmJ1c2luZXNzVHlwZSA9IDE7DQogICAgICAgIGFkZE5vdGlmeShwYXJhbSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgLy8g5aSE55CGIGFkZE5vdGlmeSDmjqXlj6PmiJDlip/nmoTpgLvovpENCiAgICAgICAgICBjb25zb2xlLmxvZygnYWRkTm90aWZ5IHN1Y2Nlc3M6JywgcmVzcG9uc2UpOw0KICAgICAgICAgIC8vIOWPr+S7peWcqOi/memHjOa3u+WKoOWFtuS7lumAu+i+ke+8jOavlOWmguaPkOekuueUqOaIt+aTjeS9nOaIkOWKnw0KICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgLy8g5aSE55CGIGFkZE5vdGlmeSDmjqXlj6PlpLHotKXnmoTpgLvovpENCiAgICAgICAgICBjb25zb2xlLmVycm9yKCdhZGROb3RpZnkgZmFpbGVkOicsIGVycm9yKTsNCiAgICAgICAgICAvLyDlj6/ku6XlnKjov5nph4zmt7vliqDlhbbku5bpgLvovpHvvIzmr5TlpoLmj5DnpLrnlKjmiLfmk43kvZzlpLHotKUNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBvcGVuTmV3RHJpdmVyV2luZG93KCkgew0KICAgICAgY29uc3QgbmV3V2luZG93VXJsID0gJ2h0dHBzOi8veWR4dC5jaXRpY3N0ZWVsLmNvbTo4MDk5L3RydWNrTWFuYWdlL3hjdGdEcml2ZXJVc2VyJzsgLy8g5pu/5o2i5Li65a6e6ZmF6KaB6Lez6L2s55qE6aG16Z2iIFVSTA0KICAgICAgd2luZG93Lm9wZW4obmV3V2luZG93VXJsLCAnX2JsYW5rJyk7IC8vIOaJk+W8gOaWsOeql+WPo+W5tui3s+i9rOiHs+aMh+WumiBVUkwNCiAgICB9LA0KICAgIG9wZW5OZXdDYXJXaW5kb3coKSB7DQogICAgICBjb25zdCBuZXdXaW5kb3dVcmwgPSAnaHR0cHM6Ly95ZHh0LmNpdGljc3RlZWwuY29tOjgwOTkvdHJ1Y2tNYW5hZ2UveGN0Z0RyaXZlckNhcic7IC8vIOabv+aNouS4uuWunumZheimgei3s+i9rOeahOmhtemdoiBVUkwNCiAgICAgIHdpbmRvdy5vcGVuKG5ld1dpbmRvd1VybCwgJ19ibGFuaycpOyAvLyDmiZPlvIDmlrDnqpflj6Plubbot7Povazoh7PmjIflrpogVVJMDQogICAgfSwNCiAgICAvKiog5p+l6K+i5om55qyh5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsZXQgcGFyYW0gPSAibWVhc3VyZUZsYWdUZW1wPSIgKyB0aGlzLmZvcm0ubWVhc3VyZUZsYWc7DQogICAgICBsaXN0Q29udHJhY3QocGFyYW0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBsZXQgcmVzcG9uc2VEYXRhID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5oYW5kbGVSZXNwb25zZURhdGEocmVzcG9uc2VEYXRhKTsNCiAgICAgIH0pOw0KICAgICAgLy8gdGhpcy5oYW5kbGVSZXNwb25zZURhdGEodGhpcy5yZXNwb25zZURhdGEpOw0KICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgfSwNCiAgICAvKiog5p+l6K+i5Y+45py65L+h5oGv5YiX6KGoICovDQogICAgZ2V0RHJpdmVyTGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAvLyBsaXN0QWxsRHJpdmVyKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAvLyAgIHRoaXMuZHJpdmVyTGlzdCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAvLyAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgLy8gfSk7DQogICAgICBnZXRYY3RnRHJpdmVyVXNlckxpc3QoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kcml2ZXJMaXN0ID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5maWx0ZXJlZERyaXZlck9wdGlvbnMgPSB0aGlzLmRyaXZlckxpc3QsDQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOafpeivouWPuOacuuS/oeaBr+WIl+ihqCAqLw0KICAgIGdldENhckxpc3QoKSB7DQogICAgICBjb25zb2xlLmxvZygic3VjY2VzcyIpOw0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIC8vIGxpc3RBbGxEcml2ZXIoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIC8vICAgdGhpcy5kcml2ZXJMaXN0ID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIC8vICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAvLyB9KTsNCiAgICAgIGdldFhjdGdEcml2ZXJDYXJMaXN0KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2FyTGlzdCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMuZmlsdGVyZWRDYXJPcHRpb25zID0gdGhpcy5jYXJMaXN0Ow0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCg0KDQogICAgaGFuZGxlRHJpdmVyQ2hhbmdlKCkgew0KICAgICAgLy/pgJrov4dkcml2ZXJJZOiOt+WPluWPuOacuuS/oeaBrw0KICAgICAgaWYgKHRoaXMuZm9ybS5kcml2ZXJJZCAhPSBudWxsKSB7DQogICAgICAgIHRoaXMuZHJpdmVyTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLmlkID09IHRoaXMuZm9ybS5kcml2ZXJJZCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLm5hbWUgPSBpdGVtLm5hbWU7DQogICAgICAgICAgICB0aGlzLmZvcm0uaWRDYXJkID0gaXRlbS5pZENhcmQ7DQogICAgICAgICAgICB0aGlzLmZvcm0uY29tcGFueSA9IGl0ZW0uY29tcGFueTsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5waG9uZSA9IGl0ZW0ucGhvbmU7DQogICAgICAgICAgICB0aGlzLmZvcm0ucGhvdG8gPSBpdGVtLnBob3RvOw0KICAgICAgICAgICAgdGhpcy5mb3JtLmZhY2VJbWdMaXN0ID0gaXRlbS5mYWNlSW1nTGlzdDsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5kcml2ZXJMaWNlbnNlSW1ncyA9IGl0ZW0uZHJpdmVyTGljZW5zZUltZ3M7DQogICAgICAgICAgICB0aGlzLmZvcm0udmVoaWNsZUxpY2Vuc2VJbWdzID0gaXRlbS52ZWhpY2xlTGljZW5zZUltZ3M7DQoNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVDYXJDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygic3VjY2VzcyIpOw0KICAgICAgLy/pgJrov4dkcml2ZXJJZOiOt+WPluWPuOacuuS/oeaBrw0KICAgICAgaWYgKHRoaXMuZm9ybS5jYXJJZCAhPSBudWxsKSB7DQogICAgICAgIHRoaXMuY2FyTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLmlkID09IHRoaXMuZm9ybS5jYXJJZCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLmNhck51bWJlciA9IGl0ZW0uY2FyTnVtYmVyOw0KICAgICAgICAgICAgaWYgKGl0ZW0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID09IDEpIHsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9ICLlm73kupQiOw0KICAgICAgICAgICAgfSBlbHNlIGlmIChpdGVtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9PSAyKSB7DQogICAgICAgICAgICAgIHRoaXMuZm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPSAi5Zu95YWtIjsNCiAgICAgICAgICAgIH0gZWxzZSBpZiAoaXRlbS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPT0gMykgew0KICAgICAgICAgICAgICB0aGlzLmZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID0gIuaWsOiDvea6kCI7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLmZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID0gIiI7DQogICAgICAgICAgICB9DQogICAgICAgICAgICAvLyB0aGlzLmZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID0gaXRlbS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHM7DQoNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvL+aAp+WIq+i9rOaNog0KICAgIHNleEZvcm1hdChzZXgpIHsNCiAgICAgIGlmIChzZXggPT0gMCkgew0KICAgICAgICByZXR1cm4gIuacquefpSI7DQogICAgICB9IGVsc2UgaWYgKHNleCA9PSAxKSB7DQogICAgICAgIHJldHVybiAi55S3IjsNCiAgICAgIH0gZWxzZSBpZiAoc2V4ID09IDIpIHsNCiAgICAgICAgcmV0dXJuICLlpbMiOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICIiOw0KICAgICAgfQ0KDQogICAgfSwNCg0KICAgIC8v6L2m6L6G5o6S5pS+5qCH5YeG6L2s5o2iDQogICAgdmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzRm9ybWF0KHZlaGljbGVFbWlzc2lvblN0YW5kYXJkcykgew0KICAgICAgaWYgKHZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9PSAxKSB7DQogICAgICAgIHJldHVybiAi5Zu95LqUIjsNCiAgICAgIH0gZWxzZSBpZiAodmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID09IDIpIHsNCiAgICAgICAgcmV0dXJuICLlm73lha0iOw0KICAgICAgfSBlbHNlIGlmICh2ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPT0gMykgew0KICAgICAgICByZXR1cm4gIuaWsOiDvea6kCI7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gIiI7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuivt+axguaVsOaNrg0KICAgIGhhbmRsZVJlc3BvbnNlRGF0YShyZXNwb25zZSkgew0KICAgICAgdGhpcy5jb250cmFjdERhdGEgPSBbXTsNCiAgICAgIHRoaXMuc2hvd0NvbnRyYWN0RGF0YSA9IFtdOw0KICAgICAgcmVzcG9uc2UuZm9yRWFjaChkYXRhID0+IHsNCiAgICAgICAgbGV0IGNvbnRyYWN0SW5mbyA9IHRoaXMuY29udHJhY3RPYmooKTsNCiAgICAgICAgY29udHJhY3RJbmZvLmNvbnRyYWN0Tm8gPSBkYXRhLmNvbnRyYWN0Tm87DQogICAgICAgIGNvbnRyYWN0SW5mby5jb250cmFjdE5hbWUgPSBkYXRhLmNvbnRyYWN0TmFtZTsNCiAgICAgICAgY29udHJhY3RJbmZvLnZhbGlkVGltZSA9IGRhdGEudmFsaWRUaW1lOw0KICAgICAgICBjb250cmFjdEluZm8uY3VycmVudFBhZ2UgPSAxOw0KICAgICAgICBjb250cmFjdEluZm8ucGFnZVNpemUgPSAxMDsNCiAgICAgICAgY29udHJhY3RJbmZvLnRvdGFsID0gZGF0YS50ZGdjYjAzTGlzdC5sZW5ndGg7DQogICAgICAgIGNvbnRyYWN0SW5mby5pc09wZW4gPSBmYWxzZTsgLy8g5ZCI5ZCM6buY6K6k5pS26LW3DQogICAgICAgIGNvbnRyYWN0SW5mby5pdGVtU2VhcmNoUGFyYW0gPSB7IGl0ZW1ObzogIiIsIGl0ZW1OYW1lOiAiIiB9Ow0KICAgICAgICBpZiAoZGF0YS50ZGdjYjAzTGlzdCAhPSBudWxsKSB7DQogICAgICAgICAgZGF0YS50ZGdjYjAzTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgbGV0IGl0ZW1JbmZvID0gdGhpcy5pdGVtT2JqKCk7DQogICAgICAgICAgICBpdGVtSW5mby5jb250cmFjdE5vID0gZGF0YS5jb250cmFjdE5vOw0KICAgICAgICAgICAgaXRlbUluZm8uY29udHJhY3ROYW1lID0gZGF0YS5jb250cmFjdE5hbWU7DQogICAgICAgICAgICBpdGVtSW5mby5pdGVtTm8gPSBpdGVtLml0ZW1ObzsNCiAgICAgICAgICAgIGl0ZW1JbmZvLml0ZW1OYW1lID0gaXRlbS50ZGdjYjAxLml0ZW1OYW1lOw0KICAgICAgICAgICAgaXRlbUluZm8uaXRlbVNwZWMgPSBpdGVtLnRkZ2NiMDEuaXRlbVNwZWM7DQogICAgICAgICAgICBpdGVtSW5mby5tZWFzdXJlVW5pdCA9IGl0ZW0udGRnY2IwMS5tZWFzdXJlVW5pdDsNCiAgICAgICAgICAgIGl0ZW1JbmZvLmZhY3RvcnlEZXNjID0gaXRlbS50ZGdjYjAxLmZhY3RvcnlEZXNjOw0KICAgICAgICAgICAgaXRlbUluZm8ucHJvZHVjdGlvbkxpbmVEZXNjID0gaXRlbS50ZGdjYjAxLnByb2R1Y3Rpb25MaW5lRGVzYzsNCiAgICAgICAgICAgIGl0ZW1JbmZvLnN0YXR1cyA9IGl0ZW0uc3RhdHVzOw0KICAgICAgICAgICAgY29udHJhY3RJbmZvLml0ZW1MaXN0LnB1c2goaXRlbUluZm8pOw0KICAgICAgICAgIH0pDQogICAgICAgICAgY29udHJhY3RJbmZvLnNlYXJjaEl0ZW1MaXN0ID0gY29udHJhY3RJbmZvLml0ZW1MaXN0LmNvbmNhdCgpOw0KICAgICAgICAgIGNvbnRyYWN0SW5mby5zaG93VGFibGVMaXN0ID0gY29udHJhY3RJbmZvLnNlYXJjaEl0ZW1MaXN0LnNsaWNlKDAsIGNvbnRyYWN0SW5mby5wYWdlU2l6ZSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmNvbnRyYWN0RGF0YS5wdXNoKGNvbnRyYWN0SW5mbyk7DQogICAgICB9KQ0KICAgICAgdGhpcy5zaG93Q29udHJhY3REYXRhID0gdGhpcy5jb250cmFjdERhdGEuY29uY2F0KCk7DQogICAgfSwNCiAgICAvLyDlkIzmraXpgInmi6nkv6Hmga/mlbDmja4NCiAgICBoYW5kbGVEaWZmZXJlbnREYXRhKCkgew0KICAgICAgdGhpcy5jb250cmFjdERhdGEuZm9yRWFjaChjb250cmFjdCA9PiB7DQogICAgICAgIHRoaXMuY29udHJhY3RMaXN0LmZvckVhY2goYyA9PiB7DQogICAgICAgICAgY29uc29sZS5sb2coKQ0KICAgICAgICAgIGlmIChjLmNvbnRyYWN0Tm8gPT0gY29udHJhY3QuY29udHJhY3RObykgew0KICAgICAgICAgICAgY29udHJhY3QuaXRlbUxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgYy5pdGVtTGlzdC5mb3JFYWNoKGkgPT4gew0KICAgICAgICAgICAgICAgIGlmIChpLml0ZW1ObyA9PSBpdGVtLml0ZW1Obykgew0KICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coImJlZm9yIiwgaXRlbSkNCiAgICAgICAgICAgICAgICAgIGl0ZW0uYW1vdW50ID0gaS5hbW91bnQ7DQogICAgICAgICAgICAgICAgICBpdGVtLnN1cHBseVdlaWdodCA9IGkuc3VwcGx5V2VpZ2h0Ow0KICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coImFmdGVyIiwgaXRlbSkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgICB0aGlzLml0ZW1EYXRhQ2hhbmdlRmxhZyA9IDA7DQogICAgfSwNCg0KICAgIGNvbnRyYWN0T2JqKCkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgc3RhdHVzOiAxLA0KICAgICAgICBjb250cmFjdE5vOiBudWxsLA0KICAgICAgICBjb250cmFjdE5hbWU6IG51bGwsDQogICAgICAgIHZhbGlkVGltZTogbnVsbCwNCiAgICAgICAgaXRlbUxpc3Q6IFtdDQogICAgICB9DQogICAgfSwNCg0KICAgIGl0ZW1PYmooKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICBjb250cmFjdE5vOiBudWxsLCAgLy8g5omA5bGe5ZCI5ZCM57yW5Y+3DQogICAgICAgIGNvbnRyYWN0TmFtZTogbnVsbCwgICAvLyDmiYDlsZ7lkIjlkIzlkI0NCiAgICAgICAgaXRlbU5vOiBudWxsLCAgIC8vIOeJqei1hOWQjeensA0KICAgICAgICBpdGVtTmFtZTogbnVsbCwgIC8vIOeJqei1hOWQjeensA0KICAgICAgICBhbW91bnQ6IG51bGwsICAvLyDmlbDph48NCiAgICAgICAgaXRlbVNwZWM6IG51bGwsICAvLyDnianotYTop4TmoLwNCiAgICAgICAgbWVhc3VyZUZsYWc6IG51bGwsIC8vIOaYr+WQpuiuoemHjw0KICAgICAgICBtZWFzdXJlVW5pdDogbnVsbCwgIC8vIOiuoemHj+WNleS9jQ0KICAgICAgICBzdXBwbHlXZWlnaHQ6IG51bGwgLy/kvpvotKfph43ph48NCiAgICAgIH0NCiAgICB9LA0KDQogICAgaXRlbVJlZihjb250cmFjdCkgew0KICAgICAgcmV0dXJuIGNvbnRyYWN0LmNvbnRyYWN0Tm8gKyAiaXRlbVJlZiINCiAgICB9LA0KDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZENvbnRyYWN0KCkgew0KICAgICAgLy8gdGhpcy5yZXNldCgpOw0KICAgICAgLy8gdGhpcy5jb250cmFjdFNlbGVjdGlvbiA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5jb250cmFjdExpc3QpKTsNCiAgICAgIC8vIGlmKHRoaXMuY29udHJhY3RMaXN0Lmxlbmd0aCA+IDApIHRoaXMuY29udHJhY3RTZWxlY3Rpb24gPSB0aGlzLmNvbnRyYWN0TGlzdC5jb25jYXQoKTsNCiAgICAgIGlmICh0aGlzLmNvbnRyYWN0TGlzdC5sZW5ndGggPiAwKSB0aGlzLmhhbmRsZURpZmZlcmVudERhdGEoKTsNCiAgICAgIC8vIOWkhOeQhuW3sumAiQ0KICAgICAgdGhpcy5zaG93Q29udHJhY3REYXRhLmZvckVhY2goYyA9PiB7DQogICAgICAgIGlmIChjLmlzT3Blbikgew0KICAgICAgICAgIHRoaXMudG9nZ2xlU2VsZWN0aW9uKGMpOw0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi6YCJ5oup54mp6LWEIjsNCiAgICB9LA0KDQogICAgLy8g6K6h6YeP5pS55Y+YDQogICAgbWVhc3VyZUNoYW5nZShlKSB7DQogICAgICB0aGlzLmZvcm0ubWVhc3VyZUZsYWcgPSBlOw0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgaWYgKHRoaXMuY29udHJhY3RMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgLy8g5bey6YCJ5oup54mp6LWEDQogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+aUueWPmOaYr+WQpuiuoemHj+Wwhua4hemZpOW9k+WJjeW3sumAieeJqei1hO+8jOaYr+WQpue7p+e7rSI/JywgIuitpuWRiiIsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgIC8vIOehruWumg0KICAgICAgICAgIGNvbnNvbGUubG9nKCLnoa7lrpoiKQ0KICAgICAgICAgIHRoYXQuY29udHJhY3RMaXN0ID0gW107DQogICAgICAgICAgdGhhdC5zZWxlY3REYXRhID0ge307DQogICAgICAgICAgdGhhdC5jb250cmFjdFNlbGVjdGlvbiA9IFtdOw0KICAgICAgICAgIHRoYXQuZ2V0TGlzdCgpOw0KICAgICAgICB9KS5jYXRjaChhY3Rpb24gPT4gew0KICAgICAgICAgIC8vIOWPlua2iA0KICAgICAgICAgIGlmIChhY3Rpb24gPT0gImNhbmNlbCIpIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGFjdGlvbik7DQogICAgICAgICAgICBpZiAodGhpcy5mb3JtLm1lYXN1cmVGbGFnID09IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLm1lYXN1cmVGbGFnID0gMTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuZm9ybS5tZWFzdXJlRmxhZyA9IDA7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgLy8gdGhpcy5yZXNldCgpOw0KICAgIH0sDQoNCiAgICAvLyDpgInmi6nlkIjlkIznoa7lrpoNCiAgICBzdWJtaXRDb250cmFjdCgpIHsNCiAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMuY29udHJhY3RTZWxlY3Rpb24pOw0KICAgICAgdGhpcy5jb250cmFjdExpc3QgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuY29udHJhY3RTZWxlY3Rpb24pKTsNCiAgICAgIC8vIHRoaXMuY29udHJhY3RMaXN0ID0gdGhpcy5jb250cmFjdFNlbGVjdGlvbi5jb25jYXQoKTsNCiAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMuY29udHJhY3RMaXN0KTsNCiAgICAgIHRoaXMuaGFuZGxlRW1wdHlDb250cmFjdCgpOw0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgfSwNCiAgICAvLyDlsZXlvIDlkIjlkIwNCiAgICBvcGVuSXRlbVNlbGVjdGlvbihjb250cmFjdCkgew0KICAgICAgY29udHJhY3QuaXNPcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudG9nZ2xlU2VsZWN0aW9uKGNvbnRyYWN0KTsNCiAgICB9LA0KICAgIC8vIOaUtui1t+WQiOWQjA0KICAgIGNsb3NlSXRlbVNlbGVjdGlvbihjb250cmFjdCkgew0KICAgICAgY29udHJhY3QuaXNPcGVuID0gZmFsc2U7DQogICAgfSwNCiAgICAvLyDlpITnkIblkIjlkIzlsZXlvIDmlLbotbcNCiAgICBoYW5kbGVJdGVtT3BlbkNsb3NlKGNvbnRyYWN0KSB7DQogICAgICBpZiAoIWNvbnRyYWN0LnN0YXR1cykgcmV0dXJuOw0KICAgICAgaWYgKGNvbnRyYWN0LmlzT3Blbikgew0KICAgICAgICB0aGlzLmNsb3NlSXRlbVNlbGVjdGlvbihjb250cmFjdCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm9wZW5JdGVtU2VsZWN0aW9uKGNvbnRyYWN0KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yik5pat54mp6LWE5piv5ZCm5Y+v6YCJDQogICAgaXRlbUlzU2VsZWN0YWJsZShyb3csIGluZGV4KSB7DQogICAgICBpZiAocm93LnN0YXR1cykgew0KICAgICAgICBjb25zb2xlLmxvZygi5pivIik7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmj5DkuqTmuIXljZUNCiAgICBzdWJtaXRTdXBwbHkoKSB7DQogICAgICBpZiAodGhpcy5jb250cmFjdExpc3QubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy5tc2dFcnJvcigi6K+35re75Yqg54mp6LWEIik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmhhbmRsZVN1Ym1pdERhdGEoKSkgew0KICAgICAgICAgICAgLy8gY29uc29sZS5sb2codGhpcy5mb3JtKTsNCiAgICAgICAgICAgIGlmICh0aGlzLmZvcm0ubWVhc3VyZUZsYWcgPT0gMSkgdGhpcy5mb3JtLnN0YXR1cyA9IDExOw0KICAgICAgICAgICAgYWRkQmF0Y2godGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgLy8gY29uc29sZS5sb2cocmVzcG9uc2UpOw0KICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIua3u+WKoOaIkOWKnyIpOw0KICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0Q2FuY2VsKCk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIDsNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5o+Q5Lqk5pWw5o2uDQogICAgaGFuZGxlU3VibWl0RGF0YSgpIHsNCiAgICAgIGxldCBwYXJhbUxpc3QgPSBbXTsNCiAgICAgIGxldCB2ID0gdHJ1ZTsNCiAgICAgIHRoaXMuJHJlZnNbIml0ZW1mb3JtIl0uZm9yRWFjaChmID0+IHsNCiAgICAgICAgZi52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgICAgaWYgKCF2YWxpZCkgew0KICAgICAgICAgICAgdiA9IGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICAgIDsgLy/mlbDph4/pqozor4HkuI3pgJrov4cNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgICBpZiAodikgew0KICAgICAgICB0aGlzLmNvbnRyYWN0TGlzdC5mb3JFYWNoKChjb250cmFjdCwgaW5kZXgpID0+IHsNCiAgICAgICAgICBjb250cmFjdC5pdGVtTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgcGFyYW1MaXN0LnB1c2goaXRlbSk7DQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybS50ZGdjYjA1TGlzdCA9IHBhcmFtTGlzdDsNCiAgICAgIHJldHVybiB2Ow0KICAgIH0sDQoNCiAgICAvLyDnianotYTpgInmi6nmlLnlj5jkuovku7YNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UoZSwgZGF0YSkgew0KICAgICAgbGV0IG5vID0gZGF0YS5jb250cmFjdE5vOw0KICAgICAgaWYgKCF0aGlzLnNlbGVjdERhdGFbYCR7bm99YF0pIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuc2VsZWN0RGF0YSwgYCR7bm99YCwgW10pOw0KICAgICAgfQ0KICAgICAgdGhpcy5zZWxlY3REYXRhW2Ake25vfWBdID0gZTsNCiAgICAgIGxldCBmbGFnID0gZmFsc2U7IC8vIOihqOekuuWQiOWQjOi/mOacqua3u+WKoA0KICAgICAgdGhpcy5jb250cmFjdFNlbGVjdGlvbi5mb3JFYWNoKGNvbnRyYWN0ID0+IHsNCiAgICAgICAgaWYgKGNvbnRyYWN0LmNvbnRyYWN0Tm8gPT0gZGF0YS5jb250cmFjdE5vKSB7DQogICAgICAgICAgZmxhZyA9IHRydWU7DQogICAgICAgICAgLy8g5bey5pyJ5ZCI5ZCM5YiZ5re75Yqg54mp6LWE5L+h5oGvDQogICAgICAgICAgLy8gY29udHJhY3QuaXRlbUxpc3QgPSB0aGlzLnNlbGVjdERhdGFbYCR7bm99YF07DQogICAgICAgICAgY29udHJhY3QuaXRlbUxpc3QgPSBlOw0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgaWYgKCFmbGFnKSB7DQogICAgICAgIC8vIOWQiOWQjOacqua3u+WKoOWImeaWsOWinuWQiOWQjA0KICAgICAgICBsZXQgY29udHJhY3RJbmZvID0gdGhpcy5jb250cmFjdE9iaigpOw0KICAgICAgICBjb250cmFjdEluZm8uY29udHJhY3RObyA9IGRhdGEuY29udHJhY3RObzsNCiAgICAgICAgY29udHJhY3RJbmZvLmNvbnRyYWN0TmFtZSA9IGRhdGEuY29udHJhY3ROYW1lOw0KICAgICAgICBjb250cmFjdEluZm8udmFsaWRUaW1lID0gZGF0YS52YWxpZFRpbWU7DQogICAgICAgIGNvbnRyYWN0SW5mby5tZWFzdXJlRmxhZyA9IGRhdGEubWVhc3VyZUZsYWc7DQogICAgICAgIC8vIGNvbnRyYWN0SW5mby5pdGVtTGlzdCA9IHRoaXMuc2VsZWN0RGF0YVtgJHtub31gXTsNCiAgICAgICAgY29udHJhY3RJbmZvLml0ZW1MaXN0ID0gZTsNCiAgICAgICAgdGhpcy5jb250cmFjdFNlbGVjdGlvbi5wdXNoKGNvbnRyYWN0SW5mbyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOW9k+WJjemhtemAieaLqQ0KICAgIHRvZ2dsZVNlbGVjdGlvbihjb250cmFjdCkgew0KICAgICAgY29uc3Qgbm8gPSBjb250cmFjdC5jb250cmFjdE5vOw0KICAgICAgY29uc3Qgcm93cyA9IHRoaXMuc2VsZWN0RGF0YVtgJHtub31gXTsNCiAgICAgIGNvbnNvbGUubG9nKHJvd3MpDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGlmIChyb3dzKSB7DQogICAgICAgICAgcm93cy5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAodGhpcy5kZWxJdGVtRGF0YS5pbmNsdWRlcyhyb3cpKSB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnNbYCR7bm99YF1bMF0udG9nZ2xlUm93U2VsZWN0aW9uKHJvdywgZmFsc2UpOw0KICAgICAgICAgICAgICByb3cuYW1vdW50ID0gbnVsbDsNCiAgICAgICAgICAgICAgcm93LnN1cHBseVdlaWdodCA9IG51bGw7DQogICAgICAgICAgICAgIHRoaXMuZGVsSXRlbURhdGEuc3BsaWNlKHRoaXMuZGVsSXRlbURhdGEuaW5kZXhPZihyb3cpLCAxKTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnNbYCR7bm99YF1bMF0udG9nZ2xlUm93U2VsZWN0aW9uKHJvdywgdHJ1ZSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kcmVmc1tgJHtub31gXVswXS5jbGVhclNlbGVjdGlvbigpOw0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgY29uc29sZS5sb2coImRlbCIsIHRoaXMuZGVsSXRlbURhdGEpDQogICAgfSwNCg0KICAgIC8vIOeJqei1hOWIl+ihqOW9k+WJjemhteaUueWPmA0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UoY3VycmVudFBhZ2UsIGNvbnRyYWN0KSB7DQogICAgICBjb250cmFjdC5zaG93VGFibGVMaXN0ID0gY29udHJhY3Quc2VhcmNoSXRlbUxpc3Quc2xpY2UoY29udHJhY3QucGFnZVNpemUgKiAoY3VycmVudFBhZ2UgLSAxKSwgY29udHJhY3QucGFnZVNpemUgKiAoY3VycmVudFBhZ2UgLSAxKSArIGNvbnRyYWN0LnBhZ2VTaXplKTsNCiAgICB9LA0KICAgIC8vIOWQiOWQjOaQnOe0og0KICAgIGhhbmRsZUNvbnRyYWN0U2VhcmNoKCkgew0KICAgICAgbGV0IG5ld2xpc3QgPSBbXQ0KICAgICAgaWYgKHRoaXMuY29udHJhY3RTZWFyY2hQYXJhbS5jb250cmFjdE5vID09ICIiKSB7DQogICAgICAgIHRoaXMuc2hvd0NvbnRyYWN0RGF0YSA9IHRoaXMuY29udHJhY3REYXRhLmNvbmNhdCgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jb250cmFjdERhdGEuZm9yRWFjaChjb250cmFjdCA9PiB7DQogICAgICAgICAgaWYgKGNvbnRyYWN0LmNvbnRyYWN0Tm8uaW5jbHVkZXModGhpcy5jb250cmFjdFNlYXJjaFBhcmFtLmNvbnRyYWN0Tm8pKSB7DQogICAgICAgICAgICBuZXdsaXN0LnB1c2goY29udHJhY3QpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5zaG93Q29udHJhY3REYXRhID0gbmV3bGlzdDsNCiAgICAgIH0NCiAgICAgIC8vIOWkhOeQhuW3sumAiQ0KICAgICAgdGhpcy5zaG93Q29udHJhY3REYXRhLmZvckVhY2goYyA9PiB7DQogICAgICAgIGlmIChjLmlzT3Blbikgew0KICAgICAgICAgIHRoaXMudG9nZ2xlU2VsZWN0aW9uKGMpOw0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0Q29udHJhY3RTZWFyY2goKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiY29udHJhY3RTZWFyY2hGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZUNvbnRyYWN0U2VhcmNoKCk7DQogICAgfSwNCg0KICAgIC8v54mp6LWE5pCc57SiDQogICAgaGFuZGxlSXRlbVNlYXJjaChjb250cmFjdCkgew0KICAgICAgY29uc3QgaXRlbVNlYXJjaFBhcmFtID0gY29udHJhY3QuaXRlbVNlYXJjaFBhcmFtOw0KICAgICAgbGV0IG5ld0l0ZW1MaXN0ID0gW107DQogICAgICBpZiAoaXRlbVNlYXJjaFBhcmFtLml0ZW1OYW1lID09ICIiICYmIGl0ZW1TZWFyY2hQYXJhbS5pdGVtTm8gPT0gIiIpIHsNCiAgICAgICAgbmV3SXRlbUxpc3QgPSBjb250cmFjdC5pdGVtTGlzdDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnRyYWN0Lml0ZW1MaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uaXRlbU5hbWUuaW5jbHVkZXMoaXRlbVNlYXJjaFBhcmFtLml0ZW1OYW1lKSAmJiBpdGVtLml0ZW1Oby5pbmNsdWRlcyhpdGVtU2VhcmNoUGFyYW0uaXRlbU5vKSkgew0KICAgICAgICAgICAgbmV3SXRlbUxpc3QucHVzaChpdGVtKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgICBjb250cmFjdC5zZWFyY2hJdGVtTGlzdCA9IG5ld0l0ZW1MaXN0Ow0KICAgICAgY29udHJhY3QudG90YWwgPSBuZXdJdGVtTGlzdC5sZW5ndGg7DQogICAgICBjb250cmFjdC5jdXJyZW50UGFnZSA9IDE7DQogICAgICBjb250cmFjdC5zaG93VGFibGVMaXN0ID0gY29udHJhY3Quc2VhcmNoSXRlbUxpc3Quc2xpY2UoMCwgY29udHJhY3QucGFnZVNpemUpOw0KICAgICAgLy8gdGhpcy50b2dnbGVTZWxlY3Rpb24oY29udHJhY3QpOw0KICAgIH0sDQogICAgLy8g54mp6LWE6YeN572uDQogICAgcmVzZXRJdGVtU2VhcmNoKGNvbnRyYWN0KSB7DQogICAgICBjb25zdCBpdGVtUmVmID0gdGhpcy5pdGVtUmVmKGNvbnRyYWN0KTsNCiAgICAgIHRoaXMuJHJlZnNbYCR7aXRlbVJlZn1gXVswXS5yZXNldEZpZWxkcygpOw0KICAgICAgdGhpcy5oYW5kbGVJdGVtU2VhcmNoKGNvbnRyYWN0KTsNCiAgICB9LA0KDQogICAgLy8gIOiOt+WPluihjOmUrg0KICAgIGdldFJvd0tleXMocm93KSB7DQogICAgICByZXR1cm4gcm93Lml0ZW1ObzsNCiAgICB9LA0KDQogICAgLy8g54mp6LWE5Yig6Zmk54K55Ye75LqL5Lu2DQogICAgaGFuZGxlSXRlbURlbChjb250cmFjdCwgaXRlbU5vKSB7DQogICAgICAvLyDnianotYTliJfooajkuK3liKDpmaQNCiAgICAgIHRoaXMuY29udHJhY3RMaXN0LmZvckVhY2goYyA9PiB7DQogICAgICAgIGlmIChjLmNvbnRyYWN0Tm8gPT0gY29udHJhY3QuY29udHJhY3RObykgew0KICAgICAgICAgIGMuaXRlbUxpc3QuZm9yRWFjaCgoaSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGlmIChpLml0ZW1ObyA9PSBpdGVtTm8pIHsNCiAgICAgICAgICAgICAgYy5pdGVtTGlzdC5zcGxpY2UoaW5kZXgsIDEpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICAvLyDlt7LpgInpobnkuK3liKDpmaQNCiAgICAgIE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0RGF0YSkuZm9yRWFjaChubyA9PiB7DQogICAgICAgIGlmIChubyA9PSBjb250cmFjdC5jb250cmFjdE5vKSB7DQogICAgICAgICAgdGhpcy5zZWxlY3REYXRhW2Ake25vfWBdLmZvckVhY2goaSA9PiB7DQogICAgICAgICAgICBpZiAoaS5pdGVtTm8gPT0gaXRlbU5vKSB7DQogICAgICAgICAgICAgIHRoaXMuZGVsSXRlbURhdGEucHVzaChpKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgdGhpcy5oYW5kbGVFbXB0eUNvbnRyYWN0KCk7DQogICAgfSwNCiAgICAvLyDlpITnkIbnqbrlkIjlkIwNCiAgICBoYW5kbGVFbXB0eUNvbnRyYWN0KCkgew0KICAgICAgdGhpcy5jb250cmFjdExpc3QgPSB0aGlzLmNvbnRyYWN0TGlzdC5maWx0ZXIoYyA9PiBjLml0ZW1MaXN0Lmxlbmd0aCAhPSAwKQ0KICAgIH0NCiAgfSwNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+cA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dgcb/supplier/addSupplyInfo", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-alert title=\"提示：若无法找到适合的物资编码，请联系业务部门人员进行物资新增，并绑定合同！\" type=\"success\" effect=\"dark\"></el-alert>\r\n    <br>\r\n    <el-alert title=\"提示：供货清单仅允许选择“X”结尾代表新件的物资编码，若需配送返修件，请前往返修单页面申请\" type=\"success\" effect=\"dark\">\r\n    </el-alert>\r\n    <br>\r\n    <!-- <div class=\"transition-box\">\r\n\r\n        <div>\r\n            <strong>提示：若无法找到适合的物资编码，请联系业务部门人员进行物资新增，并绑定合同！<br>\r\n                提示：供货清单仅允许选择“X”结尾代表新件的物资编码，若需配送返修件，请前往返修单页面申请<br>\r\n                提示：尊敬的供应商们，根据管理需要，特此通知：自3月11日0点至3月17日24点期间，请注意对计量类物资不进行过磅操作。请将送货单交至物管处后前往分厂卸货。从3月18日0点开始，将恢复原有送货流程。请您通知相关司机人员遵守上述安排。感谢您的配合与理解！\r\n            </strong>\r\n        </div>\r\n\r\n    </div> -->\r\n\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n      <div style=\"width:100%\">\r\n        <!-- <el-form-item label=\"运输车牌号：\" prop=\"carNum\">\r\n            <el-input style=\"width:220px\" v-model=\"form.carNum\" clearable></el-input>\r\n        </el-form-item> -->\r\n\r\n        <el-form-item label=\"货车司机\" prop=\"driverId\" :rules=\"[{ required: true, message: '司机信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"form.driverId\" filterable :filter-method=\"filterDriverData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleDriverChange\">\r\n            <el-option v-for=\"item in filteredDriverOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.driverInfo\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewDriverWindow\">前往新增或修改司机信息\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item class=\"custom-form-label\" label=\"提醒：\">\r\n          <span style=\"color:#0052cc;\">应海关高认要求，入厂货车必须提供货车司机具体信息</span>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"司机名称\" prop=\"name\" v-if=\"form.name != null\">\r\n          <el-input v-model=\"form.name\" placeholder=\"请输入司机名称\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\" v-if=\"form.phone != null\">\r\n          <el-input v-model=\"form.phone\" placeholder=\"请输入手机号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idCard\" v-if=\"form.idCard != null\">\r\n          <el-input v-model=\"form.idCard\" placeholder=\"请输入身份证号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"人脸照片\" prop=\"faceImg\" v-if=\"form.photo != null && form.photo != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.faceImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n                      lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"form.photo\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"驾驶证照片\" prop=\"driverLicenseImgs\" v-if=\"form.driverLicenseImgs != null && form.driverLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.drivingLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"form.driverLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"行驶证照片\" prop=\"vehicleLicenseImgs\" v-if=\"form.vehicleLicenseImgs != null && form.vehicleLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.driverLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"form.vehicleLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"货车\" prop=\"carId\" :rules=\"[{ required: true, message: '货车信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"form.carId\" filterable :filter-method=\"filterCarData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleCarChange\">\r\n            <el-option v-for=\"item in filteredCarOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.carNumber\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewCarWindow\">前往新增或修改货车信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车牌号\" prop=\"carNum\" v-if=\"form.carNumber != null\">\r\n          <el-input v-model=\"form.carNumber\" placeholder=\"请输入车牌号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"车辆排放标准\" prop=\"vehicleEmissionStandards\" v-if=\"form.vehicleEmissionStandards != null\">\r\n                   <el-select v-model=\"form.vehicleEmissionStandards\" placeholder=\"请选择车辆排放标准\" disabled style=\"width:300px\">\r\n            <el-option v-for=\"dict in vehicleEmissionStandardsOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"供货时间：\" prop=\"supplyTime\">\r\n          <el-date-picker value-format=\"yyyy-MM-dd\" v-model=\"form.supplyTime\" type=\"date\" placeholder=\"选择日期\"\r\n            style=\"width:300px\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item class=\"custom-form-label\" label=\"提醒：\">\r\n          <span style=\"color:#0052cc;\">供货时间开始三天内，货车准许入厂，请认真填写供货时间</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否计量：\" prop=\"measureFlag\">\r\n          <el-radio-group @input=\"measureChange\" v-model=\"form.measureFlag\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"物资信息：\">\r\n          <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddContract\">选择物资\r\n              </el-button>\r\n            </el-col>\r\n          </el-row>\r\n          <el-card class=\"box-card contractCard\" v-for=\"(contract, index) in contractList\" :key=\"index\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>合同编号：{{ contract.contractNo }}</span>\r\n            </div>\r\n            <!-- <div v-for=\"item in contract.itemList\" :key=\"item.itemNo\" class=\"text item\">\r\n                    <el-row>\r\n                        <el-col>\r\n                            <div style=\"padding: 6px 0;\">\r\n                                {{ `${item.itemName}（编号：${item.itemNo}，规格：${item.itemSpec})` }}\r\n                                <el-button icon=\"el-icon-delete\" circle type=\"danger\" size=\"mini\" @click=\"handleItemDel(contract,item)\"></el-button>\r\n                            </div>\r\n                        </el-col>\r\n                        <el-col>\r\n                            <el-form ref=\"itemform\" :model=\"item\">\r\n                                <el-form-item v-if=\"!form.measureFlag\" :rules=\"[{ required: true, message: '数量不能为空'}]\"\r\n                                label=\"数量：\" label-width=\"100px\" prop=\"amount\">\r\n                                    <el-input v-model.number=\"item.amount\" style=\"width:220px\"\r\n                    type=\"number\" clearable></el-input> {{ item.measureUnit }}\r\n                                </el-form-item>\r\n                                <el-form-item v-if=\"form.measureFlag\" :rules=\"[{ required: true, message: '数量不能为空'}]\"\r\n                                label=\"数量：\" label-width=\"100px\" prop=\"amount\">\r\n                                    <el-input v-model.number=\"item.amount\" style=\"width:220px\"\r\n                    type=\"number\" clearable></el-input> 件\r\n                                </el-form-item>\r\n                                <el-form-item v-if=\"form.measureFlag\" style=\"margin-top: 10px;\" :rules=\"[{ required: true, message: '数量不能为空'}]\"\r\n                                label=\"净重：\" label-width=\"100px\" prop=\"amount\">\r\n                                    <el-input v-model.number=\"item.supplyWeight\" style=\"width:220px\"\r\n                    type=\"number\" clearable></el-input> {{ item.measureUnit }}\r\n                                </el-form-item>\r\n                            </el-form>\r\n                        </el-col>\r\n                    </el-row>\r\n                </div> -->\r\n            <el-table :data=\"contract.itemList\" :header-cell-style=\"itemHeaderStyle\" style=\"width: 100%\">\r\n              <el-table-column prop=\"itemNo\" label=\"物资编码\" min-width=\"150\">\r\n              </el-table-column>\r\n              <el-table-column prop=\"itemName\" label=\"物资名称\" min-width=\"200\">\r\n              </el-table-column>\r\n              <el-table-column label=\"规格\" min-width=\"150\">\r\n                <template slot-scope=\"scopes\">\r\n                  {{\r\n                    scopes.row.itemSpec == null ?\r\n                      `无(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})` :\r\n                      `${scopes.row.itemSpec}(分厂:${scopes.row.factoryDesc}，产线:${scopes.row.productionLineDesc})`\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 非计量 -->\r\n              <el-table-column v-if=\"!form.measureFlag\" label=\"数量\" min-width=\"150\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-form ref=\"itemform\" :model=\"scope.row\" inline-message>\r\n                      <el-form-item :rules=\"[{ required: true, message: '数量不能为空' }]\" prop=\"amount\">\r\n                        <el-input v-model.number=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 计量 -->\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"数量\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-form ref=\"itemform\" :model=\"scope.row\" inline-message>\r\n                      <el-form-item :rules=\"[{ required: true, message: '数量不能为空' }]\" prop=\"amount\">\r\n                        <el-input v-model.number=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">件</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"净重\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-form ref=\"itemform\" :model=\"scope.row\" inline-message>\r\n                      <el-form-item :rules=\"[{ required: true, message: '净重不能为空' }]\" prop=\"supplyWeight\">\r\n                        <el-input v-model.number=\"scope.row.supplyWeight\" placeholder=\"请输入重量\" type=\"number\" />\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"small\" @click=\"handleItemDel(contract, scope.row.itemNo)\">删除\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </el-card>\r\n        </el-form-item>\r\n        <!-- <el-button v-if=\"contractList.length > 0\" type=\"success\" icon=\"el-icon-check\" size=\"mini\" @click=\"submitSupply\">提交清单</el-button> -->\r\n      </div>\r\n    </el-form>\r\n\r\n    <!-- 添加或修改对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body fullscreen>\r\n      <div>\r\n        <el-form :model=\"contractSearchParam\" ref=\"contractSearchForm\" :inline=\"true\" label-width=\"68px\">\r\n          <el-form-item label=\"合同编号\" prop=\"contractNo\">\r\n            <el-input v-model=\"contractSearchParam.contractNo\" placeholder=\"请输入合同编号\" clearable size=\"small\"\r\n              @keyup.enter.native=\"handleContractSearch\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleContractSearch\">搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetContractSearch\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div style=\"height:634px\" class=\"dialog-box\">\r\n        <el-empty v-if=\"showContractData.length == 0\" description=\"无数据\"></el-empty>\r\n        <div v-else class=\"mybox-card\" v-for=\"(contract, index) in showContractData\" :key=\"index\">\r\n          <div class=\"mybox-header\" @click=\"handleItemOpenClose(contract)\">\r\n            <div class=\"clearfix\">\r\n              <span style=\"font-size:16px;margin-right: 20px;\">合同名称：{{ contract.contractName }}（编号：{{\r\n                contract.contractNo\r\n              }}）</span>\r\n              <!-- <el-tag v-if=\"contract.status == 1\" type=\"success\">可用</el-tag>\r\n              <el-tag v-else type=\"danger\">禁用</el-tag> -->\r\n              <i v-if=\"contract.isOpen\" style=\"float: right; padding: 3px 0\" class=\"el-icon-arrow-up\"></i>\r\n              <i v-else class=\"el-icon-arrow-down\" style=\"float: right; padding: 3px 0\"></i>\r\n              <!-- <el-button  v-if=\"contract.isOpen\" style=\"float: right; padding: 3px 0\" type=\"text\"  @click=\"closeItemSelection(contract)\">收起</el-button>\r\n              <el-button v-else style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"openItemSelection(contract)\">展开</el-button> -->\r\n            </div>\r\n          </div>\r\n          <div class=\"mybox-body\" v-if=\"contract.isOpen\">\r\n            <el-form :model=\"contract.itemSearchParam\" :ref=\"itemRef(contract)\" :inline=\"true\" label-width=\"68px\">\r\n              <el-form-item label=\"物资编号\" prop=\"itemNo\">\r\n                <el-input v-model=\"contract.itemSearchParam.itemNo\" placeholder=\"请输入物资编号\" clearable size=\"small\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"物资名称\" prop=\"itemName\">\r\n                <el-input v-model=\"contract.itemSearchParam.itemName\" placeholder=\"请输入物资名称\" clearable size=\"small\" />\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleItemSearch(contract)\">搜索\r\n                </el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetItemSearch(contract)\">重置\r\n                </el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n            <el-table :ref=\"contract.contractNo\" :data=\"contract.showTableList\" :header-cell-style=\"itemHeaderStyle\"\r\n              style=\"width: 100%\" :row-key=\"getRowKeys\" @selection-change=\"handleSelectionChange($event, contract)\">\r\n              <el-table-column type=\"selection\" width=\"55\" :selectable=\"itemIsSelectable\" :reserve-selection=\"true\">\r\n              </el-table-column>\r\n              <el-table-column prop=\"itemNo\" label=\"物资编码\" min-width=\"150\">\r\n              </el-table-column>\r\n              <el-table-column prop=\"itemName\" label=\"物资名称\" min-width=\"200\">\r\n              </el-table-column>\r\n              <el-table-column label=\"规格\" min-width=\"150\">\r\n\r\n                <template slot-scope=\"scopes\">\r\n                  {{\r\n                    scopes.row.itemSpec == null ?\r\n                      `无(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})` :\r\n                      `${scopes.row.itemSpec}(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})`\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <!-- <el-table-column\r\n              label=\"状态\">\r\n              <template slot-scope=\"props\">\r\n                  <el-tag v-if=\"props.row.status == 1\" type=\"success\">可用</el-tag>\r\n                  <el-tag v-else type=\"danger\">禁用</el-tag>\r\n              </template>\r\n          </el-table-column> -->\r\n              <!-- 非计量 -->\r\n              <el-table-column v-if=\"!form.measureFlag\" label=\"数量\" min-width=\"150\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-input v-model=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 计量 -->\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"数量\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-input v-model=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">件</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"净重\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-input v-model=\"scope.row.supplyWeight\" placeholder=\"请输入重量\" type=\"number\" />\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <el-pagination background small @current-change=\"handleCurrentChange($event, contract, index)\"\r\n              :current-page.sync=\"contract.currentPage\" :page-size=\"contract.pageSize\" layout=\"total,prev, pager, next\"\r\n              :total=\"contract.total\">\r\n            </el-pagination>\r\n            <!-- <el-checkbox-group v-model=\"contract.checkboxGroup\" size=\"small\"> -->\r\n            <!-- <el-checkbox v-for=\"item in contract.itemList\" :key=\"item.itemNo\" :label=\"item.itemName\" @change=\"checkChange($event,item)\" border></el-checkbox> -->\r\n            <!-- </el-checkbox-group> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitContract\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"用户协议\" :visible.sync=\"showDialog\" width=\"60%\" :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\" center :modal=\"false\">\r\n      <div class=\"dialog-body\">\r\n        <p>尊敬的合作伙伴：</p>\r\n        <p>您好！衷心感谢您一直以来对我司的支持，为了强化生产现场管理、保障我司的信息安全，特将有关规定告知如下：</p>\r\n        <ol>\r\n          <li>1、请您在进入我司区域后，包括办公区域、生产区域、研发区域、厂区道路等，<span class=\"highlight\">未经允许，不随意拍照或录像。</span></li>\r\n          <li>2、请您妥善保管工作照或录像，<span class=\"highlight\">不将其随意转发</span>任何无关人员，<span\r\n              class=\"highlight\">不擅自剪辑、传播、上传、发布任何平台。</span>\r\n          </li>\r\n          <li>3、请您在我司许可的指定工作区域内活动，<span class=\"highlight\">不随意走动，全力保护我司的各类信息安全，遵守我司的各项管理规定。</span></li>\r\n          <li>4、请对您公司<span class=\"highlight\">所有派出</span>进入我司区域工作的<span class=\"highlight\">人员进行宣贯，知晓并遵守</span>以上三项规定。</li>\r\n        </ol>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button :disabled=\"!isAgreeEnabled\" @click=\"onAgreeClick\">本人已阅知，并承诺遵守{{ countDown ? '(' + countDown + ')' :\r\n          ''\r\n        }}</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n\r\n\r\n\r\n</template>\r\n\r\n<style scoped>\r\n.mybox-card {\r\n  border-radius: 4px;\r\n  border: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  color: #303133;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);\r\n  margin-bottom: 10px\r\n}\r\n\r\n.mybox-header {\r\n  padding: 18px 20px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.mybox-body {\r\n  border-top: 1px solid #ebeef5;\r\n  /* padding: 20px; */\r\n}\r\n\r\n.el-dialog__body {\r\n  padding: 10px 30px;\r\n}\r\n\r\n.contractCard {\r\n  margin-bottom: 10px\r\n}\r\n\r\n.dialog-box {\r\n  overflow: auto;\r\n}\r\n\r\n.dialog-box::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center\r\n}\r\n\r\n.transition-box {\r\n  grid-auto-columns: 10px;\r\n  width: auto;\r\n  height: auto;\r\n  border-radius: 4px;\r\n  background-color: #CCCCCC;\r\n  text-align: center;\r\n  color: #DC1437;\r\n  padding: 20px 20px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  margin-right: 20px;\r\n  text-align: left;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.image-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n  gap: 10px;\r\n}\r\n\r\n.custom-form-label>>>.el-form-item__label {\r\n  color: #0052cc;\r\n}\r\n\r\n\r\n.highlight {\r\n  color: red;\r\n  font-weight: bold;\r\n}\r\n\r\n.dialog-body p {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.dialog-body ol {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.dialog-body ol li {\r\n  margin-bottom: 5px;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { listContract, addBatch } from \"@/api/dgcb/supplier/supply\";\r\nimport { listAllDriver, getXctgDriverUserList, getXctgDriverCarList } from \"@/api/dgcb/driver/driver\";\r\nimport { isNotify, addNotify } from \"@/api/truck/notify/notify\";\r\nimport UploadImage from '@/components/MoreUploadImage';//引用组件\r\n\r\nexport default {\r\n  components: {\r\n    UploadImage,\r\n  },\r\n  name: \"addSupplyInfo\",\r\n  props: {\r\n    submitCancel: {\r\n      type: Function,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      searchDriverQuery: '',\r\n      filteredDriverOptions: [],\r\n\r\n      searchCarQuery: '',\r\n      filteredCarOptions: [],\r\n\r\n      itemHeaderStyle: {\r\n        \"background-color\": \"#fff\"\r\n      },\r\n      driverList: [],\r\n      carList: [],\r\n      // 物资信息隐藏序列\r\n      openIndex: [],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 合同待确认选择项\r\n      contractSelection: [],\r\n      // 已选择合同列表\r\n      contractList: [],\r\n      // 物资多选信息\r\n      selectData: {},\r\n      // 显示物资列表\r\n      // showTableList:[],\r\n      // 源合同数据\r\n      contractData: [],\r\n      // 过滤数据\r\n      showContractData: [],\r\n      // 删除数据\r\n      delItemData: [],\r\n      form: {\r\n        carNum: null,\r\n        supplyTime: null,\r\n        measureFlag: 0,\r\n        status: 1,\r\n        tdgcb05List: []\r\n      },\r\n      // 合同搜索条件\r\n      contractSearchParam: {\r\n        contractNo: \"\"\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        /**\r\n         carNum: [\r\n         {\r\n         required: true,\r\n         message: \"车牌号不能为空\",\r\n         trigger: \"blur\"\r\n         },\r\n         {\r\n         pattern: /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/,\r\n         message: \"车牌号格式不正确\"\r\n         }\r\n         ],*/\r\n        supplyTime: [\r\n          {\r\n            required: true,\r\n            message: \"供货时间不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n      },\r\n      // 测试合同数据\r\n      responseData: [{\r\n        contractNo: '1',\r\n        contractName: '合同名称1',\r\n        validTime: '2016-05-02',\r\n        tdgcb03List: []\r\n      }],\r\n      showDialog: false,\r\n      isAgreeEnabled: false,\r\n      countDown: 5,\r\n      timer: null,\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 默认显示前50条，若有搜索，则显示搜索后的数据\r\n    displayDriverListOptions() {\r\n      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);\r\n    },\r\n    displayCarListOptions() {\r\n      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);\r\n    }\r\n  },\r\n\r\n  created() {\r\n    console.log('showDialog:', this.showDialog);\r\n    this.getList();\r\n    this.getDriverList();\r\n    this.getCarList();\r\n\r\n    let param = {}\r\n    param.businessType = 1;\r\n    isNotify(param).then(response => {\r\n      if (response.data) { // 假设接口返回的数据为 true 或 false\r\n        this.showDialog = true;\r\n      } else {\r\n        this.showDialog = false;\r\n      }\r\n    }).catch(error => {\r\n      console.error('Failed to call isNotify:', error);\r\n      this.showDialog = false; // 如果接口调用失败，不显示弹框\r\n    });\r\n\r\n\r\n    this.timer = setInterval(() => {\r\n      if (this.countDown > 0) {\r\n        this.countDown--;\r\n      } else {\r\n        this.isAgreeEnabled = true;\r\n        clearInterval(this.timer);\r\n      }\r\n    }, 1000);\r\n  },\r\n  beforeDestroy() {\r\n    clearInterval(this.timer);\r\n  },\r\n  methods: {\r\n    // 搜索过滤逻辑\r\n    filterDriverData(query) {\r\n      this.searchDriverQuery = query;\r\n\r\n      if (this.searchDriverQuery) {\r\n\r\n        this.filteredDriverOptions = this.driverList.filter(item =>\r\n          item.driverInfo.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredDriverOptions = this.driverList.slice(0, 50);\r\n      }\r\n    },\r\n\r\n    // 搜索过滤逻辑\r\n    filterCarData(query) {\r\n      this.searchCarQuery = query;\r\n\r\n      if (this.searchCarQuery) {\r\n\r\n        this.filteredCarOptions = this.carList.filter(item =>\r\n          item.carNumber.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredCarOptions = this.carList.slice(0, 50);\r\n      }\r\n    },\r\n\r\n    onAgreeClick() {\r\n      if (this.isAgreeEnabled) {\r\n        this.showDialog = false;\r\n        // 在这里可以添加用户同意后的逻辑\r\n        let param = {};\r\n        param.businessType = 1;\r\n        addNotify(param).then(response => {\r\n          // 处理 addNotify 接口成功的逻辑\r\n          console.log('addNotify success:', response);\r\n          // 可以在这里添加其他逻辑，比如提示用户操作成功\r\n        }).catch(error => {\r\n          // 处理 addNotify 接口失败的逻辑\r\n          console.error('addNotify failed:', error);\r\n          // 可以在这里添加其他逻辑，比如提示用户操作失败\r\n        });\r\n      }\r\n    },\r\n    openNewDriverWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    openNewCarWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    /** 查询批次列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      let param = \"measureFlagTemp=\" + this.form.measureFlag;\r\n      listContract(param).then(response => {\r\n        let responseData = response.data;\r\n        this.handleResponseData(responseData);\r\n      });\r\n      // this.handleResponseData(this.responseData);\r\n      this.loading = false;\r\n    },\r\n    /** 查询司机信息列表 */\r\n    getDriverList() {\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverUserList().then(response => {\r\n        this.driverList = response.data;\r\n        this.filteredDriverOptions = this.driverList,\r\n          this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 查询司机信息列表 */\r\n    getCarList() {\r\n      console.log(\"success\");\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverCarList().then(response => {\r\n        this.carList = response.data;\r\n        this.filteredCarOptions = this.carList;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n\r\n\r\n    handleDriverChange() {\r\n      //通过driverId获取司机信息\r\n      if (this.form.driverId != null) {\r\n        this.driverList.forEach(item => {\r\n          if (item.id == this.form.driverId) {\r\n            this.form.name = item.name;\r\n            this.form.idCard = item.idCard;\r\n            this.form.company = item.company;\r\n            this.form.phone = item.phone;\r\n            this.form.photo = item.photo;\r\n            this.form.faceImgList = item.faceImgList;\r\n            this.form.driverLicenseImgs = item.driverLicenseImgs;\r\n            this.form.vehicleLicenseImgs = item.vehicleLicenseImgs;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleCarChange() {\r\n      console.log(\"success\");\r\n      //通过driverId获取司机信息\r\n      if (this.form.carId != null) {\r\n        this.carList.forEach(item => {\r\n          if (item.id == this.form.carId) {\r\n            this.form.carNumber = item.carNumber;\r\n            if (item.vehicleEmissionStandards == 1) {\r\n              this.form.vehicleEmissionStandards = \"国五\";\r\n            } else if (item.vehicleEmissionStandards == 2) {\r\n              this.form.vehicleEmissionStandards = \"国六\";\r\n            } else if (item.vehicleEmissionStandards == 3) {\r\n              this.form.vehicleEmissionStandards = \"新能源\";\r\n            } else {\r\n              this.form.vehicleEmissionStandards = \"\";\r\n            }\r\n            // this.form.vehicleEmissionStandards = item.vehicleEmissionStandards;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    //性别转换\r\n    sexFormat(sex) {\r\n      if (sex == 0) {\r\n        return \"未知\";\r\n      } else if (sex == 1) {\r\n        return \"男\";\r\n      } else if (sex == 2) {\r\n        return \"女\";\r\n      } else {\r\n        return \"\";\r\n      }\r\n\r\n    },\r\n\r\n    //车辆排放标准转换\r\n    vehicleEmissionStandardsFormat(vehicleEmissionStandards) {\r\n      if (vehicleEmissionStandards == 1) {\r\n        return \"国五\";\r\n      } else if (vehicleEmissionStandards == 2) {\r\n        return \"国六\";\r\n      } else if (vehicleEmissionStandards == 3) {\r\n        return \"新能源\";\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n\r\n    // 处理请求数据\r\n    handleResponseData(response) {\r\n      this.contractData = [];\r\n      this.showContractData = [];\r\n      response.forEach(data => {\r\n        let contractInfo = this.contractObj();\r\n        contractInfo.contractNo = data.contractNo;\r\n        contractInfo.contractName = data.contractName;\r\n        contractInfo.validTime = data.validTime;\r\n        contractInfo.currentPage = 1;\r\n        contractInfo.pageSize = 10;\r\n        contractInfo.total = data.tdgcb03List.length;\r\n        contractInfo.isOpen = false; // 合同默认收起\r\n        contractInfo.itemSearchParam = { itemNo: \"\", itemName: \"\" };\r\n        if (data.tdgcb03List != null) {\r\n          data.tdgcb03List.forEach(item => {\r\n            let itemInfo = this.itemObj();\r\n            itemInfo.contractNo = data.contractNo;\r\n            itemInfo.contractName = data.contractName;\r\n            itemInfo.itemNo = item.itemNo;\r\n            itemInfo.itemName = item.tdgcb01.itemName;\r\n            itemInfo.itemSpec = item.tdgcb01.itemSpec;\r\n            itemInfo.measureUnit = item.tdgcb01.measureUnit;\r\n            itemInfo.factoryDesc = item.tdgcb01.factoryDesc;\r\n            itemInfo.productionLineDesc = item.tdgcb01.productionLineDesc;\r\n            itemInfo.status = item.status;\r\n            contractInfo.itemList.push(itemInfo);\r\n          })\r\n          contractInfo.searchItemList = contractInfo.itemList.concat();\r\n          contractInfo.showTableList = contractInfo.searchItemList.slice(0, contractInfo.pageSize)\r\n        }\r\n        this.contractData.push(contractInfo);\r\n      })\r\n      this.showContractData = this.contractData.concat();\r\n    },\r\n    // 同步选择信息数据\r\n    handleDifferentData() {\r\n      this.contractData.forEach(contract => {\r\n        this.contractList.forEach(c => {\r\n          console.log()\r\n          if (c.contractNo == contract.contractNo) {\r\n            contract.itemList.forEach(item => {\r\n              c.itemList.forEach(i => {\r\n                if (i.itemNo == item.itemNo) {\r\n                  console.log(\"befor\", item)\r\n                  item.amount = i.amount;\r\n                  item.supplyWeight = i.supplyWeight;\r\n                  console.log(\"after\", item)\r\n                }\r\n              })\r\n            })\r\n          }\r\n        })\r\n      })\r\n      this.itemDataChangeFlag = 0;\r\n    },\r\n\r\n    contractObj() {\r\n      return {\r\n        status: 1,\r\n        contractNo: null,\r\n        contractName: null,\r\n        validTime: null,\r\n        itemList: []\r\n      }\r\n    },\r\n\r\n    itemObj() {\r\n      return {\r\n        contractNo: null,  // 所属合同编号\r\n        contractName: null,   // 所属合同名\r\n        itemNo: null,   // 物资名称\r\n        itemName: null,  // 物资名称\r\n        amount: null,  // 数量\r\n        itemSpec: null,  // 物资规格\r\n        measureFlag: null, // 是否计量\r\n        measureUnit: null,  // 计量单位\r\n        supplyWeight: null //供货重量\r\n      }\r\n    },\r\n\r\n    itemRef(contract) {\r\n      return contract.contractNo + \"itemRef\"\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAddContract() {\r\n      // this.reset();\r\n      // this.contractSelection = JSON.parse(JSON.stringify(this.contractList));\r\n      // if(this.contractList.length > 0) this.contractSelection = this.contractList.concat();\r\n      if (this.contractList.length > 0) this.handleDifferentData();\r\n      // 处理已选\r\n      this.showContractData.forEach(c => {\r\n        if (c.isOpen) {\r\n          this.toggleSelection(c);\r\n        }\r\n      })\r\n      this.open = true;\r\n      this.title = \"选择物资\";\r\n    },\r\n\r\n    // 计量改变\r\n    measureChange(e) {\r\n      this.form.measureFlag = e;\r\n      let that = this;\r\n      if (this.contractList.length > 0) {\r\n        // 已选择物资\r\n        this.$confirm('改变是否计量将清除当前已选物资，是否继续\"?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function () {\r\n          // 确定\r\n          console.log(\"确定\")\r\n          that.contractList = [];\r\n          that.selectData = {};\r\n          that.contractSelection = [];\r\n          that.getList();\r\n        }).catch(action => {\r\n          // 取消\r\n          if (action == \"cancel\") {\r\n            console.log(action);\r\n            if (this.form.measureFlag == 0) {\r\n              this.form.measureFlag = 1;\r\n            } else {\r\n              this.form.measureFlag = 0;\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        this.getList();\r\n      }\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      // this.reset();\r\n    },\r\n\r\n    // 选择合同确定\r\n    submitContract() {\r\n      // console.log(this.contractSelection);\r\n      this.contractList = JSON.parse(JSON.stringify(this.contractSelection));\r\n      // this.contractList = this.contractSelection.concat();\r\n      // console.log(this.contractList);\r\n      this.handleEmptyContract();\r\n      this.open = false;\r\n    },\r\n    // 展开合同\r\n    openItemSelection(contract) {\r\n      contract.isOpen = true;\r\n      this.toggleSelection(contract);\r\n    },\r\n    // 收起合同\r\n    closeItemSelection(contract) {\r\n      contract.isOpen = false;\r\n    },\r\n    // 处理合同展开收起\r\n    handleItemOpenClose(contract) {\r\n      if (!contract.status) return;\r\n      if (contract.isOpen) {\r\n        this.closeItemSelection(contract);\r\n      } else {\r\n        this.openItemSelection(contract);\r\n      }\r\n    },\r\n\r\n    // 判断物资是否可选\r\n    itemIsSelectable(row, index) {\r\n      if (row.status) {\r\n        console.log(\"是\");\r\n        return true;\r\n      } else {\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 提交清单\r\n    submitSupply() {\r\n      if (this.contractList.length == 0) {\r\n        this.msgError(\"请添加物资\");\r\n        return;\r\n      }\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.handleSubmitData()) {\r\n            // console.log(this.form);\r\n            if (this.form.measureFlag == 1) this.form.status = 11;\r\n            addBatch(this.form).then(response => {\r\n              // console.log(response);\r\n              if (response.code == 200) {\r\n                this.msgSuccess(\"添加成功\");\r\n                this.submitCancel();\r\n              }\r\n            })\r\n          }\r\n          ;\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理提交数据\r\n    handleSubmitData() {\r\n      let paramList = [];\r\n      let v = true;\r\n      this.$refs[\"itemform\"].forEach(f => {\r\n        f.validate(valid => {\r\n          if (!valid) {\r\n            v = false\r\n          }\r\n          ; //数量验证不通过\r\n        })\r\n      })\r\n      if (v) {\r\n        this.contractList.forEach((contract, index) => {\r\n          contract.itemList.forEach(item => {\r\n            paramList.push(item);\r\n          })\r\n        })\r\n      }\r\n      this.form.tdgcb05List = paramList;\r\n      return v;\r\n    },\r\n\r\n    // 物资选择改变事件\r\n    handleSelectionChange(e, data) {\r\n      let no = data.contractNo;\r\n      if (!this.selectData[`${no}`]) {\r\n        this.$set(this.selectData, `${no}`, []);\r\n      }\r\n      this.selectData[`${no}`] = e;\r\n      let flag = false; // 表示合同还未添加\r\n      this.contractSelection.forEach(contract => {\r\n        if (contract.contractNo == data.contractNo) {\r\n          flag = true;\r\n          // 已有合同则添加物资信息\r\n          // contract.itemList = this.selectData[`${no}`];\r\n          contract.itemList = e;\r\n        }\r\n      })\r\n      if (!flag) {\r\n        // 合同未添加则新增合同\r\n        let contractInfo = this.contractObj();\r\n        contractInfo.contractNo = data.contractNo;\r\n        contractInfo.contractName = data.contractName;\r\n        contractInfo.validTime = data.validTime;\r\n        contractInfo.measureFlag = data.measureFlag;\r\n        // contractInfo.itemList = this.selectData[`${no}`];\r\n        contractInfo.itemList = e;\r\n        this.contractSelection.push(contractInfo);\r\n      }\r\n    },\r\n\r\n    // 当前页选择\r\n    toggleSelection(contract) {\r\n      const no = contract.contractNo;\r\n      const rows = this.selectData[`${no}`];\r\n      console.log(rows)\r\n      this.$nextTick(() => {\r\n        if (rows) {\r\n          rows.forEach(row => {\r\n            if (this.delItemData.includes(row)) {\r\n              this.$refs[`${no}`][0].toggleRowSelection(row, false);\r\n              row.amount = null;\r\n              row.supplyWeight = null;\r\n              this.delItemData.splice(this.delItemData.indexOf(row), 1);\r\n            } else {\r\n              this.$refs[`${no}`][0].toggleRowSelection(row, true);\r\n            }\r\n          });\r\n        } else {\r\n          this.$refs[`${no}`][0].clearSelection();\r\n        }\r\n      })\r\n      console.log(\"del\", this.delItemData)\r\n    },\r\n\r\n    // 物资列表当前页改变\r\n    handleCurrentChange(currentPage, contract) {\r\n      contract.showTableList = contract.searchItemList.slice(contract.pageSize * (currentPage - 1), contract.pageSize * (currentPage - 1) + contract.pageSize);\r\n    },\r\n    // 合同搜索\r\n    handleContractSearch() {\r\n      let newlist = []\r\n      if (this.contractSearchParam.contractNo == \"\") {\r\n        this.showContractData = this.contractData.concat();\r\n      } else {\r\n        this.contractData.forEach(contract => {\r\n          if (contract.contractNo.includes(this.contractSearchParam.contractNo)) {\r\n            newlist.push(contract);\r\n          }\r\n        })\r\n        this.showContractData = newlist;\r\n      }\r\n      // 处理已选\r\n      this.showContractData.forEach(c => {\r\n        if (c.isOpen) {\r\n          this.toggleSelection(c);\r\n        }\r\n      })\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetContractSearch() {\r\n      this.resetForm(\"contractSearchForm\");\r\n      this.handleContractSearch();\r\n    },\r\n\r\n    //物资搜索\r\n    handleItemSearch(contract) {\r\n      const itemSearchParam = contract.itemSearchParam;\r\n      let newItemList = [];\r\n      if (itemSearchParam.itemName == \"\" && itemSearchParam.itemNo == \"\") {\r\n        newItemList = contract.itemList;\r\n      } else {\r\n        contract.itemList.forEach(item => {\r\n          if (item.itemName.includes(itemSearchParam.itemName) && item.itemNo.includes(itemSearchParam.itemNo)) {\r\n            newItemList.push(item);\r\n          }\r\n        })\r\n      }\r\n      contract.searchItemList = newItemList;\r\n      contract.total = newItemList.length;\r\n      contract.currentPage = 1;\r\n      contract.showTableList = contract.searchItemList.slice(0, contract.pageSize);\r\n      // this.toggleSelection(contract);\r\n    },\r\n    // 物资重置\r\n    resetItemSearch(contract) {\r\n      const itemRef = this.itemRef(contract);\r\n      this.$refs[`${itemRef}`][0].resetFields();\r\n      this.handleItemSearch(contract);\r\n    },\r\n\r\n    //  获取行键\r\n    getRowKeys(row) {\r\n      return row.itemNo;\r\n    },\r\n\r\n    // 物资删除点击事件\r\n    handleItemDel(contract, itemNo) {\r\n      // 物资列表中删除\r\n      this.contractList.forEach(c => {\r\n        if (c.contractNo == contract.contractNo) {\r\n          c.itemList.forEach((i, index) => {\r\n            if (i.itemNo == itemNo) {\r\n              c.itemList.splice(index, 1);\r\n            }\r\n          })\r\n        }\r\n      })\r\n      // 已选项中删除\r\n      Object.keys(this.selectData).forEach(no => {\r\n        if (no == contract.contractNo) {\r\n          this.selectData[`${no}`].forEach(i => {\r\n            if (i.itemNo == itemNo) {\r\n              this.delItemData.push(i);\r\n            }\r\n          })\r\n        }\r\n      })\r\n      this.handleEmptyContract();\r\n    },\r\n    // 处理空合同\r\n    handleEmptyContract() {\r\n      this.contractList = this.contractList.filter(c => c.itemList.length != 0)\r\n    }\r\n  },\r\n}\r\n</script>\r\n"]}]}