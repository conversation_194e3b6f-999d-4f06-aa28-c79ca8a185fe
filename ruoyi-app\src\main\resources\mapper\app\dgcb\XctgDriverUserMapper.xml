<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.XctgDriverUserMapper">
    
    <resultMap type="XctgDriverUser" id="XctgDriverUserResult">
        <result property="id"    column="id"    />
        <result property="openId"    column="open_id"    />
        <result property="name"    column="name"    />
        <result property="gender"    column="gender"    />
        <result property="idCard"    column="id_card"    />
        <result property="company"    column="company"    />
        <result property="phone"    column="phone"    />
        <result property="photo"    column="photo"    />
        <result property="driverLicenseImgs"    column="driver_license_imgs"    />
        <result property="vehicleLicenseImgs"    column="vehicle_license_imgs"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
    </resultMap>

    <sql id="selectXctgDriverUserVo">
        select id, open_id, name, gender, id_card, company, phone, photo, driver_license_imgs, vehicle_license_imgs, create_time, update_time, delete_time from xctg_driver_user
    </sql>

    <select id="selectXctgDriverUserList" parameterType="XctgDriverUser" resultMap="XctgDriverUserResult">
        <include refid="selectXctgDriverUserVo"/>
        <where>  
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="company != null  and company != ''"> and company like concat('%', #{company}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="driverLicenseImgs != null  and driverLicenseImgs != ''"> and driver_license_imgs = #{driverLicenseImgs}</if>
            <if test="vehicleLicenseImgs != null  and vehicleLicenseImgs != ''"> and vehicle_license_imgs = #{vehicleLicenseImgs}</if>
            <if test="deleteTime != null "> and delete_time = #{deleteTime}</if>
            <if test="searchValue != null and searchValue != ''">and name like concat('%', #{searchValue}, '%' ) or id_card like concat('%', #{searchValue}, '%')
            </if>
        </where>
        order by id desc
    </select>
    
    <select id="selectXctgDriverUserById" parameterType="Long" resultMap="XctgDriverUserResult">
        <include refid="selectXctgDriverUserVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertXctgDriverUser" parameterType="XctgDriverUser" useGeneratedKeys="true" keyProperty="id">
        insert into xctg_driver_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null">open_id,</if>
            <if test="name != null">name,</if>
            <if test="gender != null">gender,</if>
            <if test="idCard != null">id_card,</if>
            <if test="company != null">company,</if>
            <if test="phone != null">phone,</if>
            <if test="photo != null">photo,</if>
            <if test="driverLicenseImgs != null">driver_license_imgs,</if>
            <if test="vehicleLicenseImgs != null">vehicle_license_imgs,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null">#{openId},</if>
            <if test="name != null">#{name},</if>
            <if test="gender != null">#{gender},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="company != null">#{company},</if>
            <if test="phone != null">#{phone},</if>
            <if test="photo != null">#{photo},</if>
            <if test="driverLicenseImgs != null">#{driverLicenseImgs},</if>
            <if test="vehicleLicenseImgs != null">#{vehicleLicenseImgs},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
         </trim>
    </insert>

    <update id="updateXctgDriverUser" parameterType="XctgDriverUser">
        update xctg_driver_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="openId != null">open_id = #{openId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="company != null">company = #{company},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="driverLicenseImgs != null">driver_license_imgs = #{driverLicenseImgs},</if>
            <if test="vehicleLicenseImgs != null">vehicle_license_imgs = #{vehicleLicenseImgs},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteXctgDriverUserById" parameterType="Long">
        delete from xctg_driver_user where id = #{id}
    </delete>

    <delete id="deleteXctgDriverUserByIds" parameterType="String">
        delete from xctg_driver_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>