package com.ruoyi.app.leave.service.impl;

import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.domain.*;
import com.ruoyi.app.leave.mapper.DicMapper;
import com.ruoyi.app.leave.mapper.StoreinWwjgMapper;
import com.ruoyi.app.leave.service.IStoreinWwjgService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 外委加工入库Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class StoreinWwjgServiceImpl implements IStoreinWwjgService 
{
    @Autowired
    private StoreinWwjgMapper storeinWwjgMapper;

    @Autowired
    private DicMapper dicMapper;

    private static final Logger log = LoggerFactory.getLogger(StoreinWwjgServiceImpl.class);

    /**
     * 查询外委加工入库
     * 
     * @param id 外委加工入库主键
     * @return 外委加工入库
     */
    @Override
    public StoreinWwjgMeasure selectStoreinWwjgById(Long id)
    {
        return storeinWwjgMapper.selectStoreinWwjgById(id);
    }

    /**
     * 查询外委加工入库列表
     * 
     * @param storeinWwjgMeasure 外委加工入库
     * @return 外委加工入库
     */
    @Override
    public List<StoreinWwjgMeasure> selectStoreinWwjgList(StoreinWwjgMeasure storeinWwjgMeasure)
    {
        return storeinWwjgMapper.selectStoreinWwjgList(storeinWwjgMeasure);
    }

    /**
     * 新增外委加工入库
     * 
     * @param storeinWwjgMeasure 外委加工入库
     * @return 结果
     */
    @Override
    public int insertStoreinWwjg(StoreinWwjgMeasure storeinWwjgMeasure)
    {
        return storeinWwjgMapper.insertStoreinWwjg(storeinWwjgMeasure);
    }

    /**
     * 修改外委加工入库
     * 
     * @param storeinWwjgMeasure 外委加工入库
     * @return 结果
     */
    @Override
    public int updateStoreinWwjg(StoreinWwjgMeasure storeinWwjgMeasure)
    {
        return storeinWwjgMapper.updateStoreinWwjg(storeinWwjgMeasure);
    }

    /**
     * 批量删除外委加工入库
     * 
     * @param ids 需要删除的外委加工入库主键
     * @return 结果
     */
    @Override
    public int deleteStoreinWwjgByIds(Long[] ids)
    {
        return storeinWwjgMapper.deleteStoreinWwjgByIds(ids);
    }

    /**
     * 删除外委加工入库信息
     * 
     * @param id 外委加工入库主键
     * @return 结果
     */
    @Override
    public int deleteStoreinWwjgById(Long id)
    {
        return storeinWwjgMapper.deleteStoreinWwjgById(id);
    }

    /**
     * 根据匹配ID删除外委加工入库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @Override
    public int deleteStoreinWwjgByMatchid(String matchid)
    {
        return storeinWwjgMapper.deleteStoreinWwjgByMatchid(matchid);
    }

    @Override
    public void handleExternalProcessingStockIn(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial, String directSupplyPlanNo) {
        // 更新计量系统数据
        DicMeasure dicMeasure = new DicMeasure();

        dicMeasure.setCarno(leaveTask.getCarNum());
        dicMeasure.setTargettime(nowDate);
        dicMeasure.setMflag("1");
        dicMeasure.setShflag("1");
        dicMeasure.setMsrmemo("炉号：" + leaveTask.getHeatNo() +
                            "钢种：" + leaveTask.getSteelGrade() +
                            "规格：" + leaveTask.getSpec1Length() +
                            "数量：" + leaveTask.getTotals());
        dicMeasure.setMatno(leaveTask.getTotals());
        if (leaveTask.getIsDirectSupply() == 1) {
            dicMeasure.setZgflag("1");
            dicMeasure.setZgplanid(directSupplyPlanNo);
        }

        //todo 根據車牌號更新
        dicMapper.updateDicByCarNo(dicMeasure);

        // 创建外委加工入库记录
        StoreinWwjgMeasure storeinWwjgMeasure = new StoreinWwjgMeasure();
        storeinWwjgMeasure.setValidflag(1L);  // 有效标志
        storeinWwjgMeasure.setCarno(leaveTask.getCarNum());  // 车号
        storeinWwjgMeasure.setOperatype("13");  // 操作类型：外委加工入库
        storeinWwjgMeasure.setPlanid(leavePlan.getPlanNo());  // 计划号
        storeinWwjgMeasure.setSourcename(leavePlan.getSourceCompany());  // 委外加工出厂原发货单位
        storeinWwjgMeasure.setTargetname(leavePlan.getTargetCompany());  // 委外加工出厂原厂外单位
        storeinWwjgMeasure.setMaterialname(leaveTaskMaterial.getMaterialName());  // 物料名称
        storeinWwjgMeasure.setMaterialspec(leaveTaskMaterial.getMaterialSpec());  // 物料规格
        storeinWwjgMeasure.setCreateman(SecurityUtils.getLoginUser().getUser().getNickName());  // 发货人
        storeinWwjgMeasure.setCreatedate(leaveTask.getCreateTime());  // 发货时间
        storeinWwjgMeasure.setHeatno(leaveTask.getHeatNo());  // 炉号
        storeinWwjgMeasure.setSteelgrade(leaveTask.getSteelGrade());  // 钢种
        storeinWwjgMeasure.setSpec(leaveTask.getSpec1Length() != null ? leaveTask.getSpec1Length().toString() : null);  // 规格
        storeinWwjgMeasure.setMatno(leaveTask.getTotals());  // 件数/支数/张数
        storeinWwjgMeasure.setJgstyle(leaveTask.getProcessType());  // 加工类型
        storeinWwjgMeasure.setMemo(leaveTask.getRemark());  // 备注

        // 根据车号获取matchid
        DicMeasure dicQuery = new DicMeasure();
        dicQuery.setCarno(leaveTask.getCarNum());
        List<DicMeasure> dicMeasures = dicMapper.selectDicList(dicQuery);
        if (dicMeasures.size() == 1 && dicMeasures.get(0) != null) {
            storeinWwjgMeasure.setMatchid(dicMeasures.get(0).getMatchid());
        } else {
            log.error("StoreinWwjgServiceImpl.handleExternalProcessingStockIn 委外加工入库对接计量系统异常");
        }

        // 插入外委加工入库记录
        int i = storeinWwjgMapper.insertStoreinWwjg(storeinWwjgMeasure);
        if (i < 0) {
            log.error("StoreinWwjgServiceImpl.handleExternalProcessingStockIn 委外加工入库对接计量系统异常");
        }
    }
} 