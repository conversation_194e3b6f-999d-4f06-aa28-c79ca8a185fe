<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.DicMapper">

    <resultMap id="DicResult" type="DicMeasure">
        <result property="id" column="ID"/>
        <result property="validflag" column="VALIDFLAG"/>
        <result property="type" column="TYPE"/>
        <result property="icNo" column="IC_NO"/>
        <result property="carno" column="CARNO"/>
        <result property="driver" column="DRIVER"/>
        <result property="drivercode" column="DRIVERCODE"/>
        <result property="fromman" column="FROMMAN"/>
        <result property="frommancode" column="FROMMANCODE"/>
        <result property="fromdata" column="FROMDATA"/>
        <result property="deposit" column="DEPOSIT"/>
        <result property="backman" column="BACKMAN"/>
        <result property="backmancode" column="BACKMANCODE"/>
        <result property="backdate" column="BACKDATE"/>
        <result property="unitcode" column="UNITCODE"/>
        <result property="unitname" column="UNITNAME"/>
        <result property="unittime" column="UNITTIME"/>
        <result property="unitman" column="UNITMAN"/>
        <result property="tcode" column="TCODE"/>
        <result property="tname" column="TNAME"/>
        <result property="createman" column="CREATEMAN"/>
        <result property="createdate" column="CREATEDATE"/>
        <result property="operamemo" column="OPERAMEMO"/>
        <result property="cardLevel" column="CARD_LEVEL"/>
        <result property="icmode" column="ICMODE"/>
        <result property="customercode" column="CUSTOMERCODE"/>
        <result property="customername" column="CUSTOMERNAME"/>
        <result property="uptimestamp" column="UPTIMESTAMP"/>
        <result property="uptime" column="UPTIME"/>
        <result property="cardState" column="CARD_STATE"/>
        <result property="operatype" column="OPERATYPE"/>
        <result property="matchid" column="MATCHID"/>
        <result property="planid" column="PLANID"/>
        <result property="orderno" column="ORDERNO"/>
        <result property="taskcode" column="TASKCODE"/>
        <result property="materialcode" column="MATERIALCODE"/>
        <result property="materialspeccode" column="MATERIALSPECCODE"/>
        <result property="shipcode" column="SHIPCODE"/>
        <result property="sourcecode" column="SOURCECODE"/>
        <result property="sourcetime" column="SOURCETIME"/>
        <result property="targetcode" column="TARGETCODE"/>
        <result property="targettime" column="TARGETTIME"/>
        <result property="gross" column="GROSS"/>
        <result property="grosstime" column="GROSSTIME"/>
        <result property="grossweighid" column="GROSSWEIGHID"/>
        <result property="grossweigh" column="GROSSWEIGH"/>
        <result property="grossoperatorid" column="GROSSOPERATORID"/>
        <result property="grossoperator" column="GROSSOPERATOR"/>
        <result property="tare" column="TARE"/>
        <result property="taretime" column="TARETIME"/>
        <result property="tareweighid" column="TAREWEIGHID"/>
        <result property="tareweigh" column="TAREWEIGH"/>
        <result property="tareoperatorid" column="TAREOPERATORID"/>
        <result property="tareoperator" column="TAREOPERATOR"/>
        <result property="deduction" column="DEDUCTION"/>
        <result property="deductiontime" column="DEDUCTIONTIME"/>
        <result property="deductionunit" column="DEDUCTIONUNIT"/>
        <result property="entergate" column="ENTERGATE"/>
        <result property="mcount" column="MCOUNT"/>
        <result property="bgross" column="BGROSS"/>
        <result property="btare" column="BTARE"/>
        <result property="bgrosstime" column="BGROSSTIME"/>
        <result property="bgrossweighid" column="BGROSSWEIGHID"/>
        <result property="bgrossweigh" column="BGROSSWEIGH"/>
        <result property="btaretime" column="BTARETIME"/>
        <result property="btareweighid" column="BTAREWEIGHID"/>
        <result property="btareweigh" column="BTAREWEIGH"/>
        <result property="bmatchid" column="BMATCHID"/>
        <result property="barchcode" column="BARCHCODE"/>
        <result property="dflag" column="DFLAG"/>
        <result property="bflag" column="BFLAG"/>
        <result property="fgbflag" column="FGBFLAG"/>
        <result property="fgpriceflag" column="FGPRICEFLAG"/>
        <result property="deduction3" column="DEDUCTION3"/>
        <result property="deduction4" column="DEDUCTION4"/>
        <result property="deduction2" column="DEDUCTION2"/>
        <result property="deduction1" column="DEDUCTION1"/>
        <result property="deduction5" column="DEDUCTION5"/>
        <result property="storeroom" column="STOREROOM"/>
        <result property="validman" column="VALIDMAN"/>
        <result property="validman2" column="VALIDMAN2"/>
        <result property="mflag" column="MFLAG"/>
        <result property="rfidId" column="RFID_ID"/>
        <result property="rfidNo" column="RFID_NO"/>
        <result property="entertime" column="ENTERTIME"/>
        <result property="fgprice1" column="FGPRICE1"/>
        <result property="materialname" column="MATERIALNAME"/>
        <result property="materialspec" column="MATERIALSPEC"/>
        <result property="ship" column="SHIP"/>
        <result property="sourcename" column="SOURCENAME"/>
        <result property="targetname" column="TARGETNAME"/>
        <result property="fgprice2" column="FGPRICE2"/>
        <result property="fgsignpricelogid" column="FGSIGNPRICELOGID"/>
        <result property="fgphoto" column="FGPHOTO"/>
        <result property="shflag" column="SHFLAG"/>
        <result property="tarehour" column="TAREHOUR"/>
        <result property="plancount" column="PLANCOUNT"/>
        <result property="tarelogid" column="TARELOGID"/>
        <result property="grosslogid" column="GROSSLOGID"/>
        <result property="bgrosslogid" column="BGROSSLOGID"/>
        <result property="btarelogid" column="BTARELOGID"/>
        <result property="msrmemo" column="MSRMEMO"/>
        <result property="materialcount" column="MATERIALCOUNT"/>
        <result property="matno" column="MATNO"/>
        <result property="bbflag" column="BBFLAG"/>
        <result property="bbman" column="BBMAN"/>
        <result property="zgplanid" column="ZGPLANID"/>
        <result property="zgflag" column="ZGFLAG"/>
        <result property="zgtargettime" column="ZGTARGETTIME"/>
        <result property="btareoperatorid" column="BTAREOPERATORID"/>
        <result property="btareoperator" column="BTAREOPERATOR"/>
        <result property="bgrossoperatorid" column="BGROSSOPERATORID"/>
        <result property="bgrossoperator" column="BGROSSOPERATOR"/>
        <result property="zoushu" column="ZOUSHU"/>
        <result property="orderld" column="ORDERLD"/>
        <result property="fgtype" column="FGTYPE"/>
        <result property="fgcbtype" column="FGCBTYPE"/>
    </resultMap>

    <sql id="selectDicVo">
        select ID, VALIDFLAG, TYPE, IC_NO, CARNO, DRIVER, DRIVERCODE, FROMMAN, FROMMANCODE, FROMDATA, DEPOSIT, BACKMAN, BACKMANCODE, BACKDATE, UNITCODE, UNITNAME, UNITTIME, UNITMAN, TCODE, TNAME, CREATEMAN, CREATEDATE, OPERAMEMO, CARD_LEVEL, ICMODE, CUSTOMERCODE, CUSTOMERNAME, UPTIMESTAMP, UPTIME, CARD_STATE, OPERATYPE, MATCHID, PLANID, ORDERNO, TASKCODE, MATERIALCODE, MATERIALSPECCODE, SHIPCODE, SOURCECODE, SOURCETIME, TARGETCODE, TARGETTIME, GROSS, GROSSTIME, GROSSWEIGHID, GROSSWEIGH, GROSSOPERATORID, GROSSOPERATOR, TARE, TARETIME, TAREWEIGHID, TAREWEIGH, TAREOPERATORID, TAREOPERATOR, DEDUCTION, DEDUCTIONTIME, DEDUCTIONUNIT, ENTERGATE, MCOUNT, BGROSS, BTARE, BGROSSTIME, BGROSSWEIGHID, BGROSSWEIGH, BTARETIME, BTAREWEIGHID, BTAREWEIGH, BMATCHID, BARCHCODE, DFLAG, BFLAG, FGBFLAG, FGPRICEFLAG, DEDUCTION3, DEDUCTION4, DEDUCTION2, DEDUCTION1, DEDUCTION5, STOREROOM, VALIDMAN, VALIDMAN2, MFLAG, RFID_ID, RFID_NO, ENTERTIME, FGPRICE1, MATERIALNAME, MATERIALSPEC, SHIP, SOURCENAME, TARGETNAME, FGPRICE2, FGSIGNPRICELOGID, FGPHOTO, SHFLAG, TAREHOUR, PLANCOUNT, TARELOGID, GROSSLOGID, BGROSSLOGID, BTARELOGID, MSRMEMO, MATERIALCOUNT, MATNO, BBFLAG, BBMAN, ZGPLANID, ZGFLAG, ZGTARGETTIME, BTAREOPERATORID, BTAREOPERATOR, BGROSSOPERATORID, BGROSSOPERATOR, ZOUSHU, ORDERLD, FGTYPE, FGCBTYPE
        from D_IC_T
    </sql>

    <select id="selectDicList" parameterType="DicMeasure" resultMap="DicResult">
        <include refid="selectDicVo"/>
        <where>
            <if test="id != null"> and ID = #{id}</if>
            <if test="validflag != null"> and VALIDFLAG = #{validflag}</if>
            <if test="type != null"> and TYPE = #{type}</if>
            <if test="icNo != null and icNo != ''"> and IC_NO = #{icNo}</if>
            <if test="carno != null and carno != ''"> and CARNO = #{carno}</if>
            <if test="driver != null and driver != ''"> and DRIVER = #{driver}</if>
            <if test="drivercode != null and drivercode != ''"> and DRIVERCODE = #{drivercode}</if>
            <if test="fromman != null and fromman != ''"> and FROMMAN = #{fromman}</if>
            <if test="frommancode != null and frommancode != ''"> and FROMMANCODE = #{frommancode}</if>
            <if test="fromdata != null"> and FROMDATA = #{fromdata}</if>
            <if test="deposit != null"> and DEPOSIT = #{deposit}</if>
            <if test="backman != null and backman != ''"> and BACKMAN = #{backman}</if>
            <if test="backmancode != null and backmancode != ''"> and BACKMANCODE = #{backmancode}</if>
            <if test="backdate != null"> and BACKDATE = #{backdate}</if>
            <if test="unitcode != null and unitcode != ''"> and UNITCODE = #{unitcode}</if>
            <if test="unitname != null and unitname != ''"> and UNITNAME = #{unitname}</if>
            <if test="unittime != null"> and UNITTIME = #{unittime}</if>
            <if test="unitman != null and unitman != ''"> and UNITMAN = #{unitman}</if>
            <if test="tcode != null and tcode != ''"> and TCODE = #{tcode}</if>
            <if test="tname != null and tname != ''"> and TNAME = #{tname}</if>
            <if test="createman != null and createman != ''"> and CREATEMAN = #{createman}</if>
            <if test="createdate != null"> and CREATEDATE = #{createdate}</if>
            <if test="operamemo != null and operamemo != ''"> and OPERAMEMO = #{operamemo}</if>
            <if test="cardLevel != null"> and CARD_LEVEL = #{cardLevel}</if>
            <if test="icmode != null"> and ICMODE = #{icmode}</if>
            <if test="customercode != null and customercode != ''"> and CUSTOMERCODE = #{customercode}</if>
            <if test="customername != null and customername != ''"> and CUSTOMERNAME = #{customername}</if>
            <if test="uptimestamp != null"> and UPTIMESTAMP = #{uptimestamp}</if>
            <if test="uptime != null"> and UPTIME = #{uptime}</if>
            <if test="cardState != null and cardState != ''"> and CARD_STATE = #{cardState}</if>
            <if test="operatype != null"> and OPERATYPE = #{operatype}</if>
            <if test="matchid != null and matchid != ''"> and MATCHID = #{matchid}</if>
            <if test="planid != null and planid != ''"> and PLANID = #{planid}</if>
            <if test="orderno != null and orderno != ''"> and ORDERNO = #{orderno}</if>
            <if test="taskcode != null and taskcode != ''"> and TASKCODE = #{taskcode}</if>
            <if test="materialcode != null and materialcode != ''"> and MATERIALCODE = #{materialcode}</if>
            <if test="materialspeccode != null and materialspeccode != ''"> and MATERIALSPECCODE = #{materialspeccode}</if>
            <if test="shipcode != null and shipcode != ''"> and SHIPCODE = #{shipcode}</if>
            <if test="sourcecode != null and sourcecode != ''"> and SOURCECODE = #{sourcecode}</if>
            <if test="sourcetime != null"> and SOURCETIME = #{sourcetime}</if>
            <if test="targetcode != null and targetcode != ''"> and TARGETCODE = #{targetcode}</if>
            <if test="targettime != null"> and TARGETTIME = #{targettime}</if>
            <if test="gross != null"> and GROSS = #{gross}</if>
            <if test="grosstime != null"> and GROSSTIME = #{grosstime}</if>
            <if test="grossweighid != null"> and GROSSWEIGHID = #{grossweighid}</if>
            <if test="grossweigh != null"> and GROSSWEIGH = #{grossweigh}</if>
            <if test="grossoperatorid != null"> and GROSSOPERATORID = #{grossoperatorid}</if>
            <if test="grossoperator != null and grossoperator != ''"> and GROSSOPERATOR = #{grossoperator}</if>
            <if test="tare != null"> and TARE = #{tare}</if>
            <if test="taretime != null"> and TARETIME = #{taretime}</if>
            <if test="tareweighid != null"> and TAREWEIGHID = #{tareweighid}</if>
            <if test="tareweigh != null"> and TAREWEIGH = #{tareweigh}</if>
            <if test="tareoperatorid != null"> and TAREOPERATORID = #{tareoperatorid}</if>
            <if test="tareoperator != null and tareoperator != ''"> and TAREOPERATOR = #{tareoperator}</if>
            <if test="deduction != null"> and DEDUCTION = #{deduction}</if>
            <if test="deductiontime != null"> and DEDUCTIONTIME = #{deductiontime}</if>
            <if test="deductionunit != null and deductionunit != ''"> and DEDUCTIONUNIT = #{deductionunit}</if>
            <if test="entergate != null"> and ENTERGATE = #{entergate}</if>
            <if test="mcount != null"> and MCOUNT = #{mcount}</if>
            <if test="bgross != null"> and BGROSS = #{bgross}</if>
            <if test="btare != null"> and BTARE = #{btare}</if>
            <if test="bgrosstime != null"> and BGROSSTIME = #{bgrosstime}</if>
            <if test="bgrossweighid != null"> and BGROSSWEIGHID = #{bgrossweighid}</if>
            <if test="bgrossweigh != null"> and BGROSSWEIGH = #{bgrossweigh}</if>
            <if test="btaretime != null"> and BTARETIME = #{btaretime}</if>
            <if test="btareweighid != null"> and BTAREWEIGHID = #{btareweighid}</if>
            <if test="btareweigh != null"> and BTAREWEIGH = #{btareweigh}</if>
            <if test="bmatchid != null"> and BMATCHID = #{bmatchid}</if>
            <if test="barchcode != null and barchcode != ''"> and BARCHCODE = #{barchcode}</if>
            <if test="dflag != null"> and DFLAG = #{dflag}</if>
            <if test="bflag != null"> and BFLAG = #{bflag}</if>
            <if test="fgbflag != null"> and FGBFLAG = #{fgbflag}</if>
            <if test="fgpriceflag != null"> and FGPRICEFLAG = #{fgpriceflag}</if>
            <if test="deduction3 != null"> and DEDUCTION3 = #{deduction3}</if>
            <if test="deduction4 != null"> and DEDUCTION4 = #{deduction4}</if>
            <if test="deduction2 != null"> and DEDUCTION2 = #{deduction2}</if>
            <if test="deduction1 != null"> and DEDUCTION1 = #{deduction1}</if>
            <if test="deduction5 != null"> and DEDUCTION5 = #{deduction5}</if>
            <if test="storeroom != null"> and STOREROOM = #{storeroom}</if>
            <if test="validman != null"> and VALIDMAN = #{validman}</if>
            <if test="validman2 != null"> and VALIDMAN2 = #{validman2}</if>
            <if test="mflag != null"> and MFLAG = #{mflag}</if>
            <if test="rfidId != null"> and RFID_ID = #{rfidId}</if>
            <if test="rfidNo != null and rfidNo != ''"> and RFID_NO = #{rfidNo}</if>
            <if test="entertime != null"> and ENTERTIME = #{entertime}</if>
            <if test="fgprice1 != null"> and FGPRICE1 = #{fgprice1}</if>
            <if test="materialname != null and materialname != ''"> and MATERIALNAME = #{materialname}</if>
            <if test="materialspec != null and materialspec != ''"> and MATERIALSPEC = #{materialspec}</if>
            <if test="ship != null and ship != ''"> and SHIP = #{ship}</if>
            <if test="sourcename != null and sourcename != ''"> and SOURCENAME = #{sourcename}</if>
            <if test="targetname != null and targetname != ''"> and TARGETNAME = #{targetname}</if>
            <if test="fgprice2 != null"> and FGPRICE2 = #{fgprice2}</if>
            <if test="fgsignpricelogid != null"> and FGSIGNPRICELOGID = #{fgsignpricelogid}</if>
            <if test="fgphoto != null"> and FGPHOTO = #{fgphoto}</if>
            <if test="shflag != null"> and SHFLAG = #{shflag}</if>
            <if test="tarehour != null"> and TAREHOUR = #{tarehour}</if>
            <if test="plancount != null"> and PLANCOUNT = #{plancount}</if>
            <if test="tarelogid != null"> and TARELOGID = #{tarelogid}</if>
            <if test="grosslogid != null"> and GROSSLOGID = #{grosslogid}</if>
            <if test="bgrosslogid != null"> and BGROSSLOGID = #{bgrosslogid}</if>
            <if test="btarelogid != null"> and BTARELOGID = #{btarelogid}</if>
            <if test="msrmemo != null and msrmemo != ''"> and MSRMEMO = #{msrmemo}</if>
            <if test="materialcount != null"> and MATERIALCOUNT = #{materialcount}</if>
            <if test="matno != null and matno != ''"> and MATNO = #{matno}</if>
            <if test="bbflag != null"> and BBFLAG = #{bbflag}</if>
            <if test="bbman != null and bbman != ''"> and BBMAN = #{bbman}</if>
            <if test="zgplanid != null"> and ZGPLANID = #{zgplanid}</if>
            <if test="zgflag != null"> and ZGFLAG = #{zgflag}</if>
            <if test="zgtargettime != null"> and ZGTARGETTIME = #{zgtargettime}</if>
            <if test="btareoperatorid != null"> and BTAREOPERATORID = #{btareoperatorid}</if>
            <if test="btareoperator != null and btareoperator != ''"> and BTAREOPERATOR = #{btareoperator}</if>
            <if test="bgrossoperatorid != null"> and BGROSSOPERATORID = #{bgrossoperatorid}</if>
            <if test="bgrossoperator != null and bgrossoperator != ''"> and BGROSSOPERATOR = #{bgrossoperator}</if>
            <if test="zoushu != null"> and ZOUSHU = #{zoushu}</if>
            <if test="orderld != null"> and ORDERLD = #{orderld}</if>
            <if test="fgtype != null"> and FGTYPE = #{fgtype}</if>
            <if test="fgcbtype != null and fgcbtype != ''"> and FGCBTYPE = #{fgcbtype}</if>
        </where>
    </select>

    <select id="selectDicById" parameterType="Long" resultMap="DicResult">
        <include refid="selectDicVo"/>
        where ID = #{id}
    </select>

    <insert id="insertDic" parameterType="DicMeasure">
        insert into D_IC_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="validflag != null">VALIDFLAG,</if>
            <if test="type != null">TYPE,</if>
            <if test="icNo != null">IC_NO,</if>
            <if test="carno != null">CARNO,</if>
            <if test="driver != null">DRIVER,</if>
            <if test="drivercode != null">DRIVERCODE,</if>
            <if test="fromman != null">FROMMAN,</if>
            <if test="frommancode != null">FROMMANCODE,</if>
            <if test="fromdata != null">FROMDATA,</if>
            <if test="deposit != null">DEPOSIT,</if>
            <if test="backman != null">BACKMAN,</if>
            <if test="backmancode != null">BACKMANCODE,</if>
            <if test="backdate != null">BACKDATE,</if>
            <if test="unitcode != null">UNITCODE,</if>
            <if test="unitname != null">UNITNAME,</if>
            <if test="unittime != null">UNITTIME,</if>
            <if test="unitman != null">UNITMAN,</if>
            <if test="tcode != null">TCODE,</if>
            <if test="tname != null">TNAME,</if>
            <if test="createman != null">CREATEMAN,</if>
            <if test="createdate != null">CREATEDATE,</if>
            <if test="operamemo != null">OPERAMEMO,</if>
            <if test="cardLevel != null">CARD_LEVEL,</if>
            <if test="icmode != null">ICMODE,</if>
            <if test="customercode != null">CUSTOMERCODE,</if>
            <if test="customername != null">CUSTOMERNAME,</if>
            <if test="uptimestamp != null">UPTIMESTAMP,</if>
            <if test="uptime != null">UPTIME,</if>
            <if test="cardState != null">CARD_STATE,</if>
            <if test="operatype != null">OPERATYPE,</if>
            <if test="matchid != null">MATCHID,</if>
            <if test="planid != null">PLANID,</if>
            <if test="orderno != null">ORDERNO,</if>
            <if test="taskcode != null">TASKCODE,</if>
            <if test="materialcode != null">MATERIALCODE,</if>
            <if test="materialspeccode != null">MATERIALSPECCODE,</if>
            <if test="shipcode != null">SHIPCODE,</if>
            <if test="sourcecode != null">SOURCECODE,</if>
            <if test="sourcetime != null">SOURCETIME,</if>
            <if test="targetcode != null">TARGETCODE,</if>
            <if test="targettime != null">TARGETTIME,</if>
            <if test="gross != null">GROSS,</if>
            <if test="grosstime != null">GROSSTIME,</if>
            <if test="grossweighid != null">GROSSWEIGHID,</if>
            <if test="grossweigh != null">GROSSWEIGH,</if>
            <if test="grossoperatorid != null">GROSSOPERATORID,</if>
            <if test="grossoperator != null">GROSSOPERATOR,</if>
            <if test="tare != null">TARE,</if>
            <if test="taretime != null">TARETIME,</if>
            <if test="tareweighid != null">TAREWEIGHID,</if>
            <if test="tareweigh != null">TAREWEIGH,</if>
            <if test="tareoperatorid != null">TAREOPERATORID,</if>
            <if test="tareoperator != null">TAREOPERATOR,</if>
            <if test="deduction != null">DEDUCTION,</if>
            <if test="deductiontime != null">DEDUCTIONTIME,</if>
            <if test="deductionunit != null">DEDUCTIONUNIT,</if>
            <if test="entergate != null">ENTERGATE,</if>
            <if test="mcount != null">MCOUNT,</if>
            <if test="bgross != null">BGROSS,</if>
            <if test="btare != null">BTARE,</if>
            <if test="bgrosstime != null">BGROSSTIME,</if>
            <if test="bgrossweighid != null">BGROSSWEIGHID,</if>
            <if test="bgrossweigh != null">BGROSSWEIGH,</if>
            <if test="btaretime != null">BTARETIME,</if>
            <if test="btareweighid != null">BTAREWEIGHID,</if>
            <if test="btareweigh != null">BTAREWEIGH,</if>
            <if test="bmatchid != null">BMATCHID,</if>
            <if test="barchcode != null">BARCHCODE,</if>
            <if test="dflag != null">DFLAG,</if>
            <if test="bflag != null">BFLAG,</if>
            <if test="fgbflag != null">FGBFLAG,</if>
            <if test="fgpriceflag != null">FGPRICEFLAG,</if>
            <if test="deduction3 != null">DEDUCTION3,</if>
            <if test="deduction4 != null">DEDUCTION4,</if>
            <if test="deduction2 != null">DEDUCTION2,</if>
            <if test="deduction1 != null">DEDUCTION1,</if>
            <if test="deduction5 != null">DEDUCTION5,</if>
            <if test="storeroom != null">STOREROOM,</if>
            <if test="validman != null">VALIDMAN,</if>
            <if test="validman2 != null">VALIDMAN2,</if>
            <if test="mflag != null">MFLAG,</if>
            <if test="rfidId != null">RFID_ID,</if>
            <if test="rfidNo != null">RFID_NO,</if>
            <if test="entertime != null">ENTERTIME,</if>
            <if test="fgprice1 != null">FGPRICE1,</if>
            <if test="materialname != null">MATERIALNAME,</if>
            <if test="materialspec != null">MATERIALSPEC,</if>
            <if test="ship != null">SHIP,</if>
            <if test="sourcename != null">SOURCENAME,</if>
            <if test="targetname != null">TARGETNAME,</if>
            <if test="fgprice2 != null">FGPRICE2,</if>
            <if test="fgsignpricelogid != null">FGSIGNPRICELOGID,</if>
            <if test="fgphoto != null">FGPHOTO,</if>
            <if test="shflag != null">SHFLAG,</if>
            <if test="tarehour != null">TAREHOUR,</if>
            <if test="plancount != null">PLANCOUNT,</if>
            <if test="tarelogid != null">TARELOGID,</if>
            <if test="grosslogid != null">GROSSLOGID,</if>
            <if test="bgrosslogid != null">BGROSSLOGID,</if>
            <if test="btarelogid != null">BTARELOGID,</if>
            <if test="msrmemo != null">MSRMEMO,</if>
            <if test="materialcount != null">MATERIALCOUNT,</if>
            <if test="matno != null">MATNO,</if>
            <if test="bbflag != null">BBFLAG,</if>
            <if test="bbman != null">BBMAN,</if>
            <if test="zgplanid != null">ZGPLANID,</if>
            <if test="zgflag != null">ZGFLAG,</if>
            <if test="zgtargettime != null">ZGTARGETTIME,</if>
            <if test="btareoperatorid != null">BTAREOPERATORID,</if>
            <if test="btareoperator != null">BTAREOPERATOR,</if>
            <if test="bgrossoperatorid != null">BGROSSOPERATORID,</if>
            <if test="bgrossoperator != null">BGROSSOPERATOR,</if>
            <if test="zoushu != null">ZOUSHU,</if>
            <if test="orderld != null">ORDERLD,</if>
            <if test="fgtype != null">FGTYPE,</if>
            <if test="fgcbtype != null">FGCBTYPE,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="validflag != null">#{validflag},</if>
            <if test="type != null">#{type},</if>
            <if test="icNo != null">#{icNo},</if>
            <if test="carno != null">#{carno},</if>
            <if test="driver != null">#{driver},</if>
            <if test="drivercode != null">#{drivercode},</if>
            <if test="fromman != null">#{fromman},</if>
            <if test="frommancode != null">#{frommancode},</if>
            <if test="fromdata != null">#{fromdata},</if>
            <if test="deposit != null">#{deposit},</if>
            <if test="backman != null">#{backman},</if>
            <if test="backmancode != null">#{backmancode},</if>
            <if test="backdate != null">#{backdate},</if>
            <if test="unitcode != null">#{unitcode},</if>
            <if test="unitname != null">#{unitname},</if>
            <if test="unittime != null">#{unittime},</if>
            <if test="unitman != null">#{unitman},</if>
            <if test="tcode != null">#{tcode},</if>
            <if test="tname != null">#{tname},</if>
            <if test="createman != null">#{createman},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="operamemo != null">#{operamemo},</if>
            <if test="cardLevel != null">#{cardLevel},</if>
            <if test="icmode != null">#{icmode},</if>
            <if test="customercode != null">#{customercode},</if>
            <if test="customername != null">#{customername},</if>
            <if test="uptimestamp != null">#{uptimestamp},</if>
            <if test="uptime != null">#{uptime},</if>
            <if test="cardState != null">#{cardState},</if>
            <if test="operatype != null">#{operatype},</if>
            <if test="matchid != null">#{matchid},</if>
            <if test="planid != null">#{planid},</if>
            <if test="orderno != null">#{orderno},</if>
            <if test="taskcode != null">#{taskcode},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialspeccode != null">#{materialspeccode},</if>
            <if test="shipcode != null">#{shipcode},</if>
            <if test="sourcecode != null">#{sourcecode},</if>
            <if test="sourcetime != null">#{sourcetime},</if>
            <if test="targetcode != null">#{targetcode},</if>
            <if test="targettime != null">#{targettime},</if>
            <if test="gross != null">#{gross},</if>
            <if test="grosstime != null">#{grosstime},</if>
            <if test="grossweighid != null">#{grossweighid},</if>
            <if test="grossweigh != null">#{grossweigh},</if>
            <if test="grossoperatorid != null">#{grossoperatorid},</if>
            <if test="grossoperator != null">#{grossoperator},</if>
            <if test="tare != null">#{tare},</if>
            <if test="taretime != null">#{taretime},</if>
            <if test="tareweighid != null">#{tareweighid},</if>
            <if test="tareweigh != null">#{tareweigh},</if>
            <if test="tareoperatorid != null">#{tareoperatorid},</if>
            <if test="tareoperator != null">#{tareoperator},</if>
            <if test="deduction != null">#{deduction},</if>
            <if test="deductiontime != null">#{deductiontime},</if>
            <if test="deductionunit != null">#{deductionunit},</if>
            <if test="entergate != null">#{entergate},</if>
            <if test="mcount != null">#{mcount},</if>
            <if test="bgross != null">#{bgross},</if>
            <if test="btare != null">#{btare},</if>
            <if test="bgrosstime != null">#{bgrosstime},</if>
            <if test="bgrossweighid != null">#{bgrossweighid},</if>
            <if test="bgrossweigh != null">#{bgrossweigh},</if>
            <if test="btaretime != null">#{btaretime},</if>
            <if test="btareweighid != null">#{btareweighid},</if>
            <if test="btareweigh != null">#{btareweigh},</if>
            <if test="bmatchid != null">#{bmatchid},</if>
            <if test="barchcode != null">#{barchcode},</if>
            <if test="dflag != null">#{dflag},</if>
            <if test="bflag != null">#{bflag},</if>
            <if test="fgbflag != null">#{fgbflag},</if>
            <if test="fgpriceflag != null">#{fgpriceflag},</if>
            <if test="deduction3 != null">#{deduction3},</if>
            <if test="deduction4 != null">#{deduction4},</if>
            <if test="deduction2 != null">#{deduction2},</if>
            <if test="deduction1 != null">#{deduction1},</if>
            <if test="deduction5 != null">#{deduction5},</if>
            <if test="storeroom != null">#{storeroom},</if>
            <if test="validman != null">#{validman},</if>
            <if test="validman2 != null">#{validman2},</if>
            <if test="mflag != null">#{mflag},</if>
            <if test="rfidId != null">#{rfidId},</if>
            <if test="rfidNo != null">#{rfidNo},</if>
            <if test="entertime != null">#{entertime},</if>
            <if test="fgprice1 != null">#{fgprice1},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="ship != null">#{ship},</if>
            <if test="sourcename != null">#{sourcename},</if>
            <if test="targetname != null">#{targetname},</if>
            <if test="fgprice2 != null">#{fgprice2},</if>
            <if test="fgsignpricelogid != null">#{fgsignpricelogid},</if>
            <if test="fgphoto != null">#{fgphoto},</if>
            <if test="shflag != null">#{shflag},</if>
            <if test="tarehour != null">#{tarehour},</if>
            <if test="plancount != null">#{plancount},</if>
            <if test="tarelogid != null">#{tarelogid},</if>
            <if test="grosslogid != null">#{grosslogid},</if>
            <if test="bgrosslogid != null">#{bgrosslogid},</if>
            <if test="btarelogid != null">#{btarelogid},</if>
            <if test="msrmemo != null">#{msrmemo},</if>
            <if test="materialcount != null">#{materialcount},</if>
            <if test="matno != null">#{matno},</if>
            <if test="bbflag != null">#{bbflag},</if>
            <if test="bbman != null">#{bbman},</if>
            <if test="zgplanid != null">#{zgplanid},</if>
            <if test="zgflag != null">#{zgflag},</if>
            <if test="zgtargettime != null">#{zgtargettime},</if>
            <if test="btareoperatorid != null">#{btareoperatorid},</if>
            <if test="btareoperator != null">#{btareoperator},</if>
            <if test="bgrossoperatorid != null">#{bgrossoperatorid},</if>
            <if test="bgrossoperator != null">#{bgrossoperator},</if>
            <if test="zoushu != null">#{zoushu},</if>
            <if test="orderld != null">#{orderld},</if>
            <if test="fgtype != null">#{fgtype},</if>
            <if test="fgcbtype != null">#{fgcbtype},</if>
        </trim>
    </insert>

    <update id="updateDic" parameterType="DicMeasure">
        update D_IC_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="validflag != null">VALIDFLAG = #{validflag},</if>
            <if test="type != null">TYPE = #{type},</if>
            <if test="icNo != null">IC_NO = #{icNo},</if>
            <if test="carno != null">CARNO = #{carno},</if>
            <if test="driver != null">DRIVER = #{driver},</if>
            <if test="drivercode != null">DRIVERCODE = #{drivercode},</if>
            <if test="fromman != null">FROMMAN = #{fromman},</if>
            <if test="frommancode != null">FROMMANCODE = #{frommancode},</if>
            <if test="fromdata != null">FROMDATA = #{fromdata},</if>
            <if test="deposit != null">DEPOSIT = #{deposit},</if>
            <if test="backman != null">BACKMAN = #{backman},</if>
            <if test="backmancode != null">BACKMANCODE = #{backmancode},</if>
            <if test="backdate != null">BACKDATE = #{backdate},</if>
            <if test="unitcode != null">UNITCODE = #{unitcode},</if>
            <if test="unitname != null">UNITNAME = #{unitname},</if>
            <if test="unittime != null">UNITTIME = #{unittime},</if>
            <if test="unitman != null">UNITMAN = #{unitman},</if>
            <if test="tcode != null">TCODE = #{tcode},</if>
            <if test="tname != null">TNAME = #{tname},</if>
            <if test="createman != null">CREATEMAN = #{createman},</if>
            <if test="createdate != null">CREATEDATE = #{createdate},</if>
            <if test="operamemo != null">OPERAMEMO = #{operamemo},</if>
            <if test="cardLevel != null">CARD_LEVEL = #{cardLevel},</if>
            <if test="icmode != null">ICMODE = #{icmode},</if>
            <if test="customercode != null">CUSTOMERCODE = #{customercode},</if>
            <if test="customername != null">CUSTOMERNAME = #{customername},</if>
            <if test="uptimestamp != null">UPTIMESTAMP = #{uptimestamp},</if>
            <if test="uptime != null">UPTIME = #{uptime},</if>
            <if test="cardState != null">CARD_STATE = #{cardState},</if>
            <if test="operatype != null">OPERATYPE = #{operatype},</if>
            <if test="matchid != null">MATCHID = #{matchid},</if>
            <if test="planid != null">PLANID = #{planid},</if>
            <if test="orderno != null">ORDERNO = #{orderno},</if>
            <if test="taskcode != null">TASKCODE = #{taskcode},</if>
            <if test="materialcode != null">MATERIALCODE = #{materialcode},</if>
            <if test="materialspeccode != null">MATERIALSPECCODE = #{materialspeccode},</if>
            <if test="shipcode != null">SHIPCODE = #{shipcode},</if>
            <if test="sourcecode != null">SOURCECODE = #{sourcecode},</if>
            <if test="sourcetime != null">SOURCETIME = #{sourcetime},</if>
            <if test="targetcode != null">TARGETCODE = #{targetcode},</if>
            <if test="targettime != null">TARGETTIME = #{targettime},</if>
            <if test="gross != null">GROSS = #{gross},</if>
            <if test="grosstime != null">GROSSTIME = #{grosstime},</if>
            <if test="grossweighid != null">GROSSWEIGHID = #{grossweighid},</if>
            <if test="grossweigh != null">GROSSWEIGH = #{grossweigh},</if>
            <if test="grossoperatorid != null">GROSSOPERATORID = #{grossoperatorid},</if>
            <if test="grossoperator != null">GROSSOPERATOR = #{grossoperator},</if>
            <if test="tare != null">TARE = #{tare},</if>
            <if test="taretime != null">TARETIME = #{taretime},</if>
            <if test="tareweighid != null">TAREWEIGHID = #{tareweighid},</if>
            <if test="tareweigh != null">TAREWEIGH = #{tareweigh},</if>
            <if test="tareoperatorid != null">TAREOPERATORID = #{tareoperatorid},</if>
            <if test="tareoperator != null">TAREOPERATOR = #{tareoperator},</if>
            <if test="deduction != null">DEDUCTION = #{deduction},</if>
            <if test="deductiontime != null">DEDUCTIONTIME = #{deductiontime},</if>
            <if test="deductionunit != null">DEDUCTIONUNIT = #{deductionunit},</if>
            <if test="entergate != null">ENTERGATE = #{entergate},</if>
            <if test="mcount != null">MCOUNT = #{mcount},</if>
            <if test="bgross != null">BGROSS = #{bgross},</if>
            <if test="btare != null">BTARE = #{btare},</if>
            <if test="bgrosstime != null">BGROSSTIME = #{bgrosstime},</if>
            <if test="bgrossweighid != null">BGROSSWEIGHID = #{bgrossweighid},</if>
            <if test="bgrossweigh != null">BGROSSWEIGH = #{bgrossweigh},</if>
            <if test="btaretime != null">BTARETIME = #{btaretime},</if>
            <if test="btareweighid != null">BTAREWEIGHID = #{btareweighid},</if>
            <if test="btareweigh != null">BTAREWEIGH = #{btareweigh},</if>
            <if test="bmatchid != null">BMATCHID = #{bmatchid},</if>
            <if test="barchcode != null">BARCHCODE = #{barchcode},</if>
            <if test="dflag != null">DFLAG = #{dflag},</if>
            <if test="bflag != null">BFLAG = #{bflag},</if>
            <if test="fgbflag != null">FGBFLAG = #{fgbflag},</if>
            <if test="fgpriceflag != null">FGPRICEFLAG = #{fgpriceflag},</if>
            <if test="deduction3 != null">DEDUCTION3 = #{deduction3},</if>
            <if test="deduction4 != null">DEDUCTION4 = #{deduction4},</if>
            <if test="deduction2 != null">DEDUCTION2 = #{deduction2},</if>
            <if test="deduction1 != null">DEDUCTION1 = #{deduction1},</if>
            <if test="deduction5 != null">DEDUCTION5 = #{deduction5},</if>
            <if test="storeroom != null">STOREROOM = #{storeroom},</if>
            <if test="validman != null">VALIDMAN = #{validman},</if>
            <if test="validman2 != null">VALIDMAN2 = #{validman2},</if>
            <if test="mflag != null">MFLAG = #{mflag},</if>
            <if test="rfidId != null">RFID_ID = #{rfidId},</if>
            <if test="rfidNo != null">RFID_NO = #{rfidNo},</if>
            <if test="entertime != null">ENTERTIME = #{entertime},</if>
            <if test="fgprice1 != null">FGPRICE1 = #{fgprice1},</if>
            <if test="materialname != null">MATERIALNAME = #{materialname},</if>
            <if test="materialspec != null">MATERIALSPEC = #{materialspec},</if>
            <if test="ship != null">SHIP = #{ship},</if>
            <if test="sourcename != null">SOURCENAME = #{sourcename},</if>
            <if test="targetname != null">TARGETNAME = #{targetname},</if>
            <if test="fgprice2 != null">FGPRICE2 = #{fgprice2},</if>
            <if test="fgsignpricelogid != null">FGSIGNPRICELOGID = #{fgsignpricelogid},</if>
            <if test="fgphoto != null">FGPHOTO = #{fgphoto},</if>
            <if test="shflag != null">SHFLAG = #{shflag},</if>
            <if test="tarehour != null">TAREHOUR = #{tarehour},</if>
            <if test="plancount != null">PLANCOUNT = #{plancount},</if>
            <if test="tarelogid != null">TARELOGID = #{tarelogid},</if>
            <if test="grosslogid != null">GROSSLOGID = #{grosslogid},</if>
            <if test="bgrosslogid != null">BGROSSLOGID = #{bgrosslogid},</if>
            <if test="btarelogid != null">BTARELOGID = #{btarelogid},</if>
            <if test="msrmemo != null">MSRMEMO = #{msrmemo},</if>
            <if test="materialcount != null">MATERIALCOUNT = #{materialcount},</if>
            <if test="matno != null">MATNO = #{matno},</if>
            <if test="bbflag != null">BBFLAG = #{bbflag},</if>
            <if test="bbman != null">BBMAN = #{bbman},</if>
            <if test="zgplanid != null">ZGPLANID = #{zgplanid},</if>
            <if test="zgflag != null">ZGFLAG = #{zgflag},</if>
            <if test="zgtargettime != null">ZGTARGETTIME = #{zgtargettime},</if>
            <if test="btareoperatorid != null">BTAREOPERATORID = #{btareoperatorid},</if>
            <if test="btareoperator != null">BTAREOPERATOR = #{btareoperator},</if>
            <if test="bgrossoperatorid != null">BGROSSOPERATORID = #{bgrossoperatorid},</if>
            <if test="bgrossoperator != null">BGROSSOPERATOR = #{bgrossoperator},</if>
            <if test="zoushu != null">ZOUSHU = #{zoushu},</if>
            <if test="orderld != null">ORDERLD = #{orderld},</if>
            <if test="fgtype != null">FGTYPE = #{fgtype},</if>
            <if test="fgcbtype != null">FGCBTYPE = #{fgcbtype},</if>
        </trim>
        where ID = #{id}
    </update>

    <update id="updateDicByCarNo" parameterType="DicMeasure">
        update D_IC_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="validflag != null">VALIDFLAG = #{validflag},</if>
            <if test="type != null">TYPE = #{type},</if>
            <if test="icNo != null">IC_NO = #{icNo},</if>
            <if test="driver != null">DRIVER = #{driver},</if>
            <if test="drivercode != null">DRIVERCODE = #{drivercode},</if>
            <if test="fromman != null">FROMMAN = #{fromman},</if>
            <if test="frommancode != null">FROMMANCODE = #{frommancode},</if>
            <if test="fromdata != null">FROMDATA = #{fromdata},</if>
            <if test="deposit != null">DEPOSIT = #{deposit},</if>
            <if test="backman != null">BACKMAN = #{backman},</if>
            <if test="backmancode != null">BACKMANCODE = #{backmancode},</if>
            <if test="backdate != null">BACKDATE = #{backdate},</if>
            <if test="unitcode != null">UNITCODE = #{unitcode},</if>
            <if test="unitname != null">UNITNAME = #{unitname},</if>
            <if test="unittime != null">UNITTIME = #{unittime},</if>
            <if test="unitman != null">UNITMAN = #{unitman},</if>
            <if test="tcode != null">TCODE = #{tcode},</if>
            <if test="tname != null">TNAME = #{tname},</if>
            <if test="createman != null">CREATEMAN = #{createman},</if>
            <if test="createdate != null">CREATEDATE = #{createdate},</if>
            <if test="operamemo != null">OPERAMEMO = #{operamemo},</if>
            <if test="cardLevel != null">CARD_LEVEL = #{cardLevel},</if>
            <if test="icmode != null">ICMODE = #{icmode},</if>
            <if test="customercode != null">CUSTOMERCODE = #{customercode},</if>
            <if test="customername != null">CUSTOMERNAME = #{customername},</if>
            <if test="uptimestamp != null">UPTIMESTAMP = #{uptimestamp},</if>
            <if test="uptime != null">UPTIME = #{uptime},</if>
            <if test="cardState != null">CARD_STATE = #{cardState},</if>
            <if test="operatype != null">OPERATYPE = #{operatype},</if>
            <if test="matchid != null">MATCHID = #{matchid},</if>
            <if test="planid != null">PLANID = #{planid},</if>
            <if test="orderno != null">ORDERNO = #{orderno},</if>
            <if test="taskcode != null">TASKCODE = #{taskcode},</if>
            <if test="materialcode != null">MATERIALCODE = #{materialcode},</if>
            <if test="materialspeccode != null">MATERIALSPECCODE = #{materialspeccode},</if>
            <if test="shipcode != null">SHIPCODE = #{shipcode},</if>
            <if test="sourcecode != null">SOURCECODE = #{sourcecode},</if>
            <if test="sourcetime != null">SOURCETIME = #{sourcetime},</if>
            <if test="targetcode != null">TARGETCODE = #{targetcode},</if>
            <if test="targettime != null">TARGETTIME = #{targettime},</if>
            <if test="gross != null">GROSS = #{gross},</if>
            <if test="grosstime != null">GROSSTIME = #{grosstime},</if>
            <if test="grossweighid != null">GROSSWEIGHID = #{grossweighid},</if>
            <if test="grossweigh != null">GROSSWEIGH = #{grossweigh},</if>
            <if test="grossoperatorid != null">GROSSOPERATORID = #{grossoperatorid},</if>
            <if test="grossoperator != null">GROSSOPERATOR = #{grossoperator},</if>
            <if test="tare != null">TARE = #{tare},</if>
            <if test="taretime != null">TARETIME = #{taretime},</if>
            <if test="tareweighid != null">TAREWEIGHID = #{tareweighid},</if>
            <if test="tareweigh != null">TAREWEIGH = #{tareweigh},</if>
            <if test="tareoperatorid != null">TAREOPERATORID = #{tareoperatorid},</if>
            <if test="tareoperator != null">TAREOPERATOR = #{tareoperator},</if>
            <if test="deduction != null">DEDUCTION = #{deduction},</if>
            <if test="deductiontime != null">DEDUCTIONTIME = #{deductiontime},</if>
            <if test="deductionunit != null">DEDUCTIONUNIT = #{deductionunit},</if>
            <if test="entergate != null">ENTERGATE = #{entergate},</if>
            <if test="mcount != null">MCOUNT = #{mcount},</if>
            <if test="bgross != null">BGROSS = #{bgross},</if>
            <if test="btare != null">BTARE = #{btare},</if>
            <if test="bgrosstime != null">BGROSSTIME = #{bgrosstime},</if>
            <if test="bgrossweighid != null">BGROSSWEIGHID = #{bgrossweighid},</if>
            <if test="bgrossweigh != null">BGROSSWEIGH = #{bgrossweigh},</if>
            <if test="btaretime != null">BTARETIME = #{btaretime},</if>
            <if test="btareweighid != null">BTAREWEIGHID = #{btareweighid},</if>
            <if test="btareweigh != null">BTAREWEIGH = #{btareweigh},</if>
            <if test="bmatchid != null">BMATCHID = #{bmatchid},</if>
            <if test="barchcode != null">BARCHCODE = #{barchcode},</if>
            <if test="dflag != null">DFLAG = #{dflag},</if>
            <if test="bflag != null">BFLAG = #{bflag},</if>
            <if test="fgbflag != null">FGBFLAG = #{fgbflag},</if>
            <if test="fgpriceflag != null">FGPRICEFLAG = #{fgpriceflag},</if>
            <if test="deduction3 != null">DEDUCTION3 = #{deduction3},</if>
            <if test="deduction4 != null">DEDUCTION4 = #{deduction4},</if>
            <if test="deduction2 != null">DEDUCTION2 = #{deduction2},</if>
            <if test="deduction1 != null">DEDUCTION1 = #{deduction1},</if>
            <if test="deduction5 != null">DEDUCTION5 = #{deduction5},</if>
            <if test="storeroom != null">STOREROOM = #{storeroom},</if>
            <if test="validman != null">VALIDMAN = #{validman},</if>
            <if test="validman2 != null">VALIDMAN2 = #{validman2},</if>
            <if test="mflag != null">MFLAG = #{mflag},</if>
            <if test="rfidId != null">RFID_ID = #{rfidId},</if>
            <if test="rfidNo != null">RFID_NO = #{rfidNo},</if>
            <if test="entertime != null">ENTERTIME = #{entertime},</if>
            <if test="fgprice1 != null">FGPRICE1 = #{fgprice1},</if>
            <if test="materialname != null">MATERIALNAME = #{materialname},</if>
            <if test="materialspec != null">MATERIALSPEC = #{materialspec},</if>
            <if test="ship != null">SHIP = #{ship},</if>
            <if test="sourcename != null">SOURCENAME = #{sourcename},</if>
            <if test="targetname != null">TARGETNAME = #{targetname},</if>
            <if test="fgprice2 != null">FGPRICE2 = #{fgprice2},</if>
            <if test="fgsignpricelogid != null">FGSIGNPRICELOGID = #{fgsignpricelogid},</if>
            <if test="fgphoto != null">FGPHOTO = #{fgphoto},</if>
            <if test="shflag != null">SHFLAG = #{shflag},</if>
            <if test="tarehour != null">TAREHOUR = #{tarehour},</if>
            <if test="plancount != null">PLANCOUNT = #{plancount},</if>
            <if test="tarelogid != null">TARELOGID = #{tarelogid},</if>
            <if test="grosslogid != null">GROSSLOGID = #{grosslogid},</if>
            <if test="bgrosslogid != null">BGROSSLOGID = #{bgrosslogid},</if>
            <if test="btarelogid != null">BTARELOGID = #{btarelogid},</if>
            <if test="msrmemo != null">MSRMEMO = #{msrmemo},</if>
            <if test="materialcount != null">MATERIALCOUNT = #{materialcount},</if>
            <if test="matno != null">MATNO = #{matno},</if>
            <if test="bbflag != null">BBFLAG = #{bbflag},</if>
            <if test="bbman != null">BBMAN = #{bbman},</if>
            <if test="zgplanid != null">ZGPLANID = #{zgplanid},</if>
            <if test="zgflag != null">ZGFLAG = #{zgflag},</if>
            <if test="zgtargettime != null">ZGTARGETTIME = #{zgtargettime},</if>
            <if test="btareoperatorid != null">BTAREOPERATORID = #{btareoperatorid},</if>
            <if test="btareoperator != null">BTAREOPERATOR = #{btareoperator},</if>
            <if test="bgrossoperatorid != null">BGROSSOPERATORID = #{bgrossoperatorid},</if>
            <if test="bgrossoperator != null">BGROSSOPERATOR = #{bgrossoperator},</if>
            <if test="zoushu != null">ZOUSHU = #{zoushu},</if>
            <if test="orderld != null">ORDERLD = #{orderld},</if>
            <if test="fgtype != null">FGTYPE = #{fgtype},</if>
            <if test="fgcbtype != null">FGCBTYPE = #{fgcbtype},</if>
        </trim>
        where CARNO = #{carno}
    </update>

    <delete id="deleteDicById" parameterType="Long">
        delete from D_IC_T where CARNO = #{carno}
    </delete>

    <delete id="deleteDicByIds" parameterType="String">
        delete from D_IC_T where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDicByMatchid" parameterType="String">
        delete from D_IC_T where MATCHID = #{matchid}
    </delete>
</mapper>