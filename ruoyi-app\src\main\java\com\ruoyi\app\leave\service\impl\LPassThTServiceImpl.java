package com.ruoyi.app.leave.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LPassThTMapper;
import com.ruoyi.app.leave.domain.LPassThT;
import com.ruoyi.app.leave.service.ILPassThTService;

/**
 * 退货申请主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class LPassThTServiceImpl implements ILPassThTService 
{
    @Autowired
    private LPassThTMapper lPassThTMapper;

    /**
     * 查询退货申请主
     * 
     * @param id 退货申请主ID
     * @return 退货申请主
     */
    @Override
    public LPassThT selectLPassThTById(Long id)
    {
        return lPassThTMapper.selectLPassThTById(id);
    }

    /**
     * 查询退货申请主列表
     * 
     * @param lPassThT 退货申请主
     * @return 退货申请主
     */
    @Override
    public List<LPassThT> selectLPassThTList(LPassThT lPassThT)
    {
        return lPassThTMapper.selectLPassThTList(lPassThT);
    }

    /**
     * 新增退货申请主
     * 
     * @param lPassThT 退货申请主
     * @return 结果
     */
    @Override
    public int insertLPassThT(LPassThT lPassThT)
    {
        return lPassThTMapper.insertLPassThT(lPassThT);
    }

    /**
     * 修改退货申请主
     * 
     * @param lPassThT 退货申请主
     * @return 结果
     */
    @Override
    public int updateLPassThT(LPassThT lPassThT)
    {
        return lPassThTMapper.updateLPassThT(lPassThT);
    }

    /**
     * 批量删除退货申请主
     * 
     * @param ids 需要删除的退货申请主ID
     * @return 结果
     */
    @Override
    public int deleteLPassThTByIds(Long[] ids)
    {
        return lPassThTMapper.deleteLPassThTByIds(ids);
    }

    /**
     * 删除退货申请主信息
     * 
     * @param id 退货申请主ID
     * @return 结果
     */
    @Override
    public int deleteLPassThTById(Long id)
    {
        return lPassThTMapper.deleteLPassThTById(id);
    }
}
