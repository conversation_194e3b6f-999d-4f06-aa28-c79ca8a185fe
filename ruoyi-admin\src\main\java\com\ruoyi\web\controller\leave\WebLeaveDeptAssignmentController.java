package com.ruoyi.web.controller.leave;

import com.ruoyi.app.leave.domain.LeaveDeptAssignment;
import com.ruoyi.app.leave.service.ILeaveDeptAssignmentService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 出门证部门归属Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/web/leave/assignment")
public class WebLeaveDeptAssignmentController extends BaseController
{
    @Autowired
    private ILeaveDeptAssignmentService leaveDeptAssignmentService;

    /**
     * 查询出门证部门归属列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeaveDeptAssignment leaveDeptAssignment)
    {
        startPage();
        List<LeaveDeptAssignment> list = leaveDeptAssignmentService.selectLeaveDeptAssignmentList(leaveDeptAssignment);
        return getDataTable(list);
    }

    /**
     * 导出出门证部门归属列表
     */
    @Log(title = "出门证部门归属", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveDeptAssignment leaveDeptAssignment)
    {
        List<LeaveDeptAssignment> list = leaveDeptAssignmentService.selectLeaveDeptAssignmentList(leaveDeptAssignment);
        ExcelUtil<LeaveDeptAssignment> util = new ExcelUtil<LeaveDeptAssignment>(LeaveDeptAssignment.class);
        return util.exportExcel(list, "assignment");
    }

    /**
     * 获取出门证部门归属详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveDeptAssignmentService.selectLeaveDeptAssignmentById(id));
    }

    /**
     * 新增出门证部门归属
     */
    @Log(title = "出门证部门归属", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveDeptAssignment leaveDeptAssignment)
    {
        return toAjax(leaveDeptAssignmentService.insertLeaveDeptAssignment(leaveDeptAssignment));
    }

    /**
     * 修改出门证部门归属
     */
    @Log(title = "出门证部门归属", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveDeptAssignment leaveDeptAssignment)
    {
        return toAjax(leaveDeptAssignmentService.updateLeaveDeptAssignment(leaveDeptAssignment));
    }

    /**
     * 删除出门证部门归属
     */
    @Log(title = "出门证部门归属", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveDeptAssignmentService.deleteLeaveDeptAssignmentByIds(ids));
    }
}
