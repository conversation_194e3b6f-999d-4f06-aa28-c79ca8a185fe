package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.LeavePlanMaterial;

/**
 * 出门证计划申请物资Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface LeavePlanMaterialMapper 
{
    /**
     * 查询出门证计划申请物资
     * 
     * @param id 出门证计划申请物资ID
     * @return 出门证计划申请物资
     */

    public LeavePlanMaterial selectLeavePlanMaterialById(Long id);

    /**
     * 查询出门证计划申请物资列表
     * 
     * @param leavePlanMaterial 出门证计划申请物资
     * @return 出门证计划申请物资集合
     */
    public List<LeavePlanMaterial> selectLeavePlanMaterialList(LeavePlanMaterial leavePlanMaterial);

    /**
     * 新增出门证计划申请物资
     * 
     * @param leavePlanMaterial 出门证计划申请物资
     * @return 结果
     */
    public int insertLeavePlanMaterial(LeavePlanMaterial leavePlanMaterial);

    /**
     * 修改出门证计划申请物资
     * 
     * @param leavePlanMaterial 出门证计划申请物资
     * @return 结果
     */
    public int updateLeavePlanMaterial(LeavePlanMaterial leavePlanMaterial);

    /**
     * 删除出门证计划申请物资
     * 
     * @param id 出门证计划申请物资ID
     * @return 结果
     */
    public int deleteLeavePlanMaterialById(Long id);

    /**
     * 批量删除出门证计划申请物资
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteLeavePlanMaterialByIds(Long[] ids);

    /**
     * 根据计划编号查询出门证计划申请物资
     * 
     * @param applyNo 计划编号
     * @return 出门证计划申请物资
     */
    public List<LeavePlanMaterial> selectLeavePlanMaterialByApplyNo(String applyNo);

    /**
     * 根据计划编号删除出门证计划申请物资
     * 
     * @param applyNo 计划编号
     * @return 结果
     */
    public int deleteLeavePlanMaterialByApplyNo(String applyNo);
}
