package com.ruoyi.app.leave.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LPassFhTMapper;
import com.ruoyi.app.leave.domain.LPassFhT;
import com.ruoyi.app.leave.service.ILPassFhTService;

/**
 * 出厂返回主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class LPassFhTServiceImpl implements ILPassFhTService 
{
    @Autowired
    private LPassFhTMapper lPassFhTMapper;

    /**
     * 查询出厂返回主
     * 
     * @param id 出厂返回主ID
     * @return 出厂返回主
     */
    @Override
    public LPassFhT selectLPassFhTById(Long id)
    {
        return lPassFhTMapper.selectLPassFhTById(id);
    }

    /**
     * 查询出厂返回主列表
     * 
     * @param lPassFhT 出厂返回主
     * @return 出厂返回主
     */
    @Override
    public List<LPassFhT> selectLPassFhTList(LPassFhT lPassFhT)
    {
        return lPassFhTMapper.selectLPassFhTList(lPassFhT);
    }

    /**
     * 新增出厂返回主
     * 
     * @param lPassFhT 出厂返回主
     * @return 结果
     */
    @Override
    public int insertLPassFhT(LPassFhT lPassFhT)
    {
        return lPassFhTMapper.insertLPassFhT(lPassFhT);
    }

    /**
     * 修改出厂返回主
     * 
     * @param lPassFhT 出厂返回主
     * @return 结果
     */
    @Override
    public int updateLPassFhT(LPassFhT lPassFhT)
    {
        return lPassFhTMapper.updateLPassFhT(lPassFhT);
    }

    /**
     * 批量删除出厂返回主
     * 
     * @param ids 需要删除的出厂返回主ID
     * @return 结果
     */
    @Override
    public int deleteLPassFhTByIds(Long[] ids)
    {
        return lPassFhTMapper.deleteLPassFhTByIds(ids);
    }

    /**
     * 删除出厂返回主信息
     * 
     * @param id 出厂返回主ID
     * @return 结果
     */
    @Override
    public int deleteLPassFhTById(Long id)
    {
        return lPassFhTMapper.deleteLPassFhTById(id);
    }
}
