{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\leave\\customer.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\leave\\customer.js", "mtime": 1756170476739}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listCustomer", "query", "request", "url", "method", "params", "getCustomer", "id", "addCustomer", "data", "updateCustomer", "delCustomer", "exportCustomer", "invalidCustomer"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/api/leave/customer.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询出门证厂外客户列表\r\nexport function listCustomer(query) {\r\n  return request({\r\n    url: '/web/leave/customer/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询出门证厂外客户详细\r\nexport function getCustomer(id) {\r\n  return request({\r\n    url: '/web/leave/customer/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增出门证厂外客户\r\nexport function addCustomer(data) {\r\n  return request({\r\n    url: '/web/leave/customer',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改出门证厂外客户\r\nexport function updateCustomer(data) {\r\n  return request({\r\n    url: '/web/leave/customer',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除出门证厂外客户\r\nexport function delCustomer(id) {\r\n  return request({\r\n    url: '/web/leave/customer/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出出门证厂外客户\r\nexport function exportCustomer(query) {\r\n  return request({\r\n    url: '/web/leave/customer/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 废弃客户方法\r\nexport function invalidCustomer(id) { \r\n  return request({\r\n    url: '/web/leave/customer/'+id, \r\n    method: 'put',\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,EAAE,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,EAAE;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACD,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACJ,EAAE,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,EAAE;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,cAAcA,CAACX,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,eAAeA,CAACN,EAAE,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAACI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}