<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LPassFhTMapper">
    
    <resultMap type="LPassFhT" id="LPassFhTResult">
        <result property="id"    column="id"    />
        <result property="validflag"    column="validflag"    />
        <result property="materialcode"    column="materialcode"    />
        <result property="materialname"    column="materialname"    />
        <result property="materialspec"    column="materialspec"    />
        <result property="materialtype"    column="materialtype"    />
        <result property="sourcecode"    column="sourcecode"    />
        <result property="sourcename"    column="sourcename"    />
        <result property="targetcode"    column="targetcode"    />
        <result property="targetname"    column="targetname"    />
        <result property="count"    column="count"    />
        <result property="weight"    column="weight"    />
        <result property="unit"    column="unit"    />
        <result property="validtime"    column="validtime"    />
        <result property="unitcode"    column="unitcode"    />
        <result property="unitname"    column="unitname"    />
        <result property="planreturndate"    column="planreturndate"    />
        <result property="realreturndate"    column="realreturndate"    />
        <result property="lflag"    column="lflag"    />
        <result property="leader"    column="leader"    />
        <result property="ldate"    column="ldate"    />
        <result property="pflag"    column="pflag"    />
        <result property="productman"    column="productman"    />
        <result property="pdate"    column="pdate"    />
        <result property="printnum"    column="printnum"    />
        <result property="createman"    column="createman"    />
        <result property="createdate"    column="createdate"    />
        <result property="updateman"    column="updateman"    />
        <result property="updatedate"    column="updatedate"    />
        <result property="memo"    column="memo"    />
        <result property="leadermemo"    column="leadermemo"    />
        <result property="productmemo"    column="productmemo"    />
        <result property="carno"    column="carno"    />
        <result property="heatno"    column="heatno"    />
        <result property="leaveman"    column="leaveman"    />
        <result property="leavedate"    column="leavedate"    />
        <result property="leavegate"    column="leavegate"    />
        <result property="enterman"    column="enterman"    />
        <result property="enterdate"    column="enterdate"    />
        <result property="entergate"    column="entergate"    />
        <result property="receiveman"    column="receiveman"    />
        <result property="receivedate"    column="receivedate"    />
        <result property="receiveunit"    column="receiveunit"    />
        <result property="supervisor"    column="supervisor"    />
        <result property="outmemo"    column="outmemo"    />
        <result property="attachpath"    column="attachpath"    />
        <result property="printnump"    column="printnump"    />
        <result property="cmzhao"    column="cmzhao"    />
        <result property="flagfp"    column="flagfp"    />
        <result property="yxflag"    column="yxflag"    />
        <result property="yxday"    column="yxday"    />
        <result property="wzzgy"    column="wzzgy"    />
        <result property="applyid"    column="applyid"    />
        <result property="measureflag"    column="measureflag"    />
        <result property="wzflag"    column="wzflag"    />
        <result property="jgflag"    column="jgflag"    />
        <result property="jgleader"    column="jgleader"    />
        <result property="jgldate"    column="jgldate"    />
        <result property="jglmemo"    column="jglmemo"    />
        <result property="fsflag"    column="fsflag"    />
        <result property="sqflag"    column="sqflag"    />
        <result property="sqleader"    column="sqleader"    />
        <result property="sqlmemo"    column="sqlmemo"    />
        <result property="sqldate"    column="sqldate"    />
        <result property="fgflag"    column="fgflag"    />
        <result property="fgleader"    column="fgleader"    />
        <result property="fglmemo"    column="fglmemo"    />
        <result property="fgldate"    column="fgldate"    />
        <result property="sbflag"    column="sbflag"    />
        <result property="sbleader"    column="sbleader"    />
        <result property="sblmemo"    column="sblmemo"    />
        <result property="sbldate"    column="sbldate"    />
        <result property="syflag"    column="syflag"    />
        <result property="syleader"    column="syleader"    />
        <result property="sylmemo"    column="sylmemo"    />
        <result property="syldate"    column="syldate"    />
        <result property="hsflag"    column="hsflag"    />
        <result property="shmore"    column="shmore"    />
        <result property="yxqmemo"    column="yxqmemo"    />
        <result property="fhrqmemo"    column="fhrqmemo"    />
        <result property="operatype"    column="operatype"    />
        <result property="crkflag"    column="crkflag"    />
        <result property="cancelmemo"    column="cancelmemo"    />
        <result property="uptimestamp"    column="uptimestamp"    />
        <result property="uptime"    column="uptime"    />
        <result property="receivename"    column="receivename"    />
        <result property="receivecode"    column="receivecode"    />
        <result property="printflag"    column="printflag"    />
        <result property="printflagdate"    column="printflagdate"    />
        <result property="bdmemo"    column="bdmemo"    />
        <result property="fhdwmemo"    column="fhdwmemo"    />
        <result property="wlmaterialname"    column="wlmaterialname"    />
        <result property="oldValidflag"    column="old_validflag"    />
        <result property="mtype"    column="mtype"    />
    </resultMap>

    <sql id="selectLPassFhTVo">
        select id, validflag, materialcode, materialname, materialspec, materialtype, sourcecode, sourcename, targetcode, targetname, count, weight, unit, validtime, unitcode, unitname, planreturndate, realreturndate, lflag, leader, ldate, pflag, productman, pdate, printnum, createman, createdate, updateman, updatedate, memo, leadermemo, productmemo, carno, heatno, leaveman, leavedate, leavegate, enterman, enterdate, entergate, receiveman, receivedate, receiveunit, supervisor, outmemo, attachpath, printnump, cmzhao, flagfp, yxflag, yxday, wzzgy, applyid, measureflag, wzflag, jgflag, jgleader, jgldate, jglmemo, fsflag, sqflag, sqleader, sqlmemo, sqldate, fgflag, fgleader, fglmemo, fgldate, sbflag, sbleader, sblmemo, sbldate, syflag, syleader, sylmemo, syldate, hsflag, shmore, yxqmemo, fhrqmemo, operatype, crkflag, cancelmemo, uptimestamp, uptime, receivename, receivecode, printflag, printflagdate, bdmemo, fhdwmemo, wlmaterialname, old_validflag, mtype from L_PASS_FH_T
    </sql>

    <select id="selectLPassFhTList" parameterType="LPassFhT" resultMap="LPassFhTResult">
        <include refid="selectLPassFhTVo"/>
        <where>  
            <if test="validflag != null "> and validflag = #{validflag}</if>
            <if test="materialcode != null  and materialcode != ''"> and materialcode = #{materialcode}</if>
            <if test="materialname != null  and materialname != ''"> and materialname like concat('%', #{materialname}, '%')</if>
            <if test="materialspec != null  and materialspec != ''"> and materialspec = #{materialspec}</if>
            <if test="materialtype != null  and materialtype != ''"> and materialtype = #{materialtype}</if>
            <if test="sourcecode != null  and sourcecode != ''"> and sourcecode = #{sourcecode}</if>
            <if test="sourcename != null  and sourcename != ''"> and sourcename like concat('%', #{sourcename}, '%')</if>
            <if test="targetcode != null  and targetcode != ''"> and targetcode = #{targetcode}</if>
            <if test="targetname != null  and targetname != ''"> and targetname like concat('%', #{targetname}, '%')</if>
            <if test="count != null "> and count = #{count}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="validtime != null "> and validtime = #{validtime}</if>
            <if test="unitcode != null  and unitcode != ''"> and unitcode = #{unitcode}</if>
            <if test="unitname != null  and unitname != ''"> and unitname like concat('%', #{unitname}, '%')</if>
            <if test="planreturndate != null "> and planreturndate = #{planreturndate}</if>
            <if test="realreturndate != null "> and realreturndate = #{realreturndate}</if>
            <if test="lflag != null "> and lflag = #{lflag}</if>
            <if test="leader != null  and leader != ''"> and leader = #{leader}</if>
            <if test="ldate != null "> and ldate = #{ldate}</if>
            <if test="pflag != null "> and pflag = #{pflag}</if>
            <if test="productman != null  and productman != ''"> and productman = #{productman}</if>
            <if test="pdate != null "> and pdate = #{pdate}</if>
            <if test="printnum != null "> and printnum = #{printnum}</if>
            <if test="createman != null  and createman != ''"> and createman = #{createman}</if>
            <if test="createdate != null "> and createdate = #{createdate}</if>
            <if test="updateman != null  and updateman != ''"> and updateman = #{updateman}</if>
            <if test="updatedate != null "> and updatedate = #{updatedate}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="leadermemo != null  and leadermemo != ''"> and leadermemo = #{leadermemo}</if>
            <if test="productmemo != null  and productmemo != ''"> and productmemo = #{productmemo}</if>
            <if test="carno != null  and carno != ''"> and carno = #{carno}</if>
            <if test="heatno != null  and heatno != ''"> and heatno = #{heatno}</if>
            <if test="leaveman != null  and leaveman != ''"> and leaveman = #{leaveman}</if>
            <if test="leavedate != null "> and leavedate = #{leavedate}</if>
            <if test="leavegate != null  and leavegate != ''"> and leavegate = #{leavegate}</if>
            <if test="enterman != null  and enterman != ''"> and enterman = #{enterman}</if>
            <if test="enterdate != null "> and enterdate = #{enterdate}</if>
            <if test="entergate != null  and entergate != ''"> and entergate = #{entergate}</if>
            <if test="receiveman != null  and receiveman != ''"> and receiveman = #{receiveman}</if>
            <if test="receivedate != null "> and receivedate = #{receivedate}</if>
            <if test="receiveunit != null  and receiveunit != ''"> and receiveunit = #{receiveunit}</if>
            <if test="supervisor != null  and supervisor != ''"> and supervisor = #{supervisor}</if>
            <if test="outmemo != null  and outmemo != ''"> and outmemo = #{outmemo}</if>
            <if test="attachpath != null  and attachpath != ''"> and attachpath = #{attachpath}</if>
            <if test="printnump != null "> and printnump = #{printnump}</if>
            <if test="cmzhao != null  and cmzhao != ''"> and cmzhao = #{cmzhao}</if>
            <if test="flagfp != null "> and flagfp = #{flagfp}</if>
            <if test="yxflag != null "> and yxflag = #{yxflag}</if>
            <if test="yxday != null "> and yxday = #{yxday}</if>
            <if test="wzzgy != null  and wzzgy != ''"> and wzzgy = #{wzzgy}</if>
            <if test="applyid != null  and applyid != ''"> and applyid = #{applyid}</if>
            <if test="measureflag != null "> and measureflag = #{measureflag}</if>
            <if test="wzflag != null "> and wzflag = #{wzflag}</if>
            <if test="jgflag != null "> and jgflag = #{jgflag}</if>
            <if test="jgleader != null  and jgleader != ''"> and jgleader = #{jgleader}</if>
            <if test="jgldate != null "> and jgldate = #{jgldate}</if>
            <if test="jglmemo != null  and jglmemo != ''"> and jglmemo = #{jglmemo}</if>
            <if test="fsflag != null "> and fsflag = #{fsflag}</if>
            <if test="sqflag != null "> and sqflag = #{sqflag}</if>
            <if test="sqleader != null  and sqleader != ''"> and sqleader = #{sqleader}</if>
            <if test="sqlmemo != null  and sqlmemo != ''"> and sqlmemo = #{sqlmemo}</if>
            <if test="sqldate != null "> and sqldate = #{sqldate}</if>
            <if test="fgflag != null "> and fgflag = #{fgflag}</if>
            <if test="fgleader != null  and fgleader != ''"> and fgleader = #{fgleader}</if>
            <if test="fglmemo != null  and fglmemo != ''"> and fglmemo = #{fglmemo}</if>
            <if test="fgldate != null "> and fgldate = #{fgldate}</if>
            <if test="sbflag != null "> and sbflag = #{sbflag}</if>
            <if test="sbleader != null  and sbleader != ''"> and sbleader = #{sbleader}</if>
            <if test="sblmemo != null  and sblmemo != ''"> and sblmemo = #{sblmemo}</if>
            <if test="sbldate != null "> and sbldate = #{sbldate}</if>
            <if test="syflag != null "> and syflag = #{syflag}</if>
            <if test="syleader != null  and syleader != ''"> and syleader = #{syleader}</if>
            <if test="sylmemo != null  and sylmemo != ''"> and sylmemo = #{sylmemo}</if>
            <if test="syldate != null "> and syldate = #{syldate}</if>
            <if test="hsflag != null "> and hsflag = #{hsflag}</if>
            <if test="shmore != null "> and shmore = #{shmore}</if>
            <if test="yxqmemo != null  and yxqmemo != ''"> and yxqmemo = #{yxqmemo}</if>
            <if test="fhrqmemo != null  and fhrqmemo != ''"> and fhrqmemo = #{fhrqmemo}</if>
            <if test="operatype != null "> and operatype = #{operatype}</if>
            <if test="crkflag != null "> and crkflag = #{crkflag}</if>
            <if test="cancelmemo != null  and cancelmemo != ''"> and cancelmemo = #{cancelmemo}</if>
            <if test="uptimestamp != null "> and uptimestamp = #{uptimestamp}</if>
            <if test="uptime != null "> and uptime = #{uptime}</if>
            <if test="receivename != null  and receivename != ''"> and receivename like concat('%', #{receivename}, '%')</if>
            <if test="receivecode != null  and receivecode != ''"> and receivecode = #{receivecode}</if>
            <if test="printflag != null "> and printflag = #{printflag}</if>
            <if test="printflagdate != null "> and printflagdate = #{printflagdate}</if>
            <if test="bdmemo != null  and bdmemo != ''"> and bdmemo = #{bdmemo}</if>
            <if test="fhdwmemo != null  and fhdwmemo != ''"> and fhdwmemo = #{fhdwmemo}</if>
            <if test="wlmaterialname != null  and wlmaterialname != ''"> and wlmaterialname like concat('%', #{wlmaterialname}, '%')</if>
            <if test="oldValidflag != null "> and old_validflag = #{oldValidflag}</if>
            <if test="mtype != null  and mtype != ''"> and mtype = #{mtype}</if>
        </where>
    </select>
    
    <select id="selectLPassFhTById" parameterType="Long" resultMap="LPassFhTResult">
        <include refid="selectLPassFhTVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLPassFhT" parameterType="LPassFhT">
        insert into L_PASS_FH_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="validflag != null">validflag,</if>
            <if test="materialcode != null">materialcode,</if>
            <if test="materialname != null">materialname,</if>
            <if test="materialspec != null">materialspec,</if>
            <if test="materialtype != null">materialtype,</if>
            <if test="sourcecode != null">sourcecode,</if>
            <if test="sourcename != null">sourcename,</if>
            <if test="targetcode != null">targetcode,</if>
            <if test="targetname != null">targetname,</if>
            <if test="count != null">count,</if>
            <if test="weight != null">weight,</if>
            <if test="unit != null">unit,</if>
            <if test="validtime != null">validtime,</if>
            <if test="unitcode != null">unitcode,</if>
            <if test="unitname != null">unitname,</if>
            <if test="planreturndate != null">planreturndate,</if>
            <if test="realreturndate != null">realreturndate,</if>
            <if test="lflag != null">lflag,</if>
            <if test="leader != null">leader,</if>
            <if test="ldate != null">ldate,</if>
            <if test="pflag != null">pflag,</if>
            <if test="productman != null">productman,</if>
            <if test="pdate != null">pdate,</if>
            <if test="printnum != null">printnum,</if>
            <if test="createman != null">createman,</if>
            <if test="createdate != null">createdate,</if>
            <if test="updateman != null">updateman,</if>
            <if test="updatedate != null">updatedate,</if>
            <if test="memo != null">memo,</if>
            <if test="leadermemo != null">leadermemo,</if>
            <if test="productmemo != null">productmemo,</if>
            <if test="carno != null">carno,</if>
            <if test="heatno != null">heatno,</if>
            <if test="leaveman != null">leaveman,</if>
            <if test="leavedate != null">leavedate,</if>
            <if test="leavegate != null">leavegate,</if>
            <if test="enterman != null">enterman,</if>
            <if test="enterdate != null">enterdate,</if>
            <if test="entergate != null">entergate,</if>
            <if test="receiveman != null">receiveman,</if>
            <if test="receivedate != null">receivedate,</if>
            <if test="receiveunit != null">receiveunit,</if>
            <if test="supervisor != null">supervisor,</if>
            <if test="outmemo != null">outmemo,</if>
            <if test="attachpath != null">attachpath,</if>
            <if test="printnump != null">printnump,</if>
            <if test="cmzhao != null">cmzhao,</if>
            <if test="flagfp != null">flagfp,</if>
            <if test="yxflag != null">yxflag,</if>
            <if test="yxday != null">yxday,</if>
            <if test="wzzgy != null">wzzgy,</if>
            <if test="applyid != null">applyid,</if>
            <if test="measureflag != null">measureflag,</if>
            <if test="wzflag != null">wzflag,</if>
            <if test="jgflag != null">jgflag,</if>
            <if test="jgleader != null">jgleader,</if>
            <if test="jgldate != null">jgldate,</if>
            <if test="jglmemo != null">jglmemo,</if>
            <if test="fsflag != null">fsflag,</if>
            <if test="sqflag != null">sqflag,</if>
            <if test="sqleader != null">sqleader,</if>
            <if test="sqlmemo != null">sqlmemo,</if>
            <if test="sqldate != null">sqldate,</if>
            <if test="fgflag != null">fgflag,</if>
            <if test="fgleader != null">fgleader,</if>
            <if test="fglmemo != null">fglmemo,</if>
            <if test="fgldate != null">fgldate,</if>
            <if test="sbflag != null">sbflag,</if>
            <if test="sbleader != null">sbleader,</if>
            <if test="sblmemo != null">sblmemo,</if>
            <if test="sbldate != null">sbldate,</if>
            <if test="syflag != null">syflag,</if>
            <if test="syleader != null">syleader,</if>
            <if test="sylmemo != null">sylmemo,</if>
            <if test="syldate != null">syldate,</if>
            <if test="hsflag != null">hsflag,</if>
            <if test="shmore != null">shmore,</if>
            <if test="yxqmemo != null">yxqmemo,</if>
            <if test="fhrqmemo != null">fhrqmemo,</if>
            <if test="operatype != null">operatype,</if>
            <if test="crkflag != null">crkflag,</if>
            <if test="cancelmemo != null">cancelmemo,</if>
            <if test="uptimestamp != null">uptimestamp,</if>
            <if test="uptime != null">uptime,</if>
            <if test="receivename != null">receivename,</if>
            <if test="receivecode != null">receivecode,</if>
            <if test="printflag != null">printflag,</if>
            <if test="printflagdate != null">printflagdate,</if>
            <if test="bdmemo != null">bdmemo,</if>
            <if test="fhdwmemo != null">fhdwmemo,</if>
            <if test="wlmaterialname != null">wlmaterialname,</if>
            <if test="oldValidflag != null">old_validflag,</if>
            <if test="mtype != null">mtype,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="validflag != null">#{validflag},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="materialtype != null">#{materialtype},</if>
            <if test="sourcecode != null">#{sourcecode},</if>
            <if test="sourcename != null">#{sourcename},</if>
            <if test="targetcode != null">#{targetcode},</if>
            <if test="targetname != null">#{targetname},</if>
            <if test="count != null">#{count},</if>
            <if test="weight != null">#{weight},</if>
            <if test="unit != null">#{unit},</if>
            <if test="validtime != null">#{validtime},</if>
            <if test="unitcode != null">#{unitcode},</if>
            <if test="unitname != null">#{unitname},</if>
            <if test="planreturndate != null">#{planreturndate},</if>
            <if test="realreturndate != null">#{realreturndate},</if>
            <if test="lflag != null">#{lflag},</if>
            <if test="leader != null">#{leader},</if>
            <if test="ldate != null">#{ldate},</if>
            <if test="pflag != null">#{pflag},</if>
            <if test="productman != null">#{productman},</if>
            <if test="pdate != null">#{pdate},</if>
            <if test="printnum != null">#{printnum},</if>
            <if test="createman != null">#{createman},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="updateman != null">#{updateman},</if>
            <if test="updatedate != null">#{updatedate},</if>
            <if test="memo != null">#{memo},</if>
            <if test="leadermemo != null">#{leadermemo},</if>
            <if test="productmemo != null">#{productmemo},</if>
            <if test="carno != null">#{carno},</if>
            <if test="heatno != null">#{heatno},</if>
            <if test="leaveman != null">#{leaveman},</if>
            <if test="leavedate != null">#{leavedate},</if>
            <if test="leavegate != null">#{leavegate},</if>
            <if test="enterman != null">#{enterman},</if>
            <if test="enterdate != null">#{enterdate},</if>
            <if test="entergate != null">#{entergate},</if>
            <if test="receiveman != null">#{receiveman},</if>
            <if test="receivedate != null">#{receivedate},</if>
            <if test="receiveunit != null">#{receiveunit},</if>
            <if test="supervisor != null">#{supervisor},</if>
            <if test="outmemo != null">#{outmemo},</if>
            <if test="attachpath != null">#{attachpath},</if>
            <if test="printnump != null">#{printnump},</if>
            <if test="cmzhao != null">#{cmzhao},</if>
            <if test="flagfp != null">#{flagfp},</if>
            <if test="yxflag != null">#{yxflag},</if>
            <if test="yxday != null">#{yxday},</if>
            <if test="wzzgy != null">#{wzzgy},</if>
            <if test="applyid != null">#{applyid},</if>
            <if test="measureflag != null">#{measureflag},</if>
            <if test="wzflag != null">#{wzflag},</if>
            <if test="jgflag != null">#{jgflag},</if>
            <if test="jgleader != null">#{jgleader},</if>
            <if test="jgldate != null">#{jgldate},</if>
            <if test="jglmemo != null">#{jglmemo},</if>
            <if test="fsflag != null">#{fsflag},</if>
            <if test="sqflag != null">#{sqflag},</if>
            <if test="sqleader != null">#{sqleader},</if>
            <if test="sqlmemo != null">#{sqlmemo},</if>
            <if test="sqldate != null">#{sqldate},</if>
            <if test="fgflag != null">#{fgflag},</if>
            <if test="fgleader != null">#{fgleader},</if>
            <if test="fglmemo != null">#{fglmemo},</if>
            <if test="fgldate != null">#{fgldate},</if>
            <if test="sbflag != null">#{sbflag},</if>
            <if test="sbleader != null">#{sbleader},</if>
            <if test="sblmemo != null">#{sblmemo},</if>
            <if test="sbldate != null">#{sbldate},</if>
            <if test="syflag != null">#{syflag},</if>
            <if test="syleader != null">#{syleader},</if>
            <if test="sylmemo != null">#{sylmemo},</if>
            <if test="syldate != null">#{syldate},</if>
            <if test="hsflag != null">#{hsflag},</if>
            <if test="shmore != null">#{shmore},</if>
            <if test="yxqmemo != null">#{yxqmemo},</if>
            <if test="fhrqmemo != null">#{fhrqmemo},</if>
            <if test="operatype != null">#{operatype},</if>
            <if test="crkflag != null">#{crkflag},</if>
            <if test="cancelmemo != null">#{cancelmemo},</if>
            <if test="uptimestamp != null">#{uptimestamp},</if>
            <if test="uptime != null">#{uptime},</if>
            <if test="receivename != null">#{receivename},</if>
            <if test="receivecode != null">#{receivecode},</if>
            <if test="printflag != null">#{printflag},</if>
            <if test="printflagdate != null">#{printflagdate},</if>
            <if test="bdmemo != null">#{bdmemo},</if>
            <if test="fhdwmemo != null">#{fhdwmemo},</if>
            <if test="wlmaterialname != null">#{wlmaterialname},</if>
            <if test="oldValidflag != null">#{oldValidflag},</if>
            <if test="mtype != null">#{mtype},</if>
         </trim>
    </insert>

    <update id="updateLPassFhT" parameterType="LPassFhT">
        update L_PASS_FH_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="validflag != null">validflag = #{validflag},</if>
            <if test="materialcode != null">materialcode = #{materialcode},</if>
            <if test="materialname != null">materialname = #{materialname},</if>
            <if test="materialspec != null">materialspec = #{materialspec},</if>
            <if test="materialtype != null">materialtype = #{materialtype},</if>
            <if test="sourcecode != null">sourcecode = #{sourcecode},</if>
            <if test="sourcename != null">sourcename = #{sourcename},</if>
            <if test="targetcode != null">targetcode = #{targetcode},</if>
            <if test="targetname != null">targetname = #{targetname},</if>
            <if test="count != null">count = #{count},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="validtime != null">validtime = #{validtime},</if>
            <if test="unitcode != null">unitcode = #{unitcode},</if>
            <if test="unitname != null">unitname = #{unitname},</if>
            <if test="planreturndate != null">planreturndate = #{planreturndate},</if>
            <if test="realreturndate != null">realreturndate = #{realreturndate},</if>
            <if test="lflag != null">lflag = #{lflag},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="ldate != null">ldate = #{ldate},</if>
            <if test="pflag != null">pflag = #{pflag},</if>
            <if test="productman != null">productman = #{productman},</if>
            <if test="pdate != null">pdate = #{pdate},</if>
            <if test="printnum != null">printnum = #{printnum},</if>
            <if test="createman != null">createman = #{createman},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
            <if test="updateman != null">updateman = #{updateman},</if>
            <if test="updatedate != null">updatedate = #{updatedate},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="leadermemo != null">leadermemo = #{leadermemo},</if>
            <if test="productmemo != null">productmemo = #{productmemo},</if>
            <if test="carno != null">carno = #{carno},</if>
            <if test="heatno != null">heatno = #{heatno},</if>
            <if test="leaveman != null">leaveman = #{leaveman},</if>
            <if test="leavedate != null">leavedate = #{leavedate},</if>
            <if test="leavegate != null">leavegate = #{leavegate},</if>
            <if test="enterman != null">enterman = #{enterman},</if>
            <if test="enterdate != null">enterdate = #{enterdate},</if>
            <if test="entergate != null">entergate = #{entergate},</if>
            <if test="receiveman != null">receiveman = #{receiveman},</if>
            <if test="receivedate != null">receivedate = #{receivedate},</if>
            <if test="receiveunit != null">receiveunit = #{receiveunit},</if>
            <if test="supervisor != null">supervisor = #{supervisor},</if>
            <if test="outmemo != null">outmemo = #{outmemo},</if>
            <if test="attachpath != null">attachpath = #{attachpath},</if>
            <if test="printnump != null">printnump = #{printnump},</if>
            <if test="cmzhao != null">cmzhao = #{cmzhao},</if>
            <if test="flagfp != null">flagfp = #{flagfp},</if>
            <if test="yxflag != null">yxflag = #{yxflag},</if>
            <if test="yxday != null">yxday = #{yxday},</if>
            <if test="wzzgy != null">wzzgy = #{wzzgy},</if>
            <if test="applyid != null">applyid = #{applyid},</if>
            <if test="measureflag != null">measureflag = #{measureflag},</if>
            <if test="wzflag != null">wzflag = #{wzflag},</if>
            <if test="jgflag != null">jgflag = #{jgflag},</if>
            <if test="jgleader != null">jgleader = #{jgleader},</if>
            <if test="jgldate != null">jgldate = #{jgldate},</if>
            <if test="jglmemo != null">jglmemo = #{jglmemo},</if>
            <if test="fsflag != null">fsflag = #{fsflag},</if>
            <if test="sqflag != null">sqflag = #{sqflag},</if>
            <if test="sqleader != null">sqleader = #{sqleader},</if>
            <if test="sqlmemo != null">sqlmemo = #{sqlmemo},</if>
            <if test="sqldate != null">sqldate = #{sqldate},</if>
            <if test="fgflag != null">fgflag = #{fgflag},</if>
            <if test="fgleader != null">fgleader = #{fgleader},</if>
            <if test="fglmemo != null">fglmemo = #{fglmemo},</if>
            <if test="fgldate != null">fgldate = #{fgldate},</if>
            <if test="sbflag != null">sbflag = #{sbflag},</if>
            <if test="sbleader != null">sbleader = #{sbleader},</if>
            <if test="sblmemo != null">sblmemo = #{sblmemo},</if>
            <if test="sbldate != null">sbldate = #{sbldate},</if>
            <if test="syflag != null">syflag = #{syflag},</if>
            <if test="syleader != null">syleader = #{syleader},</if>
            <if test="sylmemo != null">sylmemo = #{sylmemo},</if>
            <if test="syldate != null">syldate = #{syldate},</if>
            <if test="hsflag != null">hsflag = #{hsflag},</if>
            <if test="shmore != null">shmore = #{shmore},</if>
            <if test="yxqmemo != null">yxqmemo = #{yxqmemo},</if>
            <if test="fhrqmemo != null">fhrqmemo = #{fhrqmemo},</if>
            <if test="operatype != null">operatype = #{operatype},</if>
            <if test="crkflag != null">crkflag = #{crkflag},</if>
            <if test="cancelmemo != null">cancelmemo = #{cancelmemo},</if>
            <if test="uptimestamp != null">uptimestamp = #{uptimestamp},</if>
            <if test="uptime != null">uptime = #{uptime},</if>
            <if test="receivename != null">receivename = #{receivename},</if>
            <if test="receivecode != null">receivecode = #{receivecode},</if>
            <if test="printflag != null">printflag = #{printflag},</if>
            <if test="printflagdate != null">printflagdate = #{printflagdate},</if>
            <if test="bdmemo != null">bdmemo = #{bdmemo},</if>
            <if test="fhdwmemo != null">fhdwmemo = #{fhdwmemo},</if>
            <if test="wlmaterialname != null">wlmaterialname = #{wlmaterialname},</if>
            <if test="oldValidflag != null">old_validflag = #{oldValidflag},</if>
            <if test="mtype != null">mtype = #{mtype},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLPassFhTById" parameterType="Long">
        delete from L_PASS_FH_T where id = #{id}
    </delete>

    <delete id="deleteLPassFhTByIds" parameterType="String">
        delete from L_PASS_FH_T where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectByCmzhao" parameterType="String" resultMap="LPassFhTResult">
        <include refid="selectLPassFhTVo"/>
        where cmzhao = #{cmzhao}
    </select>
    
    <select id="selectMaxId" resultType="Long">
        select nvl(max(id), 0) from l_pass_fh_t
    </select>
    
</mapper>