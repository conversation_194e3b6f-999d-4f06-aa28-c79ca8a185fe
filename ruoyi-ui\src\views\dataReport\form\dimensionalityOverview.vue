<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h2>技经能源指标报表</h2>
    </div>


        <!-- 能源指标部分 -->
    <el-card class="box-card energy-section" shadow="hover">
      <div slot="header" class="clearfix section-header">
        <span>能源指标概览</span>
      </div>
      <div class="chart-container">
        <!-- 能源报表一行显示 -->
        <div class="energy-charts-row">
          <!-- 部门能源消耗详情（原图四，现在放在第一位置） -->
          <div class="chart-item energy-chart-half">
            <div class="chart-title">
              <span>部门能源消耗详情</span>
              <div class="energy-dept-selector">
                <el-select v-model="currentEnergyDept" size="small" placeholder="选择部门" @change="initEnergyDetailCharts">
                  <el-option
                    v-for="factory in allFactories"
                    :key="factory"
                    :label="factory"
                    :value="factory">
                  </el-option>
                </el-select>
              </div>
            </div>
            <div class="energy-subchart-container">
              <div v-for="type in energyTypes" :key="type.value" 
                  class="energy-subchart">
                <div class="subchart-title">{{ type.label }}</div>
                <div :id="'energySubchart_' + type.value" class="subchart"></div>
              </div>
            </div>
          </div>

          <!-- 部门能源消耗趋势（原图一，现在放在第三位置） -->
          <div class="chart-item energy-chart-half">
            <div class="chart-title">
              <span>能源数据监控</span>
            </div>
            <div class="key-indicators-container">
              <div 
                v-for="(indicator, index) in keyEnergyIndicators" 
                :key="index" 
                class="indicator-card"
                :class="[indicator.status, {'danger': indicator.change < -50 || indicator.change > 50}]"
              >
                <div class="indicator-title">{{ indicator.name }}</div>
                <div class="indicator-value">{{ indicator.today }} <span class="indicator-unit">{{ indicator.unit }}</span></div>
                <!-- <div class="indicator-target">
                  目标范围: {{ indicator.targetMin }} ~ {{ indicator.targetMax }} {{ indicator.unit }}
                </div> -->
                <div class="indicator-compare">
                  <span>昨日: {{ indicator.yesterday }}</span>
                  <!-- <span 
                    class="indicator-change" 
                    :class="{ 'up': indicator.change > 0, 'down': indicator.change < 0 }"
                  >
                    {{ indicator.change > 0 ? '+' : '' }}{{ indicator.change }}%
                  </span> -->
                </div>
                <div class="indicator-target">
                  <span 
                    class="indicator-change" 
                    :class="{ 'up': indicator.change > 0, 'down': indicator.change < 0 }"
                  >
                   环比变化:{{ indicator.change > 0 ? '+' : '' }}{{ indicator.change }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <el-card class="box-card tech-economic-section" shadow="hover">
      <div slot="header" class="clearfix">
        <span>技经指标数据表格</span>
        <el-button
          size="mini"
          type="primary"
          style="float: right; margin-left: 10px"
          @click="loadExcelFromRemote"
          :loading="excelLoading"
        >
          {{ excelLoading ? "正在加载..." : "重新加载数据" }}
        </el-button>
      </div>
      <el-table
        :data="techIndicators"
        border
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :span-method="objectSpanMethod"
      >
        <el-table-column
          prop="factory"
          label="分厂"
          width="250"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="指标名称"
          width="280"
          align="center"
        >
          <template slot-scope="scope">
            <span
              :style="{
                color: '#409EFF',
                fontWeight: 'bold',
                backgroundColor: scope.row.highlight
                  ? '#a9d3ff'
                  : 'transparent',
              }"
              >{{ scope.row.name }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="target"
          label="目标"
          width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="unit"
          label="单位"
          width="150"
          align="center"
        ></el-table-column>
        <!-- <el-table-column prop="jan" label="01月实绩" align="center">
          <template slot-scope="scope">
            <span
              :style="{
                color: scope.row.janStatus === 1 ? '#67C23A' : '#F56C6C',
                fontWeight: 'bold',
              }"
            >
              {{ scope.row.jan }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="feb" label="02月实绩" align="center">
          <template slot-scope="scope">
            <span
              :style="{
                color: scope.row.febStatus === 1 ? '#67C23A' : '#F56C6C',
                fontWeight: 'bold',
              }"
            >
              {{ scope.row.feb }}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column prop="mar" label="03月实绩" align="center">
          <template slot-scope="scope">
            <span
              :style="{
                color: scope.row.marStatus === 1 ? '#67C23A' : '#F56C6C',
                fontWeight: 'bold',
              }"
            >
              {{ scope.row.mar }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="apr" label="04月实绩" align="center">
          <template slot-scope="scope">
            <span
              :style="{
                color: scope.row.aprStatus === 1 ? '#67C23A' : '#F56C6C',
                fontWeight: 'bold',
              }"
            >
              {{ scope.row.apr }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="may" label="05月实绩" align="center">
          <template slot-scope="scope">
            <span
              :style="{
                color: scope.row.mayStatus === 1 ? '#67C23A' : '#F56C6C',
                fontWeight: 'bold',
              }"
            >
              {{ scope.row.may }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              circle
              @click="showDetails(scope.row)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

        <!-- 指标详情对话框 -->
    <el-dialog
      title="指标详情"
      :visible.sync="detailDialogVisible"
      width="70%"
      :before-close="handleDialogClose"
    >
      <div v-if="currentIndicator">
        <h3>{{ currentIndicator.name }} ({{ currentIndicator.unit }})</h3>
        <div class="indicator-info">
          <p>分厂: {{ currentIndicator.factory }}</p>
          <p>目标值: {{ currentIndicator.target }}</p>
          <p>当前值: {{ currentIndicator.may }}</p>
        </div>
        <div id="indicatorChart" style="width: 100%; height: 400px"></div>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import * as echarts from 'echarts'
import axios from "axios";
import * as XLSX from "xlsx";
import {
  dimensionalitylistPermissionList
} from "@/api/tYjy/dimensionality";
import { dateUpdateList } from "@/api/tYjy/answer";
export default {
  name: 'DimensionalityOverview',
  data() {
    return {

      // 技经指标数据
      techIndicators: [],

      detailDialogVisible: false,
      currentIndicator: null,
      // Excel文件加载状态
      excelLoading: false,
      adminShow:"0",

      // 当前显示的部门（用于月度趋势图切换）
      // currentBusinessUnit: '炼铁事业部', // 当前选择的事业部
      // currentDepartment: '',
      // currentIndicator: '',
      // autoSwitchDepartment: true,
      // 定时器
      trendChartTimer: null, // 用于分厂切换
      businessUnitTimer: null, // 用于事业部切换
      scrollTimer: null,
      tableScrollPaused: false, // 是否暂停表格自动滚动
      completionChartTimer: null, // 用于完成率图表滚动
      indicatorCardsScrollTimer: null, // 用于指标卡片滚动
      completionChartScrollDirection: 'down', // 滚动方向：'up' 或 'down'
      completionChartScrollPaused: false, // 是否暂停自动滚动
      indicatorCardsScrollPaused: false, // 是否暂停指标卡片自动滚动
  
      energyDeptTimer: null, // 能源部门切换定时器
      scrollSpeed: 50, // 滚动速度，数值越大速度越慢
      
      // 事件处理器引用
      chartMouseOverHandler: null,
      chartMouseOutHandler: null,
      tableMouseEnterHandler: null, // 表格鼠标进入处理器
      tableMouseLeaveHandler: null, // 表格鼠标离开处理器
      
      // 当前选择的能源类型和部门
      currentEnergyType: 'electricity',
      currentEnergyDept: '炼铁分厂', // 修改为分厂名称
      
      // 部门列表
      departments: ['炼铁事业部', '炼钢事业部', '轧钢事业部', '马科托钢球', '特板事业部', '动力事业部', '物流事业部', '检修事业部'],
      
      // 指标完成情况数据 - 从图片中提取的数据
      completionData: [
        { department: '炼铁事业部-炼铁分厂', indicators: [
          { name: '综合燃料比', target: 512.33, actual: 523.45, unit: 'kg/t', isHigherBetter: false, values: [520.26, 523.67, 523.45, 519.06], completionRates: ['-1.55%', '-2.00%', '-2.17%', '-1.31%'] },
          { name: '工序能耗', target: 369.91, actual: 369.69, unit: 'kgCe/t', isHigherBetter: false, values: [369.74, 367.98, 369.69, 363.76], completionRates: ['0.05%', '0.52%', '0.06%', '1.66%'] },
          { name: '400高炉工序能耗', target: 43.70, actual: 43.56, unit: 'kgCe/t', isHigherBetter: false, values: [43.67, 43.67, 43.56, 43.67], completionRates: ['0.07%', '0.07%', '0.32%', '0.07%'] },
          { name: '360高炉工序能耗', target: 45.01, actual: 44.84, unit: 'kgCe/t', isHigherBetter: false, values: [45.00, 44.93, 44.84, 44.99], completionRates: ['0.02%', '0.07%', '0.38%', '0.04%'] }
        ]},
        { department: '炼铁事业部-烧结分厂', indicators: [
          { name: '工序能耗', target: 154.65, actual: 154.56, unit: 'kgCe/t', isHigherBetter: false, values: [154.65, 154.91, 154.56, 154.57], completionRates: ['0.01%', '0.03%', '0.06%', '0.05%'] },
          { name: '矿渣增幅', target: 16.00, actual: 15.80, unit: 'kgCe/t', isHigherBetter: false, values: [15.82, 15.94, 15.80, 15.85], completionRates: ['0.50%', '0.38%', '1.25%', '0.94%'] },
        ]},
        { department: '炼钢事业部-一炼钢', indicators: [
          { name: '综合石灰消耗', target: 42.95, actual: 42.89, unit: 'kg/t', isHigherBetter: false, values: [42.94, 42.82, 42.89, 40.55], completionRates: ['0.02%', '0.30%', '0.14%', '5.59%'] },
          { name: '氧气消耗', target: 44.56, actual: 44.45, unit: 'm3/t', isHigherBetter: false, values: [44.50, 44.34, 44.45, 44.37], completionRates: ['0.13%', '0.49%', '0.25%', '0.43%'] },
          { name: '电炉工序能耗', target: 57.50, actual: 57.32, unit: 'kgCe/t', isHigherBetter: false, values: [57.34, 57.33, 57.32, 57.19], completionRates: ['0.28%', '0.30%', '0.31%', '0.54%'] },
          { name: '钢铁料收得率', target: 91.50, actual: 91.32, unit: '%', isHigherBetter: true, values: [91.51, 91.32, 91.32, 91.32], completionRates: ['0.01%', '-0.20%', '-0.20%', '-0.20%'] }
        ]},
        { department: '炼钢事业部-二炼钢', indicators: [
          { name: '综合石灰消耗', target: 50.00, actual: 48.95, unit: 'kg/t', isHigherBetter: false, values: [49.90, 49.40, 48.95, 49.00], completionRates: ['0.20%', '1.08%', '2.10%', '2.00%'] },
          { name: '氧气消耗', target: 45.65, actual: 45.26, unit: 'm3/t', isHigherBetter: false, values: [45.49, 45.37, 45.26, 45.35], completionRates: ['0.35%', '0.39%', '0.85%', '0.66%'] },
          { name: '转炉工序能耗', target: -28.50, actual: -29.52, unit: 'kgCe/t', isHigherBetter: true, values: [29.78, 29.57, 29.52, 29.61], completionRates: ['-0.98%', '-3.75%', '-3.58%', '-3.89%'] },
          { name: '钢铁料收得率', target: 91.50, actual: 91.16, unit: '%', isHigherBetter: true, values: [91.50, 91.32, 91.16, 91.15], completionRates: ['0.00%', '-0.20%', '-0.37%', '-0.38%'] }
        ]},
        { department: '轧钢事业部-综合利用', indicators: [
          { name: '废金金属量', target: 0.65, actual: 0.64, unit: '%', isHigherBetter: false, values: [0.65, 0.53, 0.64, 0.49], completionRates: ['0.00%', '18.40%', '1.54%', '-26.15%'] },
          { name: '电耗', target: 22.33, actual: 23.81, unit: 'kWh/t', isHigherBetter: false, values: [22.33, 23.07, 23.81, 21.19], completionRates: ['0.45%', '-3.31%', '-6.62%', '5.11%'] }
        ]},
        { department: '轧钢事业部-一轧钢', indicators: [
          { name: '热轧综合成材率', target: 96.35, actual: 96.22, unit: '%', isHigherBetter: true, values: [96.23, 96.25, 96.22, 96.24], completionRates: ['-0.12%', '-0.10%', '-0.13%', '-0.11%'] },
          { name: '热轧钢材工序能耗', target: 58.55, actual: 58.51, unit: 'kgCe/t', isHigherBetter: false, values: [58.47, 58.52, 58.51, 58.45], completionRates: ['0.14%', '0.05%', '0.07%', '0.17%'] }
        ]},
        { department: '轧钢事业部-二轧钢', indicators: [
          { name: '热轧综合成材率(大棒)', target: 95.37, actual: 95.37, unit: '%', isHigherBetter: true, values: [95.37, 95.22, 95.37, 95.37], completionRates: ['0.00%', '-0.15%', '0.00%', '0.00%'] },
          { name: '热轧综合成材率(小棒)', target: 96.56, actual: 96.56, unit: '%', isHigherBetter: true, values: [96.39, 96.56, 96.56, 96.56], completionRates: ['-0.17%', '0.00%', '0.00%', '0.00%'] },
          { name: '热轧钢材工序能耗(大棒)', target: 67.28, actual: 72.49, unit: 'kgCe/t', isHigherBetter: false, values: [71.35, 73.80, 72.49, 66.24], completionRates: ['-0.93%', '0.75%', '0.00%', '-0.25%'] },
          { name: '热轧钢材工序能耗(小棒)', target: 42.05, actual: 42.02, unit: 'kgCe/t', isHigherBetter: false, values: [42.03, 42.05, 42.02, 45.68], completionRates: ['0.05%', '0.05%', '0.05%', '0.34%'] }
        ]},
        { department: '轧钢事业部-三轧钢', indicators: [
          { name: '热轧综合成材率', target: 96.04, actual: 95.50, unit: '%', isHigherBetter: true, values: [95.76, 96.30, 95.50, 95.51], completionRates: ['-0.28%', '0.44%', '-0.50%', '-0.50%'] },
          { name: '热轧钢材工序能耗', target: 56.31, actual: 54.67, unit: 'kgCe/t', isHigherBetter: false, values: [55.26, 56.34, 54.67, 55.19], completionRates: ['-0.79%', '0.71%', '-1.33%', '-1.18%'] }
        ]},
        { department: '轧钢事业部-特殊钢轧材', indicators: [
          { name: '热轧钢材工序能耗', target: 67.04, actual: 68.64, unit: 'kgCe/t', isHigherBetter: false, values: [67.35, 64.09, 68.64, 64.77], completionRates: ['0.46%', '-3.26%', '0.00%', '-0.23%'] },
          { name: '综合成材率', target: 96.73, actual: 96.73, unit: '%', isHigherBetter: true, values: [96.73, 96.79, 96.73, 96.45], completionRates: ['0.00%', '0.06%', '0.00%', '-0.28%'] }
        ]},
        { department: '轧钢事业部-棒材轧制厂', indicators: [
          { name: '热轧钢材工序能耗(棒扁)', target: 56.93, actual: 61.81, unit: 'kgCe/t', isHigherBetter: false, values: [66.14, 60.00, 61.81, 59.96], completionRates: ['-0.91%', '-1.35%', '0.00%', '-0.24%'] },
          { name: '热轧钢材工序能耗(大盘)', target: 57.08, actual: 61.28, unit: 'kgCe/t', isHigherBetter: false, values: [64.30, 60.29, 61.28, 60.02], completionRates: ['-0.19%', '-0.15%', '0.00%', '-0.26%'] },
          { name: '综合成材率(棒扁轧材)', target: 96.45, actual: 96.12, unit: '%', isHigherBetter: true, values: [96.14, 96.11, 96.12, 96.03], completionRates: ['-0.31%', '-0.33%', '0.00%', '-0.29%'] },
          { name: '综合成材率(大盘卷)', target: 95.85, actual: 95.84, unit: '%', isHigherBetter: true, values: [95.86, 95.90, 95.84, 95.87], completionRates: ['0.01%', '0.04%', '0.00%', '0.02%'] }
        ]},
        { department: '轧钢事业部-棒线材深加工(棒)', indicators: [
          { name: '综合成材率', target: 92.60, actual: 92.60, unit: '%', isHigherBetter: true, values: [92.60, 92.60, 92.60, 92.60], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }
        ]},
        { department: '轧钢事业部-棒线材深加工(线)', indicators: [
          { name: '控线材综合成材率', target: 98.55, actual: 98.56, unit: '%', isHigherBetter: true, values: [98.55, 98.55, 98.56, 98.55], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }
        ]},
        { department: '轧钢事业部-线材深加工', indicators: [
          { name: '冷镦材综合成材率', target: 96.36, actual: 96.02, unit: '%', isHigherBetter: true, values: [96.36, 96.36, 96.02, 94.44], completionRates: ['0.00%', '0.00%', '0.00%', '-1.94%'] }
        ]},
        { department: '马科托钢球-马科托钢球', indicators: [
          { name: '综合成材率', target: 93.19, actual: 93.61, unit: '%', isHigherBetter: true, values: [93.13, 93.54, 93.61, 93.80], completionRates: ['-0.06%', '0.42%', '0.00%', '0.20%'] },
          { name: '氧气消耗', target: 44.64, actual: 44.63, unit: 'm3/t', isHigherBetter: false, values: [44.64, 44.64, 44.63, 44.64], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }
        ]},
        { department: '特板事业部-特钢炼钢分厂', indicators: [
          { name: '氧气消耗', target: 44.64, actual: 44.63, unit: 'm3/t', isHigherBetter: false, values: [44.64, 44.64, 44.63, 44.64], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] },
          { name: '转炉工序能耗', target: -28.50, actual: -29.91, unit: 'kgCe/t', isHigherBetter: true, values: [28.93, 29.67, 29.91, 29.55], completionRates: ['-0.98%', '-3.75%', '-3.58%', '-3.89%'] },
          { name: '钢铁料收得率', target: 91.60, actual: 91.27, unit: '%', isHigherBetter: true, values: [91.80, 91.31, 91.27, 91.27], completionRates: ['0.22%', '-0.33%', '-0.37%', '-0.37%'] },
          { name: '综合石灰消耗', target: 43.67, actual: 46.07, unit: 'kg/t', isHigherBetter: false, values: [46.01, 47.19, 46.07, 43.97], completionRates: ['2.84%', '3.27%', '2.14%', '2.07%'] }
        ]},
        { department: '特板事业部-中板分厂', indicators: [
          { name: '综合命中率', target: 98.62, actual: 98.63, unit: '%', isHigherBetter: true, values: [98.63, 98.65, 98.63, 98.63], completionRates: ['0.00%', '0.02%', '0.00%', '0.00%'] },
          { name: '综合成材率', target: 92.55, actual: 92.04, unit: '%', isHigherBetter: true, values: [92.63, 92.03, 92.04, 92.65], completionRates: ['0.09%', '-0.51%', '-0.51%', '0.02%'] },
          { name: '整客户交付率', target: 98.75, actual: 98.78, unit: '%', isHigherBetter: true, values: [98.75, 98.77, 98.78, 98.75], completionRates: ['0.00%', '0.02%', '0.00%', '0.00%'] },
          { name: '热轧工序能耗', target: 45.02, actual: 43.32, unit: 'kgCe/t', isHigherBetter: false, values: [44.60, 44.15, 43.32, 43.80], completionRates: ['-0.93%', '-1.25%', '-1.68%', '-0.70%'] },
          { name: '热装比', target: 75.00, actual: 75.85, unit: '%', isHigherBetter: true, values: [75.40, 77.64, 75.85, 74.05], completionRates: ['0.53%', '2.18%', '0.00%', '-1.95%'] }
        ]},
        { department: '特板事业部-厚板分厂', indicators: [
          { name: '综合命中率', target: 97.49, actual: 97.27, unit: '%', isHigherBetter: true, values: [97.49, 97.53, 97.27, 97.52], completionRates: ['0.00%', '0.04%', '-0.26%', '0.05%'] },
          { name: '综合成材率', target: 90.91, actual: 90.76, unit: '%', isHigherBetter: true, values: [90.41, 90.79, 90.76, 90.78], completionRates: ['-0.55%', '0.32%', '-0.26%', '0.02%'] },
          { name: '整客户交付率', target: 96.34, actual: 96.34, unit: '%', isHigherBetter: true, values: [96.37, 96.35, 96.34, 96.31], completionRates: ['0.03%', '-0.02%', '0.00%', '-0.03%'] },
          { name: '热轧工序能耗', target: 48.62, actual: 45.85, unit: 'kgCe/t', isHigherBetter: false, values: [46.27, 46.01, 45.85, 47.11], completionRates: ['-2.79%', '-2.56%', '-2.34%', '-1.54%'] },
          { name: '热装比(200℃)', target: 50.00, actual: 31.23, unit: '%', isHigherBetter: true, values: [50.60, 51.28, 31.23, 50.28], completionRates: ['1.20%', '2.56%', '0.00%', '-1.56%'] }
        ]},
        { department: '特板事业部-钢材深加工', indicators: [
          { name: '整客户交付率', target: 99.11, actual: 99.12, unit: '%', isHigherBetter: true, values: [99.11, 99.10, 99.12, 99.12], completionRates: ['0.00%', '-0.01%', '0.00%', '0.00%'] },
          { name: '综合命中率', target: 99.73, actual: 99.75, unit: '%', isHigherBetter: true, values: [99.73, 99.74, 99.75, 99.74], completionRates: ['0.00%', '-0.01%', '0.00%', '0.00%'] }
        ]},
        { department: '动力事业部-热电', indicators: [
          { name: '标煤产汽率', target: 10.85, actual: 10.90, unit: 't/tCe', isHigherBetter: true, values: [10.87, 10.89, 10.90, 10.92], completionRates: ['0.19%', '0.20%', '0.00%', '0.00%'] }
        ]},
        { department: '动力事业部-供电工区', indicators: [
          { name: '供电功率因数', target: 95.00, actual: 98.00, unit: '%(95.3-100)', isHigherBetter: true, values: [98.66, 98.66, 98.00, 98.00], completionRates: ['3.20%', '3.20%', '0.00%', '0.00%'] }
        ]},
        { department: '动力事业部-水处理分厂', indicators: [
          { name: '吨钢软水水', target: 1.62, actual: 1.63, unit: 'm3/t', isHigherBetter: false, values: [1.62, 1.61, 1.63, 1.69], completionRates: ['0.62%', '-0.62%', '0.00%', '0.62%'] },
          { name: '吨钢热水处理量', target: 21.20, actual: 20.25, unit: 't/t钢', isHigherBetter: false, values: [19.89, 20.14, 20.25, 20.28], completionRates: ['-6.19%', '-3.57%', '0.00%', '1.40%'] }
        ]},
        { department: '动力事业部-制氧分厂', indicators: [
          { name: '氧气就耗率', target: 0.02, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }
        ]},
        { department: '动力事业部-煤气分厂', indicators: [
          { name: '高炉煤气就耗率', target: 0.02, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }
        ]},
        { department: '物流事业部-储运公司', indicators: [
          { name: '360混匀矿水分合格率', target: 90.56, actual: 91.32, unit: '%', isHigherBetter: true, values: [90.63, 90.68, 91.32, 90.68], completionRates: ['0.07%', '0.05%', '0.00%', '0.00%'] },
          { name: '混匀矿稳定率', target: 97.82, actual: 97.89, unit: '%', isHigherBetter: true, values: [97.83, 98.01, 97.89, 98.01], completionRates: ['0.18%', '0.17%', '0.00%', '0.00%'] }
        ]},
        { department: '检修事业部-检修分厂', indicators: [
          { name: '热修机率', target: 0.10, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }
        ]}
      ],
      completionData1: 
      [
      {department:'钙业分厂'},
      {department:'矿渣微粉'},
      {department:'烧结分厂-1#烧结'},
      {department:'烧结分厂-2#烧结'},
      {department:'炼铁分厂-1#高炉'},
      {department:'炼铁分厂-2#高炉'},
      {department:'炼铁分厂-3#高炉'},
      {department:'炼铁分厂-小喷煤'},
      {department:'炼铁分厂-大喷煤'},
      {department:'一炼钢'},
      {department:'二炼钢'},
      {department:'一轧钢'},
      {department:'二轧-大棒'},
      {department:'二轧-小棒'},
      {department:'特板炼钢'},
      {department:'3500中板'},
      {department:'4300厚板'},
      {department:'4300水处理'},
      {department:'热处理'},
      {department:'高线分厂'},
      {department:'线材深加工'},
      {department:'棒材深加工'},
      {department:'热电分厂-热电'},
      {department:'热电分厂-亚临界'},
      {department:'热电分厂-余热'},
      {department:'热电分厂-鼓风'},
      {department:'制氧分厂'},
      {department:'制氧分厂-一期'},
      {department:'制氧分厂-三期'},
      {department:'制氧分厂-空压站'},
      {department:'水处理-一期'},
      {department:'水处理-二期'},
      {department:'水处理-三期'},
      {department:'煤气分厂'},
      {department:'供电一区'},
      {department:'兴澄钢球'},
      {department:'兴澄港务'},
      {department:'储运公司'},
      {department:'综合利用'},
      {department:'合金炉分厂'},
      {department:'物管部'},
      {department:'后勤部'},
      {department:'其他'},
      {department:'损耗'},
      {department:'合计'},

      ],
      // 未完成指标数据
      incompleteData: [],
      
      // 能源指标统计数据
      energyStats: [
        { title: '综合能耗', value: '5.21吨标煤/吨钢', change: -2.3, status: 'good' },
        { title: '水资源消耗', value: '3.8立方米/吨钢', change: -1.5, status: 'good' },
        { title: '电力消耗', value: '485千瓦时/吨钢', change: 0.8, status: 'warning' },
        { title: '煤气回收率', value: '98.5%', change: 1.2, status: 'good' },
        { title: '余热回收率', value: '75.2%', change: 2.5, status: 'good' },
        { title: '二氧化碳排放', value: '1.85吨/吨钢', change: -3.2, status: 'good' }
      ],
      
      // 能源消耗数据
      energyConsumptionData: [
        { name: '电力', value: 35 },
        { name: '煤炭', value: 25 },
        { name: '天然气', value: 15 },
        { name: '蒸汽', value: 10 },
        { name: '其他', value: 15 }
      ],
      
      // 部门能源消耗数据（新增）- 按月统计
      departmentEnergyData: {
        months: ['1月', '2月', '3月', '4月'],
        departments: ['炼铁事业部', '炼钢事业部', '轧钢事业部', '马科托钢球', '特板事业部', '动力事业部', '物流事业部', '检修事业部'],
        electricity: { // 电力消耗 (万千瓦时)
          '炼铁事业部': [1250, 1180, 1220, 1260],
          '炼钢事业部': [1850, 1790, 1810, 1880],
          '轧钢事业部': [1450, 1420, 1480, 1440],
          '马科托钢球': [420, 410, 430, 425],
          '特板事业部': [980, 950, 970, 990],
          '动力事业部': [320, 310, 330, 325],
          '物流事业部': [180, 175, 185, 182],
          '检修事业部': [150, 145, 155, 152]
        },
        water: { // 水资源消耗 (万吨)
          '炼铁事业部': [85, 82, 86, 88],
          '炼钢事业部': [120, 115, 118, 122],
          '轧钢事业部': [95, 92, 96, 94],
          '马科托钢球': [28, 27, 29, 28.5],
          '特板事业部': [65, 63, 66, 67],
          '动力事业部': [180, 175, 185, 182],
          '物流事业部': [15, 14, 16, 15.5],
          '检修事业部': [12, 11.5, 12.5, 12.2]
        },
        gas: { // 天然气消耗 (万立方米)
          '炼铁事业部': [320, 310, 325, 330],
          '炼钢事业部': [480, 470, 485, 490],
          '轧钢事业部': [380, 370, 385, 375],
          '马科托钢球': [110, 105, 112, 108],
          '特板事业部': [250, 245, 255, 260],
          '动力事业部': [85, 82, 87, 86],
          '物流事业部': [45, 43, 46, 44],
          '检修事业部': [35, 34, 36, 35.5]
        },
        steam: { // 蒸汽消耗 (万吨)
          '炼铁事业部': [45, 43, 46, 47],
          '炼钢事业部': [65, 63, 66, 67],
          '轧钢事业部': [52, 50, 53, 51],
          '马科托钢球': [15, 14.5, 15.5, 15.2],
          '特板事业部': [35, 34, 36, 37],
          '动力事业部': [12, 11.5, 12.5, 12.2],
          '物流事业部': [8, 7.8, 8.2, 8.1],
          '检修事业部': [6, 5.8, 6.2, 6.1]
        }
      },
      
      // 能源类型选项
      energyTypes: [
        { label: '电力消耗', value: 'electricity', unit: '千瓦时', color: '#2f80ed' },
        { label: '水资源消耗', value: 'water', unit: '吨', color: '#2d9cdb' },
        { label: '天然气消耗', value: 'gas', unit: '立方米', color: '#1a73e8' },
        { label: '蒸汽消耗', value: 'steam', unit: '吨', color: '#27ae60' }
      ],
      
      // 能源成本数据
      energyCostData: {
        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        electricity: [320, 332, 301, 334, 390, 330, 320, 315, 310, 325, 315, 318],
        coal: [220, 182, 191, 234, 290, 330, 310, 295, 300, 285, 270, 275],
        gas: [150, 232, 201, 154, 190, 180, 165, 175, 190, 195, 205, 210],
        steam: [98, 77, 101, 99, 120, 125, 110, 100, 105, 115, 110, 120]
      },
      
      // 能源详情数据 - 当日消耗
      energyDetailData: [
        { 
          name: '转炉煤气', 
          category: 'gas',
          value: 125.6, 
          unit: '万m³', 
          target: 130, 
          warning: 140, 
          danger: 150, 
          status: 'normal',
          trend: -2.1 // 相比昨日变化百分比
        },
        { 
          name: '高炉煤气', 
          category: 'gas',
          value: 287.3, 
          unit: '万m³', 
          target: 280, 
          warning: 300, 
          danger: 320, 
          status: 'warning',
          trend: 5.3
        },
        { 
          name: '焦炉煤气', 
          category: 'gas',
          value: 98.4, 
          unit: '万m³', 
          target: 100, 
          warning: 110, 
          danger: 120, 
          status: 'normal',
          trend: -1.2
        },
        { 
          name: '天然气', 
          category: 'gas',
          value: 45.7, 
          unit: '万m³', 
          target: 40, 
          warning: 45, 
          danger: 50, 
          status: 'danger',
          trend: 12.5
        },
        { 
          name: '饱和蒸汽', 
          category: 'steam',
          value: 56.2, 
          unit: '万吨', 
          target: 55, 
          warning: 60, 
          danger: 65, 
          status: 'normal',
          trend: 1.8
        },
        { 
          name: '过热蒸汽', 
          category: 'steam',
          value: 32.8, 
          unit: '万吨', 
          target: 30, 
          warning: 35, 
          danger: 40, 
          status: 'warning',
          trend: 7.2
        },
        { 
          name: '工业用水', 
          category: 'water',
          value: 142.5, 
          unit: '万吨', 
          target: 140, 
          warning: 150, 
          danger: 160, 
          status: 'normal',
          trend: 1.5
        },
        { 
          name: '循环冷却水', 
          category: 'water',
          value: 285.3, 
          unit: '万吨', 
          target: 280, 
          warning: 300, 
          danger: 320, 
          status: 'warning',
          trend: 3.8
        },
        { 
          name: '高压电力', 
          category: 'electricity',
          value: 1856.4, 
          unit: '万kWh', 
          target: 1800, 
          warning: 1900, 
          danger: 2000, 
          status: 'warning',
          trend: 4.2
        },
        { 
          name: '中压电力', 
          category: 'electricity',
          value: 945.2, 
          unit: '万kWh', 
          target: 950, 
          warning: 1000, 
          danger: 1050, 
          status: 'normal',
          trend: -0.8
        }
      ],
      
      // 分厂能源消耗数据 - 按月统计
      factoryEnergyData: {
        months: ['1月', '2月', '3月', '4月', '5月', '6月'],
        electricity: { // 电力消耗 (万千瓦时)
          '钙业分厂':[1400884,1434005,1435766,1376319,1301095,1269630],
          '矿渣微粉':[5805794,2131847,6089046,6100999,6417335,6478262],
          '烧结分厂-1#烧结':[13117902,12943568,11061444,10812393,11623702,11032539],
          '烧结分厂-2#烧结':[13033926,10436634,9287884,9769316,9879397,10565760],
          '炼铁分厂-1#高炉':[0,0,0,0,0,0],
          '炼铁分厂-2#高炉':[66940,0,0,0,0,0],
          '炼铁分厂-3#高炉':[0,0,0,0,0,0],
          '炼铁分厂-小喷煤':[0,0,0,0,0,0],
          '一炼钢':[30768167,30125781,30297463,28980497,29774159,31343397],
          '二炼钢':[22488495,21968943,21787916,22171067,21242115,21788119],
          '一轧钢':[5795777,5452204,5711051,5648575,5496447,5733403],
          '二轧-大棒':[3246250,3195091,3268836,3363082,3262553,3466935],
          '二轧-小棒':[5059018,4954511,4811987,5053456,4687922,4852370],
          '特板炼钢':[30387862,29842019,26431716,29469372,29271035,29035520],
          '3500中板':[7706300,6644420,7397716,7328102,7206215,7421179],
          '4300厚板':[13519112,12464662,10028536,12881286,12674940,13166679],
          '热处理':[2813937,2726501,2275425,2384412,2206548,3135105],
          '高线分厂':[7621452,7822538,7920470,7152446,7071412,7342066],
          '线材深加工':[968654,878075,925284,902573,915434,915053],
          '棒材深加工':[3330295,3280875,3350518,3481898,3304357,3326433],
          '热电分厂-热电':[8709519,9757840,10102720,9660480,9307680,7473520],
          '热电分厂-亚临界':[2842245,4042080,3634400,4309800,4358435,4033588],
          '热电分厂-余热':[133500,247280,112640,112640,218560,136480],
          '热电分厂-鼓风':[12146140,21995616,21969803,23377762,28428976,37036933],
          '制氧分厂-一期':[26203549,26456523,20347464,26646735,25523888,27290746],
          '制氧分厂-三期':[29239632,27886434,29274657,26601919,26162015,26865678],
          '制氧分厂-空压站':[6092759,6483609,6455930,6661039,6369297,6464792],
          '水处理-一期':[2467240,2470442,2515829,2549457,3222031,2884069],
          '水处理-二期':[4951897,4902986,4843723,5040984,5021708,5263224],
          '水处理-三期':[5224649,5320012,5060813,5407588,5488715,5816560],
          '煤气分厂':[643132,693466,657052,659543,624830,620973],
          '供电一区':[100415,103537,103906,133611,166027,180227],
          '兴澄钢球':[1087981,840818,981751,1057275,909275,1188557],
          '兴澄港务':[68023,59481,69918,63336,44541,65389],
          '储运公司':[5759324,5859975,5352206,5316640,5378337,5644938],
          '综合利用':[1046763,1048737,1103178,1006943,1082975,1068868],
          '合金炉分厂':[2769092,4321005,3221559,4932761,4789800,5878980],
          '物管部':[39902,43498,34953,29662,24373,24227],
          '后勤部':[46436,51144,39739,36459,36817,36596],
          '其他':[824375,775107,749943,688365,764337,978520],
          '物管部':[1295140,664000,621920,541480,576620,836960],
          '合计':[278822476,280325264,269335161,281710272,284833903,300662274]
          // '炼铁分厂': [850, 820, 840, 860],
          // '烧结分厂': [400, 380, 390, 410],
          // '一炼钢': [950, 920, 940, 960],
          // '二炼钢': [900, 880, 890, 920],
          // '综合利用': [280, 270, 290, 275],
          // '一轧钢': [380, 370, 390, 375],
          // '二轧钢': [360, 350, 370, 355],
          // '三轧钢': [340, 330, 350, 335],
          // '特殊钢轧材': [320, 310, 330, 315],
          // '棒材轧制厂': [300, 290, 310, 295],
          // '棒线材深加工(棒)': [180, 170, 190, 175],
          // '棒线材深加工(线)': [160, 150, 170, 155],
          // '线材深加工': [140, 130, 150, 135],
          // '马科托钢球': [420, 410, 430, 425],
          // '特钢炼钢分厂': [380, 370, 390, 375],
          // '中板分厂': [360, 350, 370, 355],
          // '厚板分厂': [340, 330, 350, 335],
          // '钢材深加工': [320, 310, 330, 315],
          // '热电': [180, 170, 190, 175],
          // '供电工区': [160, 150, 170, 155],
          // '水处理分厂': [140, 130, 150, 135],
          // '制氧分厂': [120, 110, 130, 115],
          // '煤气分厂': [100, 90, 110, 95],
          // '储运公司': [180, 175, 185, 182],
          // '检修分厂': [150, 145, 155, 152]
        },
        water: { // 水资源消耗 (万吨)
          '钙业分厂':[2517,2376,2253,2173,2259,2301],
          '矿渣微粉':[2890,2591,2478,2358,2381,2256],
          '烧结分厂-1#烧结':[41290,40443,39591,40136,40852,40146],
          '烧结分厂-2#烧结':[71804,67539,69009,69947,72050,70344],
          '炼铁分厂-1#高炉':[20870,21082,21231,26729,24702,28188],
          '炼铁分厂-2#高炉':[61032,65615,56218,65690,63567,67033],
          '炼铁分厂-3#高炉':[70604,69964,78613,85914,85358,99016],
          '炼铁分厂-喷煤（0#2#高炉）':[2308,2457,2897,3017,2851,3597],
          '一炼钢':[4631152,4529160,4609510,4645449,4536932,4618563],
          '二炼钢':[9104629,8974584,8707241,8864449,8574110,8780493],
          '一轧钢':[1613416,1566303,1603660,1604021,1574222,1641956],
          '二轧-大棒':[1549976,1473652,1448998,1482840,1420848,1461097],
          '二轧-小棒':[1761922,1636518,1599618,1707777,1611290,1566453],
          '特板炼钢':[11065202,10861897,10078370,10798271,10539246,10414072],
          '3500中板':[2528768,2531102,2577106,2614333,2509852,2746164],
          '4300厚板':[53602,52973,47100,51727,48993,52439],
          '4300水处理':[51957,59997,58691,55515,61891,66474],
          '热处理':[15873,13257,11536,10213,10796,10018],
          '高线分厂':[24783,21328,19082,23117,24548,23518],
          '热电分厂-热电':[32654,35991,46976,34907,35831,30211],
          '热电分厂-鼓风':[66781,60578,74506,84715,56847,60222],
          '热电分厂-亚临界':[50702,42898,53360,80937,75090,83220],
          '热电分厂-净水厂直供原水':[82710,66641,58670,59022,51034,51739],
          '制氧分厂-一期':[48107,89953,40665,51367,56605,55866],
          '制氧分厂-三期':[27136,13397,10726,21896,25682,11716],
          '制氧分厂-空压站':[3928,3563,4385,4983,3343,3542],
          '水处理-一期':[666457,684861,699017,706374,703497,737291],
          '水处理-二期':[283733,290660,245217,243883,245300,285485],
          '水处理-三期':[162012,140737,143298,211329,206303,245132],
          '供电一区':[2273,2357,2502,2597,2385,2153],
          '储运公司':[41906,40362,41980,42361,41963,40779],
          '综合利用':[25117,24586,19931,25926,22555,15964],
          '合金炉分厂':[939944,1406061,1129690,1591196,1392990,1834398],
          '后勤综合楼':[606264,473027,520855,233384,168146,365997],
          '公用':[79065,79871,79634,79871,80971,81341],
          '合计':[35793384,35448381,34204614,35628424,34375290,35599184]
          // '炼铁分厂': [55, 53, 56, 57],
          // '烧结分厂': [30, 29, 31, 32],
          // '一炼钢': [65, 63, 66, 67],
          // '二炼钢': [60, 58, 61, 62],
          // '综合利用': [20, 19, 21, 20.5],
          // '一轧钢': [25, 24, 26, 25.5],
          // '二轧钢': [23, 22, 24, 23.5],
          // '三轧钢': [22, 21, 23, 22.5],
          // '特殊钢轧材': [21, 20, 22, 21.5],
          // '棒材轧制厂': [20, 19, 21, 20.5],
          // '棒线材深加工(棒)': [12, 11, 13, 12.5],
          // '棒线材深加工(线)': [11, 10, 12, 11.5],
          // '线材深加工': [9, 8, 10, 9.5],
          // '马科托钢球': [28, 27, 29, 28.5],
          // '特钢炼钢分厂': [25, 24, 26, 25.5],
          // '中板分厂': [23, 22, 24, 23.5],
          // '厚板分厂': [22, 21, 23, 22.5],
          // '钢材深加工': [21, 20, 22, 21.5],
          // '热电': [65, 63, 67, 66],
          // '供电工区': [45, 43, 47, 46],
          // '水处理分厂': [70, 68, 72, 71],
          // '制氧分厂': [55, 53, 57, 56],
          // '煤气分厂': [45, 43, 47, 46],
          // '储运公司': [15, 14, 16, 15.5],
          // '检修分厂': [12, 11.5, 12.5, 12.2]
        },
        gas: {
          '钙业分厂':[2080,14317,12875,6240,0,0],
          '矿渣微粉':[0,0,0,0,0,0],
          '烧结分厂-1#烧结':[928,358,1054,2476,1040,1040],
          '烧结分厂-2#烧结':[13471,3440,11592,14448,19094,38272],
          '炼铁分厂-1#高炉':[4264,2392,1832,3952,1456,2184],
          '炼铁分厂-2#高炉':[3744,6344,14808,4104,3432,5616],
          '炼铁分厂-3#高炉':[48880,74483,62541,92272,17992,47889],
          '炼铁分厂-小喷煤':[936,1144,1040,1212,936,1040],
          '炼铁分厂-大喷煤':[42432,35464,32200,20696,15288,17784],
          '一炼钢':[321044,441179,438423,450993,381575,351275],
          '二炼钢':[150844,208889,212605,212274,204372,195425],
          '一轧钢':[2857,12084,12120,6818,6230,16217],
          '二轧-大棒':[23824,30000,14520,10630,24036,24489],
          '二轧-小棒':[16000,12487,8926,4555,16024,10496],
          '特板炼钢':[154790,218832,190568,135757,143405,140886],
          '3500中板':[619233,644217,767563,908906,890002,926636],
          '4300厚板':[492656,621775,785247,500988,563886,792919],
          '热处理':[1093740,1370389,1296365,1408988,1170360,1299076],
          '高线分厂':[214,96,298,207,100,204],
          '棒材深加工':[1133600,1177473,1139440,1214108,1141737,1153930],
          '线材深加工':[614092,501810,509750,552280,559440,517720],
          '热电分厂-热电':[15713,33472,183549,21279,82525,33965],
          '热电分厂-鼓风':[58638,35678,204014,28303,60770,58240],
          '热电分厂-亚临界':[94063,60326,27315,63941,90389,93837],
          '煤气分厂':[6285,7289,7616,7537,7561,3075],
          '兴澄钢球':[194970,206307,208890,225422,209463,215730],
          '综合利用':[0,0,0,0,0,0],
          '合计':[5109298,5720245,6145151,5898386,5611113,5947945]
          
           // 天然气消耗 (万立方米)
          // '炼铁分厂': [220, 210, 225, 230],
          // '烧结分厂': [100, 95, 105, 110],
          // '一炼钢': [250, 240, 255, 260],
          // '二炼钢': [230, 220, 235, 240],
          // '综合利用': [75, 70, 78, 76],
          // '一轧钢': [95, 90, 98, 96],
          // '二轧钢': [90, 85, 93, 91],
          // '三轧钢': [85, 80, 88, 86],
          // '特殊钢轧材': [80, 75, 83, 81],
          // '棒材轧制厂': [75, 70, 78, 76],
          // '棒线材深加工(棒)': [45, 40, 48, 46],
          // '棒线材深加工(线)': [40, 35, 43, 41],
          // '线材深加工': [35, 30, 38, 36],
          // '马科托钢球': [110, 105, 112, 108],
          // '特钢炼钢分厂': [95, 90, 98, 96],
          // '中板分厂': [90, 85, 93, 91],
          // '厚板分厂': [85, 80, 88, 86],
          // '钢材深加工': [80, 75, 83, 81],
          // '热电': [35, 30, 38, 36],
          // '供电工区': [25, 20, 28, 26],
          // '水处理分厂': [20, 15, 23, 21],
          // '制氧分厂': [15, 10, 18, 16],
          // '煤气分厂': [10, 5, 13, 11],
          // '储运公司': [45, 43, 46, 44],
          // '检修分厂': [35, 34, 36, 35.5]
        },
        steam: { // 蒸汽消耗 (万吨)
          '钙业分厂':[0,0,0,0,0,0],
          '矿渣微粉':[0,0,0,0,0,0],
          '烧结分厂-1#烧结':[0,0,0,0,0,0],
          '烧结分厂-2#烧结':[2368,2379,1765,1615,1422,1663],
          '炼铁分厂-1#高炉':[578,637,485,554,388,671],
          '炼铁分厂-2#高炉':[295,141,109,265,419,312],
          '炼铁分厂-3#高炉':[1445,2143,1633,124,0,127],
          '一炼钢':[116,388,50,50,96,164],
          '二炼钢':[12927,16443,17638,17071,16843,16351],
          '一轧钢':[0,0,0,0,0,0],
          '二轧-大棒':[209,222,253,133,198,116],
          '二轧-小棒':[193,204,233,123,182,108],
          '特板炼钢':[22968,22336,18819,21647,21604,21708],
          '3500中板':[0,0,0,0,0,0],
          '4300厚板':[0,0,0,0,0,0],
          '高线分厂':[0,0,0,0,0,0],
          '棒材深加工':[0,0,0,0,0,0],
          '线材深加工':[2240,1616,1364,1121,1082,920],
          '热电分厂-热电':[85199,90583,83075,93108,89615,95156],
          '热电分厂-鼓风':[0,0,0,0,0,0],
          '热电分厂-热水':[0,0,0,0,0,0],
          '煤气分厂':[0,0,0,0,0,0],
          '制氧分厂':[400,400,400,400,400,400],
          '后勤部':[0,0,0,0,0,0],
          '外销蒸汽':[32568,17534,27334,24311,21126,19504],
          '综合利用':[216,31,16,19,32,46],
          '损耗':[1270,1166,1088,907,838,825],
          '合计':[162992,156223,154262,161448,154245,158071]
          // '炼铁分厂': [30, 28, 31, 32],
          // '烧结分厂': [15, 13, 16, 17],
          // '一炼钢': [35, 33, 36, 37],
          // '二炼钢': [30, 28, 31, 32],
          // '综合利用': [10, 9, 11, 10.5],
          // '一轧钢': [13, 12, 14, 13.5],
          // '二轧钢': [12, 11, 13, 12.5],
          // '三轧钢': [11, 10, 12, 11.5],
          // '特殊钢轧材': [10, 9, 11, 10.5],
          // '棒材轧制厂': [9, 8, 10, 9.5],
          // '棒线材深加工(棒)': [5, 4, 6, 5.5],
          // '棒线材深加工(线)': [4, 3, 5, 4.5],
          // '线材深加工': [3, 2, 4, 3.5],
          // '马科托钢球': [15, 14.5, 15.5, 15.2],
          // '特钢炼钢分厂': [13, 12, 14, 13.5],
          // '中板分厂': [12, 11, 13, 12.5],
          // '厚板分厂': [11, 10, 12, 11.5],
          // '钢材深加工': [10, 9, 11, 10.5],
          // '热电': [5, 4, 6, 5.5],
          // '供电工区': [3, 2, 4, 3.5],
          // '水处理分厂': [2, 1, 3, 2.5],
          // '制氧分厂': [1.5, 0.5, 2.5, 2],
          // '煤气分厂': [1, 0.8, 1.2, 1.1],
          // '储运公司': [8, 7.8, 8.2, 8.1],
          // '检修分厂': [6, 5.8, 6.2, 6.1]
        }
      },
      
      // 关键能源指标当日值与昨日对比
      keyEnergyIndicators: [],
      // [
      //   {
      //     name: '氧气',
      //     today: 46.80, // 超出目标范围上限
      //     yesterday: 44.64,
      //     unit: 'm³/t',
      //     targetMin: 42.00,
      //     targetMax: 45.00,
      //     change: 4.84,
      //     status: 'danger'
      //   },
      //   {
      //     name: '氮气',
      //     today: 14.82,
      //     yesterday: 15.21,
      //     unit: 'm³/t',
      //     targetMin: 14.00,
      //     targetMax: 16.00,
      //     change: -2.56,
      //     status: 'good'
      //   },
      //   {
      //     name: '氩气',
      //     today: 0.85,
      //     yesterday: 0.88,
      //     unit: 'm³/t',
      //     targetMin: 0.80,
      //     targetMax: 0.90,
      //     change: -3.41,
      //     status: 'good'
      //   },
      //   {
      //     name: '空气',
      //     today: 450, // 低于目标范围下限
      //     yesterday: 481,
      //     unit: 'm³/h',
      //     targetMin: 460,
      //     targetMax: 500,
      //     change: -6.44,
      //     status: 'danger'
      //   },
      //   {
      //     name: '高炉煤气',
      //     today: 985.3,
      //     yesterday: 962.7,
      //     unit: 'm³/t',
      //     targetMin: 950.0,
      //     targetMax: 1000.0,
      //     change: 2.35,
      //     status: 'good'
      //   },
      //   {
      //     name: '转炉煤气',
      //     today: 85.2,
      //     yesterday: 88.4,
      //     unit: 'm³/t',
      //     targetMin: 80.0,
      //     targetMax: 90.0,
      //     change: -3.62,
      //     status: 'good'
      //   },
      //   {
      //     name: '焦炉煤气',
      //     today: 41.3,
      //     yesterday: 42.8,
      //     unit: 'm³/t',
      //     targetMin: 40.0,
      //     targetMax: 45.0,
      //     change: -3.50,
      //     status: 'good'
      //   },
      //   {
      //     name: '饱和蒸汽',
      //     today: 0.52,
      //     yesterday: 0.54,
      //     unit: 't/t',
      //     targetMin: 0.50,
      //     targetMax: 0.58,
      //     change: -3.70,
      //     status: 'good'
      //   },
      //   {
      //     name: '过热蒸汽',
      //     today: 0.33,
      //     yesterday: 0.31,
      //     unit: 't/t',
      //     targetMin: 0.30,
      //     targetMax: 0.35,
      //     change: 6.45,
      //     status: 'warning'
      //   },
      //   {
      //     name: '低压蒸汽',
      //     today: 0.21,
      //     yesterday: 0.23,
      //     unit: 't/t',
      //     targetMin: 0.20,
      //     targetMax: 0.25,
      //     change: -8.70,
      //     status: 'good'
      //   },
      //   {
      //     name: '天然气',
      //     today: 24.3,
      //     yesterday: 25.1,
      //     unit: 'm³/t',
      //     targetMin: 22.0,
      //     targetMax: 26.0,
      //     change: -3.19,
      //     status: 'good'
      //   },
      //   {
      //     name: '压缩天然气',
      //     today: 2.85,
      //     yesterday: 2.91,
      //     unit: 'm³/t',
      //     targetMin: 2.70,
      //     targetMax: 3.00,
      //     change: -2.06,
      //     status: 'good'
      //   }
      // ],
      
      // 能源指标是否超出目标范围标记
      isEnergyChartWarning: {
        electricity: false,
        water: false,
        gas: false,
        steam: false
      },
      
      // 能源目标范围
      energyTargetRanges: {
        electricity: { min: 800, max: 900, unit: '千瓦时' },
        water: { min: 50, max: 60, unit: '吨' },
        gas: { min: 200, max: 240, unit: '立方米' },
        steam: { min: 25, max: 35, unit: '吨' }
      },
    }
  },
  computed: {
    // 计算所有部门的完成率数据（只显示分厂层级）
    departmentCompletionRates() {
      // 按部门分组计算完成率，只处理有"-"的分厂层级
      const departmentRates = this.completionData
        .filter(dept => dept.department.includes('-')) // 只选择分厂层级
        .map(dept => {
          const indicators = dept.indicators
          let completedCount = 0
          let totalIndicators = indicators.length
          
          // 计算已完成指标数量，使用4月份数据
          indicators.forEach(indicator => {
            // 使用4月份数据（第3个索引）
            const actual = indicator.values[3] // 第四周/4月份的实际值
            const target = indicator.target
            
            const isCompleted = indicator.isHigherBetter 
              ? actual >= target 
              : actual <= target
            
            if (isCompleted) completedCount++
          })
          
          // 计算完成率
          const completionRate = (completedCount / totalIndicators) * 100
          
          return {
            department: dept.department.split('-')[1], // 只显示分厂名称
            fullDepartment: dept.department, // 保存完整部门名称用于数据查询
            completionRate: parseFloat(completionRate.toFixed(1)),
            totalIndicators: totalIndicators,
            completedIndicators: completedCount
          }
        })
      
      // 按完成率从高到低排序
      return departmentRates.sort((a, b) => b.completionRate - a.completionRate)
    },
    
    // 计算所有指标的完成率（4月份数据）
    allIndicatorCompletionRates() {
      const allIndicators = []
      
      this.completionData.forEach(dept => {
        if (dept.department.includes('-')) { // 只选择分厂层级
          const departmentName = dept.department.split('-')[1]
          
          dept.indicators.forEach(indicator => {
            // 直接使用04月列的完成率数据
            // 检查是否有最后一列（04月）的完成率数据
            const completionRateStr = indicator.completionRates && indicator.completionRates[3]; // 04月完成率
            let completionRate = 0;
            
            // 如果有直接的完成率数据，则使用它
            if (completionRateStr) {
              // 将百分比字符串转换为数值，例如 "-1.31%" -> -1.31
              completionRate = parseFloat(completionRateStr.replace('%', ''));
            } else {
              // 如果没有直接数据，则用原来的方法计算
              const actual = indicator.values[3]; // 4月份实际值
              const target = indicator.target;
              
              if (indicator.isHigherBetter) {
                // 如果指标越高越好，完成率 = 实际值/目标值 * 100% - 100%
                completionRate = ((actual / target) * 100) - 100;
              } else {
                // 如果指标越低越好，完成率 = 目标值/实际值 * 100% - 100%
                completionRate = ((target / actual) * 100) - 100;
              }
            }
            
            // 对完成率进行处理
            // 非数字完成率调整为0
            if (isNaN(completionRate) || !isFinite(completionRate)) {
              completionRate = 0;
            }
            
            // 限制最大完成率绝对值为200%
            if (Math.abs(completionRate) > 200) {
              completionRate = completionRate > 0 ? 200 : -200;
            }
            
            allIndicators.push({
              department: departmentName,
              indicator: indicator.name,
              completionRate: parseFloat(completionRate.toFixed(1)),
              target: indicator.target,
              actual: indicator.values[3], // 4月份实际值
              unit: indicator.unit,
              isHigherBetter: indicator.isHigherBetter
            });
          });
        }
      });
      
      // 按完成率从高到低排序
      return allIndicators.sort((a, b) => b.completionRate - a.completionRate);
    },
    
    // 直接使用所有指标完成率数据
    paginatedIndicatorRates() {
      return this.allIndicatorCompletionRates
    },
    
    // 获取事业部列表（用于筛选）
    businessUnits() {
      const units = new Set()
      this.completionData.forEach(dept => {
        if (dept.department.includes('-')) {
          units.add(dept.department.split('-')[0])
        } else {
          units.add(dept.department)
        }
      })
      return Array.from(units)
    },
    
    // 获取当前选择的事业部下的分厂列表
    currentBusinessUnitDepartments() {
      if (!this.currentBusinessUnit) return []
      
      return this.completionData
        .filter(dept => dept.department.startsWith(this.currentBusinessUnit + '-'))
        .map(dept => ({
          value: dept.department,
          label: dept.department.split('-')[1]
        }))
    },
    
    // 获取当前部门的指标列表
    currentDepartmentIndicators() {
      const deptData = this.completionData.find(dept => dept.department === this.currentDepartment)
      return deptData ? deptData.indicators : []
    },
    
    // 获取所有分厂列表（用于能源消耗详情）
    allFactories() {
      return this.completionData1.map(dept => dept.department)
    }
  },
  watch: {
    // // 监听事业部变化
    // currentBusinessUnit(newVal) {
    //   if (this.currentBusinessUnitDepartments.length > 0) {
    //     this.currentDepartment = this.currentBusinessUnitDepartments[0].value
    //   }
      
    //   // 启动自动切换
    //   this.startAutoSwitchWithinBusinessUnit()
    // },
    
    // // 监听部门变化，自动选择第一个指标
    // currentDepartment(newVal) {
    //   if (this.currentDepartmentIndicators.length > 0) {
    //     this.currentIndicator = this.currentDepartmentIndicators[0].name
    //   }
    //   this.initMonthlyTrendChart()
    // },
    
    // // 监听指标变化，更新图表
    // currentIndicator() {
    //   this.initMonthlyTrendChart()
    // }
  },
  mounted() {
    // 初始化图表
    this.initCharts()
    
    // 计算未完成指标数据
    this.calculateIncompleteData()
    
    // 确保currentEnergyDept有一个有效的初始值
    this.$nextTick(() => {
      this.initEnergyDetailCharts()
      if (this.allFactories && this.allFactories.length > 0) {
        this.currentEnergyDept = this.allFactories[0]
        // 重新初始化能源详情图表
        this.initEnergyDetailCharts()
      }
    })
    this.loadExcelFromRemote();
    // 启动滚动表格
    this.$nextTick(() => {
      // 复制表格内容以实现无缝滚动
      const table = this.$refs.incompleteTable
      if (table && this.incompleteData.length > 0) {
        // 获取表格内容部分（不包含表头）
        const tableBody = table.querySelector('.el-table__body-wrapper')
        if (tableBody) {
          const originalContent = tableBody.innerHTML
          // 在表格内容后面添加一份相同的内容，而不是整个表格
          tableBody.innerHTML += originalContent
        }
      }
      this.startTableScroll()
    })
    
    // 启动事业部内部自动切换
    this.startAutoSwitchWithinBusinessUnit()
    
    // 启动事业部切换（每30秒切换一次）
    this.startBusinessUnitSwitch()
    
    // 启动指标卡片自动滚动
    this.$nextTick(() => {
      this.initIndicatorCardsScroll()
      
      // 为指标卡片容器添加鼠标悬停事件处理
      const cardsContainer = this.$refs.indicatorCardsContainer
      if (cardsContainer) {
        cardsContainer.addEventListener('mouseover', () => {
          this.indicatorCardsScrollPaused = true
        })
        
        cardsContainer.addEventListener('mouseout', () => {
          this.indicatorCardsScrollPaused = false
        })
      }
    })
    
    // 监听窗口大小变化，重新渲染图表
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    // 清除定时器
    this.stopAutoSwitch()
    
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer)
      
      // 移除表格滚动的事件监听器
      const table = this.$refs.incompleteTable
      if (table) {
        const tableBody = table.querySelector('.el-table__body-wrapper')
        if (tableBody && this.tableMouseEnterHandler && this.tableMouseLeaveHandler) {
          tableBody.removeEventListener('mouseenter', this.tableMouseEnterHandler);
          tableBody.removeEventListener('mouseleave', this.tableMouseLeaveHandler);
        }
      }
    }
    
    if (this.businessUnitTimer) {
      clearInterval(this.businessUnitTimer)
    }
    
    if (this.energyDeptTimer) {
      clearInterval(this.energyDeptTimer)
    }
    
    if (this.completionChartTimer) {
      clearInterval(this.completionChartTimer)
    }
    
    if (this.indicatorCardsScrollTimer) {
      clearInterval(this.indicatorCardsScrollTimer)
    }
    
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.resizeCharts)
    
    // 移除指标卡片容器的事件监听器
    const cardsContainer = this.$refs.indicatorCardsContainer
    if (cardsContainer) {
      cardsContainer.removeEventListener('mouseover', () => {
        this.indicatorCardsScrollPaused = true
      })
      
      cardsContainer.removeEventListener('mouseout', () => {
        this.indicatorCardsScrollPaused = false
      })
    }
    
    // 销毁图表实例
    this.disposeCharts()
  },
  created() {
      this.getDateUpdateList()
  },
  methods: {
    // 初始化所有图表

    loadExcelFromRemote() {
      this.excelLoading = true;
      const url =
        "https://ydxt.citicsteel.com:8099/minio/xctg/temp/jm关键技径指标.xlsx";
      axios({
        method: "get",
        url,
        responseType: "arraybuffer",
      })
        .then((response) => {
          const data = new Uint8Array(response.data);
          const workbook = XLSX.read(data, { type: "array" });
          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
          const jsonData = XLSX.utils.sheet_to_json(firstSheet, {
            header: 1,
            range: 1,
          });
          console.log(jsonData);
          this.processExcelData(jsonData);
          this.excelLoading = false;
        })
        .catch((error) => {
          console.error("加载Excel文件失败:", error);
          this.$message.error("加载Excel文件失败，请稍后重试");
          this.excelLoading = false;
        });
    },
    getDetail(){
      dimensionalitylistPermissionList().then(res => {
        this.adminShow = res.msg;
      });
    },
    getDateUpdateList(){
      dateUpdateList().then(res => {
        this.keyEnergyIndicators = res.data;
      });
    },

    
    processExcelData(jsonData) {
      if (!jsonData || jsonData.length < 2) return;
      const headers = jsonData[0];
      const colIndexes = {
        factory: headers.indexOf("分厂"),
        name: headers.indexOf("指标名称"),
        target: headers.indexOf("目标"),
        unit: headers.indexOf("单位"),
        jan: headers.indexOf("01月实绩"),
        janStatus: headers.indexOf("01月实绩") + 1,
        feb: headers.indexOf("02月实绩"),
        febStatus: headers.indexOf("02月实绩") + 1,
        mar: headers.indexOf("03月实绩"),
        marStatus: headers.indexOf("03月实绩") + 1,
        apr: headers.indexOf("04月实绩"),
        aprStatus: headers.indexOf("04月实绩") + 1,
        may: headers.indexOf("05月实绩"),
        mayStatus: headers.indexOf("05月实绩") + 1,
      };

      const dataRows = jsonData.slice(1);
      const techIndicators = dataRows
        .map((row) => {
          if (!row || row.length === 0) return null;
          if (
            row[colIndexes.factory] === undefined ||
            row[colIndexes.name] === undefined
          )
            return null;

          const indicator = {
            factory: row[colIndexes.factory] || "",
            name: row[colIndexes.name] || "",
            target:
              row[colIndexes.target] !== undefined
                ? String(row[colIndexes.target])
                : "",
            unit: row[colIndexes.unit] || "",
            jan:
              row[colIndexes.jan] !== undefined
                ? String(row[colIndexes.jan])
                : "",
            janStatus: row[colIndexes.janStatus] === 1.0 ? 1 : 0,
            feb:
              row[colIndexes.feb] !== undefined
                ? String(row[colIndexes.feb])
                : "",
            febStatus: row[colIndexes.febStatus] === 1.0 ? 1 : 0,
            mar:
              row[colIndexes.mar] !== undefined
                ? String(row[colIndexes.mar])
                : "",
            marStatus: row[colIndexes.marStatus] === 1.0 ? 1 : 0,
            apr:
              row[colIndexes.apr] !== undefined
                ? String(row[colIndexes.apr])
                : "",
            aprStatus: row[colIndexes.aprStatus] === 1.0 ? 1 : 0,
            may:
              row[colIndexes.may] !== undefined
                ? String(row[colIndexes.may])
                : "",
            mayStatus: row[colIndexes.mayStatus] === 1.0 ? 1 : 0,
          };

          indicator.monthlyData = [
            parseFloat(indicator.jan) || 0,
            parseFloat(indicator.feb) || 0,
            parseFloat(indicator.mar) || 0,
            parseFloat(indicator.apr) || 0,
            parseFloat(indicator.may) || 0,
          ];

          return indicator;
        })
        .filter(Boolean);

      // 处理相同分厂和指标名称的数据，确保数据一致性
      const uniqueKeys = new Map();
      const uniqueIndicators = [];

      // 先处理相同指标的合并
      techIndicators.forEach((indicator) => {
        const key = `${indicator.factory}_${indicator.name}_${indicator.unit}`;
        if (!uniqueKeys.has(key)) {
          uniqueKeys.set(key, uniqueIndicators.length);
          uniqueIndicators.push(indicator);
        }
      });

      this.techIndicators = uniqueIndicators;
    },
    
    initCharts() {
      this.$nextTick(() => {
        // 初始化部门完成率柱状图
        this.initDepartmentCompletionChart()
        
        // 设置默认部门和指标
        if (this.currentBusinessUnitDepartments.length > 0 && !this.currentDepartment) {
          this.currentDepartment = this.currentBusinessUnitDepartments[0].value
        }
        
        if (this.currentDepartmentIndicators.length > 0 && !this.currentIndicator) {
          this.currentIndicator = this.currentDepartmentIndicators[0].name
        }
        
        // 初始化月度趋势折线图
        this.initMonthlyTrendChart()
        
        // 设置能源部门默认值并初始化能源子图表
        if (this.allFactories && this.allFactories.length > 0) {
          if (!this.currentEnergyDept) {
            this.currentEnergyDept = this.allFactories[0]
          }
          // 初始化能源子图表
          this.initEnergyDetailCharts()
        }
        
        // 启动事业部内部自动切换
        this.startAutoSwitchWithinBusinessUnit()
        
        // 启动事业部切换（每30秒切换一次）
        this.startBusinessUnitSwitch()
        
        // 启动能源部门自动切换
        this.startEnergyDeptSwitch()
      })
    },
    
    // 启动事业部内部自动切换
    startAutoSwitchWithinBusinessUnit() {
      // 停止之前的自动切换
      this.stopAutoSwitch()
      
      // 启动新的自动切换
      this.trendChartTimer = setInterval(() => {
        this.switchDepartmentWithinBusinessUnit()
      }, 3000) // 分厂切换频率为3秒
    },
    
    // 在当前事业部内切换部门
    switchDepartmentWithinBusinessUnit() {
      const departments = this.currentBusinessUnitDepartments
      if (departments.length <= 1) return
      
      const currentIndex = departments.findIndex(dept => dept.value === this.currentDepartment)
      const nextIndex = (currentIndex + 1) % departments.length
      this.currentDepartment = departments[nextIndex].value
    },
    
    // 处理自动切换开关变化
    handleAutoSwitchChange(value) {
      if (value) {
        // 启动自动切换
        this.startAutoSwitchWithinBusinessUnit()
      } else {
        // 停止自动切换
        this.stopAutoSwitch()
      }
    },
    
    // 启动自动切换
    startAutoSwitch() {
      if (this.trendChartTimer) {
        clearInterval(this.trendChartTimer)
      }
      
      this.trendChartTimer = setInterval(() => {
        this.switchDepartment()
      }, 3000)
    },
    
    // 停止自动切换
    stopAutoSwitch() {
      if (this.trendChartTimer) {
        clearInterval(this.trendChartTimer)
        this.trendChartTimer = null
      }
    },
    
    // 切换完成率图表页码
    changePage(page) {
      this.currentPage = page
      this.initDepartmentCompletionChart()
    },
    
    // 计算未完成指标数据，筛选负数完成率
    calculateIncompleteData() {
      const incomplete = [];
      
      this.completionData.forEach(dept => {
        dept.indicators.forEach(indicator => {
          // 直接使用04月列的完成率数据
          // 检查是否有最后一列（04月）的完成率数据
          const completionRateStr = indicator.completionRates && indicator.completionRates[3]; // 04月完成率
          let completionRate = 0;
          
          // 如果有直接的完成率数据，则使用它
          if (completionRateStr) {
            // 将百分比字符串转换为数值，例如 "-1.31%" -> -1.31
            completionRate = parseFloat(completionRateStr.replace('%', ''));
          } else {
            // 如果没有直接数据，则用原来的方法计算
            const actual = indicator.values[3]; // 4月份实际值
            const target = indicator.target;
            
            if (indicator.isHigherBetter) {
              // 如果指标越高越好，完成率 = 实际值/目标值 * 100% - 100%
              completionRate = ((actual / target) * 100) - 100;
            } else {
              // 如果指标越低越好，完成率 = 目标值/实际值 * 100% - 100%
              completionRate = ((target / actual) * 100) - 100;
            }
          }
          
          // 只添加完成率为负数的记录
          if (completionRate < 0) {
            incomplete.push({
              department: dept.department.includes('-') ? dept.department.split('-')[1] : dept.department,
              indicator: indicator.name,
              target: indicator.target,
              actual: indicator.values[3], // 4月份实际值
              unit: indicator.unit,
              completionRate: parseFloat(completionRate.toFixed(1))
            });
          }
        });
      });
      
      // 按完成率从低到高排序
      this.incompleteData = incomplete.sort((a, b) => a.completionRate - b.completionRate);
    },
    
    // 启动表格滚动
    startTableScroll() {
      if (this.incompleteData.length > 0) {
        const table = this.$refs.incompleteTable
        
        // 复制表格内容以实现无缝滚动
        if (table) {
          // 清除之前的滚动定时器
          if (this.scrollTimer) {
            clearInterval(this.scrollTimer)
          }
          
          // 获取表格内容部分（不包含表头）
          const tableBody = table.querySelector('.el-table__body-wrapper')
          if (tableBody) {
            // 创建新的滚动定时器
            let scrollTop = 0
            this.scrollTimer = setInterval(() => {
              // 如果暂停滚动，则不执行滚动操作
              if (this.tableScrollPaused) return;
              
              scrollTop++
              if (scrollTop >= tableBody.scrollHeight / 2) {
                scrollTop = 0
              }
              tableBody.scrollTop = scrollTop
            }, this.scrollSpeed)
            
            // 创建鼠标事件处理函数
            this.tableMouseEnterHandler = () => {
              this.tableScrollPaused = true;
            };
            
            this.tableMouseLeaveHandler = () => {
              this.tableScrollPaused = false;
            };
            
            // 添加鼠标悬停事件处理
            tableBody.addEventListener('mouseenter', this.tableMouseEnterHandler);
            tableBody.addEventListener('mouseleave', this.tableMouseLeaveHandler);
          }
        }
      }
    },
    
    // 切换部门趋势图
    switchDepartment() {
      const currentIndex = this.departments.indexOf(this.currentDepartment)
      const nextIndex = (currentIndex + 1) % this.departments.length
      this.currentDepartment = this.departments[nextIndex]
    },
    
    // 初始化部门完成率柱状图 - 修改为横向柱状图
    initDepartmentCompletionChart() {
      const chartDom = document.getElementById('departmentCompletionChart')
      const chart = echarts.init(chartDom)
      
      // 计算默认显示的百分比
      const defaultDisplayPercent = Math.min(6 / this.allIndicatorCompletionRates.length * 100, 100)
      
      // 使用指标级别的完成率数据
      const labels = this.paginatedIndicatorRates.map(item => {
        return {
          deptName: item.department,
          indicatorName: item.indicator,
          fullText: `${item.department}\n${item.indicator}`
        }
      })
      const rates = this.paginatedIndicatorRates.map(item => item.completionRate)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0]
            const item = this.paginatedIndicatorRates[data.dataIndex]
            const actualText = item.isHigherBetter 
              ? `实际值: ${item.actual}${item.unit}`
              : `实际值: ${item.actual}${item.unit} `
            return `${item.department}-${item.indicator}<br/>
                    目标值: ${item.target}${item.unit}<br/>
                    ${actualText}<br/>
                    完成率: ${item.completionRate}%`
          }.bind(this)
        },
        grid: {
          left: '0', // 左移30px
          right: '0',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            yAxisIndex: [0],
            start: 0,
            end: defaultDisplayPercent,
            width: 10,
            handleSize: 20,
            showDetail: false,
            zoomLock: false,
            moveOnMouseWheel: true,
            preventDefaultMouseMove: true
          },
          {
            type: 'inside',
            yAxisIndex: [0],
            start: 0,
            end: defaultDisplayPercent,
            zoomLock: false
          }
        ],
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            restore: {},
            saveAsImage: {}
          },
          right: 10,
          top: 0
        },
        xAxis: {
          type: 'value',
          name: '完成率(%)',
          min: -30, // 调整最小值为-30%，足够显示负数完成率
          max: 30, // 调整最大值为30%，使图表更聚焦
          axisLine: {
            lineStyle: {
              color: '#5470c6'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#E0E6F1'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: labels.map(item => item.fullText),
          axisLabel: {
            formatter: function(value) {
              return value;
            },
            color: '#333',
            lineHeight: 16,
            margin: 12,
            rich: {
              dept: {
                fontWeight: 'bold',
                lineHeight: 20
              },
              indicator: {
                lineHeight: 20
              }
            }
          },
          axisLine: {
            lineStyle: {
              color: '#5470c6'
            }
          }
        },
        series: [
          {
            name: '完成率',
            type: 'bar',
            data: rates,
            itemStyle: {
              color: function(params) {
                // 根据完成率设置不同颜色
                if (params.data >= 100) {
                  return '#5470c6' // 蓝色，超过100%
                } else if (params.data >= 90) {
                  return '#91cc75'  // 绿色，完成率高
                } else if (params.data >= 70) {
                  return '#fac858'  // 黄色，完成率中等
                } else if (params.data >= 0) {
                  return '#ee6666'  // 红色，完成率低
                } else {
                  return '#ff5252'  // 更深的红色，负数完成率
                }
              }
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{c}%',
              color: function(params) {
                // 负数完成率用白色文字，更加明显
                return params.data < 0 ? '#ffffff' : '#333333';
              },
              fontWeight: 'bold',
              distance: 15
            },
            barWidth: '50%', // 柱子更细
            barCategoryGap: '40%', // 增加柱子间距
            animationDelay: function(idx) {
              return idx * 100 + 100
            }
          }
        ],
        animationEasing: 'elasticOut',
        animationDelayUpdate: function(idx) {
          return idx * 5
        }
      }
      
      chart.setOption(option)
      
      // 保存dataZoom状态，防止滚动条回弹
      chart.on('datazoom', function(params) {
        const { start, end } = params
        option.dataZoom[0].start = start
        option.dataZoom[0].end = end
        option.dataZoom[1].start = start
        option.dataZoom[1].end = end
        chart.setOption(option)
      })
      
      // 创建事件处理器
      this.chartMouseOverHandler = () => {
        this.completionChartScrollPaused = true
      }
      
      this.chartMouseOutHandler = () => {
        this.completionChartScrollPaused = false
      }
      
      // 添加鼠标悬停事件，暂停自动滚动
      chartDom.addEventListener('mouseover', this.chartMouseOverHandler)
      
      // 添加鼠标离开事件，恢复自动滚动
      chartDom.addEventListener('mouseout', this.chartMouseOutHandler)
      
      // 窗口大小变化时自动调整图表大小
      window.addEventListener('resize', function() {
        chart.resize()
      })
      
      // 保存图表实例以便后续使用
      this.completionChart = chart
    },
    
    // 启动完成率图表自动滚动
    startCompletionChartScroll() {
      if (this.completionChartTimer) {
        clearInterval(this.completionChartTimer)
      }
      
      this.completionChartTimer = setInterval(() => {
        if (this.completionChartScrollPaused) return
        
        if (!this.completionChart) return
        
        const option = this.completionChart.getOption()
        let start = option.dataZoom[0].start
        let end = option.dataZoom[0].end
        const step = 0.1 // 每次滚动的百分比，改为0.1，使滚动非常缓慢
        const range = end - start // 当前显示的范围
        
        // 根据滚动方向调整滚动位置
        if (this.completionChartScrollDirection === 'down') {
          // 向下滚动
          if (end < 100) {
            start += step
            end += step
          } else {
            // 已到底部，改变方向
            this.completionChartScrollDirection = 'up'
          }
        } else {
          // 向上滚动
          if (start > 0) {
            start -= step
            end -= step
          } else {
            // 已到顶部，改变方向
            this.completionChartScrollDirection = 'down'
          }
        }
        
        // 更新滚动位置
        option.dataZoom[0].start = start
        option.dataZoom[0].end = end
        option.dataZoom[1].start = start
        option.dataZoom[1].end = end
        this.completionChart.setOption(option)
      }, 300) // 滚动间隔改为300毫秒，进一步减慢滚动速度
    },
    
    // 初始化月度趋势折线图 - 显示单条折线
    initMonthlyTrendChart() {
      const chartDom = document.getElementById('monthlyTrendChart')
      const chart = echarts.init(chartDom)
      
      // 查找当前部门的数据
      const deptData = this.completionData.find(dept => dept.department === this.currentDepartment || dept.department.startsWith(this.currentDepartment))
      
      if (!deptData || !this.currentIndicator) {
        return
      }
      
      // 查找当前指标
      const indicator = deptData.indicators.find(ind => ind.name === this.currentIndicator)
      
      if (!indicator) {
        return
      }
      
      // 准备月份数据
      const months = ['1月', '2月', '3月', '4月']
      
      // 计算数据范围，以便设置y轴范围使折线居中
      const values = indicator.values
      const min = Math.min(...values)
      const max = Math.max(...values)
      const range = max - min
      
      // 设置y轴范围，扩大20%的范围使波动看起来更明显
      const yMin = min - range * 0.4
      const yMax = max + range * 0.4
      
      const option = {
        title: {
          text: `${this.currentDepartment} - ${this.currentIndicator}`,
          left: 'center',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            return `${params[0].name}<br/>${indicator.name}: ${params[0].value} ${indicator.unit}<br/>目标值: ${indicator.target} ${indicator.unit}`
          },
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '10%',
          top: '60px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: months,
          axisLine: {
            lineStyle: {
              width: 2
            }
          }
        },
        yAxis: {
          type: 'value',
          name: indicator.unit,
          min: yMin,
          max: yMax,
          axisLabel: {
            formatter: function(value) {
              return value.toFixed(2);
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLine: {
            lineStyle: {
              width: 2
            }
          }
        },
        series: [
          {
            name: indicator.name,
            type: 'line',
            data: indicator.values,
            smooth: true, // 平滑曲线
            symbol: 'emptyCircle',
            symbolSize: 10,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(64, 158, 255, 0.5)'
              },
              scale: true
            },
            itemStyle: {
              color: '#409EFF',
              borderWidth: 2
            },
            lineStyle: {
              width: 4,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
              shadowBlur: 10,
              shadowOffsetY: 5
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.7)'
                },
                {
                  offset: 0.5,
                  color: 'rgba(64, 158, 255, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ])
            },
            markLine: {
              silent: true,
              lineStyle: {
                color: '#F56C6C',
                type: 'dashed',
                width: 2
              },
              data: [
                {
                  yAxis: indicator.target,
                  label: {
                    formatter: `目标值: ${indicator.target}`,
                    position: 'insideEndTop',
                    fontSize: 12,
                    backgroundColor: 'rgba(245, 108, 108, 0.2)',
                    padding: [2, 4],
                    borderRadius: 2
                  }
                }
              ]
            },
            animationDuration: 2000,
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
              return idx * 200;
            }
          },
          {
            name: '目标值',
            type: 'line',
            data: Array(months.length).fill(indicator.target),
            lineStyle: {
              color: '#F56C6C',
              type: 'dashed',
              width: 2
            },
            symbol: 'none'
          }
        ],
        legend: {
          data: [indicator.name, '目标值'],
          bottom: '0%'
        }
      }
      
      chart.setOption(option)
    },
    
    // 初始化能源消耗子图表
    initEnergyDetailCharts() {
      // 检查当前选择的部门数据是否有效
      if (!this.currentEnergyDept || !this.factoryEnergyData) return;
      
      // 重置警告状态
      for (let key in this.isEnergyChartWarning) {
        this.isEnergyChartWarning[key] = false;
      }
      
      // 为每种能源类型创建单独的图表
      this.energyTypes.forEach(type => {
        const chartDom = document.getElementById('energySubchart_' + type.value);
        if (!chartDom) return;
        
        // 清除之前的实例
        const existingChart = echarts.getInstanceByDom(chartDom);
        if (existingChart) {
          existingChart.dispose();
        }
        
        const chart = echarts.init(chartDom);
        
        const data = this.factoryEnergyData[type.value][this.currentEnergyDept] || [];
        const months = this.factoryEnergyData.months;
        const targetRange = this.energyTargetRanges[type.value];
        
        // 计算数据的最小值和最大值，以便设置y轴的范围
        const minValue = Math.min(...data);
        const maxValue = Math.max(...data);
        const valueRange = maxValue - minValue;
        
        // 设置y轴的最小值和最大值，以使折线居中并增加波动感
        // 通过缩小y轴范围使折线显得更加曲折
        const yMin = minValue - valueRange * 0.3; // 下方预留30%的空间
        const yMax = maxValue + valueRange * 0.3; // 上方预留30%的空间
        
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: function(params) {
              const value = params[0].value;
              return `${params[0].name}<br/>${type.label}: ${value} ${targetRange.unit}`;
            }
          },
          grid: {
            left: '10%',
            right: '5%',
            bottom: '15%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: months,
            axisLine: {
              lineStyle: {
                color: '#333333' // 黑色坐标轴
              }
            },
            axisLabel: {
              color: '#333333' // 黑色坐标轴文字
            },
            boundaryGap: false // 让曲线从坐标轴开始
          },
          yAxis: {
            type: 'value',
            name: targetRange.unit,
            min: yMin, // 设置最小值使折线居中
            max: yMax, // 设置最大值使折线居中
            axisLine: {
              lineStyle: {
                color: '#333333' // 黑色坐标轴
              }
            },
            axisLabel: {
              color: '#333333', // 黑色坐标轴文字
              formatter: function(value) {
                // 保留适当的小数位数以避免过度拥挤
                if (value >= 100) {
                  return value.toFixed(0);
                } else {
                  return value.toFixed(1);
                }
              }
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#E0E6F1'
              }
            }
          },
          series: [
            {
              name: type.label,
              type: 'line',
              data: data,
              smooth: true, // 使用平滑曲线
              smoothMonotone: 'none', // 不保持单调性，允许更多波动
              symbol: 'circle',
              symbolSize: 8,
              sampling: 'average', // 使用平均采样
              lineStyle: {
                width: 4, // 加粗线条
                color: type.color // 使用更深的颜色
              },
              itemStyle: {
                color: type.color,
                borderWidth: 2,
                borderColor: '#fff',
                shadowColor: 'rgba(0, 0, 0, 0.3)',
                shadowBlur: 5
              },
              emphasis: {
                itemStyle: {
                  borderWidth: 3,
                  shadowBlur: 10
                },
                lineStyle: {
                  width: 6 // 鼠标悬停时线条更粗
                }
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: this.hexToRgba(type.color, 0.6) // 更高的透明度使颜色更深
                  },
                  {
                    offset: 1,
                    color: this.hexToRgba(type.color, 0.1)
                  }
                ])
              }
            }
          ]
        };
        
        chart.setOption(option);
      });
    },
    
    // 颜色转换辅助函数
    hexToRgba(hex, alpha) {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    },
    
    // 启动能源部门自动切换
    startEnergyDeptSwitch() {
      // 停止之前的能源部门切换
      if (this.energyDeptTimer) {
        clearInterval(this.energyDeptTimer)
      }
      
      // 启动新的能源部门切换
      this.energyDeptTimer = setInterval(() => {
        this.switchEnergyDept()
      }, 10000) // 每10秒切换一次部门，原来是5000
    },
    
    // 切换能源部门
    switchEnergyDept() {
      const currentIndex = this.allFactories.indexOf(this.currentEnergyDept)
      const nextIndex = (currentIndex + 1) % this.allFactories.length
      this.currentEnergyDept = this.allFactories[nextIndex]
      this.initEnergyDetailCharts()
    },
    
    // 窗口大小变化时重新渲染图表
    resizeCharts() {
      const chartIds = [
        'departmentCompletionChart',
        'monthlyTrendChart'
      ]
      
      chartIds.forEach(id => {
        const chartDom = document.getElementById(id)
        if (chartDom) {
          const chart = echarts.getInstanceByDom(chartDom)
          if (chart) {
            chart.resize()
          }
        }
      })
      
      // 调整能源子图表大小
      this.energyTypes.forEach(type => {
        const chartDom = document.getElementById('energySubchart_' + type.value)
        if (chartDom) {
          const chart = echarts.getInstanceByDom(chartDom)
          if (chart) {
            chart.resize()
          }
        }
      })
    },
    
    // 销毁图表实例
    disposeCharts() {
      const chartIds = [
        'departmentCompletionChart',
        'monthlyTrendChart'
      ]
      
      chartIds.forEach(id => {
        const chartDom = document.getElementById(id)
        if (chartDom) {
          // 移除事件监听器
          if (id === 'departmentCompletionChart' && this.chartMouseOverHandler && this.chartMouseOutHandler) {
            chartDom.removeEventListener('mouseover', this.chartMouseOverHandler)
            chartDom.removeEventListener('mouseout', this.chartMouseOutHandler)
          }
          
          const chart = echarts.getInstanceByDom(chartDom)
          if (chart) {
            chart.dispose()
          }
        }
      })
      
      // 清除完成率图表引用
      this.completionChart = null
      
      // 销毁能源子图表
      this.energyTypes.forEach(type => {
        const chartDom = document.getElementById('energySubchart_' + type.value)
        if (chartDom) {
          const chart = echarts.getInstanceByDom(chartDom)
          if (chart) {
            chart.dispose()
          }
        }
      })
    },
    

    // 启动事业部切换
    startBusinessUnitSwitch() {
      // 停止之前的事业部切换
      if (this.businessUnitTimer) {
        clearInterval(this.businessUnitTimer)
      }
      
      // 启动新的事业部切换
      this.businessUnitTimer = setInterval(() => {
        this.switchBusinessUnit()
      }, 30000) // 事业部切换频率为30秒
    },
    
    // 切换事业部
    switchBusinessUnit() {
      const currentIndex = this.departments.indexOf(this.currentBusinessUnit)
      const nextIndex = (currentIndex + 1) % this.departments.length
      this.currentBusinessUnit = this.departments[nextIndex]
    },
    
    // 初始化关键能源指标对比图
    initKeyEnergyIndicatorsChart() {
      const chartDom = document.getElementById('keyEnergyIndicatorsChart')
      const chart = echarts.init(chartDom)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const dataIndex = params[0].dataIndex
            const indicator = this.keyEnergyIndicators[dataIndex]
            const changeText = indicator.change > 0 ? `+${indicator.change}%` : `${indicator.change}%`
            const changeColor = indicator.status === 'good' ? '#67C23A' : indicator.status === 'warning' ? '#E6A23C' : '#F56C6C'
            
            return `
              <div style="font-weight:bold">${indicator.name}</div>
              <div>今日: ${indicator.today} ${indicator.unit}</div>
              <div>昨日: ${indicator.yesterday} ${indicator.unit}</div>
              <div>变化: <span style="color:${changeColor}">${changeText}</span></div>
            `
          }.bind(this)
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.keyEnergyIndicators.map(item => item.name)
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              if (value >= 100) {
                return value.toFixed(0)
              } else {
                return value.toFixed(1)
              }
            }
          }
        },
        series: [
          {
            name: '今日值',
            type: 'bar',
            data: this.keyEnergyIndicators.map((item, index) => {
              return {
                value: item.today,
                itemStyle: {
                  color: item.status === 'good' ? '#67C23A' : item.status === 'warning' ? '#E6A23C' : '#F56C6C'
                }
              }
            }),
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                const index = params.dataIndex
                return this.keyEnergyIndicators[index].today
              }.bind(this)
            },
            barWidth: '30%'
          },
          {
            name: '昨日值',
            type: 'bar',
            data: this.keyEnergyIndicators.map(item => item.yesterday),
            barWidth: '30%',
            itemStyle: {
              color: '#909399',
              opacity: 0.5
            }
          }
        ],
        legend: {
          data: ['今日值', '昨日值'],
          bottom: 0
        }
      }
      
      chart.setOption(option)
    },
    
    // 根据索引位置计算渐变颜色样式
    getGradientStyle(index, total) {
      // 计算该指标在整体中的相对位置（0-1之间）
      const position = index / (total - 1);
      
      // 定义绿色到红色的渐变颜色数组
      const colorStops = [
        '#52c41a', // 亮绿色
        '#85ce61', // 绿色
        '#b3e19d', // 浅绿色
        '#d4f8be', // 非常浅的绿色
        '#faff72', // 黄色
        '#fadb14', // 金黄色
        '#ffa940', // 橙色
        '#fa8c16', // 深橙色
        '#ff7875', // 浅红色
        '#ff4d4f', // 红色
        '#f5222d', // 亮红色
        '#cf1322'  // 深红色
      ];
      
      // 根据位置在渐变色之间插值获取颜色
      // 找到对应的颜色区间
      const segmentCount = colorStops.length - 1;
      const segment = Math.min(Math.floor(position * segmentCount), segmentCount - 1);
      const localPosition = (position * segmentCount) - segment; // 在当前区间内的相对位置 (0-1)
      
      // 获取区间的起止颜色
      const startColor = colorStops[segment];
      const endColor = colorStops[segment + 1];
      
      // 在两个颜色之间插值
      const bgColor = this.interpolateColors(startColor, endColor, localPosition);
      
      // 计算文字颜色
      // 提取RGB并计算亮度
      let r = parseInt(bgColor.slice(1, 3), 16);
      let g = parseInt(bgColor.slice(3, 5), 16);
      let b = parseInt(bgColor.slice(5, 7), 16);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      
      // 亮度大于140使用深色文字，否则使用浅色文字
      let textColor = brightness > 140 ? '#333333' : '#ffffff';
      
      // 设置边框颜色（比背景色稍深）
      const borderColor = this.adjustColor(bgColor, -20);
      
      return {
        backgroundColor: bgColor,
        color: textColor,
        borderTopColor: borderColor
      };
    },
    
    // 颜色插值函数
    interpolateColors(color1, color2, factor) {
      // 解析颜色
      let r1 = parseInt(color1.slice(1, 3), 16);
      let g1 = parseInt(color1.slice(3, 5), 16);
      let b1 = parseInt(color1.slice(5, 7), 16);
      
      let r2 = parseInt(color2.slice(1, 3), 16);
      let g2 = parseInt(color2.slice(3, 5), 16);
      let b2 = parseInt(color2.slice(5, 7), 16);
      
      // 线性插值
      let r = Math.round(r1 + factor * (r2 - r1));
      let g = Math.round(g1 + factor * (g2 - g1));
      let b = Math.round(b1 + factor * (b2 - b1));
      
      // 转回十六进制
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    },
    
    // 获取徽章样式
    getBadgeStyle(index, total) {
      // 获取当前卡片的背景色
      const position = index / (total - 1);
      const colorStops = [
        '#52c41a', // 亮绿色
        '#85ce61', // 绿色
        '#b3e19d', // 浅绿色
        '#d4f8be', // 非常浅的绿色
        '#faff72', // 黄色
        '#fadb14', // 金黄色
        '#ffa940', // 橙色
        '#fa8c16', // 深橙色
        '#ff7875', // 浅红色
        '#ff4d4f', // 红色
        '#f5222d', // 亮红色
        '#cf1322'  // 深红色
      ];
      
      const segmentCount = colorStops.length - 1;
      const segment = Math.min(Math.floor(position * segmentCount), segmentCount - 1);
      const localPosition = (position * segmentCount) - segment;
      
      const startColor = colorStops[segment];
      const endColor = colorStops[segment + 1];
      
      const bgColor = this.interpolateColors(startColor, endColor, localPosition);
      
      // 计算文字颜色
      let r = parseInt(bgColor.slice(1, 3), 16);
      let g = parseInt(bgColor.slice(3, 5), 16);
      let b = parseInt(bgColor.slice(5, 7), 16);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      
      // 根据背景亮度设置徽章样式
      if (brightness > 140) {
        // 浅色背景使用深色边框的徽章
        const badgeBgColor = this.adjustColor(bgColor, -40);
        return {
          backgroundColor: badgeBgColor,
          color: '#ffffff',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
        };
      } else {
        // 深色背景使用浅色徽章
        return {
          backgroundColor: 'rgba(255, 255, 255, 0.25)',
          color: '#ffffff',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
        };
      }
    },
    
    // 颜色调整辅助函数
    adjustColor(color, amount) {
      // 如果是白色特殊处理
      if (color === '#ffffff') {
        return '#e0e0e0';
      }
      
      // 将颜色转换为RGB
      let r = parseInt(color.slice(1, 3), 16);
      let g = parseInt(color.slice(3, 5), 16);
      let b = parseInt(color.slice(5, 7), 16);
      
      // 调整亮度
      r = Math.max(0, Math.min(255, r + amount));
      g = Math.max(0, Math.min(255, g + amount));
      b = Math.max(0, Math.min(255, b + amount));
      
      // 转回十六进制
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    },
    
    // 初始化指标卡片滚动
    initIndicatorCardsScroll() {
      // 清除之前的滚动定时器
      if (this.indicatorCardsScrollTimer) {
        clearInterval(this.indicatorCardsScrollTimer);
      }
      
      const container = this.$refs.indicatorCardsContainer;
      if (!container) return;
      
      // 计算滚动所需参数
      let scrollTop = 0;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      const maxScroll = scrollHeight - clientHeight;
      
      // 如果内容不足以滚动，直接返回
      if (maxScroll <= 0) return;
      
      // 滚动步长和速度
      const step = 0.5; // 滚动步长更小，使滚动更平滑
      const scrollInterval = 20; // 滚动更新频率更高，更流畅
      
      this.indicatorCardsScrollTimer = setInterval(() => {
        // 鼠标悬停时暂停滚动
        if (this.indicatorCardsScrollPaused) return;
        
        scrollTop += step;
        
        // 当滚动到底部时，快速回到顶部并继续滚动
        if (scrollTop >= maxScroll) {
          // 重置滚动位置到顶部
          scrollTop = 0;
          container.scrollTop = 0;
          
          // 短暂暂停一下，让用户能看到回到顶部的过程
          this.indicatorCardsScrollPaused = true;
          setTimeout(() => {
            this.indicatorCardsScrollPaused = false;
          }, 1000);
        } else {
          container.scrollTop = scrollTop;
        }
      }, scrollInterval);
    },

    // 表头样式
    headerCellStyle() {
      return {
        backgroundColor: "#f5f7fa",
        color: "#606266",
        fontWeight: "bold",
      };
    },

        // 显示指标详情
        showDetails(row) {
      this.currentIndicator = row;
      this.detailDialogVisible = true;
      this.$nextTick(() => {
        this.initDetailChart();
      });
    },

    // 初始化详情图表
    initDetailChart() {
      if (!this.currentIndicator) return;

      const chartDom = document.getElementById("indicatorChart");
      const myChart = echarts.init(chartDom);

      const months = ["1月", "2月", "3月", "4月", "5月"];
      
      // 生成波动较大的数据，确保围绕目标值有明显起伏
      const targetValue = parseFloat(this.currentIndicator.target) || 100;
      
      // 使用实际数据，如果没有则生成波动数据
      let actualData = [];
      if (this.currentIndicator.monthlyData && this.currentIndicator.monthlyData.length === 5) {
        actualData = this.currentIndicator.monthlyData;
      } else {
        // 生成波动数据，确保有起伏
        const fluctuationRange = targetValue * 0.3; // 波动范围为目标值的30%
        actualData = months.map(() => {
          const fluctuation = (Math.random() - 0.5) * 2 * fluctuationRange;
          return Math.max(0, targetValue + fluctuation);
        });
      }
      
      // 目标值线
      const targetData = Array(5).fill(targetValue);

      const option = {
        title: {
          text: `${this.currentIndicator.name}月度趋势`,
          left: "center",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
          formatter: function(params) {
            const actualValue = params[0].value.toFixed(2);
            const targetValue = params[1].value.toFixed(2);
            const diff = (actualValue - targetValue).toFixed(2);
            // 使用统一的白色显示差值
            const diffColor = 'color:#ffffff';
            
            return `${params[0].name}<br/>
                   ${params[0].marker} ${params[0].seriesName}: ${actualValue}<br/>
                   ${params[1].marker} ${params[1].seriesName}: ${targetValue}<br/>
                   <span style="${diffColor}">差值: ${diff}</span>`;
          }
        },
        legend: {
          data: ["实际值", "目标值"],
          bottom: 10,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: months,
        },
        yAxis: {
          type: "value",
          name: this.currentIndicator.unit,
          axisLabel: {
            formatter: "{value}",
          },
          scale: true, // 缩放Y轴以突出显示数据波动
        },
        series: [
          {
            name: "实际值",
            type: "line",
            data: actualData,
            itemStyle: {
              color: "#409EFF",
            },
            lineStyle: {
              width: 3,
            },
            symbol: "circle",
            symbolSize: 8,
            markPoint: {
              data: [
                { type: "max", name: "最大值" },
                { type: "min", name: "最小值" }
              ]
            }
          },
          {
            name: "目标值",
            type: "line",
            data: targetData,
            itemStyle: {
              color: "#F56C6C",
            },
            lineStyle: {
              width: 2,
              type: "dashed",
            },
            markLine: {
              data: [{ type: "average", name: "目标值" }],
              label: {
                formatter: "目标值: {c}"
              }
            }
          },
        ],
      };

      myChart.setOption(option);

      // 响应式处理
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },

    // 关闭对话框
    handleDialogClose() {
      this.detailDialogVisible = false;
      this.currentIndicator = null;
      // 清除图表实例
      const chartDom = document.getElementById("indicatorChart");
      if (chartDom) {
        const chart = echarts.getInstanceByDom(chartDom);
        if (chart) {
          chart.dispose();
        }
      }
    },
        // 处理表格单元格合并
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 仅对分厂和指标名称列进行合并
      if (columnIndex === 0 || columnIndex === 1) {
        // 获取当前行数据
        const currentRow = this.techIndicators[rowIndex];
        if (!currentRow) return { rowspan: 1, colspan: 1 };

        // 分厂列合并处理
        if (columnIndex === 0) {
          // 如果是第一行或者与前一行分厂不同，则计算合并行数
          if (
            rowIndex === 0 ||
            currentRow.factory !== this.techIndicators[rowIndex - 1].factory
          ) {
            // 计算连续相同分厂的行数
            let rowspan = 1;
            for (let i = rowIndex + 1; i < this.techIndicators.length; i++) {
              if (this.techIndicators[i].factory === currentRow.factory) {
                rowspan++;
              } else {
                break;
              }
            }
            return { rowspan, colspan: 1 };
          } else {
            // 如果当前行与前一行分厂相同，则隐藏当前单元格
            return { rowspan: 0, colspan: 0 };
          }
        }

        // 指标名称列合并处理
        if (columnIndex === 1) {
          // 如果是第一行，或者与前一行分厂不同，或者分厂相同但指标名称不同
          if (
            rowIndex === 0 ||
            currentRow.factory !== this.techIndicators[rowIndex - 1].factory ||
            currentRow.name !== this.techIndicators[rowIndex - 1].name
          ) {
            // 计算连续相同分厂和指标名称的行数
            let rowspan = 1;
            for (let i = rowIndex + 1; i < this.techIndicators.length; i++) {
              if (
                this.techIndicators[i].factory === currentRow.factory &&
                this.techIndicators[i].name === currentRow.name
              ) {
                rowspan++;
              } else {
                break;
              }
            }
            return { rowspan, colspan: 1 };
          } else {
            // 如果当前行与前一行分厂和指标名称都相同，则隐藏当前单元格
            return { rowspan: 0, colspan: 0 };
          }
        }
      }
      return { rowspan: 1, colspan: 1 };
    },
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.page-title {
  text-align: center;
  margin-bottom: 20px;
}

.page-title h2 {
  font-size: 24px;
  color: #303133;
  margin: 0;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  transition: all 0.3s;
}

.box-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.section-header {
  font-weight: bold;
  font-size: 18px;
  color: #333;
  padding: 5px 0;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.tech-economic-section .section-header {
  color: #4a6ee0;
  border-bottom-color: #4a6ee0;
}

.energy-section .section-header {
  color: #47b475;
  border-bottom-color: #47b475;
}

.chart-container {
  display: flex;
  flex-direction: column;
  margin: 0 -10px;
}

.top-charts-row,
.energy-charts-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  width: 100%;
  height: 600px!important;
}

.chart-item {
  flex: 1;
  min-width: 32%;
  margin: 10px;
  background-color: #fff;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trend-controls {
  display: flex;
  align-items: center;
}

.chart {
  height: 340px;
  width: 100%;
}

.scroll-table-container {
  height: 340px;
  overflow: hidden;
  position: relative;
}

.scroll-table {
  height: 100%;
  overflow-y: hidden; /* 修改为hidden，使用JS控制滚动 */
  scrollbar-width: thin;
}

.completion-rate {
  font-weight: bold;
}

.completion-rate.red {
  color: #F56C6C;
}

.completion-rate.deep-red {
  color: #ff0000;
  font-weight: bolder;
}

.completion-rate.green {
  color: #67C23A;
}

.energy-stats {
  padding: 10px;
}

.stat-card {
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
  margin-bottom: 15px;
  border-left: 4px solid #409EFF;
  transition: all 0.3s;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-card.good {
  border-left-color: #67C23A;
}

.stat-card.warning {
  border-left-color: #E6A23C;
}

.stat-card.danger {
  border-left-color: #F56C6C;
}

.stat-title {
  font-size: 14px;
  color: #606266;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin: 5px 0;
}

.stat-change {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  align-items: center;
}

/* 动画效果 */
.box-card {
  animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tech-economic-section {
  animation-delay: 0.1s;
  background-color: #e6f0ff; /* 修改技经指标部分的背景色，更深的蓝色背景 */
}

.energy-section {
  animation-delay: 0.3s;
  background-color: #e6fff0; /* 修改能源指标部分的背景色，更深的绿色背景 */
}

/* 修改卡片背景色 */
.chart-item {
  background-color: #ffffff; /* 白色背景 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 增强阴影 */
  border: 1px solid rgba(0, 0, 0, 0.05); /* 添加细边框 */
}

/* 技经指标部分的卡片特殊样式 */
.tech-economic-section .chart-item {
  background-color: #f8faff; /* 浅蓝色调背景 */
  border-top: 3px solid #4a6ee0; /* 蓝色上边框 */
}

/* 能源指标部分的卡片特殊样式 */
.energy-section .chart-item {
  background-color: #f8fff9; /* 浅绿色调背景 */
  border-top: 3px solid #47b475; /* 绿色上边框 */
}

/* 媒体查询，适应不同屏幕尺寸 */
@media screen and (max-width: 1600px) {
  .top-charts-row,
  .energy-charts-row {
    flex-wrap: wrap;
  }
  
  .chart-item {
    min-width: 45%;
    flex: 0 0 45%;
  }
}

@media screen and (max-width: 1200px) {
  .chart-item {
    min-width: 100%;
    flex: 0 0 100%;
  }
}

/* 未完成指标表格样式 */
.el-table {
  border: none;
}

.el-table::before, .el-table::after {
  content: none;
}

.el-table td.el-table__cell,
.el-table th.el-table__cell {
  border-bottom: 1px solid #ebeef5;
}

.el-table th.el-table__cell {
  background-color: #f8f8f8;
  color: #606266;
  font-weight: bold;
  font-size: 15px;
}

.el-table td.el-table__cell {
  color: #606266;
  font-size: 14px;
  padding: 8px 0;
}

.el-table__row:hover > td {
  background-color: #f5f7fa;
}

.energy-type-selector {
  display: flex;
  align-items: center;
}

.energy-detail-container {
  height: 340px;
  overflow: auto;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
}

.status-indicator.normal {
  background-color: #f0f9eb;
  color: #67C23A;
}

.status-indicator.warning {
  background-color: #fdf6ec;
  color: #E6A23C;
}

.status-indicator.danger {
  background-color: #fef0f0;
  color: #F56C6C;
}

.status-indicator i {
  margin-left: 5px;
  font-size: 16px;
}

.trend-value {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.trend-value.up {
  color: #F56C6C;
}

.trend-value.down {
  color: #67C23A;
}

.trend-value i {
  margin-right: 3px;
}

.key-indicators-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 20px;
  height: 500px;
  overflow-y: auto;
}

.indicator-card {
  flex: 0 0 23%;
  margin-bottom: 12px;
  padding: 10px;
  background-color: #f0f7ff;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  text-align: center;
  position: relative;
  border-left: 4px solid #409EFF;
  transition: all 0.3s;
  height: 125px;
}

.indicator-card.danger {
  background-color: #dc143c; /* 猩红色 */
  color: white;
}

.indicator-card.danger .indicator-title,
.indicator-card.danger .indicator-value,
.indicator-card.danger .indicator-target,
.indicator-card.danger .indicator-unit,
.indicator-card.danger .indicator-compare {
  color: white;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
}

.indicator-card.good {
  border-left-color: #67C23A;
  background-color: #f0fff5;
}

.indicator-card.warning {
  border-left-color: #E6A23C;
  background-color: #fffbf0;
}

.indicator-card.danger {
  border-left-color: #F56C6C;
}

.indicator-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 3px;
  font-weight: bold;
}

.indicator-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin: 3px 0;
}

.indicator-target {
  font-size: 12px;
  color: #909399;
  margin-bottom: 3px;
}

.indicator-unit {
  font-size: 12px;
  color: #909399;
}

.indicator-compare {
  font-size: 12px;
  color: #606266;
}

.indicator-change.up {
  color: #67C23A;
}

.indicator-change.down {
  color: #F56C6C;
}

.energy-chart-half {
  flex: 0 0 48%;
  min-width: 48%;
}

.energy-subchart-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  height: 500px;
}

.energy-subchart {
  flex: 0 0 48%;
  height: 48%;
  margin-bottom: 10px;
  border-radius: 4px;
  padding: 5px;
  background-color: #f0f8ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  border: 1px solid #d1e6ff;
}

.energy-chart-warning {
  background-color: rgba(245, 108, 108, 0.1);
  border: 1px solid rgba(245, 108, 108, 0.3);
}

.subchart-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  text-align: center;
  margin-bottom: 5px;
  background-color: #e6f0ff;
  padding: 3px 0;
  border-radius: 3px;
}

.subchart {
  height: calc(100% - 0px);
  width: 100%;
}

.chart-wrapper {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
}

.chart {
  height: 100%;
  width: 100%;
}

/* 指标卡片样式 */
.indicator-cards-container {
  height: 320px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: #e0e0e0 #f8f8f8;
  padding: 6px 3px;
}

.indicator-cards-container::-webkit-scrollbar {
  width: 6px;
}

.indicator-cards-container::-webkit-scrollbar-track {
  background: #f8f8f8;
}

.indicator-cards-container::-webkit-scrollbar-thumb {
  background-color: #e0e0e0;
  border-radius: 3px;
}

.indicator-cards-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 一行显示3个 */
  gap: 10px; /* 更小的间距 */
  max-width: 100%;
}

.indicator-card-item {
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  padding: 8px;
  transition: all 0.3s ease;
  border-top: 3px solid #909399;
  display: flex;
  flex-direction: column;
  height: 110px; /* 更小的高度 */
  width: 100%;
  max-width: 100%;
  margin: 0 auto; /* 居中显示 */
}

.indicator-card-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
}

.indicator-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.indicator-name {
  font-size: 13px;
  font-weight: bold;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.completion-badge {
  background-color: #f0f0f0;
  padding: 1px 5px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: bold;
  min-width: 40px;
  text-align: center;
  transition: all 0.3s ease;
  margin-left: 4px;
}

.indicator-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.indicator-department {
  font-size: 11px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.indicator-values {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.value-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
  background-color: rgba(255, 255, 255, 0.15);
  padding: 1px 4px;
  border-radius: 2px;
}

.value-label {
  font-size: 10px;
  font-weight: 500;
}

.value-number {
  font-size: 11px;
  font-weight: 600;
}

/* 不同完成率的卡片样式 */
.indicator-card-item.excellent {
  background-color: #67c23a;
  border-top-color: #4d9e29;
}

.indicator-card-item.excellent .indicator-name,
.indicator-card-item.excellent .indicator-department,
.indicator-card-item.excellent .value-number,
.indicator-card-item.excellent .value-label {
  color: #ffffff;
}

.indicator-card-item.excellent .completion-badge {
  background-color: #4d9e29;
  color: #ffffff;
}

.indicator-card-item.very-good {
  background-color: #85ce61;
  border-top-color: #67c23a;
}

.indicator-card-item.very-good .indicator-name,
.indicator-card-item.very-good .indicator-department,
.indicator-card-item.very-good .value-number,
.indicator-card-item.very-good .value-label {
  color: #ffffff;
}

.indicator-card-item.very-good .completion-badge {
  background-color: #67c23a;
  color: #ffffff;
}

.indicator-card-item.good {
  background-color: #b3e19d;
  border-top-color: #85ce61;
}

.indicator-card-item.good .indicator-name,
.indicator-card-item.good .indicator-department,
.indicator-card-item.good .value-number {
  color: #2e2e2e;
}

.indicator-card-item.good .completion-badge {
  background-color: #85ce61;
  color: #ffffff;
}

.indicator-card-item.normal {
  background-color: #f0f9eb;
  border-top-color: #b3e19d;
}

.indicator-card-item.warning {
  background-color: #fdf6ec;
  border-top-color: #e6a23c;
}

.indicator-card-item.warning .completion-badge {
  background-color: #e6a23c;
  color: #ffffff;
}

.indicator-card-item.bad {
  background-color: #fef0f0;
  border-top-color: #f56c6c;
}

.indicator-card-item.bad .completion-badge {
  background-color: #f56c6c;
  color: #ffffff;
}

.indicator-card-item.very-bad {
  background-color: #f56c6c;
  border-top-color: #d63b3b;
}

.indicator-card-item.very-bad .indicator-name,
.indicator-card-item.very-bad .indicator-department,
.indicator-card-item.very-bad .value-number,
.indicator-card-item.very-bad .value-label {
  color: #ffffff;
}

.indicator-card-item.very-bad .completion-badge {
  background-color: #d63b3b;
  color: #ffffff;
}

.indicator-card-item.terrible {
  background-color: #dc143c; /* 猩红色 */
  border-top-color: #a00f2d;
}

.indicator-card-item.terrible .indicator-name,
.indicator-card-item.terrible .indicator-department,
.indicator-card-item.terrible .value-number,
.indicator-card-item.terrible .value-label {
  color: #ffffff;
}

.indicator-card-item.terrible .completion-badge {
  background-color: #a00f2d;
  color: #ffffff;
}
</style>

