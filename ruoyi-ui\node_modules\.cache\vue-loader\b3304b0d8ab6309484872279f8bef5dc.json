{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\list.vue", "mtime": 1756170476796}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA", "file": "list.vue", "sourceRoot": "src/views/assess/self/config/user", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"工号\" prop=\"workNo\">\r\n            <el-input\r\n              v-model=\"queryParams.workNo\"\r\n              placeholder=\"请输入工号\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n  \r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n  \r\n      <el-table v-loading=\"loading\" :data=\"selfAssessUserList\">\r\n        <!-- <el-table-column label=\"编号\" align=\"center\" prop=\"id\" /> -->\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n        <!-- <el-table-column label=\"身份\" align=\"center\" prop=\"assessRole\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ dicts.self_assess_role[scope.row.assessRole][\"label\"] }}\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column label=\"部门\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-for=\"item, index in scope.row.deptList\" v-bind:key=\"index\">\r\n              {{ scope.row.deptList.length > 1 && index + 1 != scope.row.deptList.length ? item.deptName + \", \" : item.deptName}}  \r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleConfig(scope.row)\"\r\n            >配置</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n  \r\n    </div>\r\n  </template>\r\n  \r\n  <script>\r\n  import { getToken } from \"@/utils/auth\";\r\n  import { listAvailable } from \"@/api/assess/self/user\";\r\n  import { listDept } from \"@/api/assess/lateral/dept\";\r\n  import Treeselect from \"@riophae/vue-treeselect\";\r\n  import \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n  \r\n  export default {\r\n    name: \"SelfAssessUserList\",\r\n    components: {\r\n      Treeselect\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        selfAssessUserList: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null, \r\n          name: null,\r\n          assessRole: null,\r\n          benefitLinkFlag: null,\r\n          averageLinkFlag: null\r\n        },\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 字典\r\n        dicts:{\r\n          self_assess_role:[],\r\n          sys_yes_no:[]\r\n        },\r\n        // 部门下拉树\r\n        deptOptions:[],\r\n        // 导入参数\r\n        upload: {\r\n          // 是否禁用上传\r\n          isUploading: false,\r\n          // 设置上传的请求头部\r\n          headers: { Authorization: 'Bearer ' + getToken() },\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/web/selfAssessUser/importInfo\",\r\n        },\r\n        // 导入结果\r\n        importRes:[],\r\n        openImportRes:false\r\n      };\r\n    },\r\n    created() {\r\n      this.getList();\r\n      this.getTreeselect();\r\n      this.getDicts(\"self_assess_role\").then(response => {\r\n        this.dicts.self_assess_role = this.formatterDict(response.data);\r\n      });\r\n      this.getDicts(\"sys_yes_no\").then(response => {\r\n        this.dicts.sys_yes_no = this.formatterDict(response.data);\r\n      });\r\n    },\r\n    methods: {\r\n      formatterDict(dict){\r\n        let result = []\r\n        dict.forEach(dict => {\r\n          result.push({\r\n            label:dict.dictLabel,\r\n            value:dict.dictValue\r\n          })\r\n        });\r\n        return result;\r\n      },\r\n      /** 查询绩效考核-干部自评人员配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listAvailable(this.queryParams).then(response => {\r\n          this.selfAssessUserList = response.data;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      // 取消按钮\r\n      cancel() {\r\n        this.open = false;\r\n        this.reset();\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.form = {\r\n          id: null,\r\n          workNo: null,\r\n          name: null,\r\n          assessRole: null,\r\n          benefitLinkFlag: null,\r\n          averageLinkFlag: null\r\n        };\r\n        this.resetForm(\"form\");\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n  \r\n      /** 转换横向评价部门数据结构 */\r\n      normalizer(node) {\r\n        if (node.children && !node.children.length) {\r\n          delete node.children;\r\n        }\r\n        return {\r\n          id: node.deptId,\r\n          label: node.deptName,\r\n          children: node.children\r\n        };\r\n      },\r\n        /** 查询横向评价部门下拉树结构 */\r\n      getTreeselect() {\r\n        listDept().then(response => {\r\n          this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n        });\r\n      },\r\n  \r\n      /** 配置点击事件 */\r\n      handleConfig(row){\r\n        this.$router.push({\r\n          path:\"/assess/self/user/detail\",\r\n          query:{\r\n            userId:row.id\r\n          }\r\n        })\r\n      },\r\n  \r\n      \r\n    }\r\n  };\r\n  </script>\r\n  <style>\r\n  .redtext{\r\n    color: red;\r\n  }\r\n  </style>\r\n  "]}]}