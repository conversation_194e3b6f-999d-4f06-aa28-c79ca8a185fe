package com.ruoyi.app.leave.service.impl;

import java.util.List;

import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.StoreoutKqdbMapper;
import com.ruoyi.app.leave.domain.StoreoutKqdbMeasure;
import com.ruoyi.app.leave.service.IStoreoutKqdbService;
import com.ruoyi.app.leave.mapper.DicMapper;
import com.ruoyi.app.leave.domain.DicMeasure;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.domain.LeaveTaskMaterial;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 库区调拨出库Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class StoreoutKqdbServiceImpl implements IStoreoutKqdbService 
{
    @Autowired
    private StoreoutKqdbMapper storeoutKqdbMapper;

    @Autowired
    private DicMapper dicMapper;

    private static final Logger log = LoggerFactory.getLogger(StoreoutKqdbServiceImpl.class);

    /**
     * 查询库区调拨出库
     * 
     * @param id 库区调拨出库主键
     * @return 库区调拨出库
     */
    @Override
    public StoreoutKqdbMeasure selectStoreoutKqdbById(Long id)
    {
        return storeoutKqdbMapper.selectStoreoutKqdbById(id);
    }

    /**
     * 查询库区调拨出库列表
     * 
     * @param storeoutKqdbMeasure 库区调拨出库
     * @return 库区调拨出库
     */
    @Override
    public List<StoreoutKqdbMeasure> selectStoreoutKqdbList(StoreoutKqdbMeasure storeoutKqdbMeasure)
    {
        return storeoutKqdbMapper.selectStoreoutKqdbList(storeoutKqdbMeasure);
    }

    /**
     * 新增库区调拨出库
     * 
     * @param storeoutKqdbMeasure 库区调拨出库
     * @return 结果
     */
    @Override
    public int insertStoreoutKqdb(StoreoutKqdbMeasure storeoutKqdbMeasure)
    {
        return storeoutKqdbMapper.insertStoreoutKqdb(storeoutKqdbMeasure);
    }

    /**
     * 修改库区调拨出库
     * 
     * @param storeoutKqdbMeasure 库区调拨出库
     * @return 结果
     */
    @Override
    public int updateStoreoutKqdb(StoreoutKqdbMeasure storeoutKqdbMeasure)
    {
        return storeoutKqdbMapper.updateStoreoutKqdb(storeoutKqdbMeasure);
    }

    /**
     * 批量删除库区调拨出库
     * 
     * @param ids 需要删除的库区调拨出库主键
     * @return 结果
     */
    @Override
    public int deleteStoreoutKqdbByIds(Long[] ids)
    {
        return storeoutKqdbMapper.deleteStoreoutKqdbByIds(ids);
    }

    /**
     * 删除库区调拨出库信息
     * 
     * @param id 库区调拨出库主键
     * @return 结果
     */
    @Override
    public int deleteStoreoutKqdbById(Long id)
    {
        return storeoutKqdbMapper.deleteStoreoutKqdbById(id);
    }

    /**
     * 根据matchid删除库区调拨出库信息
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @Override
    public int deleteStoreoutKqdbByMatchid(String matchid)
    {
        return storeoutKqdbMapper.deleteStoreoutKqdbByMatchid(matchid);
    }

    /**
     * 跨区调拨出库对接计量系统
     * @param nowDate 当前时间
     * @param leaveTask 任务对象
     * @param leavePlan 计划对象
     * @param leaveTaskMaterial 物资对象
     */
    public void handleKqdbStockOut(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial) {
        // 1. 更新计量系统数据
        DicMeasure dicMeasure = new DicMeasure();
        dicMeasure.setCarno(leaveTask.getCarNum());
        dicMeasure.setOperatype(6L);
        dicMeasure.setPlanid(leavePlan.getPlanNo());
        dicMeasure.setMaterialname(leaveTaskMaterial.getMaterialName());
        dicMeasure.setMaterialspec(leaveTaskMaterial.getMaterialSpec());
        dicMeasure.setSourcename(leavePlan.getSourceCompany());
        dicMeasure.setTargetname(leavePlan.getReceiveCompany());
        dicMeasure.setSourcetime(nowDate);
        dicMeasure.setMatno(leaveTask.getStockOutTotals());
        dicMeasure.setPlancount(leavePlan.getPlannedAmount());
        dicMeasure.setZoushu(leaveTask.getAxles());
        // 物料特殊处理
        String materialName = leaveTaskMaterial.getMaterialName();
        String targetName = leavePlan.getReceiveCompany();
        String sourceName = leavePlan.getSourceCompany();
        if ("废钢".equals(materialName) || "废钢（报废圆钢）".equals(materialName) || "热压铁块".equals(materialName)) {
            dicMeasure.setFgpriceflag("1");
            if ("滨江废钢处理分厂".equals(targetName) || "江阴兴澄特种钢铁有限公司".equals(targetName)) {
                dicMeasure.setShflag("0");
            } else {
                dicMeasure.setShflag("1");
            }
        } else {
            dicMeasure.setFgpriceflag("0");
            dicMeasure.setShflag("1");
        }

        if (sourceName != null && sourceName.contains("滨江废钢处理分厂")) {
            dicMeasure.setFgpriceflag("0");
            dicMeasure.setShflag("1");
        }
        if (sourceName != null && sourceName.contains("滨江废钢处理分厂") && targetName != null && targetName.contains("滨江废钢处理分厂")) {
            dicMeasure.setFgpriceflag("0");
            dicMeasure.setShflag("0");
        }


        dicMeasure.setMsrmemo("炉号：" + leaveTask.getStockOutHeatNo() + "钢种：" + leaveTask.getStockOutSteelGrade() + "规格：" + leaveTask.getStockOutSpec1Length() + "数量：" + leaveTask.getTotals());
        dicMapper.updateDicByCarNo(dicMeasure);

        // 2. 创建跨区调拨出库记录
        StoreoutKqdbMeasure storeoutKqdbMeasure = new StoreoutKqdbMeasure();
        storeoutKqdbMeasure.setValidflag(1L);
        storeoutKqdbMeasure.setCarno(leaveTask.getCarNum());
        storeoutKqdbMeasure.setOperatype("6");
        storeoutKqdbMeasure.setPlanid(leavePlan.getPlanNo());
        storeoutKqdbMeasure.setStorename(leavePlan.getSourceCompany());
        storeoutKqdbMeasure.setTargetname(leavePlan.getReceiveCompany());
        storeoutKqdbMeasure.setMaterialname(leaveTaskMaterial.getMaterialName());
        storeoutKqdbMeasure.setMaterialspec(leaveTaskMaterial.getMaterialSpec());
        storeoutKqdbMeasure.setCreateman(SecurityUtils.getLoginUser().getUser().getNickName());
        storeoutKqdbMeasure.setCreatedate(nowDate);
        storeoutKqdbMeasure.setHeatno(leaveTask.getStockOutHeatNo());
        storeoutKqdbMeasure.setSteelgrade(leaveTask.getStockOutSteelGrade());
        storeoutKqdbMeasure.setMemo(leaveTask.getStockOutRemark());
        storeoutKqdbMeasure.setSpec(leaveTask.getStockOutSpec1Length() != null ? leaveTask.getStockOutSpec1Length().toString() : null);
        storeoutKqdbMeasure.setMatno(leaveTask.getStockOutTotals());

        // 根据车号获取matchid
        DicMeasure dicQuery = new DicMeasure();
        dicQuery.setCarno(leaveTask.getCarNum());
        java.util.List<DicMeasure> dicMeasures = dicMapper.selectDicList(dicQuery);
        if (dicMeasures.size() == 1 && dicMeasures.get(0) != null) {
            storeoutKqdbMeasure.setMatchid(dicMeasures.get(0).getMatchid());
        } else {
            log.error("StoreoutKqdbServiceImpl.handleKqdbStockOut 跨区调拨出库对接计量系统异常");
        }

        // 插入跨区调拨出库记录
        int i = storeoutKqdbMapper.insertStoreoutKqdb(storeoutKqdbMeasure);
        if (i < 0) {
            log.error("StoreoutKqdbServiceImpl.handleKqdbStockOut 跨区调拨出库对接计量系统异常");
        }
    }
} 