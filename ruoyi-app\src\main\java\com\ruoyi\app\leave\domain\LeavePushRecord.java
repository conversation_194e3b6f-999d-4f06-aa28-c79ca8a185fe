package com.ruoyi.app.leave.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出门证推送记录对象 leave_push_record
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public class LeavePushRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 业务单号 */
    @Excel(name = "业务单号")
    private String businessNo;

    /** 业务类型 */
    @Excel(name = "类型 1-同步计量 2-同步智慧物流 3-同步智慧安保")
    private Integer type;

    /** 推送状态 0-待推送 1-推送成功 2-推送失败 */
    @Excel(name = "推送状态 0-待推送 1-推送成功 2-推送失败")
    private Integer pushStatus;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Integer retryCount;

    /** 失败原因 */
    @Excel(name = "失败原因")
    private String failReason;

    /** 下次重试时间 */
    @Excel(name = "下次重试时间")
    private String nextRetryTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBusinessNo(String businessNo) 
    {
        this.businessNo = businessNo;
    }

    public String getBusinessNo() 
    {
        return businessNo;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public Integer getType()    
    {
        return type;
    }

    public void setPushStatus(Integer pushStatus) 
    {
        this.pushStatus = pushStatus;
    }

    public Integer getPushStatus() 
    {
        return pushStatus;
    }

    public void setRetryCount(Integer retryCount) 
    {
        this.retryCount = retryCount;
    }

    public Integer getRetryCount() 
    {
        return retryCount;
    }

    public void setFailReason(String failReason) 
    {
        this.failReason = failReason;
    }

    public String getFailReason() 
    {
        return failReason;
    }

    public void setNextRetryTime(String nextRetryTime) 
    {
        this.nextRetryTime = nextRetryTime;
    }

    public String getNextRetryTime() 
    {
        return nextRetryTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("businessNo", getBusinessNo())
            .append("type", getType())
            .append("pushStatus", getPushStatus())
            .append("retryCount", getRetryCount())
            .append("failReason", getFailReason())
            .append("nextRetryTime", getNextRetryTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
} 