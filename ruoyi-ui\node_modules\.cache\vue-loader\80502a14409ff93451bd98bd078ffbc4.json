{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue", "mtime": 1756170476797}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEluZm9CeURhdGUsIHNhdmVJbmZvLCBzdWJtaXRJbmZvLCBkZWxJbmZvLCBsaXN0QmVBc3Nlc3NlZCB9IGZyb20gIkAvYXBpL2Fzc2Vzcy9zZWxmL2luZm8iOwovLyBpbXBvcnQgeyBiYXRjaFRhcmdldCwgbGlzdFNlbGZUYXJnZXRBbGwgfSBmcm9tICJAL2FwaS9hc3Nlc3Mvc2VsZi90YXJnZXQiOwppbXBvcnQgeyBnZXRSZXBvcnREZXB0TGlzdCwgZ2V0QnlXb3JrTm9EZXB0SWQgfSBmcm9tICJAL2FwaS9hc3Nlc3Mvc2VsZi91c2VyIjsKaW1wb3J0IHsgZm9ybWF0RGF0ZVltIH0gZnJvbSAiQC91dGlscy9pbmRleCIKaW1wb3J0IHsgVnVlU2lnbmF0dXJlUGFkIH0gZnJvbSAndnVlLXNpZ25hdHVyZS1wYWQnOwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIFZ1ZVNpZ25hdHVyZVBhZAogIH0sCiAgbmFtZTogIlNlbGZBc3Nlc3NSZXBvcnQiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIG9wZW5TaWduOmZhbHNlLAogICAgICAvLyDnu6nmlYjogIPmoLgt6Ieq6K+E5oyH5qCH6YWN572u6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHVzZXJJZDpudWxsLAogICAgICAgIHdvcmtObzogbnVsbCwKICAgICAgICBkZXB0SWQ6bnVsbCwKICAgICAgICBhc3Nlc3NEYXRlOiBudWxsLAogICAgICB9LAogICAgICAvLyDogIPmoLjlubTmnIjmlofmnKzmmL7npLoKICAgICAgYXNzZXNzRGF0ZVRleHQ6bnVsbCwKICAgICAgLy8g6YOo6Zeo5pi+56S6CiAgICAgIGRlcHROYW1lOm51bGwsCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgIH0sCiAgICAgIC8vIOeUqOaIt+S/oeaBrwogICAgICB1c2VySW5mbzp7fSwKICAgICAgLy8g5ZCI5bm25Y2V5YWD5qC85L+h5oGvCiAgICAgIHNwYW5MaXN0OnsKICAgICAgICBpdGVtTGlzdDpbXSwKICAgICAgICBzdGFuZGFyZExpc3Q6W10KICAgICAgfSwKICAgICAgLy8g5piv5ZCm5pi+56S66YeN572u5oyJ6ZKuCiAgICAgIHJlc2V0U2hvdzpmYWxzZSwKICAgICAgLy8g5piv5ZCm5Y+q6K+7CiAgICAgIHJlYWRPbmx5OmZhbHNlLAogICAgICAvLyDpg6jpl6jpgInpobkKICAgICAgZGVwdE9wdGlvbnM6W10sCiAgICAgIC8vIOiHquivhOS/oeaBr0lkCiAgICAgIGlkOm51bGwsCiAgICAgIC8vIOiHquivhOWIhuaVsAogICAgICBzZWxmU2NvcmU6MTAwLAogICAgICAvLyDnirbmgIEKICAgICAgc3RhdHVzOiIwIiwKICAgICAgLy8g6Ieq6K+E5L+h5oGvCiAgICAgIGluZm86e30sCiAgICAgIC8vIOaoquWQkeiiq+iAg+ivhOS/oeaBrwogICAgICBiZUFzc2Vzc2VkTGlzdDpbXSwKICAgICAgLy8g6YCA5Zue55CG55SxCiAgICAgIHJlamVjdFJlYXNvbjoiIiwKICAgICAgLy8g6Ieq6K+E562+5ZCNCiAgICAgIHNlbGZTaWduOiIiLAogICAgICAvLyDnrb7lkI3mnb/phY3nva4KICAgICAgc2lnbk9wdGlvbnM6IHsKICAgICAgICBvbkJlZ2luOiAoKSA9PiB0aGlzLiRyZWZzLnNpZ25hdHVyZVBhZC5yZXNpemVDYW52YXMoKSwKICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDEpJwogICAgICB9LAogICAgICBzaWduOiIiLAogICAgICBmaWxlOm51bGwsCiAgICAgIGZpbGVMaXN0OltdLAogICAgICB1cGxvYWQ6IHsKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2FwcC9jb21tb24vdXBsb2FkTWluaW8iLAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgfSwKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlID0gZm9ybWF0RGF0ZVltKG5ldyBEYXRlKCkuZ2V0VGltZSgpKQogICAgdGhpcy5hc3Nlc3NEYXRlVGV4dCA9IHRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZS5yZXBsYWNlKCItIiwiIOW5tCAiKSArICIg5pyIIjsKICAgIC8vIHRoaXMuZ2V0U2VsZkFzc2Vzc1VzZXIoKTsKICAgIHRoaXMuZ2V0UmVwb3J0RGVwdExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIAogICAgLy8g6I635Y+W6YOo6Zeo5L+h5oGvCiAgICBnZXRSZXBvcnREZXB0TGlzdCgpewogICAgICBnZXRSZXBvcnREZXB0TGlzdCgpLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMpCiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIHRoaXMuaGFuZGxlRGVwdExpc3QocmVzLmRhdGEpOwogICAgICAgICAgLy8g5qC55o2u6YOo6Zeo6I635Y+W55So5oi35L+h5oGvCiAgICAgICAgICB0aGlzLmdldEJ5V29ya05vRGVwdElkKCk7CiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8vIOiOt+WPlueUqOaIt+S/oeaBrwogICAgZ2V0QnlXb3JrTm9EZXB0SWQoKXsKICAgICAgZ2V0QnlXb3JrTm9EZXB0SWQoe2RlcHRJZDp0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMpCiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMudXNlcklkID0gcmVzLmRhdGEuaWQ7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLndvcmtObyA9IHJlcy5kYXRhLndvcmtObzsKICAgICAgICAgIHRoaXMudXNlckluZm8gPSByZXMuZGF0YTsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgLy8g6I635Y+W6KKr6ICD5qC45L+h5oGvCiAgICAgICAgICB0aGlzLmdldEJlQXNzZXNzZWRMaXN0KCk7CiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIAogICAgLy8g6I635Y+W6KKr6ICD5qC45L+h5oGvCiAgICBnZXRCZUFzc2Vzc2VkTGlzdCgpewogICAgICBsaXN0QmVBc3Nlc3NlZCh7ZGVwdElkOnRoaXMucXVlcnlQYXJhbXMuZGVwdElkLGFzc2Vzc0RhdGU6dGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlfSkudGhlbihyZXMgPT57CiAgICAgICAgbGV0IGJlQXNzZXNzZWRMaXN0ID0gW107CiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIGlmKHJlcy5kYXRhLmxlbmd0aCA+IDApewogICAgICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICAgIGJlQXNzZXNzZWRMaXN0ID0gWy4uLmJlQXNzZXNzZWRMaXN0LC4uLml0ZW0uaHJMYXRlcmFsQXNzZXNzSW5mb0xpc3RdCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMuYmVBc3Nlc3NlZExpc3QgPSBiZUFzc2Vzc2VkTGlzdDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgY29uc29sZS5sb2coYmVBc3Nlc3NlZExpc3QpCiAgICAgIH0pCiAgICB9LAogICAgLyoqIOafpeivoue7qeaViOiAg+aguC3oh6ror4TmjIfmoIfphY3nva7liJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGdldEluZm9CeURhdGUodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UuZGF0YSk7CiAgICAgICAgLy8gY29uc29sZS5sb2codHlwZW9mIHJlc3BvbnNlLmRhdGEpOwogICAgICAgIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7CiAgICAgICAgICAvLyDmjIfmoIfphY3nva7mlbDmja4KICAgICAgICAgIHRoaXMuaGFuZGxlU3Bhbkxpc3QocmVzcG9uc2UuZGF0YSk7CiAgICAgICAgICB0aGlzLmxpc3QgPSByZXNwb25zZS5kYXRhLm1hcChpdGVtID0+IHtpdGVtLnBlcmZvcm1hbmNlID0gIiI7aXRlbS5kZVBvaW50cyA9IG51bGw7IHJldHVybiBpdGVtfSk7CiAgICAgICAgICB0aGlzLnN0YXR1cyA9ICIwIjsKICAgICAgICAgIHRoaXMucmVhZE9ubHkgPSBmYWxzZTsKICAgICAgICAgIHRoaXMucmVzZXRTaG93ID0gZmFsc2U7CiAgICAgICAgICB0aGlzLnJlamVjdFJlYXNvbiA9IG51bGw7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOiHquivhOS/oeaBrwogICAgICAgICAgbGV0IGluZm8gPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgbGV0IGxpc3QgPSBKU09OLnBhcnNlKGluZm8uY29udGVudCk7CiAgICAgICAgICB0aGlzLmhhbmRsZVNwYW5MaXN0KGxpc3QpOwogICAgICAgICAgdGhpcy5saXN0ID0gbGlzdDsKICAgICAgICAgIHRoaXMuaWQgPSBpbmZvLmlkOwogICAgICAgICAgdGhpcy5zZWxmU2NvcmUgPSBpbmZvLnNlbGZTY29yZTsKICAgICAgICAgIHRoaXMucmVqZWN0UmVhc29uID0gaW5mby5yZWplY3RSZWFzb247CiAgICAgICAgICB0aGlzLnN0YXR1cyA9IGluZm8uc3RhdHVzOwogICAgICAgICAgaWYoaW5mby5zaWduKXsKICAgICAgICAgICAgdGhpcy5zZWxmU2lnbiA9IEpTT04ucGFyc2UoaW5mby5zaWduKTsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuaW5mbyA9IGluZm87CiAgICAgICAgICBpZihpbmZvLnN0YXR1cyA9PSAiMCIpewogICAgICAgICAgICB0aGlzLnJlYWRPbmx5ID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMucmVzZXRTaG93ID0gdHJ1ZTsKICAgICAgICAgIH1lbHNlewogICAgICAgICAgICB0aGlzLnJlYWRPbmx5ID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5yZXNldFNob3cgPSBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDlpITnkIbliJfooagKICAgIGhhbmRsZVNwYW5MaXN0KGRhdGEpewogICAgICBsZXQgaXRlbUxpc3QgPSBbXTsKICAgICAgbGV0IHN0YW5kYXJkTGlzdCA9IFtdOwogICAgICBsZXQgaXRlbUZsYWcgPSAwOwogICAgICBsZXQgc3RhbmRhcmRGbGFnID0gMDsKICAgICAgZm9yKGxldCBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspewogICAgICAgIC8vIOebuOWQjOiAg+aguOmhueOAgeivhOWIhuagh+WHhuWQiOW5tgogICAgICAgIGlmKGkgPT0gMCl7CiAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgfSkKICAgICAgICAgIHN0YW5kYXJkTGlzdC5wdXNoKHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgfSkKICAgICAgICB9ZWxzZXsKICAgICAgICAgIC8vIOiAg+aguOmhuQogICAgICAgICAgaWYoZGF0YVtpIC0gMV0uaXRlbSA9PSBkYXRhW2ldLml0ZW0pewogICAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICAgIGNvbHNwYW46IDAKICAgICAgICAgICAgfSkKICAgICAgICAgICAgaXRlbUxpc3RbaXRlbUZsYWddLnJvd3NwYW4gKz0gMTsKICAgICAgICAgIH1lbHNlewogICAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAxLAogICAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgICAgfSkKICAgICAgICAgICAgaXRlbUZsYWcgPSBpOwogICAgICAgICAgfQogICAgICAgICAgLy8g6K+E5YiG5qCH5YeGCiAgICAgICAgICBpZihkYXRhW2kgLSAxXS5zdGFuZGFyZCA9PSBkYXRhW2ldLnN0YW5kYXJkKXsKICAgICAgICAgICAgc3RhbmRhcmRMaXN0LnB1c2goewogICAgICAgICAgICAgIHJvd3NwYW46IDAsCiAgICAgICAgICAgICAgY29sc3BhbjogMAogICAgICAgICAgICB9KQogICAgICAgICAgICBzdGFuZGFyZExpc3Rbc3RhbmRhcmRGbGFnXS5yb3dzcGFuICs9IDE7CiAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgc3RhbmRhcmRMaXN0LnB1c2goewogICAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgICB9KQogICAgICAgICAgICBzdGFuZGFyZEZsYWcgPSBpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLnNwYW5MaXN0Lml0ZW1MaXN0ID0gaXRlbUxpc3Q7CiAgICAgIHRoaXMuc3Bhbkxpc3Quc3RhbmRhcmRMaXN0ID0gc3RhbmRhcmRMaXN0OwogICAgfSwKCgogICAgLy8g5aSE55CG6YOo6Zeo5LiL5ouJ6YCJ6aG5CiAgICBoYW5kbGVEZXB0TGlzdChkYXRhKXsKICAgICAgLy8gbGV0IHN5YiA9IFsi54K86ZOB5LqL5Lia6YOoIiwi54K86ZKi5LqL5Lia6YOoIiwi6L2n6ZKi5LqL5Lia6YOoIiwi54m55p2/5LqL5Lia6YOoIiwi5Yqo5Yqb5LqL5Lia6YOoIiwi54mp5rWB5LqL5Lia6YOoIiwi56CU56m26ZmiIl07CiAgICAgIGxldCBkZXB0TGlzdCA9IFtdOwogICAgICBkYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgLy9pZihzeWIuaW5kZXhPZihpdGVtLmRlcHROYW1lKSA9PSAtMSl7CiAgICAgICAgICBkZXB0TGlzdC5wdXNoKHsKICAgICAgICAgICAgZGVwdE5hbWU6aXRlbS5kZXB0TmFtZSwKICAgICAgICAgICAgZGVwdElkOml0ZW0uZGVwdElkCiAgICAgICAgICB9KQogICAgICAgIC8vfQogICAgICB9KQogICAgICB0aGlzLmRlcHRPcHRpb25zID0gZGVwdExpc3Q7CiAgICAgIGlmKGRlcHRMaXN0Lmxlbmd0aCA+IDApewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gZGVwdExpc3RbMF0uZGVwdElkOwogICAgICAgIHRoaXMuZGVwdE5hbWUgPSBkZXB0TGlzdFswXS5kZXB0TmFtZTsKICAgICAgfQogICAgfSwKCgogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMuYXNzZXNzRGF0ZVRleHQgPSB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGUucmVwbGFjZSgiLSIsIiDlubQgIikgKyAiIOaciCI7CiAgICAgIHRoaXMuZGVwdE9wdGlvbnMuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZihpdGVtLmRlcHRJZCA9PSB0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCl7CiAgICAgICAgICB0aGlzLmRlcHROYW1lID0gaXRlbS5kZXB0TmFtZTsKICAgICAgICB9CiAgICAgIH0pCiAgICAgIHRoaXMuaWQgPSBudWxsOwogICAgICB0aGlzLmluZm8gPSBudWxsOwogICAgICB0aGlzLnNlbGZTY29yZSA9ICIiOwogICAgICB0aGlzLmdldEJ5V29ya05vRGVwdElkKCk7CiAgICB9LAoKICAgIC8vIOS/neWtmAogICAgc2F2ZSgpewogICAgICBpZih0aGlzLmxpc3QubGVuZ3RoID09IDApewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmnKrphY3nva7nm7jlhbPkv6Hmga/vvIzor7flhYjphY3nva7mjIfmoIcnCiAgICAgICAgICB9KTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBsZXQgZGF0YSA9IHRoaXMuaGFuZGxlRGF0YSh0aGlzLmxpc3QpOwogICAgICBsZXQgZm9ybSA9IHsKICAgICAgICBpZDp0aGlzLmlkLAogICAgICAgIHdvcmtObzp0aGlzLnVzZXJJbmZvLndvcmtObywKICAgICAgICBhc3Nlc3NEYXRlOnRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSwKICAgICAgICBkZXB0SWQ6dGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQsCiAgICAgICAgY29udGVudDpKU09OLnN0cmluZ2lmeShkYXRhKSwKICAgICAgICBzdGF0dXM6IjAiLAogICAgICAgIHVzZXJJZDp0aGlzLnF1ZXJ5UGFyYW1zLnVzZXJJZCwKICAgICAgICBkZXB0TmFtZTp0aGlzLmRlcHROYW1lLAogICAgICAgIG5hbWU6dGhpcy51c2VySW5mby5uYW1lLAogICAgICAgIHNlbGZTY29yZTp0aGlzLnNlbGZTY29yZSwKICAgICAgICBqb2I6dGhpcy51c2VySW5mby5qb2IsCiAgICAgICAgcG9zdFR5cGU6dGhpcy51c2VySW5mby5wb3N0VHlwZQogICAgICB9CiAgICAgIHNhdmVJbmZvKGZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApewogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfIScKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSkKICAgIH0sCgogICAgLy8g56Gu6K6k5o+Q5Lqk54K55Ye75LqL5Lu2CiAgICBzdWJtaXQoKXsKICAgICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTlkI7lsIbmtYHovazoh7PkuIvkuIDoioLngrksIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuc3VibWl0RGF0YSgpOwogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKCiAgICAgICAgfSk7CiAgICB9LAoKICAgIG9uU3VibWl0KCl7CiAgICAgIGlmKHRoaXMudmVyaWZ5SW5zZXJ0KCkpewogICAgICAgIHRoaXMub3BlblNpZ24gPSB0cnVlOwogICAgICB9CiAgICB9LAoKICAgIGNsZWFyU2lnbigpewogICAgICB0aGlzLiRyZWZzLnNpZ25hdHVyZVBhZC5jbGVhclNpZ25hdHVyZSgpOwogICAgfSwKCiAgICAvLyDmj5DkuqTmlbDmja7pqozor4EKICAgIHZlcmlmeUluc2VydCgpewogICAgICBpZih0aGlzLmxpc3QubGVuZ3RoID09IDApewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmnKrphY3nva7nm7jlhbPkv6Hmga/vvIzor7flhYjphY3nva7mjIfmoIcnCiAgICAgICAgICB9KTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBmb3IobGV0IGkgPSAwOyBpIDwgdGhpcy5saXN0Lmxlbmd0aDsgaSsrKXsKICAgICAgICBpZighdGhpcy5saXN0W2ldLnBlcmZvcm1hbmNlIHx8ICF0aGlzLmxpc3RbaV0uZGVQb2ludHMpewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgbWVzc2FnZTogJ+S/oeaBr+acquWhq+WGmeWujOaVtCcKICAgICAgICAgIH0pOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH1lbHNlIGlmKHRoaXMubGlzdFtpXS5kZVBvaW50cyAhPSAwICYmICF0aGlzLmxpc3RbaV0ucG9pbnRzUmVhc29uKXsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmnInliqDlh4/liIbnmoTor7floavlhpnljp/lm6AnCiAgICAgICAgICB9KTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYoIXRoaXMuc2VsZlNjb3JlKXsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgICBtZXNzYWdlOiAn6K+35aGr5YaZ6Ieq6K+E5YiG5pWwJwogICAgICAgICAgfSk7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIHRydWU7CiAgICB9LAoKICAgIC8vIOaWsOWinuaVsOaNrgogICAgc3VibWl0RGF0YSgpewogICAgICBsZXQgZGF0YSA9IHRoaXMuaGFuZGxlRGF0YSh0aGlzLmxpc3QpOwogICAgICBsZXQgZm9ybSA9IHsKICAgICAgICBpZDp0aGlzLmlkLAogICAgICAgIHdvcmtObzp0aGlzLnVzZXJJbmZvLndvcmtObywKICAgICAgICBhc3Nlc3NEYXRlOnRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSwKICAgICAgICBkZXB0SWQ6dGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQsCiAgICAgICAgY29udGVudDpKU09OLnN0cmluZ2lmeShkYXRhKSwKICAgICAgICBzdGF0dXM6IjEiLAogICAgICAgIHVzZXJJZDp0aGlzLnF1ZXJ5UGFyYW1zLnVzZXJJZCwKICAgICAgICBkZXB0TmFtZTp0aGlzLmRlcHROYW1lLAogICAgICAgIG5hbWU6dGhpcy51c2VySW5mby5uYW1lLAogICAgICAgIHNlbGZTY29yZTp0aGlzLnNlbGZTY29yZSwKICAgICAgICBqb2I6dGhpcy51c2VySW5mby5qb2IsCiAgICAgICAgcG9zdFR5cGU6dGhpcy51c2VySW5mby5wb3N0VHlwZSwKICAgICAgICBhdmVyYWdlTGlua0ZsYWc6dGhpcy51c2VySW5mby5hdmVyYWdlTGlua0ZsYWcsCiAgICAgICAgYmVuZWZpdExpbmtGbGFnOnRoaXMudXNlckluZm8uYmVuZWZpdExpbmtGbGFnLAogICAgICAgIHNpZ246SlNPTi5zdHJpbmdpZnkodGhpcy5zaWduKQogICAgICB9CiAgICAgIHN1Ym1pdEluZm8oZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgIHRoaXMuc2lnbiA9ICIiOwogICAgICAgICAgdGhpcy4kcmVmcy5zaWduYXR1cmVQYWQuY2xlYXJTaWduYXR1cmUoKTsKICAgICAgICAgIHRoaXMub3BlblNpZ24gPSBmYWxzZTsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmj5DkuqTmiJDlip8hJwogICAgICAgICAgfSk7CiAgICAgICAgfWVsc2V7CgogICAgICAgIH0KICAgICAgfSkKICAgIH0sCgogICAgLy8g5L+d5a2Y6YeN572uCiAgICByZXNldEluZm8oKXsKICAgICAgLy8g5Yig6Zmk5L+d5a2Y5L+h5oGvCiAgICAgIGRlbEluZm8oe2lkOnRoaXMuaWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIHRoaXMuaWQgPSBudWxsOwogICAgICAgICAgdGhpcy5zZWxmU2NvcmUgPSBudWxsOwogICAgICAgICAgLy8g6I635Y+W6YWN572u5L+h5oGvCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOWkhOeQhuaPkOS6pOaVsOaNrgogICAgaGFuZGxlRGF0YShkYXRhKXsKICAgICAgbGV0IHJlc3VsdCA9IFtdCiAgICAgIGRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBsZXQgZm9ybSA9IHsKICAgICAgICAgIGl0ZW06IGl0ZW0uaXRlbSwKICAgICAgICAgIGNhdGVnb3J5OiBpdGVtLmNhdGVnb3J5LAogICAgICAgICAgdGFyZ2V0OiBpdGVtLnRhcmdldCwKICAgICAgICAgIHN0YW5kYXJkOiBpdGVtLnN0YW5kYXJkLAogICAgICAgICAgcGVyZm9ybWFuY2U6IGl0ZW0ucGVyZm9ybWFuY2UsCiAgICAgICAgICBkZVBvaW50czogaXRlbS5kZVBvaW50cywKICAgICAgICAgIHBvaW50c1JlYXNvbjppdGVtLnBvaW50c1JlYXNvbgogICAgICAgIH07CiAgICAgICAgcmVzdWx0LnB1c2goZm9ybSk7CiAgICAgIH0pCiAgICAgIHJldHVybiByZXN1bHQKICAgIH0sCgogICAgLyoqIOagh+WHhumFjee9rui3s+i9rCAqLwogICAgaGFuZGxlQ29uZmlnKCl7CiAgICAgIGdldEJ5V29ya05vRGVwdElkKHtkZXB0SWQ6dGhpcy5xdWVyeVBhcmFtcy5kZXB0SWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgY29uc29sZS5sb2cocmVzKQogICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICBpZihyZXMuZGF0YS5pZCl7CiAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgIHBhdGg6Ii9hc3Nlc3Mvc2VsZi91c2VyL2RldGFpbCIsCiAgICAgICAgICAgIHF1ZXJ5OnsKICAgICAgICAgICAgICB1c2VySWQ6cmVzLmRhdGEuaWQKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgICAgfQogICAgICB9KQogICAgICAKICAgIH0sCgoKICAgIC8vIOWQiOW5tuWNleWFg+agvOaWueazlQogICAgb2JqZWN0U3Bhbk1ldGhvZCh7IHJvdywgY29sdW1uLCByb3dJbmRleCwgY29sdW1uSW5kZXggfSkgewogICAgICAvLyDnrKzkuIDliJfnm7jlkIzpobnlkIjlubYKICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAwKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuc3Bhbkxpc3QuaXRlbUxpc3Rbcm93SW5kZXhdOwogICAgICB9CiAgICAgIC8vIOivhOWIhuagh+WHhuebuOWQjOWQiOW5tgogICAgICBpZihjb2x1bW5JbmRleCA9PT0gMyl7CiAgICAgICAgcmV0dXJuIHRoaXMuc3Bhbkxpc3Quc3RhbmRhcmRMaXN0W3Jvd0luZGV4XTsKICAgICAgfQogICAgICAvLyDnsbvliKvml6DlhoXlrrkg5ZCI5bm2CiAgICAgIGlmKGNvbHVtbkluZGV4ID09PSAxKXsKICAgICAgICBpZighcm93LmNhdGVnb3J5KXsKICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIHJvd3NwYW46IDAsCiAgICAgICAgICAgIGNvbHNwYW46IDAKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgaWYoY29sdW1uSW5kZXggPT09IDIpewogICAgICAgIGlmKCFyb3cuY2F0ZWdvcnkpewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMgogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvLyDooqvogIPmoLjkuovpobnngrnlh7vkuovku7YKICAgIGhhbmRsZUJlQXNzZXNzZWRDbGljayhyb3csb3B0aW9uUm93LGluZGV4KXsKICAgICAgY29uc29sZS5sb2cocm93KQogICAgICAvLyDlsIbkuovpobnloavlhaXlrozmiJDlrp7nu6nliJfvvIjlvIPnlKjvvIkKICAgICAgLy8gaWYocm93LnBlcmZvcm1hbmNlKXsKICAgICAgLy8gICB0aGlzLiRzZXQocm93LCAncGVyZm9ybWFuY2UnLCByb3cucGVyZm9ybWFuY2UgKyAi77ybIiArIG9wdGlvblJvdy5hc3Nlc3NDb250ZW50KTsKICAgICAgLy8gfWVsc2V7CiAgICAgIC8vICAgdGhpcy4kc2V0KHJvdywgJ3BlcmZvcm1hbmNlJywgb3B0aW9uUm93LmFzc2Vzc0NvbnRlbnQpOwogICAgICAvLyB9CiAgICAgIAogICAgICAvLyDlsIbliIbmlbDloavlhaXliqDlh4/liIbliJcKICAgICAgaWYocm93LmRlUG9pbnRzKXsKICAgICAgICB0aGlzLiRzZXQocm93LCAnZGVQb2ludHMnLCBOdW1iZXIocm93LmRlUG9pbnRzKSArIE51bWJlcihvcHRpb25Sb3cuZGVkdWN0aW9uT2ZQb2ludCkpOwogICAgICB9ZWxzZXsKICAgICAgICB0aGlzLiRzZXQocm93LCAnZGVQb2ludHMnLCBOdW1iZXIob3B0aW9uUm93LmRlZHVjdGlvbk9mUG9pbnQpKTsKICAgICAgfQogICAgICAKICAgICAgLy8g5bCG5LqL6aG5K+WIhuaVsOWhq+WFpeWKoOWHj+WIhueQhueUseWIlwogICAgICBsZXQgcmVhc29uQ29udGVudCA9IG9wdGlvblJvdy5hc3Nlc3NDb250ZW50ICsgIigiICsgb3B0aW9uUm93LmRlZHVjdGlvbk9mUG9pbnQgKyAi5YiGKSI7CiAgICAgIGlmKHJvdy5wb2ludHNSZWFzb24pewogICAgICAgIHRoaXMuJHNldChyb3csICdwb2ludHNSZWFzb24nLCByb3cucG9pbnRzUmVhc29uICsgIu+8myIgKyByZWFzb25Db250ZW50KTsKICAgICAgfWVsc2V7CiAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3BvaW50c1JlYXNvbicsIHJlYXNvbkNvbnRlbnQpOwogICAgICB9CiAgICAgIAogICAgICB0aGlzLiRyZWZzW2Bwb3BvdmVyJHtpbmRleH1gXS5zaG93UG9wcGVyID0gZmFsc2U7CiAgICAgIC8vIOmHjeaWsOiuoeeul+iHquivhOWIhuaVsAogICAgICB0aGlzLnNjb3JlSW5wdXQoKTsKICAgIH0sCgogICAgLy8g5Yqg5YeP5YiG6L6T5YWlCiAgICBzY29yZUlucHV0KHJvdyA9IG51bGwpewogICAgICAvLyDpqozor4HmnIjluqbph43ngrnlt6XkvZznmoTliqDlh4/liIblj6rog73kuLox44CBM+aIljXvvIjku4XlvZPkvKDlhaVyb3flj4LmlbDml7bov5vooYzpqozor4HvvIkKICAgICAgaWYgKHJvdyAmJiByb3cuaXRlbSkgewogICAgICAgIGxldCBub1NwYWNlU3RyID0gcm93Lml0ZW0ucmVwbGFjZSgvXHMrL2csICcnKTsKICAgICAgICBpZiAobm9TcGFjZVN0ci5pbmNsdWRlcygi5pyI5bqm6YeN54K55bel5L2cIikpIHsKICAgICAgICAgIGxldCB2YWx1ZSA9IHJvdy5kZVBvaW50czsKICAgICAgICAgIGlmICh2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gdW5kZWZpbmVkICYmIHZhbHVlICE9PSAnJykgewogICAgICAgICAgICBsZXQgbnVtVmFsdWUgPSBOdW1iZXIodmFsdWUpOwogICAgICAgICAgICBpZiAoIVsxLCAzLCA1XS5pbmNsdWRlcyhudW1WYWx1ZSkpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfmnIjluqbph43ngrnlt6XkvZznmoTliqDlh4/liIblj6rog73kuLox5YiG44CBM+WIhuaIljXliIYnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgLy8g6YeN572u5Li656m65YC8CiAgICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ2RlUG9pbnRzJywgbnVsbCk7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDph43mlrDorqHnrpfoh6ror4TliIbmlbAKICAgICAgbGV0IGRlUG9pbnRzID0gMDsKICAgICAgbGV0IHBvaW50cyA9IDA7CiAgICAgIHRoaXMubGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGxldCBub1NwYWNlU3RyID0gaXRlbS5pdGVtLnJlcGxhY2UoL1xzKy9nLCAnJyk7CiAgICAgICAgaWYoaXRlbS5kZVBvaW50cyAmJiBub1NwYWNlU3RyLmluY2x1ZGVzKCLmnIjluqbph43ngrnlt6XkvZwiKSl7CiAgICAgICAgICBwb2ludHMgKz0gTnVtYmVyKGl0ZW0uZGVQb2ludHMpOwogICAgICAgIH1lbHNlIGlmKGl0ZW0uZGVQb2ludHMpewogICAgICAgICAgZGVQb2ludHMgKz0gTnVtYmVyKGl0ZW0uZGVQb2ludHMpOwogICAgICAgIH0KICAgICAgfSkKICAgICAgdGhpcy5zZWxmU2NvcmUgPSA4NSArIGRlUG9pbnRzICsgcG9pbnRzOwogICAgfSwKCiAgICAvLyDnrb7lkI3kuIrkvKDnm7jlhbMKICAgIHVwbG9hZFNpZ25hdHVyZSgpewogICAgICBjb25zdCB7IGlzRW1wdHksIGRhdGEgfSA9IHRoaXMuJHJlZnMuc2lnbmF0dXJlUGFkLnNhdmVTaWduYXR1cmUoKTsKICAgICAgY29uc29sZS5sb2coaXNFbXB0eSxkYXRhKQogICAgICBpZihpc0VtcHR5KXsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgIG1lc3NhZ2U6ICfor7fnrb7lkI0hJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfWVsc2V7CiAgICAgICAgY29uc3QgYmxvYkJpbiA9IGF0b2IoZGF0YS5zcGxpdCgnLCcpWzFdKTsKICAgICAgICBsZXQgYXJyYXkgPSBbXTsKICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGJsb2JCaW4ubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIGFycmF5LnB1c2goYmxvYkJpbi5jaGFyQ29kZUF0KGkpKTsKICAgICAgICB9CiAgICAgICAgY29uc3QgZmlsZUJsb2IgPSBuZXcgQmxvYihbbmV3IFVpbnQ4QXJyYXkoYXJyYXkpXSwgeyB0eXBlOiAnaW1hZ2UvcG5nJyB9KTsKICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpOwogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZmlsZScsIGZpbGVCbG9iLCBgJHtEYXRlLm5vdygpfS5wbmdgKTsKICAgICAgICBmZXRjaCh0aGlzLnVwbG9hZC51cmwsIHsKICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLAogICAgICAgICAgYm9keTogZm9ybURhdGEsCiAgICAgICAgfSkKICAgICAgICAudGhlbihyZXNwb25zZSA9PiByZXNwb25zZS5qc29uKCkpCiAgICAgICAgLnRoZW4oZGF0YSA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZygnU3VjY2VzczonLCBkYXRhKTsKICAgICAgICAgIGlmKGRhdGEuY29kZSA9PSAyMDApewogICAgICAgICAgICB0aGlzLnNpZ24gPSB7ZmlsZU5hbWU6dGhpcy51c2VySW5mby5uYW1lICsgIi5wbmciLHVybDpkYXRhLnVybH07CiAgICAgICAgICAgIHRoaXMuc3VibWl0KCk7CiAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn562+5ZCN5LiK5Lyg5aSx6LSlJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yOicsIGVycm9yKTsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICBtZXNzYWdlOiAn562+5ZCN5LiK5Lyg5byC5bi4JwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgCiAgICB9LAogIH0sCn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2LA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/assess/self", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            :clearable=\"false\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\">\r\n            <el-option\r\n              v-for=\"item in deptOptions\"\r\n              :key=\"item.deptId\"\r\n              :label=\"item.deptName\"\r\n              :value=\"item.deptId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-s-tools\" size=\"mini\" @click=\"handleConfig\">指标配置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row>\r\n        <el-form-item label=\"当前状态\">\r\n          <el-tag v-if=\"status == '0' && rejectReason\" type=\"danger\">退 回</el-tag>\r\n          <el-tag v-if=\"status == '0' && !rejectReason\" type=\"info\">未提交</el-tag>\r\n          <el-tag v-if=\"status == '1'\" type=\"warning\">部门领导评分</el-tag>\r\n          <el-tag v-if=\"status == '2'\" type=\"warning\">事业部评分</el-tag>\r\n          <el-tag v-if=\"status == '3'\" type=\"warning\">运改部/组织部审核</el-tag>\r\n          <el-tag v-if=\"status == '4'\" type=\"warning\">总经理部评分</el-tag>\r\n          <el-tag v-if=\"status == '5'\" type=\"success\">已完成</el-tag>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row v-if=\"rejectReason\">\r\n        <el-form-item label=\"退回原因\" prop=\"rejectReason\">\r\n            <span class=\"el-icon-warning\" style=\"color: #f56c6c;\"></span>\r\n            {{ rejectReason }}\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n      <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n      <el-descriptions class=\"margin-top\" :column=\"3\">\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            姓名\r\n          </template>\r\n          {{ userInfo.name }}\r\n        </el-descriptions-item>\r\n        <!-- <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            工号\r\n          </template>\r\n          {{ userInfo.workNo }}\r\n        </el-descriptions-item> -->\r\n        <!-- <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            身份\r\n          </template>\r\n          <span v-if=\"userInfo.assessRole == '0'\">干部</span>\r\n          <span v-if=\"userInfo.assessRole == '1'\">一把手</span>\r\n          <span v-if=\"userInfo.assessRole == '2'\">条线领导</span>\r\n        </el-descriptions-item> -->\r\n\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            部门\r\n          </template>\r\n          {{ deptName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            考核年月\r\n          </template>\r\n          {{ assessDateText }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-table v-loading=\"loading\" :data=\"list\"\r\n        :span-method=\"objectSpanMethod\" border>\r\n        <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n        <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"140\"/>\r\n        <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"150\" />\r\n        <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n        <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.performance }}</span>\r\n                <div v-else style=\"display: flex\">\r\n                  <!-- <el-button icon=\"el-icon-search\" size=\"small\"></el-button> -->\r\n                  <el-popover\r\n                    placement=\"left\"\r\n                    width=\"636\"\r\n                    trigger=\"click\"\r\n                    :ref=\"'popover' + scope.$index\">\r\n                    <el-table :data=\"beAssessedList\">\r\n                      <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                      <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                      <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      <el-table-column width=\"80\" label=\"操作\">\r\n                        <template slot-scope=\"beAssessed\">\r\n                          <el-button\r\n                            size=\"mini\"\r\n                            type=\"text\"\r\n                            icon=\"el-icon-edit\"\r\n                            @click=\"handleBeAssessedClick(scope.row,beAssessed.row,scope.$index)\"\r\n                          >填入</el-button>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                    <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                  </el-popover>\r\n                  <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.performance\" placeholder=\"请输入完成实绩\" />\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"108\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.dePoints }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"number\" autosize v-model=\"scope.row.dePoints\" placeholder=\"请输入加减分\" @input=\"scoreInput(scope.row)\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\"  width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.pointsReason }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.pointsReason\" placeholder=\"请输入加减分原因\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" style=\"margin-top: 10px;\" label-position=\"left\">\r\n            <el-form-item v-if=\"readOnly\" label=\"自评分数 / 签名：\" prop=\"deptId\">\r\n              <div style=\"display: flex;\">\r\n                <span >{{ selfScore + \" 分 / \" }}</span>\r\n                <span v-if=\"!selfSign\">{{info.name}}</span>\r\n                <el-image v-else\r\n                  style=\"width: 100px; height: 46px\"\r\n                  :src=\"selfSign.url\"\r\n                  :fit=\"fit\"></el-image>\r\n              </div>\r\n              \r\n            </el-form-item>\r\n            <el-form-item v-else label=\"自评分数：\" prop=\"selfScore\">\r\n              <div style=\"display: flex;width: 180px;\">\r\n                <el-input type=\"number\" autosize v-model=\"selfScore\" placeholder=\"请输入分数\" :readonly=\"readOnly\" />\r\n                <span style=\"margin-left: 8px;\">分</span>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"status > '1' && info.deptScore && info.deptUserName\" label=\"部门领导评分 / 签名：\">\r\n              <span >{{ info.deptScore + \" 分 / \" + info.deptUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"status > '2' && info.businessUserName && info.businessScore\" label=\"事业部领导评分 / 签名：\">\r\n              <span>{{ info.businessScore + \" 分 / \" + info.businessUserName }}</span>\r\n            </el-form-item>\r\n            <!-- <el-form-item v-if=\"status > '4' && info.leaderScore && info.leaderName\" label=\"总经理部领导评分 / 签名：\" prop=\"deptId\">\r\n              <span >{{ info.leaderScore + \" 分 / \" + info.leaderName }}</span>\r\n            </el-form-item> -->\r\n          </el-form>\r\n      <div v-if=\"!readOnly\" style=\"text-align: center;\">\r\n        <el-button v-if=\"resetShow\" plain type=\"info\" @click=\"resetInfo\">重 置 </el-button>\r\n        <el-button type=\"success\" @click=\"save\">保 存</el-button>\r\n        <el-button type=\"primary\" @click=\"onSubmit\">提 交</el-button>\r\n      </div>\r\n\r\n      <!-- 签名板 -->\r\n      <el-dialog title=\"签字确认\" :visible.sync=\"openSign\" width=\"760px\" append-to-body>\r\n        <div style=\"border: 1px #ccc solid;width: 702px;\">\r\n            <vue-signature-pad\r\n            width=\"700px\"\r\n            height=\"300px\"\r\n            ref=\"signaturePad\"\r\n            :options=\"signOptions\"\r\n          />\r\n        </div>\r\n        <div style=\"text-align: center;padding: 10px 10px;\">\r\n          <el-button style=\"margin-right: 20px;\" type=\"success\" @click=\"clearSign\">清除</el-button>\r\n          <el-button type=\"primary\" @click=\"uploadSignature\">确认</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { getInfoByDate, saveInfo, submitInfo, delInfo, listBeAssessed } from \"@/api/assess/self/info\";\r\n  // import { batchTarget, listSelfTargetAll } from \"@/api/assess/self/target\";\r\n  import { getReportDeptList, getByWorkNoDeptId } from \"@/api/assess/self/user\";\r\n  import { formatDateYm } from \"@/utils/index\"\r\n  import { VueSignaturePad } from 'vue-signature-pad';\r\n\r\n  export default {\r\n    components: {\r\n      VueSignaturePad\r\n    },\r\n    name: \"SelfAssessReport\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        openSign:false,\r\n        // 绩效考核-自评指标配置表格数据\r\n        list: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          userId:null,\r\n          workNo: null,\r\n          deptId:null,\r\n          assessDate: null,\r\n        },\r\n        // 考核年月文本显示\r\n        assessDateText:null,\r\n        // 部门显示\r\n        deptName:null,\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 用户信息\r\n        userInfo:{},\r\n        // 合并单元格信息\r\n        spanList:{\r\n          itemList:[],\r\n          standardList:[]\r\n        },\r\n        // 是否显示重置按钮\r\n        resetShow:false,\r\n        // 是否只读\r\n        readOnly:false,\r\n        // 部门选项\r\n        deptOptions:[],\r\n        // 自评信息Id\r\n        id:null,\r\n        // 自评分数\r\n        selfScore:100,\r\n        // 状态\r\n        status:\"0\",\r\n        // 自评信息\r\n        info:{},\r\n        // 横向被考评信息\r\n        beAssessedList:[],\r\n        // 退回理由\r\n        rejectReason:\"\",\r\n        // 自评签名\r\n        selfSign:\"\",\r\n        // 签名板配置\r\n        signOptions: {\r\n          onBegin: () => this.$refs.signaturePad.resizeCanvas(),\r\n          backgroundColor: 'rgba(255, 255, 255, 1)'\r\n        },\r\n        sign:\"\",\r\n        file:null,\r\n        fileList:[],\r\n        upload: {\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/app/common/uploadMinio\",\r\n          isUploading: false,\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.queryParams.assessDate = formatDateYm(new Date().getTime())\r\n      this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n      // this.getSelfAssessUser();\r\n      this.getReportDeptList();\r\n    },\r\n    methods: {\r\n      \r\n      // 获取部门信息\r\n      getReportDeptList(){\r\n        getReportDeptList().then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.handleDeptList(res.data);\r\n            // 根据部门获取用户信息\r\n            this.getByWorkNoDeptId();\r\n          }\r\n        })\r\n      },\r\n      // 获取用户信息\r\n      getByWorkNoDeptId(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.queryParams.userId = res.data.id;\r\n            this.queryParams.workNo = res.data.workNo;\r\n            this.userInfo = res.data;\r\n            this.getList();\r\n            // 获取被考核信息\r\n            this.getBeAssessedList();\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 获取被考核信息\r\n      getBeAssessedList(){\r\n        listBeAssessed({deptId:this.queryParams.deptId,assessDate:this.queryParams.assessDate}).then(res =>{\r\n          let beAssessedList = [];\r\n          if(res.code == 200){\r\n            if(res.data.length > 0){\r\n              res.data.forEach(item => {\r\n                beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n              })\r\n              this.beAssessedList = beAssessedList;\r\n            }\r\n          }\r\n          console.log(beAssessedList)\r\n        })\r\n      },\r\n      /** 查询绩效考核-自评指标配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        getInfoByDate(this.queryParams).then(response => {\r\n          console.log(response.data);\r\n          // console.log(typeof response.data);\r\n          if (Array.isArray(response.data)) {\r\n            // 指标配置数据\r\n            this.handleSpanList(response.data);\r\n            this.list = response.data.map(item => {item.performance = \"\";item.dePoints = null; return item});\r\n            this.status = \"0\";\r\n            this.readOnly = false;\r\n            this.resetShow = false;\r\n            this.rejectReason = null;\r\n          } else {\r\n            // 自评信息\r\n            let info = response.data;\r\n            let list = JSON.parse(info.content);\r\n            this.handleSpanList(list);\r\n            this.list = list;\r\n            this.id = info.id;\r\n            this.selfScore = info.selfScore;\r\n            this.rejectReason = info.rejectReason;\r\n            this.status = info.status;\r\n            if(info.sign){\r\n              this.selfSign = JSON.parse(info.sign);\r\n            }\r\n            this.info = info;\r\n            if(info.status == \"0\"){\r\n              this.readOnly = false;\r\n              this.resetShow = true;\r\n            }else{\r\n              this.readOnly = true;\r\n              this.resetShow = false;\r\n            }\r\n          }\r\n          this.loading = false;\r\n        });\r\n      },\r\n\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let itemList = [];\r\n        let standardList = [];\r\n        let itemFlag = 0;\r\n        let standardFlag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项、评分标准合并\r\n          if(i == 0){\r\n            itemList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            standardList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            // 考核项\r\n            if(data[i - 1].item == data[i].item){\r\n              itemList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              itemList[itemFlag].rowspan += 1;\r\n            }else{\r\n              itemList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              itemFlag = i;\r\n            }\r\n            // 评分标准\r\n            if(data[i - 1].standard == data[i].standard){\r\n              standardList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              standardList[standardFlag].rowspan += 1;\r\n            }else{\r\n              standardList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              standardFlag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList.itemList = itemList;\r\n        this.spanList.standardList = standardList;\r\n      },\r\n\r\n\r\n      // 处理部门下拉选项\r\n      handleDeptList(data){\r\n        // let syb = [\"炼铁事业部\",\"炼钢事业部\",\"轧钢事业部\",\"特板事业部\",\"动力事业部\",\"物流事业部\",\"研究院\"];\r\n        let deptList = [];\r\n        data.forEach(item => {\r\n          //if(syb.indexOf(item.deptName) == -1){\r\n            deptList.push({\r\n              deptName:item.deptName,\r\n              deptId:item.deptId\r\n            })\r\n          //}\r\n        })\r\n        this.deptOptions = deptList;\r\n        if(deptList.length > 0){\r\n          this.queryParams.deptId = deptList[0].deptId;\r\n          this.deptName = deptList[0].deptName;\r\n        }\r\n      },\r\n\r\n\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n        this.deptOptions.forEach(item => {\r\n          if(item.deptId == this.queryParams.deptId){\r\n            this.deptName = item.deptName;\r\n          }\r\n        })\r\n        this.id = null;\r\n        this.info = null;\r\n        this.selfScore = \"\";\r\n        this.getByWorkNoDeptId();\r\n      },\r\n\r\n      // 保存\r\n      save(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"0\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType\r\n        }\r\n        saveInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.$message({\r\n              type: 'success',\r\n              message: '保存成功!'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n      // 确认提交点击事件\r\n      submit(){\r\n          this.$confirm('确认后将流转至下一节点, 是否继续?', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitData();\r\n          }).catch(() => {\r\n\r\n          });\r\n      },\r\n\r\n      onSubmit(){\r\n        if(this.verifyInsert()){\r\n          this.openSign = true;\r\n        }\r\n      },\r\n\r\n      clearSign(){\r\n        this.$refs.signaturePad.clearSignature();\r\n      },\r\n\r\n      // 提交数据验证\r\n      verifyInsert(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        for(let i = 0; i < this.list.length; i++){\r\n          if(!this.list[i].performance || !this.list[i].dePoints){\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '信息未填写完整'\r\n            });\r\n            return false;\r\n          }else if(this.list[i].dePoints != 0 && !this.list[i].pointsReason){\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '有加减分的请填写原因'\r\n            });\r\n            return false;\r\n          }\r\n        }\r\n        if(!this.selfScore){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '请填写自评分数'\r\n            });\r\n            return false;\r\n        }\r\n        return true;\r\n      },\r\n\r\n      // 新增数据\r\n      submitData(){\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"1\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType,\r\n          averageLinkFlag:this.userInfo.averageLinkFlag,\r\n          benefitLinkFlag:this.userInfo.benefitLinkFlag,\r\n          sign:JSON.stringify(this.sign)\r\n        }\r\n        submitInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.sign = \"\";\r\n            this.$refs.signaturePad.clearSignature();\r\n            this.openSign = false;\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n          }else{\r\n\r\n          }\r\n        })\r\n      },\r\n\r\n      // 保存重置\r\n      resetInfo(){\r\n        // 删除保存信息\r\n        delInfo({id:this.id}).then(res => {\r\n          if(res.code == 200){\r\n            this.id = null;\r\n            this.selfScore = null;\r\n            // 获取配置信息\r\n            this.getList();\r\n          }\r\n        })\r\n      },\r\n\r\n      // 处理提交数据\r\n      handleData(data){\r\n        let result = []\r\n        data.forEach(item => {\r\n          let form = {\r\n            item: item.item,\r\n            category: item.category,\r\n            target: item.target,\r\n            standard: item.standard,\r\n            performance: item.performance,\r\n            dePoints: item.dePoints,\r\n            pointsReason:item.pointsReason\r\n          };\r\n          result.push(form);\r\n        })\r\n        return result\r\n      },\r\n\r\n      /** 标准配置跳转 */\r\n      handleConfig(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            if(res.data.id){\r\n            this.$router.push({\r\n              path:\"/assess/self/user/detail\",\r\n              query:{\r\n                userId:res.data.id\r\n              }\r\n            })\r\n          }\r\n          }\r\n        })\r\n        \r\n      },\r\n\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList.itemList[rowIndex];\r\n        }\r\n        // 评分标准相同合并\r\n        if(columnIndex === 3){\r\n          return this.spanList.standardList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      // 被考核事项点击事件\r\n      handleBeAssessedClick(row,optionRow,index){\r\n        console.log(row)\r\n        // 将事项填入完成实绩列（弃用）\r\n        // if(row.performance){\r\n        //   this.$set(row, 'performance', row.performance + \"；\" + optionRow.assessContent);\r\n        // }else{\r\n        //   this.$set(row, 'performance', optionRow.assessContent);\r\n        // }\r\n        \r\n        // 将分数填入加减分列\r\n        if(row.dePoints){\r\n          this.$set(row, 'dePoints', Number(row.dePoints) + Number(optionRow.deductionOfPoint));\r\n        }else{\r\n          this.$set(row, 'dePoints', Number(optionRow.deductionOfPoint));\r\n        }\r\n        \r\n        // 将事项+分数填入加减分理由列\r\n        let reasonContent = optionRow.assessContent + \"(\" + optionRow.deductionOfPoint + \"分)\";\r\n        if(row.pointsReason){\r\n          this.$set(row, 'pointsReason', row.pointsReason + \"；\" + reasonContent);\r\n        }else{\r\n          this.$set(row, 'pointsReason', reasonContent);\r\n        }\r\n        \r\n        this.$refs[`popover${index}`].showPopper = false;\r\n        // 重新计算自评分数\r\n        this.scoreInput();\r\n      },\r\n\r\n      // 加减分输入\r\n      scoreInput(row = null){\r\n        // 验证月度重点工作的加减分只能为1、3或5（仅当传入row参数时进行验证）\r\n        if (row && row.item) {\r\n          let noSpaceStr = row.item.replace(/\\s+/g, '');\r\n          if (noSpaceStr.includes(\"月度重点工作\")) {\r\n            let value = row.dePoints;\r\n            if (value !== null && value !== undefined && value !== '') {\r\n              let numValue = Number(value);\r\n              if (![1, 3, 5].includes(numValue)) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '月度重点工作的加减分只能为1分、3分或5分'\r\n                });\r\n                // 重置为空值\r\n                this.$set(row, 'dePoints', null);\r\n                return;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 重新计算自评分数\r\n        let dePoints = 0;\r\n        let points = 0;\r\n        this.list.forEach(item => {\r\n          let noSpaceStr = item.item.replace(/\\s+/g, '');\r\n          if(item.dePoints && noSpaceStr.includes(\"月度重点工作\")){\r\n            points += Number(item.dePoints);\r\n          }else if(item.dePoints){\r\n            dePoints += Number(item.dePoints);\r\n          }\r\n        })\r\n        this.selfScore = 85 + dePoints + points;\r\n      },\r\n\r\n      // 签名上传相关\r\n      uploadSignature(){\r\n        const { isEmpty, data } = this.$refs.signaturePad.saveSignature();\r\n        console.log(isEmpty,data)\r\n        if(isEmpty){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请签名!'\r\n          });\r\n          return false;\r\n        }else{\r\n          const blobBin = atob(data.split(',')[1]);\r\n          let array = [];\r\n          for (let i = 0; i < blobBin.length; i++) {\r\n            array.push(blobBin.charCodeAt(i));\r\n          }\r\n          const fileBlob = new Blob([new Uint8Array(array)], { type: 'image/png' });\r\n          const formData = new FormData();\r\n          formData.append('file', fileBlob, `${Date.now()}.png`);\r\n          fetch(this.upload.url, {\r\n            method: 'POST',\r\n            body: formData,\r\n          })\r\n          .then(response => response.json())\r\n          .then(data => {\r\n            console.log('Success:', data);\r\n            if(data.code == 200){\r\n              this.sign = {fileName:this.userInfo.name + \".png\",url:data.url};\r\n              this.submit();\r\n            }else{\r\n              this.$message({\r\n                type: 'error',\r\n                message: '签名上传失败'\r\n              });\r\n            }\r\n          })\r\n          .catch((error) => {\r\n            console.error('Error:', error);\r\n            this.$message({\r\n              type: 'error',\r\n              message: '签名上传异常'\r\n            });\r\n          });\r\n        }\r\n        \r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .table-striped{\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n    text-align: center;\r\n    border: 1px #888;\r\n    border-collapse: collapse;\r\n  }\r\n  .table-striped th{\r\n    height: 32px;\r\n    border: 1px solid #888;\r\n    background-color: #dedede;\r\n  }\r\n  .table-striped td{\r\n    min-height: 32px;\r\n    border: 1px solid #888;\r\n  }\r\n  .table-input .el-textarea__inner{\r\n    border: 0 !important;\r\n    resize: none !important;\r\n  }\r\n  .table-input .el-input__inner{\r\n    border: 0 !important;\r\n  }\r\n  /* .myUpload .el-upload--picture-card{\r\n    display:none !important; \r\n  } */\r\n  </style>\r\n"]}]}