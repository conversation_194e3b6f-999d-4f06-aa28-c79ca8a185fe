package com.ruoyi.measure.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.google.common.collect.Lists;
import com.ruoyi.app.dgcb.service.IDGCBCommonService;
import com.ruoyi.app.leave.service.ILeaveTaskService;
import com.ruoyi.app.truck.common.domain.TruckLog;
import com.ruoyi.app.truck.common.mapper.TruckLogMapper;
import com.ruoyi.app.truck.scrapSteel.domain.ScrapSteelLog;
import com.ruoyi.app.truck.scrapSteel.domain.ScrapSteelOrder;
import com.ruoyi.app.truck.scrapSteel.mapper.ScrapSteelLogMapper;
import com.ruoyi.app.truck.scrapSteel.mapper.ScrapSteelOrderMapper;
import com.ruoyi.app.truck.scrapSteel.service.impl.ScrapSteelOrderServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.measure.domain.*;
import com.ruoyi.app.vehicleAccess.common.utils.RequestUtils;
import com.ruoyi.app.vehicleAccess.domain.AccessInfo;
import com.ruoyi.app.vehicleAccess.service.impl.XctgVehicleAuditFlowServiceImpl;
import com.ruoyi.common.utils.FastJsonUtils;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.measure.enums.OperateTypeEnum;
import com.ruoyi.measure.mapper.*;
import com.ruoyi.measure.service.ISmartLogisticsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.jsoup.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/10/26 10:30
 */
@Service
public class SmartLogisticsServiceImpl implements ISmartLogisticsService {
    private static final Logger log = LoggerFactory.getLogger(XctgVehicleAuditFlowServiceImpl.class);

    //智慧物流过磅回皮通知
    public static final String NOTIFY_URL = "http://172.16.64.235:61121/nx/api/measureNotify";

    //废钢智能判级计量信息推送
    public static final String NOTIFYFG_URL = "http://10.100.1.10:8010/imp-ib-iv-igs-server/ibd/igs/v1/measure/putInfoXc";

    //计划信息推送
    public static final String PUSH_PLAN_URL = "https://172.16.64.235:65522/app-transport/api/measure";


    @Autowired
    private IDGCBCommonService dgcbCommonService;

    @Autowired
    private CreateQrCodeDtoMapper createQrCodeDtoMapper;

    @Autowired
    private PassFHMapper passFHMapper;

    @Autowired
    private PassFhItemMapper passFhItemMapper;


    @Autowired
    private KqdbTaskMapper kqdbTaskMapper;

    @Autowired
    private KqdbItemMapper kqdbItemMapper;

    @Autowired
    private CcbfhTaskMapper ccbfhTaskMapper;

    @Autowired
    private CcbfhItemMapper ccbfhItemMapper;

    @Autowired
    private FgdbTaskMapper fgdbTaskMapper;

    @Autowired
    private CndbTaskMapper cndbTaskMapper;

    @Autowired
    private ForcastMapper forcastMapper;

    @Autowired
    private MeasureCommonMapper measureCommonMapper;

    @Autowired
    private ICMapper icMapper;

    @Autowired
    private ScrapSteelOrderMapper scrapSteelOrderMapper;

    @Autowired
    private TSJLInfoMapper tsjlInfoMapper;

    @Autowired
    private ILeaveTaskService leaveTaskService;


    @Override
    public String generateQrCode(GenerateQrCodeDto generateQrCodeDto) {
        String qrCode = "";
        try{
            //对二维码进行加密
            if(StringUtils.isBlank(generateQrCodeDto.getOrderId())){
                qrCode = dgcbCommonService.EncryptDES(generateQrCodeDto.getPlanId() + "|" + generateQrCodeDto.getCarNo());
            }else{
                qrCode = dgcbCommonService.EncryptDES(generateQrCodeDto.getPlanId() + "|" + generateQrCodeDto.getCarNo() + "|" + generateQrCodeDto.getOrderId());
            }

            AccessInfo accessInfo = new AccessInfo();
            BeanUtils.copyProperties(generateQrCodeDto, accessInfo);

            String param = RequestUtils.parseUrlParams(accessInfo);
            log.info(param);
            String url = "http://172.16.13.222:8080/asseapi/api/v1/exts/xccarsinfo";
            log.info("爱索白名单信息推送请求:" + param);
            String result = HttpUtils.sendPostToVehicle(url, param);
            log.info("爱索白名单信息推送结果:" + result);
        }catch (Exception e){
            e.printStackTrace();
            log.error("爱索白名单信息推送异常",e);
            throw new RuntimeException("推送爱索白名单失败");
        }
        return qrCode;
    }

    @Override
    public String createQrCode(CreateQrCodeDto createQrCodeDto) {
        String qrCode = "";
        try{
            //对二维码进行加密
            if(StringUtils.isBlank(createQrCodeDto.getOrderId())){
                qrCode = dgcbCommonService.EncryptDES(createQrCodeDto.getPlanId() + "|" + createQrCodeDto.getCarNo());
            }else{
                qrCode = dgcbCommonService.EncryptDES(createQrCodeDto.getPlanId() + "|" + createQrCodeDto.getCarNo() + "|" + createQrCodeDto.getOrderId());
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("计量二维码创建异常",e);
            throw new RuntimeException("计量二维码创建异常");
        }
        return qrCode;
    }

    @Override
    public void notify(MeasureNotifyDto measureNotifyDto) {
        try{
            switch (measureNotifyDto.getOperaType()){
                case -1://无业务计量皮重
                    log.info("业务类型：无业务计量皮重");
                    break;
                case 0://厂内调拨
                    log.info("业务类型：厂内调拨");
                    notifyLogistics(measureNotifyDto);
                    break;
                case 1://废钢船运
                    log.info("业务类型：废钢船运");
                    notifyGrade(measureNotifyDto);
                    break;
                case 2://废钢汽运
                    log.info("业务类型：废钢汽运");
                    notifyGrade(measureNotifyDto);
                    syncToScrapSteel(measureNotifyDto);
                    break;
                case 3://船运其他采购
                    log.info("业务类型：船运其他采购");
                    break;
                case 4://销售退货
                    log.info("业务类型：销售退货");
                    break;
                case 6://跨区调拨
                    log.info("业务类型：跨区调拨");
                    notifyLogistics(measureNotifyDto);
                    break;
                case 7://船运销售
                    log.info("业务类型：船运销售");
                    break;
                case 8://汽运销售
                    log.info("业务类型：汽运销售");
                    break;
                case 9://汽运其他采购
                    log.info("业务类型：汽运其他采购");
                    break;
                case 11://委外加工出厂
                    log.info("业务类型：委外加工出厂");
                    notifyLogistics(measureNotifyDto);
                    break;
                case 12://出厂不返回
                    log.info("业务类型：出厂不返回");
                    notifyLogistics(measureNotifyDto);
                    break;
                case 13://委外加工进厂
                    log.info("业务类型：委外加工进厂");
                    notifyLogistics(measureNotifyDto);
                    break;
                case 15://委外废料返回
                    log.info("业务类型：委外废料返回");
                    notifyLogistics(measureNotifyDto);
                    break;
                case 20://源头治超
                    log.info("业务类型：源头治超");
                    return;
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("计量称重信息推送异常",e);
        }
    }


    private void notifyLogistics(MeasureNotifyDto measureNotifyDto){
        try {
            //1.推送消息到智慧物流
            Header[] headers = {new BasicHeader("appKey", "nx23102000"),
                    new BasicHeader("appSecret", "zWhDnVjPsWYH3ZxVEjG8")};
            log.info("智慧物流计量信息推送请求："+FastJsonUtils.toJSON(measureNotifyDto));
            String result = HttpUtil.doPost(NOTIFY_URL, headers, FastJsonUtils.toJSON(measureNotifyDto));
            log.info("智慧物流计量信息推送结果："+result);
        }catch (Exception e){
            e.printStackTrace();
            log.error("智慧物流计量信息推送异常",e);
        }
    }

    private void notifyGrade(MeasureNotifyDto measureNotifyDto){
        CompletableFuture.runAsync(() -> {
            try{
                String param = FastJsonUtils.toJSON(measureNotifyDto);
                log.info("废钢智能判级计量信息推送请求："+FastJsonUtils.toJSON(measureNotifyDto));
                String result = HttpUtil.doPost(NOTIFYFG_URL,param);
                log.info("废钢智能判级计量信息推送结果："+result);
            }
            catch (Exception e){
                e.printStackTrace();
                log.error("废钢智能判级计量信息推送异常",e);
            }
        });
    }

    private void syncToScrapSteel(MeasureNotifyDto measureNotifyDto){
        CompletableFuture.runAsync(() -> {
            try{
                ScrapSteelOrder measureIndex = new ScrapSteelOrder();
                measureIndex.setCarNo(measureNotifyDto.getCarNo());



                //获取毛重
                if(StringUtils.isNotBlank(measureNotifyDto.getGrossTime())){
                    measureIndex.setGrossTime(DateUtils.parseDate(measureNotifyDto.getGrossTime(),"yyyy-MM-dd HH:mm:ss"));
                    ScrapSteelOrder scrapSteelOrder = scrapSteelOrderMapper.selectScrapSteelOrderByTime(measureIndex);
                    if(scrapSteelOrder == null){
                        log.info("废钢未查询到计量车号对应的订单：",measureNotifyDto);
                    } else {
                        if (scrapSteelOrder.getGross() == null){
                            scrapSteelOrder.setGross(measureNotifyDto.getGross());
                            scrapSteelOrder.setGrossTime(measureIndex.getGrossTime());
                            SpringUtils.getBean(ScrapSteelOrderServiceImpl.class).updateScrapSteelOrder(scrapSteelOrder);

                            //插入日志
                            ScrapSteelLog truckLog = new ScrapSteelLog();
                            truckLog.setTicketNo(scrapSteelOrder.getTicketNo());
                            truckLog.setInfo("预约单号为\"" + scrapSteelOrder.getTicketNo() +"的车辆\"" + scrapSteelOrder.getCarNo() + "\"同步计量信息成功" + "，毛重：" + scrapSteelOrder.getGross() + "kg");
//                    truckLog.setCreateBy(scrapSteelOrder.getCreateBy());
                            truckLog.setCreateTime(scrapSteelOrder.getGrossTime());
                            //truckLogMapper.insertTruckLog(truckLog);
                            SpringUtils.getBean(ScrapSteelLogMapper.class).insertScrapSteelLog(truckLog);
                        }
                    }

                }
                if(StringUtils.isNotBlank(measureNotifyDto.getTareTime())){
                    measureIndex.setTareTime(DateUtils.parseDate(measureNotifyDto.getTareTime(),"yyyy-MM-dd HH:mm:ss"));
                    ScrapSteelOrder scrapSteelOrder = scrapSteelOrderMapper.selectScrapSteelOrderByTime(measureIndex);
                    //获取皮重
                    if(scrapSteelOrder == null){
                        log.info("废钢未查询到计量车号对应的订单：",measureNotifyDto);
                    }else{
                        if (scrapSteelOrder.getTare() == null){
                            scrapSteelOrder.setTare(measureNotifyDto.getTare());
                            scrapSteelOrder.setTareTime(measureIndex.getTareTime());
                            //若毛重存在，则计算净重
                            if(scrapSteelOrder.getGross() != null){
                                scrapSteelOrder.setNetWeight(scrapSteelOrder.getGross().subtract(scrapSteelOrder.getTare()));
                            }
                            SpringUtils.getBean(ScrapSteelOrderServiceImpl.class).updateScrapSteelOrder(scrapSteelOrder);
                            //插入日志
                            ScrapSteelLog truckLog = new ScrapSteelLog();
                            truckLog.setTicketNo(scrapSteelOrder.getTicketNo());
                            if (scrapSteelOrder.getNetWeight() != null){
                                truckLog.setInfo("预约单号为\"" + scrapSteelOrder.getTicketNo() +"的车辆\"" + scrapSteelOrder.getCarNo() + "\"同步计量信息成功" + "，皮重：" + scrapSteelOrder.getTare() + "kg，净重：" + scrapSteelOrder.getNetWeight() + "kg");
                            }else{
                                truckLog.setInfo("预约单号为\"" + scrapSteelOrder.getTicketNo() +"的车辆\"" + scrapSteelOrder.getCarNo() + "\"同步计量信息成功" + "，皮重：" + scrapSteelOrder.getTare() + "kg");
                            }
//                    truckLog.setCreateBy(scrapSteelOrder.getCreateBy());
                            truckLog.setCreateTime(scrapSteelOrder.getTareTime());
                            //truckLogMapper.insertTruckLog(truckLog);
                            SpringUtils.getBean(ScrapSteelLogMapper.class).insertScrapSteelLog(truckLog);
                        }
                    }

                }
            }
            catch (Exception e){
                e.printStackTrace();
                log.error("废钢同步计量信息推送异常",e);
            }
        });
    }

    private void syncToLeaveTask(MeasureNotifyDto measureNotifyDto) {
        CompletableFuture.runAsync(() -> {
            try {
                com.ruoyi.app.leave.dto.MeasureNotifyDto param = new com.ruoyi.app.leave.dto.MeasureNotifyDto();
                BeanUtils.copyProperties(measureNotifyDto, com.ruoyi.app.leave.dto.MeasureNotifyDto.class);
                leaveTaskService.listenMeasurePushInfo(param);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("出门证同步计量信息推送异常", e);
            }
        });
    }

    /**
     * 推送计划信息
     * @param planTask
     */
//    public void pushPlanInfo2(PlanTask planTask){
//        try {
//            //1.推送计划信息到智慧物流
//            Header[] headers = {new BasicHeader("appKey", "nx23102000"),
//                    new BasicHeader("appSecret  ", "zWhDnVjPsWYH3ZxVEjG8")};
//            log.info("智慧物流计划信息推送请求："+FastJsonUtils.toJSON(Lists.newArrayList(planTask)));
//            //测试编码问题
//            String sourceName = planTask.getSourceName();
//            String receiveName = planTask.getReceiveName();
//            String targetName = planTask.getTargetName();
//
//            String gbkData1 = new String(sourceName.getBytes("UTF-8"), "GBK");
//            log.info("智慧物流计划信息推送请求->测试编码,字段sourceName,utf-GBK:"+gbkData1);
//            String gbkData2 = new String(receiveName.getBytes("UTF-8"), "GBK");
//            log.info("智慧物流计划信息推送请求->测试编码,字段receiveName,utf-GBK:"+gbkData2);
//            String gbkData3 = new String(targetName.getBytes("UTF-8"), "GBK");
//            log.info("智慧物流计划信息推送请求->测试编码,字段targetName,utf-GBK:"+gbkData3);
//
//            String gbkData4 = new String(sourceName.getBytes("UTF-8"));
//            log.info("智慧物流计划信息推送请求->测试编码,字段sourceName,utf:"+gbkData4);
//            String gbkData5 = new String(receiveName.getBytes("UTF-8"));
//            log.info("智慧物流计划信息推送请求->测试编码,字段receiveName,utf:"+gbkData5);
//            String gbkData6 = new String(targetName.getBytes("UTF-8"));
//            log.info("智慧物流计划信息推送请求->测试编码,字段targetName,utf:"+gbkData6);
//
//            String gbkData7 = new String(sourceName.getBytes("GBK"));
//            log.info("智慧物流计划信息推送请求->测试编码,字段sourceName,GBK:"+gbkData7);
//            String gbkData8 = new String(receiveName.getBytes("GBK"));
//            log.info("智慧物流计划信息推送请求->测试编码,字段receiveName,GBK:"+gbkData8);
//            String gbkData9 = new String(targetName.getBytes("GBK"));
//            log.info("智慧物流计划信息推送请求->测试编码,字段targetName,GBK:"+gbkData9);
//
//            String result = HttpUtil.doPost(PUSH_PLAN_URL, headers, FastJsonUtils.toJSON(planTask));
//            log.info("智慧物流计划信息推送结果："+result);
//        }catch (Exception e){
//            e.printStackTrace();
//            log.error("智慧物流计划信息推送异常",e);
//        }
//    }

    @Override
    public void pushPlanInfo(PlanTask planTask){
        log.info("智慧物流计划信息推送请求："+FastJsonUtils.toJSON(Lists.newArrayList(planTask)));
        OperateTypeEnum operateTypeEnum = OperateTypeEnum.getByCode(planTask.getOperaType());
        try {
            if(!StringUtils.isBlank(planTask.getCarNo())){
                String carNo = URLDecoder.decode(planTask.getCarNo(),"UTF-8");
                planTask.setCarNo(carNo);
            }
//            if((planTask.getBusinessType()==5 && planTask.getOperaType() != 30 )|| planTask.getBusinessType() ==6){
//                if(StringUtils.isBlank(planTask.getMatchId()) || StringUtils.isBlank(planTask.getCarNo())){
//                    log.error("智慧物流计划信息推送异常：matchId或车号为空，计划信息："+planTask.getBusinessId()+"|"+planTask.getOperaType() + "|" + planTask.getBusinessType() +"|" +planTask.getCarNo() +"|" +planTask.getMatchId());
//                    return;
//                }
//
//                if(StringUtils.isBlank(planTask.getCarNo())){
//                    String matchId = planTask.getMatchId();
//                    String carNo = "";
//                    if(planTask.getBusinessType()==6){
//                        carNo = measureCommonMapper.getCarNoByMatchId(matchId);
//                    }else {
//                        carNo = icMapper.getCarNoByMatchId(matchId);
//                    }
//                    if(StringUtils.isBlank(carNo)){
//                        log.error("智慧物流计划信息推送异常:matchId值为"+matchId);
//                        return;
//                    }else {
//                        planTask.setCarNo(carNo);
//                    }
//                }
//            }
            switch(operateTypeEnum){
                case CNDB:
                    this.handleCndbTask(planTask);
                    break;
                case CNDBFG:
                    this.handleFgdbTask(planTask);
                    break;
                case PASS_FH:
                    this.handlePassFhData(planTask);
                    break;
                case KQDB:
                    this.handleKqdbTask(planTask);
                    break;
                case CC_BFH:
                    this.handleCcbfhTask(planTask);
                    break;
                case WWFL_FH:
                    this.handleForcastTask(planTask);
                    break;
                default:
                    log.error("智慧物流计划信息推送异常:operateType值为"+planTask.getOperaType());
                    return;
            }

            //1.推送计划信息到智慧物流
            Header[] headers = {new BasicHeader("appKey", "nx23102000"),
                    new BasicHeader("appSecret", "zWhDnVjPsWYH3ZxVEjG8")};
            log.info("智慧物流计划信息推送请求(处理后)："+FastJsonUtils.toJSON(Lists.newArrayList(planTask)));
//            String result = HttpUtil.doPost(PUSH_PLAN_URL, headers, FastJsonUtils.toJSON(Lists.newArrayList(planTask)));
            String result = HttpUtil.doPostNoSSLHeader(PUSH_PLAN_URL, FastJsonUtils.toJSON(Lists.newArrayList(planTask)), headers);
            log.info("智慧物流计划信息推送结果："+result);
        }catch (Exception e){
            e.printStackTrace();
            log.error("智慧物流计划信息推送异常",e);
        }
    }

    @Override
    public List<CreateQrCodeDto> getQRCodeByCreatManId(CreateQrCodeDto createQrCodeDto) {
        return createQrCodeDtoMapper.getQRCodeByCreatManId(createQrCodeDto);
    }

    //处理委外加工出门返厂类型的数据
    private void handlePassFhData(PlanTask planTask){
        //获取主键
        Integer id = planTask.getBusinessId();
        //获取主表
        PassFh passFH = passFHMapper.getPassFhById(id);
        planTask.setPlanId(passFH.getCmzhao());
        planTask.setOperaType(OperateTypeEnum.PASS_FH.getCode());
        planTask.setMeasureFlag(passFH.getMeasureFlag());
        planTask.setValidFlag(passFH.getValidFlag());
        planTask.setSourceCode(passFH.getSourceCode());
        planTask.setSourceName(passFH.getSourceName());
        planTask.setReceiveCode(passFH.getReceiveCode());
        planTask.setReceiveName(passFH.getReceiveName());
        planTask.setTargetCode(passFH.getTargetCode());
        planTask.setTargetName(passFH.getTargetName());
        planTask.setMaterialName(passFH.getMaterialName());
        planTask.setMaterialSpec(passFH.getMaterialSpec());
        planTask.setCount(planTask.getCount());
        planTask.setMemo(passFH.getMemo());
        planTask.setHeatNo(passFH.getHeatNo());
        planTask.setSteelGrade(passFH.getSteelGrade());
        planTask.setValidTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, passFH.getValidTime()));
        planTask.setPlanReturnDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, passFH.getPlanReturnDate()));
        //获取物资详情
        List<MaterialItem> passFhItemList = passFhItemMapper.getPassFHItems(id);
        List<PlanItem> planItemList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(passFhItemList)){
            passFhItemList.forEach(x->{
                PlanItem planItem = new PlanItem();
                planItem.setId(x.getId());
                planItem.setMaterialName(x.getMaterialName());
                planItem.setMaterialSpec(x.getMaterialSpec());
                planItem.setUnit(x.getUnit());
                if(StringUtils.isBlank(x.getCount())){
                    planItem.setCount(BigDecimal.ZERO);
                }else{
                    planItem.setCount(new BigDecimal(x.getCount()));
                }
                planItem.setFpFlag(x.getFpFlag());
                if(Objects.nonNull(x.getOutDate())){
                    planItem.setOutDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, x.getOutDate()));
                }
                if(Objects.nonNull(x.getInDate())){
                    planItem.setInDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, x.getInDate()));
                }
                if(Objects.nonNull(x.getShDate())){
                    planItem.setShDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, x.getShDate()));
                }
                planItem.setOutDoor(x.getOutDoor());
                planItem.setInDoor(x.getInDoor());
                planItem.setReceiver(x.getShren());
                planItem.setMwshCount(x.getMwshCount());
                planItem.setMwshCcountEd(x.getMwshCountEd());

                planItemList.add(planItem);
            });
            planTask.setPlanItemList(planItemList);
        }
    }


    //跨区调拨
    private void handleKqdbTask(PlanTask planTask){
        //获取主键
        Integer id = planTask.getBusinessId();
        KqdbTask kqdbTask = kqdbTaskMapper.getKqdbTaskById(id);
        planTask.setPlanId(kqdbTask.getPlanId());
        planTask.setOperaType(OperateTypeEnum.KQDB.getCode());
        planTask.setMeasureFlag(kqdbTask.getMeasureFlag());
        planTask.setValidFlag(kqdbTask.getValidFlag());
        planTask.setSourceCode(kqdbTask.getStoreCode());
        planTask.setSourceName(kqdbTask.getStoreName());
        planTask.setTargetCode(kqdbTask.getTargetCode());
        planTask.setTargetName(kqdbTask.getTargetName());
        planTask.setMaterialName(kqdbTask.getMaterialName());
        planTask.setMaterialSpec(kqdbTask.getMaterialSpec());
        planTask.setCount(kqdbTask.getCount());
        planTask.setPlanAmount(kqdbTask.getPlanAmount());
        planTask.setHeatNo(kqdbTask.getHeatNo());
        planTask.setSteelGrade(kqdbTask.getSteelGrade());
        planTask.setMatNo(kqdbTask.getMatNo());

        if(Objects.nonNull(kqdbTask.getBeginTime())){
            planTask.setBeginTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,kqdbTask.getBeginTime()));
        }
        if(Objects.nonNull(kqdbTask.getEndTime())){
            planTask.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,kqdbTask.getEndTime()));
        }
        if(Objects.nonNull(kqdbTask.getValidTime())){
            planTask.setValidTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,kqdbTask.getValidTime()));
        }


        List<MaterialItem> kqdbItemList =  kqdbItemMapper.getKqdbItemByPid(id);
        List<PlanItem> planItemList = new ArrayList<>();
        if(kqdbItemList.size()>0){
            for (MaterialItem item:kqdbItemList) {
                PlanItem planItem = new PlanItem();
                planItem.setId(item.getId());
                planItem.setMaterialName(item.getMaterialName());
                planItem.setMaterialSpec(item.getMaterialSpec());
                planItem.setUnit(item.getUnit());
                if(StringUtils.isBlank(item.getCount())){
                    planItem.setCount(BigDecimal.ZERO);
                }else{
                    planItem.setCount(new BigDecimal(item.getCount()));
                }
                planItemList.add(planItem);
            }
        }
        planTask.setPlanItemList(planItemList);
    }

    //出厂不返回
    private void handleCcbfhTask(PlanTask planTask){
        Integer id = planTask.getBusinessId();
        CcbfhTask ccbfhTask = ccbfhTaskMapper.getCcbfhTaskById(id);
        planTask.setPlanId(ccbfhTask.getCmzhao());
        planTask.setOperaType(OperateTypeEnum.CC_BFH.getCode());
        planTask.setMeasureFlag(ccbfhTask.getMeasureFlag());
        planTask.setValidFlag(ccbfhTask.getValidFlag());
        planTask.setSourceCode(ccbfhTask.getSourceCode());
        planTask.setSourceName(ccbfhTask.getSourceName());
        planTask.setTargetCode(ccbfhTask.getTargetCode());
        planTask.setTargetName(ccbfhTask.getTargetName());
        planTask.setMemo(ccbfhTask.getMemo());
        planTask.setOutMemo(ccbfhTask.getOutMemo());
        planTask.setMaterialName(ccbfhTask.getMaterialName());
        planTask.setMaterialSpec(ccbfhTask.getMaterialSpec());
        planTask.setCount(ccbfhTask.getCount());
        if(Objects.nonNull(ccbfhTask.getValidTime())){
            planTask.setValidTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,ccbfhTask.getValidTime()));
        }


        List<MaterialItem> ccbfhItemList =  ccbfhItemMapper.getCcbfhItemByPid(id);
        List<PlanItem> planItemList = new ArrayList<>();
        if(ccbfhItemList.size()>0){
            for (MaterialItem item:ccbfhItemList) {
                PlanItem planItem = new PlanItem();
                planItem.setId(item.getId());
                planItem.setMaterialName(item.getMaterialName());
                planItem.setMaterialSpec(item.getMaterialSpec());
                planItem.setUnit(item.getUnit());
                if(StringUtils.isBlank(item.getCount())){
                    planItem.setCount(BigDecimal.ZERO);
                }else{
                    planItem.setCount(new BigDecimal(item.getCount()));
                }
                planItemList.add(planItem);
            }
        }
        planTask.setPlanItemList(planItemList);
    }




    //废钢调拨
    private void handleFgdbTask(PlanTask planTask){
        Integer id = planTask.getBusinessId();
        FgdbTask fgdbTask = fgdbTaskMapper.getFgdbTaskById(id);
        planTask.setPlanId(fgdbTask.getPlanId());
        planTask.setOperaType(OperateTypeEnum.CNDBFG.getCode());
        planTask.setValidFlag(fgdbTask.getValidFlag());
        planTask.setSourceCode(fgdbTask.getStoreCode());
        planTask.setSourceName(fgdbTask.getStoreName());
        planTask.setTargetCode(fgdbTask.getTargetCode());
        planTask.setTargetName(fgdbTask.getTargetName());
        planTask.setMaterialName(fgdbTask.getMaterialName());
        planTask.setPlanAmount(fgdbTask.getPlanAmount());
        if(Objects.nonNull(fgdbTask.getBeginTime())){
            planTask.setBeginTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,fgdbTask.getBeginTime()));
        }
        if(Objects.nonNull(fgdbTask.getEndTime())){
            planTask.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,fgdbTask.getEndTime()));
        }

    }

    //厂内调拨
    private void handleCndbTask(PlanTask planTask){
        Integer id = planTask.getBusinessId();
        CndbTask cndbTask = cndbTaskMapper.getCndbTaskById(id);
        planTask.setPlanId(cndbTask.getTaskCode());
        planTask.setOperaType(OperateTypeEnum.CNDB.getCode());
        planTask.setValidFlag(cndbTask.getValidFlag());
        planTask.setSourceCode(cndbTask.getSourceCode());
        planTask.setSourceName(cndbTask.getSourceName());
        planTask.setTargetCode(cndbTask.getTargetCode());
        planTask.setTargetName(cndbTask.getTargetName());
        planTask.setMaterialName(cndbTask.getMaterialName());
        planTask.setMeasureFlag(Integer.parseInt(cndbTask.getMFlag()));
        planTask.setsFlag(cndbTask.getSFlag());
    }


    private void handleForcastTask(PlanTask planTask){
        Integer id = planTask.getBusinessId();
        Forcast forcast = forcastMapper.getForcastById(id);
        planTask.setPlanId(forcast.getPlanId());
        planTask.setOperaType(OperateTypeEnum.WWFL_FH.getCode());
        planTask.setValidFlag(forcast.getValidFlag());
        planTask.setSourceCode(forcast.getSourceCode());
        planTask.setSourceName(forcast.getSourceName());
        planTask.setTargetCode(forcast.getTargetCode());
        planTask.setTargetName(forcast.getTargetName());
        planTask.setMaterialName(forcast.getMaterialName());
        planTask.setMaterialSpec(forcast.getMaterialSpec());
        if(Objects.nonNull(forcast.getBeginTime())){
            planTask.setBeginTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,forcast.getBeginTime()));
        }
        if(Objects.nonNull(forcast.getEndTime())){
            planTask.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,forcast.getEndTime()));
        }
    }


    @Override
    public List<TSJLInfo> getHongZhaInfoUnRead(String ticketNo) {
        List<TSJLInfo> resultList = tsjlInfoMapper.getHongZhaInfoUnRead(ticketNo);
        for(TSJLInfo info : resultList){
            tsjlInfoMapper.updateRead(info.getTicketNo());
        }
        return resultList;
    }
}

