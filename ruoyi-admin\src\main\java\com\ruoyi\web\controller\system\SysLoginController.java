package com.ruoyi.web.controller.system;

import java.io.IOException;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.fs.FeishuAuthUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.sign.AesUtils;
import com.ruoyi.common.utils.sign.RsaUtils;
import com.ruoyi.common.utils.sm4.SM4Utils;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysMenuService;
import sun.misc.BASE64Decoder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysUserServiceImpl sysUserService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) throws Exception {
        loginBody.setUsername(SM4Utils.decryptData_CBC(loginBody.getUsername()));
        loginBody.setPassword(SM4Utils.decryptData_CBC(loginBody.getPassword()));
        SysUser sysUser = sysUserService.selectUserByUserName(loginBody.getUsername());
        //移动应用系统用户名是否为空
        if (StringUtils.isNull(sysUser)) {
            return AjaxResult.error("用户名不存在");
        } else {
            //是否为产销用户
//            if (StringUtils.startsWith(loginBody.getUsername(), "X")) {
//                // 产销密码校验
//                String result = sysUserService.loginInMes(loginBody.getUsername(), loginBody.getPassword());
//                if(result.equals("PASS")){
//                    // 密码重置为产销密码
//                    sysUserService.resetUserPwd(loginBody.getUsername(), SecurityUtils.encryptPassword(loginBody.getPassword()));
//                }
//            }
            AjaxResult ajax = AjaxResult.success();
            // 生成令牌
            String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                    loginBody.getUuid());
            ajax.put(Constants.TOKEN, token);
            return ajax;
        }
    }

    @PostMapping("/uniLogin")
    public AjaxResult uniLogin(@RequestBody LoginBody loginBody) throws Exception {
        loginBody.setUsername(RsaUtils.decryptByPrivateKey(loginBody.getUsername()));
        loginBody.setPassword(RsaUtils.decryptByPrivateKey(loginBody.getPassword()));
        SysUser sysUser = sysUserService.selectUserByUserName(loginBody.getUsername());
        //移动应用系统用户名是否为空
        if (StringUtils.isNull(sysUser)) {
            return AjaxResult.error("用户名不存在");
        } else {
            //是否为产销用户
//            if (StringUtils.startsWith(loginBody.getUsername(), "X")) {
//                // 产销密码校验
//                String result = sysUserService.loginInMes(loginBody.getUsername(), loginBody.getPassword());
//                if(result.equals("PASS")){
//                    // 密码重置为产销密码
//                    sysUserService.resetUserPwd(loginBody.getUsername(), SecurityUtils.encryptPassword(loginBody.getPassword()));
//                }
//            }
            AjaxResult ajax = AjaxResult.success();
            // 生成令牌
            String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                    loginBody.getUuid());
            ajax.put(Constants.TOKEN, token);
            return ajax;
        }
    }


    @PostMapping("/fsLogin")
    public AjaxResult fsLogin(@RequestBody LoginBody loginBody) throws Exception {
        Map<String, Object> userInfo = FeishuAuthUtil.getUserInfo(loginBody.getCode());
        if(Objects.nonNull(userInfo)&&Objects.nonNull(userInfo.get("user_id"))){
            SysUser user = sysUserService.selectUserByLikeUserName(userInfo.get("user_id").toString());
            //移动应用系统用户名是否为空
                if(Objects.isNull(user)){
                    return AjaxResult.error("未查询到用户信息");
                }
                // 生成token
                String token = loginService.codeLogin(user);
                AjaxResult ajax = AjaxResult.success();
                ajax.put(Constants.TOKEN,token);
                return ajax;

        }
        else{
            return AjaxResult.error("飞书中用户不存在");
        }

    }


    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/loginWithoutCaptcha")
    public AjaxResult loginWithoutCaptcha(@RequestBody LoginBody loginBody) throws IOException {
        SysUser sysUser = sysUserService.selectUserByUserName(loginBody.getUsername());
        //移动应用系统用户名是否为空
        if (StringUtils.isNull(sysUser)) {
            return AjaxResult.error("用户名不存在");
        } else {
            //是否为产销用户
//            if (StringUtils.startsWith(loginBody.getUsername(), "X")) {
//                // 产销密码校验
//                String result = sysUserService.loginInMes(loginBody.getUsername(), loginBody.getPassword());
//                if(result.equals("PASS")){
//                    // 密码重置为产销密码
//                    sysUserService.resetUserPwd(loginBody.getUsername(), SecurityUtils.encryptPassword(loginBody.getPassword()));
//                }
//            }
            AjaxResult ajax = AjaxResult.success();
            // 生成令牌
            String token = loginService.loginWithoutCaptcha(loginBody.getUsername(), loginBody.getPassword(),
                    loginBody.getUuid());
            ajax.put(Constants.TOKEN, token);
            return ajax;
        }
    }

    private final static String loginUrl = "https://usercenter.citicsteel.com/WebAPIV46/API/LogOn/LogOnByAuthorizationCode";

    private final static String systemCode = "Base";

    private final static String securityKey = "It.LianGangTie.Com";

    /**
     * 统一认证接口
     *
     * @param code 登录信息
     * @return 结果
     */
    @PostMapping("/loginByCode")
    public AjaxResult loginByCode(HttpServletRequest request, HttpServletResponse response, String code) throws IOException {
        String function = "LogOnByAuthorizationCode";
        String ipaddress = request.getRemoteAddr();
        String getRoles = "false";
        String getModules = "false";
        StringBuilder url = new StringBuilder();
        url.append(loginUrl)
                .append("?systemCode=")
                .append(systemCode)
                .append("&securityKey=")
                .append(securityKey)
                .append("&code=")
                .append(code)
                .append("&function=")
                .append(function)
                .append("&ipaddress=")
                .append(ipaddress)
                .append("&getRoles=")
                .append(getRoles)
                .append("&getModules=")
                .append(getModules);
        String rspStr = HttpUtils.sendSSLPost(url.toString(), null);
        JSONObject obj = JSON.parseObject(rspStr);
        if(!obj.getString("StatusCode").equals("OK")){
            return AjaxResult.error("单点登录获取用户信息失败");
        }
        String userName = obj.getJSONObject("UserInfo").getString("UserName");

        if(userName.substring(0,1).equals("x")||userName.substring(0,1).equals("q")||userName.substring(0,1).equals("g")||userName.substring(0,1).equals("y"))userName = userName.substring(1);
        SysUser user = sysUserService.selectUserByLikeUserName(userName);
        if(Objects.isNull(user)){
            return AjaxResult.error("未查询到用户信息");
        }
        if(!user.getStatus().equals("0")){
            return AjaxResult.error("无网页权限");
        }
        // 生成token
        String token = loginService.codeLogin(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put(Constants.TOKEN,token);
        return ajax;


    }
    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        SysUser sysUser = sysUserService.selectUserByUserName(loginUser.getUsername());
        user.setRsDeptCode(sysUser.getRsDeptCode());
        user.setRsDeptName(sysUser.getRsDeptName());
        user.setNickName(sysUser.getNickName());
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 用户信息
        SysUser user = loginUser.getUser();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(user.getUserId());
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
