package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 外委加工出库对象 l_storeout_wwjg_t
 * 
 * <AUTHOR>
 */
public class StoreoutWwjgMeasure extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 有效标志 */
    @Excel(name = "有效标志")
    private Long validflag;

    /** 匹配ID */
    @Excel(name = "匹配ID")
    private String matchid;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carno;

    /** IC卡号 */
    @Excel(name = "IC卡号")
    private String icno;

    /** 操作类型 */
    @Excel(name = "操作类型")
    private String operatype;

    /** 计划ID */
    @Excel(name = "计划ID")
    private String planid;

    /** 来源编码 */
    @Excel(name = "来源编码")
    private String sourcecode;

    /** 来源名称 */
    @Excel(name = "来源名称")
    private String sourcename;

    /** 库位 */
    @Excel(name = "库位")
    private String storepos;

    /** 目标编码 */
    @Excel(name = "目标编码")
    private String targetcode;

    /** 目标名称 */
    @Excel(name = "目标名称")
    private String targetname;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialcode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialname;

    /** 物料规格编码 */
    @Excel(name = "物料规格编码")
    private String materialspeccode;

    /** 物料规格 */
    @Excel(name = "物料规格")
    private String materialspec;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 数量 */
    @Excel(name = "数量")
    private Long counts;

    /** 炉号 */
    @Excel(name = "炉号")
    private String heatno;

    /** 钢级 */
    @Excel(name = "钢级")
    private String steelevel;

    /** 钢种 */
    @Excel(name = "钢种")
    private String steelgrade;

    /** 运输方式 */
    @Excel(name = "运输方式")
    private String transitway;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createman;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdate;

    /** 更新日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedate;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updateman;

    /** 物料编号 */
    @Excel(name = "物料编号")
    private String matno;

    /** 规格 */
    @Excel(name = "规格")
    private String spec;

    /** 长度 */
    @Excel(name = "长度")
    private String lengcd;

    /** 加工方式 */
    @Excel(name = "加工方式")
    private String jgstyle;

    /** 物料编号1 */
    @Excel(name = "物料编号1")
    private String matno1;

    /** 物料编号2 */
    @Excel(name = "物料编号2")
    private String matno2;

    /** 物料编号3 */
    @Excel(name = "物料编号3")
    private String matno3;

    /** 规格1 */
    @Excel(name = "规格1")
    private String spec1;

    /** 规格2 */
    @Excel(name = "规格2")
    private String spec2;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setValidflag(Long validflag) 
    {
        this.validflag = validflag;
    }

    public Long getValidflag() 
    {
        return validflag;
    }

    public void setMatchid(String matchid) 
    {
        this.matchid = matchid;
    }

    public String getMatchid() 
    {
        return matchid;
    }

    public void setCarno(String carno) 
    {
        this.carno = carno;
    }

    public String getCarno() 
    {
        return carno;
    }

    public void setIcno(String icno) 
    {
        this.icno = icno;
    }

    public String getIcno() 
    {
        return icno;
    }

    public void setOperatype(String operatype) 
    {
        this.operatype = operatype;
    }

    public String getOperatype() 
    {
        return operatype;
    }

    public void setPlanid(String planid) 
    {
        this.planid = planid;
    }

    public String getPlanid() 
    {
        return planid;
    }

    public void setSourcecode(String sourcecode) 
    {
        this.sourcecode = sourcecode;
    }

    public String getSourcecode() 
    {
        return sourcecode;
    }

    public void setSourcename(String sourcename) 
    {
        this.sourcename = sourcename;
    }

    public String getSourcename() 
    {
        return sourcename;
    }

    public void setStorepos(String storepos) 
    {
        this.storepos = storepos;
    }

    public String getStorepos() 
    {
        return storepos;
    }

    public void setTargetcode(String targetcode) 
    {
        this.targetcode = targetcode;
    }

    public String getTargetcode() 
    {
        return targetcode;
    }

    public void setTargetname(String targetname) 
    {
        this.targetname = targetname;
    }

    public String getTargetname() 
    {
        return targetname;
    }

    public void setMaterialcode(String materialcode) 
    {
        this.materialcode = materialcode;
    }

    public String getMaterialcode() 
    {
        return materialcode;
    }

    public void setMaterialname(String materialname) 
    {
        this.materialname = materialname;
    }

    public String getMaterialname() 
    {
        return materialname;
    }

    public void setMaterialspeccode(String materialspeccode) 
    {
        this.materialspeccode = materialspeccode;
    }

    public String getMaterialspeccode() 
    {
        return materialspeccode;
    }

    public void setMaterialspec(String materialspec) 
    {
        this.materialspec = materialspec;
    }

    public String getMaterialspec() 
    {
        return materialspec;
    }

    public void setWeight(BigDecimal weight) 
    {
        this.weight = weight;
    }

    public BigDecimal getWeight() 
    {
        return weight;
    }

    public void setCounts(Long counts) 
    {
        this.counts = counts;
    }

    public Long getCounts() 
    {
        return counts;
    }

    public void setHeatno(String heatno) 
    {
        this.heatno = heatno;
    }

    public String getHeatno() 
    {
        return heatno;
    }

    public void setSteelevel(String steelevel) 
    {
        this.steelevel = steelevel;
    }

    public String getSteelevel() 
    {
        return steelevel;
    }

    public void setSteelgrade(String steelgrade) 
    {
        this.steelgrade = steelgrade;
    }

    public String getSteelgrade() 
    {
        return steelgrade;
    }

    public void setTransitway(String transitway) 
    {
        this.transitway = transitway;
    }

    public String getTransitway() 
    {
        return transitway;
    }

    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }

    public void setCreateman(String createman) 
    {
        this.createman = createman;
    }

    public String getCreateman() 
    {
        return createman;
    }

    public void setCreatedate(Date createdate) 
    {
        this.createdate = createdate;
    }

    public Date getCreatedate() 
    {
        return createdate;
    }

    public void setUpdatedate(Date updatedate) 
    {
        this.updatedate = updatedate;
    }

    public Date getUpdatedate() 
    {
        return updatedate;
    }

    public void setUpdateman(String updateman) 
    {
        this.updateman = updateman;
    }

    public String getUpdateman() 
    {
        return updateman;
    }

    public void setMatno(String matno) 
    {
        this.matno = matno;
    }

    public String getMatno() 
    {
        return matno;
    }

    public void setSpec(String spec) 
    {
        this.spec = spec;
    }

    public String getSpec() 
    {
        return spec;
    }

    public void setLengcd(String lengcd) 
    {
        this.lengcd = lengcd;
    }

    public String getLengcd() 
    {
        return lengcd;
    }

    public void setJgstyle(String jgstyle) 
    {
        this.jgstyle = jgstyle;
    }

    public String getJgstyle() 
    {
        return jgstyle;
    }

    public void setMatno1(String matno1) 
    {
        this.matno1 = matno1;
    }

    public String getMatno1() 
    {
        return matno1;
    }

    public void setMatno2(String matno2) 
    {
        this.matno2 = matno2;
    }

    public String getMatno2() 
    {
        return matno2;
    }

    public void setMatno3(String matno3) 
    {
        this.matno3 = matno3;
    }

    public String getMatno3() 
    {
        return matno3;
    }

    public void setSpec1(String spec1) 
    {
        this.spec1 = spec1;
    }

    public String getSpec1() 
    {
        return spec1;
    }

    public void setSpec2(String spec2) 
    {
        this.spec2 = spec2;
    }

    public String getSpec2() 
    {
        return spec2;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("validflag", getValidflag())
            .append("matchid", getMatchid())
            .append("carno", getCarno())
            .append("icno", getIcno())
            .append("operatype", getOperatype())
            .append("planid", getPlanid())
            .append("sourcecode", getSourcecode())
            .append("sourcename", getSourcename())
            .append("storepos", getStorepos())
            .append("targetcode", getTargetcode())
            .append("targetname", getTargetname())
            .append("materialcode", getMaterialcode())
            .append("materialname", getMaterialname())
            .append("materialspeccode", getMaterialspeccode())
            .append("materialspec", getMaterialspec())
            .append("weight", getWeight())
            .append("counts", getCounts())
            .append("heatno", getHeatno())
            .append("steelevel", getSteelevel())
            .append("steelgrade", getSteelgrade())
            .append("transitway", getTransitway())
            .append("memo", getMemo())
            .append("createman", getCreateman())
            .append("createdate", getCreatedate())
            .append("updatedate", getUpdatedate())
            .append("updateman", getUpdateman())
            .append("matno", getMatno())
            .append("spec", getSpec())
            .append("lengcd", getLengcd())
            .append("jgstyle", getJgstyle())
            .append("matno1", getMatno1())
            .append("matno2", getMatno2())
            .append("matno3", getMatno3())
            .append("spec1", getSpec1())
            .append("spec2", getSpec2())
            .toString();
    }
} 