{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue?vue&type=style&index=0&id=630aa830&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue", "mtime": 1756170476797}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnRhYmxlLXN0cmlwZWR7CiAgbWFyZ2luLXRvcDogMTBweDsKICBtYXJnaW4tYm90dG9tOiAxMHB4OwogIHdpZHRoOiAxMDAlOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBib3JkZXI6IDFweCAjODg4OwogIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7Cn0KLnRhYmxlLXN0cmlwZWQgdGh7CiAgaGVpZ2h0OiAzMnB4OwogIGJvcmRlcjogMXB4IHNvbGlkICM4ODg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2RlZGVkZTsKfQoudGFibGUtc3RyaXBlZCB0ZHsKICBtaW4taGVpZ2h0OiAzMnB4OwogIGJvcmRlcjogMXB4IHNvbGlkICM4ODg7Cn0KLnRhYmxlLWlucHV0IC5lbC10ZXh0YXJlYV9faW5uZXJ7CiAgYm9yZGVyOiAwICFpbXBvcnRhbnQ7CiAgcmVzaXplOiBub25lICFpbXBvcnRhbnQ7Cn0KLnRhYmxlLWlucHV0IC5lbC1pbnB1dF9faW5uZXJ7CiAgYm9yZGVyOiAwICFpbXBvcnRhbnQ7Cn0KLyogLm15VXBsb2FkIC5lbC11cGxvYWQtLXBpY3R1cmUtY2FyZHsKICBkaXNwbGF5Om5vbmUgIWltcG9ydGFudDsgCn0gKi8K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/assess/self", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            :clearable=\"false\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\">\r\n            <el-option\r\n              v-for=\"item in deptOptions\"\r\n              :key=\"item.deptId\"\r\n              :label=\"item.deptName\"\r\n              :value=\"item.deptId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-s-tools\" size=\"mini\" @click=\"handleConfig\">指标配置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row>\r\n        <el-form-item label=\"当前状态\">\r\n          <el-tag v-if=\"status == '0' && rejectReason\" type=\"danger\">退 回</el-tag>\r\n          <el-tag v-if=\"status == '0' && !rejectReason\" type=\"info\">未提交</el-tag>\r\n          <el-tag v-if=\"status == '1'\" type=\"warning\">部门领导评分</el-tag>\r\n          <el-tag v-if=\"status == '2'\" type=\"warning\">事业部评分</el-tag>\r\n          <el-tag v-if=\"status == '3'\" type=\"warning\">运改部/组织部审核</el-tag>\r\n          <el-tag v-if=\"status == '4'\" type=\"warning\">总经理部评分</el-tag>\r\n          <el-tag v-if=\"status == '5'\" type=\"success\">已完成</el-tag>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row v-if=\"rejectReason\">\r\n        <el-form-item label=\"退回原因\" prop=\"rejectReason\">\r\n            <span class=\"el-icon-warning\" style=\"color: #f56c6c;\"></span>\r\n            {{ rejectReason }}\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n      <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n      <el-descriptions class=\"margin-top\" :column=\"3\">\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            姓名\r\n          </template>\r\n          {{ userInfo.name }}\r\n        </el-descriptions-item>\r\n        <!-- <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            工号\r\n          </template>\r\n          {{ userInfo.workNo }}\r\n        </el-descriptions-item> -->\r\n        <!-- <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            身份\r\n          </template>\r\n          <span v-if=\"userInfo.assessRole == '0'\">干部</span>\r\n          <span v-if=\"userInfo.assessRole == '1'\">一把手</span>\r\n          <span v-if=\"userInfo.assessRole == '2'\">条线领导</span>\r\n        </el-descriptions-item> -->\r\n\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            部门\r\n          </template>\r\n          {{ deptName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            考核年月\r\n          </template>\r\n          {{ assessDateText }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-table v-loading=\"loading\" :data=\"list\"\r\n        :span-method=\"objectSpanMethod\" border>\r\n        <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n        <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"140\"/>\r\n        <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"150\" />\r\n        <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n        <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.performance }}</span>\r\n                <div v-else style=\"display: flex\">\r\n                  <!-- <el-button icon=\"el-icon-search\" size=\"small\"></el-button> -->\r\n                  <el-popover\r\n                    placement=\"left\"\r\n                    width=\"636\"\r\n                    trigger=\"click\"\r\n                    :ref=\"'popover' + scope.$index\">\r\n                    <el-table :data=\"beAssessedList\">\r\n                      <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                      <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                      <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      <el-table-column width=\"80\" label=\"操作\">\r\n                        <template slot-scope=\"beAssessed\">\r\n                          <el-button\r\n                            size=\"mini\"\r\n                            type=\"text\"\r\n                            icon=\"el-icon-edit\"\r\n                            @click=\"handleBeAssessedClick(scope.row,beAssessed.row,scope.$index)\"\r\n                          >填入</el-button>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                    <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                  </el-popover>\r\n                  <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.performance\" placeholder=\"请输入完成实绩\" />\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"108\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.dePoints }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"number\" autosize v-model=\"scope.row.dePoints\" placeholder=\"请输入加减分\" @input=\"scoreInput(scope.row)\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\"  width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.pointsReason }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.pointsReason\" placeholder=\"请输入加减分原因\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" style=\"margin-top: 10px;\" label-position=\"left\">\r\n            <el-form-item v-if=\"readOnly\" label=\"自评分数 / 签名：\" prop=\"deptId\">\r\n              <div style=\"display: flex;\">\r\n                <span >{{ selfScore + \" 分 / \" }}</span>\r\n                <span v-if=\"!selfSign\">{{info.name}}</span>\r\n                <el-image v-else\r\n                  style=\"width: 100px; height: 46px\"\r\n                  :src=\"selfSign.url\"\r\n                  :fit=\"fit\"></el-image>\r\n              </div>\r\n              \r\n            </el-form-item>\r\n            <el-form-item v-else label=\"自评分数：\" prop=\"selfScore\">\r\n              <div style=\"display: flex;width: 180px;\">\r\n                <el-input type=\"number\" autosize v-model=\"selfScore\" placeholder=\"请输入分数\" :readonly=\"readOnly\" />\r\n                <span style=\"margin-left: 8px;\">分</span>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"status > '1' && info.deptScore && info.deptUserName\" label=\"部门领导评分 / 签名：\">\r\n              <span >{{ info.deptScore + \" 分 / \" + info.deptUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"status > '2' && info.businessUserName && info.businessScore\" label=\"事业部领导评分 / 签名：\">\r\n              <span>{{ info.businessScore + \" 分 / \" + info.businessUserName }}</span>\r\n            </el-form-item>\r\n            <!-- <el-form-item v-if=\"status > '4' && info.leaderScore && info.leaderName\" label=\"总经理部领导评分 / 签名：\" prop=\"deptId\">\r\n              <span >{{ info.leaderScore + \" 分 / \" + info.leaderName }}</span>\r\n            </el-form-item> -->\r\n          </el-form>\r\n      <div v-if=\"!readOnly\" style=\"text-align: center;\">\r\n        <el-button v-if=\"resetShow\" plain type=\"info\" @click=\"resetInfo\">重 置 </el-button>\r\n        <el-button type=\"success\" @click=\"save\">保 存</el-button>\r\n        <el-button type=\"primary\" @click=\"onSubmit\">提 交</el-button>\r\n      </div>\r\n\r\n      <!-- 签名板 -->\r\n      <el-dialog title=\"签字确认\" :visible.sync=\"openSign\" width=\"760px\" append-to-body>\r\n        <div style=\"border: 1px #ccc solid;width: 702px;\">\r\n            <vue-signature-pad\r\n            width=\"700px\"\r\n            height=\"300px\"\r\n            ref=\"signaturePad\"\r\n            :options=\"signOptions\"\r\n          />\r\n        </div>\r\n        <div style=\"text-align: center;padding: 10px 10px;\">\r\n          <el-button style=\"margin-right: 20px;\" type=\"success\" @click=\"clearSign\">清除</el-button>\r\n          <el-button type=\"primary\" @click=\"uploadSignature\">确认</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { getInfoByDate, saveInfo, submitInfo, delInfo, listBeAssessed } from \"@/api/assess/self/info\";\r\n  // import { batchTarget, listSelfTargetAll } from \"@/api/assess/self/target\";\r\n  import { getReportDeptList, getByWorkNoDeptId } from \"@/api/assess/self/user\";\r\n  import { formatDateYm } from \"@/utils/index\"\r\n  import { VueSignaturePad } from 'vue-signature-pad';\r\n\r\n  export default {\r\n    components: {\r\n      VueSignaturePad\r\n    },\r\n    name: \"SelfAssessReport\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        openSign:false,\r\n        // 绩效考核-自评指标配置表格数据\r\n        list: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          userId:null,\r\n          workNo: null,\r\n          deptId:null,\r\n          assessDate: null,\r\n        },\r\n        // 考核年月文本显示\r\n        assessDateText:null,\r\n        // 部门显示\r\n        deptName:null,\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 用户信息\r\n        userInfo:{},\r\n        // 合并单元格信息\r\n        spanList:{\r\n          itemList:[],\r\n          standardList:[]\r\n        },\r\n        // 是否显示重置按钮\r\n        resetShow:false,\r\n        // 是否只读\r\n        readOnly:false,\r\n        // 部门选项\r\n        deptOptions:[],\r\n        // 自评信息Id\r\n        id:null,\r\n        // 自评分数\r\n        selfScore:100,\r\n        // 状态\r\n        status:\"0\",\r\n        // 自评信息\r\n        info:{},\r\n        // 横向被考评信息\r\n        beAssessedList:[],\r\n        // 退回理由\r\n        rejectReason:\"\",\r\n        // 自评签名\r\n        selfSign:\"\",\r\n        // 签名板配置\r\n        signOptions: {\r\n          onBegin: () => this.$refs.signaturePad.resizeCanvas(),\r\n          backgroundColor: 'rgba(255, 255, 255, 1)'\r\n        },\r\n        sign:\"\",\r\n        file:null,\r\n        fileList:[],\r\n        upload: {\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/app/common/uploadMinio\",\r\n          isUploading: false,\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.queryParams.assessDate = formatDateYm(new Date().getTime())\r\n      this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n      // this.getSelfAssessUser();\r\n      this.getReportDeptList();\r\n    },\r\n    methods: {\r\n      \r\n      // 获取部门信息\r\n      getReportDeptList(){\r\n        getReportDeptList().then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.handleDeptList(res.data);\r\n            // 根据部门获取用户信息\r\n            this.getByWorkNoDeptId();\r\n          }\r\n        })\r\n      },\r\n      // 获取用户信息\r\n      getByWorkNoDeptId(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.queryParams.userId = res.data.id;\r\n            this.queryParams.workNo = res.data.workNo;\r\n            this.userInfo = res.data;\r\n            this.getList();\r\n            // 获取被考核信息\r\n            this.getBeAssessedList();\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 获取被考核信息\r\n      getBeAssessedList(){\r\n        listBeAssessed({deptId:this.queryParams.deptId,assessDate:this.queryParams.assessDate}).then(res =>{\r\n          let beAssessedList = [];\r\n          if(res.code == 200){\r\n            if(res.data.length > 0){\r\n              res.data.forEach(item => {\r\n                beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n              })\r\n              this.beAssessedList = beAssessedList;\r\n            }\r\n          }\r\n          console.log(beAssessedList)\r\n        })\r\n      },\r\n      /** 查询绩效考核-自评指标配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        getInfoByDate(this.queryParams).then(response => {\r\n          console.log(response.data);\r\n          // console.log(typeof response.data);\r\n          if (Array.isArray(response.data)) {\r\n            // 指标配置数据\r\n            this.handleSpanList(response.data);\r\n            this.list = response.data.map(item => {item.performance = \"\";item.dePoints = null; return item});\r\n            this.status = \"0\";\r\n            this.readOnly = false;\r\n            this.resetShow = false;\r\n            this.rejectReason = null;\r\n          } else {\r\n            // 自评信息\r\n            let info = response.data;\r\n            let list = JSON.parse(info.content);\r\n            this.handleSpanList(list);\r\n            this.list = list;\r\n            this.id = info.id;\r\n            this.selfScore = info.selfScore;\r\n            this.rejectReason = info.rejectReason;\r\n            this.status = info.status;\r\n            if(info.sign){\r\n              this.selfSign = JSON.parse(info.sign);\r\n            }\r\n            this.info = info;\r\n            if(info.status == \"0\"){\r\n              this.readOnly = false;\r\n              this.resetShow = true;\r\n            }else{\r\n              this.readOnly = true;\r\n              this.resetShow = false;\r\n            }\r\n          }\r\n          this.loading = false;\r\n        });\r\n      },\r\n\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let itemList = [];\r\n        let standardList = [];\r\n        let itemFlag = 0;\r\n        let standardFlag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项、评分标准合并\r\n          if(i == 0){\r\n            itemList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            standardList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            // 考核项\r\n            if(data[i - 1].item == data[i].item){\r\n              itemList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              itemList[itemFlag].rowspan += 1;\r\n            }else{\r\n              itemList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              itemFlag = i;\r\n            }\r\n            // 评分标准\r\n            if(data[i - 1].standard == data[i].standard){\r\n              standardList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              standardList[standardFlag].rowspan += 1;\r\n            }else{\r\n              standardList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              standardFlag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList.itemList = itemList;\r\n        this.spanList.standardList = standardList;\r\n      },\r\n\r\n\r\n      // 处理部门下拉选项\r\n      handleDeptList(data){\r\n        // let syb = [\"炼铁事业部\",\"炼钢事业部\",\"轧钢事业部\",\"特板事业部\",\"动力事业部\",\"物流事业部\",\"研究院\"];\r\n        let deptList = [];\r\n        data.forEach(item => {\r\n          //if(syb.indexOf(item.deptName) == -1){\r\n            deptList.push({\r\n              deptName:item.deptName,\r\n              deptId:item.deptId\r\n            })\r\n          //}\r\n        })\r\n        this.deptOptions = deptList;\r\n        if(deptList.length > 0){\r\n          this.queryParams.deptId = deptList[0].deptId;\r\n          this.deptName = deptList[0].deptName;\r\n        }\r\n      },\r\n\r\n\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n        this.deptOptions.forEach(item => {\r\n          if(item.deptId == this.queryParams.deptId){\r\n            this.deptName = item.deptName;\r\n          }\r\n        })\r\n        this.id = null;\r\n        this.info = null;\r\n        this.selfScore = \"\";\r\n        this.getByWorkNoDeptId();\r\n      },\r\n\r\n      // 保存\r\n      save(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"0\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType\r\n        }\r\n        saveInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.$message({\r\n              type: 'success',\r\n              message: '保存成功!'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n      // 确认提交点击事件\r\n      submit(){\r\n          this.$confirm('确认后将流转至下一节点, 是否继续?', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitData();\r\n          }).catch(() => {\r\n\r\n          });\r\n      },\r\n\r\n      onSubmit(){\r\n        if(this.verifyInsert()){\r\n          this.openSign = true;\r\n        }\r\n      },\r\n\r\n      clearSign(){\r\n        this.$refs.signaturePad.clearSignature();\r\n      },\r\n\r\n      // 提交数据验证\r\n      verifyInsert(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        for(let i = 0; i < this.list.length; i++){\r\n          if(!this.list[i].performance || !this.list[i].dePoints){\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '信息未填写完整'\r\n            });\r\n            return false;\r\n          }else if(this.list[i].dePoints != 0 && !this.list[i].pointsReason){\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '有加减分的请填写原因'\r\n            });\r\n            return false;\r\n          }\r\n        }\r\n        if(!this.selfScore){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '请填写自评分数'\r\n            });\r\n            return false;\r\n        }\r\n        return true;\r\n      },\r\n\r\n      // 新增数据\r\n      submitData(){\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"1\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType,\r\n          averageLinkFlag:this.userInfo.averageLinkFlag,\r\n          benefitLinkFlag:this.userInfo.benefitLinkFlag,\r\n          sign:JSON.stringify(this.sign)\r\n        }\r\n        submitInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.sign = \"\";\r\n            this.$refs.signaturePad.clearSignature();\r\n            this.openSign = false;\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n          }else{\r\n\r\n          }\r\n        })\r\n      },\r\n\r\n      // 保存重置\r\n      resetInfo(){\r\n        // 删除保存信息\r\n        delInfo({id:this.id}).then(res => {\r\n          if(res.code == 200){\r\n            this.id = null;\r\n            this.selfScore = null;\r\n            // 获取配置信息\r\n            this.getList();\r\n          }\r\n        })\r\n      },\r\n\r\n      // 处理提交数据\r\n      handleData(data){\r\n        let result = []\r\n        data.forEach(item => {\r\n          let form = {\r\n            item: item.item,\r\n            category: item.category,\r\n            target: item.target,\r\n            standard: item.standard,\r\n            performance: item.performance,\r\n            dePoints: item.dePoints,\r\n            pointsReason:item.pointsReason\r\n          };\r\n          result.push(form);\r\n        })\r\n        return result\r\n      },\r\n\r\n      /** 标准配置跳转 */\r\n      handleConfig(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            if(res.data.id){\r\n            this.$router.push({\r\n              path:\"/assess/self/user/detail\",\r\n              query:{\r\n                userId:res.data.id\r\n              }\r\n            })\r\n          }\r\n          }\r\n        })\r\n        \r\n      },\r\n\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList.itemList[rowIndex];\r\n        }\r\n        // 评分标准相同合并\r\n        if(columnIndex === 3){\r\n          return this.spanList.standardList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      // 被考核事项点击事件\r\n      handleBeAssessedClick(row,optionRow,index){\r\n        console.log(row)\r\n        // 将事项填入完成实绩列（弃用）\r\n        // if(row.performance){\r\n        //   this.$set(row, 'performance', row.performance + \"；\" + optionRow.assessContent);\r\n        // }else{\r\n        //   this.$set(row, 'performance', optionRow.assessContent);\r\n        // }\r\n        \r\n        // 将分数填入加减分列\r\n        if(row.dePoints){\r\n          this.$set(row, 'dePoints', Number(row.dePoints) + Number(optionRow.deductionOfPoint));\r\n        }else{\r\n          this.$set(row, 'dePoints', Number(optionRow.deductionOfPoint));\r\n        }\r\n        \r\n        // 将事项+分数填入加减分理由列\r\n        let reasonContent = optionRow.assessContent + \"(\" + optionRow.deductionOfPoint + \"分)\";\r\n        if(row.pointsReason){\r\n          this.$set(row, 'pointsReason', row.pointsReason + \"；\" + reasonContent);\r\n        }else{\r\n          this.$set(row, 'pointsReason', reasonContent);\r\n        }\r\n        \r\n        this.$refs[`popover${index}`].showPopper = false;\r\n        // 重新计算自评分数\r\n        this.scoreInput();\r\n      },\r\n\r\n      // 加减分输入\r\n      scoreInput(row = null){\r\n        // 验证月度重点工作的加减分只能为1、3或5（仅当传入row参数时进行验证）\r\n        if (row && row.item) {\r\n          let noSpaceStr = row.item.replace(/\\s+/g, '');\r\n          if (noSpaceStr.includes(\"月度重点工作\")) {\r\n            let value = row.dePoints;\r\n            if (value !== null && value !== undefined && value !== '') {\r\n              let numValue = Number(value);\r\n              if (![1, 3, 5].includes(numValue)) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '月度重点工作的加减分只能为1分、3分或5分'\r\n                });\r\n                // 重置为空值\r\n                this.$set(row, 'dePoints', null);\r\n                return;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 重新计算自评分数\r\n        let dePoints = 0;\r\n        let points = 0;\r\n        this.list.forEach(item => {\r\n          let noSpaceStr = item.item.replace(/\\s+/g, '');\r\n          if(item.dePoints && noSpaceStr.includes(\"月度重点工作\")){\r\n            points += Number(item.dePoints);\r\n          }else if(item.dePoints){\r\n            dePoints += Number(item.dePoints);\r\n          }\r\n        })\r\n        this.selfScore = 85 + dePoints + points;\r\n      },\r\n\r\n      // 签名上传相关\r\n      uploadSignature(){\r\n        const { isEmpty, data } = this.$refs.signaturePad.saveSignature();\r\n        console.log(isEmpty,data)\r\n        if(isEmpty){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请签名!'\r\n          });\r\n          return false;\r\n        }else{\r\n          const blobBin = atob(data.split(',')[1]);\r\n          let array = [];\r\n          for (let i = 0; i < blobBin.length; i++) {\r\n            array.push(blobBin.charCodeAt(i));\r\n          }\r\n          const fileBlob = new Blob([new Uint8Array(array)], { type: 'image/png' });\r\n          const formData = new FormData();\r\n          formData.append('file', fileBlob, `${Date.now()}.png`);\r\n          fetch(this.upload.url, {\r\n            method: 'POST',\r\n            body: formData,\r\n          })\r\n          .then(response => response.json())\r\n          .then(data => {\r\n            console.log('Success:', data);\r\n            if(data.code == 200){\r\n              this.sign = {fileName:this.userInfo.name + \".png\",url:data.url};\r\n              this.submit();\r\n            }else{\r\n              this.$message({\r\n                type: 'error',\r\n                message: '签名上传失败'\r\n              });\r\n            }\r\n          })\r\n          .catch((error) => {\r\n            console.error('Error:', error);\r\n            this.$message({\r\n              type: 'error',\r\n              message: '签名上传异常'\r\n            });\r\n          });\r\n        }\r\n        \r\n      },\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .table-striped{\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n    text-align: center;\r\n    border: 1px #888;\r\n    border-collapse: collapse;\r\n  }\r\n  .table-striped th{\r\n    height: 32px;\r\n    border: 1px solid #888;\r\n    background-color: #dedede;\r\n  }\r\n  .table-striped td{\r\n    min-height: 32px;\r\n    border: 1px solid #888;\r\n  }\r\n  .table-input .el-textarea__inner{\r\n    border: 0 !important;\r\n    resize: none !important;\r\n  }\r\n  .table-input .el-input__inner{\r\n    border: 0 !important;\r\n  }\r\n  /* .myUpload .el-upload--picture-card{\r\n    display:none !important; \r\n  } */\r\n  </style>\r\n"]}]}