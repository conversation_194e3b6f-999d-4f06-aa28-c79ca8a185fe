package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.DicMeasure;

/**
 * IC卡信息Service接口
 * 
 * <AUTHOR>
 */
public interface IDicService 
{
    /**
     * 查询IC卡信息
     * 
     * @param id IC卡信息主键
     * @return IC卡信息
     */
    public DicMeasure selectDicById(Long id);

    /**
     * 查询IC卡信息列表
     * 
     * @param dicMeasure IC卡信息
     * @return IC卡信息集合
     */
    public List<DicMeasure> selectDicList(DicMeasure dicMeasure);

    /**
     * 新增IC卡信息
     * 
     * @param dicMeasure IC卡信息
     * @return 结果
     */
    public int insertDic(DicMeasure dicMeasure);

    /**
     * 修改IC卡信息
     * 
     * @param dicMeasure IC卡信息
     * @return 结果
     */
    public int updateDic(DicMeasure dicMeasure);

    /**
     * 批量删除IC卡信息
     * 
     * @param ids 需要删除的IC卡信息主键集合
     * @return 结果
     */
    public int deleteDicByIds(Long[] ids);

    /**
     * 删除IC卡信息信息
     * 
     * @param id IC卡信息主键
     * @return 结果
     */
    public int deleteDicById(Long id);

    /**
     * 根据匹配ID删除IC卡信息
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    public int deleteDicByMatchid(String matchid);
} 