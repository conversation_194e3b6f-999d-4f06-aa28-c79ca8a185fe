package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.StoreoutWwjgMeasure;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 外委加工出库Mapper接口
 * 
 * <AUTHOR>
 */
public interface StoreoutWwjgMapper 
{
    /**
     * 查询外委加工出库
     * 
     * @param id 外委加工出库主键
     * @return 外委加工出库
     */
    @DataSource(DataSourceType.XCC1)
    public StoreoutWwjgMeasure selectStoreoutWwjgById(Long id);

    /**
     * 查询外委加工出库列表
     * 
     * @param storeoutWwjgMeasure 外委加工出库
     * @return 外委加工出库集合
     */
    @DataSource(DataSourceType.XCC1)
    public List<StoreoutWwjgMeasure> selectStoreoutWwjgList(StoreoutWwjgMeasure storeoutWwjgMeasure);

    /**
     * 新增外委加工出库
     * 
     * @param storeoutWwjgMeasure 外委加工出库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int insertStoreoutWwjg(StoreoutWwjgMeasure storeoutWwjgMeasure);

    /**
     * 修改外委加工出库
     * 
     * @param storeoutWwjgMeasure 外委加工出库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int updateStoreoutWwjg(StoreoutWwjgMeasure storeoutWwjgMeasure);

    /**
     * 删除外委加工出库
     * 
     * @param id 外委加工出库主键
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreoutWwjgById(Long id);

    /**
     * 批量删除外委加工出库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreoutWwjgByIds(Long[] ids);

    /**
     * 根据匹配ID删除外委加工出库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreoutWwjgByMatchid(String matchid);
} 