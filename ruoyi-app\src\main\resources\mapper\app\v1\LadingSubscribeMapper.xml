<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.v1.mapper.LadingSubscribeMapper">

    <resultMap type="LadingSubscribe" id="LadingSubscribeResult">
        <result property="id" column="id"/>
        <result property="flowNo" column="flow_no"/>
        <result property="delivyNo" column="delivy_no"/>
        <result property="openId" column="open_id"/>
        <result property="billOfLadingNo" column="bill_of_lading_no"/>
        <result property="stockCode" column="stock_code"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="location" column="location"/>
        <result property="stockName" column="stock_name"/>
        <result property="planWt" column="plan_wt"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="consignUserName" column="consign_user_name"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="vehicleNo" column="vehicle_no"/>
        <result property="licensePlateColor" column="license_plate_color"/>
        <result property="driverName" column="driver_name"/>
        <result property="phoneNo" column="phone_no"/>
        <result property="zuBillNo" column="zu_bill_no"/>
        <result property="fleetName" column="fleet_name"/>
        <result property="idCard" column="id_card"/>
        <result property="delivyTime" column="delivy_time"/>
        <result property="carryCompanyName" column="carry_company_name"/>
        <result property="delivyRemark" column="delivy_remark"/>
        <result property="status" column="status"/>
        <result property="predictEntryBeginDate" column="predict_entry_begin_date"/>
        <result property="predictEntryEndDate" column="predict_entry_end_date"/>
        <result property="photo" column="photo"/>
        <result property="driverLicenseImgs" column="driver_license_imgs"/>
        <result property="vehicleLicenseImgs" column="vehicle_license_imgs"/>
        <result property="passStatus" column="pass_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="billRemark" column="bill_remark"/>
    </resultMap>

    <resultMap type="LadingSubscribe" id="LadingServiceResult">
        <result property="id" column="id"/>
        <result property="delivy_no" column="delivy_no"/>
        <result property="rate" column="rate"/>
        <result property="service" column="service"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteTime" column="delete_time"/>
    </resultMap>

    <resultMap type="LadingSubscribe" id="LadingGoodsResult">
        <result property="delivyTime1" column="DELIVY_TIME"/>
        <result property="stockName" column="STOCK_NAME"/>
        <result property="consignUserName" column="BALANCE_USER_NAME"/>
        <result property="vehicleNo" column="VEHICLE_NO"/>
        <result property="planWt" column="WT"/>
    </resultMap>

    <sql id="selectLadingSubscribeVo">
        select id,
               flow_no,
               open_id,
               delivy_no,
               predict_entry_begin_date,
               predict_entry_end_date,
               stock_code,
               stock_name,
               bill_of_lading_no,
               plan_wt,
               plan_end_time,
               vendor_name,
               consign_user_name,
               vehicle_no,
               license_plate_color,
               driver_name,
               phone_no,
               id_card,
               carry_company_name,
               delivy_time,
               location,
               status,
               photo,
               driver_license_imgs,
               vehicle_license_imgs,
               pass_status,
               create_time,
               update_time,
               delete_time
        from t_lading_subscribe
    </sql>

    <select id="selectLadingSubscribeByIdRange" parameterType="LadingSubscribe" resultMap="LadingSubscribeResult">
        <include refid="selectLadingSubscribeVo"/>
        where id >= #{start}
        and #{end} >= id
    </select>

    <select id="selectLadingSubscribeList" parameterType="LadingSubscribe" resultMap="LadingSubscribeResult">
        <include refid="selectLadingSubscribeVo"/>
        <where>
            <if test="openId != null  and openId != ''">and open_id = #{openId}</if>
            <if test="delivyNo != null  and delivyNo != ''">and delivy_no = #{delivyNo}</if>
            <if test="billOfLadingNo != null  and billOfLadingNo != ''">and bill_of_lading_no = #{billOfLadingNo}</if>
            <if test="vehicleNo != null  and vehicleNo != ''">and vehicle_no = #{vehicleNo}</if>
            <if test="licensePlateColor != null ">and license_plate_color = #{licensePlateColor}</if>
            <if test="idCard != null  and idCard != ''">and id_card = #{idCard}</if>
            <if test="driverName != null  and driverName != ''">and driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="phoneNo != null  and phoneNo != ''">and phone_no = #{phoneNo}</if>
            <if test="carryCompanyName != null  and carryCompanyName != ''">and carry_company_name = #{carryCompanyName}
            </if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="filterOneMonth">
                and create_time >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
            </if>
            and delete_time is null
        </where>
        order by delivy_no desc
    </select>

    <select id="selectDriverReserveList" parameterType="LadingSubscribe" resultMap="LadingSubscribeResult">
        <include refid="selectLadingSubscribeVo"/>
        <where>
            <if test="openId != null  and openId != ''">and open_id = #{openId}</if>
            <if test="delivyNo != null  and delivyNo != ''">and delivy_no = #{delivyNo}</if>
            <if test="billOfLadingNo != null  and billOfLadingNo != ''">and bill_of_lading_no = #{billOfLadingNo}</if>
            <if test="vehicleNo != null  and vehicleNo != ''">and vehicle_no = #{vehicleNo}</if>
            <if test="licensePlateColor != null ">and license_plate_color = #{licensePlateColor}</if>
            <if test="idCard != null  and idCard != ''">and id_card = #{idCard}</if>
            <if test="driverName != null  and driverName != ''">and driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="phoneNo != null  and phoneNo != ''">and phone_no = #{phoneNo}</if>
            <if test="carryCompanyName != null  and carryCompanyName != ''">and carry_company_name = #{carryCompanyName}
            </if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="entryTime != null">and predict_entry_end_date >= #{entryTime}
            and #{entryTime} >= predict_entry_begin_date
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%Y-%c-%d %H:%i:%S') &gt;= date_format(#{params.beginTime},'%Y-%c-%d %H:%i:%S')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(#{params.endTime},'%Y-%c-%d %H:%i:%S') >= date_format(create_time,'%Y-%c-%d %H:%i:%S')
            </if>
            and delete_time is null
            and open_id is not null
        </where>
        order by create_time desc
    </select>

    <select id="selectDriverLadingRecordResult" parameterType="String" resultMap="LadingSubscribeResult">
        <include refid="selectLadingSubscribeVo"/>
        WHERE open_id = #{openId}
        and create_time > (SELECT DATE_ADD(now(),INTERVAL -1 MONTH))
        and delete_time is null
        union
        <include refid="selectLadingSubscribeVo"/>
        WHERE vehicle_no IN
        ( SELECT car_number FROM xctg_driver_car
        WHERE open_id = #{openId}
        )
        and create_time > (SELECT DATE_ADD(now(),INTERVAL -1 MONTH))
        and delete_time is null
        order by delivy_no desc
    </select>

    <select id="selectLadingSubscribeById" parameterType="Long" resultMap="LadingSubscribeResult">
        <include refid="selectLadingSubscribeVo"/>
        where id = #{id}
    </select>

    <select id="selectLadingSubscribeByFLowNo" parameterType="String" resultMap="LadingSubscribeResult">
        <include refid="selectLadingSubscribeVo"/>
        where flow_no = #{flowNo}
    </select>

    <select id="selectLadingSubscribeId" parameterType="LadingSubscribe" resultType="Long">
        select id from t_lading_subscribe
        where delivy_no = #{delivyNo}
        and open_id = #{openId}
    </select>

    <select id="selectSubscribeIdByDelivyNo" parameterType="String" resultType="Long">
        select id from t_lading_subscribe
        where delivy_no = #{delivyNo}
    </select>

    <select id="selectLadingSubscribeByDelivyNo" parameterType="String" resultMap="LadingSubscribeResult">
        <include refid="selectLadingSubscribeVo"/>
        where delivy_no = #{delivyNo} and delete_time is null
        limit 1
    </select>

    <select id="selectLadingService" parameterType="String" resultMap="LadingServiceResult">
        select id,rate,service
        from t_service
        where delivy_no = #{delivyNo}
        order by create_time desc
        limit 1
    </select>

    <select id="selectLadingGoods" parameterType="String" resultMap="LadingGoodsResult">
        select
        v.DELIVY_TIME, --发运时间
        v.STOCK_NAME, --库区
        v.BALANCE_USER_NAME,--收货用户
        v.VEHICLE_NO, --车号
        v.WT --装载重量
        from xcc1.v_delivy_info_180 v
        where 1=1
        and v.bill_of_lading_no = #{billOfLadingNo}
    </select>

    <insert id="insertLadingSubscribe" parameterType="LadingSubscribe">
        insert into t_lading_subscribe
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="flowNo != null">flow_no,</if>
            <if test="delivyNo != null">delivy_no,</if>
            <if test="openId != null">open_id,</if>
            <if test="billOfLadingNo != null">bill_of_lading_no,</if>
            <if test="stockCode != null">stock_code,</if>
            <if test="stockName != null">stock_name,</if>
            <if test="consignUserName != null">consign_user_name,</if>
            <if test="vehicleNo != null">vehicle_no,</if>
            <if test="licensePlateColor != null">license_plate_color,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="planWt != null">plan_wt,</if>
            <if test="planEndTime != null">plan_end_time,</if>
            <if test="phoneNo != null">phone_no,</if>
            <if test="idCard != null">id_card,</if>
            <if test="carryCompanyName != null">carry_company_name,</if>
            <if test="delivyTime != null">delivy_time,</if>
            <if test="location != null">location,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="status != null">status,</if>
            <if test="predictEntryBeginDate != null">predict_entry_begin_date,</if>
            <if test="predictEntryEndDate != null">predict_entry_end_date,</if>
            <if test="photo != null">photo,</if>
            <if test="driverLicenseImgs != null">driver_license_imgs,</if>
            <if test="vehicleLicenseImgs != null">vehicle_license_imgs,</if>
            <if test="passStatus != null">pass_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="flowNo != null">#{flowNo},</if>
            <if test="delivyNo != null">#{delivyNo},</if>
            <if test="openId != null">#{openId},</if>
            <if test="billOfLadingNo != null">#{billOfLadingNo},</if>
            <if test="stockCode != null">#{stockCode},</if>
            <if test="stockName != null">#{stockName},</if>
            <if test="consignUserName != null">#{consignUserName},</if>
            <if test="vehicleNo != null">#{vehicleNo},</if>
            <if test="licensePlateColor != null">#{licensePlateColor},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="planWt != null">#{planWt},</if>
            <if test="planEndTime != null">#{planEndTime},</if>
            <if test="phoneNo != null">#{phoneNo},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="carryCompanyName != null">#{carryCompanyName},</if>
            <if test="delivyTime != null">#{delivyTime},</if>
            <if test="location != null">#{location},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="status != null">#{status},</if>
            <if test="predictEntryBeginDate != null">#{predictEntryBeginDate},</if>
            <if test="predictEntryEndDate != null">#{predictEntryEndDate},</if>
            <if test="photo != null">#{photo},</if>
            <if test="driverLicenseImgs != null">#{driverLicenseImgs},</if>
            <if test="vehicleLicenseImgs != null">#{vehicleLicenseImgs},</if>
            <if test="passStatus != null">#{passStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
        </trim>
    </insert>

    <insert id="insertLadingService" parameterType="LadingSubscribe">
        insert into t_service
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="delivyNo != null">delivy_no,</if>
            <if test="rate != null">rate,</if>
            <if test="service != null">service,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="delivyNo != null">#{delivyNo},</if>
            <if test="rate != null">#{rate},</if>
            <if test="service != null">#{service},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
        </trim>
    </insert>

    <update id="updateLadingSubscribe" parameterType="LadingSubscribe">
        update t_lading_subscribe
        <trim prefix="SET" suffixOverrides=",">
            <if test="openId != null">open_id = #{openId},</if>
            <if test="delivyTime != null">delivy_time = #{delivyTime},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="licensePlateColor != null">license_plate_color = #{licensePlateColor},</if>
            <if test="phoneNo != null">phone_no = #{phoneNo},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="location != null">location = #{location},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="predictEntryBeginDate != null">predict_entry_begin_date = #{predictEntryBeginDate},</if>
            <if test="predictEntryEndDate != null">predict_entry_end_date = #{predictEntryEndDate},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="driverLicenseImgs != null">driver_license_imgs = #{driverLicenseImgs},</if>
            <if test="vehicleLicenseImgs != null">vehicle_license_imgs = #{vehicleLicenseImgs},</if>
            <if test="passStatus != null">pass_status = #{passStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateLadingSubscribeByFlowNo" parameterType="LadingSubscribe">
        update t_lading_subscribe
        <trim prefix="SET" suffixOverrides=",">
            <if test="openId != null">open_id = #{openId},</if>
            <if test="delivyTime != null">delivy_time = #{delivyTime},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="licensePlateColor != null">license_plate_color = #{licensePlateColor},</if>
            <if test="phoneNo != null">phone_no = #{phoneNo},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="location != null">location = #{location},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="predictEntryBeginDate != null">predict_entry_begin_date = #{predictEntryBeginDate},</if>
            <if test="predictEntryEndDate != null">predict_entry_end_date = #{predictEntryEndDate},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="driverLicenseImgs != null">driver_license_imgs = #{driverLicenseImgs},</if>
            <if test="vehicleLicenseImgs != null">vehicle_license_imgs = #{vehicleLicenseImgs},</if>
            <if test="passStatus != null">pass_status = #{passStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
        </trim>
        where flow_no = #{flowNo}
    </update>

    <update id="updateLadingService" parameterType="LadingSubscribe">
        update t_service
        <trim prefix="SET" suffixOverrides=",">
            <if test="rate != null">rate = #{rate},</if>
            <if test="service != null">service = #{service},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
        </trim>
        where delivy_no = #{delivyNo}
    </update>

    <update id="updateLadingServiceStatus" parameterType="String">
        update t_service
        set status = 1
        where delivy_no = #{delivyNo}
    </update>

    <update id="updateDriverReserveStatus" parameterType="Long">
        update t_lading_subscribe
        <trim prefix="SET" suffixOverrides=",">
            status = 1,
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLadingSubscribeById" parameterType="Long">
        delete from t_lading_subscribe where id = #{id}
    </delete>

    <delete id="deleteLadingSubscribeByIds" parameterType="String">
        delete from t_lading_subscribe where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectLadingDetailByladingNo" parameterType="String" resultMap="LadingSubscribeResult">
        SELECT
	    stock_code, stock_name, consign_user_name, plan_wt, plan_end_time, bill_of_lading_no, bill_remark
        FROM
	    xcc1.T0101 t
        WHERE
	    t.bill_of_lading_no = #{billOfLadingNo}
    </select>

    <select id="selectStationDetailByStockCode" parameterType="String" resultMap="LadingSubscribeResult">
        SELECT
	    code, name, longitude, latitude
        FROM
	    xctg_station t
        WHERE
	    t.code = #{code}
    </select>

    <select id="selectCarryCompanyNameByBillNo" parameterType="String" resultMap="LadingSubscribeResult">
       SELECT zu_bill_no, fleet_name
       FROM
       TABLE ( xcc1.get_zu_bill ( #{billNo} ) )
    </select>

    <select id="getDelivyNo" resultType="String">
       SELECT xcc1.get_delivy_no() FROM DUAL
    </select>

    <select id="selectLadingDetailByDelivyNo" parameterType="String" resultMap="LadingSubscribeResult">
        SELECT
	    delivy_no, bill_of_lading_no, vehicle_no, driver_name, phone_no, carry_company_name, delivy_remark
        FROM
	    xcc1.tsmdw02
        WHERE
	    delivy_no = #{delivyNo}
    </select>

    <resultMap type="LadingSubscribe" id="LadingRemarkResult">
        <result property="id" column="id"/>
        <result property="billOfLadingNo" column="bill_of_lading_no"/>
        <result property="consignUserName" column="consign_user_name"/>
        <result property="stockName" column="stock_name"/>
        <result property="planWt" column="plan_wt"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="delivyRemark" column="delivy_remark"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteTime" column="delete_time"/>
    </resultMap>

    <sql id="selectLadingRemarkVo">
        select id, bill_of_lading_no, consign_user_name, stock_name, plan_wt, plan_end_time, delivy_remark, status, create_time, update_time, delete_time from t_lading_remark
    </sql>

    <select id="selectLadingRemarkById" parameterType="Long" resultMap="LadingRemarkResult">
        <include refid="selectLadingRemarkVo"/>
        where id = #{id}
    </select>

    <insert id="insertLadingRemark" parameterType="LadingSubscribe">
        insert into t_lading_remark
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="billOfLadingNo != null">bill_of_lading_no,</if>
            <if test="consignUserName != null">consign_user_name,</if>
            <if test="stockName != null">stock_name,</if>
            <if test="planWt != null">plan_wt,</if>
            <if test="planEndTime != null">plan_end_time,</if>
            <if test="delivyRemark != null">delivy_remark,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="billOfLadingNo != null">#{billOfLadingNo},</if>
            <if test="consignUserName != null">#{consignUserName},</if>
            <if test="stockName != null">#{stockName},</if>
            <if test="planWt != null">#{planWt},</if>
            <if test="planEndTime != null">#{planEndTime},</if>
            <if test="delivyRemark != null">#{delivyRemark},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
        </trim>
    </insert>

    <update id="updateLadingRemark" parameterType="LadingSubscribe">
        update t_lading_remark
        <trim prefix="SET" suffixOverrides=",">
            <if test="delivyRemark != null">delivy_remark = #{delivyRemark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateLadingRemarkStatus" parameterType="Long">
        update t_lading_remark
        <trim prefix="SET" suffixOverrides=",">
            status = 1,
        </trim>
        where id = #{id}
    </update>

    <select id="selectLadingRemarkId" parameterType="LadingSubscribe" resultType="Long">
        select id from t_lading_remark
        <where>
            <if test="billOfLadingNo != null  and billOfLadingNo != ''">and bill_of_lading_no = #{billOfLadingNo}</if>
            and delete_time is null
        </where>
    </select>

    <select id="selectLadingRemarkByBillNo" parameterType="String" resultMap="LadingRemarkResult">
        select * from t_lading_remark
        where bill_of_lading_no = #{billOfLadingNo}
        and delete_time is null
        limit 1
    </select>

    <select id="selectLadingRemarkList" parameterType="LadingSubscribe" resultMap="LadingRemarkResult">
        select * from t_lading_remark
        <where>
            <if test="billOfLadingNo != null  and billOfLadingNo != ''">and bill_of_lading_no = #{billOfLadingNo}</if>
            <if test="consignUserName != null  and consignUserName != ''">and consign_user_name like concat('%',
                #{consignUserName}, '%')
            </if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            and delete_time is null
        </where>
        order by update_time desc
    </select>

    <resultMap type="LadingSubscribe" id="LocusResult">
        <result property="id" column="id"/>
        <result property="openId" column="open_id"/>
        <result property="delivyNo" column="delivy_no"/>
        <result property="billOfLadingNo" column="bill_of_lading_no"/>
        <result property="consignUserName" column="consign_user_name"/>
        <result property="stockName" column="stock_name"/>
        <result property="vehicleNo" column="vehicle_no"/>
        <result property="driverName" column="driver_name"/>
        <result property="phoneNo" column="phone_no"/>
        <result property="idCard" column="id_card"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="location" column="location"/>
        <result property="files" column="files"/>
        <result property="workName" column="work_name"/>
        <result property="eventNo" column="event_no"/>
        <result property="eventContent" column="event_content"/>
        <result property="delivyTime" column="delivy_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteTime" column="delete_time"/>
    </resultMap>

    <insert id="insertLocus" parameterType="LadingSubscribe">
        insert into xctg_yq_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null">open_id,</if>
            <if test="delivyNo != null">delivy_no,</if>
            <if test="billOfLadingNo != null">bill_of_lading_no,</if>
            <if test="consignUserName != null">consign_user_name,</if>
            <if test="stockName != null">stock_name,</if>
            <if test="vehicleNo != null">vehicle_no,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="phoneNo != null">phone_no,</if>
            <if test="idCard != null">id_card,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="location != null">location,</if>
            <if test="files != null">files,</if>
            <if test="workName != null">work_name,</if>
            <if test="eventNo != null">event_no,</if>
            <if test="eventContent != null">event_content,</if>
            <if test="delivyTime != null">delivy_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null">#{openId},</if>
            <if test="delivyNo != null">#{delivyNo},</if>
            <if test="billOfLadingNo != null">#{billOfLadingNo},</if>
            <if test="consignUserName != null">#{consignUserName},</if>
            <if test="stockName != null">#{stockName},</if>
            <if test="vehicleNo != null">#{vehicleNo},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="phoneNo != null">#{phoneNo},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="location != null">#{location},</if>
            <if test="files != null">#{files},</if>
            <if test="workName != null">#{workName},</if>
            <if test="eventNo != null">#{eventNo},</if>
            <if test="eventContent != null">#{eventContent},</if>
            <if test="delivyTime != null">#{delivyTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
        </trim>
    </insert>

</mapper>
