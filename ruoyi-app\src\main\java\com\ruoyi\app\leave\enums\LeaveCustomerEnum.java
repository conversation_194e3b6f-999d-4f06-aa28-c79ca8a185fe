package com.ruoyi.app.leave.enums;

/**
 * 客户状态 0-作废 1-正常 2-领卡单位
 */
public enum LeaveCustomerEnum {
    CANCEL(0, "作废"),
    NORMAL(1, "正常"),
    OWNER(2, "领卡单位");

    private final int code;
    private final String desc;


    public int getCode() {return code;}

    public String getDesc() {return desc;}

    LeaveCustomerEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

   public static LeaveCustomerEnum getByCode(int code) {
        for (LeaveCustomerEnum value : LeaveCustomerEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
   }

   public static LeaveCustomerEnum getByDesc(String desc) {
        for (LeaveCustomerEnum value : LeaveCustomerEnum.values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
   }

}
