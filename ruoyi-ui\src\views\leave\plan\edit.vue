<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="card-header">
        <h3>{{ isEdit ? '修改申请' : '新增申请' }}</h3>
      </div>

      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划类型" prop="planType">
              <el-radio-group v-model="form.planType" @change="handlePlanTypeChange">
                <el-radio :label="1">出厂不返回</el-radio>
                <el-radio :label="2">出厂返回</el-radio>
                <el-radio :label="3">跨区调拨</el-radio>
                <el-radio :label="4">退货申请</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="业务类型" prop="businessCategory">
              <el-radio-group v-model="form.businessCategory" @change="handleBusinessCategoryChange">
                <el-radio v-if="form.planType === 1" :label="1">通用</el-radio>
                <el-radio v-if="form.planType === 2" :label="11">通用</el-radio>
                <el-radio v-if="form.planType === 2" :label="12">委外加工</el-radio>
                <el-radio v-if="form.planType === 3" :label="21">有计划量计量</el-radio>
                <el-radio v-if="form.planType === 3" :label="22">短期</el-radio>
                <el-radio v-if="form.planType === 3" :label="23">钢板（圆钢）</el-radio>
                <el-radio v-if="form.planType === 4" :label="31">通用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否计量" prop="measureFlag">
              <el-select v-model="form.measureFlag" placeholder="请选择是否计量" :disabled="isSpecialCondition">
                <el-option label="计量" :value="1"></el-option>
                <el-option label="不计量" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="form.planType === 3 && form.businessCategory === 21">
            <el-form-item label="计划量（吨）" prop="plannedAmount">
              <el-input-number v-model="form.plannedAmount" :min="0" controls-position="right" placeholder="请输入计划量"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.planType === 3 && form.businessCategory === 21">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-tooltip class="item" effect="dark" content="开始时间默认为该日期的0时0分0秒" placement="top">
                <i class="el-icon-question" style="margin-left: 2px;"></i>
              </el-tooltip>
              <el-date-picker
                v-model="form.startTime"
                type="date"
                placeholder="选择开始时间"
                value-format="yyyy-MM-dd"
                :picker-options="form.planType === 3 && form.businessCategory === 21 ? datePickerOptions : {}">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-tooltip class="item" effect="dark" content="结束时间默认为该日期的23时59分59秒" placement="top">
                <i class="el-icon-question" style="margin-left: 2px;"></i>
              </el-tooltip>
              <el-date-picker
                v-model="form.endTime"
                type="date"
                placeholder="选择结束时间"
                value-format="yyyy-MM-dd"
                :picker-options="form.planType === 3 && form.businessCategory === 21 ? datePickerOptions : {}">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="!(form.planType === 3 && form.businessCategory === 21)">
          <el-col :span="12">
            <el-form-item label="有效期" prop="expireTime">
              <el-tooltip class="item" effect="dark" content="有效期默认为该日期的23时59分59秒" placement="top">
                <i class="el-icon-question" style="margin-left: 2px;"></i>
              </el-tooltip>
              <el-date-picker
                v-model="form.expireTime"
                type="date"
                placeholder="选择有效期"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请单位" prop="sourceCompanyCode">
              <el-select
                v-model="form.sourceCompanyCode"
                placeholder="请选择申请单位"
                filterable
                remote
                reserve-keyword
                :remote-method="remoteSearchDepartment"
                :loading="departmentLoading">
                <template slot="selected">
                  <span>{{ getDepartmentName(form.sourceCompanyCode) }}</span>
                </template>
                <el-option
                  v-for="item in departmentOptions"
                  :key="item.id"
                  :label="item.storeName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="收货单位" prop="receiveCompanyCode">
              <el-select
                v-model="form.receiveCompanyCode"
                placeholder="请选择收货单位"
                filterable
                remote
                reserve-keyword
                :remote-method="form.planType === 3 ? remoteSearchDepartment : remoteSearchCustomer"
                :loading="form.planType === 3 ? departmentLoading : customerLoading">
                <template slot="selected">
                  <span>{{ form.planType === 3 ? getDepartmentName(form.receiveCompanyCode) : getCustomerName(form.receiveCompanyCode) }}</span>
                </template>
                <el-option
                  v-for="item in form.planType === 3 ? departmentOptions : customerOptions"
                  :key="item.id"
                  :label="form.planType === 3 ? item.storeName : item.customerName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12" v-if="form.planType === 2">
            <el-form-item label="返回单位" prop="targetCompanyCode">
              <el-select
                v-model="form.targetCompanyCode"
                placeholder="请选择返回单位"
                filterable
                remote
                reserve-keyword
                :remote-method="remoteSearchDepartment"
                :loading="departmentLoading">
                <template slot="selected">
                  <span>{{ getDepartmentName(form.targetCompanyCode) }}</span>
                </template>
                <el-option
                  v-for="item in departmentOptions"
                  :key="item.id"
                  :label="item.storeName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="form.planType === 4">
            <el-form-item label="退货单位" prop="refundCompanyCode">
              <el-select
                v-model="form.refundCompanyCode"
                placeholder="请选择退货单位"
                filterable
                remote
                reserve-keyword
                :remote-method="remoteSearchDepartment"
                :loading="departmentLoading">
                <template slot="selected">
                  <span>{{ getDepartmentName(form.refundCompanyCode) }}</span>
                </template>
                <el-option
                  v-for="item in departmentOptions"
                  :key="item.id"
                  :label="item.storeName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12" v-if="form.planType === 2">
            <el-form-item label="计划返回时间" prop="plannedReturnTime">
              <el-date-picker
                v-model="form.plannedReturnTime"
                type="date"
                placeholder="选择计划返回时间"
                value-format="yyyy-MM-dd"
                :picker-options="{
                  disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7;
                  }
                }">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="监装人" prop="monitor">
              <el-input v-model="form.monitor" placeholder="请输入监装人"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="物资专管员" prop="specialManager">
              <el-input v-model="form.specialManager" placeholder="请输入物资专管员"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物资类型" prop="itemType">
              <el-select v-model="form.itemType" placeholder="请选择物资类型">
                <el-option label="钢材" :value="1"></el-option>
                <el-option label="钢板" :value="2"></el-option>
                <el-option label="其他" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="是否复审" prop="secApproveFlag">
              <el-select v-model="form.secApproveFlag" placeholder="请选择是否复审">
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
              <div class="tip-text">在写申请单时，一般是不需要复审的，但一些物资比较特殊或者贵重等原因，必须经过更高领导审核(签字)，因此请申请人注意选择</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出厂原因" prop="reason">
              <el-input v-model="form.reason" placeholder="请输入出厂原因"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="合同号" prop="contractNo">
              <el-input v-model="form.contractNo" placeholder="请输入合同号"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 图片上传 -->
        <el-form-item label="申请图片">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            list-type="picture-card"
            :file-list="imgFileList"
            :limit="3"
            :on-preview="handleImgPreview"
            :on-remove="handleImgRemove"
            :on-success="handleImgSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeImgUpload"
            :on-exceed="handleExceed"
            :data="uploadParams"
            multiple>
            <i class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过10MB</div>
          </el-upload>
          <el-dialog :visible.sync="imgPreviewVisible">
            <img width="100%" :src="imgPreviewUrl" alt="">
          </el-dialog>
        </el-form-item>

        <!-- 附件上传 -->
        <el-form-item label="申请附件">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :file-list="fileList"
            :limit="3"
            :on-preview="handleFilePreview"
            :on-remove="handleFileRemove"
            :on-success="handleAnnexFileSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeFileUpload"
            :on-exceed="handleExceed"
            :data="uploadParams">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">可上传任意类型文件，且不超过20MB</div>
          </el-upload>
        </el-form-item>

        <!-- 物资列表 -->
        <el-card class="material-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>物资列表</span>
            <div class="material-btn-group">
              <el-button size="small" type="primary" plain icon="el-icon-download" @click="handleExportMaterialTemplate">导出物资模板</el-button>
              <el-button size="small" type="success" plain icon="el-icon-upload2" @click="handleImportMaterial">导入物资</el-button>
              <el-button size="small" type="warning" plain icon="el-icon-plus" @click="handleAddMaterial">添加物资</el-button>
            </div>
          </div>

          <el-table
            :data="form.materials"
            style="width: 100%"
            border>
            <el-table-column
              type="index"
              width="50"
              label="序号">
            </el-table-column>
            
            <el-table-column
              prop="materialName"
              label="物资名称"
              width="180">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.materialId"
                  placeholder="请输入物资名称"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="remoteSearchMaterial"
                  :loading="materialLoading"
                  @change="(value) => handleMaterialSelect(value, scope.row)">
                  <el-option
                    v-for="item in materialOptions"
                    :key="item.id"
                    :label="item.materialName"
                    :value="item.id">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              prop="materialSpec"
              label="物资型号规格"
              width="180">
              <template slot-scope="scope">
                <el-input v-model="scope.row.materialSpec" placeholder="请输入物资型号规格"></el-input>
              </template>
            </el-table-column>
            <el-table-column
              prop="planNum"
              label="计划数量"
              width="180">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.planNum" :min="0" controls-position="right" placeholder="请输入计划数量"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column
              prop="measureUnit"
              label="计量单位"
              width="120">
              <template slot-scope="scope">
                <el-input v-model="scope.row.measureUnit" placeholder="请输入计量单位"></el-input>
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark" placeholder="请输入备注"></el-input>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="120">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDeleteMaterial(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <div class="form-footer">
          <el-button type="primary" @click="submitForm">{{ isEdit ? '修 改' : '确 定' }}</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-form>
    </el-card>
    
    <!-- 导入物资对话框 -->
    <el-dialog :title="'导入物资'" :visible.sync="importVisible" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleMaterialFileSuccess"
        :auto-upload="false"
        drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将物资Excel文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件，且不超过5MB</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="importVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDepartment } from "@/api/leave/department";
import { listCustomer } from "@/api/leave/customer";
import { listMaterial } from "@/api/leave/material";
import { addPlan, updatePlan, detailPlan, exportMaterialTemplate, importMaterialList } from "@/api/leave/plan";
import { getToken } from "@/utils/auth";

export default {
  name: "EditLeavePlan",
  data() {
    return {
      // 导入参数
      importVisible: false,
      // 上传参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/web/leave/plan/importMaterialList"
      },
      // 是否为编辑模式
      isEdit: false,
      // 表单参数
      form: {
        planType: 1,
        businessCategory: 1,
        measureFlag: 1,
        plannedAmount: 0,
        sourceCompanyCode: "",
        sourceCompany: "",
        receiveCompanyCode: "",
        receiveCompany: "",
        targetCompanyCode: "",
        targetCompany: "",
        refundCompanyCode: "",
        refundCompany: "",
        contractNo: "",
        plannedReturnTime: "",
        startTime: "",
        endTime: "",
        expireTime: "", // 有效期
        monitor: "",
        specialManager: "",
        itemType: 1,
        reason: "",
        secApproveFlag: 0,
        applyImgUrl: "", // 申请图片URL
        applyFileUrl: "", // 申请文件URL
        materials: []
      },
      // 表单校验规则
      rules: {
        planType: [
          { required: true, message: "请选择计划类型", trigger: "change" }
        ],
        businessCategory: [
          { required: true, message: "请选择业务类型", trigger: "change" }
        ],
        measureFlag: [
          { required: true, message: "请选择是否计量", trigger: "change" }
        ],
        applyCompany: [
          { required: true, message: "请选择申请单位", trigger: "change" }
        ],
        receiveCompany: [
          { required: true, message: "请选择收货单位", trigger: "change" }
        ],
        targetCompany: [
          { required: true, message: "请选择返回单位", trigger: "change" }
        ],
        monitor: [
          { required: true, message: "请输入监装人", trigger: "blur" }
        ],
        specialManager: [
          { required: true, message: "请输入物资专管员", trigger: "blur" }
        ],
        materialType: [
          { required: true, message: "请选择物资类型", trigger: "change" }
        ],
        leaveReason: [
          { required: true, message: "请输入出厂原因", trigger: "blur" }
        ],
        plannedReturnTime: [
          { required: true, message: "请选择计划返回时间", trigger: "change" }
        ]
      },
      // 单位下拉选项
      departmentOptions: [],
      customerOptions: [],
      departmentLoading: false,
      customerLoading: false,
      // 物资下拉选项
      materialOptions: [],
      materialLoading: false,
      
      // 上传相关
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/uploadMinio",
      uploadParams: {
        // 上传时可能需要的额外参数
        uploadType: 'leavePlan'
      },
      imgFileList: [], // 图片文件列表
      fileList: [], // 文件列表
      imgPreviewVisible: false, // 图片预览对话框可见性
      imgPreviewUrl: "", // 图片预览URL
      
      // 部门和客户选项关联映射
      departmentMap: new Map(), // 部门id到名称的映射
      customerMap: new Map(), // 客户id到名称的映射
      // 添加日期限制对象
      datePickerOptions: {
        disabledDate(time) {
          // 获取当前月份的25号日期
          const currentDate = new Date();
          const year = currentDate.getFullYear();
          const month = currentDate.getMonth();
          const monthLimit = new Date(year, month, 25, 23, 59, 59);
          
          // 禁用超过当月25号的日期
          return time.getTime() > monthLimit.getTime();
        }
      },
    };
  },
  computed: {
    // 特殊条件：跨区调拨且有计划量计量时
    isSpecialCondition() {
      return this.form.planType === 3 && this.form.businessCategory === 21;
    }
  },
  watch: {
    // 监控特殊条件变化，自动设置是否计量为"计量"
    isSpecialCondition(val) {
      if (val) {
        this.form.measureFlag = 1;
      }
    },
    // 计划类型、业务类型、计划结束时间变化时，同步有效期
    'form.planType': {
    handler(val) {
      this.syncExpireTime();
    }
  },
  'form.businessCategory': {
    handler(val) {
      this.syncExpireTime();
    }
  },
  'form.endTime': {
    handler(val) {
      this.syncExpireTime();
    }
  }
  },
  created() {
    // 获取路由参数中的applyNo，判断是新增还是编辑
    const applyNo = this.$route.params.applyNo;
    if (applyNo) {
      this.isEdit = true;
      this.getDetail(applyNo);
    } else {
       // 新增时，设置有效期为当前日期+3天 23:59:59
      const now = new Date();
      now.setDate(now.getDate() + 3);
      const yyyy = now.getFullYear();
      const mm = String(now.getMonth() + 1).padStart(2, '0');
      const dd = String(now.getDate()).padStart(2, '0');
      this.form.expireTime = `${yyyy}-${mm}-${dd}`; 
      // 初始化一行物资数据
      this.handleAddMaterial();
      // 根据选中的计划类型设置默认业务类型
      this.handlePlanTypeChange(this.form.planType);
      // 初始化表单校验规则
      this.updateFormRules();
    }
  },
  methods: {
    // 同步有效期
    syncExpireTime() {
    if (this.form.planType === 3 && this.form.businessCategory === 21) {
      this.form.expireTime = this.form.endTime;
      }else if (!this.form.expireTime) {
      // 只在有效期为空时设置默认值（3天）
      const now = new Date();
      now.setDate(now.getDate() + 3);
      const yyyy = now.getFullYear();
      const mm = String(now.getMonth() + 1).padStart(2, '0');
      const dd = String(now.getDate()).padStart(2, '0');
      this.form.expireTime = `${yyyy}-${mm}-${dd}`;
    }
    },    
    // 获取申请详情
    getDetail(applyNo) {
      detailPlan(applyNo).then(response => {
        if (response.code === 200) {
          const detail = response.data;
          
          // 填充基本信息
          this.form.id = detail.id;
          this.form.applyNo = detail.applyNo;
          this.form.planType = detail.planType;
          this.form.businessCategory = detail.businessCategory;
          this.form.measureFlag = detail.measureFlag;
          this.form.plannedAmount = detail.plannedAmount || 0;
          
          // 保存单位代码和名称
          this.form.sourceCompanyCode = detail.sourceCompanyCode;
          this.form.sourceCompany = detail.sourceCompany;
          this.form.receiveCompanyCode = detail.receiveCompanyCode;
          this.form.receiveCompany = detail.receiveCompany;
          this.form.targetCompanyCode = detail.targetCompanyCode;
          this.form.targetCompany = detail.targetCompany;
          this.form.refundCompanyCode = detail.refundDepartmentCode;
          this.form.refundCompany = detail.refundDepartment;
          
          // 手动添加已存在的部门和客户到选项数组，以便正确显示选中的值
          // 添加申请单位到部门选项
          if (detail.sourceCompanyCode && detail.sourceCompany) {
            this.addToDepartmentOptions(detail.sourceCompanyCode, detail.sourceCompany);
          }
          
          // 添加收货单位（可能是部门或客户）
          if (detail.receiveCompanyCode && detail.receiveCompany) {
            if (detail.planType === 3) {
              // 跨区调拨时，收货单位是部门
              this.addToDepartmentOptions(detail.receiveCompanyCode, detail.receiveCompany);
            } else {
              // 其他情况，收货单位是客户
              this.addToCustomerOptions(detail.receiveCompanyCode, detail.receiveCompany);
            }
          }
          
          // 添加返回单位到部门选项
          if (detail.targetCompanyCode && detail.targetCompany) {
            this.addToDepartmentOptions(detail.targetCompanyCode, detail.targetCompany);
          }
          
          // 添加退货单位到部门选项
          if (detail.refundDepartmentCode && detail.refundDepartment) {
            this.addToDepartmentOptions(detail.refundDepartmentCode, detail.refundDepartment);
          }
          
          this.form.plannedReturnTime = detail.planReturnTime;
          this.form.startTime = detail.startTime;
          this.form.endTime = detail.endTime;
          this.form.monitor = detail.monitor;
          this.form.specialManager = detail.specialManager;
          this.form.itemType = detail.itemType;
          this.form.reason = detail.reason;
          this.form.contractNo = detail.contractNo;
          this.form.secApproveFlag = detail.secApproveFlag;
          this.form.applyImgUrl = detail.applyImgUrl;
          this.form.applyFileUrl = detail.applyFileUrl;
          
          // 处理图片和文件列表
          this.initFileList();
          
          // 填充物资列表
          if (detail.materials && detail.materials.length > 0) {
            this.form.materials = detail.materials.map(item => ({
              id: item.id,
              materialId: item.materialId,
              materialName: item.materialName,
              materialSpec: item.materialSpec,
              planNum: item.planNum,
              measureUnit: item.measureUnit,
              remark: item.remark
            }));
            
            // 添加物资到选项数组
            detail.materials.forEach(item => {
              if (item.materialId && item.materialName) {
                this.addToMaterialOptions(item.materialId, item.materialName, item.materialSpec, item.measureUnit);
              }
            });
          } else {
            this.handleAddMaterial();
          }
          
          // 更新表单校验规则
          this.updateFormRules();
        }
      });
    },
    // 远程搜索部门
    remoteSearchDepartment(query) {
      if (query !== '') {
        this.departmentLoading = true;
        listDepartment({
          storeName: query,
          pageNum: 1,
          pageSize: 20
        }).then(response => {
          this.departmentLoading = false;
          if (response.code === 200) {
            this.departmentOptions = response.rows;
          }
        }).finally(() => {
          this.departmentLoading = false;
        });
      } else {
        this.departmentOptions = [];
      }
    },

    // 远程搜索客户
    remoteSearchCustomer(query) {
      if (query !== '') {
        this.customerLoading = true;
        listCustomer({
          customerName: query,
          pageNum: 1,
          pageSize: 20
        }).then(response => {
          this.customerLoading = false;
          if (response.code === 200) {
            this.customerOptions = response.rows;
          }
        }).finally(() => {
          this.customerLoading = false;
        });
      } else {
        this.customerOptions = [];
      }
    },

    // 业务类型变更时触发
    handleBusinessCategoryChange() {
      // 更新表单校验规则
      this.updateFormRules();
    },

    // 计划类型变更时触发
    handlePlanTypeChange(val) {
      // 根据计划类型设置默认业务类型
      switch (val) {
        case 1:
          this.form.businessCategory = 1;
          break;
        case 2:
          this.form.businessCategory = 11;
          break;
        case 3:
          this.form.businessCategory = 21;
          break;
        case 4:
          this.form.businessCategory = 31;
          break;
        default:
          break;
      }

      // 重置不需要的字段
      if (val !== 2) {
        this.form.targetCompany = "";
        this.form.plannedReturnTime = "";
      }

      if (val !== 4) {
        this.form.refundCompany = "";
      }

      if (val !== 3 || this.form.businessCategory !== 21) {
        this.form.startTime = "";
        this.form.endTime = "";
      }

      // 更新表单校验规则
      this.updateFormRules();
    },

    // 更新表单校验规则
    updateFormRules() {
      const tempRules = {
        planType: [
          { required: true, message: "请选择计划类型", trigger: "change" }
        ],
        businessCategory: [
          { required: true, message: "请选择业务类型", trigger: "change" }
        ],
        measureFlag: [
          { required: true, message: "请选择是否计量", trigger: "change" }
        ],
        applyCompany: [
          { required: true, message: "请选择申请单位", trigger: "change" }
        ],
        monitor: [
          { required: true, message: "请输入监装人", trigger: "blur" }
        ],
        specialManager: [
          { required: true, message: "请输入物资专管员", trigger: "blur" }
        ],
        materialType: [
          { required: true, message: "请选择物资类型", trigger: "change" }
        ]
      };

      // 根据条件添加校验规则
      if (this.form.planType !== 1) {
        tempRules.receiveCompany = [
          { required: true, message: "请选择收货单位", trigger: "change" }
        ];
      }

      if (this.form.planType === 2) {
        tempRules.targetCompany = [
          { required: true, message: "请选择返回单位", trigger: "change" }
        ];
        tempRules.plannedReturnTime = [
          { required: true, message: "请选择计划返回时间", trigger: "change" }
        ];
      }

      if (this.form.planType === 4) {
        tempRules.refundCompany = [
          { required: true, message: "请选择退货单位", trigger: "change" }
        ];
      }

      if (this.form.planType === 3 && this.form.businessCategory === 21) {
        tempRules.plannedAmount = [
          { required: true, message: "请输入计划量", trigger: "blur" }
        ];
        tempRules.startTime = [
          { required: true, message: "请选择开始时间", trigger: "change" }
        ];
        tempRules.endTime = [
          { required: true, message: "请选择结束时间", trigger: "change" }
        ];
      }

      // 更新规则
      this.rules = tempRules;
    },

    // 添加物资
    handleAddMaterial() {
      this.form.materials.push({
        materialId: "",
        materialName: "",
        materialSpec: "",
        planNum: 1,
        measureUnit: "",
        remark: ""
      });
    },
    // 删除物资
    handleDeleteMaterial(index) {
      this.form.materials.splice(index, 1);
      // 如果删完了，至少保留一行
      if (this.form.materials.length === 0) {
        this.handleAddMaterial();
      }
    },
    // 远程搜索物资
    remoteSearchMaterial(query) {
      if (query !== '') {
        this.materialLoading = true;
        listMaterial({
          materialName: query,
          pageNum: 1,
          pageSize: 20
        }).then(response => {
          this.materialLoading = false;
          if (response.code === 200) {
            this.materialOptions = response.rows;
          }
        }).finally(() => {
          this.materialLoading = false;
        });
      } else {
        this.materialOptions = [];
      }
    },
    // 处理物资选择
    handleMaterialSelect(value, row) {
      // 根据选中的物资ID找到对应的物资详情
      const selectedMaterial = this.materialOptions.find(item => item.id === value);
      if (selectedMaterial) {
        row.materialName = selectedMaterial.materialName;
        // 如果物资信息中有型号规格和单位，可以自动填充
        if (selectedMaterial.materialSpec) {
          row.materialSpec = selectedMaterial.materialSpec;
        }
        if (selectedMaterial.measureUnit) {
          row.measureUnit = selectedMaterial.measureUnit;
        }
      }
    },
    // 初始化文件列表
    initFileList() {
      // 处理图片
      if (this.form.applyImgUrl) {
        try {
          // 尝试解析JSON
          const imgFiles = JSON.parse(this.form.applyImgUrl);
          this.imgFileList = imgFiles.map(item => {
            return {
              name: item.name,
              url: item.url
            };
          });
        } catch (e) {
          console.log(e);
        }
      }
      
      // 处理文件
      if (this.form.applyFileUrl) {
        try {
          // 尝试解析JSON
          const files = JSON.parse(this.form.applyFileUrl);
          this.fileList = files.map(item => {
            return {
              name: item.name,
              url: item.url,
            };
          });
        } catch (e) {
          console.log(e);
        }
      }
    },
    
    // 图片上传成功
    handleImgSuccess(response, file, fileList) {
      console.log(response);
      console.log(file);
      console.log(fileList);
      if (response.code === 200) {
        // 更新图片URL到表单
        // this.imgFileList = fileList;
        
        // 构建JSON格式的图片数据
        /**
         * response格式：
         * {
    "msg": "操作成功",
    "originalFileName": "006r3PQBjw1f8p3zb1wioj30c80c8jru.jpg",
    "fileName": "xctg/2025/03/25/da2a4a3f-6962-4831-a4e5-6ecc2397e483.jpg",
    "code": 200,
    "url": "https://ydxt.citicsteel.com:8099/minio/xctg/2025/03/25/da2a4a3f-6962-4831-a4e5-6ecc2397e483.jpg"
}
         */

        
        // 只获取originalFileName、url 转换为JSON字符串保存
        const imgData = {
          name: response.originalFileName,
          url: response.url
        };
        this.imgFileList.push(imgData);
        console.log(this.imgFileList);
        this.form.applyImgUrl = JSON.stringify(this.imgFileList);
        console.log(this.form.applyImgUrl);
      } else {
        this.$message.error('图片上传失败');
      }
    },
    
    // 附件上传成功
    handleAnnexFileSuccess(response, file, fileList) {
      console.log(response);
      console.log(file);
      console.log(fileList);
      if (response.code === 200) {
        // 只获取originalFileName、url 转换为JSON字符串保存
        const annexFileData = {
          name: response.originalFileName,
          url: response.url
        };
        this.fileList.push(annexFileData);
        console.log(this.fileList);
        this.form.applyFileUrl = JSON.stringify(this.fileList);
        console.log(this.form.applyFileUrl);
      } else {
        this.$message.error('附件上传失败');
      }
    },

    // 物资导入文件上传成功处理
    handleMaterialFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.importVisible = false;
      
      if (response.code === 200) {
        this.$message.success("导入成功");
        // 将导入的物资列表添加到当前表单的物资列表中
        if (response.data && response.data.length > 0) {
          // 追加新导入的物资
          const importedMaterials = response.data.map(item => {
            // 如果没有materialId，使用-1作为默认ID，并确保该物资能显示
            const materialId = item.materialId || -1;
            // 物资名称必须有值
            const materialName = item.materialName || "未知物资";
            
            // 如果是默认ID的物资，则添加到选项中
            if (materialId === -1) {
              this.addToMaterialOptions(
                materialId,
                materialName,
                item.materialSpec || "",
                item.measureUnit || ""
              );
            }
            
            return {
              materialId: materialId,
              materialName: materialName,
              materialSpec: item.materialSpec || "",
              planNum: item.planNum || 1,
              measureUnit: item.measureUnit || "",
              remark: item.remark || ''
            };
          });
          
          // 将导入的物资添加到表单中
          this.form.materials = this.form.materials.concat(importedMaterials);
          
          // 使用nextTick确保视图更新
          this.$nextTick(() => {
            // 触发一次刷新，确保下拉框正确显示
            this.materialOptions = [...this.materialOptions];
          });
        }
      } else {
        this.$message.error(response.msg || "导入失败");
      }
    },
    
    // 根据物资名称查询materialId
    queryMaterialIdByName(rowIndex) {
      // 此方法已不再使用，保留空方法以避免可能的调用错误
    },
    
    // 图片预览
    handleImgPreview(file) {
      this.imgPreviewUrl = file.url || (file.response && file.response.data);
      this.imgPreviewVisible = true;
    },
    
    // 文件预览
    handleFilePreview(file) {
      window.open(file.url || (file.response && file.response.data));
    },
    
    // 移除图片
    handleImgRemove(file, fileList) {
      //移除fileList中url与file.url相同的文件
      this.imgFileList = this.imgFileList.filter(item => item.url !== file.url);
      
      // 转换为JSON字符串保存
      this.form.applyImgUrl = JSON.stringify(imgFileList);
    },
    
    // 移除文件
    handleFileRemove(file, fileList) {
      //移除fileList中url与file.url相同的文件
      this.fileList = this.fileList.filter(item => item.url !== file.url);
      
      // 转换为JSON字符串保存
      this.form.applyFileUrl = JSON.stringify(fileList);
    },
    
    // 超出文件数量限制
    handleExceed() {
      this.$message.warning('最多只能上传3个文件');
    },

    // 表单提交
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 验证物资列表
          if (this.form.materials.length === 0) {
            this.$message.error("请至少添加一项物资");
            return;
          }

          // 验证物资列表的必填项
          let valid = true;
          this.form.materials.forEach((item, index) => {
            if (!item.materialId || !item.materialName || !item.materialSpec || !item.planNum || !item.measureUnit) {
              this.$message.error(`第${index + 1}行物资信息不完整，请填写完整`);
              valid = false;
            }
          });

          if (!valid) {
            return;
          }

          // 验证计划量大于0
          if (this.form.planType === 3 && this.form.businessCategory === 21 && (!this.form.plannedAmount || this.form.plannedAmount <= 0)) {
            this.$message.error("计划量必须大于0");
            return;
          }
          //对有效期单独处理
          if (this.form.planType === 3 && this.form.businessCategory === 21) {
            if (this.form.endTime) {
              this.form.expireTime = this.form.endTime + " 23:59:59";
            }
          } else if (this.form.expireTime) {
            this.form.expireTime = this.form.expireTime + " 23:59:59";
          }
          //对开始结束时间单独处理
          if (this.form.startTime && this.form.endTime) {
            this.form.startTime = this.form.startTime + " 00:00:00";
            this.form.endTime = this.form.endTime + " 23:59:59";
          }

          // 根据是否编辑模式调用不同的API
          const apiMethod = this.isEdit ? updatePlan : addPlan;
          const successMsg = this.isEdit ? "修改成功" : "申请提交成功";
          console.log(this.form);
          // 调用API提交数据
          apiMethod(this.form).then(response => {
            if (response.code === 200) {
              this.$message.success(successMsg);
              this.$tab.closeOpenPage(this.$route);
              // 跳转到列表页面并刷新
              this.$router.push({ 
                path: "/leave/leavePlanList", 
                query: { 
                  t: Date.now(),
                  refresh: true // 添加刷新标记
                }
              });
            } else {
              this.$message.error(response.msg || "提交失败");
            }
          }).catch(error => {
            console.error("提交失败", error);
            this.$message.error("提交过程中发生错误，请稍后再试");
          });
        }
      });
    },
    // 取消按钮
    cancel() {
      this.$router.push("/leave/plan");
    },
    // 图片上传前的验证
    beforeImgUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isImage) {
        this.$message.error('只能上传图片格式文件!');
        return false;
      }
      if (!isLt10M) {
        this.$message.error('图片大小不能超过 10MB!');
        return false;
      }
      return true;
    },

    // 文件上传前的验证
    beforeFileUpload(file) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error('文件大小不能超过 20MB!');
        return false;
      }
      return true;
    },
    
    // 上传错误处理
    handleUploadError(err, file, fileList) {
      console.error("上传失败:", err);
      
      if (err.status === 403) {
        this.$message.error('上传失败：没有权限');
      } else {
        this.$message.error('上传失败：' + (err.message || '未知错误'));
      }
    },
    // 获取部门名称
    getDepartmentName(id) {
      if (!id) return '';
      
      // 查询选项中是否有匹配的
      const dept = this.departmentOptions.find(item => item.id == id);
      if (dept) {
        return dept.storeName;
      }
      
      // 根据不同的字段ID返回对应的中文名称
      if (id === this.form.sourceCompanyCode && this.form.sourceCompany) {
        return this.form.sourceCompany;
      }
      
      if (id === this.form.receiveCompanyCode && this.form.receiveCompany && this.form.planType === 3) {
        return this.form.receiveCompany;
      }
      
      if (id === this.form.targetCompanyCode && this.form.targetCompany) {
        return this.form.targetCompany;
      }
      
      if (id === this.form.refundCompanyCode && this.form.refundCompany) {
        return this.form.refundCompany;
      }
      
      // 否则显示ID
      return `ID: ${id}`;
    },
    
    // 获取客户名称
    getCustomerName(id) {
      if (!id) return '';
      
      // 查询选项中是否有匹配的
      const customer = this.customerOptions.find(item => item.id == id);
      if (customer) {
        return customer.customerName;
      }
      
      // 如果是收货单位且不是跨区调拨，则使用已有的中文名称
      if (id === this.form.receiveCompanyCode && this.form.receiveCompany && this.form.planType !== 3) {
        return this.form.receiveCompany;
      }
      
      // 否则显示ID
      return `ID: ${id}`;
    },
    // 添加部门到选项数组方法
    addToDepartmentOptions(id, name) {
      // 检查是否已存在相同ID的选项
      if (!this.departmentOptions.some(item => item.id == id)) {
        this.departmentOptions.push({
          id: id,
          storeName: name
        });
      }
    },
    
    // 添加客户到选项数组方法
    addToCustomerOptions(id, name) {
      // 检查是否已存在相同ID的选项
      if (!this.customerOptions.some(item => item.id == id)) {
        this.customerOptions.push({
          id: id,
          customerName: name
        });
      }
    },
    
    // 添加物资到选项数组方法
    addToMaterialOptions(id, name, spec, unit) {
      // 检查是否已存在相同ID的选项
      if (!this.materialOptions.some(item => item.id == id)) {
        this.materialOptions.push({
          id: id,
          materialName: name,
          materialSpec: spec,
          measureUnit: unit
        });
      }
    },
    // 导出物资模板
    handleExportMaterialTemplate() {
      exportMaterialTemplate().then(response => {
        this.download(response.msg);
      });
    },
    
    // 显示导入物资对话框
    handleImportMaterial() {
      this.importVisible = true;
    },
    
    // 文件上传中处理
    handleFileUploadProgress() {
      this.upload.isUploading = true;
    },
    
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.box-card {
  margin-bottom: 20px;
  border-radius: 5px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.material-card {
  margin-top: 20px;
  margin-bottom: 20px;
}
.form-footer {
  margin-top: 30px;
  text-align: center;
}
.material-btn-group {
  float: right;
}
.material-btn-group .el-button {
  margin-left: 8px;
  border-radius: 4px;
}
.material-btn-group .el-button:first-child {
  margin-left: 0;
}
.tip-text {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1.2;
  margin-top: 5px;
}
</style>
