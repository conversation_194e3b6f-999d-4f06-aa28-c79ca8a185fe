package com.ruoyi.app.leave.service.impl;

import java.util.List;
import java.util.Objects;

import com.ruoyi.app.leave.domain.LeavePlanMaterial;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeaveMaterialMapper;
import com.ruoyi.app.leave.domain.LeaveMaterial;
import com.ruoyi.app.leave.service.ILeaveMaterialService;

/**
 * 出门证物资Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeaveMaterialServiceImpl implements ILeaveMaterialService 
{
    @Autowired
    private LeaveMaterialMapper leaveMaterialMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询出门证物资
     * 
     * @param id 出门证物资ID
     * @return 出门证物资
     */
    @Override
    public LeaveMaterial selectLeaveMaterialById(Long id)
    {
        return leaveMaterialMapper.selectLeaveMaterialById(id);
    }

    /**
     * 查询出门证物资列表
     * 
     * @param leaveMaterial 出门证物资
     * @return 出门证物资
     */
    @Override
    public List<LeaveMaterial> selectLeaveMaterialList(LeaveMaterial leaveMaterial)
    {
        List<LeaveMaterial> leavePlanMaterialList = leaveMaterialMapper.selectLeaveMaterialList(leaveMaterial);
        for(LeaveMaterial planMaterial : leavePlanMaterialList){
            SysUser createrUser = sysUserMapper.selectUserByUserName(planMaterial.getCreateBy());
            if(Objects.nonNull(createrUser)){
                planMaterial.setCreateBy(createrUser.getNickName()+"("+planMaterial.getCreateBy()+")");
            }else{
                planMaterial.setCreateBy(planMaterial.getCreateBy());
            }

        }
        return leavePlanMaterialList;
    }

    /**
     * 新增出门证物资
     * 
     * @param leaveMaterial 出门证物资
     * @return 结果
     */
    @Override
    public int insertLeaveMaterial(LeaveMaterial leaveMaterial,String workNo)
    {
        SysUser sysUser = sysUserMapper.selectUserByUserName(workNo);
        leaveMaterial.setCreateBy(workNo);
        leaveMaterial.setCreateTime(DateUtils.getNowDate());
        return leaveMaterialMapper.insertLeaveMaterial(leaveMaterial);
    }

    /**
     * 修改出门证物资
     * 
     * @param leaveMaterial 出门证物资
     * @return 结果
     */
    @Override
    public int updateLeaveMaterial(LeaveMaterial leaveMaterial)
    {
        leaveMaterial.setUpdateTime(DateUtils.getNowDate());
        return leaveMaterialMapper.updateLeaveMaterial(leaveMaterial);
    }

    /**
     * 批量删除出门证物资
     * 
     * @param ids 需要删除的出门证物资ID
     * @return 结果
     */
    @Override
    public int deleteLeaveMaterialByIds(Long[] ids)
    {
        return leaveMaterialMapper.deleteLeaveMaterialByIds(ids);
    }

    /**
     * 删除出门证物资信息
     * 
     * @param id 出门证物资ID
     * @return 结果
     */
    @Override
    public int deleteLeaveMaterialById(Long id)
    {
        return leaveMaterialMapper.deleteLeaveMaterialById(id);
    }
}
