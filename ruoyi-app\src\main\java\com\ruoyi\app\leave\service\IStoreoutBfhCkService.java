package com.ruoyi.app.leave.service;

import java.util.Date;
import java.util.List;

import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.domain.LeaveTaskMaterial;
import com.ruoyi.app.leave.domain.StoreoutBfhCkMeasure;

/**
 * 备发货出库Service接口
 * 
 * <AUTHOR>
 */
public interface IStoreoutBfhCkService 
{
    /**
     * 查询备发货出库
     * 
     * @param id 备发货出库主键
     * @return 备发货出库
     */
    public StoreoutBfhCkMeasure selectStoreoutBfhCkById(Long id);

    /**
     * 查询备发货出库列表
     * 
     * @param storeoutBfhCkMeasure 备发货出库
     * @return 备发货出库集合
     */
    public List<StoreoutBfhCkMeasure> selectStoreoutBfhCkList(StoreoutBfhCkMeasure storeoutBfhCkMeasure);

    /**
     * 新增备发货出库
     * 
     * @param storeoutBfhCkMeasure 备发货出库
     * @return 结果
     */
    public int insertStoreoutBfhCk(StoreoutBfhCkMeasure storeoutBfhCkMeasure);

    /**
     * 修改备发货出库
     * 
     * @param storeoutBfhCkMeasure 备发货出库
     * @return 结果
     */
    public int updateStoreoutBfhCk(StoreoutBfhCkMeasure storeoutBfhCkMeasure);

    /**
     * 批量删除备发货出库
     * 
     * @param ids 需要删除的备发货出库主键集合
     * @return 结果
     */
    public int deleteStoreoutBfhCkByIds(Long[] ids);

    /**
     * 删除备发货出库信息
     * 
     * @param id 备发货出库主键
     * @return 结果
     */
    public int deleteStoreoutBfhCkById(Long id);

    /**
     * 根据匹配ID删除备发货出库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    public int deleteStoreoutBfhCkByMatchid(String matchid);

    void handleunReturnStockOut(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial);
}