# 建议相似度功能使用说明

## 功能概述

建议相似度功能可以自动计算所有建议之间的相似度，帮助用户识别重复或相似的建议内容。

## 数据库配置

### 1. 创建数据表

执行以下SQL语句创建 `suggest_similarity` 表：

```sql
-- 建议相似度表
CREATE TABLE `suggest_similarity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `suggest_number` varchar(50) NOT NULL COMMENT '建议编号',
  `compared_suggest_number` varchar(50) NOT NULL COMMENT '比较建议编号',
  `suggest_similarity` decimal(5,4) NOT NULL COMMENT '相似度结果',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_suggest_number` (`suggest_number`),
  KEY `idx_compared_suggest_number` (`compared_suggest_number`),
  KEY `idx_similarity` (`suggest_similarity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='建议相似度表';
```

## 功能使用

### 1. 计算相似度

1. 进入建议比较页面 (`/suggestCompare/info`)
2. 点击工具栏中的 **"计算相似度"** 按钮
3. 系统会弹出确认对话框，点击 **"确定"** 开始计算
4. 计算过程中会显示加载状态
5. 计算完成后会显示成功提示

### 2. 查看相似度

1. 在建议列表中，点击 **"相似度"** 列的 **"查看"** 按钮
2. 系统会弹出相似度详情对话框
3. 对话框中显示与该建议相似的其他建议列表，包括：
   - 建议编号
   - 建议名称
   - 相似度百分比
   - 详情查看按钮

### 3. 查看详细对比

1. 在相似度详情对话框中，点击 **"详情"** 列的 **"查看"** 按钮
2. 系统会弹出详细对比对话框
3. 对话框中同时显示两个建议的完整内容，方便进行详细对比

## 相似度算法

系统使用 Jaccard 相似度算法计算建议之间的相似度：

1. **文本预处理**：合并建议名称和内容，转换为小写
2. **分词处理**：按空格分割文本为单词
3. **过滤处理**：过滤掉单字符单词
4. **相似度计算**：使用 Jaccard 系数计算相似度
   - 相似度 = 交集单词数 / 并集单词数
   - 结果范围：0-1，值越大表示越相似

## 注意事项

1. **计算时间**：相似度计算需要比较所有建议两两之间的相似度，数据量大时可能需要较长时间
2. **数据清空**：每次计算相似度都会清空现有的相似度数据，重新计算
3. **数据要求**：至少需要2个建议才能进行相似度计算
4. **性能优化**：系统使用批量插入和分批处理来优化大数据量的处理性能

## 技术实现

### 后端实现

- **实体类**：`SuggestSimilarity.java`
- **Mapper**：`SuggestSimilarityMapper.java`
- **Service**：`ISuggestSimilarityService.java` 和 `SuggestSimilarityServiceImpl.java`
- **Controller**：`SuggestSimilarityController.java`
- **XML映射**：`SuggestSimilarityMapper.xml`

### 前端实现

- **页面**：`ruoyi-ui/src/views/suggestCompare/info/index.vue`
- **API**：`ruoyi-ui/src/api/suggestCompare/info.js`

### 主要API接口

- `POST /suggestCompare/info/calculateSimilarity` - 计算相似度
- `GET /suggestCompare/info/similarityList/{suggestNumber}` - 查询相似度列表
- `GET /suggestCompare/info/getBySuggestNumber/{suggestNumber}` - 根据编号查询建议

## 扩展功能

如需扩展相似度算法或添加其他功能，可以：

1. 修改 `calculateTextSimilarity` 方法中的算法
2. 添加更多的相似度计算维度
3. 增加相似度阈值设置功能
4. 添加相似度报告导出功能 