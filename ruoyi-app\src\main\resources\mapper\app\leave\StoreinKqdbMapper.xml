<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.StoreinKqdbMapper">
    
    <resultMap type="StoreinKqdbMeasure" id="StoreinKqdbResult">
        <result property="id"    column="ID"    />
        <result property="validflag"    column="VALIDFLAG"    />
        <result property="matchid"    column="MATCHID"    />
        <result property="carno"    column="CARNO"    />
        <result property="icno"    column="ICNO"    />
        <result property="operatype"    column="OPERATYPE"    />
        <result property="planid"    column="PLANID"    />
        <result property="storecode"    column="STORECODE"    />
        <result property="storename"    column="STORENAME"    />
        <result property="storepos"    column="STOREPOS"    />
        <result property="sourcecode"    column="SOURCECODE"    />
        <result property="sourcename"    column="SOURCENAME"    />
        <result property="materialcode"    column="MATERIALCODE"    />
        <result property="materialname"    column="MATERIALNAME"    />
        <result property="materialspeccode"    column="MATERIALSPECCODE"    />
        <result property="materialspec"    column="MATERIALSPEC"    />
        <result property="weight"    column="WEIGHT"    />
        <result property="counts"    column="COUNTS"    />
        <result property="heatno"    column="HEATNO"    />
        <result property="steelevel"    column="STEELLEVEL"    />
        <result property="steelgrade"    column="STEELGRADE"    />
        <result property="transitway"    column="TRANSITWAY"    />
        <result property="memo"    column="MEMO"    />
        <result property="createman"    column="CREATEMAN"    />
        <result property="createdate"    column="CREATEDATE"    />
        <result property="updatedate"    column="UPDATEDATE"    />
        <result property="updateman"    column="UPDATEMAN"    />
        <result property="zgflag"    column="ZGFLAG"    />
        <result property="zgdepartment"    column="ZGDEPARTMENT"    />
        <result property="zgdepartmentcode"    column="ZGDEPARTMENTCODE"    />
        <result property="taskcode"    column="TASKCODE"    />
        <result property="zgmaterialname"    column="ZGMATERIALNAME"    />
        <result property="zgmaterialnamecode"    column="ZGMATERIALNAMECODE"    />
    </resultMap>

    <sql id="selectStoreinKqdbVo">
        select ID, VALIDFLAG, MATCHID, CARNO, ICNO, OPERATYPE, PLANID, STORECODE, STORENAME, STOREPOS, SOURCECODE, SOURCENAME, MATERIALCODE, MATERIALNAME, MATERIALSPECCODE, MATERIALSPEC, WEIGHT, COUNTS, HEATNO, STEELLEVEL, STEELGRADE, TRANSITWAY, MEMO, CREATEMAN, CREATEDATE, UPDATEDATE, UPDATEMAN, ZGFLAG, ZGDEPARTMENT, ZGDEPARTMENTCODE, TASKCODE, ZGMATERIALNAME, ZGMATERIALNAMECODE from L_STOREIN_KQDB_T
    </sql>

    <select id="selectStoreinKqdbList" parameterType="StoreinKqdbMeasure" resultMap="StoreinKqdbResult">
        <include refid="selectStoreinKqdbVo"/>
        <where>  
            <if test="validflag != null "> and VALIDFLAG = #{validflag}</if>
            <if test="matchid != null  and matchid != ''"> and MATCHID = #{matchid}</if>
            <if test="carno != null  and carno != ''"> and CARNO = #{carno}</if>
            <if test="icno != null  and icno != ''"> and ICNO = #{icno}</if>
            <if test="operatype != null  and operatype != ''"> and OPERATYPE = #{operatype}</if>
            <if test="planid != null  and planid != ''"> and PLANID = #{planid}</if>
            <if test="storecode != null  and storecode != ''"> and STORECODE = #{storecode}</if>
            <if test="storename != null  and storename != ''"> and STORENAME like concat('%', #{storename}, '%')</if>
            <if test="storepos != null  and storepos != ''"> and STOREPOS = #{storepos}</if>
            <if test="sourcecode != null  and sourcecode != ''"> and SOURCECODE = #{sourcecode}</if>
            <if test="sourcename != null  and sourcename != ''"> and SOURCENAME like concat('%', #{sourcename}, '%')</if>
            <if test="materialcode != null  and materialcode != ''"> and MATERIALCODE = #{materialcode}</if>
            <if test="materialname != null  and materialname != ''"> and MATERIALNAME like concat('%', #{materialname}, '%')</if>
            <if test="materialspeccode != null  and materialspeccode != ''"> and MATERIALSPECCODE = #{materialspeccode}</if>
            <if test="materialspec != null  and materialspec != ''"> and MATERIALSPEC = #{materialspec}</if>
            <if test="weight != null "> and WEIGHT = #{weight}</if>
            <if test="counts != null "> and COUNTS = #{counts}</if>
            <if test="heatno != null  and heatno != ''"> and HEATNO = #{heatno}</if>
            <if test="steelevel != null  and steelevel != ''"> and STEELLEVEL = #{steelevel}</if>
            <if test="steelgrade != null  and steelgrade != ''"> and STEELGRADE = #{steelgrade}</if>
            <if test="transitway != null  and transitway != ''"> and TRANSITWAY = #{transitway}</if>
            <if test="memo != null  and memo != ''"> and MEMO = #{memo}</if>
            <if test="createman != null  and createman != ''"> and CREATEMAN = #{createman}</if>
            <if test="createdate != null "> and CREATEDATE = #{createdate}</if>
            <if test="updatedate != null "> and UPDATEDATE = #{updatedate}</if>
            <if test="updateman != null  and updateman != ''"> and UPDATEMAN = #{updateman}</if>
            <if test="zgflag != null "> and ZGFLAG = #{zgflag}</if>
            <if test="zgdepartment != null  and zgdepartment != ''"> and ZGDEPARTMENT = #{zgdepartment}</if>
            <if test="zgdepartmentcode != null  and zgdepartmentcode != ''"> and ZGDEPARTMENTCODE = #{zgdepartmentcode}</if>
            <if test="taskcode != null  and taskcode != ''"> and TASKCODE = #{taskcode}</if>
            <if test="zgmaterialname != null  and zgmaterialname != ''"> and ZGMATERIALNAME = #{zgmaterialname}</if>
            <if test="zgmaterialnamecode != null  and zgmaterialnamecode != ''"> and ZGMATERIALNAMECODE = #{zgmaterialnamecode}</if>
        </where>
    </select>
    
    <select id="selectStoreinKqdbById" parameterType="Long" resultMap="StoreinKqdbResult">
        <include refid="selectStoreinKqdbVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertStoreinKqdb" parameterType="StoreinKqdbMeasure">
        insert into L_STOREIN_KQDB_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="validflag != null">VALIDFLAG,</if>
            <if test="matchid != null">MATCHID,</if>
            <if test="carno != null">CARNO,</if>
            <if test="icno != null">ICNO,</if>
            <if test="operatype != null">OPERATYPE,</if>
            <if test="planid != null">PLANID,</if>
            <if test="storecode != null">STORECODE,</if>
            <if test="storename != null">STORENAME,</if>
            <if test="storepos != null">STOREPOS,</if>
            <if test="sourcecode != null">SOURCECODE,</if>
            <if test="sourcename != null">SOURCENAME,</if>
            <if test="materialcode != null">MATERIALCODE,</if>
            <if test="materialname != null">MATERIALNAME,</if>
            <if test="materialspeccode != null">MATERIALSPECCODE,</if>
            <if test="materialspec != null">MATERIALSPEC,</if>
            <if test="weight != null">WEIGHT,</if>
            <if test="counts != null">COUNTS,</if>
            <if test="heatno != null">HEATNO,</if>
            <if test="steelevel != null">STEELLEVEL,</if>
            <if test="steelgrade != null">STEELGRADE,</if>
            <if test="transitway != null">TRANSITWAY,</if>
            <if test="memo != null">MEMO,</if>
            <if test="createman != null">CREATEMAN,</if>
            <if test="createdate != null">CREATEDATE,</if>
            <if test="updatedate != null">UPDATEDATE,</if>
            <if test="updateman != null">UPDATEMAN,</if>
            <if test="zgflag != null">ZGFLAG,</if>
            <if test="zgdepartment != null">ZGDEPARTMENT,</if>
            <if test="zgdepartmentcode != null">ZGDEPARTMENTCODE,</if>
            <if test="taskcode != null">TASKCODE,</if>
            <if test="zgmaterialname != null">ZGMATERIALNAME,</if>
            <if test="zgmaterialnamecode != null">ZGMATERIALNAMECODE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="validflag != null">#{validflag},</if>
            <if test="matchid != null">#{matchid},</if>
            <if test="carno != null">#{carno},</if>
            <if test="icno != null">#{icno},</if>
            <if test="operatype != null">#{operatype},</if>
            <if test="planid != null">#{planid},</if>
            <if test="storecode != null">#{storecode},</if>
            <if test="storename != null">#{storename},</if>
            <if test="storepos != null">#{storepos},</if>
            <if test="sourcecode != null">#{sourcecode},</if>
            <if test="sourcename != null">#{sourcename},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspeccode != null">#{materialspeccode},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="weight != null">#{weight},</if>
            <if test="counts != null">#{counts},</if>
            <if test="heatno != null">#{heatno},</if>
            <if test="steelevel != null">#{steelevel},</if>
            <if test="steelgrade != null">#{steelgrade},</if>
            <if test="transitway != null">#{transitway},</if>
            <if test="memo != null">#{memo},</if>
            <if test="createman != null">#{createman},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="updatedate != null">#{updatedate},</if>
            <if test="updateman != null">#{updateman},</if>
            <if test="zgflag != null">#{zgflag},</if>
            <if test="zgdepartment != null">#{zgdepartment},</if>
            <if test="zgdepartmentcode != null">#{zgdepartmentcode},</if>
            <if test="taskcode != null">#{taskcode},</if>
            <if test="zgmaterialname != null">#{zgmaterialname},</if>
            <if test="zgmaterialnamecode != null">#{zgmaterialnamecode},</if>
         </trim>
    </insert>

    <update id="updateStoreinKqdb" parameterType="StoreinKqdbMeasure">
        update L_STOREIN_KQDB_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="validflag != null">VALIDFLAG = #{validflag},</if>
            <if test="matchid != null">MATCHID = #{matchid},</if>
            <if test="carno != null">CARNO = #{carno},</if>
            <if test="icno != null">ICNO = #{icno},</if>
            <if test="operatype != null">OPERATYPE = #{operatype},</if>
            <if test="planid != null">PLANID = #{planid},</if>
            <if test="storecode != null">STORECODE = #{storecode},</if>
            <if test="storename != null">STORENAME = #{storename},</if>
            <if test="storepos != null">STOREPOS = #{storepos},</if>
            <if test="sourcecode != null">SOURCECODE = #{sourcecode},</if>
            <if test="sourcename != null">SOURCENAME = #{sourcename},</if>
            <if test="materialcode != null">MATERIALCODE = #{materialcode},</if>
            <if test="materialname != null">MATERIALNAME = #{materialname},</if>
            <if test="materialspeccode != null">MATERIALSPECCODE = #{materialspeccode},</if>
            <if test="materialspec != null">MATERIALSPEC = #{materialspec},</if>
            <if test="weight != null">WEIGHT = #{weight},</if>
            <if test="counts != null">COUNTS = #{counts},</if>
            <if test="heatno != null">HEATNO = #{heatno},</if>
            <if test="steelevel != null">STEELLEVEL = #{steelevel},</if>
            <if test="steelgrade != null">STEELGRADE = #{steelgrade},</if>
            <if test="transitway != null">TRANSITWAY = #{transitway},</if>
            <if test="memo != null">MEMO = #{memo},</if>
            <if test="createman != null">CREATEMAN = #{createman},</if>
            <if test="createdate != null">CREATEDATE = #{createdate},</if>
            <if test="updatedate != null">UPDATEDATE = #{updatedate},</if>
            <if test="updateman != null">UPDATEMAN = #{updateman},</if>
            <if test="zgflag != null">ZGFLAG = #{zgflag},</if>
            <if test="zgdepartment != null">ZGDEPARTMENT = #{zgdepartment},</if>
            <if test="zgdepartmentcode != null">ZGDEPARTMENTCODE = #{zgdepartmentcode},</if>
            <if test="taskcode != null">TASKCODE = #{taskcode},</if>
            <if test="zgmaterialname != null">ZGMATERIALNAME = #{zgmaterialname},</if>
            <if test="zgmaterialnamecode != null">ZGMATERIALNAMECODE = #{zgmaterialnamecode},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteStoreinKqdbById" parameterType="Long">
        delete from L_STOREIN_KQDB_T where ID = #{id}
    </delete>

    <delete id="deleteStoreinKqdbByIds" parameterType="String">
        delete from L_STOREIN_KQDB_T where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteStoreinKqdbByMatchid" parameterType="String">
        delete from L_STOREIN_KQDB_T where MATCHID = #{matchid}
    </delete>
</mapper> 