<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeaveLogMapper">
    
    <resultMap type="LeaveLog" id="LeaveLogResult">
        <result property="id"    column="id"    />
        <result property="logType"    column="log_type"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="taskNo"    column="task_no"    />
        <result property="info"    column="info"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectLeaveLogVo">
        select id, log_type, apply_no, task_no, info, create_time, create_by from leave_log
    </sql>

    <select id="selectLeaveLogList" parameterType="LeaveLog" resultMap="LeaveLogResult">
        <include refid="selectLeaveLogVo"/>
        <where>  
            <if test="logType != null "> and log_type = #{logType}</if>
            <if test="applyNo != null "> and apply_no = #{applyNo}</if>
            <if test="taskNo != null  and taskNo != ''"> and task_no = #{taskNo}</if>
            <if test="info != null  and info != ''"> and info = #{info}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectLeaveLogById" parameterType="Long" resultMap="LeaveLogResult">
        <include refid="selectLeaveLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLeaveLog" parameterType="LeaveLog" useGeneratedKeys="true" keyProperty="id">
        insert into leave_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logType != null">log_type,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="taskNo != null">task_no,</if>
            <if test="info != null">info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logType != null">#{logType},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="taskNo != null">#{taskNo},</if>
            <if test="info != null">#{info},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateLeaveLog" parameterType="LeaveLog">
        update leave_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="logType != null">log_type = #{logType},</if>
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="taskNo != null">task_no = #{taskNo},</if>
            <if test="info != null">info = #{info},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveLogById" parameterType="Long">
        delete from leave_log where id = #{id}
    </delete>

    <delete id="deleteLeaveLogByIds" parameterType="String">
        delete from leave_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectLeaveLogByApplyNo" parameterType="String" resultMap="LeaveLogResult">
        <include refid="selectLeaveLogVo"/>
        where apply_no = #{applyNo} order by create_time desc
    </select>
    
</mapper>