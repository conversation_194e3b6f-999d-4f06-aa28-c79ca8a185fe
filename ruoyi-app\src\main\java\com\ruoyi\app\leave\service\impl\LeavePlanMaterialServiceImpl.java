package com.ruoyi.app.leave.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeavePlanMaterialMapper;
import com.ruoyi.app.leave.domain.LeavePlanMaterial;
import com.ruoyi.app.leave.service.ILeavePlanMaterialService;

/**
 * 出门证计划申请物资Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeavePlanMaterialServiceImpl implements ILeavePlanMaterialService 
{
    @Autowired
    private LeavePlanMaterialMapper leavePlanMaterialMapper;

    /**
     * 查询出门证计划申请物资
     * 
     * @param id 出门证计划申请物资ID
     * @return 出门证计划申请物资
     */
    @Override
    public LeavePlanMaterial selectLeavePlanMaterialById(Long id)
    {
        return leavePlanMaterialMapper.selectLeavePlanMaterialById(id);
    }

    /**
     * 查询出门证计划申请物资列表
     * 
     * @param leavePlanMaterial 出门证计划申请物资
     * @return 出门证计划申请物资
     */
    @Override
    public List<LeavePlanMaterial> selectLeavePlanMaterialList(LeavePlanMaterial leavePlanMaterial)
    {
        return leavePlanMaterialMapper.selectLeavePlanMaterialList(leavePlanMaterial);
    }

    /**
     * 新增出门证计划申请物资
     * 
     * @param leavePlanMaterial 出门证计划申请物资
     * @return 结果
     */
    @Override
    public int insertLeavePlanMaterial(LeavePlanMaterial leavePlanMaterial)
    {
        leavePlanMaterial.setCreateTime(DateUtils.getNowDate());
        return leavePlanMaterialMapper.insertLeavePlanMaterial(leavePlanMaterial);
    }

    /**
     * 修改出门证计划申请物资
     * 
     * @param leavePlanMaterial 出门证计划申请物资
     * @return 结果
     */
    @Override
    public int updateLeavePlanMaterial(LeavePlanMaterial leavePlanMaterial)
    {
        leavePlanMaterial.setUpdateTime(DateUtils.getNowDate());
        return leavePlanMaterialMapper.updateLeavePlanMaterial(leavePlanMaterial);
    }

    /**
     * 批量删除出门证计划申请物资
     * 
     * @param ids 需要删除的出门证计划申请物资ID
     * @return 结果
     */
    @Override
    public int deleteLeavePlanMaterialByIds(Long[] ids)
    {
        return leavePlanMaterialMapper.deleteLeavePlanMaterialByIds(ids);
    }

    /**
     * 删除出门证计划申请物资信息
     * 
     * @param id 出门证计划申请物资ID
     * @return 结果
     */
    @Override
    public int deleteLeavePlanMaterialById(Long id)
    {
        return leavePlanMaterialMapper.deleteLeavePlanMaterialById(id);
    }

    /**
     * 根据申请编号删除出门证计划申请物资
     * 
     * @param applyNo 申请编号
     * @return 结果
     */
    @Override
    public int deleteLeavePlanMaterialByApplyNo(String applyNo)
    {
        return leavePlanMaterialMapper.deleteLeavePlanMaterialByApplyNo(applyNo);
    }
}
