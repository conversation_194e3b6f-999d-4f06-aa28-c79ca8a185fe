<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.StoreoutWwjgMapper">
    
    <resultMap type="StoreoutWwjgMeasure" id="StoreoutWwjgResult">
        <result property="id"    column="ID"    />
        <result property="validflag"    column="VALIDFLAG"    />
        <result property="matchid"    column="MATCHID"    />
        <result property="carno"    column="CARNO"    />
        <result property="icno"    column="ICNO"    />
        <result property="operatype"    column="OPERATYPE"    />
        <result property="planid"    column="PLANID"    />
        <result property="sourcecode"    column="SOURCECODE"    />
        <result property="sourcename"    column="SOURCENAME"    />
        <result property="storepos"    column="STOREPOS"    />
        <result property="targetcode"    column="TARGETCODE"    />
        <result property="targetname"    column="TARGETNAME"    />
        <result property="materialcode"    column="MATERIALCODE"    />
        <result property="materialname"    column="MATERIALNAME"    />
        <result property="materialspeccode"    column="MATERIALSPECCODE"    />
        <result property="materialspec"    column="MATERIALSPEC"    />
        <result property="weight"    column="WEIGHT"    />
        <result property="counts"    column="COUNTS"    />
        <result property="heatno"    column="HEATNO"    />
        <result property="steelevel"    column="STEELLEVEL"    />
        <result property="steelgrade"    column="STEELGRADE"    />
        <result property="transitway"    column="TRANSITWAY"    />
        <result property="memo"    column="MEMO"    />
        <result property="createman"    column="CREATEMAN"    />
        <result property="createdate"    column="CREATEDATE"    />
        <result property="updatedate"    column="UPDATEDATE"    />
        <result property="updateman"    column="UPDATEMAN"    />
        <result property="matno"    column="MATNO"    />
        <result property="spec"    column="SPEC"    />
        <result property="lengcd"    column="LENGCD"    />
        <result property="jgstyle"    column="JGSTYLE"    />
        <result property="matno1"    column="MATNO1"    />
        <result property="matno2"    column="MATNO2"    />
        <result property="matno3"    column="MATNO3"    />
        <result property="spec1"    column="SPEC1"    />
        <result property="spec2"    column="SPEC2"    />
    </resultMap>

    <sql id="selectStoreoutWwjgVo">
        select ID, VALIDFLAG, MATCHID, CARNO, ICNO, OPERATYPE, PLANID, SOURCECODE, SOURCENAME, STOREPOS, TARGETCODE, TARGETNAME, MATERIALCODE, MATERIALNAME, MATERIALSPECCODE, MATERIALSPEC, WEIGHT, COUNTS, HEATNO, STEELLEVEL, STEELGRADE, TRANSITWAY, MEMO, CREATEMAN, CREATEDATE, UPDATEDATE, UPDATEMAN, MATNO, SPEC, LENGCD, JGSTYLE, MATNO1, MATNO2, MATNO3, SPEC1, SPEC2 from L_STOREOUT_WWJG_T
    </sql>

    <select id="selectStoreoutWwjgList" parameterType="StoreoutWwjgMeasure" resultMap="StoreoutWwjgResult">
        <include refid="selectStoreoutWwjgVo"/>
        <where>  
            <if test="validflag != null "> and VALIDFLAG = #{validflag}</if>
            <if test="matchid != null  and matchid != ''"> and MATCHID = #{matchid}</if>
            <if test="carno != null  and carno != ''"> and CARNO = #{carno}</if>
            <if test="icno != null  and icno != ''"> and ICNO = #{icno}</if>
            <if test="operatype != null  and operatype != ''"> and OPERATYPE = #{operatype}</if>
            <if test="planid != null  and planid != ''"> and PLANID = #{planid}</if>
            <if test="sourcecode != null  and sourcecode != ''"> and SOURCECODE = #{sourcecode}</if>
            <if test="sourcename != null  and sourcename != ''"> and SOURCENAME like concat('%', #{sourcename}, '%')</if>
            <if test="storepos != null  and storepos != ''"> and STOREPOS = #{storepos}</if>
            <if test="targetcode != null  and targetcode != ''"> and TARGETCODE = #{targetcode}</if>
            <if test="targetname != null  and targetname != ''"> and TARGETNAME like concat('%', #{targetname}, '%')</if>
            <if test="materialcode != null  and materialcode != ''"> and MATERIALCODE = #{materialcode}</if>
            <if test="materialname != null  and materialname != ''"> and MATERIALNAME like concat('%', #{materialname}, '%')</if>
            <if test="materialspeccode != null  and materialspeccode != ''"> and MATERIALSPECCODE = #{materialspeccode}</if>
            <if test="materialspec != null  and materialspec != ''"> and MATERIALSPEC = #{materialspec}</if>
            <if test="weight != null "> and WEIGHT = #{weight}</if>
            <if test="counts != null "> and COUNTS = #{counts}</if>
            <if test="heatno != null  and heatno != ''"> and HEATNO = #{heatno}</if>
            <if test="steelevel != null  and steelevel != ''"> and STEELLEVEL = #{steelevel}</if>
            <if test="steelgrade != null  and steelgrade != ''"> and STEELGRADE = #{steelgrade}</if>
            <if test="transitway != null  and transitway != ''"> and TRANSITWAY = #{transitway}</if>
            <if test="memo != null  and memo != ''"> and MEMO = #{memo}</if>
            <if test="createman != null  and createman != ''"> and CREATEMAN = #{createman}</if>
            <if test="createdate != null "> and CREATEDATE = #{createdate}</if>
            <if test="updatedate != null "> and UPDATEDATE = #{updatedate}</if>
            <if test="updateman != null  and updateman != ''"> and UPDATEMAN = #{updateman}</if>
            <if test="matno != null  and matno != ''"> and MATNO = #{matno}</if>
            <if test="spec != null  and spec != ''"> and SPEC = #{spec}</if>
            <if test="lengcd != null  and lengcd != ''"> and LENGCD = #{lengcd}</if>
            <if test="jgstyle != null  and jgstyle != ''"> and JGSTYLE = #{jgstyle}</if>
            <if test="matno1 != null  and matno1 != ''"> and MATNO1 = #{matno1}</if>
            <if test="matno2 != null  and matno2 != ''"> and MATNO2 = #{matno2}</if>
            <if test="matno3 != null  and matno3 != ''"> and MATNO3 = #{matno3}</if>
            <if test="spec1 != null  and spec1 != ''"> and SPEC1 = #{spec1}</if>
            <if test="spec2 != null  and spec2 != ''"> and SPEC2 = #{spec2}</if>
        </where>
    </select>
    
    <select id="selectStoreoutWwjgById" parameterType="Long" resultMap="StoreoutWwjgResult">
        <include refid="selectStoreoutWwjgVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertStoreoutWwjg" parameterType="StoreoutWwjgMeasure">
        insert into L_STOREOUT_WWJG_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="validflag != null">VALIDFLAG,</if>
            <if test="matchid != null">MATCHID,</if>
            <if test="carno != null">CARNO,</if>
            <if test="icno != null">ICNO,</if>
            <if test="operatype != null">OPERATYPE,</if>
            <if test="planid != null">PLANID,</if>
            <if test="sourcecode != null">SOURCECODE,</if>
            <if test="sourcename != null">SOURCENAME,</if>
            <if test="storepos != null">STOREPOS,</if>
            <if test="targetcode != null">TARGETCODE,</if>
            <if test="targetname != null">TARGETNAME,</if>
            <if test="materialcode != null">MATERIALCODE,</if>
            <if test="materialname != null">MATERIALNAME,</if>
            <if test="materialspeccode != null">MATERIALSPECCODE,</if>
            <if test="materialspec != null">MATERIALSPEC,</if>
            <if test="weight != null">WEIGHT,</if>
            <if test="counts != null">COUNTS,</if>
            <if test="heatno != null">HEATNO,</if>
            <if test="steelevel != null">STEELLEVEL,</if>
            <if test="steelgrade != null">STEELGRADE,</if>
            <if test="transitway != null">TRANSITWAY,</if>
            <if test="memo != null">MEMO,</if>
            <if test="createman != null">CREATEMAN,</if>
            <if test="createdate != null">CREATEDATE,</if>
            <if test="updatedate != null">UPDATEDATE,</if>
            <if test="updateman != null">UPDATEMAN,</if>
            <if test="matno != null">MATNO,</if>
            <if test="spec != null">SPEC,</if>
            <if test="lengcd != null">LENGCD,</if>
            <if test="jgstyle != null">JGSTYLE,</if>
            <if test="matno1 != null">MATNO1,</if>
            <if test="matno2 != null">MATNO2,</if>
            <if test="matno3 != null">MATNO3,</if>
            <if test="spec1 != null">SPEC1,</if>
            <if test="spec2 != null">SPEC2,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="validflag != null">#{validflag},</if>
            <if test="matchid != null">#{matchid},</if>
            <if test="carno != null">#{carno},</if>
            <if test="icno != null">#{icno},</if>
            <if test="operatype != null">#{operatype},</if>
            <if test="planid != null">#{planid},</if>
            <if test="sourcecode != null">#{sourcecode},</if>
            <if test="sourcename != null">#{sourcename},</if>
            <if test="storepos != null">#{storepos},</if>
            <if test="targetcode != null">#{targetcode},</if>
            <if test="targetname != null">#{targetname},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspeccode != null">#{materialspeccode},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="weight != null">#{weight},</if>
            <if test="counts != null">#{counts},</if>
            <if test="heatno != null">#{heatno},</if>
            <if test="steelevel != null">#{steelevel},</if>
            <if test="steelgrade != null">#{steelgrade},</if>
            <if test="transitway != null">#{transitway},</if>
            <if test="memo != null">#{memo},</if>
            <if test="createman != null">#{createman},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="updatedate != null">#{updatedate},</if>
            <if test="updateman != null">#{updateman},</if>
            <if test="matno != null">#{matno},</if>
            <if test="spec != null">#{spec},</if>
            <if test="lengcd != null">#{lengcd},</if>
            <if test="jgstyle != null">#{jgstyle},</if>
            <if test="matno1 != null">#{matno1},</if>
            <if test="matno2 != null">#{matno2},</if>
            <if test="matno3 != null">#{matno3},</if>
            <if test="spec1 != null">#{spec1},</if>
            <if test="spec2 != null">#{spec2},</if>
         </trim>
    </insert>

    <update id="updateStoreoutWwjg" parameterType="StoreoutWwjgMeasure">
        update L_STOREOUT_WWJG_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="validflag != null">VALIDFLAG = #{validflag},</if>
            <if test="matchid != null">MATCHID = #{matchid},</if>
            <if test="carno != null">CARNO = #{carno},</if>
            <if test="icno != null">ICNO = #{icno},</if>
            <if test="operatype != null">OPERATYPE = #{operatype},</if>
            <if test="planid != null">PLANID = #{planid},</if>
            <if test="sourcecode != null">SOURCECODE = #{sourcecode},</if>
            <if test="sourcename != null">SOURCENAME = #{sourcename},</if>
            <if test="storepos != null">STOREPOS = #{storepos},</if>
            <if test="targetcode != null">TARGETCODE = #{targetcode},</if>
            <if test="targetname != null">TARGETNAME = #{targetname},</if>
            <if test="materialcode != null">MATERIALCODE = #{materialcode},</if>
            <if test="materialname != null">MATERIALNAME = #{materialname},</if>
            <if test="materialspeccode != null">MATERIALSPECCODE = #{materialspeccode},</if>
            <if test="materialspec != null">MATERIALSPEC = #{materialspec},</if>
            <if test="weight != null">WEIGHT = #{weight},</if>
            <if test="counts != null">COUNTS = #{counts},</if>
            <if test="heatno != null">HEATNO = #{heatno},</if>
            <if test="steelevel != null">STEELLEVEL = #{steelevel},</if>
            <if test="steelgrade != null">STEELGRADE = #{steelgrade},</if>
            <if test="transitway != null">TRANSITWAY = #{transitway},</if>
            <if test="memo != null">MEMO = #{memo},</if>
            <if test="createman != null">CREATEMAN = #{createman},</if>
            <if test="createdate != null">CREATEDATE = #{createdate},</if>
            <if test="updatedate != null">UPDATEDATE = #{updatedate},</if>
            <if test="updateman != null">UPDATEMAN = #{updateman},</if>
            <if test="matno != null">MATNO = #{matno},</if>
            <if test="spec != null">SPEC = #{spec},</if>
            <if test="lengcd != null">LENGCD = #{lengcd},</if>
            <if test="jgstyle != null">JGSTYLE = #{jgstyle},</if>
            <if test="matno1 != null">MATNO1 = #{matno1},</if>
            <if test="matno2 != null">MATNO2 = #{matno2},</if>
            <if test="matno3 != null">MATNO3 = #{matno3},</if>
            <if test="spec1 != null">SPEC1 = #{spec1},</if>
            <if test="spec2 != null">SPEC2 = #{spec2},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteStoreoutWwjgById" parameterType="Long">
        delete from L_STOREOUT_WWJG_T where ID = #{id}
    </delete>

    <delete id="deleteStoreoutWwjgByIds" parameterType="String">
        delete from L_STOREOUT_WWJG_T where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteStoreoutWwjgByMatchid" parameterType="String">
        delete from L_STOREOUT_WWJG_T where MATCHID = #{matchid}
    </delete>
</mapper> 