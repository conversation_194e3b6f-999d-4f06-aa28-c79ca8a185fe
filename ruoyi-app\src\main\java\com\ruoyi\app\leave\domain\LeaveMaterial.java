package com.ruoyi.app.leave.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出门证物资对象 leave_material
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public class LeaveMaterial extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 物资名称 */
    @Excel(name = "物资名称")
    private String materialName;

    /** 物资型号规格 */
    @Excel(name = "物资型号规格")
    private String materialSpec;

    /** 拼音头 */
    @Excel(name = "拼音头")
    private String queryWord;

    /** 计量单位 */
    @Excel(name = "计量单位")
    private String measureUnit;

    /** 是否计量 只计量-1 只质检-0 */
    @Excel(name = "是否计量 只计量-1 只质检-0")
    private Integer measureFlag;

    /** 是否贵重金属  是-1  否-0 */
    @Excel(name = "是否贵重金属  是-1  否-0")
    private Integer materialType;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMaterialName(String materialName) 
    {
        this.materialName = materialName;
    }

    public String getMaterialName() 
    {
        return materialName;
    }
    public void setMaterialSpec(String materialSpec) 
    {
        this.materialSpec = materialSpec;
    }

    public String getMaterialSpec() 
    {
        return materialSpec;
    }
    public void setMeasureUnit(String measureUnit) 
    {
        this.measureUnit = measureUnit;
    }

    public String getMeasureUnit() 
    {
        return measureUnit;
    }
    public void setMeasureFlag(Integer measureFlag) 
    {
        this.measureFlag = measureFlag;
    }

    public Integer getMeasureFlag() 
    {
        return measureFlag;
    }

    public String getQueryWord() {
        return queryWord;
    }

    public void setQueryWord(String queryWord) {
        this.queryWord = queryWord;
    }

    public Integer getMaterialType() {
        return materialType;
    }

    public void setMaterialType(Integer materialType) {
        this.materialType = materialType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("materialName", getMaterialName())
            .append("materialSpec", getMaterialSpec())
            .append("measureUnit", getMeasureUnit())
            .append("measureFlag", getMeasureFlag())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
                .append("queryWord", getQueryWord())
                .append("materialType", getMaterialType())
            .toString();
    }
}
