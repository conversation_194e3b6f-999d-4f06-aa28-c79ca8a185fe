package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 退货申请主对象 L_PASS_TH_T
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public class LPassThT extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 有效标记：1正常，2出厂，0作废，8完成 */
    @Excel(name = "有效标记：1正常，2出厂，0作废，8完成")
    private Long validflag;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialcode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialname;

    /** 规格 */
    @Excel(name = "规格")
    private String materialspec;

    /** 型号/钢种 */
    @Excel(name = "型号/钢种")
    private String materialtype;

    /** 发货编码 */
    @Excel(name = "发货编码")
    private String sourcecode;

    /** 发货单位 */
    @Excel(name = "发货单位")
    private String sourcename;

    /** 收货编码 */
    @Excel(name = "收货编码")
    private String targetcode;

    /** 收货单位 */
    @Excel(name = "收货单位")
    private String targetname;

    /** 数量 */
    @Excel(name = "数量")
    private Long count;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validtime;

    /** 申请单位编码 */
    @Excel(name = "申请单位编码")
    private String unitcode;

    /** 申请单位名称 */
    @Excel(name = "申请单位名称")
    private String unitname;

    /** 领导审核：0不通过，1未审核，2通过 */
    @Excel(name = "领导审核：0不通过，1未审核，2通过")
    private Long lflag;

    /** 领导 */
    @Excel(name = "领导")
    private String leader;

    /** 领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ldate;

    /** 生产中心审核：0不通过，1未审核，2通过 */
    @Excel(name = "生产中心审核：0不通过，1未审核，2通过")
    private Long pflag;

    /** 生产中心审核人 */
    @Excel(name = "生产中心审核人")
    private String productman;

    /** 生产中心审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生产中心审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pdate;

    /** 打印次数 */
    @Excel(name = "打印次数")
    private Long printnum;

    /** 添加人 */
    @Excel(name = "添加人")
    private String createman;

    /** 添加时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "添加时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdate;

    /** 修改人 */
    @Excel(name = "修改人")
    private String updateman;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedate;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 领导意见 */
    @Excel(name = "领导意见")
    private String leadermemo;

    /** 生产中心意见 */
    @Excel(name = "生产中心意见")
    private String productmemo;

    /** 炉号 */
    @Excel(name = "炉号")
    private String heatno;

    /** 出厂人 */
    @Excel(name = "出厂人")
    private String leaveman;

    /** 出厂日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出厂日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date leavedate;

    /** 出厂大门 */
    @Excel(name = "出厂大门")
    private String leavegate;

    /** 监装人 */
    @Excel(name = "监装人")
    private String supervisor;

    /** 出厂原因 */
    @Excel(name = "出厂原因")
    private String outmemo;

    /** 车号 */
    @Excel(name = "车号")
    private String carno;

    /** 附件路径 */
    @Excel(name = "附件路径")
    private String attachpath;

    /** 打印出门证次数 */
    @Excel(name = "打印出门证次数")
    private Long printnump;

    /** 出门证号 字母+年份+数字 */
    @Excel(name = "出门证号 字母+年份+数字")
    private String cmzhao;

    /** 正常0 过期 10 */
    @Excel(name = "正常0 过期 10")
    private Long yxflag;

    /** 有效天数默认为3天 4；5；6（可能性比较小） */
    @Excel(name = "有效天数默认为3天 4；5；6", readConverterExp = "可=能性比较小")
    private Long yxday;

    /** 物资专管员 */
    @Excel(name = "物资专管员")
    private String wzzgy;

    /** 申请号 */
    @Excel(name = "申请号")
    private String applyid;

    /** 是否需要计量：0不需要，1需要 */
    @Excel(name = "是否需要计量：0不需要，1需要")
    private Long measureflag;

    /** 0滨江厂区1花山厂区 */
    @Excel(name = "0滨江厂区1花山厂区")
    private Long hsflag;

    /** 0不可以合并审核，1需要合并审核 */
    @Excel(name = "0不可以合并审核，1需要合并审核")
    private Long shmore;

    /** 修改有效期原因 */
    @Excel(name = "修改有效期原因")
    private String yxqmemo;

    /** 0为不做处理，1采购退货 */
    @Excel(name = "0为不做处理，1采购退货")
    private Long thflag;

    /** 采购退货单位 */
    @Excel(name = "采购退货单位")
    private String thdwname;

    /** 采购退货单位code */
    @Excel(name = "采购退货单位code")
    private String thdwcode;

    /** 作废原因 */
    @Excel(name = "作废原因")
    private String cancelmemo;

    /** 0可以打印1不可以打印 */
    @Excel(name = "0可以打印1不可以打印")
    private Long printflag;

    /** 审核补打时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核补打时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date printflagdate;

    /** 补打原因 */
    @Excel(name = "补打原因")
    private String bdmemo;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uptime;

    /** 更新时间戳 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间戳", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uptimestamp;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setValidflag(Long validflag) 
    {
        this.validflag = validflag;
    }

    public Long getValidflag() 
    {
        return validflag;
    }
    public void setMaterialcode(String materialcode) 
    {
        this.materialcode = materialcode;
    }

    public String getMaterialcode() 
    {
        return materialcode;
    }
    public void setMaterialname(String materialname) 
    {
        this.materialname = materialname;
    }

    public String getMaterialname() 
    {
        return materialname;
    }
    public void setMaterialspec(String materialspec) 
    {
        this.materialspec = materialspec;
    }

    public String getMaterialspec() 
    {
        return materialspec;
    }
    public void setMaterialtype(String materialtype) 
    {
        this.materialtype = materialtype;
    }

    public String getMaterialtype() 
    {
        return materialtype;
    }
    public void setSourcecode(String sourcecode) 
    {
        this.sourcecode = sourcecode;
    }

    public String getSourcecode() 
    {
        return sourcecode;
    }
    public void setSourcename(String sourcename) 
    {
        this.sourcename = sourcename;
    }

    public String getSourcename() 
    {
        return sourcename;
    }
    public void setTargetcode(String targetcode) 
    {
        this.targetcode = targetcode;
    }

    public String getTargetcode() 
    {
        return targetcode;
    }
    public void setTargetname(String targetname) 
    {
        this.targetname = targetname;
    }

    public String getTargetname() 
    {
        return targetname;
    }
    public void setCount(Long count) 
    {
        this.count = count;
    }

    public Long getCount() 
    {
        return count;
    }
    public void setWeight(BigDecimal weight) 
    {
        this.weight = weight;
    }

    public BigDecimal getWeight() 
    {
        return weight;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setValidtime(Date validtime) 
    {
        this.validtime = validtime;
    }

    public Date getValidtime() 
    {
        return validtime;
    }
    public void setUnitcode(String unitcode) 
    {
        this.unitcode = unitcode;
    }

    public String getUnitcode() 
    {
        return unitcode;
    }
    public void setUnitname(String unitname) 
    {
        this.unitname = unitname;
    }

    public String getUnitname() 
    {
        return unitname;
    }
    public void setLflag(Long lflag) 
    {
        this.lflag = lflag;
    }

    public Long getLflag() 
    {
        return lflag;
    }
    public void setLeader(String leader) 
    {
        this.leader = leader;
    }

    public String getLeader() 
    {
        return leader;
    }
    public void setLdate(Date ldate) 
    {
        this.ldate = ldate;
    }

    public Date getLdate() 
    {
        return ldate;
    }
    public void setPflag(Long pflag) 
    {
        this.pflag = pflag;
    }

    public Long getPflag() 
    {
        return pflag;
    }
    public void setProductman(String productman) 
    {
        this.productman = productman;
    }

    public String getProductman() 
    {
        return productman;
    }
    public void setPdate(Date pdate) 
    {
        this.pdate = pdate;
    }

    public Date getPdate() 
    {
        return pdate;
    }
    public void setPrintnum(Long printnum) 
    {
        this.printnum = printnum;
    }

    public Long getPrintnum() 
    {
        return printnum;
    }
    public void setCreateman(String createman) 
    {
        this.createman = createman;
    }

    public String getCreateman() 
    {
        return createman;
    }
    public void setCreatedate(Date createdate) 
    {
        this.createdate = createdate;
    }

    public Date getCreatedate() 
    {
        return createdate;
    }
    public void setUpdateman(String updateman) 
    {
        this.updateman = updateman;
    }

    public String getUpdateman() 
    {
        return updateman;
    }
    public void setUpdatedate(Date updatedate) 
    {
        this.updatedate = updatedate;
    }

    public Date getUpdatedate() 
    {
        return updatedate;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setLeadermemo(String leadermemo) 
    {
        this.leadermemo = leadermemo;
    }

    public String getLeadermemo() 
    {
        return leadermemo;
    }
    public void setProductmemo(String productmemo) 
    {
        this.productmemo = productmemo;
    }

    public String getProductmemo() 
    {
        return productmemo;
    }
    public void setHeatno(String heatno) 
    {
        this.heatno = heatno;
    }

    public String getHeatno() 
    {
        return heatno;
    }
    public void setLeaveman(String leaveman) 
    {
        this.leaveman = leaveman;
    }

    public String getLeaveman() 
    {
        return leaveman;
    }
    public void setLeavedate(Date leavedate) 
    {
        this.leavedate = leavedate;
    }

    public Date getLeavedate() 
    {
        return leavedate;
    }
    public void setLeavegate(String leavegate) 
    {
        this.leavegate = leavegate;
    }

    public String getLeavegate() 
    {
        return leavegate;
    }
    public void setSupervisor(String supervisor) 
    {
        this.supervisor = supervisor;
    }

    public String getSupervisor() 
    {
        return supervisor;
    }
    public void setOutmemo(String outmemo) 
    {
        this.outmemo = outmemo;
    }

    public String getOutmemo() 
    {
        return outmemo;
    }
    public void setCarno(String carno) 
    {
        this.carno = carno;
    }

    public String getCarno() 
    {
        return carno;
    }
    public void setAttachpath(String attachpath) 
    {
        this.attachpath = attachpath;
    }

    public String getAttachpath() 
    {
        return attachpath;
    }
    public void setPrintnump(Long printnump) 
    {
        this.printnump = printnump;
    }

    public Long getPrintnump() 
    {
        return printnump;
    }
    public void setCmzhao(String cmzhao) 
    {
        this.cmzhao = cmzhao;
    }

    public String getCmzhao() 
    {
        return cmzhao;
    }
    public void setYxflag(Long yxflag) 
    {
        this.yxflag = yxflag;
    }

    public Long getYxflag() 
    {
        return yxflag;
    }
    public void setYxday(Long yxday) 
    {
        this.yxday = yxday;
    }

    public Long getYxday() 
    {
        return yxday;
    }
    public void setWzzgy(String wzzgy) 
    {
        this.wzzgy = wzzgy;
    }

    public String getWzzgy() 
    {
        return wzzgy;
    }
    public void setApplyid(String applyid) 
    {
        this.applyid = applyid;
    }

    public String getApplyid() 
    {
        return applyid;
    }
    public void setMeasureflag(Long measureflag) 
    {
        this.measureflag = measureflag;
    }

    public Long getMeasureflag() 
    {
        return measureflag;
    }
    public void setHsflag(Long hsflag) 
    {
        this.hsflag = hsflag;
    }

    public Long getHsflag() 
    {
        return hsflag;
    }
    public void setShmore(Long shmore) 
    {
        this.shmore = shmore;
    }

    public Long getShmore() 
    {
        return shmore;
    }
    public void setYxqmemo(String yxqmemo) 
    {
        this.yxqmemo = yxqmemo;
    }

    public String getYxqmemo() 
    {
        return yxqmemo;
    }
    public void setThflag(Long thflag) 
    {
        this.thflag = thflag;
    }

    public Long getThflag() 
    {
        return thflag;
    }
    public void setThdwname(String thdwname) 
    {
        this.thdwname = thdwname;
    }

    public String getThdwname() 
    {
        return thdwname;
    }
    public void setThdwcode(String thdwcode) 
    {
        this.thdwcode = thdwcode;
    }

    public String getThdwcode() 
    {
        return thdwcode;
    }
    public void setCancelmemo(String cancelmemo) 
    {
        this.cancelmemo = cancelmemo;
    }

    public String getCancelmemo() 
    {
        return cancelmemo;
    }
    public void setPrintflag(Long printflag) 
    {
        this.printflag = printflag;
    }

    public Long getPrintflag() 
    {
        return printflag;
    }
    public void setPrintflagdate(Date printflagdate) 
    {
        this.printflagdate = printflagdate;
    }

    public Date getPrintflagdate() 
    {
        return printflagdate;
    }
    public void setBdmemo(String bdmemo) 
    {
        this.bdmemo = bdmemo;
    }

    public String getBdmemo() 
    {
        return bdmemo;
    }
    public void setUptime(Date uptime) 
    {
        this.uptime = uptime;
    }

    public Date getUptime() 
    {
        return uptime;
    }
    public void setUptimestamp(Date uptimestamp) 
    {
        this.uptimestamp = uptimestamp;
    }

    public Date getUptimestamp() 
    {
        return uptimestamp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("validflag", getValidflag())
            .append("materialcode", getMaterialcode())
            .append("materialname", getMaterialname())
            .append("materialspec", getMaterialspec())
            .append("materialtype", getMaterialtype())
            .append("sourcecode", getSourcecode())
            .append("sourcename", getSourcename())
            .append("targetcode", getTargetcode())
            .append("targetname", getTargetname())
            .append("count", getCount())
            .append("weight", getWeight())
            .append("unit", getUnit())
            .append("validtime", getValidtime())
            .append("unitcode", getUnitcode())
            .append("unitname", getUnitname())
            .append("lflag", getLflag())
            .append("leader", getLeader())
            .append("ldate", getLdate())
            .append("pflag", getPflag())
            .append("productman", getProductman())
            .append("pdate", getPdate())
            .append("printnum", getPrintnum())
            .append("createman", getCreateman())
            .append("createdate", getCreatedate())
            .append("updateman", getUpdateman())
            .append("updatedate", getUpdatedate())
            .append("memo", getMemo())
            .append("leadermemo", getLeadermemo())
            .append("productmemo", getProductmemo())
            .append("heatno", getHeatno())
            .append("leaveman", getLeaveman())
            .append("leavedate", getLeavedate())
            .append("leavegate", getLeavegate())
            .append("supervisor", getSupervisor())
            .append("outmemo", getOutmemo())
            .append("carno", getCarno())
            .append("attachpath", getAttachpath())
            .append("printnump", getPrintnump())
            .append("cmzhao", getCmzhao())
            .append("yxflag", getYxflag())
            .append("yxday", getYxday())
            .append("wzzgy", getWzzgy())
            .append("applyid", getApplyid())
            .append("measureflag", getMeasureflag())
            .append("hsflag", getHsflag())
            .append("shmore", getShmore())
            .append("yxqmemo", getYxqmemo())
            .append("thflag", getThflag())
            .append("thdwname", getThdwname())
            .append("thdwcode", getThdwcode())
            .append("cancelmemo", getCancelmemo())
            .append("printflag", getPrintflag())
            .append("printflagdate", getPrintflagdate())
            .append("bdmemo", getBdmemo())
            .append("uptime", getUptime())
            .append("uptimestamp", getUptimestamp())
            .toString();
    }
}
