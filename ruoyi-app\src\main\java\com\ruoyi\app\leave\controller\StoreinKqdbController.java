package com.ruoyi.app.leave.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.StoreinKqdbMeasure;
import com.ruoyi.app.leave.service.IStoreinKqdbService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 库区调拨入库Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/leave/storeinkqdb")
public class StoreinKqdbController extends BaseController
{
    @Autowired
    private IStoreinKqdbService storeinKqdbService;

    /**
     * 查询库区调拨入库列表
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinkqdb:list')")
    @GetMapping("/list")
    public TableDataInfo list(StoreinKqdbMeasure storeinKqdbMeasure)
    {
        startPage();
        List<StoreinKqdbMeasure> list = storeinKqdbService.selectStoreinKqdbList(storeinKqdbMeasure);
        return getDataTable(list);
    }

    /**
     * 导出库区调拨入库列表
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinkqdb:export')")
    @Log(title = "库区调拨入库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StoreinKqdbMeasure storeinKqdbMeasure)
    {
        List<StoreinKqdbMeasure> list = storeinKqdbService.selectStoreinKqdbList(storeinKqdbMeasure);
        ExcelUtil<StoreinKqdbMeasure> util = new ExcelUtil<StoreinKqdbMeasure>(StoreinKqdbMeasure.class);
        util.exportEasyExcel(list, "库区调拨入库数据", StoreinKqdbMeasure.class);
    }

    /**
     * 获取库区调拨入库详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinkqdb:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeinKqdbService.selectStoreinKqdbById(id));
    }

    /**
     * 新增库区调拨入库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinkqdb:add')")
    @Log(title = "库区调拨入库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StoreinKqdbMeasure storeinKqdbMeasure)
    {
        return toAjax(storeinKqdbService.insertStoreinKqdb(storeinKqdbMeasure));
    }

    /**
     * 修改库区调拨入库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinkqdb:edit')")
    @Log(title = "库区调拨入库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StoreinKqdbMeasure storeinKqdbMeasure)
    {
        return toAjax(storeinKqdbService.updateStoreinKqdb(storeinKqdbMeasure));
    }

    /**
     * 删除库区调拨入库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinkqdb:remove')")
    @Log(title = "库区调拨入库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(storeinKqdbService.deleteStoreinKqdbByIds(ids));
    }

    /**
     * 根据matchid删除库区调拨入库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeinkqdb:remove')")
    @Log(title = "库区调拨入库", businessType = BusinessType.DELETE)
    @DeleteMapping("/matchid/{matchid}")
    public AjaxResult removeByMatchid(@PathVariable String matchid)
    {
        return toAjax(storeinKqdbService.deleteStoreinKqdbByMatchid(matchid));
    }
} 