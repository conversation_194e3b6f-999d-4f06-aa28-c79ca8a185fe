package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LPassFhT;

/**
 * 出厂返回主Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface ILPassFhTService 
{
    /**
     * 查询出厂返回主
     * 
     * @param id 出厂返回主ID
     * @return 出厂返回主
     */
    public LPassFhT selectLPassFhTById(Long id);

    /**
     * 查询出厂返回主列表
     * 
     * @param lPassFhT 出厂返回主
     * @return 出厂返回主集合
     */
    public List<LPassFhT> selectLPassFhTList(LPassFhT lPassFhT);

    /**
     * 新增出厂返回主
     * 
     * @param lPassFhT 出厂返回主
     * @return 结果
     */
    public int insertLPassFhT(LPassFhT lPassFhT);

    /**
     * 修改出厂返回主
     * 
     * @param lPassFhT 出厂返回主
     * @return 结果
     */
    public int updateLPassFhT(LPassFhT lPassFhT);

    /**
     * 批量删除出厂返回主
     * 
     * @param ids 需要删除的出厂返回主ID
     * @return 结果
     */
    public int deleteLPassFhTByIds(Long[] ids);

    /**
     * 删除出厂返回主信息
     * 
     * @param id 出厂返回主ID
     * @return 结果
     */
    public int deleteLPassFhTById(Long id);
}
