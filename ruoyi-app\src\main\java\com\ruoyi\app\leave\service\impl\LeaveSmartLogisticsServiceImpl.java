package com.ruoyi.app.leave.service.impl;
import com.google.common.collect.Lists;
import com.ruoyi.app.leave.domain.*;
import com.ruoyi.app.leave.enums.LeavePlanTypeEnum;
import com.ruoyi.app.leave.mapper.LeavePlanMapper;
import com.ruoyi.app.leave.mapper.LeaveTaskMapper;
import com.ruoyi.app.leave.mapper.LeaveTaskMaterialMapper;
import com.ruoyi.app.leave.service.LeaveSmartLogisticsService;
import com.ruoyi.common.utils.FastJsonUtils;
import com.ruoyi.common.utils.http.HttpUtil;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025/3/31 下午3:12
 */
@Component
public class LeaveSmartLogisticsServiceImpl implements LeaveSmartLogisticsService {
    private static final Logger log = LoggerFactory.getLogger(LeaveSmartLogisticsServiceImpl.class);

    //计划信息推送
    public static final String PUSH_PLAN_URL = "https://172.16.64.235:65522/app-transport/api/measure";

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private LeavePlanMapper leavePlanMapper;

    @Autowired
    private LeaveTaskMapper leaveTaskMapper;

    @Autowired
    private LeaveTaskMaterialMapper leaveTaskMaterialMapper;


    @Override
    public void pushPlanInfo(String applyNo,Long[] ids) {
        CompletableFuture.runAsync(() -> {
            try {
                //1.获取出门证信息
                LeavePlan leavePlan = leavePlanMapper.selectLeavePlanByApplyNo(applyNo);

                // 如果 measureFlag 为 0（不计量），则不执行后续操作
                if (leavePlan.getMeasureFlag() == 0) {
                    return;
                }

                LeavePlanTask planTask = new LeavePlanTask();
                // 从 LeavePlan 提取字段
                // 根据 LeavePlanTypeEnum 映射到 OperateTypeEnum
                LeavePlanTypeEnum leavePlanTypeEnum = LeavePlanTypeEnum.getByCode(leavePlan.getPlanType());

                int operaType = -1;
                switch (leavePlanTypeEnum) {
                    case OUT_NO_RETURN:
                        operaType = 12; // 出厂不返回
                        break;
                    case OUT_RETURN:
                        operaType = 11; // 委外加工出门返厂
                        break;
                    case CROSS_AREA_TRANSFER:
                        operaType = 6; // 跨区调拨
                        break;
                    case RETURN_APPLY:
                        operaType = -1; // 退货申请
                        break;
                    default:
                        throw new RuntimeException("未知的计划类型：" + leavePlanTypeEnum);
                }

                // 退货申请不推送到智慧物流
                if (operaType == -1) {
                    return;
                }
                planTask.setOperaType(operaType);
                planTask.setPlanId(leavePlan.getPlanNo());
                planTask.setSourceCode(leavePlan.getSourceCompanyCode());
                planTask.setSourceName(leavePlan.getSourceCompany());
                planTask.setReceiveCode(leavePlan.getReceiveCompanyCode());
                planTask.setReceiveName(leavePlan.getReceiveCompany());
                planTask.setTargetCode(leavePlan.getTargetCompanyCode());
                planTask.setTargetName(leavePlan.getTargetCompany());
                planTask.setMaterialName(leavePlan.getMaterials().get(0).getMaterialName());
                planTask.setMaterialSpec(leavePlan.getMaterials().get(0).getMaterialSpec());
                planTask.setCount(leavePlan.getPlannedAmount().toString());
                planTask.setCreateDate(leavePlan.getApplyTime().toString());
                planTask.setUpdateTime(leavePlan.getUpdateTime().toString());
                planTask.setBeginTime(leavePlan.getStartTime().toString());
                planTask.setEndTime(leavePlan.getEndTime().toString());
                planTask.setPlanAmount(leavePlan.getPlannedAmount().intValue());
                planTask.setValidTime(leavePlan.getExpireTime().toString());
                planTask.setValidFlag(leavePlan.getPlanStatus());
                planTask.setMeasureFlag(leavePlan.getMeasureFlag());
                planTask.setMemo(leavePlan.getReason());
                planTask.setOutMemo(leavePlan.getReason());
                planTask.setPlanReturnDate(leavePlan.getPlanReturnTime().toString());
                planTask.setBusinessType(leavePlan.getPlanType());

                // 标志变量，用于判断是否已经赋值
                boolean isAssigned = false;

                // 从 LeaveTaskMaterial 提取字段
                List<LeavePlanItem> planItemList = new ArrayList<>();
                for (Long id : ids) {
                    LeaveTask leaveTask = leaveTaskMapper.selectLeaveTaskById(id);
                    LeaveTaskMaterial leaveTaskMaterial = leaveTaskMaterialMapper.selectLeaveTaskMaterialById(id);
                    LeavePlanItem item = new LeavePlanItem();

                    // 只在第一次遍历时赋值
                    if (!isAssigned) {
                        planTask.setHeatNo(leaveTask.getHeatNo());
                        planTask.setSteelGrade(leaveTask.getSteelGrade());
                        planTask.setCarNo(leaveTask.getCarNum());
                        isAssigned = true;
                    }

                    item.setId(leaveTaskMaterial.getMaterialId().intValue());
                    item.setMaterialName(leaveTaskMaterial.getMaterialName());
                    item.setMaterialSpec(leaveTaskMaterial.getMaterialSpec());
                    item.setUnit(leaveTaskMaterial.getMeasureUnit());
                    item.setCount(leaveTaskMaterial.getPlanNum());
                    //物资状态 item.setFpFlag()  1 正常 2 确认出厂 3 确认进厂 4收货完成，5门卫接收部分，6分厂接收部分

                    item.setOutDate(leaveTask.getLeaveTime().toString());
                    item.setInDate(leaveTask.getEnterTime().toString());
                    item.setShDate(leaveTask.getLoadingTime().toString());
                    item.setOutDoor(leaveTask.getLeaveDoor().toString());
                    item.setInDoor(leaveTask.getEnterDoor().toString());
                    item.setReceiver(leaveTask.getUnloadingWorkNo());
                    item.setMwshCount(leaveTaskMaterial.getDoormanReceiveNum().intValue());
                    item.setMwshCcountEd(leaveTaskMaterial.getFactoryReceiveNum().intValue());
                    planItemList.add(item);
                }
                planTask.setPlanItemList(planItemList);

                //2.推送出门证信息
                Header[] headers = {new BasicHeader("appKey", "nx23102000"),
                        new BasicHeader("appSecret", "zWhDnVjPsWYH3ZxVEjG8")};
                log.info("智慧物流计划信息推送请求(处理后)："+ FastJsonUtils.toJSON(Lists.newArrayList(planTask)));
                String result = HttpUtil.doPostNoSSLHeader(PUSH_PLAN_URL, FastJsonUtils.toJSON(Lists.newArrayList(planTask)), headers);
                log.info("智慧物流计划信息推送结果："+result);

            } catch (Exception e) {
                log.error("推送出门证信息失败", e);
            }
        }, threadPoolTaskExecutor);
    }
}
