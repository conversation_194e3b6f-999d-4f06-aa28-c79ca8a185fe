package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 库区调拨出库对象 storeout_kqdb
 * 
 * <AUTHOR>
 */
public class StoreoutKqdbMeasure extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 有效标志 */
    @Excel(name = "有效标志")
    private Long validflag;

    /** 匹配ID */
    @Excel(name = "匹配ID")
    private String matchid;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carno;

    /** IC卡号 */
    @Excel(name = "IC卡号")
    private String icno;

    /** 操作类型 */
    @Excel(name = "操作类型")
    private String operatype;

    /** 计划ID */
    @Excel(name = "计划ID")
    private String planid;

    /** 仓库编码 */
    @Excel(name = "仓库编码")
    private String storecode;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    private String storename;

    /** 库位 */
    @Excel(name = "库位")
    private String storepos;

    /** 目标编码 */
    @Excel(name = "目标编码")
    private String targetcode;

    /** 目标名称 */
    @Excel(name = "目标名称")
    private String targetname;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialcode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialname;

    /** 物料规格编码 */
    @Excel(name = "物料规格编码")
    private String materialspeccode;

    /** 物料规格 */
    @Excel(name = "物料规格")
    private String materialspec;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 数量 */
    @Excel(name = "数量")
    private Long counts;

    /** 炉号 */
    @Excel(name = "炉号")
    private String heatno;

    /** 钢级 */
    @Excel(name = "钢级")
    private String steelevel;

    /** 钢种 */
    @Excel(name = "钢种")
    private String steelgrade;

    /** 运输方式 */
    @Excel(name = "运输方式")
    private String transitway;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createman;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedate;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updateman;

    /** 规格 */
    @Excel(name = "规格")
    private String spec;

    /** 物料号 */
    @Excel(name = "物料号")
    private String matno;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setValidflag(Long validflag) 
    {
        this.validflag = validflag;
    }

    public Long getValidflag() 
    {
        return validflag;
    }

    public void setMatchid(String matchid) 
    {
        this.matchid = matchid;
    }

    public String getMatchid() 
    {
        return matchid;
    }

    public void setCarno(String carno) 
    {
        this.carno = carno;
    }

    public String getCarno() 
    {
        return carno;
    }

    public void setIcno(String icno) 
    {
        this.icno = icno;
    }

    public String getIcno() 
    {
        return icno;
    }

    public void setOperatype(String operatype) 
    {
        this.operatype = operatype;
    }

    public String getOperatype() 
    {
        return operatype;
    }

    public void setPlanid(String planid) 
    {
        this.planid = planid;
    }

    public String getPlanid() 
    {
        return planid;
    }

    public void setStorecode(String storecode) 
    {
        this.storecode = storecode;
    }

    public String getStorecode() 
    {
        return storecode;
    }

    public void setStorename(String storename) 
    {
        this.storename = storename;
    }

    public String getStorename() 
    {
        return storename;
    }

    public void setStorepos(String storepos) 
    {
        this.storepos = storepos;
    }

    public String getStorepos() 
    {
        return storepos;
    }

    public void setTargetcode(String targetcode) 
    {
        this.targetcode = targetcode;
    }

    public String getTargetcode() 
    {
        return targetcode;
    }

    public void setTargetname(String targetname) 
    {
        this.targetname = targetname;
    }

    public String getTargetname() 
    {
        return targetname;
    }

    public void setMaterialcode(String materialcode) 
    {
        this.materialcode = materialcode;
    }

    public String getMaterialcode() 
    {
        return materialcode;
    }

    public void setMaterialname(String materialname) 
    {
        this.materialname = materialname;
    }

    public String getMaterialname() 
    {
        return materialname;
    }

    public void setMaterialspeccode(String materialspeccode) 
    {
        this.materialspeccode = materialspeccode;
    }

    public String getMaterialspeccode() 
    {
        return materialspeccode;
    }

    public void setMaterialspec(String materialspec) 
    {
        this.materialspec = materialspec;
    }

    public String getMaterialspec() 
    {
        return materialspec;
    }

    public void setWeight(BigDecimal weight) 
    {
        this.weight = weight;
    }

    public BigDecimal getWeight() 
    {
        return weight;
    }

    public void setCounts(Long counts) 
    {
        this.counts = counts;
    }

    public Long getCounts() 
    {
        return counts;
    }

    public void setHeatno(String heatno) 
    {
        this.heatno = heatno;
    }

    public String getHeatno() 
    {
        return heatno;
    }

    public void setSteelevel(String steelevel) 
    {
        this.steelevel = steelevel;
    }

    public String getSteelevel() 
    {
        return steelevel;
    }

    public void setSteelgrade(String steelgrade) 
    {
        this.steelgrade = steelgrade;
    }

    public String getSteelgrade() 
    {
        return steelgrade;
    }

    public void setTransitway(String transitway) 
    {
        this.transitway = transitway;
    }

    public String getTransitway() 
    {
        return transitway;
    }

    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }

    public void setCreateman(String createman) 
    {
        this.createman = createman;
    }

    public String getCreateman() 
    {
        return createman;
    }

    public void setCreatedate(Date createdate) 
    {
        this.createdate = createdate;
    }

    public Date getCreatedate() 
    {
        return createdate;
    }

    public void setUpdatedate(Date updatedate) 
    {
        this.updatedate = updatedate;
    }

    public Date getUpdatedate() 
    {
        return updatedate;
    }

    public void setUpdateman(String updateman) 
    {
        this.updateman = updateman;
    }

    public String getUpdateman() 
    {
        return updateman;
    }

    public void setSpec(String spec) 
    {
        this.spec = spec;
    }

    public String getSpec() 
    {
        return spec;
    }

    public void setMatno(String matno) 
    {
        this.matno = matno;
    }

    public String getMatno() 
    {
        return matno;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("validflag", getValidflag())
            .append("matchid", getMatchid())
            .append("carno", getCarno())
            .append("icno", getIcno())
            .append("operatype", getOperatype())
            .append("planid", getPlanid())
            .append("storecode", getStorecode())
            .append("storename", getStorename())
            .append("storepos", getStorepos())
            .append("targetcode", getTargetcode())
            .append("targetname", getTargetname())
            .append("materialcode", getMaterialcode())
            .append("materialname", getMaterialname())
            .append("materialspeccode", getMaterialspeccode())
            .append("materialspec", getMaterialspec())
            .append("weight", getWeight())
            .append("counts", getCounts())
            .append("heatno", getHeatno())
            .append("steelevel", getSteelevel())
            .append("steelgrade", getSteelgrade())
            .append("transitway", getTransitway())
            .append("memo", getMemo())
            .append("createman", getCreateman())
            .append("createdate", getCreatedate())
            .append("updatedate", getUpdatedate())
            .append("updateman", getUpdateman())
            .append("spec", getSpec())
            .append("matno", getMatno())
            .toString();
    }
} 