package com.ruoyi.app.leave.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LPassTMapper;
import com.ruoyi.app.leave.domain.LPassT;
import com.ruoyi.app.leave.service.ILPassTService;

/**
 * 不返回主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class LPassTServiceImpl implements ILPassTService 
{
    @Autowired
    private LPassTMapper lPassTMapper;

    /**
     * 查询不返回主
     * 
     * @param id 不返回主ID
     * @return 不返回主
     */
    @Override
    public LPassT selectLPassTById(Long id)
    {
        return lPassTMapper.selectLPassTById(id);
    }

    /**
     * 查询不返回主列表
     * 
     * @param lPassT 不返回主
     * @return 不返回主
     */
    @Override
    public List<LPassT> selectLPassTList(LPassT lPassT)
    {
        return lPassTMapper.selectLPassTList(lPassT);
    }

    /**
     * 新增不返回主
     * 
     * @param lPassT 不返回主
     * @return 结果
     */
    @Override
    public int insertLPassT(LPassT lPassT)
    {
        return lPassTMapper.insertLPassT(lPassT);
    }

    /**
     * 修改不返回主
     * 
     * @param lPassT 不返回主
     * @return 结果
     */
    @Override
    public int updateLPassT(LPassT lPassT)
    {
        return lPassTMapper.updateLPassT(lPassT);
    }

    /**
     * 批量删除不返回主
     * 
     * @param ids 需要删除的不返回主ID
     * @return 结果
     */
    @Override
    public int deleteLPassTByIds(Long[] ids)
    {
        return lPassTMapper.deleteLPassTByIds(ids);
    }

    /**
     * 删除不返回主信息
     * 
     * @param id 不返回主ID
     * @return 结果
     */
    @Override
    public int deleteLPassTById(Long id)
    {
        return lPassTMapper.deleteLPassTById(id);
    }
}
