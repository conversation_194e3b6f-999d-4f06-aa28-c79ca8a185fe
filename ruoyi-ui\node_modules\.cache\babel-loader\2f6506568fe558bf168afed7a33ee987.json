{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\customer\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\customer\\index.vue", "mtime": 1756170476828}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_customer", "require", "name", "components", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "customerList", "title", "open", "queryParams", "pageNum", "pageSize", "validFlag", "customerCode", "customerName", "queryWord", "erpCode", "tele", "address", "memo", "createBy", "createTime", "form", "rules", "created", "getList", "methods", "_this", "listCustomer", "then", "response", "rows", "cancel", "reset", "id", "updateTime", "updateBy", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getCustomer", "submitForm", "_this3", "$refs", "validate", "valid", "updateCustomer", "msgSuccess", "addCustomer", "handleDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "type", "invalidCustomer", "handleExport", "_this5", "exportCustomer", "download", "msg"], "sources": ["src/views/leave/customer/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户名称\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"请输入客户名称\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"拼音头\" prop=\"queryWord\">\r\n        <el-input v-model=\"queryParams.queryWord\" placeholder=\"请输入拼音头\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"状态\" prop=\"validFlag\">\r\n        <el-select v-model=\"queryParams.validFlag\" clearable placeholder=\"请选择状态\">\r\n          <el-option label=\"有效\" :value=\"1\" />\r\n          <el-option label=\"已废弃\" :value=\"0\" />\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          v-hasPermi=\"['leave:customer:add']\">新增</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\r\n          v-hasPermi=\"['leave:customer:edit']\">修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\r\n          v-hasPermi=\"['leave:customer:remove']\">废弃</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\"\r\n          v-hasPermi=\"['leave:customer:export']\">导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"customerList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"客户编码\" align=\"center\" prop=\"customerCode\" /> -->\r\n      <el-table-column label=\"客户名称\" align=\"center\" prop=\"customerName\" />\r\n      <el-table-column label=\"拼音头\" align=\"center\" prop=\"queryWord\" />\r\n      <el-table-column label=\"创建人\" align=\"center\" prop=\"createBy\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['leave:customer:edit']\">修改</el-button>\r\n          <el-button\r\n            size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['leave:customer:remove']\">废弃</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改出门证厂外客户对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"客户名称\" prop=\"customerName\">\r\n          <el-input v-model=\"form.customerName\" placeholder=\"请输入客户名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"拼音头\" prop=\"queryWord\">\r\n          <el-input v-model=\"form.queryWord\" placeholder=\"请输入拼音头\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"电话\" prop=\"tele\">\r\n          <el-input v-model=\"form.tele\" placeholder=\"请输入电话\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"地址\" prop=\"address\">\r\n          <el-input v-model=\"form.address\" placeholder=\"请输入地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"memo\">\r\n          <el-input v-model=\"form.memo\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCustomer, getCustomer, delCustomer, addCustomer, updateCustomer, exportCustomer,invalidCustomer } from \"@/api/leave/customer\";\r\n\r\nexport default {\r\n  name: \"Customer\",\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证厂外客户表格数据\r\n      customerList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        validFlag: 1,\r\n        customerCode: null,\r\n        customerName: null,\r\n        queryWord: null,\r\n        erpCode: null,\r\n        tele: null,\r\n        address: null,\r\n        memo: null,\r\n        createBy: null,\r\n        createTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询出门证厂外客户列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCustomer(this.queryParams).then(response => {\r\n        this.customerList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        validFlag: null,\r\n        customerCode: null,\r\n        customerName: null,\r\n        queryWord: null,\r\n        erpCode: null,\r\n        tele: null,\r\n        address: null,\r\n        memo: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"客户添加\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getCustomer(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"客户修改\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateCustomer(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCustomer(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 废弃按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认废弃出门证厂外客户编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return invalidCustomer(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"废弃成功\");\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证厂外客户数据项?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return exportCustomer(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      })\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;AA2FA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,OAAA;QACAC,IAAA;QACAC,OAAA;QACAC,IAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,kBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA3B,OAAA;MACA,IAAA4B,sBAAA,OAAAnB,WAAA,EAAAoB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAArB,YAAA,GAAAwB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAtB,KAAA,GAAAyB,QAAA,CAAAzB,KAAA;QACAsB,KAAA,CAAA3B,OAAA;MACA;IACA;IACA;IACAgC,MAAA,WAAAA,OAAA;MACA,KAAAxB,IAAA;MACA,KAAAyB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAX,IAAA;QACAY,EAAA;QACAtB,SAAA;QACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,OAAA;QACAC,IAAA;QACAC,OAAA;QACAC,IAAA;QACAE,UAAA;QACAD,QAAA;QACAe,UAAA;QACAC,QAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA7B,WAAA,CAAAC,OAAA;MACA,KAAAe,OAAA;IACA;IACA,aACAc,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAxC,GAAA,GAAAwC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAT,EAAA;MAAA;MACA,KAAAhC,MAAA,GAAAuC,SAAA,CAAAG,MAAA;MACA,KAAAzC,QAAA,IAAAsC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAZ,KAAA;MACA,KAAAzB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAuC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAf,KAAA;MACA,IAAAC,EAAA,GAAAa,GAAA,CAAAb,EAAA,SAAAjC,GAAA;MACA,IAAAgD,qBAAA,EAAAf,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAkB,MAAA,CAAA1B,IAAA,GAAAQ,QAAA,CAAA/B,IAAA;QACAiD,MAAA,CAAAxC,IAAA;QACAwC,MAAA,CAAAzC,KAAA;MACA;IACA;IACA,WACA2C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA7B,IAAA,CAAAY,EAAA;YACA,IAAAqB,wBAAA,EAAAJ,MAAA,CAAA7B,IAAA,EAAAO,IAAA,WAAAC,QAAA;cACAqB,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAA3C,IAAA;cACA2C,MAAA,CAAA1B,OAAA;YACA;UACA;YACA,IAAAgC,qBAAA,EAAAN,MAAA,CAAA7B,IAAA,EAAAO,IAAA,WAAAC,QAAA;cACAqB,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAA3C,IAAA;cACA2C,MAAA,CAAA1B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAiC,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,IAAA1D,GAAA,GAAA8C,GAAA,CAAAb,EAAA,SAAAjC,GAAA;MACA,KAAA2D,QAAA,uBAAA3D,GAAA;QACA4D,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAlC,IAAA;QACA,WAAAmC,yBAAA,EAAA/D,GAAA;MACA,GAAA4B,IAAA;QACA8B,MAAA,CAAAlC,OAAA;QACAkC,MAAA,CAAAH,UAAA;MACA;IACA;IACA,aACAS,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAzD,WAAA,QAAAA,WAAA;MACA,KAAAmD,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAlC,IAAA;QACA,WAAAsC,wBAAA,EAAA1D,WAAA;MACA,GAAAoB,IAAA,WAAAC,QAAA;QACAoC,MAAA,CAAAE,QAAA,CAAAtC,QAAA,CAAAuC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}