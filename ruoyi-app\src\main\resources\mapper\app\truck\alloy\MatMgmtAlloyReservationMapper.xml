<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.truck.alloy.mapper.MatMgmtAlloyReservationMapper">

    <resultMap type="MatMgmtAlloyReservation" id="MatMgmtAlloyReservationResult">
        <result property="id" column="id"/>
        <result property="reservationNo" column="reservation_no"/>
        <result property="status" column="status"/>
        <result property="supplierSalesName" column="supplier_sales_name"/>
        <result property="supplierSalesPhone" column="supplier_sales_phone"/>
        <result property="applyCompanyId" column="apply_company_id"/>
        <result property="applyCompanyName" column="apply_company_name"/>
        <result property="alloyType" column="alloy_type"/>
        <result property="alloyValue" column="alloy_value"/>
        <result property="alloyLabel" column="alloy_label"/>
        <result property="electrodeType" column="electrode_type"/>
        <result property="estimatedWeight" column="estimated_weight"/>
        <result property="expectedDeliveryTime" column="expected_delivery_time"/>
        <result property="checkinTime" column="checkin_time"/>
        <result property="enterDoor" column="enter_door"/>
        <result property="effectiveStartTime" column="effective_start_time"/>
        <result property="effectiveEndTime" column="effective_end_time"/>
        <result property="actualEarliestTime" column="actual_earliest_time"/>
        <result property="actualLatestTime" column="actual_latest_time"/>
        <result property="carNo" column="car_no"/>
        <result property="licensePlateColor" column="license_plate_color"/>
        <result property="vehicleEmissionStandards" column="vehicle_emission_standards"/>
        <result property="operatorOpenid" column="operator_openid"/>
        <result property="driverName" column="driver_name"/>
        <result property="driverCardNo" column="driver_card_no"/>
        <result property="driverMobile" column="driver_mobile"/>
        <result property="driverFaceImg" column="driver_face_img"/>
        <result property="driverLicenseImgs" column="driver_license_imgs"/>
        <result property="businessApprover" column="business_approver"/>
        <result property="drivingLicenseImg" column="driving_license_img"/>
        <result property="businessApproveTime" column="business_approve_time"/>
        <result property="businessApproveReason" column="business_approve_reason"/>
        <result property="gross" column="gross"/>
        <result property="tare" column="tare"/>
        <result property="netWeight" column="net_weight"/>
        <result property="grossTime" column="gross_time"/>
        <result property="tareTime" column="tare_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="pushNo" column="push_no"/>
        <result property="approvalDept" column="approval_dept"/>
        <result property="deliveryCategoryCode" column="delivery_category_code"/>
    </resultMap>

    <sql id="selectMatMgmtAlloyReservationVo">
        select id,
               reservation_no,
               status,
               supplier_sales_name,
               supplier_sales_phone,
               apply_company_id,
               apply_company_name,
               alloy_type,
               alloy_value,
               alloy_label,
               electrode_type,
               estimated_weight,
               expected_delivery_time,
               checkin_time,
               enter_door,
               effective_start_time,
               effective_end_time,
               actual_earliest_time,
               actual_latest_time,
               car_no,
               license_plate_color,
               vehicle_emission_standards,
               operator_openid,
               driver_name,
               driver_card_no,
               driver_mobile,
               driver_face_img,
               driver_license_imgs,
               business_approver,
               driving_license_img,
               business_approve_time,
               business_approve_reason,
               gross,
               tare,
               net_weight,
               gross_time,
               tare_time,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag,
               push_no,
               approval_dept,
               delivery_category_code
        from mat_mgmt_alloy_reservation
    </sql>

    <select id="selectMatMgmtAlloyReservationList" parameterType="MatMgmtAlloyReservation"
            resultMap="MatMgmtAlloyReservationResult">
        <include refid="selectMatMgmtAlloyReservationVo"/>
        <where>
            <if test="reservationNo != null  and reservationNo != ''">and reservation_no = #{reservationNo}</if>
            <if test="pushNo != null  and pushNo != ''">and push_no = #{pushNo}</if>
            <if test="approvalDept != null  and approvalDept != ''">and approval_dept = #{approvalDept}</if>
            <if test="deliveryCategoryCode != null  and deliveryCategoryCode != ''">and delivery_category_code = #{deliveryCategoryCode}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="supplierSalesName != null  and supplierSalesName != ''">and supplier_sales_name like concat('%',
                #{supplierSalesName}, '%')
            </if>
            <if test="supplierSalesPhone != null  and supplierSalesPhone != ''">and supplier_sales_phone =
                #{supplierSalesPhone}
            </if>
            <if test="applyCompanyId != null  and applyCompanyId != ''">and apply_company_id = #{applyCompanyId}</if>
            <if test="applyCompanyName != null  and applyCompanyName != ''">and apply_company_name like concat('%',
                #{applyCompanyName}, '%')
            </if>
            <if test="alloyType != null  and alloyType != ''">and alloy_type = #{alloyType}</if>
            <if test="alloyValue != null  and alloyValue != ''">and alloy_value = #{alloyValue}</if>
            <if test="alloyLabel != null  and alloyLabel != ''">and alloy_label = #{alloyLabel}</if>
            <if test="electrodeType != null  and electrodeType != ''">and electrode_type = #{electrodeType}</if>
            <if test="estimatedWeight != null ">and estimated_weight = #{estimatedWeight}</if>
            <if test="expectedDeliveryTime != null ">and expected_delivery_time = #{expectedDeliveryTime}</if>
            <if test="checkinTime != null ">and checkin_time = #{checkinTime}</if>
            <if test="enterDoor != null ">and enter_door = #{enterDoor}</if>
            <if test="effectiveStartTime != null ">and effective_start_time = #{effectiveStartTime}</if>
            <if test="effectiveEndTime != null ">and effective_end_time = #{effectiveEndTime}</if>
            <if test="actualEarliestTime != null ">and actual_earliest_time = #{actualEarliestTime}</if>
            <if test="actualLatestTime != null ">and actual_latest_time = #{actualLatestTime}</if>
            <if test="carNo != null  and carNo != ''">and car_no = #{carNo}</if>
            <if test="licensePlateColor != null  and licensePlateColor != ''">and license_plate_color =
                #{licensePlateColor}
            </if>
            <if test="vehicleEmissionStandards != null ">and vehicle_emission_standards = #{vehicleEmissionStandards}
            </if>
            <if test="operatorOpenid != null  and operatorOpenid != ''">and operator_openid = #{operatorOpenid}</if>
            <if test="driverName != null  and driverName != ''">and driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="driverCardNo != null  and driverCardNo != ''">and driver_card_no = #{driverCardNo}</if>
            <if test="driverMobile != null  and driverMobile != ''">and driver_mobile = #{driverMobile}</if>
            <if test="driverFaceImg != null  and driverFaceImg != ''">and driver_face_img = #{driverFaceImg}</if>
            <if test="driverLicenseImgs != null  and driverLicenseImgs != ''">and driver_license_imgs =
                #{driverLicenseImgs}
            </if>
            <if test="businessApprover != null  and businessApprover != ''">and business_approver =
                #{businessApprover}
            </if>
            <if test="drivingLicenseImg != null  and drivingLicenseImg != ''">and driving_license_img =
                #{drivingLicenseImg}
            </if>
            <if test="businessApproveTime != null ">and business_approve_time = #{businessApproveTime}</if>
            <if test="businessApproveReason != null  and businessApproveReason != ''">and business_approve_reason =
                #{businessApproveReason}
            </if>
            <if test="gross != null ">and gross = #{gross}</if>
            <if test="tare != null ">and tare = #{tare}</if>
            <if test="netWeight != null ">and net_weight = #{netWeight}</if>
            <if test="grossTime != null ">and gross_time = #{grossTime}</if>
            <if test="tareTime != null ">and tare_time = #{tareTime}</if>
            <if test="queryStartTime != null and queryStartTime != ''">and create_time &gt;= #{queryStartTime}</if>
            <if test="queryEndTime != null and queryEndTime != '' ">and create_time &lt;= #{queryEndTime}</if>
            <if test="searchValue != null and searchValue != ''">and (driver_name like concat('%', #{searchValue}, '%' )
                or car_no like concat('%', #{searchValue}, '%' ) or apply_company_name like concat('%', #{searchValue},
                '%' ))
            </if>
            <if test="reservationStatusList != null and reservationStatusList.size() > 0">
                and status in
                <foreach item="item" collection="reservationStatusList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>

    <select id="selectMatMgmtAlloyReservationById" parameterType="Long" resultMap="MatMgmtAlloyReservationResult">
        <include refid="selectMatMgmtAlloyReservationVo"/>
        where id = #{id}
    </select>

    <select id="selectMatMgmtAlloyReservationByReservationNo" parameterType="String"
            resultMap="MatMgmtAlloyReservationResult">
        <include refid="selectMatMgmtAlloyReservationVo"/>
        where reservation_no = #{reservationNo}
    </select>

    <insert id="insertMatMgmtAlloyReservation" parameterType="MatMgmtAlloyReservation" useGeneratedKeys="true"
            keyProperty="id">
        insert into mat_mgmt_alloy_reservation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reservationNo != null">reservation_no,</if>
            <if test="status != null">status,</if>
            <if test="supplierSalesName != null">supplier_sales_name,</if>
            <if test="supplierSalesPhone != null">supplier_sales_phone,</if>
            <if test="applyCompanyId != null">apply_company_id,</if>
            <if test="applyCompanyName != null">apply_company_name,</if>
            <if test="alloyType != null">alloy_type,</if>
            <if test="alloyValue != null">alloy_value,</if>
            <if test="alloyLabel != null">alloy_label,</if>
            <if test="electrodeType != null">electrode_type,</if>
            <if test="estimatedWeight != null">estimated_weight,</if>
            <if test="expectedDeliveryTime != null">expected_delivery_time,</if>
            <if test="checkinTime != null">checkin_time,</if>
            <if test="enterDoor != null">enter_door,</if>
            <if test="effectiveStartTime != null">effective_start_time,</if>
            <if test="effectiveEndTime != null">effective_end_time,</if>
            <if test="actualEarliestTime != null">actual_earliest_time,</if>
            <if test="actualLatestTime != null">actual_latest_time,</if>
            <if test="carNo != null">car_no,</if>
            <if test="licensePlateColor != null">license_plate_color,</if>
            <if test="vehicleEmissionStandards != null">vehicle_emission_standards,</if>
            <if test="operatorOpenid != null">operator_openid,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="driverCardNo != null">driver_card_no,</if>
            <if test="driverMobile != null">driver_mobile,</if>
            <if test="driverFaceImg != null">driver_face_img,</if>
            <if test="driverLicenseImgs != null">driver_license_imgs,</if>
            <if test="businessApprover != null">business_approver,</if>
            <if test="drivingLicenseImg != null">driving_license_img,</if>
            <if test="businessApproveTime != null">business_approve_time,</if>
            <if test="businessApproveReason != null">business_approve_reason,</if>
            <if test="gross != null">gross,</if>
            <if test="tare != null">tare,</if>
            <if test="netWeight != null">net_weight,</if>
            <if test="grossTime != null">gross_time,</if>
            <if test="tareTime != null">tare_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="pushNo != null">push_no,</if>
            <if test="approvalDept != null">approval_dept,</if>
            <if test="deliveryCategoryCode != null">delivery_category_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reservationNo != null">#{reservationNo},</if>
            <if test="status != null">#{status},</if>
            <if test="supplierSalesName != null">#{supplierSalesName},</if>
            <if test="supplierSalesPhone != null">#{supplierSalesPhone},</if>
            <if test="applyCompanyId != null">#{applyCompanyId},</if>
            <if test="applyCompanyName != null">#{applyCompanyName},</if>
            <if test="alloyType != null">#{alloyType},</if>
            <if test="alloyValue != null">#{alloyValue},</if>
            <if test="alloyLabel != null">#{alloyLabel},</if>
            <if test="electrodeType != null">#{electrodeType},</if>
            <if test="estimatedWeight != null">#{estimatedWeight},</if>
            <if test="expectedDeliveryTime != null">#{expectedDeliveryTime},</if>
            <if test="checkinTime != null">#{checkinTime},</if>
            <if test="enterDoor != null">#{enterDoor},</if>
            <if test="effectiveStartTime != null">#{effectiveStartTime},</if>
            <if test="effectiveEndTime != null">#{effectiveEndTime},</if>
            <if test="actualEarliestTime != null">#{actualEarliestTime},</if>
            <if test="actualLatestTime != null">#{actualLatestTime},</if>
            <if test="carNo != null">#{carNo},</if>
            <if test="licensePlateColor != null">#{licensePlateColor},</if>
            <if test="vehicleEmissionStandards != null">#{vehicleEmissionStandards},</if>
            <if test="operatorOpenid != null">#{operatorOpenid},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="driverCardNo != null">#{driverCardNo},</if>
            <if test="driverMobile != null">#{driverMobile},</if>
            <if test="driverFaceImg != null">#{driverFaceImg},</if>
            <if test="driverLicenseImgs != null">#{driverLicenseImgs},</if>
            <if test="businessApprover != null">#{businessApprover},</if>
            <if test="drivingLicenseImg != null">#{drivingLicenseImg},</if>
            <if test="businessApproveTime != null">#{businessApproveTime},</if>
            <if test="businessApproveReason != null">#{businessApproveReason},</if>
            <if test="gross != null">#{gross},</if>
            <if test="tare != null">#{tare},</if>
            <if test="netWeight != null">#{netWeight},</if>
            <if test="grossTime != null">#{grossTime},</if>
            <if test="tareTime != null">#{tareTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="pushNo != null">#{pushNo},</if>
            <if test="approvalDept != null">#{approvalDept},</if>
            <if test="deliveryCategoryCode != null">#{deliveryCategoryCode},</if>
        </trim>
    </insert>

    <update id="updateMatMgmtAlloyReservation" parameterType="MatMgmtAlloyReservation">
        update mat_mgmt_alloy_reservation
        <trim prefix="SET" suffixOverrides=",">
            <if test="reservationNo != null">reservation_no = #{reservationNo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="supplierSalesName != null">supplier_sales_name = #{supplierSalesName},</if>
            <if test="supplierSalesPhone != null">supplier_sales_phone = #{supplierSalesPhone},</if>
            <if test="applyCompanyId != null">apply_company_id = #{applyCompanyId},</if>
            <if test="applyCompanyName != null">apply_company_name = #{applyCompanyName},</if>
            <if test="alloyType != null">alloy_type = #{alloyType},</if>
            <if test="alloyValue != null">alloy_value = #{alloyValue},</if>
            <if test="alloyLabel != null">alloy_label = #{alloyLabel},</if>
            <if test="electrodeType != null">electrode_type = #{electrodeType},</if>
            <if test="estimatedWeight != null">estimated_weight = #{estimatedWeight},</if>
            <if test="expectedDeliveryTime != null">expected_delivery_time = #{expectedDeliveryTime},</if>
            <if test="checkinTime != null">checkin_time = #{checkinTime},</if>
            <if test="enterDoor != null">enter_door = #{enterDoor},</if>
            <if test="effectiveStartTime != null">effective_start_time = #{effectiveStartTime},</if>
            <if test="effectiveEndTime != null">effective_end_time = #{effectiveEndTime},</if>
            <if test="actualEarliestTime != null">actual_earliest_time = #{actualEarliestTime},</if>
            <if test="actualLatestTime != null">actual_latest_time = #{actualLatestTime},</if>
            <if test="carNo != null">car_no = #{carNo},</if>
            <if test="licensePlateColor != null">license_plate_color = #{licensePlateColor},</if>
            <if test="vehicleEmissionStandards != null">vehicle_emission_standards = #{vehicleEmissionStandards},</if>
            <if test="operatorOpenid != null">operator_openid = #{operatorOpenid},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="driverCardNo != null">driver_card_no = #{driverCardNo},</if>
            <if test="driverMobile != null">driver_mobile = #{driverMobile},</if>
            <if test="driverFaceImg != null">driver_face_img = #{driverFaceImg},</if>
            <if test="driverLicenseImgs != null">driver_license_imgs = #{driverLicenseImgs},</if>
            <if test="businessApprover != null">business_approver = #{businessApprover},</if>
            <if test="drivingLicenseImg != null">driving_license_img = #{drivingLicenseImg},</if>
            <if test="businessApproveTime != null">business_approve_time = #{businessApproveTime},</if>
            <if test="businessApproveReason != null">business_approve_reason = #{businessApproveReason},</if>
            <if test="gross != null">gross = #{gross},</if>
            <if test="tare != null">tare = #{tare},</if>
            <if test="netWeight != null">net_weight = #{netWeight},</if>
            <if test="grossTime != null">gross_time = #{grossTime},</if>
            <if test="tareTime != null">tare_time = #{tareTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="pushNo != null">push_no = #{pushNo},</if>
            <if test="approvalDept != null">approval_dept = #{approvalDept},</if>
            <if test="deliveryCategoryCode != null">delivery_category_code = #{deliveryCategoryCode},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateMatMgmtAlloyReservationStatus" parameterType="MatMgmtAlloyReservation">
        update mat_mgmt_alloy_reservation
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
        </trim>
        where reservation_no = #{reservationNo}
    </update>

    <delete id="deleteMatMgmtAlloyReservationById" parameterType="Long">
        delete
        from mat_mgmt_alloy_reservation
        where id = #{id}
    </delete>

    <delete id="deleteMatMgmtAlloyReservationByIds" parameterType="String">
        delete from mat_mgmt_alloy_reservation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
