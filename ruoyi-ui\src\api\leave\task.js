import request from '@/utils/request'

// 查询出门证任务列表
export function listTask(query) {
  return request({
    url: '/web/leaveTask/task/list',
    method: 'get',
    params: query
  })
}

// 查询出门证任务列表
export function listAllTask(query) {
  return request({
    url: '/web/leaveTask/task/listAll',
    method: 'get',
    params: query
  })
}

// 查询出门证任务详细
export function getTask(id) {
  return request({
    url: '/web/leaveTask/task/' + id,
    method: 'get'
  })
}

// 根据任务号查询出门证任务详细
export function getTaskByTaskNo(taskNo) {
  return request({
    url: '/web/leaveTask/task/getTaskByTaskNo/' + taskNo,
    method: 'get'
  })
}

// 查询出门证任务详细
export function getDirectSupplyPlanAndTaskDetail(query) {
  return request({
    url: '/web/leaveTask/task/getDirectSupplyPlanAndTaskDetail',
    method: 'get',
    params: query
  })
}

// 新增出门证任务
export function addTask(data) {
  return request({
    url: '/web/leaveTask/task',
    method: 'post',
    data: data
  })
}

// 返回雪花
export function addTaskAndMaterial(data) {
  return request({
    url: '/web/leaveTask/task/taskAndMaterial',
    method: 'post',
    data: data
  })
}

// 修改出门证任务
export function updateTask(data) {
  return request({
    url: '/web/leaveTask/task',
    method: 'put',
    data: data
  })
}

// 删除出门证任务
export function delTask(id) {
  return request({
    url: '/web/leaveTask/task/' + id,
    method: 'delete'
  })
}

// 导出出门证任务
export function exportTask(query) {
  return request({
    url: '/web/leaveTask/task/export',
    method: 'get',
    params: query
  })
}

// 导出出门证任务
export function addTaskMaterial(data) {
  return request({
    url: '/web/leave/taskMaterial',
    method: 'post',
    data: data
  })
}

// 根据taskNo查询物资
export function getTaskmaterials(query) {
  return request({
    url: '/web/leave/taskMaterial/list',
    method: 'get',
    params: query
  })
}

// 修改任务物资物资
export function editTaskmaterials(data) {
  return request({
    url: '/web/leave/taskMaterial',
    method: 'put',
    data: data
  })
}


// 日志生成
export function addLeaveLog(data) {
  return request({
    url: '/web/leave/log/handledLog',
    method: 'post',
    data: data
  })
}

// 查询日志
export function getTaskLogs(query) {
  return request({
    url: '/web/leave/log/list',
    method: 'get',
    params: query
  })
}

// 是否允许派车
export function isAllowDispatch(data) {
  return request({
    url: '/web/leaveTask/task/isAllowDispatch',
    method: 'post',
    data: data
  })
}

// 是否允许派车
export function addTaskAndMaterialAndAddLeaveLog(data) {
  return request({
    url: '/web/leaveTask/task/addTaskAndMaterialAndAddLeaveLog',
    method: 'post',
    data: data
  })
}


export function addLeaveLogAndEditTaskMaterialsAndUpdateTask(data) {
  return request({
    url: '/web/leaveTask/task/addLeaveLogAndEditTaskMaterialsAndUpdateTask',
    method: 'post',
    data: data
  })
}

// 查询加工类型
export function getProcessList(data) {
  return request({
    url: '/web/leaveTask/task/getProcessList',
    method: 'get',
    data: data
  })
}

// 查询直供计划
export function getDirectSupplyPlans(data) {
  return request({
    url: '/web/leaveTask/task/getDirectSupplyPlans',
    method: 'post',
    data: data
  })
}

// 查询加工类型
export function getPlanMaterials(query) {
  return request({
    url: '/web/leave/planMaterial/list',
    method: 'get',
    params: query
  })
}



// 处理分厂确认和直供
export function handleUnload(data) {
  return request({
    url: '/web/leaveTask/task/handleUnload',
    method: 'post',
    data: data
  })
}

// 处理出库
export function handleStockOut(data) {
  return request({
    url: '/web/leaveTask/task/handleStockOut',
    method: 'post',
    data: data
  })
}


