<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LPassFhItemTMapper">
    
    <resultMap type="LPassFhItemT" id="LPassFhItemTResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="materialcode"    column="materialcode"    />
        <result property="materialname"    column="materialname"    />
        <result property="materialspec"    column="materialspec"    />
        <result property="materialtype"    column="materialtype"    />
        <result property="unit"    column="unit"    />
        <result property="count"    column="count"    />
        <result property="weight"    column="weight"    />
        <result property="memo"    column="memo"    />
        <result property="returncount"    column="returncount"    />
        <result property="fpflag"    column="fpflag"    />
        <result property="outdate"    column="outdate"    />
        <result property="indate"    column="indate"    />
        <result property="shdate"    column="shdate"    />
        <result property="outdoor"    column="outdoor"    />
        <result property="indoor"    column="indoor"    />
        <result property="shren"    column="shren"    />
        <result property="mwshCount"    column="mwsh_count"    />
        <result property="mwshCounted"    column="mwsh_counted"    />
        <result property="counttmp"    column="counttmp"    />
        <result property="wlmaterialname"    column="wlmaterialname"    />
    </resultMap>

    <sql id="selectLPassFhItemTVo">
        select id, pid, materialcode, materialname, materialspec, materialtype, unit, count, weight, memo, returncount, fpflag, outdate, indate, shdate, outdoor, indoor, shren, mwsh_count, mwsh_counted, counttmp, wlmaterialname from L_PASS_FH_ITEM_T
    </sql>

    <select id="selectLPassFhItemTList" parameterType="LPassFhItemT" resultMap="LPassFhItemTResult">
        <include refid="selectLPassFhItemTVo"/>
        <where>  
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="materialcode != null  and materialcode != ''"> and materialcode = #{materialcode}</if>
            <if test="materialname != null  and materialname != ''"> and materialname like concat('%', #{materialname}, '%')</if>
            <if test="materialspec != null  and materialspec != ''"> and materialspec = #{materialspec}</if>
            <if test="materialtype != null  and materialtype != ''"> and materialtype = #{materialtype}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="count != null  and count != ''"> and count = #{count}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="returncount != null "> and returncount = #{returncount}</if>
            <if test="fpflag != null "> and fpflag = #{fpflag}</if>
            <if test="outdate != null "> and outdate = #{outdate}</if>
            <if test="indate != null "> and indate = #{indate}</if>
            <if test="shdate != null "> and shdate = #{shdate}</if>
            <if test="outdoor != null  and outdoor != ''"> and outdoor = #{outdoor}</if>
            <if test="indoor != null  and indoor != ''"> and indoor = #{indoor}</if>
            <if test="shren != null  and shren != ''"> and shren = #{shren}</if>
            <if test="mwshCount != null "> and mwsh_count = #{mwshCount}</if>
            <if test="mwshCounted != null "> and mwsh_counted = #{mwshCounted}</if>
            <if test="counttmp != null "> and counttmp = #{counttmp}</if>
            <if test="wlmaterialname != null  and wlmaterialname != ''"> and wlmaterialname like concat('%', #{wlmaterialname}, '%')</if>
        </where>
    </select>
    
    <select id="selectLPassFhItemTById" parameterType="Long" resultMap="LPassFhItemTResult">
        <include refid="selectLPassFhItemTVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLPassFhItemT" parameterType="LPassFhItemT">
        insert into L_PASS_FH_ITEM_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid != null">pid,</if>
            <if test="materialcode != null">materialcode,</if>
            <if test="materialname != null">materialname,</if>
            <if test="materialspec != null">materialspec,</if>
            <if test="materialtype != null">materialtype,</if>
            <if test="unit != null">unit,</if>
            <if test="count != null">count,</if>
            <if test="weight != null">weight,</if>
            <if test="memo != null">memo,</if>
            <if test="returncount != null">returncount,</if>
            <if test="fpflag != null">fpflag,</if>
            <if test="outdate != null">outdate,</if>
            <if test="indate != null">indate,</if>
            <if test="shdate != null">shdate,</if>
            <if test="outdoor != null">outdoor,</if>
            <if test="indoor != null">indoor,</if>
            <if test="shren != null">shren,</if>
            <if test="mwshCount != null">mwsh_count,</if>
            <if test="mwshCounted != null">mwsh_counted,</if>
            <if test="counttmp != null">counttmp,</if>
            <if test="wlmaterialname != null">wlmaterialname,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pid != null">#{pid},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="materialtype != null">#{materialtype},</if>
            <if test="unit != null">#{unit},</if>
            <if test="count != null">#{count},</if>
            <if test="weight != null">#{weight},</if>
            <if test="memo != null">#{memo},</if>
            <if test="returncount != null">#{returncount},</if>
            <if test="fpflag != null">#{fpflag},</if>
            <if test="outdate != null">#{outdate},</if>
            <if test="indate != null">#{indate},</if>
            <if test="shdate != null">#{shdate},</if>
            <if test="outdoor != null">#{outdoor},</if>
            <if test="indoor != null">#{indoor},</if>
            <if test="shren != null">#{shren},</if>
            <if test="mwshCount != null">#{mwshCount},</if>
            <if test="mwshCounted != null">#{mwshCounted},</if>
            <if test="counttmp != null">#{counttmp},</if>
            <if test="wlmaterialname != null">#{wlmaterialname},</if>
         </trim>
    </insert>

    <update id="updateLPassFhItemT" parameterType="LPassFhItemT">
        update L_PASS_FH_ITEM_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="materialcode != null">materialcode = #{materialcode},</if>
            <if test="materialname != null">materialname = #{materialname},</if>
            <if test="materialspec != null">materialspec = #{materialspec},</if>
            <if test="materialtype != null">materialtype = #{materialtype},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="count != null">count = #{count},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="returncount != null">returncount = #{returncount},</if>
            <if test="fpflag != null">fpflag = #{fpflag},</if>
            <if test="outdate != null">outdate = #{outdate},</if>
            <if test="indate != null">indate = #{indate},</if>
            <if test="shdate != null">shdate = #{shdate},</if>
            <if test="outdoor != null">outdoor = #{outdoor},</if>
            <if test="indoor != null">indoor = #{indoor},</if>
            <if test="shren != null">shren = #{shren},</if>
            <if test="mwshCount != null">mwsh_count = #{mwshCount},</if>
            <if test="mwshCounted != null">mwsh_counted = #{mwshCounted},</if>
            <if test="counttmp != null">counttmp = #{counttmp},</if>
            <if test="wlmaterialname != null">wlmaterialname = #{wlmaterialname},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLPassFhItemTById" parameterType="Long">
        delete from L_PASS_FH_ITEM_T where id = #{id}
    </delete>

    <delete id="deleteByPid" parameterType="Long">
        delete from L_PASS_FH_ITEM_T where pid = #{pid}
    </delete>

    <delete id="deleteLPassFhItemTByIds" parameterType="String">
        delete from L_PASS_FH_ITEM_T where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>