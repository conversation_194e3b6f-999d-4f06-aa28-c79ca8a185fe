<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px">
      <el-row :gutter="30">
        <el-col :span="6">
          <el-form-item label="计划号" prop="planNo">
            <el-input
              v-model="queryParams.planNo"
              placeholder="请输入计划号"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计划类型" prop="planType">
            <el-select v-model="queryParams.planType" placeholder="请选择计划类型" clearable size="small" style="width: 100%">
              <el-option label="出厂不返回" value="1" />
              <el-option label="出厂返回" value="2" />
              <el-option label="跨区调拨" value="3" />
              <el-option label="退货申请" value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item label="是否计量" prop="measureFlag">
            <el-select v-model="queryParams.measureFlag" placeholder="请选择是否计量" clearable size="small" style="width: 100%">
              <el-option label="计量" value="1" />
              <el-option label="不计量" value="0" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="申请单位" prop="sourceCompany">
            <el-input
              v-model="queryParams.sourceCompany"
              placeholder="请输入申请单位"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="收货单位" prop="receiveCompany">
            <el-input
              v-model="queryParams.receiveCompany"
              placeholder="请输入收货单位"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="返回单位" prop="targetCompany">
            <el-input
              v-model="queryParams.targetCompany"
              placeholder="请输入返回单位"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>

        <!-- <el-col :span="6">
          <el-form-item label="物资专管员" prop="specialManager">
            <el-input
              v-model="queryParams.specialManager"
              placeholder="请输入物资专管员"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col> -->

        <el-col :span="6">
          <el-form-item label="计划状态" prop="planStatus">
            <el-select v-model="queryParams.planStatus" placeholder="请选择计划状态" clearable size="small" style="width: 100%">
              <el-option label="待分厂审批" value="1" />
              <el-option label="待分厂复审" value="2" />
              <el-option label="待生产指挥中心审批" value="3" />
              <el-option label="审批完成" value="4" />
              <el-option label="已出厂" value="5" />
              <el-option label="部分收货" value="6" />
              <el-option label="已完成" value="7" />
              <el-option label="驳回" value="11" />
              <el-option label="废弃" value="12" />
              <el-option label="过期" value="13" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="返回时间" prop="planReturnTime">
            <el-date-picker 
              clearable 
              size="small" 
              style="width: 100%"
              v-model="daterangePlanReturn"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handlePlanReturnTimeChange">
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="有效期" prop="expireTime">
            <el-date-picker 
              clearable 
              size="small" 
              style="width: 100%"
              v-model="daterangeExpire"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleExpireTimeChange">
            </el-date-picker>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="申请时间" prop="applyTime">
            <el-date-picker 
              clearable 
              size="small" 
              style="width: 100%"
              v-model="daterangeApply"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleApplyTimeChange">
            </el-date-picker>
          </el-form-item>
        </el-col>
        
        <el-col :span="24" style="text-align: center; margin-top: 10px">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-if="this.showAddBtn"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button v-if="this.showCancelBtn"
          type="danger"
          icon="el-icon-close"
          size="mini"
          :disabled="single"
          @click="handleInvalidate"
        >作废</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-printer"
          size="mini"
          :disabled="single"
          @click="handlePrintApplication"
        >打印申请单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-printer"
          size="mini"
          :disabled="single"
          @click="handlePrintMaterialList"
        >打印物料清单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-printer"
          size="mini"
          :disabled="single"
          @click="handlePrintPermit"
        >打印出门证</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col> -->
	  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="planList" @selection-change="handleSelectionChange" class="plan-table">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请编号" align="center" prop="applyNo" width="180" />
      <el-table-column label="计划号" align="center" prop="planNo" />
      <el-table-column label="计划类型" align="center" prop="planType" width="120" >
      <!-- tag标签 -->
      <template slot-scope="scope">
        <el-tag v-if="scope.row.planType == '1'" type="success">出厂不返回</el-tag>
        <el-tag v-if="scope.row.planType == '2'" type="warning">出厂返回</el-tag>
        <el-tag v-if="scope.row.planType == '3'" type="info">跨区调拨</el-tag>
        <el-tag v-if="scope.row.planType == '4'" type="danger">退货申请</el-tag>
      </template>

      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applyUserName" />
      <el-table-column label="申请单位" align="center" prop="sourceCompany" width="200" />
      <el-table-column label="是否计量" align="center" prop="measureFlag" >
        <!-- tag标签 -->
        <template slot-scope="scope">
          <el-tag v-if="scope.row.measureFlag == '1'" type="success">计量</el-tag>
          <el-tag v-if="scope.row.measureFlag == '0'" type="danger">不计量</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column label="计划量" align="center" prop="plannedAmount" /> -->
      <el-table-column label="收货单位" align="center" prop="receiveCompany" width="200" />
      <el-table-column label="返回单位" align="center" prop="targetCompany" />
      
      <el-table-column label="申请有效期" align="center" prop="expireTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出厂原因" align="center" prop="reason" />
      <el-table-column label="计划状态" align="center" prop="planStatus" >
        <!-- tag标签 -->
        <template slot-scope="scope">
          <el-tag v-if="scope.row.planStatus == '1'" type="warning">待分厂审批</el-tag>
          <el-tag v-if="scope.row.planStatus == '2'" type="warning">待分厂复审</el-tag>
          <el-tag v-if="scope.row.planStatus == '3'" type="warning">待生产指挥中心审批</el-tag>
          <el-tag v-if="scope.row.planStatus == '4'" type="success">审批完成</el-tag>
          <el-tag v-if="scope.row.planStatus == '5'" type="success">已出厂</el-tag>
          <el-tag v-if="scope.row.planStatus == '6'" type="info">部分收货</el-tag>
          <el-tag v-if="scope.row.planStatus == '7'" type="success">已完成</el-tag>
          <el-tag v-if="scope.row.planStatus == '11'" type="danger">驳回</el-tag>
          <el-tag v-if="scope.row.planStatus == '12'" type="danger">废弃</el-tag>
          <el-tag v-if="scope.row.planStatus == '13'" type="danger">过期</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="applyTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="分厂审核" align="center" prop="factoryApproveFlag" >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.factoryApproveFlag === '1'" type="success">同意</el-tag>
          <el-tag v-else-if="scope.row.factoryApproveFlag === '0'" type="danger">拒绝</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="分厂复审" align="center" prop="factorySecApproveFlag" >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.factorySecApproveFlag === '1'" type="success">同意</el-tag>
          <el-tag v-else-if="scope.row.factorySecApproveFlag === '0'" type="danger">拒绝</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="生产指挥中心审核" align="center" prop="centerApproveFlag" >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.centerApproveFlag === '1'" type="success">同意</el-tag>
          <el-tag v-else-if="scope.row.centerApproveFlag === '0'" type="danger">拒绝</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.showModifyBtn"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button> -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    
  </div>
</template>

<script>
import { listPlan, getPlan, delPlan, addPlan, updatePlan, exportPlan } from "@/api/leave/plan";
import Editor from '@/components/Editor';

export default {
  name: "Plan",
  components: {
    Editor,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      applyNos: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出门证计划申请表格数据
      planList: [],
      // 日期范围
      daterangePlanReturn: [],
      daterangeExpire: [],
      daterangeApply: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示新增按钮
      showAddBtn:false,
      //是否显示废弃按钮
      showCancelBtn:false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planNo: null,
        planType: null,
        businessCategory: null,
        measureFlag: null,
        plannedAmount: null,
        receiveCompany: null,
        receiveCompanyCode: null,
        targetCompany: null,
        targetCompanyCode: null,
        sourceCompany: null,
        sourceCompanyCode: null,
        planReturnStartTime: null,
        planReturnEndTime: null,
        realReturnTime: null,
        monitor: null,
        specialManager: null,
        expireStartTime: null,
        expireEndTime: null,
        reason: null,
        itemType: null,
        planStatus: null,
        applyStartTime: null,
        applyEndTime: null,
        applyWorkNo: null,
        factoryApproveTime: null,
        factoryApproveWorkNo: null,
        factoryApproveFlag: null,
        factoryApproveContent: null,
        factorySecApproveFlag: null,
        factorySecApproveTime: null,
        factorySecApproveWorkNo: null,
        factorySecApproveContent: null,
        centerApproveTime: null,
        centerApproveWorkNo: null,
        centerApproveFlag: null,
        centerApproveContent: null,
        applyFileUrl: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  watch: {
  '$route.query.refresh': {
    immediate: true,
    handler(newVal) {
      if (newVal) {
        this.getList(); // 刷新列表数据的方法
          }
        }
      }
    },
  methods: {
    /** 查询出门证计划申请列表 */
    getList() {
      this.loading = true;
      listPlan(this.queryParams).then(response => {
        this.planList = response.rows;
        console.log(this.planList);
        this.showAddBtn =this.planList[0].showAddBtn;
        this.showCancelBtn =this.planList[0].showCancelBtn;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.applyNos = selection.map(item => item.applyNo)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push("/leave/plan/edit");
    },
    
    /** 修改按钮操作 */
    handleUpdate(row) {
      if (this.applyNos.length > 0) {
        this.$router.push(`/leave/plan/edit/${this.applyNos[0]}`);
      } else {
        this.$router.push(`/leave/plan/edit/${row.applyNo}`);
      }
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除出门证计划申请编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delPlan(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有出门证计划申请数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportPlan(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    handleDetail(row) {
      // 跳转到详情页，并传递ID参数
      this.$router.push({
        path: `/leave/plan/detail/${row.applyNo}`
      });
    },
    // 计划返回时间范围发生变化
    handlePlanReturnTimeChange(val) {
      if (val) {
        this.queryParams.planReturnStartTime = val[0];
        this.queryParams.planReturnEndTime = val[1];
      } else {
        this.queryParams.planReturnStartTime = null;
        this.queryParams.planReturnEndTime = null;
      }
    },
    
    // 申请有效期范围发生变化
    handleExpireTimeChange(val) {
      if (val) {
        this.queryParams.expireStartTime = val[0];
        this.queryParams.expireEndTime = val[1];
      } else {
        this.queryParams.expireStartTime = null;
        this.queryParams.expireEndTime = null;
      }
    },
    
    // 申请时间范围发生变化
    handleApplyTimeChange(val) {
      if (val) {
        this.queryParams.applyStartTime = val[0];
        this.queryParams.applyEndTime = val[1];
      } else {
        this.queryParams.applyStartTime = null;
        this.queryParams.applyEndTime = null;
      }
    },
    
    /** 作废按钮操作 */
    handleInvalidate(row) {
      // TODO: 实现作废功能
    },
    
    /** 打印申请单按钮操作 */
    handlePrintApplication(row) {
      // TODO: 实现打印申请单功能
    },
    
    /** 打印物料清单按钮操作 */
    handlePrintMaterialList(row) {
      // TODO: 实现打印物料清单功能
    },
    
    /** 打印出门证按钮操作 */
    handlePrintPermit(row) {
      // TODO: 实现打印出门证功能
    },
  }
};
</script>

<style>
.mb8 {
  margin-bottom: 8px;
}

.plan-table {
  width: 100%;
}

.plan-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  text-align: center !important;
}

.plan-table td {
  text-align: center !important;
}

/* 表单样式调整 */
.el-form-item {
  margin-bottom: 15px;
  width: 100%;
}

.el-form-item__content {
  width: calc(100% - 80px);
}

.el-date-editor.el-input {
  width: 100%;
}

.el-date-editor--daterange.el-input__inner {
  width: 100% !important;
}
</style>
