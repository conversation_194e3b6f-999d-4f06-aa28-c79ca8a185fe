<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LPassThTMapper">
    
    <resultMap type="LPassThT" id="LPassThTResult">
        <result property="id"    column="id"    />
        <result property="validflag"    column="validflag"    />
        <result property="materialcode"    column="materialcode"    />
        <result property="materialname"    column="materialname"    />
        <result property="materialspec"    column="materialspec"    />
        <result property="materialtype"    column="materialtype"    />
        <result property="sourcecode"    column="sourcecode"    />
        <result property="sourcename"    column="sourcename"    />
        <result property="targetcode"    column="targetcode"    />
        <result property="targetname"    column="targetname"    />
        <result property="count"    column="count"    />
        <result property="weight"    column="weight"    />
        <result property="unit"    column="unit"    />
        <result property="validtime"    column="validtime"    />
        <result property="unitcode"    column="unitcode"    />
        <result property="unitname"    column="unitname"    />
        <result property="lflag"    column="lflag"    />
        <result property="leader"    column="leader"    />
        <result property="ldate"    column="ldate"    />
        <result property="pflag"    column="pflag"    />
        <result property="productman"    column="productman"    />
        <result property="pdate"    column="pdate"    />
        <result property="printnum"    column="printnum"    />
        <result property="createman"    column="createman"    />
        <result property="createdate"    column="createdate"    />
        <result property="updateman"    column="updateman"    />
        <result property="updatedate"    column="updatedate"    />
        <result property="memo"    column="memo"    />
        <result property="leadermemo"    column="leadermemo"    />
        <result property="productmemo"    column="productmemo"    />
        <result property="heatno"    column="heatno"    />
        <result property="leaveman"    column="leaveman"    />
        <result property="leavedate"    column="leavedate"    />
        <result property="leavegate"    column="leavegate"    />
        <result property="supervisor"    column="supervisor"    />
        <result property="outmemo"    column="outmemo"    />
        <result property="carno"    column="carno"    />
        <result property="attachpath"    column="attachpath"    />
        <result property="printnump"    column="printnump"    />
        <result property="cmzhao"    column="cmzhao"    />
        <result property="yxflag"    column="yxflag"    />
        <result property="yxday"    column="yxday"    />
        <result property="wzzgy"    column="wzzgy"    />
        <result property="applyid"    column="applyid"    />
        <result property="measureflag"    column="measureflag"    />
        <result property="hsflag"    column="hsflag"    />
        <result property="shmore"    column="shmore"    />
        <result property="yxqmemo"    column="yxqmemo"    />
        <result property="thflag"    column="thflag"    />
        <result property="thdwname"    column="thdwname"    />
        <result property="thdwcode"    column="thdwcode"    />
        <result property="cancelmemo"    column="cancelmemo"    />
        <result property="printflag"    column="printflag"    />
        <result property="printflagdate"    column="printflagdate"    />
        <result property="bdmemo"    column="bdmemo"    />
        <result property="uptime"    column="uptime"    />
        <result property="uptimestamp"    column="uptimestamp"    />
    </resultMap>

    <sql id="selectLPassThTVo">
        select id, validflag, materialcode, materialname, materialspec, materialtype, sourcecode, sourcename, targetcode, targetname, count, weight, unit, validtime, unitcode, unitname, lflag, leader, ldate, pflag, productman, pdate, printnum, createman, createdate, updateman, updatedate, memo, leadermemo, productmemo, heatno, leaveman, leavedate, leavegate, supervisor, outmemo, carno, attachpath, printnump, cmzhao, yxflag, yxday, wzzgy, applyid, measureflag, hsflag, shmore, yxqmemo, thflag, thdwname, thdwcode, cancelmemo, printflag, printflagdate, bdmemo, uptime, uptimestamp from L_PASS_TH_T
    </sql>

    <select id="selectLPassThTList" parameterType="LPassThT" resultMap="LPassThTResult">
        <include refid="selectLPassThTVo"/>
        <where>  
            <if test="validflag != null "> and validflag = #{validflag}</if>
            <if test="materialcode != null  and materialcode != ''"> and materialcode = #{materialcode}</if>
            <if test="materialname != null  and materialname != ''"> and materialname like concat('%', #{materialname}, '%')</if>
            <if test="materialspec != null  and materialspec != ''"> and materialspec = #{materialspec}</if>
            <if test="materialtype != null  and materialtype != ''"> and materialtype = #{materialtype}</if>
            <if test="sourcecode != null  and sourcecode != ''"> and sourcecode = #{sourcecode}</if>
            <if test="sourcename != null  and sourcename != ''"> and sourcename like concat('%', #{sourcename}, '%')</if>
            <if test="targetcode != null  and targetcode != ''"> and targetcode = #{targetcode}</if>
            <if test="targetname != null  and targetname != ''"> and targetname like concat('%', #{targetname}, '%')</if>
            <if test="count != null "> and count = #{count}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="validtime != null "> and validtime = #{validtime}</if>
            <if test="unitcode != null  and unitcode != ''"> and unitcode = #{unitcode}</if>
            <if test="unitname != null  and unitname != ''"> and unitname like concat('%', #{unitname}, '%')</if>
            <if test="lflag != null "> and lflag = #{lflag}</if>
            <if test="leader != null  and leader != ''"> and leader = #{leader}</if>
            <if test="ldate != null "> and ldate = #{ldate}</if>
            <if test="pflag != null "> and pflag = #{pflag}</if>
            <if test="productman != null  and productman != ''"> and productman = #{productman}</if>
            <if test="pdate != null "> and pdate = #{pdate}</if>
            <if test="printnum != null "> and printnum = #{printnum}</if>
            <if test="createman != null  and createman != ''"> and createman = #{createman}</if>
            <if test="createdate != null "> and createdate = #{createdate}</if>
            <if test="updateman != null  and updateman != ''"> and updateman = #{updateman}</if>
            <if test="updatedate != null "> and updatedate = #{updatedate}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="leadermemo != null  and leadermemo != ''"> and leadermemo = #{leadermemo}</if>
            <if test="productmemo != null  and productmemo != ''"> and productmemo = #{productmemo}</if>
            <if test="heatno != null  and heatno != ''"> and heatno = #{heatno}</if>
            <if test="leaveman != null  and leaveman != ''"> and leaveman = #{leaveman}</if>
            <if test="leavedate != null "> and leavedate = #{leavedate}</if>
            <if test="leavegate != null  and leavegate != ''"> and leavegate = #{leavegate}</if>
            <if test="supervisor != null  and supervisor != ''"> and supervisor = #{supervisor}</if>
            <if test="outmemo != null  and outmemo != ''"> and outmemo = #{outmemo}</if>
            <if test="carno != null  and carno != ''"> and carno = #{carno}</if>
            <if test="attachpath != null  and attachpath != ''"> and attachpath = #{attachpath}</if>
            <if test="printnump != null "> and printnump = #{printnump}</if>
            <if test="cmzhao != null  and cmzhao != ''"> and cmzhao = #{cmzhao}</if>
            <if test="yxflag != null "> and yxflag = #{yxflag}</if>
            <if test="yxday != null "> and yxday = #{yxday}</if>
            <if test="wzzgy != null  and wzzgy != ''"> and wzzgy = #{wzzgy}</if>
            <if test="applyid != null  and applyid != ''"> and applyid = #{applyid}</if>
            <if test="measureflag != null "> and measureflag = #{measureflag}</if>
            <if test="hsflag != null "> and hsflag = #{hsflag}</if>
            <if test="shmore != null "> and shmore = #{shmore}</if>
            <if test="yxqmemo != null  and yxqmemo != ''"> and yxqmemo = #{yxqmemo}</if>
            <if test="thflag != null "> and thflag = #{thflag}</if>
            <if test="thdwname != null  and thdwname != ''"> and thdwname like concat('%', #{thdwname}, '%')</if>
            <if test="thdwcode != null  and thdwcode != ''"> and thdwcode = #{thdwcode}</if>
            <if test="cancelmemo != null  and cancelmemo != ''"> and cancelmemo = #{cancelmemo}</if>
            <if test="printflag != null "> and printflag = #{printflag}</if>
            <if test="printflagdate != null "> and printflagdate = #{printflagdate}</if>
            <if test="bdmemo != null  and bdmemo != ''"> and bdmemo = #{bdmemo}</if>
            <if test="uptime != null "> and uptime = #{uptime}</if>
            <if test="uptimestamp != null "> and uptimestamp = #{uptimestamp}</if>
        </where>
    </select>
    
    <select id="selectLPassThTById" parameterType="Long" resultMap="LPassThTResult">
        <include refid="selectLPassThTVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLPassThT" parameterType="LPassThT">
        insert into L_PASS_TH_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="validflag != null">validflag,</if>
            <if test="materialcode != null">materialcode,</if>
            <if test="materialname != null">materialname,</if>
            <if test="materialspec != null">materialspec,</if>
            <if test="materialtype != null">materialtype,</if>
            <if test="sourcecode != null">sourcecode,</if>
            <if test="sourcename != null">sourcename,</if>
            <if test="targetcode != null">targetcode,</if>
            <if test="targetname != null">targetname,</if>
            <if test="count != null">count,</if>
            <if test="weight != null">weight,</if>
            <if test="unit != null">unit,</if>
            <if test="validtime != null">validtime,</if>
            <if test="unitcode != null">unitcode,</if>
            <if test="unitname != null">unitname,</if>
            <if test="lflag != null">lflag,</if>
            <if test="leader != null">leader,</if>
            <if test="ldate != null">ldate,</if>
            <if test="pflag != null">pflag,</if>
            <if test="productman != null">productman,</if>
            <if test="pdate != null">pdate,</if>
            <if test="printnum != null">printnum,</if>
            <if test="createman != null">createman,</if>
            <if test="createdate != null">createdate,</if>
            <if test="updateman != null">updateman,</if>
            <if test="updatedate != null">updatedate,</if>
            <if test="memo != null">memo,</if>
            <if test="leadermemo != null">leadermemo,</if>
            <if test="productmemo != null">productmemo,</if>
            <if test="heatno != null">heatno,</if>
            <if test="leaveman != null">leaveman,</if>
            <if test="leavedate != null">leavedate,</if>
            <if test="leavegate != null">leavegate,</if>
            <if test="supervisor != null">supervisor,</if>
            <if test="outmemo != null">outmemo,</if>
            <if test="carno != null">carno,</if>
            <if test="attachpath != null">attachpath,</if>
            <if test="printnump != null">printnump,</if>
            <if test="cmzhao != null">cmzhao,</if>
            <if test="yxflag != null">yxflag,</if>
            <if test="yxday != null">yxday,</if>
            <if test="wzzgy != null">wzzgy,</if>
            <if test="applyid != null">applyid,</if>
            <if test="measureflag != null">measureflag,</if>
            <if test="hsflag != null">hsflag,</if>
            <if test="shmore != null">shmore,</if>
            <if test="yxqmemo != null">yxqmemo,</if>
            <if test="thflag != null">thflag,</if>
            <if test="thdwname != null">thdwname,</if>
            <if test="thdwcode != null">thdwcode,</if>
            <if test="cancelmemo != null">cancelmemo,</if>
            <if test="printflag != null">printflag,</if>
            <if test="printflagdate != null">printflagdate,</if>
            <if test="bdmemo != null">bdmemo,</if>
            <if test="uptime != null">uptime,</if>
            <if test="uptimestamp != null">uptimestamp,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="validflag != null">#{validflag},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="materialtype != null">#{materialtype},</if>
            <if test="sourcecode != null">#{sourcecode},</if>
            <if test="sourcename != null">#{sourcename},</if>
            <if test="targetcode != null">#{targetcode},</if>
            <if test="targetname != null">#{targetname},</if>
            <if test="count != null">#{count},</if>
            <if test="weight != null">#{weight},</if>
            <if test="unit != null">#{unit},</if>
            <if test="validtime != null">#{validtime},</if>
            <if test="unitcode != null">#{unitcode},</if>
            <if test="unitname != null">#{unitname},</if>
            <if test="lflag != null">#{lflag},</if>
            <if test="leader != null">#{leader},</if>
            <if test="ldate != null">#{ldate},</if>
            <if test="pflag != null">#{pflag},</if>
            <if test="productman != null">#{productman},</if>
            <if test="pdate != null">#{pdate},</if>
            <if test="printnum != null">#{printnum},</if>
            <if test="createman != null">#{createman},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="updateman != null">#{updateman},</if>
            <if test="updatedate != null">#{updatedate},</if>
            <if test="memo != null">#{memo},</if>
            <if test="leadermemo != null">#{leadermemo},</if>
            <if test="productmemo != null">#{productmemo},</if>
            <if test="heatno != null">#{heatno},</if>
            <if test="leaveman != null">#{leaveman},</if>
            <if test="leavedate != null">#{leavedate},</if>
            <if test="leavegate != null">#{leavegate},</if>
            <if test="supervisor != null">#{supervisor},</if>
            <if test="outmemo != null">#{outmemo},</if>
            <if test="carno != null">#{carno},</if>
            <if test="attachpath != null">#{attachpath},</if>
            <if test="printnump != null">#{printnump},</if>
            <if test="cmzhao != null">#{cmzhao},</if>
            <if test="yxflag != null">#{yxflag},</if>
            <if test="yxday != null">#{yxday},</if>
            <if test="wzzgy != null">#{wzzgy},</if>
            <if test="applyid != null">#{applyid},</if>
            <if test="measureflag != null">#{measureflag},</if>
            <if test="hsflag != null">#{hsflag},</if>
            <if test="shmore != null">#{shmore},</if>
            <if test="yxqmemo != null">#{yxqmemo},</if>
            <if test="thflag != null">#{thflag},</if>
            <if test="thdwname != null">#{thdwname},</if>
            <if test="thdwcode != null">#{thdwcode},</if>
            <if test="cancelmemo != null">#{cancelmemo},</if>
            <if test="printflag != null">#{printflag},</if>
            <if test="printflagdate != null">#{printflagdate},</if>
            <if test="bdmemo != null">#{bdmemo},</if>
            <if test="uptime != null">#{uptime},</if>
            <if test="uptimestamp != null">#{uptimestamp},</if>
         </trim>
    </insert>

    <update id="updateLPassThT" parameterType="LPassThT">
        update L_PASS_TH_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="validflag != null">validflag = #{validflag},</if>
            <if test="materialcode != null">materialcode = #{materialcode},</if>
            <if test="materialname != null">materialname = #{materialname},</if>
            <if test="materialspec != null">materialspec = #{materialspec},</if>
            <if test="materialtype != null">materialtype = #{materialtype},</if>
            <if test="sourcecode != null">sourcecode = #{sourcecode},</if>
            <if test="sourcename != null">sourcename = #{sourcename},</if>
            <if test="targetcode != null">targetcode = #{targetcode},</if>
            <if test="targetname != null">targetname = #{targetname},</if>
            <if test="count != null">count = #{count},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="validtime != null">validtime = #{validtime},</if>
            <if test="unitcode != null">unitcode = #{unitcode},</if>
            <if test="unitname != null">unitname = #{unitname},</if>
            <if test="lflag != null">lflag = #{lflag},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="ldate != null">ldate = #{ldate},</if>
            <if test="pflag != null">pflag = #{pflag},</if>
            <if test="productman != null">productman = #{productman},</if>
            <if test="pdate != null">pdate = #{pdate},</if>
            <if test="printnum != null">printnum = #{printnum},</if>
            <if test="createman != null">createman = #{createman},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
            <if test="updateman != null">updateman = #{updateman},</if>
            <if test="updatedate != null">updatedate = #{updatedate},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="leadermemo != null">leadermemo = #{leadermemo},</if>
            <if test="productmemo != null">productmemo = #{productmemo},</if>
            <if test="heatno != null">heatno = #{heatno},</if>
            <if test="leaveman != null">leaveman = #{leaveman},</if>
            <if test="leavedate != null">leavedate = #{leavedate},</if>
            <if test="leavegate != null">leavegate = #{leavegate},</if>
            <if test="supervisor != null">supervisor = #{supervisor},</if>
            <if test="outmemo != null">outmemo = #{outmemo},</if>
            <if test="carno != null">carno = #{carno},</if>
            <if test="attachpath != null">attachpath = #{attachpath},</if>
            <if test="printnump != null">printnump = #{printnump},</if>
            <if test="cmzhao != null">cmzhao = #{cmzhao},</if>
            <if test="yxflag != null">yxflag = #{yxflag},</if>
            <if test="yxday != null">yxday = #{yxday},</if>
            <if test="wzzgy != null">wzzgy = #{wzzgy},</if>
            <if test="applyid != null">applyid = #{applyid},</if>
            <if test="measureflag != null">measureflag = #{measureflag},</if>
            <if test="hsflag != null">hsflag = #{hsflag},</if>
            <if test="shmore != null">shmore = #{shmore},</if>
            <if test="yxqmemo != null">yxqmemo = #{yxqmemo},</if>
            <if test="thflag != null">thflag = #{thflag},</if>
            <if test="thdwname != null">thdwname = #{thdwname},</if>
            <if test="thdwcode != null">thdwcode = #{thdwcode},</if>
            <if test="cancelmemo != null">cancelmemo = #{cancelmemo},</if>
            <if test="printflag != null">printflag = #{printflag},</if>
            <if test="printflagdate != null">printflagdate = #{printflagdate},</if>
            <if test="bdmemo != null">bdmemo = #{bdmemo},</if>
            <if test="uptime != null">uptime = #{uptime},</if>
            <if test="uptimestamp != null">uptimestamp = #{uptimestamp},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLPassThTById" parameterType="Long">
        delete from L_PASS_TH_T where id = #{id}
    </delete>

    <delete id="deleteLPassThTByIds" parameterType="String">
        delete from L_PASS_TH_T where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectByCmzhao" parameterType="String" resultMap="LPassThTResult">
        <include refid="selectLPassThTVo"/>
        where cmzhao = #{cmzhao}
    </select>
    
    <select id="selectMaxId" resultType="Long">
        select nvl(max(id), 0) from l_pass_th_t
    </select>
    
</mapper>