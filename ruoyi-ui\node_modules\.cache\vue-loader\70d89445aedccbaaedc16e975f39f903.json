{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\index.vue?vue&type=style&index=0&id=12d24f57&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\index.vue", "mtime": 1756170476771}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucmVkdGV4dHsNCiAgY29sb3I6IHJlZDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmlBA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/assess/self/config", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"工号\" prop=\"workNo\">\r\n          <el-input\r\n            v-model=\"queryParams.workNo\"\r\n            placeholder=\"请输入工号\"\r\n            clearable\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input\r\n            v-model=\"queryParams.name\"\r\n            placeholder=\"请输入姓名\"\r\n            clearable\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份\" prop=\"assessRole\">\r\n          <el-select v-model=\"queryParams.assessRole\" placeholder=\"请选择身份\" clearable>\r\n            <el-option\r\n              v-for=\"dict in dicts.self_assess_role\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row>\r\n        <el-form-item label=\"是否100%挂钩公司效益\" prop=\"benefitLinkFlag\" label-width=\"200px\">\r\n          <el-select v-model=\"queryParams.benefitLinkFlag\" placeholder=\"请选择\" clearable>\r\n            <el-option\r\n              v-for=\"dict in dicts.sys_yes_no\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否挂勾钢铁轧平均分\" prop=\"averageLinkFlag\" label-width=\"192px\">\r\n          <el-select v-model=\"queryParams.averageLinkFlag\" placeholder=\"请选择\" clearable>\r\n            <el-option\r\n              v-for=\"dict in dicts.sys_yes_no\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n      \r\n      \r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"small\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"small\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-upload\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :disabled=\"upload.isUploading\"\r\n        :action=\"upload.url\"\r\n        :show-file-list=\"false\"\r\n        :multiple=\"false\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\">\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\">导入</el-button>\r\n        </el-upload>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n          <el-button size=\"small\" type=\"info\" plain icon=\"el-icon-link\" @click=\"downloadTemplate\">导入模板下载</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n          <el-button size=\"small\" type=\"info\" plain icon=\"el-icon-link\" @click=\"permissionRefresh\">权限刷新</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"selfAssessUserList\">\r\n      <!-- <el-table-column label=\"编号\" align=\"center\" prop=\"id\" /> -->\r\n      <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n      <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n      <el-table-column label=\"身份\" align=\"center\" prop=\"assessRole\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ dicts.self_assess_role[scope.row.assessRole][\"label\"] }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"部门\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-for=\"item, index in scope.row.deptList\" v-bind:key=\"index\">\r\n            {{ scope.row.deptList.length > 1 && index + 1 != scope.row.deptList.length ? item.deptName + \", \" : item.deptName}}  \r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"职务\" align=\"center\" prop=\"job\" width=\"120\"/>\r\n      <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ dicts.self_assess_post_type[scope.row.postType][\"label\"] }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"是否100%挂钩公司效益\" align=\"center\" prop=\"benefitLinkFlag\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"是否挂勾钢铁轧平均分\" align=\"center\" prop=\"averageLinkFlag\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.averageLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.averageLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleConfig(scope.row)\"\r\n          >配置</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改绩效考核-干部自评人员配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"1000px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" label-position=\"top\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n                <el-form-item label=\"工号\" prop=\"workNo\">\r\n                  <el-input v-model=\"form.workNo\" placeholder=\"请输入工号\" />\r\n                </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"姓名\" prop=\"name\">\r\n                <el-input v-model=\"form.name\" placeholder=\"请输入姓名\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"部门\" prop=\"deptIds\">\r\n                <treeselect v-model=\"form.deptIds\" @input=\"deptChange\" :multiple=\"deptMultiple\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"身份\" prop=\"assessRole\">\r\n                <el-radio-group v-model=\"form.assessRole\">\r\n                  <el-radio\r\n                    v-for=\"dict in dicts.self_assess_role\"\r\n                    @change=\"roleChange\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.value\"\r\n                  >{{dict.label}}</el-radio>\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"职务\" prop=\"job\">\r\n                <el-input v-model=\"form.job\" placeholder=\"请输入职务\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"交叉评分领导\" prop=\"postType\">\r\n                <el-select v-model=\"form.leaders\" multiple placeholder=\"请选择\">\r\n                  <el-option\r\n                    v-for=\"item in leaderOptions\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\">\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"岗位类型\" prop=\"postType\">\r\n                <el-radio-group v-model=\"form.postType\">\r\n                  <el-radio\r\n                    v-for=\"dict in dicts.self_assess_post_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.value\"\r\n                  >{{dict.label}}</el-radio>\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"是否100%挂钩公司效益\" prop=\"benefitLinkFlag\">\r\n                <el-radio-group v-model=\"form.benefitLinkFlag\">\r\n                  <el-radio\r\n                    v-for=\"dict in dicts.sys_yes_no\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.value\"\r\n                  >{{dict.label}}</el-radio>\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"是否挂勾钢铁轧平均分\" prop=\"averageLinkFlag\">\r\n                <el-radio-group v-model=\"form.averageLinkFlag\">\r\n                  <el-radio\r\n                    v-for=\"dict in dicts.sys_yes_no\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.value\"\r\n                  >{{dict.label}}</el-radio>\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导入结果对话框 -->\r\n    <el-dialog title=\"导入结果\" :visible.sync=\"openImportRes\" width=\"1000px\" append-to-body>\r\n      <el-table :data=\"importRes\">\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\"/>\r\n        <!-- <el-table-column label=\"姓名\" align=\"center\" prop=\"name\"/> -->\r\n        <el-table-column label=\"身份\" align=\"center\" prop=\"assessRole\">\r\n          <template slot-scope=\"scope\">\r\n            {{ dicts.self_assess_role[scope.row.assessRole][\"label\"] }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"部门\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-for=\"item, index in scope.row.deptList\" v-bind:key=\"index\" :class=\"item.deptStatus ? '':'redtext'\">\r\n              {{ scope.row.deptList.length > 1 && index + 1 != scope.row.deptList.length ? item.deptName + \", \" : item.deptName}}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"是否100%挂钩公司效益\" align=\"center\" prop=\"benefitLinkFlag\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.averageLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.averageLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"是否挂勾钢铁轧平均分\" align=\"center\" prop=\"averageLinkFlag\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.averageLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.averageLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"导入结果\" align=\"center\" prop=\"msg\" />\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { getTemplateFile } from \"@/api/templateFile/list\";\r\nimport { listSelfAssessUser, getSelfAssessUser, delSelfAssessUser, addSelfAssessUser, updateSelfAssessUser, permissionRefresh, listSelfAssessUserAll } from \"@/api/assess/self/user\";\r\nimport { listDept } from \"@/api/assess/lateral/dept\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"SelfAssessUser\",\r\n  components: {\r\n    Treeselect\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 绩效考核-干部自评人员配置表格数据\r\n      selfAssessUserList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        workNo: null, \r\n        name: null,\r\n        assessRole: null,\r\n        benefitLinkFlag: null,\r\n        averageLinkFlag: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        deptIds:[{required:true, message:\"请选择部门\"}],\r\n        workNo:[{required:true, message:\"请填写工号\"}],\r\n        name:[{required:true, message:\"请输入姓名\"}],\r\n        assessRole:[{required:true, message:\"请选择身份\"}]\r\n      },\r\n      // 字典\r\n      dicts:{\r\n        self_assess_role:[],\r\n        sys_yes_no:[],\r\n        self_assess_post_type:[]\r\n      },\r\n      // 部门下拉树\r\n      deptOptions:[],\r\n      // 领导下拉框\r\n      leaderOptions:[],\r\n      // 导入参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: 'Bearer ' + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/web/selfAssessUser/importInfo\",\r\n      },\r\n      // 导入结果\r\n      importRes:[],\r\n      openImportRes:false,\r\n      // 部门是否可多选\r\n      deptMultiple:false,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getTreeselect();\r\n    this.getDicts(\"self_assess_role\").then(response => {\r\n      this.dicts.self_assess_role = this.formatterDict(response.data);\r\n    });\r\n    this.getDicts(\"sys_yes_no\").then(response => {\r\n      this.dicts.sys_yes_no = this.formatterDict(response.data);\r\n    });\r\n    this.getDicts(\"self_assess_post_type\").then(response => {\r\n      this.dicts.self_assess_post_type = this.formatterDict(response.data);\r\n    });\r\n    this.getLeaderList();\r\n  },\r\n  methods: {\r\n    formatterDict(dict){\r\n      let result = []\r\n      dict.forEach(dict => {\r\n        result.push({\r\n          label:dict.dictLabel,\r\n          value:dict.dictValue\r\n        })\r\n      });\r\n      return result;\r\n    },\r\n\r\n    /**获取条线线领导列表 */\r\n    getLeaderList(){\r\n      listSelfAssessUserAll({assessRole:\"2\"}).then(res => {\r\n        console.log(res)\r\n        if(res.code == 200){\r\n          let leaderOptions = [];\r\n          res.data.forEach(item => {\r\n            leaderOptions.push({\r\n              label:item.name,\r\n              value:item.workNo\r\n            })\r\n          })\r\n          this.leaderOptions = leaderOptions;\r\n        }\r\n      })\r\n    },\r\n    /** 查询绩效考核-干部自评人员配置列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listSelfAssessUser(this.queryParams).then(response => {\r\n        this.selfAssessUserList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        workNo: null,\r\n        name: null,\r\n        assessRole: '0',\r\n        benefitLinkFlag: 'N',\r\n        averageLinkFlag: 'N',\r\n        postType:'0',\r\n        leaders:[]\r\n      };\r\n      this.deptMultiple = false;\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加自评人员配置\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id\r\n      getSelfAssessUser({id:id}).then(response => {\r\n        this.form = response.data;\r\n        if(this.form.assessRole == \"2\"){\r\n          this.deptMultiple = true;\r\n        }else{\r\n          this.deptMultiple = false;\r\n        }\r\n        this.open = true;\r\n        this.title = \"修改自评人员配置\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      console.log(this.form)\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if(!Array.isArray(this.form.deptIds)){\r\n            this.form.deptIds = [this.form.deptIds]\r\n          }\r\n          if (this.form.id != null) {\r\n            updateSelfAssessUser(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addSelfAssessUser(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id ;\r\n      this.$modal.confirm('是否确认删除编号为\"' + id + '\"的数据项？').then(function() {\r\n        return delSelfAssessUser({id:id});\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('selfAssessUser/selfAssessUser/export', {\r\n        ...this.queryParams\r\n      }, `selfAssessUser_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 转换横向评价部门数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.deptId,\r\n        label: node.deptName,\r\n        children: node.children\r\n      };\r\n    },\r\n\t  /** 查询横向评价部门下拉树结构 */\r\n    getTreeselect() {\r\n      listDept().then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n      });\r\n    },\r\n\r\n    /** 配置点击事件 */\r\n    handleConfig(row){\r\n      this.$router.push({\r\n        path:\"/assess/self/user/detail\",\r\n        query:{\r\n          userId:row.id\r\n        }\r\n      })\r\n    },\r\n    // 身份改变事件\r\n    roleChange(value){\r\n      console.log(value)\r\n      if(value == '2'){\r\n        if(this.form.deptIds){\r\n          if(!Array.isArray(this.form.deptIds)){\r\n            this.form.deptIds = [this.form.deptIds]\r\n          }\r\n        }\r\n        this.deptMultiple = true;\r\n      }else{\r\n        if(this.form.deptIds){\r\n          this.form.deptIds = this.form.deptIds[0]\r\n        }\r\n        this.deptMultiple = false;\r\n      }\r\n    },\r\n    // 部门改变事件\r\n    deptChange(value){\r\n      console.log(value)\r\n    },\r\n    \r\n    handleFileUploadProgress(){\r\n        this.upload.isUploading = true;\r\n    },\r\n    handleFileSuccess(response){\r\n        this.upload.isUploading = false;\r\n        console.log(response)\r\n        this.handleQuery();\r\n        this.importRes = response.data;\r\n        this.openImportRes = true;\r\n    },\r\n\r\n    // 模板下载\r\n    downloadTemplate(){\r\n      getTemplateFile({id:\"41\"}).then(res => {\r\n          if(res.code == 200){\r\n            let localUrl = window.location.host;\r\n            if(localUrl === \"************:8099\"){\r\n              res.data.url = res.data.url.replace(\"ydxt.citicsteel.com:8099\",\"************:8099\");\r\n            }\r\n            let url = res.data.url;\r\n            window.open(url);\r\n          }\r\n      })\r\n    },\r\n\r\n    permissionRefresh(){\r\n      permissionRefresh().then(res => {\r\n        if(res.code == 200){\r\n          this.$modal.msgSuccess(\"刷新成功\");\r\n        }\r\n      })\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.redtext{\r\n  color: red;\r\n}\r\n</style>\r\n"]}]}