{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue?vue&type=template&id=c9a9bf16&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue", "mtime": 1756200516023}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}