<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-row>
          <el-form-item label="考核年月" prop="assessDate">
            <el-date-picker
              v-model="queryParams.assessDate"
              type="month"
              value-format="yyyy-M"
              format="yyyy 年 M 月"
              placeholder="选择考核年月"
              :clearable="false">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="部门" prop="deptId">
            <treeselect style="width: 200px;" v-model="queryParams.deptId" :multiple="false" :options="deptOptions" :normalizer="normalizer" :disable-branch-nodes="true" placeholder="请选择部门" />
          </el-form-item>
          <el-form-item label="工号" prop="name">
            <el-input
              v-model="queryParams.workNo"
              placeholder="请输入工号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 200px;">
              <el-option label="未提交/退回" value="0"></el-option>
              <el-option label="部门领导评分" value="1"></el-option>
              <el-option label="事业部评分" value="2"></el-option>
              <el-option label="运改部/组织部审核" value="3"></el-option>
              <el-option label="总经理部评分" value="4"></el-option>
              <el-option label="已完成" value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-download"
            size="small"
            @click="handleExport"
          >导出列表</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="small"
            @click="handleBatchExport"
          >批量导出(zip)</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-download"
            size="small"
            @click="handleExportTechnicalSummary"
          >导出技术序列业绩汇总表</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-download"
            size="small"
            @click="handleExportAdministrativeSummary"
          >导出行政序列业绩汇总表</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-upload
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :disabled="upload.isUploading"
          :action="upload.url"
          :show-file-list="false"
          :multiple="false"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess">
              <el-button size="small" type="success" plain icon="el-icon-download">导入最终分数</el-button>
          </el-upload>
          <!-- <el-button
            type="success"
            plain
            icon="el-icon-upload2"
            size="small"
            @click="handleImportFinalScore"
          >导入最终分数</el-button> -->
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="listInfo">
        <el-table-column label="工号" align="center" prop="workNo" width="120"/>
        <el-table-column label="姓名" align="center" prop="name" width="120"/>
        <el-table-column label="部门" align="center" prop="deptName" ></el-table-column>
        <el-table-column label="自评分" align="center" prop="selfScore" ></el-table-column>
        <el-table-column label="部门领导评分" align="center" prop="selfScore" >
          <template slot-scope="scope">
            {{ scope.row.deptScore ? scope.row.deptScore : ""}}
          </template>
        </el-table-column>
        <el-table-column label="事业部评分" align="center" prop="selfScore" >
          <template slot-scope="scope">
            {{ scope.row.businessScore ? scope.row.businessScore : ""}}
          </template>
        </el-table-column>
        <el-table-column label="运改组织部审核" align="center" prop="selfScore" >
          <template slot-scope="scope">
            {{ scope.row.organizationScore ? scope.row.organizationScore : ""}}
          </template>
        </el-table-column>
        <el-table-column label="总经理部评分" align="center" prop="selfScore" >
          <template slot-scope="scope">
            {{ scope.row.leaderScore ? scope.row.leaderScore : ""}}
          </template>
        </el-table-column>
        <el-table-column label="最终分数" align="center" prop="finalScore" >
          <template slot-scope="scope">
            {{ scope.row.finalScore ? scope.row.finalScore : ""}}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '0' && scope.row.rejectReason" type="danger">退 回</el-tag>
            <el-tag v-if="scope.row.status == '0' && !scope.row.rejectReason" type="info">未提交</el-tag>
            <el-tag v-if="scope.row.status == '1'" type="warning">部门领导评分</el-tag>
            <el-tag v-if="scope.row.status == '2'" type="warning">事业部评分</el-tag>
            <el-tag v-if="scope.row.status == '3'" type="warning">运改部/组织部审核</el-tag>
            <el-tag v-if="scope.row.status == '4'" type="warning">总经理部评分</el-tag>
            <el-tag v-if="scope.row.status == '5'" type="success">已完成</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleDetail(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <el-dialog
        :visible.sync="open"
        fullscreen>
        <h3 style="text-align: center;">月度业绩考核表</h3>
          <el-descriptions class="margin-top" :column="3">
            <el-descriptions-item>
              <template slot="label">
                姓名
              </template>
              {{ checkInfo.name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                部门
              </template>
              {{ checkInfo.deptName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                考核年月
              </template>
              {{ checkInfo.assessDate }}
            </el-descriptions-item>
          </el-descriptions>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="small"
                @click="handleExportDetail"
              >导出</el-button>
            </el-col>
          </el-row>
          <el-table v-loading="loading" :data="checkInfo.list"
            :span-method="objectSpanMethod" border>
            <el-table-column label="类型" align="center" prop="item" width="120"/>
            <el-table-column label="指标" align="center" prop="category" width="150"/>
            <el-table-column label="目标" align="center" prop="target" width="180" />
            <el-table-column label="评分标准" align="center" prop="standard" />
            <el-table-column label="完成实绩（若扣分，写明原因）" align="center" prop="performance" />
            <el-table-column label="加减分" align="center" prop="dePoints" width="150" />
            <el-table-column label="加减分原因" align="center" prop="pointsReason" width="180" />
          </el-table>
          <el-form size="small" :inline="false" label-width="200px" style="margin-top: 10px;">
            <el-form-item label="自评分数 / 签名：">
              <span >{{ checkInfo.selfScore + " 分 / " + checkInfo.name }}</span>
            </el-form-item>
            <el-form-item v-if="checkInfo.deptScore && checkInfo.deptUserName" label="部门领导评分 / 签名：">
              <span >{{ checkInfo.deptScore + " 分 / " + checkInfo.deptUserName }}</span>
            </el-form-item>
            <el-form-item v-if="checkInfo.businessUserName && checkInfo.businessScore" label="事业部领导评分 / 签名：">
              <span>{{ checkInfo.businessScore + " 分 / " + checkInfo.businessUserName }}</span>
            </el-form-item>
            <el-form-item v-if="checkInfo.organizationScore && checkInfo.organizationUserName" label="运改组织部审核：">
              <span >{{ checkInfo.organizationScore + " 分 / " + checkInfo.organizationUserName }}</span>
            </el-form-item>
            <el-form-item v-if="checkInfo.leaderScore && checkInfo.leaderName" label="总经理部领导评分 / 签名：">
              <span >{{ checkInfo.leaderScore + " 分 / " + checkInfo.leaderName }}</span>
            </el-form-item>
          </el-form>
          <div style="text-align: center;">
            <el-button plain type="info" @click="cancel">返 回</el-button>
          </div>
      </el-dialog>

    </div>
  </template>

<script>
import { listInfo, getInfo, exportInfo, exportDetail, batchExportDetail, exportTechnicalPerformanceSummary, exportAdministrativePerformanceSummary } from "@/api/assess/self/info";
import { getToken } from "@/utils/auth";
import { downLoadZip } from "@/utils/zipdownload";
import { listDept } from "@/api/assess/lateral/dept";
import { formatDateYm } from "@/utils/index";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
    name: "SelfAssessCollect",
    components: {
        Treeselect
    },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 绩效考核-干部自评人员配置表格数据
        listInfo: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          workNo: null,
          name:null,
          deptId:null,
          assessDate:null,
          status:null
        },
        // 表单参数
        form: {
          id:null,
          // 部门领导评分
          deptScore:null,
          // 事业部评分
          businessScore:null,
          // 条线领导评分
          leaderScore:null,
        },
        // 表单校验
        rules: {
        },
        deptOptions:[],
        openCheck:false,
        checkInfo:{
          name:null,
          assessDate:null,
          deptName:null,
          list:[]
        },
        // 合并单元格
        spanList:[],
        // 导入参数
        upload: {
          // 是否禁用上传
          isUploading: false,
          // 设置上传的请求头部
          headers: { Authorization: 'Bearer ' + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/web/selfAssess/info/importFinalScore",
        },
      };
    },
    created() {
        this.queryParams.assessDate = formatDateYm(new Date().getTime())
        // this.getSelfAssessUser();
        // this.getCheckDeptList();
        this.getTreeselect();
        this.getList();
    },
    methods: {
        // 获取部门信息
        /** 查询横向评价部门下拉树结构 */
        getTreeselect() {
            listDept().then(response => {
                this.deptOptions = this.handleTree(response.data, "deptId", "parentId");
            });
        },
        /** 转换横向评价部门数据结构 */
        normalizer(node) {
            if (node.children && !node.children.length) {
                delete node.children;
            }
            return {
                id: node.deptId,
                label: node.deptName,
                children: node.children
            };
        },
        /** 查询绩效考核-干部自评待审核列表 */
        getList() {
            this.loading = true;
            listInfo(this.queryParams).then(response => {
                this.listInfo = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                deptScore: null,
                businessScore: null,
                leaderScore: null,
            };
            // this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 审批详情
        handleDetail(row){
            getInfo({id:row.id}).then(res => {
                console.log(res);
                if(res.code == 200){
                    this.checkInfo = res.data;
                    let list = JSON.parse(res.data.content);
                    this.handleSpanList(list);
                    this.checkInfo.list = list;
                }
                this.open = true
            })
        },

        // 处理列表
        handleSpanList(data){
            let spanList = [];
            let flag = 0;
            for(let i = 0; i < data.length; i++){
            // 相同考核项合并
            if(i == 0){
                spanList.push({
                rowspan: 1,
                colspan: 1
                })
            }else{
                if(data[i - 1].item == data[i].item){
                spanList.push({
                    rowspan: 0,
                    colspan: 0
                })
                spanList[flag].rowspan += 1;
                }else{
                spanList.push({
                    rowspan: 1,
                    colspan: 1
                })
                flag = i;
                }
            }
            }
            this.spanList = spanList;
        },

        // 合并单元格方法
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 第一列相同项合并
            if (columnIndex === 0) {
            return this.spanList[rowIndex];
            }
            // 类别无内容 合并
            if(columnIndex === 1){
            if(!row.category){
                return {
                rowspan: 0,
                colspan: 0
                }
            }
            }
            if(columnIndex === 2){
            if(!row.category){
                return {
                rowspan: 1,
                colspan: 2
                }
            }
            }
        },

        // 导出按钮点击事件
        handleExport(){
          exportInfo({...this.queryParams}).then(res => {
            console.log(res)
            this.download(res.msg,"月度绩效考核汇总表.xlsx")
          })
        },

        // 详细导出
        handleExportDetail(){
          exportDetail({id:this.checkInfo.id}).then(res => {
            console.log(res)
            this.download(res.msg,"月度绩效考核表.xlsx")
          })
        },

        handleBatchExport(){
          downLoadZip("/web/selfAssess/info/batchExportDetail?assessDate=" + this.queryParams.assessDate, "ruoyi");
        },

        // 导出技术序列业绩汇总表
        handleExportTechnicalSummary(){
          if (!this.queryParams.assessDate) {
            this.$modal.msgError("请选择考核年月");
            return;
          }
          exportTechnicalPerformanceSummary({assessDate: this.queryParams.assessDate}).then(res => {
            console.log(res)
            this.download(res.msg,"技术序列业绩汇总表.xlsx")
          }).catch(error => {
            console.error("导出失败：", error);
            this.$modal.msgError("导出失败，请稍后重试");
          })
        },

        // 导出行政序列业绩汇总表
        handleExportAdministrativeSummary(){
          if (!this.queryParams.assessDate) {
            this.$modal.msgError("请选择考核年月");
            return;
          }
          exportAdministrativePerformanceSummary({assessDate: this.queryParams.assessDate}).then(res => {
            console.log(res)
            this.download(res.msg,"行政序列业绩汇总表.xlsx")
          }).catch(error => {
            console.error("导出失败：", error);
            this.$modal.msgError("导出失败，请稍后重试");
          })
        },

        // 导入相关
        handleFileUploadProgress(){
        this.upload.isUploading = true;
        },
        handleFileSuccess(response){
            this.upload.isUploading = false;
            console.log(response)
            // this.handleQuery();
            // this.importRes = response.data;
            // this.openImportRes = true;
        },
    }
};
</script>
