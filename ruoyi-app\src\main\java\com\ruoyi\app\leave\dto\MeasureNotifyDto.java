package com.ruoyi.app.leave.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/10/23 14:58
 */
@ApiModel(description ="计量称重信息通知")
public class MeasureNotifyDto {
    @ApiModelProperty(value = "地磅ID", example = "123456789")
    private String terminalId;
    @ApiModelProperty(value = "地磅名称", example = "123456789")
    private String terminalName;
    @ApiModelProperty(value = "计划号", example = "123456789")
    private String planId;
    @ApiModelProperty(value = "车牌号", example = "苏B18TH6")
    private String carNo;
    @ApiModelProperty(value = "毛重", example = "10")
    private BigDecimal gross;
    @ApiModelProperty(value = "毛重时间", example = "2023-10-23 00:00:00")
    private String grossTime;
//    @ApiModelProperty(value = "称重类型  1-毛重 2-净重", example = "1")
//    private Integer weightType;
    @ApiModelProperty(value = "皮重", example = "8")
    private BigDecimal tare;
    @ApiModelProperty(value = "皮重时间", example = "2023-10-23 00:00:00")
    private String tareTime;

    @ApiModelProperty(value = "验配ID", example = "11623112400001")
    private String matchId;

    @ApiModelProperty(value = "业务类型", example = "6")
    private int operaType;

    @ApiModelProperty(value = "称重类型", example = "1")
    private String measureType;

    public String getMatchId() {
        return matchId;
    }

    public void setMatchId(String matchId) {
        this.matchId = matchId;
    }

    public int getOperaType() {
        return operaType;
    }

    public void setOperaType(int operaType) {
        this.operaType = operaType;
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public BigDecimal getGross() {
        return gross;
    }

    public void setGross(BigDecimal gross) {
        this.gross = gross;
    }

    public String getGrossTime() {
        return grossTime;
    }

    public void setGrossTime(String grossTime) {
        this.grossTime = grossTime;
    }

    public BigDecimal getTare() {
        return tare;
    }

    public void setTare(BigDecimal tare) {
        this.tare = tare;
    }

    public String getTareTime() {
        return tareTime;
    }

    public void setTareTime(String tareTime) {
        this.tareTime = tareTime;
    }
}
