package com.ruoyi.app.leave.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LCarplanTMapper;
import com.ruoyi.app.leave.domain.LCarplanT;
import com.ruoyi.app.leave.service.ILCarplanTService;

/**
 * 跨区调拨主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class LCarplanTServiceImpl implements ILCarplanTService 
{
    @Autowired
    private LCarplanTMapper lCarplanTMapper;

    /**
     * 查询跨区调拨主
     * 
     * @param id 跨区调拨主ID
     * @return 跨区调拨主
     */
    @Override
    public LCarplanT selectLCarplanTById(Long id)
    {
        return lCarplanTMapper.selectLCarplanTById(id);
    }

    /**
     * 查询跨区调拨主列表
     * 
     * @param lCarplanT 跨区调拨主
     * @return 跨区调拨主
     */
    @Override
    public List<LCarplanT> selectLCarplanTList(LCarplanT lCarplanT)
    {
        return lCarplanTMapper.selectLCarplanTList(lCarplanT);
    }

    /**
     * 新增跨区调拨主
     * 
     * @param lCarplanT 跨区调拨主
     * @return 结果
     */
    @Override
    public int insertLCarplanT(LCarplanT lCarplanT)
    {
        return lCarplanTMapper.insertLCarplanT(lCarplanT);
    }

    /**
     * 修改跨区调拨主
     * 
     * @param lCarplanT 跨区调拨主
     * @return 结果
     */
    @Override
    public int updateLCarplanT(LCarplanT lCarplanT)
    {
        return lCarplanTMapper.updateLCarplanT(lCarplanT);
    }

    /**
     * 批量删除跨区调拨主
     * 
     * @param ids 需要删除的跨区调拨主ID
     * @return 结果
     */
    @Override
    public int deleteLCarplanTByIds(Long[] ids)
    {
        return lCarplanTMapper.deleteLCarplanTByIds(ids);
    }

    /**
     * 删除跨区调拨主信息
     * 
     * @param id 跨区调拨主ID
     * @return 结果
     */
    @Override
    public int deleteLCarplanTById(Long id)
    {
        return lCarplanTMapper.deleteLCarplanTById(id);
    }
}
