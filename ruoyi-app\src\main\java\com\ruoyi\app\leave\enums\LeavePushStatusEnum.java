package com.ruoyi.app.leave.enums;

/**
 * 推送状态枚举
 * 
 * <AUTHOR>
 */
public enum LeavePushStatusEnum {
    
    WAITING(0, "待推送"),
    SUCCESS(1, "推送成功"),
    FAIL(2, "推送失败");

    private final Integer code;
    private final String info;

    LeavePushStatusEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 枚举对象
     */
    public static LeavePushStatusEnum getByCode(Integer code) {
        for (LeavePushStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 