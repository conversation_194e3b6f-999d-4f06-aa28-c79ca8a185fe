package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.LeaveDepartment;

/**
 * 出门证部门（厂内单位）Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface LeaveDepartmentMapper
{
    /**
     * 查询出门证部门（厂内单位）
     *
     * @param id 出门证部门（厂内单位）ID
     * @return 出门证部门（厂内单位）
     */
    public LeaveDepartment selectLeaveDepartmentById(Long id);

    /**
     * 查询出门证部门（厂内单位）列表
     *
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 出门证部门（厂内单位）集合
     */
    public List<LeaveDepartment> selectLeaveDepartmentList(LeaveDepartment leaveDepartment);

    /**
     * 新增出门证部门（厂内单位）
     *
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 结果
     */
    public int insertLeaveDepartment(LeaveDepartment leaveDepartment);

    /**
     * 修改出门证部门（厂内单位）
     *
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 结果
     */
    public int updateLeaveDepartment(LeaveDepartment leaveDepartment);

    /**
     * 作废出门证部门（厂内单位）
     *
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 结果
     */
    public int cancelLeaveDepartment(LeaveDepartment leaveDepartment);

    /**
     * 删除出门证部门（厂内单位）
     *
     * @param id 出门证部门（厂内单位）ID
     * @return 结果
     */
    public int deleteLeaveDepartmentById(Long id);

    /**
     * 批量删除出门证部门（厂内单位）
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteLeaveDepartmentByIds(Long[] ids);

    List<LeaveDepartment> selectLeaveDepartmentByDeptName(LeaveDepartment leaveDepartment);
}
