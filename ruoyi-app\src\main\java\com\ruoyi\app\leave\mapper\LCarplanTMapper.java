package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.LCarplanT;

/**
 * 跨区调拨主Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface LCarplanTMapper 
{
    /**
     * 查询跨区调拨主
     * 
     * @param id 跨区调拨主ID
     * @return 跨区调拨主
     */
    public LCarplanT selectLCarplanTById(Long id);

    /**
     * 查询跨区调拨主列表
     * 
     * @param lCarplanT 跨区调拨主
     * @return 跨区调拨主集合
     */
    public List<LCarplanT> selectLCarplanTList(LCarplanT lCarplanT);

    /**
     * 新增跨区调拨主
     * 
     * @param lCarplanT 跨区调拨主
     * @return 结果
     */
    public int insertLCarplanT(LCarplanT lCarplanT);

    /**
     * 修改跨区调拨主
     * 
     * @param lCarplanT 跨区调拨主
     * @return 结果
     */
    public int updateLCarplanT(LCarplanT lCarplanT);

    /**
     * 删除跨区调拨主
     * 
     * @param id 跨区调拨主ID
     * @return 结果
     */
    public int deleteLCarplanTById(Long id);

    /**
     * 批量删除跨区调拨主
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteLCarplanTByIds(Long[] ids);

    /**
     * 查询跨区调拨主
     *
     * @param planid 出门证号
     * @return 跨区调拨主
     */
    public LCarplanT selectByPlanid(String planid);

    /**
     * 查询最大ID
     * 
     * @return 最大ID
     */
    public Long selectMaxId();
}
