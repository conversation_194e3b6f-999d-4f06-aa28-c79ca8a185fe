package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.StoreoutKqdbMeasure;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 库区调拨出库Mapper接口
 * 
 * <AUTHOR>
 */
public interface StoreoutKqdbMapper 
{
    /**
     * 查询库区调拨出库
     * 
     * @param id 库区调拨出库主键
     * @return 库区调拨出库
     */
    @DataSource(DataSourceType.XCC1)
    public StoreoutKqdbMeasure selectStoreoutKqdbById(Long id);

    /**
     * 查询库区调拨出库列表
     * 
     * @param storeoutKqdbMeasure 库区调拨出库
     * @return 库区调拨出库集合
     */
    @DataSource(DataSourceType.XCC1)
    public List<StoreoutKqdbMeasure> selectStoreoutKqdbList(StoreoutKqdbMeasure storeoutKqdbMeasure);

    /**
     * 新增库区调拨出库
     * 
     * @param storeoutKqdbMeasure 库区调拨出库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int insertStoreoutKqdb(StoreoutKqdbMeasure storeoutKqdbMeasure);

    /**
     * 修改库区调拨出库
     * 
     * @param storeoutKqdbMeasure 库区调拨出库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int updateStoreoutKqdb(StoreoutKqdbMeasure storeoutKqdbMeasure);

    /**
     * 删除库区调拨出库
     * 
     * @param id 库区调拨出库主键
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreoutKqdbById(Long id);

    /**
     * 批量删除库区调拨出库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreoutKqdbByIds(Long[] ids);

    /**
     * 根据匹配ID删除库区调拨出库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreoutKqdbByMatchid(String matchid);
} 