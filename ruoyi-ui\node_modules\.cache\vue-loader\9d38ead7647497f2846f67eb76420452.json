{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\dimensionalityOverview.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\dimensionalityOverview.vue", "mtime": 1756170476815}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQppbXBvcnQgYXhpb3MgZnJvbSAiYXhpb3MiOw0KaW1wb3J0ICogYXMgWExTWCBmcm9tICJ4bHN4IjsNCmltcG9ydCB7DQogIGRpbWVuc2lvbmFsaXR5bGlzdFBlcm1pc3Npb25MaXN0DQp9IGZyb20gIkAvYXBpL3RZankvZGltZW5zaW9uYWxpdHkiOw0KaW1wb3J0IHsgZGF0ZVVwZGF0ZUxpc3QgfSBmcm9tICJAL2FwaS90WWp5L2Fuc3dlciI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdEaW1lbnNpb25hbGl0eU92ZXJ2aWV3JywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KDQogICAgICAvLyDmioDnu4/mjIfmoIfmlbDmja4NCiAgICAgIHRlY2hJbmRpY2F0b3JzOiBbXSwNCg0KICAgICAgZGV0YWlsRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBjdXJyZW50SW5kaWNhdG9yOiBudWxsLA0KICAgICAgLy8gRXhjZWzmlofku7bliqDovb3nirbmgIENCiAgICAgIGV4Y2VsTG9hZGluZzogZmFsc2UsDQogICAgICBhZG1pblNob3c6IjAiLA0KDQogICAgICAvLyDlvZPliY3mmL7npLrnmoTpg6jpl6jvvIjnlKjkuo7mnIjluqbotovlir/lm77liIfmjaLvvIkNCiAgICAgIC8vIGN1cnJlbnRCdXNpbmVzc1VuaXQ6ICfngrzpk4HkuovkuJrpg6gnLCAvLyDlvZPliY3pgInmi6nnmoTkuovkuJrpg6gNCiAgICAgIC8vIGN1cnJlbnREZXBhcnRtZW50OiAnJywNCiAgICAgIC8vIGN1cnJlbnRJbmRpY2F0b3I6ICcnLA0KICAgICAgLy8gYXV0b1N3aXRjaERlcGFydG1lbnQ6IHRydWUsDQogICAgICAvLyDlrprml7blmagNCiAgICAgIHRyZW5kQ2hhcnRUaW1lcjogbnVsbCwgLy8g55So5LqO5YiG5Y6C5YiH5o2iDQogICAgICBidXNpbmVzc1VuaXRUaW1lcjogbnVsbCwgLy8g55So5LqO5LqL5Lia6YOo5YiH5o2iDQogICAgICBzY3JvbGxUaW1lcjogbnVsbCwNCiAgICAgIHRhYmxlU2Nyb2xsUGF1c2VkOiBmYWxzZSwgLy8g5piv5ZCm5pqC5YGc6KGo5qC86Ieq5Yqo5rua5YqoDQogICAgICBjb21wbGV0aW9uQ2hhcnRUaW1lcjogbnVsbCwgLy8g55So5LqO5a6M5oiQ546H5Zu+6KGo5rua5YqoDQogICAgICBpbmRpY2F0b3JDYXJkc1Njcm9sbFRpbWVyOiBudWxsLCAvLyDnlKjkuo7mjIfmoIfljaHniYfmu5rliqgNCiAgICAgIGNvbXBsZXRpb25DaGFydFNjcm9sbERpcmVjdGlvbjogJ2Rvd24nLCAvLyDmu5rliqjmlrnlkJHvvJondXAnIOaIliAnZG93bicNCiAgICAgIGNvbXBsZXRpb25DaGFydFNjcm9sbFBhdXNlZDogZmFsc2UsIC8vIOaYr+WQpuaaguWBnOiHquWKqOa7muWKqA0KICAgICAgaW5kaWNhdG9yQ2FyZHNTY3JvbGxQYXVzZWQ6IGZhbHNlLCAvLyDmmK/lkKbmmoLlgZzmjIfmoIfljaHniYfoh6rliqjmu5rliqgNCiAgDQogICAgICBlbmVyZ3lEZXB0VGltZXI6IG51bGwsIC8vIOiDvea6kOmDqOmXqOWIh+aNouWumuaXtuWZqA0KICAgICAgc2Nyb2xsU3BlZWQ6IDUwLCAvLyDmu5rliqjpgJ/luqbvvIzmlbDlgLzotorlpKfpgJ/luqbotormhaINCiAgICAgIA0KICAgICAgLy8g5LqL5Lu25aSE55CG5Zmo5byV55SoDQogICAgICBjaGFydE1vdXNlT3ZlckhhbmRsZXI6IG51bGwsDQogICAgICBjaGFydE1vdXNlT3V0SGFuZGxlcjogbnVsbCwNCiAgICAgIHRhYmxlTW91c2VFbnRlckhhbmRsZXI6IG51bGwsIC8vIOihqOagvOm8oOagh+i/m+WFpeWkhOeQhuWZqA0KICAgICAgdGFibGVNb3VzZUxlYXZlSGFuZGxlcjogbnVsbCwgLy8g6KGo5qC86byg5qCH56a75byA5aSE55CG5ZmoDQogICAgICANCiAgICAgIC8vIOW9k+WJjemAieaLqeeahOiDvea6kOexu+Wei+WSjOmDqOmXqA0KICAgICAgY3VycmVudEVuZXJneVR5cGU6ICdlbGVjdHJpY2l0eScsDQogICAgICBjdXJyZW50RW5lcmd5RGVwdDogJ+eCvOmTgeWIhuWOgicsIC8vIOS/ruaUueS4uuWIhuWOguWQjeensA0KICAgICAgDQogICAgICAvLyDpg6jpl6jliJfooagNCiAgICAgIGRlcGFydG1lbnRzOiBbJ+eCvOmTgeS6i+S4mumDqCcsICfngrzpkqLkuovkuJrpg6gnLCAn6L2n6ZKi5LqL5Lia6YOoJywgJ+mprOenkeaJmOmSoueQgycsICfnibnmnb/kuovkuJrpg6gnLCAn5Yqo5Yqb5LqL5Lia6YOoJywgJ+eJqea1geS6i+S4mumDqCcsICfmo4Dkv67kuovkuJrpg6gnXSwNCiAgICAgIA0KICAgICAgLy8g5oyH5qCH5a6M5oiQ5oOF5Ya15pWw5o2uIC0g5LuO5Zu+54mH5Lit5o+Q5Y+W55qE5pWw5o2uDQogICAgICBjb21wbGV0aW9uRGF0YTogWw0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfngrzpk4HkuovkuJrpg6gt54K86ZOB5YiG5Y6CJywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJ+e7vOWQiOeHg+aWmeavlCcsIHRhcmdldDogNTEyLjMzLCBhY3R1YWw6IDUyMy40NSwgdW5pdDogJ2tnL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzUyMC4yNiwgNTIzLjY3LCA1MjMuNDUsIDUxOS4wNl0sIGNvbXBsZXRpb25SYXRlczogWyctMS41NSUnLCAnLTIuMDAlJywgJy0yLjE3JScsICctMS4zMSUnXSB9LA0KICAgICAgICAgIHsgbmFtZTogJ+W3peW6j+iDveiAlycsIHRhcmdldDogMzY5LjkxLCBhY3R1YWw6IDM2OS42OSwgdW5pdDogJ2tnQ2UvdCcsIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwgdmFsdWVzOiBbMzY5Ljc0LCAzNjcuOTgsIDM2OS42OSwgMzYzLjc2XSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDUlJywgJzAuNTIlJywgJzAuMDYlJywgJzEuNjYlJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICc0MDDpq5jngonlt6Xluo/og73ogJcnLCB0YXJnZXQ6IDQzLjcwLCBhY3R1YWw6IDQzLjU2LCB1bml0OiAna2dDZS90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFs0My42NywgNDMuNjcsIDQzLjU2LCA0My42N10sIGNvbXBsZXRpb25SYXRlczogWycwLjA3JScsICcwLjA3JScsICcwLjMyJScsICcwLjA3JSddIH0sDQogICAgICAgICAgeyBuYW1lOiAnMzYw6auY54KJ5bel5bqP6IO96ICXJywgdGFyZ2V0OiA0NS4wMSwgYWN0dWFsOiA0NC44NCwgdW5pdDogJ2tnQ2UvdCcsIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwgdmFsdWVzOiBbNDUuMDAsIDQ0LjkzLCA0NC44NCwgNDQuOTldLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMiUnLCAnMC4wNyUnLCAnMC4zOCUnLCAnMC4wNCUnXSB9DQogICAgICAgIF19LA0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfngrzpk4HkuovkuJrpg6gt54On57uT5YiG5Y6CJywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJ+W3peW6j+iDveiAlycsIHRhcmdldDogMTU0LjY1LCBhY3R1YWw6IDE1NC41NiwgdW5pdDogJ2tnQ2UvdCcsIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwgdmFsdWVzOiBbMTU0LjY1LCAxNTQuOTEsIDE1NC41NiwgMTU0LjU3XSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDElJywgJzAuMDMlJywgJzAuMDYlJywgJzAuMDUlJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfnn7/muKPlop7luYUnLCB0YXJnZXQ6IDE2LjAwLCBhY3R1YWw6IDE1LjgwLCB1bml0OiAna2dDZS90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFsxNS44MiwgMTUuOTQsIDE1LjgwLCAxNS44NV0sIGNvbXBsZXRpb25SYXRlczogWycwLjUwJScsICcwLjM4JScsICcxLjI1JScsICcwLjk0JSddIH0sDQogICAgICAgIF19LA0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfngrzpkqLkuovkuJrpg6gt5LiA54K86ZKiJywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJ+e7vOWQiOefs+eBsOa2iOiAlycsIHRhcmdldDogNDIuOTUsIGFjdHVhbDogNDIuODksIHVuaXQ6ICdrZy90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFs0Mi45NCwgNDIuODIsIDQyLjg5LCA0MC41NV0sIGNvbXBsZXRpb25SYXRlczogWycwLjAyJScsICcwLjMwJScsICcwLjE0JScsICc1LjU5JSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn5rCn5rCU5raI6ICXJywgdGFyZ2V0OiA0NC41NiwgYWN0dWFsOiA0NC40NSwgdW5pdDogJ20zL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzQ0LjUwLCA0NC4zNCwgNDQuNDUsIDQ0LjM3XSwgY29tcGxldGlvblJhdGVzOiBbJzAuMTMlJywgJzAuNDklJywgJzAuMjUlJywgJzAuNDMlJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfnlLXngonlt6Xluo/og73ogJcnLCB0YXJnZXQ6IDU3LjUwLCBhY3R1YWw6IDU3LjMyLCB1bml0OiAna2dDZS90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFs1Ny4zNCwgNTcuMzMsIDU3LjMyLCA1Ny4xOV0sIGNvbXBsZXRpb25SYXRlczogWycwLjI4JScsICcwLjMwJScsICcwLjMxJScsICcwLjU0JSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn6ZKi6ZOB5paZ5pS25b6X546HJywgdGFyZ2V0OiA5MS41MCwgYWN0dWFsOiA5MS4zMiwgdW5pdDogJyUnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbOTEuNTEsIDkxLjMyLCA5MS4zMiwgOTEuMzJdLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMSUnLCAnLTAuMjAlJywgJy0wLjIwJScsICctMC4yMCUnXSB9DQogICAgICAgIF19LA0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfngrzpkqLkuovkuJrpg6gt5LqM54K86ZKiJywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJ+e7vOWQiOefs+eBsOa2iOiAlycsIHRhcmdldDogNTAuMDAsIGFjdHVhbDogNDguOTUsIHVuaXQ6ICdrZy90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFs0OS45MCwgNDkuNDAsIDQ4Ljk1LCA0OS4wMF0sIGNvbXBsZXRpb25SYXRlczogWycwLjIwJScsICcxLjA4JScsICcyLjEwJScsICcyLjAwJSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn5rCn5rCU5raI6ICXJywgdGFyZ2V0OiA0NS42NSwgYWN0dWFsOiA0NS4yNiwgdW5pdDogJ20zL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzQ1LjQ5LCA0NS4zNywgNDUuMjYsIDQ1LjM1XSwgY29tcGxldGlvblJhdGVzOiBbJzAuMzUlJywgJzAuMzklJywgJzAuODUlJywgJzAuNjYlJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfovazngonlt6Xluo/og73ogJcnLCB0YXJnZXQ6IC0yOC41MCwgYWN0dWFsOiAtMjkuNTIsIHVuaXQ6ICdrZ0NlL3QnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbMjkuNzgsIDI5LjU3LCAyOS41MiwgMjkuNjFdLCBjb21wbGV0aW9uUmF0ZXM6IFsnLTAuOTglJywgJy0zLjc1JScsICctMy41OCUnLCAnLTMuODklJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfpkqLpk4HmlpnmlLblvpfnjocnLCB0YXJnZXQ6IDkxLjUwLCBhY3R1YWw6IDkxLjE2LCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5MS41MCwgOTEuMzIsIDkxLjE2LCA5MS4xNV0sIGNvbXBsZXRpb25SYXRlczogWycwLjAwJScsICctMC4yMCUnLCAnLTAuMzclJywgJy0wLjM4JSddIH0NCiAgICAgICAgXX0sDQogICAgICAgIHsgZGVwYXJ0bWVudDogJ+i9p+mSouS6i+S4mumDqC3nu7zlkIjliKnnlKgnLCBpbmRpY2F0b3JzOiBbDQogICAgICAgICAgeyBuYW1lOiAn5bqf6YeR6YeR5bGe6YePJywgdGFyZ2V0OiAwLjY1LCBhY3R1YWw6IDAuNjQsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFswLjY1LCAwLjUzLCAwLjY0LCAwLjQ5XSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzE4LjQwJScsICcxLjU0JScsICctMjYuMTUlJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfnlLXogJcnLCB0YXJnZXQ6IDIyLjMzLCBhY3R1YWw6IDIzLjgxLCB1bml0OiAna1doL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzIyLjMzLCAyMy4wNywgMjMuODEsIDIxLjE5XSwgY29tcGxldGlvblJhdGVzOiBbJzAuNDUlJywgJy0zLjMxJScsICctNi42MiUnLCAnNS4xMSUnXSB9DQogICAgICAgIF19LA0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfovafpkqLkuovkuJrpg6gt5LiA6L2n6ZKiJywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJ+eDrei9p+e7vOWQiOaIkOadkOeOhycsIHRhcmdldDogOTYuMzUsIGFjdHVhbDogOTYuMjIsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IHRydWUsIHZhbHVlczogWzk2LjIzLCA5Ni4yNSwgOTYuMjIsIDk2LjI0XSwgY29tcGxldGlvblJhdGVzOiBbJy0wLjEyJScsICctMC4xMCUnLCAnLTAuMTMlJywgJy0wLjExJSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXJywgdGFyZ2V0OiA1OC41NSwgYWN0dWFsOiA1OC41MSwgdW5pdDogJ2tnQ2UvdCcsIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwgdmFsdWVzOiBbNTguNDcsIDU4LjUyLCA1OC41MSwgNTguNDVdLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4xNCUnLCAnMC4wNSUnLCAnMC4wNyUnLCAnMC4xNyUnXSB9DQogICAgICAgIF19LA0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfovafpkqLkuovkuJrpg6gt5LqM6L2n6ZKiJywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJ+eDrei9p+e7vOWQiOaIkOadkOeOhyjlpKfmo5IpJywgdGFyZ2V0OiA5NS4zNywgYWN0dWFsOiA5NS4zNywgdW5pdDogJyUnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbOTUuMzcsIDk1LjIyLCA5NS4zNywgOTUuMzddLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnLTAuMTUlJywgJzAuMDAlJywgJzAuMDAlJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfng63ovafnu7zlkIjmiJDmnZDnjoco5bCP5qOSKScsIHRhcmdldDogOTYuNTYsIGFjdHVhbDogOTYuNTYsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IHRydWUsIHZhbHVlczogWzk2LjM5LCA5Ni41NiwgOTYuNTYsIDk2LjU2XSwgY29tcGxldGlvblJhdGVzOiBbJy0wLjE3JScsICcwLjAwJScsICcwLjAwJScsICcwLjAwJSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXKOWkp+ajkiknLCB0YXJnZXQ6IDY3LjI4LCBhY3R1YWw6IDcyLjQ5LCB1bml0OiAna2dDZS90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFs3MS4zNSwgNzMuODAsIDcyLjQ5LCA2Ni4yNF0sIGNvbXBsZXRpb25SYXRlczogWyctMC45MyUnLCAnMC43NSUnLCAnMC4wMCUnLCAnLTAuMjUlJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfng63ovafpkqLmnZDlt6Xluo/og73ogJco5bCP5qOSKScsIHRhcmdldDogNDIuMDUsIGFjdHVhbDogNDIuMDIsIHVuaXQ6ICdrZ0NlL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzQyLjAzLCA0Mi4wNSwgNDIuMDIsIDQ1LjY4XSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDUlJywgJzAuMDUlJywgJzAuMDUlJywgJzAuMzQlJ10gfQ0KICAgICAgICBdfSwNCiAgICAgICAgeyBkZXBhcnRtZW50OiAn6L2n6ZKi5LqL5Lia6YOoLeS4iei9p+mSoicsIGluZGljYXRvcnM6IFsNCiAgICAgICAgICB7IG5hbWU6ICfng63ovafnu7zlkIjmiJDmnZDnjocnLCB0YXJnZXQ6IDk2LjA0LCBhY3R1YWw6IDk1LjUwLCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5NS43NiwgOTYuMzAsIDk1LjUwLCA5NS41MV0sIGNvbXBsZXRpb25SYXRlczogWyctMC4yOCUnLCAnMC40NCUnLCAnLTAuNTAlJywgJy0wLjUwJSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXJywgdGFyZ2V0OiA1Ni4zMSwgYWN0dWFsOiA1NC42NywgdW5pdDogJ2tnQ2UvdCcsIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwgdmFsdWVzOiBbNTUuMjYsIDU2LjM0LCA1NC42NywgNTUuMTldLCBjb21wbGV0aW9uUmF0ZXM6IFsnLTAuNzklJywgJzAuNzElJywgJy0xLjMzJScsICctMS4xOCUnXSB9DQogICAgICAgIF19LA0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfovafpkqLkuovkuJrpg6gt54m55q6K6ZKi6L2n5p2QJywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJ+eDrei9p+mSouadkOW3peW6j+iDveiAlycsIHRhcmdldDogNjcuMDQsIGFjdHVhbDogNjguNjQsIHVuaXQ6ICdrZ0NlL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzY3LjM1LCA2NC4wOSwgNjguNjQsIDY0Ljc3XSwgY29tcGxldGlvblJhdGVzOiBbJzAuNDYlJywgJy0zLjI2JScsICcwLjAwJScsICctMC4yMyUnXSB9LA0KICAgICAgICAgIHsgbmFtZTogJ+e7vOWQiOaIkOadkOeOhycsIHRhcmdldDogOTYuNzMsIGFjdHVhbDogOTYuNzMsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IHRydWUsIHZhbHVlczogWzk2LjczLCA5Ni43OSwgOTYuNzMsIDk2LjQ1XSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzAuMDYlJywgJzAuMDAlJywgJy0wLjI4JSddIH0NCiAgICAgICAgXX0sDQogICAgICAgIHsgZGVwYXJ0bWVudDogJ+i9p+mSouS6i+S4mumDqC3mo5LmnZDovafliLbljoInLCBpbmRpY2F0b3JzOiBbDQogICAgICAgICAgeyBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXKOajkuaJgSknLCB0YXJnZXQ6IDU2LjkzLCBhY3R1YWw6IDYxLjgxLCB1bml0OiAna2dDZS90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFs2Ni4xNCwgNjAuMDAsIDYxLjgxLCA1OS45Nl0sIGNvbXBsZXRpb25SYXRlczogWyctMC45MSUnLCAnLTEuMzUlJywgJzAuMDAlJywgJy0wLjI0JSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXKOWkp+ebmCknLCB0YXJnZXQ6IDU3LjA4LCBhY3R1YWw6IDYxLjI4LCB1bml0OiAna2dDZS90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFs2NC4zMCwgNjAuMjksIDYxLjI4LCA2MC4wMl0sIGNvbXBsZXRpb25SYXRlczogWyctMC4xOSUnLCAnLTAuMTUlJywgJzAuMDAlJywgJy0wLjI2JSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn57u85ZCI5oiQ5p2Q546HKOajkuaJgei9p+adkCknLCB0YXJnZXQ6IDk2LjQ1LCBhY3R1YWw6IDk2LjEyLCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5Ni4xNCwgOTYuMTEsIDk2LjEyLCA5Ni4wM10sIGNvbXBsZXRpb25SYXRlczogWyctMC4zMSUnLCAnLTAuMzMlJywgJzAuMDAlJywgJy0wLjI5JSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn57u85ZCI5oiQ5p2Q546HKOWkp+ebmOWNtyknLCB0YXJnZXQ6IDk1Ljg1LCBhY3R1YWw6IDk1Ljg0LCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5NS44NiwgOTUuOTAsIDk1Ljg0LCA5NS44N10sIGNvbXBsZXRpb25SYXRlczogWycwLjAxJScsICcwLjA0JScsICcwLjAwJScsICcwLjAyJSddIH0NCiAgICAgICAgXX0sDQogICAgICAgIHsgZGVwYXJ0bWVudDogJ+i9p+mSouS6i+S4mumDqC3mo5Lnur/mnZDmt7HliqDlt6Uo5qOSKScsIGluZGljYXRvcnM6IFsNCiAgICAgICAgICB7IG5hbWU6ICfnu7zlkIjmiJDmnZDnjocnLCB0YXJnZXQ6IDkyLjYwLCBhY3R1YWw6IDkyLjYwLCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5Mi42MCwgOTIuNjAsIDkyLjYwLCA5Mi42MF0sIGNvbXBsZXRpb25SYXRlczogWycwLjAwJScsICcwLjAwJScsICcwLjAwJScsICcwLjAwJSddIH0NCiAgICAgICAgXX0sDQogICAgICAgIHsgZGVwYXJ0bWVudDogJ+i9p+mSouS6i+S4mumDqC3mo5Lnur/mnZDmt7HliqDlt6Uo57q/KScsIGluZGljYXRvcnM6IFsNCiAgICAgICAgICB7IG5hbWU6ICfmjqfnur/mnZDnu7zlkIjmiJDmnZDnjocnLCB0YXJnZXQ6IDk4LjU1LCBhY3R1YWw6IDk4LjU2LCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5OC41NSwgOTguNTUsIDk4LjU2LCA5OC41NV0sIGNvbXBsZXRpb25SYXRlczogWycwLjAwJScsICcwLjAwJScsICcwLjAwJScsICcwLjAwJSddIH0NCiAgICAgICAgXX0sDQogICAgICAgIHsgZGVwYXJ0bWVudDogJ+i9p+mSouS6i+S4mumDqC3nur/mnZDmt7HliqDlt6UnLCBpbmRpY2F0b3JzOiBbDQogICAgICAgICAgeyBuYW1lOiAn5Ya36ZWm5p2Q57u85ZCI5oiQ5p2Q546HJywgdGFyZ2V0OiA5Ni4zNiwgYWN0dWFsOiA5Ni4wMiwgdW5pdDogJyUnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbOTYuMzYsIDk2LjM2LCA5Ni4wMiwgOTQuNDRdLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnLCAnLTEuOTQlJ10gfQ0KICAgICAgICBdfSwNCiAgICAgICAgeyBkZXBhcnRtZW50OiAn6ams56eR5omY6ZKi55CDLemprOenkeaJmOmSoueQgycsIGluZGljYXRvcnM6IFsNCiAgICAgICAgICB7IG5hbWU6ICfnu7zlkIjmiJDmnZDnjocnLCB0YXJnZXQ6IDkzLjE5LCBhY3R1YWw6IDkzLjYxLCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5My4xMywgOTMuNTQsIDkzLjYxLCA5My44MF0sIGNvbXBsZXRpb25SYXRlczogWyctMC4wNiUnLCAnMC40MiUnLCAnMC4wMCUnLCAnMC4yMCUnXSB9LA0KICAgICAgICAgIHsgbmFtZTogJ+awp+awlOa2iOiAlycsIHRhcmdldDogNDQuNjQsIGFjdHVhbDogNDQuNjMsIHVuaXQ6ICdtMy90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFs0NC42NCwgNDQuNjQsIDQ0LjYzLCA0NC42NF0sIGNvbXBsZXRpb25SYXRlczogWycwLjAwJScsICcwLjAwJScsICcwLjAwJScsICcwLjAwJSddIH0NCiAgICAgICAgXX0sDQogICAgICAgIHsgZGVwYXJ0bWVudDogJ+eJueadv+S6i+S4mumDqC3nibnpkqLngrzpkqLliIbljoInLCBpbmRpY2F0b3JzOiBbDQogICAgICAgICAgeyBuYW1lOiAn5rCn5rCU5raI6ICXJywgdGFyZ2V0OiA0NC42NCwgYWN0dWFsOiA0NC42MywgdW5pdDogJ20zL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzQ0LjY0LCA0NC42NCwgNDQuNjMsIDQ0LjY0XSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfovazngonlt6Xluo/og73ogJcnLCB0YXJnZXQ6IC0yOC41MCwgYWN0dWFsOiAtMjkuOTEsIHVuaXQ6ICdrZ0NlL3QnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbMjguOTMsIDI5LjY3LCAyOS45MSwgMjkuNTVdLCBjb21wbGV0aW9uUmF0ZXM6IFsnLTAuOTglJywgJy0zLjc1JScsICctMy41OCUnLCAnLTMuODklJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfpkqLpk4HmlpnmlLblvpfnjocnLCB0YXJnZXQ6IDkxLjYwLCBhY3R1YWw6IDkxLjI3LCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5MS44MCwgOTEuMzEsIDkxLjI3LCA5MS4yN10sIGNvbXBsZXRpb25SYXRlczogWycwLjIyJScsICctMC4zMyUnLCAnLTAuMzclJywgJy0wLjM3JSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn57u85ZCI55+z54Gw5raI6ICXJywgdGFyZ2V0OiA0My42NywgYWN0dWFsOiA0Ni4wNywgdW5pdDogJ2tnL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzQ2LjAxLCA0Ny4xOSwgNDYuMDcsIDQzLjk3XSwgY29tcGxldGlvblJhdGVzOiBbJzIuODQlJywgJzMuMjclJywgJzIuMTQlJywgJzIuMDclJ10gfQ0KICAgICAgICBdfSwNCiAgICAgICAgeyBkZXBhcnRtZW50OiAn54m55p2/5LqL5Lia6YOoLeS4readv+WIhuWOgicsIGluZGljYXRvcnM6IFsNCiAgICAgICAgICB7IG5hbWU6ICfnu7zlkIjlkb3kuK3njocnLCB0YXJnZXQ6IDk4LjYyLCBhY3R1YWw6IDk4LjYzLCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5OC42MywgOTguNjUsIDk4LjYzLCA5OC42M10sIGNvbXBsZXRpb25SYXRlczogWycwLjAwJScsICcwLjAyJScsICcwLjAwJScsICcwLjAwJSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn57u85ZCI5oiQ5p2Q546HJywgdGFyZ2V0OiA5Mi41NSwgYWN0dWFsOiA5Mi4wNCwgdW5pdDogJyUnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbOTIuNjMsIDkyLjAzLCA5Mi4wNCwgOTIuNjVdLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wOSUnLCAnLTAuNTElJywgJy0wLjUxJScsICcwLjAyJSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn5pW05a6i5oi35Lqk5LuY546HJywgdGFyZ2V0OiA5OC43NSwgYWN0dWFsOiA5OC43OCwgdW5pdDogJyUnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbOTguNzUsIDk4Ljc3LCA5OC43OCwgOTguNzVdLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wMiUnLCAnMC4wMCUnLCAnMC4wMCUnXSB9LA0KICAgICAgICAgIHsgbmFtZTogJ+eDrei9p+W3peW6j+iDveiAlycsIHRhcmdldDogNDUuMDIsIGFjdHVhbDogNDMuMzIsIHVuaXQ6ICdrZ0NlL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzQ0LjYwLCA0NC4xNSwgNDMuMzIsIDQzLjgwXSwgY29tcGxldGlvblJhdGVzOiBbJy0wLjkzJScsICctMS4yNSUnLCAnLTEuNjglJywgJy0wLjcwJSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn54Ot6KOF5q+UJywgdGFyZ2V0OiA3NS4wMCwgYWN0dWFsOiA3NS44NSwgdW5pdDogJyUnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbNzUuNDAsIDc3LjY0LCA3NS44NSwgNzQuMDVdLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC41MyUnLCAnMi4xOCUnLCAnMC4wMCUnLCAnLTEuOTUlJ10gfQ0KICAgICAgICBdfSwNCiAgICAgICAgeyBkZXBhcnRtZW50OiAn54m55p2/5LqL5Lia6YOoLeWOmuadv+WIhuWOgicsIGluZGljYXRvcnM6IFsNCiAgICAgICAgICB7IG5hbWU6ICfnu7zlkIjlkb3kuK3njocnLCB0YXJnZXQ6IDk3LjQ5LCBhY3R1YWw6IDk3LjI3LCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5Ny40OSwgOTcuNTMsIDk3LjI3LCA5Ny41Ml0sIGNvbXBsZXRpb25SYXRlczogWycwLjAwJScsICcwLjA0JScsICctMC4yNiUnLCAnMC4wNSUnXSB9LA0KICAgICAgICAgIHsgbmFtZTogJ+e7vOWQiOaIkOadkOeOhycsIHRhcmdldDogOTAuOTEsIGFjdHVhbDogOTAuNzYsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IHRydWUsIHZhbHVlczogWzkwLjQxLCA5MC43OSwgOTAuNzYsIDkwLjc4XSwgY29tcGxldGlvblJhdGVzOiBbJy0wLjU1JScsICcwLjMyJScsICctMC4yNiUnLCAnMC4wMiUnXSB9LA0KICAgICAgICAgIHsgbmFtZTogJ+aVtOWuouaIt+S6pOS7mOeOhycsIHRhcmdldDogOTYuMzQsIGFjdHVhbDogOTYuMzQsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IHRydWUsIHZhbHVlczogWzk2LjM3LCA5Ni4zNSwgOTYuMzQsIDk2LjMxXSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDMlJywgJy0wLjAyJScsICcwLjAwJScsICctMC4wMyUnXSB9LA0KICAgICAgICAgIHsgbmFtZTogJ+eDrei9p+W3peW6j+iDveiAlycsIHRhcmdldDogNDguNjIsIGFjdHVhbDogNDUuODUsIHVuaXQ6ICdrZ0NlL3QnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzQ2LjI3LCA0Ni4wMSwgNDUuODUsIDQ3LjExXSwgY29tcGxldGlvblJhdGVzOiBbJy0yLjc5JScsICctMi41NiUnLCAnLTIuMzQlJywgJy0xLjU0JSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn54Ot6KOF5q+UKDIwMOKEgyknLCB0YXJnZXQ6IDUwLjAwLCBhY3R1YWw6IDMxLjIzLCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs1MC42MCwgNTEuMjgsIDMxLjIzLCA1MC4yOF0sIGNvbXBsZXRpb25SYXRlczogWycxLjIwJScsICcyLjU2JScsICcwLjAwJScsICctMS41NiUnXSB9DQogICAgICAgIF19LA0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfnibnmnb/kuovkuJrpg6gt6ZKi5p2Q5rex5Yqg5belJywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJ+aVtOWuouaIt+S6pOS7mOeOhycsIHRhcmdldDogOTkuMTEsIGFjdHVhbDogOTkuMTIsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IHRydWUsIHZhbHVlczogWzk5LjExLCA5OS4xMCwgOTkuMTIsIDk5LjEyXSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJy0wLjAxJScsICcwLjAwJScsICcwLjAwJSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn57u85ZCI5ZG95Lit546HJywgdGFyZ2V0OiA5OS43MywgYWN0dWFsOiA5OS43NSwgdW5pdDogJyUnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbOTkuNzMsIDk5Ljc0LCA5OS43NSwgOTkuNzRdLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnLTAuMDElJywgJzAuMDAlJywgJzAuMDAlJ10gfQ0KICAgICAgICBdfSwNCiAgICAgICAgeyBkZXBhcnRtZW50OiAn5Yqo5Yqb5LqL5Lia6YOoLeeDreeUtScsIGluZGljYXRvcnM6IFsNCiAgICAgICAgICB7IG5hbWU6ICfmoIfnhaTkuqfmsb3njocnLCB0YXJnZXQ6IDEwLjg1LCBhY3R1YWw6IDEwLjkwLCB1bml0OiAndC90Q2UnLCBpc0hpZ2hlckJldHRlcjogdHJ1ZSwgdmFsdWVzOiBbMTAuODcsIDEwLjg5LCAxMC45MCwgMTAuOTJdLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4xOSUnLCAnMC4yMCUnLCAnMC4wMCUnLCAnMC4wMCUnXSB9DQogICAgICAgIF19LA0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfliqjlipvkuovkuJrpg6gt5L6b55S15bel5Yy6JywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJ+S+m+eUteWKn+eOh+WboOaVsCcsIHRhcmdldDogOTUuMDAsIGFjdHVhbDogOTguMDAsIHVuaXQ6ICclKDk1LjMtMTAwKScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5OC42NiwgOTguNjYsIDk4LjAwLCA5OC4wMF0sIGNvbXBsZXRpb25SYXRlczogWyczLjIwJScsICczLjIwJScsICcwLjAwJScsICcwLjAwJSddIH0NCiAgICAgICAgXX0sDQogICAgICAgIHsgZGVwYXJ0bWVudDogJ+WKqOWKm+S6i+S4mumDqC3msLTlpITnkIbliIbljoInLCBpbmRpY2F0b3JzOiBbDQogICAgICAgICAgeyBuYW1lOiAn5ZCo6ZKi6L2v5rC05rC0JywgdGFyZ2V0OiAxLjYyLCBhY3R1YWw6IDEuNjMsIHVuaXQ6ICdtMy90JywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFsxLjYyLCAxLjYxLCAxLjYzLCAxLjY5XSwgY29tcGxldGlvblJhdGVzOiBbJzAuNjIlJywgJy0wLjYyJScsICcwLjAwJScsICcwLjYyJSddIH0sDQogICAgICAgICAgeyBuYW1lOiAn5ZCo6ZKi54Ot5rC05aSE55CG6YePJywgdGFyZ2V0OiAyMS4yMCwgYWN0dWFsOiAyMC4yNSwgdW5pdDogJ3QvdOmSoicsIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwgdmFsdWVzOiBbMTkuODksIDIwLjE0LCAyMC4yNSwgMjAuMjhdLCBjb21wbGV0aW9uUmF0ZXM6IFsnLTYuMTklJywgJy0zLjU3JScsICcwLjAwJScsICcxLjQwJSddIH0NCiAgICAgICAgXX0sDQogICAgICAgIHsgZGVwYXJ0bWVudDogJ+WKqOWKm+S6i+S4mumDqC3liLbmsKfliIbljoInLCBpbmRpY2F0b3JzOiBbDQogICAgICAgICAgeyBuYW1lOiAn5rCn5rCU5bCx6ICX546HJywgdGFyZ2V0OiAwLjAyLCBhY3R1YWw6IDAuMDAsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFswLjAwLCAwLjAwLCAwLjAwLCAwLjAwXSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJ10gfQ0KICAgICAgICBdfSwNCiAgICAgICAgeyBkZXBhcnRtZW50OiAn5Yqo5Yqb5LqL5Lia6YOoLeeFpOawlOWIhuWOgicsIGluZGljYXRvcnM6IFsNCiAgICAgICAgICB7IG5hbWU6ICfpq5jngonnhaTmsJTlsLHogJfnjocnLCB0YXJnZXQ6IDAuMDIsIGFjdHVhbDogMC4wMCwgdW5pdDogJyUnLCBpc0hpZ2hlckJldHRlcjogZmFsc2UsIHZhbHVlczogWzAuMDAsIDAuMDAsIDAuMDAsIDAuMDBdLCBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnXSB9DQogICAgICAgIF19LA0KICAgICAgICB7IGRlcGFydG1lbnQ6ICfnianmtYHkuovkuJrpg6gt5YKo6L+Q5YWs5Y+4JywgaW5kaWNhdG9yczogWw0KICAgICAgICAgIHsgbmFtZTogJzM2MOa3t+WMgOefv+awtOWIhuWQiOagvOeOhycsIHRhcmdldDogOTAuNTYsIGFjdHVhbDogOTEuMzIsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IHRydWUsIHZhbHVlczogWzkwLjYzLCA5MC42OCwgOTEuMzIsIDkwLjY4XSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDclJywgJzAuMDUlJywgJzAuMDAlJywgJzAuMDAlJ10gfSwNCiAgICAgICAgICB7IG5hbWU6ICfmt7fljIDnn7/nqLPlrprnjocnLCB0YXJnZXQ6IDk3LjgyLCBhY3R1YWw6IDk3Ljg5LCB1bml0OiAnJScsIGlzSGlnaGVyQmV0dGVyOiB0cnVlLCB2YWx1ZXM6IFs5Ny44MywgOTguMDEsIDk3Ljg5LCA5OC4wMV0sIGNvbXBsZXRpb25SYXRlczogWycwLjE4JScsICcwLjE3JScsICcwLjAwJScsICcwLjAwJSddIH0NCiAgICAgICAgXX0sDQogICAgICAgIHsgZGVwYXJ0bWVudDogJ+ajgOS/ruS6i+S4mumDqC3mo4Dkv67liIbljoInLCBpbmRpY2F0b3JzOiBbDQogICAgICAgICAgeyBuYW1lOiAn54Ot5L+u5py6546HJywgdGFyZ2V0OiAwLjEwLCBhY3R1YWw6IDAuMDAsIHVuaXQ6ICclJywgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLCB2YWx1ZXM6IFswLjAwLCAwLjAwLCAwLjAwLCAwLjAwXSwgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJ10gfQ0KICAgICAgICBdfQ0KICAgICAgXSwNCiAgICAgIGNvbXBsZXRpb25EYXRhMTogDQogICAgICBbDQogICAgICB7ZGVwYXJ0bWVudDon6ZKZ5Lia5YiG5Y6CJ30sDQogICAgICB7ZGVwYXJ0bWVudDon55+/5rij5b6u57KJJ30sDQogICAgICB7ZGVwYXJ0bWVudDon54On57uT5YiG5Y6CLTEj54On57uTJ30sDQogICAgICB7ZGVwYXJ0bWVudDon54On57uT5YiG5Y6CLTIj54On57uTJ30sDQogICAgICB7ZGVwYXJ0bWVudDon54K86ZOB5YiG5Y6CLTEj6auY54KJJ30sDQogICAgICB7ZGVwYXJ0bWVudDon54K86ZOB5YiG5Y6CLTIj6auY54KJJ30sDQogICAgICB7ZGVwYXJ0bWVudDon54K86ZOB5YiG5Y6CLTMj6auY54KJJ30sDQogICAgICB7ZGVwYXJ0bWVudDon54K86ZOB5YiG5Y6CLeWwj+WWt+eFpCd9LA0KICAgICAge2RlcGFydG1lbnQ6J+eCvOmTgeWIhuWOgi3lpKfllrfnhaQnfSwNCiAgICAgIHtkZXBhcnRtZW50OifkuIDngrzpkqInfSwNCiAgICAgIHtkZXBhcnRtZW50OifkuozngrzpkqInfSwNCiAgICAgIHtkZXBhcnRtZW50OifkuIDovafpkqInfSwNCiAgICAgIHtkZXBhcnRtZW50Oifkuozovact5aSn5qOSJ30sDQogICAgICB7ZGVwYXJ0bWVudDon5LqM6L2nLeWwj+ajkid9LA0KICAgICAge2RlcGFydG1lbnQ6J+eJueadv+eCvOmSoid9LA0KICAgICAge2RlcGFydG1lbnQ6JzM1MDDkuK3mnb8nfSwNCiAgICAgIHtkZXBhcnRtZW50Oic0MzAw5Y6a5p2/J30sDQogICAgICB7ZGVwYXJ0bWVudDonNDMwMOawtOWkhOeQhid9LA0KICAgICAge2RlcGFydG1lbnQ6J+eDreWkhOeQhid9LA0KICAgICAge2RlcGFydG1lbnQ6J+mrmOe6v+WIhuWOgid9LA0KICAgICAge2RlcGFydG1lbnQ6J+e6v+adkOa3seWKoOW3pSd9LA0KICAgICAge2RlcGFydG1lbnQ6J+ajkuadkOa3seWKoOW3pSd9LA0KICAgICAge2RlcGFydG1lbnQ6J+eDreeUteWIhuWOgi3ng63nlLUnfSwNCiAgICAgIHtkZXBhcnRtZW50Oifng63nlLXliIbljoIt5Lqa5Li055WMJ30sDQogICAgICB7ZGVwYXJ0bWVudDon54Ot55S15YiG5Y6CLeS9meeDrSd9LA0KICAgICAge2RlcGFydG1lbnQ6J+eDreeUteWIhuWOgi3pvJPpo44nfSwNCiAgICAgIHtkZXBhcnRtZW50OifliLbmsKfliIbljoInfSwNCiAgICAgIHtkZXBhcnRtZW50OifliLbmsKfliIbljoIt5LiA5pyfJ30sDQogICAgICB7ZGVwYXJ0bWVudDon5Yi25rCn5YiG5Y6CLeS4ieacnyd9LA0KICAgICAge2RlcGFydG1lbnQ6J+WItuawp+WIhuWOgi3nqbrljovnq5knfSwNCiAgICAgIHtkZXBhcnRtZW50OifmsLTlpITnkIYt5LiA5pyfJ30sDQogICAgICB7ZGVwYXJ0bWVudDon5rC05aSE55CGLeS6jOacnyd9LA0KICAgICAge2RlcGFydG1lbnQ6J+awtOWkhOeQhi3kuInmnJ8nfSwNCiAgICAgIHtkZXBhcnRtZW50OifnhaTmsJTliIbljoInfSwNCiAgICAgIHtkZXBhcnRtZW50OifkvpvnlLXkuIDljLonfSwNCiAgICAgIHtkZXBhcnRtZW50OiflhbTmvoTpkqLnkIMnfSwNCiAgICAgIHtkZXBhcnRtZW50OiflhbTmvoTmuK/liqEnfSwNCiAgICAgIHtkZXBhcnRtZW50Oiflgqjov5Dlhazlj7gnfSwNCiAgICAgIHtkZXBhcnRtZW50Oifnu7zlkIjliKnnlKgnfSwNCiAgICAgIHtkZXBhcnRtZW50OiflkIjph5HngonliIbljoInfSwNCiAgICAgIHtkZXBhcnRtZW50OifniannrqHpg6gnfSwNCiAgICAgIHtkZXBhcnRtZW50OiflkI7li6Tpg6gnfSwNCiAgICAgIHtkZXBhcnRtZW50Oiflhbbku5YnfSwNCiAgICAgIHtkZXBhcnRtZW50OifmjZ/ogJcnfSwNCiAgICAgIHtkZXBhcnRtZW50OiflkIjorqEnfSwNCg0KICAgICAgXSwNCiAgICAgIC8vIOacquWujOaIkOaMh+agh+aVsOaNrg0KICAgICAgaW5jb21wbGV0ZURhdGE6IFtdLA0KICAgICAgDQogICAgICAvLyDog73mupDmjIfmoIfnu5/orqHmlbDmja4NCiAgICAgIGVuZXJneVN0YXRzOiBbDQogICAgICAgIHsgdGl0bGU6ICfnu7zlkIjog73ogJcnLCB2YWx1ZTogJzUuMjHlkKjmoIfnhaQv5ZCo6ZKiJywgY2hhbmdlOiAtMi4zLCBzdGF0dXM6ICdnb29kJyB9LA0KICAgICAgICB7IHRpdGxlOiAn5rC06LWE5rqQ5raI6ICXJywgdmFsdWU6ICczLjjnq4vmlrnnsbMv5ZCo6ZKiJywgY2hhbmdlOiAtMS41LCBzdGF0dXM6ICdnb29kJyB9LA0KICAgICAgICB7IHRpdGxlOiAn55S15Yqb5raI6ICXJywgdmFsdWU6ICc0ODXljYPnk6bml7Yv5ZCo6ZKiJywgY2hhbmdlOiAwLjgsIHN0YXR1czogJ3dhcm5pbmcnIH0sDQogICAgICAgIHsgdGl0bGU6ICfnhaTmsJTlm57mlLbnjocnLCB2YWx1ZTogJzk4LjUlJywgY2hhbmdlOiAxLjIsIHN0YXR1czogJ2dvb2QnIH0sDQogICAgICAgIHsgdGl0bGU6ICfkvZnng63lm57mlLbnjocnLCB2YWx1ZTogJzc1LjIlJywgY2hhbmdlOiAyLjUsIHN0YXR1czogJ2dvb2QnIH0sDQogICAgICAgIHsgdGl0bGU6ICfkuozmsKfljJbnorPmjpLmlL4nLCB2YWx1ZTogJzEuODXlkKgv5ZCo6ZKiJywgY2hhbmdlOiAtMy4yLCBzdGF0dXM6ICdnb29kJyB9DQogICAgICBdLA0KICAgICAgDQogICAgICAvLyDog73mupDmtojogJfmlbDmja4NCiAgICAgIGVuZXJneUNvbnN1bXB0aW9uRGF0YTogWw0KICAgICAgICB7IG5hbWU6ICfnlLXlipsnLCB2YWx1ZTogMzUgfSwNCiAgICAgICAgeyBuYW1lOiAn54Wk54KtJywgdmFsdWU6IDI1IH0sDQogICAgICAgIHsgbmFtZTogJ+WkqeeEtuawlCcsIHZhbHVlOiAxNSB9LA0KICAgICAgICB7IG5hbWU6ICfokrjmsb0nLCB2YWx1ZTogMTAgfSwNCiAgICAgICAgeyBuYW1lOiAn5YW25LuWJywgdmFsdWU6IDE1IH0NCiAgICAgIF0sDQogICAgICANCiAgICAgIC8vIOmDqOmXqOiDvea6kOa2iOiAl+aVsOaNru+8iOaWsOWinu+8iS0g5oyJ5pyI57uf6K6hDQogICAgICBkZXBhcnRtZW50RW5lcmd5RGF0YTogew0KICAgICAgICBtb250aHM6IFsnMeaciCcsICcy5pyIJywgJzPmnIgnLCAnNOaciCddLA0KICAgICAgICBkZXBhcnRtZW50czogWyfngrzpk4HkuovkuJrpg6gnLCAn54K86ZKi5LqL5Lia6YOoJywgJ+i9p+mSouS6i+S4mumDqCcsICfpqaznp5HmiZjpkqLnkIMnLCAn54m55p2/5LqL5Lia6YOoJywgJ+WKqOWKm+S6i+S4mumDqCcsICfnianmtYHkuovkuJrpg6gnLCAn5qOA5L+u5LqL5Lia6YOoJ10sDQogICAgICAgIGVsZWN0cmljaXR5OiB7IC8vIOeUteWKm+a2iOiAlyAo5LiH5Y2D55Om5pe2KQ0KICAgICAgICAgICfngrzpk4HkuovkuJrpg6gnOiBbMTI1MCwgMTE4MCwgMTIyMCwgMTI2MF0sDQogICAgICAgICAgJ+eCvOmSouS6i+S4mumDqCc6IFsxODUwLCAxNzkwLCAxODEwLCAxODgwXSwNCiAgICAgICAgICAn6L2n6ZKi5LqL5Lia6YOoJzogWzE0NTAsIDE0MjAsIDE0ODAsIDE0NDBdLA0KICAgICAgICAgICfpqaznp5HmiZjpkqLnkIMnOiBbNDIwLCA0MTAsIDQzMCwgNDI1XSwNCiAgICAgICAgICAn54m55p2/5LqL5Lia6YOoJzogWzk4MCwgOTUwLCA5NzAsIDk5MF0sDQogICAgICAgICAgJ+WKqOWKm+S6i+S4mumDqCc6IFszMjAsIDMxMCwgMzMwLCAzMjVdLA0KICAgICAgICAgICfnianmtYHkuovkuJrpg6gnOiBbMTgwLCAxNzUsIDE4NSwgMTgyXSwNCiAgICAgICAgICAn5qOA5L+u5LqL5Lia6YOoJzogWzE1MCwgMTQ1LCAxNTUsIDE1Ml0NCiAgICAgICAgfSwNCiAgICAgICAgd2F0ZXI6IHsgLy8g5rC06LWE5rqQ5raI6ICXICjkuIflkKgpDQogICAgICAgICAgJ+eCvOmTgeS6i+S4mumDqCc6IFs4NSwgODIsIDg2LCA4OF0sDQogICAgICAgICAgJ+eCvOmSouS6i+S4mumDqCc6IFsxMjAsIDExNSwgMTE4LCAxMjJdLA0KICAgICAgICAgICfovafpkqLkuovkuJrpg6gnOiBbOTUsIDkyLCA5NiwgOTRdLA0KICAgICAgICAgICfpqaznp5HmiZjpkqLnkIMnOiBbMjgsIDI3LCAyOSwgMjguNV0sDQogICAgICAgICAgJ+eJueadv+S6i+S4mumDqCc6IFs2NSwgNjMsIDY2LCA2N10sDQogICAgICAgICAgJ+WKqOWKm+S6i+S4mumDqCc6IFsxODAsIDE3NSwgMTg1LCAxODJdLA0KICAgICAgICAgICfnianmtYHkuovkuJrpg6gnOiBbMTUsIDE0LCAxNiwgMTUuNV0sDQogICAgICAgICAgJ+ajgOS/ruS6i+S4mumDqCc6IFsxMiwgMTEuNSwgMTIuNSwgMTIuMl0NCiAgICAgICAgfSwNCiAgICAgICAgZ2FzOiB7IC8vIOWkqeeEtuawlOa2iOiAlyAo5LiH56uL5pa557GzKQ0KICAgICAgICAgICfngrzpk4HkuovkuJrpg6gnOiBbMzIwLCAzMTAsIDMyNSwgMzMwXSwNCiAgICAgICAgICAn54K86ZKi5LqL5Lia6YOoJzogWzQ4MCwgNDcwLCA0ODUsIDQ5MF0sDQogICAgICAgICAgJ+i9p+mSouS6i+S4mumDqCc6IFszODAsIDM3MCwgMzg1LCAzNzVdLA0KICAgICAgICAgICfpqaznp5HmiZjpkqLnkIMnOiBbMTEwLCAxMDUsIDExMiwgMTA4XSwNCiAgICAgICAgICAn54m55p2/5LqL5Lia6YOoJzogWzI1MCwgMjQ1LCAyNTUsIDI2MF0sDQogICAgICAgICAgJ+WKqOWKm+S6i+S4mumDqCc6IFs4NSwgODIsIDg3LCA4Nl0sDQogICAgICAgICAgJ+eJqea1geS6i+S4mumDqCc6IFs0NSwgNDMsIDQ2LCA0NF0sDQogICAgICAgICAgJ+ajgOS/ruS6i+S4mumDqCc6IFszNSwgMzQsIDM2LCAzNS41XQ0KICAgICAgICB9LA0KICAgICAgICBzdGVhbTogeyAvLyDokrjmsb3mtojogJcgKOS4h+WQqCkNCiAgICAgICAgICAn54K86ZOB5LqL5Lia6YOoJzogWzQ1LCA0MywgNDYsIDQ3XSwNCiAgICAgICAgICAn54K86ZKi5LqL5Lia6YOoJzogWzY1LCA2MywgNjYsIDY3XSwNCiAgICAgICAgICAn6L2n6ZKi5LqL5Lia6YOoJzogWzUyLCA1MCwgNTMsIDUxXSwNCiAgICAgICAgICAn6ams56eR5omY6ZKi55CDJzogWzE1LCAxNC41LCAxNS41LCAxNS4yXSwNCiAgICAgICAgICAn54m55p2/5LqL5Lia6YOoJzogWzM1LCAzNCwgMzYsIDM3XSwNCiAgICAgICAgICAn5Yqo5Yqb5LqL5Lia6YOoJzogWzEyLCAxMS41LCAxMi41LCAxMi4yXSwNCiAgICAgICAgICAn54mp5rWB5LqL5Lia6YOoJzogWzgsIDcuOCwgOC4yLCA4LjFdLA0KICAgICAgICAgICfmo4Dkv67kuovkuJrpg6gnOiBbNiwgNS44LCA2LjIsIDYuMV0NCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIA0KICAgICAgLy8g6IO95rqQ57G75Z6L6YCJ6aG5DQogICAgICBlbmVyZ3lUeXBlczogWw0KICAgICAgICB7IGxhYmVsOiAn55S15Yqb5raI6ICXJywgdmFsdWU6ICdlbGVjdHJpY2l0eScsIHVuaXQ6ICfljYPnk6bml7YnLCBjb2xvcjogJyMyZjgwZWQnIH0sDQogICAgICAgIHsgbGFiZWw6ICfmsLTotYTmupDmtojogJcnLCB2YWx1ZTogJ3dhdGVyJywgdW5pdDogJ+WQqCcsIGNvbG9yOiAnIzJkOWNkYicgfSwNCiAgICAgICAgeyBsYWJlbDogJ+WkqeeEtuawlOa2iOiAlycsIHZhbHVlOiAnZ2FzJywgdW5pdDogJ+eri+aWueexsycsIGNvbG9yOiAnIzFhNzNlOCcgfSwNCiAgICAgICAgeyBsYWJlbDogJ+iSuOaxvea2iOiAlycsIHZhbHVlOiAnc3RlYW0nLCB1bml0OiAn5ZCoJywgY29sb3I6ICcjMjdhZTYwJyB9DQogICAgICBdLA0KICAgICAgDQogICAgICAvLyDog73mupDmiJDmnKzmlbDmja4NCiAgICAgIGVuZXJneUNvc3REYXRhOiB7DQogICAgICAgIG1vbnRoczogWycx5pyIJywgJzLmnIgnLCAnM+aciCcsICc05pyIJywgJzXmnIgnLCAnNuaciCcsICc35pyIJywgJzjmnIgnLCAnOeaciCcsICcxMOaciCcsICcxMeaciCcsICcxMuaciCddLA0KICAgICAgICBlbGVjdHJpY2l0eTogWzMyMCwgMzMyLCAzMDEsIDMzNCwgMzkwLCAzMzAsIDMyMCwgMzE1LCAzMTAsIDMyNSwgMzE1LCAzMThdLA0KICAgICAgICBjb2FsOiBbMjIwLCAxODIsIDE5MSwgMjM0LCAyOTAsIDMzMCwgMzEwLCAyOTUsIDMwMCwgMjg1LCAyNzAsIDI3NV0sDQogICAgICAgIGdhczogWzE1MCwgMjMyLCAyMDEsIDE1NCwgMTkwLCAxODAsIDE2NSwgMTc1LCAxOTAsIDE5NSwgMjA1LCAyMTBdLA0KICAgICAgICBzdGVhbTogWzk4LCA3NywgMTAxLCA5OSwgMTIwLCAxMjUsIDExMCwgMTAwLCAxMDUsIDExNSwgMTEwLCAxMjBdDQogICAgICB9LA0KICAgICAgDQogICAgICAvLyDog73mupDor6bmg4XmlbDmja4gLSDlvZPml6XmtojogJcNCiAgICAgIGVuZXJneURldGFpbERhdGE6IFsNCiAgICAgICAgeyANCiAgICAgICAgICBuYW1lOiAn6L2s54KJ54Wk5rCUJywgDQogICAgICAgICAgY2F0ZWdvcnk6ICdnYXMnLA0KICAgICAgICAgIHZhbHVlOiAxMjUuNiwgDQogICAgICAgICAgdW5pdDogJ+S4h23CsycsIA0KICAgICAgICAgIHRhcmdldDogMTMwLCANCiAgICAgICAgICB3YXJuaW5nOiAxNDAsIA0KICAgICAgICAgIGRhbmdlcjogMTUwLCANCiAgICAgICAgICBzdGF0dXM6ICdub3JtYWwnLA0KICAgICAgICAgIHRyZW5kOiAtMi4xIC8vIOebuOavlOaYqOaXpeWPmOWMlueZvuWIhuavlA0KICAgICAgICB9LA0KICAgICAgICB7IA0KICAgICAgICAgIG5hbWU6ICfpq5jngonnhaTmsJQnLCANCiAgICAgICAgICBjYXRlZ29yeTogJ2dhcycsDQogICAgICAgICAgdmFsdWU6IDI4Ny4zLCANCiAgICAgICAgICB1bml0OiAn5LiHbcKzJywgDQogICAgICAgICAgdGFyZ2V0OiAyODAsIA0KICAgICAgICAgIHdhcm5pbmc6IDMwMCwgDQogICAgICAgICAgZGFuZ2VyOiAzMjAsIA0KICAgICAgICAgIHN0YXR1czogJ3dhcm5pbmcnLA0KICAgICAgICAgIHRyZW5kOiA1LjMNCiAgICAgICAgfSwNCiAgICAgICAgeyANCiAgICAgICAgICBuYW1lOiAn54Sm54KJ54Wk5rCUJywgDQogICAgICAgICAgY2F0ZWdvcnk6ICdnYXMnLA0KICAgICAgICAgIHZhbHVlOiA5OC40LCANCiAgICAgICAgICB1bml0OiAn5LiHbcKzJywgDQogICAgICAgICAgdGFyZ2V0OiAxMDAsIA0KICAgICAgICAgIHdhcm5pbmc6IDExMCwgDQogICAgICAgICAgZGFuZ2VyOiAxMjAsIA0KICAgICAgICAgIHN0YXR1czogJ25vcm1hbCcsDQogICAgICAgICAgdHJlbmQ6IC0xLjINCiAgICAgICAgfSwNCiAgICAgICAgeyANCiAgICAgICAgICBuYW1lOiAn5aSp54S25rCUJywgDQogICAgICAgICAgY2F0ZWdvcnk6ICdnYXMnLA0KICAgICAgICAgIHZhbHVlOiA0NS43LCANCiAgICAgICAgICB1bml0OiAn5LiHbcKzJywgDQogICAgICAgICAgdGFyZ2V0OiA0MCwgDQogICAgICAgICAgd2FybmluZzogNDUsIA0KICAgICAgICAgIGRhbmdlcjogNTAsIA0KICAgICAgICAgIHN0YXR1czogJ2RhbmdlcicsDQogICAgICAgICAgdHJlbmQ6IDEyLjUNCiAgICAgICAgfSwNCiAgICAgICAgeyANCiAgICAgICAgICBuYW1lOiAn6aWx5ZKM6JK45rG9JywgDQogICAgICAgICAgY2F0ZWdvcnk6ICdzdGVhbScsDQogICAgICAgICAgdmFsdWU6IDU2LjIsIA0KICAgICAgICAgIHVuaXQ6ICfkuIflkKgnLCANCiAgICAgICAgICB0YXJnZXQ6IDU1LCANCiAgICAgICAgICB3YXJuaW5nOiA2MCwgDQogICAgICAgICAgZGFuZ2VyOiA2NSwgDQogICAgICAgICAgc3RhdHVzOiAnbm9ybWFsJywNCiAgICAgICAgICB0cmVuZDogMS44DQogICAgICAgIH0sDQogICAgICAgIHsgDQogICAgICAgICAgbmFtZTogJ+i/h+eDreiSuOaxvScsIA0KICAgICAgICAgIGNhdGVnb3J5OiAnc3RlYW0nLA0KICAgICAgICAgIHZhbHVlOiAzMi44LCANCiAgICAgICAgICB1bml0OiAn5LiH5ZCoJywgDQogICAgICAgICAgdGFyZ2V0OiAzMCwgDQogICAgICAgICAgd2FybmluZzogMzUsIA0KICAgICAgICAgIGRhbmdlcjogNDAsIA0KICAgICAgICAgIHN0YXR1czogJ3dhcm5pbmcnLA0KICAgICAgICAgIHRyZW5kOiA3LjINCiAgICAgICAgfSwNCiAgICAgICAgeyANCiAgICAgICAgICBuYW1lOiAn5bel5Lia55So5rC0JywgDQogICAgICAgICAgY2F0ZWdvcnk6ICd3YXRlcicsDQogICAgICAgICAgdmFsdWU6IDE0Mi41LCANCiAgICAgICAgICB1bml0OiAn5LiH5ZCoJywgDQogICAgICAgICAgdGFyZ2V0OiAxNDAsIA0KICAgICAgICAgIHdhcm5pbmc6IDE1MCwgDQogICAgICAgICAgZGFuZ2VyOiAxNjAsIA0KICAgICAgICAgIHN0YXR1czogJ25vcm1hbCcsDQogICAgICAgICAgdHJlbmQ6IDEuNQ0KICAgICAgICB9LA0KICAgICAgICB7IA0KICAgICAgICAgIG5hbWU6ICflvqrnjq/lhrfljbTmsLQnLCANCiAgICAgICAgICBjYXRlZ29yeTogJ3dhdGVyJywNCiAgICAgICAgICB2YWx1ZTogMjg1LjMsIA0KICAgICAgICAgIHVuaXQ6ICfkuIflkKgnLCANCiAgICAgICAgICB0YXJnZXQ6IDI4MCwgDQogICAgICAgICAgd2FybmluZzogMzAwLCANCiAgICAgICAgICBkYW5nZXI6IDMyMCwgDQogICAgICAgICAgc3RhdHVzOiAnd2FybmluZycsDQogICAgICAgICAgdHJlbmQ6IDMuOA0KICAgICAgICB9LA0KICAgICAgICB7IA0KICAgICAgICAgIG5hbWU6ICfpq5jljovnlLXlipsnLCANCiAgICAgICAgICBjYXRlZ29yeTogJ2VsZWN0cmljaXR5JywNCiAgICAgICAgICB2YWx1ZTogMTg1Ni40LCANCiAgICAgICAgICB1bml0OiAn5LiHa1doJywgDQogICAgICAgICAgdGFyZ2V0OiAxODAwLCANCiAgICAgICAgICB3YXJuaW5nOiAxOTAwLCANCiAgICAgICAgICBkYW5nZXI6IDIwMDAsIA0KICAgICAgICAgIHN0YXR1czogJ3dhcm5pbmcnLA0KICAgICAgICAgIHRyZW5kOiA0LjINCiAgICAgICAgfSwNCiAgICAgICAgeyANCiAgICAgICAgICBuYW1lOiAn5Lit5Y6L55S15YqbJywgDQogICAgICAgICAgY2F0ZWdvcnk6ICdlbGVjdHJpY2l0eScsDQogICAgICAgICAgdmFsdWU6IDk0NS4yLCANCiAgICAgICAgICB1bml0OiAn5LiHa1doJywgDQogICAgICAgICAgdGFyZ2V0OiA5NTAsIA0KICAgICAgICAgIHdhcm5pbmc6IDEwMDAsIA0KICAgICAgICAgIGRhbmdlcjogMTA1MCwgDQogICAgICAgICAgc3RhdHVzOiAnbm9ybWFsJywNCiAgICAgICAgICB0cmVuZDogLTAuOA0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgDQogICAgICAvLyDliIbljoLog73mupDmtojogJfmlbDmja4gLSDmjInmnIjnu5/orqENCiAgICAgIGZhY3RvcnlFbmVyZ3lEYXRhOiB7DQogICAgICAgIG1vbnRoczogWycx5pyIJywgJzLmnIgnLCAnM+aciCcsICc05pyIJywgJzXmnIgnLCAnNuaciCddLA0KICAgICAgICBlbGVjdHJpY2l0eTogeyAvLyDnlLXlipvmtojogJcgKOS4h+WNg+eTpuaXtikNCiAgICAgICAgICAn6ZKZ5Lia5YiG5Y6CJzpbMTQwMDg4NCwxNDM0MDA1LDE0MzU3NjYsMTM3NjMxOSwxMzAxMDk1LDEyNjk2MzBdLA0KICAgICAgICAgICfnn7/muKPlvq7nsoknOls1ODA1Nzk0LDIxMzE4NDcsNjA4OTA0Niw2MTAwOTk5LDY0MTczMzUsNjQ3ODI2Ml0sDQogICAgICAgICAgJ+eDp+e7k+WIhuWOgi0xI+eDp+e7kyc6WzEzMTE3OTAyLDEyOTQzNTY4LDExMDYxNDQ0LDEwODEyMzkzLDExNjIzNzAyLDExMDMyNTM5XSwNCiAgICAgICAgICAn54On57uT5YiG5Y6CLTIj54On57uTJzpbMTMwMzM5MjYsMTA0MzY2MzQsOTI4Nzg4NCw5NzY5MzE2LDk4NzkzOTcsMTA1NjU3NjBdLA0KICAgICAgICAgICfngrzpk4HliIbljoItMSPpq5jngoknOlswLDAsMCwwLDAsMF0sDQogICAgICAgICAgJ+eCvOmTgeWIhuWOgi0yI+mrmOeCiSc6WzY2OTQwLDAsMCwwLDAsMF0sDQogICAgICAgICAgJ+eCvOmTgeWIhuWOgi0zI+mrmOeCiSc6WzAsMCwwLDAsMCwwXSwNCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLeWwj+WWt+eFpCc6WzAsMCwwLDAsMCwwXSwNCiAgICAgICAgICAn5LiA54K86ZKiJzpbMzA3NjgxNjcsMzAxMjU3ODEsMzAyOTc0NjMsMjg5ODA0OTcsMjk3NzQxNTksMzEzNDMzOTddLA0KICAgICAgICAgICfkuozngrzpkqInOlsyMjQ4ODQ5NSwyMTk2ODk0MywyMTc4NzkxNiwyMjE3MTA2NywyMTI0MjExNSwyMTc4ODExOV0sDQogICAgICAgICAgJ+S4gOi9p+mSoic6WzU3OTU3NzcsNTQ1MjIwNCw1NzExMDUxLDU2NDg1NzUsNTQ5NjQ0Nyw1NzMzNDAzXSwNCiAgICAgICAgICAn5LqM6L2nLeWkp+ajkic6WzMyNDYyNTAsMzE5NTA5MSwzMjY4ODM2LDMzNjMwODIsMzI2MjU1MywzNDY2OTM1XSwNCiAgICAgICAgICAn5LqM6L2nLeWwj+ajkic6WzUwNTkwMTgsNDk1NDUxMSw0ODExOTg3LDUwNTM0NTYsNDY4NzkyMiw0ODUyMzcwXSwNCiAgICAgICAgICAn54m55p2/54K86ZKiJzpbMzAzODc4NjIsMjk4NDIwMTksMjY0MzE3MTYsMjk0NjkzNzIsMjkyNzEwMzUsMjkwMzU1MjBdLA0KICAgICAgICAgICczNTAw5Lit5p2/JzpbNzcwNjMwMCw2NjQ0NDIwLDczOTc3MTYsNzMyODEwMiw3MjA2MjE1LDc0MjExNzldLA0KICAgICAgICAgICc0MzAw5Y6a5p2/JzpbMTM1MTkxMTIsMTI0NjQ2NjIsMTAwMjg1MzYsMTI4ODEyODYsMTI2NzQ5NDAsMTMxNjY2NzldLA0KICAgICAgICAgICfng63lpITnkIYnOlsyODEzOTM3LDI3MjY1MDEsMjI3NTQyNSwyMzg0NDEyLDIyMDY1NDgsMzEzNTEwNV0sDQogICAgICAgICAgJ+mrmOe6v+WIhuWOgic6Wzc2MjE0NTIsNzgyMjUzOCw3OTIwNDcwLDcxNTI0NDYsNzA3MTQxMiw3MzQyMDY2XSwNCiAgICAgICAgICAn57q/5p2Q5rex5Yqg5belJzpbOTY4NjU0LDg3ODA3NSw5MjUyODQsOTAyNTczLDkxNTQzNCw5MTUwNTNdLA0KICAgICAgICAgICfmo5LmnZDmt7HliqDlt6UnOlszMzMwMjk1LDMyODA4NzUsMzM1MDUxOCwzNDgxODk4LDMzMDQzNTcsMzMyNjQzM10sDQogICAgICAgICAgJ+eDreeUteWIhuWOgi3ng63nlLUnOls4NzA5NTE5LDk3NTc4NDAsMTAxMDI3MjAsOTY2MDQ4MCw5MzA3NjgwLDc0NzM1MjBdLA0KICAgICAgICAgICfng63nlLXliIbljoIt5Lqa5Li055WMJzpbMjg0MjI0NSw0MDQyMDgwLDM2MzQ0MDAsNDMwOTgwMCw0MzU4NDM1LDQwMzM1ODhdLA0KICAgICAgICAgICfng63nlLXliIbljoIt5L2Z54OtJzpbMTMzNTAwLDI0NzI4MCwxMTI2NDAsMTEyNjQwLDIxODU2MCwxMzY0ODBdLA0KICAgICAgICAgICfng63nlLXliIbljoIt6byT6aOOJzpbMTIxNDYxNDAsMjE5OTU2MTYsMjE5Njk4MDMsMjMzNzc3NjIsMjg0Mjg5NzYsMzcwMzY5MzNdLA0KICAgICAgICAgICfliLbmsKfliIbljoIt5LiA5pyfJzpbMjYyMDM1NDksMjY0NTY1MjMsMjAzNDc0NjQsMjY2NDY3MzUsMjU1MjM4ODgsMjcyOTA3NDZdLA0KICAgICAgICAgICfliLbmsKfliIbljoIt5LiJ5pyfJzpbMjkyMzk2MzIsMjc4ODY0MzQsMjkyNzQ2NTcsMjY2MDE5MTksMjYxNjIwMTUsMjY4NjU2NzhdLA0KICAgICAgICAgICfliLbmsKfliIbljoIt56m65Y6L56uZJzpbNjA5Mjc1OSw2NDgzNjA5LDY0NTU5MzAsNjY2MTAzOSw2MzY5Mjk3LDY0NjQ3OTJdLA0KICAgICAgICAgICfmsLTlpITnkIYt5LiA5pyfJzpbMjQ2NzI0MCwyNDcwNDQyLDI1MTU4MjksMjU0OTQ1NywzMjIyMDMxLDI4ODQwNjldLA0KICAgICAgICAgICfmsLTlpITnkIYt5LqM5pyfJzpbNDk1MTg5Nyw0OTAyOTg2LDQ4NDM3MjMsNTA0MDk4NCw1MDIxNzA4LDUyNjMyMjRdLA0KICAgICAgICAgICfmsLTlpITnkIYt5LiJ5pyfJzpbNTIyNDY0OSw1MzIwMDEyLDUwNjA4MTMsNTQwNzU4OCw1NDg4NzE1LDU4MTY1NjBdLA0KICAgICAgICAgICfnhaTmsJTliIbljoInOls2NDMxMzIsNjkzNDY2LDY1NzA1Miw2NTk1NDMsNjI0ODMwLDYyMDk3M10sDQogICAgICAgICAgJ+S+m+eUteS4gOWMuic6WzEwMDQxNSwxMDM1MzcsMTAzOTA2LDEzMzYxMSwxNjYwMjcsMTgwMjI3XSwNCiAgICAgICAgICAn5YW05r6E6ZKi55CDJzpbMTA4Nzk4MSw4NDA4MTgsOTgxNzUxLDEwNTcyNzUsOTA5Mjc1LDExODg1NTddLA0KICAgICAgICAgICflhbTmvoTmuK/liqEnOls2ODAyMyw1OTQ4MSw2OTkxOCw2MzMzNiw0NDU0MSw2NTM4OV0sDQogICAgICAgICAgJ+WCqOi/kOWFrOWPuCc6WzU3NTkzMjQsNTg1OTk3NSw1MzUyMjA2LDUzMTY2NDAsNTM3ODMzNyw1NjQ0OTM4XSwNCiAgICAgICAgICAn57u85ZCI5Yip55SoJzpbMTA0Njc2MywxMDQ4NzM3LDExMDMxNzgsMTAwNjk0MywxMDgyOTc1LDEwNjg4NjhdLA0KICAgICAgICAgICflkIjph5HngonliIbljoInOlsyNzY5MDkyLDQzMjEwMDUsMzIyMTU1OSw0OTMyNzYxLDQ3ODk4MDAsNTg3ODk4MF0sDQogICAgICAgICAgJ+eJqeeuoemDqCc6WzM5OTAyLDQzNDk4LDM0OTUzLDI5NjYyLDI0MzczLDI0MjI3XSwNCiAgICAgICAgICAn5ZCO5Yuk6YOoJzpbNDY0MzYsNTExNDQsMzk3MzksMzY0NTksMzY4MTcsMzY1OTZdLA0KICAgICAgICAgICflhbbku5YnOls4MjQzNzUsNzc1MTA3LDc0OTk0Myw2ODgzNjUsNzY0MzM3LDk3ODUyMF0sDQogICAgICAgICAgJ+eJqeeuoemDqCc6WzEyOTUxNDAsNjY0MDAwLDYyMTkyMCw1NDE0ODAsNTc2NjIwLDgzNjk2MF0sDQogICAgICAgICAgJ+WQiOiuoSc6WzI3ODgyMjQ3NiwyODAzMjUyNjQsMjY5MzM1MTYxLDI4MTcxMDI3MiwyODQ4MzM5MDMsMzAwNjYyMjc0XQ0KICAgICAgICAgIC8vICfngrzpk4HliIbljoInOiBbODUwLCA4MjAsIDg0MCwgODYwXSwNCiAgICAgICAgICAvLyAn54On57uT5YiG5Y6CJzogWzQwMCwgMzgwLCAzOTAsIDQxMF0sDQogICAgICAgICAgLy8gJ+S4gOeCvOmSoic6IFs5NTAsIDkyMCwgOTQwLCA5NjBdLA0KICAgICAgICAgIC8vICfkuozngrzpkqInOiBbOTAwLCA4ODAsIDg5MCwgOTIwXSwNCiAgICAgICAgICAvLyAn57u85ZCI5Yip55SoJzogWzI4MCwgMjcwLCAyOTAsIDI3NV0sDQogICAgICAgICAgLy8gJ+S4gOi9p+mSoic6IFszODAsIDM3MCwgMzkwLCAzNzVdLA0KICAgICAgICAgIC8vICfkuozovafpkqInOiBbMzYwLCAzNTAsIDM3MCwgMzU1XSwNCiAgICAgICAgICAvLyAn5LiJ6L2n6ZKiJzogWzM0MCwgMzMwLCAzNTAsIDMzNV0sDQogICAgICAgICAgLy8gJ+eJueauiumSoui9p+adkCc6IFszMjAsIDMxMCwgMzMwLCAzMTVdLA0KICAgICAgICAgIC8vICfmo5LmnZDovafliLbljoInOiBbMzAwLCAyOTAsIDMxMCwgMjk1XSwNCiAgICAgICAgICAvLyAn5qOS57q/5p2Q5rex5Yqg5belKOajkiknOiBbMTgwLCAxNzAsIDE5MCwgMTc1XSwNCiAgICAgICAgICAvLyAn5qOS57q/5p2Q5rex5Yqg5belKOe6vyknOiBbMTYwLCAxNTAsIDE3MCwgMTU1XSwNCiAgICAgICAgICAvLyAn57q/5p2Q5rex5Yqg5belJzogWzE0MCwgMTMwLCAxNTAsIDEzNV0sDQogICAgICAgICAgLy8gJ+mprOenkeaJmOmSoueQgyc6IFs0MjAsIDQxMCwgNDMwLCA0MjVdLA0KICAgICAgICAgIC8vICfnibnpkqLngrzpkqLliIbljoInOiBbMzgwLCAzNzAsIDM5MCwgMzc1XSwNCiAgICAgICAgICAvLyAn5Lit5p2/5YiG5Y6CJzogWzM2MCwgMzUwLCAzNzAsIDM1NV0sDQogICAgICAgICAgLy8gJ+WOmuadv+WIhuWOgic6IFszNDAsIDMzMCwgMzUwLCAzMzVdLA0KICAgICAgICAgIC8vICfpkqLmnZDmt7HliqDlt6UnOiBbMzIwLCAzMTAsIDMzMCwgMzE1XSwNCiAgICAgICAgICAvLyAn54Ot55S1JzogWzE4MCwgMTcwLCAxOTAsIDE3NV0sDQogICAgICAgICAgLy8gJ+S+m+eUteW3peWMuic6IFsxNjAsIDE1MCwgMTcwLCAxNTVdLA0KICAgICAgICAgIC8vICfmsLTlpITnkIbliIbljoInOiBbMTQwLCAxMzAsIDE1MCwgMTM1XSwNCiAgICAgICAgICAvLyAn5Yi25rCn5YiG5Y6CJzogWzEyMCwgMTEwLCAxMzAsIDExNV0sDQogICAgICAgICAgLy8gJ+eFpOawlOWIhuWOgic6IFsxMDAsIDkwLCAxMTAsIDk1XSwNCiAgICAgICAgICAvLyAn5YKo6L+Q5YWs5Y+4JzogWzE4MCwgMTc1LCAxODUsIDE4Ml0sDQogICAgICAgICAgLy8gJ+ajgOS/ruWIhuWOgic6IFsxNTAsIDE0NSwgMTU1LCAxNTJdDQogICAgICAgIH0sDQogICAgICAgIHdhdGVyOiB7IC8vIOawtOi1hOa6kOa2iOiAlyAo5LiH5ZCoKQ0KICAgICAgICAgICfpkpnkuJrliIbljoInOlsyNTE3LDIzNzYsMjI1MywyMTczLDIyNTksMjMwMV0sDQogICAgICAgICAgJ+efv+a4o+W+rueyiSc6WzI4OTAsMjU5MSwyNDc4LDIzNTgsMjM4MSwyMjU2XSwNCiAgICAgICAgICAn54On57uT5YiG5Y6CLTEj54On57uTJzpbNDEyOTAsNDA0NDMsMzk1OTEsNDAxMzYsNDA4NTIsNDAxNDZdLA0KICAgICAgICAgICfng6fnu5PliIbljoItMiPng6fnu5MnOls3MTgwNCw2NzUzOSw2OTAwOSw2OTk0Nyw3MjA1MCw3MDM0NF0sDQogICAgICAgICAgJ+eCvOmTgeWIhuWOgi0xI+mrmOeCiSc6WzIwODcwLDIxMDgyLDIxMjMxLDI2NzI5LDI0NzAyLDI4MTg4XSwNCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLTIj6auY54KJJzpbNjEwMzIsNjU2MTUsNTYyMTgsNjU2OTAsNjM1NjcsNjcwMzNdLA0KICAgICAgICAgICfngrzpk4HliIbljoItMyPpq5jngoknOls3MDYwNCw2OTk2NCw3ODYxMyw4NTkxNCw4NTM1OCw5OTAxNl0sDQogICAgICAgICAgJ+eCvOmTgeWIhuWOgi3llrfnhaTvvIgwIzIj6auY54KJ77yJJzpbMjMwOCwyNDU3LDI4OTcsMzAxNywyODUxLDM1OTddLA0KICAgICAgICAgICfkuIDngrzpkqInOls0NjMxMTUyLDQ1MjkxNjAsNDYwOTUxMCw0NjQ1NDQ5LDQ1MzY5MzIsNDYxODU2M10sDQogICAgICAgICAgJ+S6jOeCvOmSoic6WzkxMDQ2MjksODk3NDU4NCw4NzA3MjQxLDg4NjQ0NDksODU3NDExMCw4NzgwNDkzXSwNCiAgICAgICAgICAn5LiA6L2n6ZKiJzpbMTYxMzQxNiwxNTY2MzAzLDE2MDM2NjAsMTYwNDAyMSwxNTc0MjIyLDE2NDE5NTZdLA0KICAgICAgICAgICfkuozovact5aSn5qOSJzpbMTU0OTk3NiwxNDczNjUyLDE0NDg5OTgsMTQ4Mjg0MCwxNDIwODQ4LDE0NjEwOTddLA0KICAgICAgICAgICfkuozovact5bCP5qOSJzpbMTc2MTkyMiwxNjM2NTE4LDE1OTk2MTgsMTcwNzc3NywxNjExMjkwLDE1NjY0NTNdLA0KICAgICAgICAgICfnibnmnb/ngrzpkqInOlsxMTA2NTIwMiwxMDg2MTg5NywxMDA3ODM3MCwxMDc5ODI3MSwxMDUzOTI0NiwxMDQxNDA3Ml0sDQogICAgICAgICAgJzM1MDDkuK3mnb8nOlsyNTI4NzY4LDI1MzExMDIsMjU3NzEwNiwyNjE0MzMzLDI1MDk4NTIsMjc0NjE2NF0sDQogICAgICAgICAgJzQzMDDljprmnb8nOls1MzYwMiw1Mjk3Myw0NzEwMCw1MTcyNyw0ODk5Myw1MjQzOV0sDQogICAgICAgICAgJzQzMDDmsLTlpITnkIYnOls1MTk1Nyw1OTk5Nyw1ODY5MSw1NTUxNSw2MTg5MSw2NjQ3NF0sDQogICAgICAgICAgJ+eDreWkhOeQhic6WzE1ODczLDEzMjU3LDExNTM2LDEwMjEzLDEwNzk2LDEwMDE4XSwNCiAgICAgICAgICAn6auY57q/5YiG5Y6CJzpbMjQ3ODMsMjEzMjgsMTkwODIsMjMxMTcsMjQ1NDgsMjM1MThdLA0KICAgICAgICAgICfng63nlLXliIbljoIt54Ot55S1JzpbMzI2NTQsMzU5OTEsNDY5NzYsMzQ5MDcsMzU4MzEsMzAyMTFdLA0KICAgICAgICAgICfng63nlLXliIbljoIt6byT6aOOJzpbNjY3ODEsNjA1NzgsNzQ1MDYsODQ3MTUsNTY4NDcsNjAyMjJdLA0KICAgICAgICAgICfng63nlLXliIbljoIt5Lqa5Li055WMJzpbNTA3MDIsNDI4OTgsNTMzNjAsODA5MzcsNzUwOTAsODMyMjBdLA0KICAgICAgICAgICfng63nlLXliIbljoIt5YeA5rC05Y6C55u05L6b5Y6f5rC0JzpbODI3MTAsNjY2NDEsNTg2NzAsNTkwMjIsNTEwMzQsNTE3MzldLA0KICAgICAgICAgICfliLbmsKfliIbljoIt5LiA5pyfJzpbNDgxMDcsODk5NTMsNDA2NjUsNTEzNjcsNTY2MDUsNTU4NjZdLA0KICAgICAgICAgICfliLbmsKfliIbljoIt5LiJ5pyfJzpbMjcxMzYsMTMzOTcsMTA3MjYsMjE4OTYsMjU2ODIsMTE3MTZdLA0KICAgICAgICAgICfliLbmsKfliIbljoIt56m65Y6L56uZJzpbMzkyOCwzNTYzLDQzODUsNDk4MywzMzQzLDM1NDJdLA0KICAgICAgICAgICfmsLTlpITnkIYt5LiA5pyfJzpbNjY2NDU3LDY4NDg2MSw2OTkwMTcsNzA2Mzc0LDcwMzQ5Nyw3MzcyOTFdLA0KICAgICAgICAgICfmsLTlpITnkIYt5LqM5pyfJzpbMjgzNzMzLDI5MDY2MCwyNDUyMTcsMjQzODgzLDI0NTMwMCwyODU0ODVdLA0KICAgICAgICAgICfmsLTlpITnkIYt5LiJ5pyfJzpbMTYyMDEyLDE0MDczNywxNDMyOTgsMjExMzI5LDIwNjMwMywyNDUxMzJdLA0KICAgICAgICAgICfkvpvnlLXkuIDljLonOlsyMjczLDIzNTcsMjUwMiwyNTk3LDIzODUsMjE1M10sDQogICAgICAgICAgJ+WCqOi/kOWFrOWPuCc6WzQxOTA2LDQwMzYyLDQxOTgwLDQyMzYxLDQxOTYzLDQwNzc5XSwNCiAgICAgICAgICAn57u85ZCI5Yip55SoJzpbMjUxMTcsMjQ1ODYsMTk5MzEsMjU5MjYsMjI1NTUsMTU5NjRdLA0KICAgICAgICAgICflkIjph5HngonliIbljoInOls5Mzk5NDQsMTQwNjA2MSwxMTI5NjkwLDE1OTExOTYsMTM5Mjk5MCwxODM0Mzk4XSwNCiAgICAgICAgICAn5ZCO5Yuk57u85ZCI5qW8JzpbNjA2MjY0LDQ3MzAyNyw1MjA4NTUsMjMzMzg0LDE2ODE0NiwzNjU5OTddLA0KICAgICAgICAgICflhaznlKgnOls3OTA2NSw3OTg3MSw3OTYzNCw3OTg3MSw4MDk3MSw4MTM0MV0sDQogICAgICAgICAgJ+WQiOiuoSc6WzM1NzkzMzg0LDM1NDQ4MzgxLDM0MjA0NjE0LDM1NjI4NDI0LDM0Mzc1MjkwLDM1NTk5MTg0XQ0KICAgICAgICAgIC8vICfngrzpk4HliIbljoInOiBbNTUsIDUzLCA1NiwgNTddLA0KICAgICAgICAgIC8vICfng6fnu5PliIbljoInOiBbMzAsIDI5LCAzMSwgMzJdLA0KICAgICAgICAgIC8vICfkuIDngrzpkqInOiBbNjUsIDYzLCA2NiwgNjddLA0KICAgICAgICAgIC8vICfkuozngrzpkqInOiBbNjAsIDU4LCA2MSwgNjJdLA0KICAgICAgICAgIC8vICfnu7zlkIjliKnnlKgnOiBbMjAsIDE5LCAyMSwgMjAuNV0sDQogICAgICAgICAgLy8gJ+S4gOi9p+mSoic6IFsyNSwgMjQsIDI2LCAyNS41XSwNCiAgICAgICAgICAvLyAn5LqM6L2n6ZKiJzogWzIzLCAyMiwgMjQsIDIzLjVdLA0KICAgICAgICAgIC8vICfkuInovafpkqInOiBbMjIsIDIxLCAyMywgMjIuNV0sDQogICAgICAgICAgLy8gJ+eJueauiumSoui9p+adkCc6IFsyMSwgMjAsIDIyLCAyMS41XSwNCiAgICAgICAgICAvLyAn5qOS5p2Q6L2n5Yi25Y6CJzogWzIwLCAxOSwgMjEsIDIwLjVdLA0KICAgICAgICAgIC8vICfmo5Lnur/mnZDmt7HliqDlt6Uo5qOSKSc6IFsxMiwgMTEsIDEzLCAxMi41XSwNCiAgICAgICAgICAvLyAn5qOS57q/5p2Q5rex5Yqg5belKOe6vyknOiBbMTEsIDEwLCAxMiwgMTEuNV0sDQogICAgICAgICAgLy8gJ+e6v+adkOa3seWKoOW3pSc6IFs5LCA4LCAxMCwgOS41XSwNCiAgICAgICAgICAvLyAn6ams56eR5omY6ZKi55CDJzogWzI4LCAyNywgMjksIDI4LjVdLA0KICAgICAgICAgIC8vICfnibnpkqLngrzpkqLliIbljoInOiBbMjUsIDI0LCAyNiwgMjUuNV0sDQogICAgICAgICAgLy8gJ+S4readv+WIhuWOgic6IFsyMywgMjIsIDI0LCAyMy41XSwNCiAgICAgICAgICAvLyAn5Y6a5p2/5YiG5Y6CJzogWzIyLCAyMSwgMjMsIDIyLjVdLA0KICAgICAgICAgIC8vICfpkqLmnZDmt7HliqDlt6UnOiBbMjEsIDIwLCAyMiwgMjEuNV0sDQogICAgICAgICAgLy8gJ+eDreeUtSc6IFs2NSwgNjMsIDY3LCA2Nl0sDQogICAgICAgICAgLy8gJ+S+m+eUteW3peWMuic6IFs0NSwgNDMsIDQ3LCA0Nl0sDQogICAgICAgICAgLy8gJ+awtOWkhOeQhuWIhuWOgic6IFs3MCwgNjgsIDcyLCA3MV0sDQogICAgICAgICAgLy8gJ+WItuawp+WIhuWOgic6IFs1NSwgNTMsIDU3LCA1Nl0sDQogICAgICAgICAgLy8gJ+eFpOawlOWIhuWOgic6IFs0NSwgNDMsIDQ3LCA0Nl0sDQogICAgICAgICAgLy8gJ+WCqOi/kOWFrOWPuCc6IFsxNSwgMTQsIDE2LCAxNS41XSwNCiAgICAgICAgICAvLyAn5qOA5L+u5YiG5Y6CJzogWzEyLCAxMS41LCAxMi41LCAxMi4yXQ0KICAgICAgICB9LA0KICAgICAgICBnYXM6IHsNCiAgICAgICAgICAn6ZKZ5Lia5YiG5Y6CJzpbMjA4MCwxNDMxNywxMjg3NSw2MjQwLDAsMF0sDQogICAgICAgICAgJ+efv+a4o+W+rueyiSc6WzAsMCwwLDAsMCwwXSwNCiAgICAgICAgICAn54On57uT5YiG5Y6CLTEj54On57uTJzpbOTI4LDM1OCwxMDU0LDI0NzYsMTA0MCwxMDQwXSwNCiAgICAgICAgICAn54On57uT5YiG5Y6CLTIj54On57uTJzpbMTM0NzEsMzQ0MCwxMTU5MiwxNDQ0OCwxOTA5NCwzODI3Ml0sDQogICAgICAgICAgJ+eCvOmTgeWIhuWOgi0xI+mrmOeCiSc6WzQyNjQsMjM5MiwxODMyLDM5NTIsMTQ1NiwyMTg0XSwNCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLTIj6auY54KJJzpbMzc0NCw2MzQ0LDE0ODA4LDQxMDQsMzQzMiw1NjE2XSwNCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLTMj6auY54KJJzpbNDg4ODAsNzQ0ODMsNjI1NDEsOTIyNzIsMTc5OTIsNDc4ODldLA0KICAgICAgICAgICfngrzpk4HliIbljoIt5bCP5Za354WkJzpbOTM2LDExNDQsMTA0MCwxMjEyLDkzNiwxMDQwXSwNCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLeWkp+WWt+eFpCc6WzQyNDMyLDM1NDY0LDMyMjAwLDIwNjk2LDE1Mjg4LDE3Nzg0XSwNCiAgICAgICAgICAn5LiA54K86ZKiJzpbMzIxMDQ0LDQ0MTE3OSw0Mzg0MjMsNDUwOTkzLDM4MTU3NSwzNTEyNzVdLA0KICAgICAgICAgICfkuozngrzpkqInOlsxNTA4NDQsMjA4ODg5LDIxMjYwNSwyMTIyNzQsMjA0MzcyLDE5NTQyNV0sDQogICAgICAgICAgJ+S4gOi9p+mSoic6WzI4NTcsMTIwODQsMTIxMjAsNjgxOCw2MjMwLDE2MjE3XSwNCiAgICAgICAgICAn5LqM6L2nLeWkp+ajkic6WzIzODI0LDMwMDAwLDE0NTIwLDEwNjMwLDI0MDM2LDI0NDg5XSwNCiAgICAgICAgICAn5LqM6L2nLeWwj+ajkic6WzE2MDAwLDEyNDg3LDg5MjYsNDU1NSwxNjAyNCwxMDQ5Nl0sDQogICAgICAgICAgJ+eJueadv+eCvOmSoic6WzE1NDc5MCwyMTg4MzIsMTkwNTY4LDEzNTc1NywxNDM0MDUsMTQwODg2XSwNCiAgICAgICAgICAnMzUwMOS4readvyc6WzYxOTIzMyw2NDQyMTcsNzY3NTYzLDkwODkwNiw4OTAwMDIsOTI2NjM2XSwNCiAgICAgICAgICAnNDMwMOWOmuadvyc6WzQ5MjY1Niw2MjE3NzUsNzg1MjQ3LDUwMDk4OCw1NjM4ODYsNzkyOTE5XSwNCiAgICAgICAgICAn54Ot5aSE55CGJzpbMTA5Mzc0MCwxMzcwMzg5LDEyOTYzNjUsMTQwODk4OCwxMTcwMzYwLDEyOTkwNzZdLA0KICAgICAgICAgICfpq5jnur/liIbljoInOlsyMTQsOTYsMjk4LDIwNywxMDAsMjA0XSwNCiAgICAgICAgICAn5qOS5p2Q5rex5Yqg5belJzpbMTEzMzYwMCwxMTc3NDczLDExMzk0NDAsMTIxNDEwOCwxMTQxNzM3LDExNTM5MzBdLA0KICAgICAgICAgICfnur/mnZDmt7HliqDlt6UnOls2MTQwOTIsNTAxODEwLDUwOTc1MCw1NTIyODAsNTU5NDQwLDUxNzcyMF0sDQogICAgICAgICAgJ+eDreeUteWIhuWOgi3ng63nlLUnOlsxNTcxMywzMzQ3MiwxODM1NDksMjEyNzksODI1MjUsMzM5NjVdLA0KICAgICAgICAgICfng63nlLXliIbljoIt6byT6aOOJzpbNTg2MzgsMzU2NzgsMjA0MDE0LDI4MzAzLDYwNzcwLDU4MjQwXSwNCiAgICAgICAgICAn54Ot55S15YiG5Y6CLeS6muS4tOeVjCc6Wzk0MDYzLDYwMzI2LDI3MzE1LDYzOTQxLDkwMzg5LDkzODM3XSwNCiAgICAgICAgICAn54Wk5rCU5YiG5Y6CJzpbNjI4NSw3Mjg5LDc2MTYsNzUzNyw3NTYxLDMwNzVdLA0KICAgICAgICAgICflhbTmvoTpkqLnkIMnOlsxOTQ5NzAsMjA2MzA3LDIwODg5MCwyMjU0MjIsMjA5NDYzLDIxNTczMF0sDQogICAgICAgICAgJ+e7vOWQiOWIqeeUqCc6WzAsMCwwLDAsMCwwXSwNCiAgICAgICAgICAn5ZCI6K6hJzpbNTEwOTI5OCw1NzIwMjQ1LDYxNDUxNTEsNTg5ODM4Niw1NjExMTEzLDU5NDc5NDVdDQogICAgICAgICAgDQogICAgICAgICAgIC8vIOWkqeeEtuawlOa2iOiAlyAo5LiH56uL5pa557GzKQ0KICAgICAgICAgIC8vICfngrzpk4HliIbljoInOiBbMjIwLCAyMTAsIDIyNSwgMjMwXSwNCiAgICAgICAgICAvLyAn54On57uT5YiG5Y6CJzogWzEwMCwgOTUsIDEwNSwgMTEwXSwNCiAgICAgICAgICAvLyAn5LiA54K86ZKiJzogWzI1MCwgMjQwLCAyNTUsIDI2MF0sDQogICAgICAgICAgLy8gJ+S6jOeCvOmSoic6IFsyMzAsIDIyMCwgMjM1LCAyNDBdLA0KICAgICAgICAgIC8vICfnu7zlkIjliKnnlKgnOiBbNzUsIDcwLCA3OCwgNzZdLA0KICAgICAgICAgIC8vICfkuIDovafpkqInOiBbOTUsIDkwLCA5OCwgOTZdLA0KICAgICAgICAgIC8vICfkuozovafpkqInOiBbOTAsIDg1LCA5MywgOTFdLA0KICAgICAgICAgIC8vICfkuInovafpkqInOiBbODUsIDgwLCA4OCwgODZdLA0KICAgICAgICAgIC8vICfnibnmrorpkqLovafmnZAnOiBbODAsIDc1LCA4MywgODFdLA0KICAgICAgICAgIC8vICfmo5LmnZDovafliLbljoInOiBbNzUsIDcwLCA3OCwgNzZdLA0KICAgICAgICAgIC8vICfmo5Lnur/mnZDmt7HliqDlt6Uo5qOSKSc6IFs0NSwgNDAsIDQ4LCA0Nl0sDQogICAgICAgICAgLy8gJ+ajkue6v+adkOa3seWKoOW3pSjnur8pJzogWzQwLCAzNSwgNDMsIDQxXSwNCiAgICAgICAgICAvLyAn57q/5p2Q5rex5Yqg5belJzogWzM1LCAzMCwgMzgsIDM2XSwNCiAgICAgICAgICAvLyAn6ams56eR5omY6ZKi55CDJzogWzExMCwgMTA1LCAxMTIsIDEwOF0sDQogICAgICAgICAgLy8gJ+eJuemSoueCvOmSouWIhuWOgic6IFs5NSwgOTAsIDk4LCA5Nl0sDQogICAgICAgICAgLy8gJ+S4readv+WIhuWOgic6IFs5MCwgODUsIDkzLCA5MV0sDQogICAgICAgICAgLy8gJ+WOmuadv+WIhuWOgic6IFs4NSwgODAsIDg4LCA4Nl0sDQogICAgICAgICAgLy8gJ+mSouadkOa3seWKoOW3pSc6IFs4MCwgNzUsIDgzLCA4MV0sDQogICAgICAgICAgLy8gJ+eDreeUtSc6IFszNSwgMzAsIDM4LCAzNl0sDQogICAgICAgICAgLy8gJ+S+m+eUteW3peWMuic6IFsyNSwgMjAsIDI4LCAyNl0sDQogICAgICAgICAgLy8gJ+awtOWkhOeQhuWIhuWOgic6IFsyMCwgMTUsIDIzLCAyMV0sDQogICAgICAgICAgLy8gJ+WItuawp+WIhuWOgic6IFsxNSwgMTAsIDE4LCAxNl0sDQogICAgICAgICAgLy8gJ+eFpOawlOWIhuWOgic6IFsxMCwgNSwgMTMsIDExXSwNCiAgICAgICAgICAvLyAn5YKo6L+Q5YWs5Y+4JzogWzQ1LCA0MywgNDYsIDQ0XSwNCiAgICAgICAgICAvLyAn5qOA5L+u5YiG5Y6CJzogWzM1LCAzNCwgMzYsIDM1LjVdDQogICAgICAgIH0sDQogICAgICAgIHN0ZWFtOiB7IC8vIOiSuOaxvea2iOiAlyAo5LiH5ZCoKQ0KICAgICAgICAgICfpkpnkuJrliIbljoInOlswLDAsMCwwLDAsMF0sDQogICAgICAgICAgJ+efv+a4o+W+rueyiSc6WzAsMCwwLDAsMCwwXSwNCiAgICAgICAgICAn54On57uT5YiG5Y6CLTEj54On57uTJzpbMCwwLDAsMCwwLDBdLA0KICAgICAgICAgICfng6fnu5PliIbljoItMiPng6fnu5MnOlsyMzY4LDIzNzksMTc2NSwxNjE1LDE0MjIsMTY2M10sDQogICAgICAgICAgJ+eCvOmTgeWIhuWOgi0xI+mrmOeCiSc6WzU3OCw2MzcsNDg1LDU1NCwzODgsNjcxXSwNCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLTIj6auY54KJJzpbMjk1LDE0MSwxMDksMjY1LDQxOSwzMTJdLA0KICAgICAgICAgICfngrzpk4HliIbljoItMyPpq5jngoknOlsxNDQ1LDIxNDMsMTYzMywxMjQsMCwxMjddLA0KICAgICAgICAgICfkuIDngrzpkqInOlsxMTYsMzg4LDUwLDUwLDk2LDE2NF0sDQogICAgICAgICAgJ+S6jOeCvOmSoic6WzEyOTI3LDE2NDQzLDE3NjM4LDE3MDcxLDE2ODQzLDE2MzUxXSwNCiAgICAgICAgICAn5LiA6L2n6ZKiJzpbMCwwLDAsMCwwLDBdLA0KICAgICAgICAgICfkuozovact5aSn5qOSJzpbMjA5LDIyMiwyNTMsMTMzLDE5OCwxMTZdLA0KICAgICAgICAgICfkuozovact5bCP5qOSJzpbMTkzLDIwNCwyMzMsMTIzLDE4MiwxMDhdLA0KICAgICAgICAgICfnibnmnb/ngrzpkqInOlsyMjk2OCwyMjMzNiwxODgxOSwyMTY0NywyMTYwNCwyMTcwOF0sDQogICAgICAgICAgJzM1MDDkuK3mnb8nOlswLDAsMCwwLDAsMF0sDQogICAgICAgICAgJzQzMDDljprmnb8nOlswLDAsMCwwLDAsMF0sDQogICAgICAgICAgJ+mrmOe6v+WIhuWOgic6WzAsMCwwLDAsMCwwXSwNCiAgICAgICAgICAn5qOS5p2Q5rex5Yqg5belJzpbMCwwLDAsMCwwLDBdLA0KICAgICAgICAgICfnur/mnZDmt7HliqDlt6UnOlsyMjQwLDE2MTYsMTM2NCwxMTIxLDEwODIsOTIwXSwNCiAgICAgICAgICAn54Ot55S15YiG5Y6CLeeDreeUtSc6Wzg1MTk5LDkwNTgzLDgzMDc1LDkzMTA4LDg5NjE1LDk1MTU2XSwNCiAgICAgICAgICAn54Ot55S15YiG5Y6CLem8k+mjjic6WzAsMCwwLDAsMCwwXSwNCiAgICAgICAgICAn54Ot55S15YiG5Y6CLeeDreawtCc6WzAsMCwwLDAsMCwwXSwNCiAgICAgICAgICAn54Wk5rCU5YiG5Y6CJzpbMCwwLDAsMCwwLDBdLA0KICAgICAgICAgICfliLbmsKfliIbljoInOls0MDAsNDAwLDQwMCw0MDAsNDAwLDQwMF0sDQogICAgICAgICAgJ+WQjuWLpOmDqCc6WzAsMCwwLDAsMCwwXSwNCiAgICAgICAgICAn5aSW6ZSA6JK45rG9JzpbMzI1NjgsMTc1MzQsMjczMzQsMjQzMTEsMjExMjYsMTk1MDRdLA0KICAgICAgICAgICfnu7zlkIjliKnnlKgnOlsyMTYsMzEsMTYsMTksMzIsNDZdLA0KICAgICAgICAgICfmjZ/ogJcnOlsxMjcwLDExNjYsMTA4OCw5MDcsODM4LDgyNV0sDQogICAgICAgICAgJ+WQiOiuoSc6WzE2Mjk5MiwxNTYyMjMsMTU0MjYyLDE2MTQ0OCwxNTQyNDUsMTU4MDcxXQ0KICAgICAgICAgIC8vICfngrzpk4HliIbljoInOiBbMzAsIDI4LCAzMSwgMzJdLA0KICAgICAgICAgIC8vICfng6fnu5PliIbljoInOiBbMTUsIDEzLCAxNiwgMTddLA0KICAgICAgICAgIC8vICfkuIDngrzpkqInOiBbMzUsIDMzLCAzNiwgMzddLA0KICAgICAgICAgIC8vICfkuozngrzpkqInOiBbMzAsIDI4LCAzMSwgMzJdLA0KICAgICAgICAgIC8vICfnu7zlkIjliKnnlKgnOiBbMTAsIDksIDExLCAxMC41XSwNCiAgICAgICAgICAvLyAn5LiA6L2n6ZKiJzogWzEzLCAxMiwgMTQsIDEzLjVdLA0KICAgICAgICAgIC8vICfkuozovafpkqInOiBbMTIsIDExLCAxMywgMTIuNV0sDQogICAgICAgICAgLy8gJ+S4iei9p+mSoic6IFsxMSwgMTAsIDEyLCAxMS41XSwNCiAgICAgICAgICAvLyAn54m55q6K6ZKi6L2n5p2QJzogWzEwLCA5LCAxMSwgMTAuNV0sDQogICAgICAgICAgLy8gJ+ajkuadkOi9p+WItuWOgic6IFs5LCA4LCAxMCwgOS41XSwNCiAgICAgICAgICAvLyAn5qOS57q/5p2Q5rex5Yqg5belKOajkiknOiBbNSwgNCwgNiwgNS41XSwNCiAgICAgICAgICAvLyAn5qOS57q/5p2Q5rex5Yqg5belKOe6vyknOiBbNCwgMywgNSwgNC41XSwNCiAgICAgICAgICAvLyAn57q/5p2Q5rex5Yqg5belJzogWzMsIDIsIDQsIDMuNV0sDQogICAgICAgICAgLy8gJ+mprOenkeaJmOmSoueQgyc6IFsxNSwgMTQuNSwgMTUuNSwgMTUuMl0sDQogICAgICAgICAgLy8gJ+eJuemSoueCvOmSouWIhuWOgic6IFsxMywgMTIsIDE0LCAxMy41XSwNCiAgICAgICAgICAvLyAn5Lit5p2/5YiG5Y6CJzogWzEyLCAxMSwgMTMsIDEyLjVdLA0KICAgICAgICAgIC8vICfljprmnb/liIbljoInOiBbMTEsIDEwLCAxMiwgMTEuNV0sDQogICAgICAgICAgLy8gJ+mSouadkOa3seWKoOW3pSc6IFsxMCwgOSwgMTEsIDEwLjVdLA0KICAgICAgICAgIC8vICfng63nlLUnOiBbNSwgNCwgNiwgNS41XSwNCiAgICAgICAgICAvLyAn5L6b55S15bel5Yy6JzogWzMsIDIsIDQsIDMuNV0sDQogICAgICAgICAgLy8gJ+awtOWkhOeQhuWIhuWOgic6IFsyLCAxLCAzLCAyLjVdLA0KICAgICAgICAgIC8vICfliLbmsKfliIbljoInOiBbMS41LCAwLjUsIDIuNSwgMl0sDQogICAgICAgICAgLy8gJ+eFpOawlOWIhuWOgic6IFsxLCAwLjgsIDEuMiwgMS4xXSwNCiAgICAgICAgICAvLyAn5YKo6L+Q5YWs5Y+4JzogWzgsIDcuOCwgOC4yLCA4LjFdLA0KICAgICAgICAgIC8vICfmo4Dkv67liIbljoInOiBbNiwgNS44LCA2LjIsIDYuMV0NCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIA0KICAgICAgLy8g5YWz6ZSu6IO95rqQ5oyH5qCH5b2T5pel5YC85LiO5pio5pel5a+55q+UDQogICAgICBrZXlFbmVyZ3lJbmRpY2F0b3JzOiBbXSwNCiAgICAgIC8vIFsNCiAgICAgIC8vICAgew0KICAgICAgLy8gICAgIG5hbWU6ICfmsKfmsJQnLA0KICAgICAgLy8gICAgIHRvZGF5OiA0Ni44MCwgLy8g6LaF5Ye655uu5qCH6IyD5Zu05LiK6ZmQDQogICAgICAvLyAgICAgeWVzdGVyZGF5OiA0NC42NCwNCiAgICAgIC8vICAgICB1bml0OiAnbcKzL3QnLA0KICAgICAgLy8gICAgIHRhcmdldE1pbjogNDIuMDAsDQogICAgICAvLyAgICAgdGFyZ2V0TWF4OiA0NS4wMCwNCiAgICAgIC8vICAgICBjaGFuZ2U6IDQuODQsDQogICAgICAvLyAgICAgc3RhdHVzOiAnZGFuZ2VyJw0KICAgICAgLy8gICB9LA0KICAgICAgLy8gICB7DQogICAgICAvLyAgICAgbmFtZTogJ+awruawlCcsDQogICAgICAvLyAgICAgdG9kYXk6IDE0LjgyLA0KICAgICAgLy8gICAgIHllc3RlcmRheTogMTUuMjEsDQogICAgICAvLyAgICAgdW5pdDogJ23Csy90JywNCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDE0LjAwLA0KICAgICAgLy8gICAgIHRhcmdldE1heDogMTYuMDAsDQogICAgICAvLyAgICAgY2hhbmdlOiAtMi41NiwNCiAgICAgIC8vICAgICBzdGF0dXM6ICdnb29kJw0KICAgICAgLy8gICB9LA0KICAgICAgLy8gICB7DQogICAgICAvLyAgICAgbmFtZTogJ+awqeawlCcsDQogICAgICAvLyAgICAgdG9kYXk6IDAuODUsDQogICAgICAvLyAgICAgeWVzdGVyZGF5OiAwLjg4LA0KICAgICAgLy8gICAgIHVuaXQ6ICdtwrMvdCcsDQogICAgICAvLyAgICAgdGFyZ2V0TWluOiAwLjgwLA0KICAgICAgLy8gICAgIHRhcmdldE1heDogMC45MCwNCiAgICAgIC8vICAgICBjaGFuZ2U6IC0zLjQxLA0KICAgICAgLy8gICAgIHN0YXR1czogJ2dvb2QnDQogICAgICAvLyAgIH0sDQogICAgICAvLyAgIHsNCiAgICAgIC8vICAgICBuYW1lOiAn56m65rCUJywNCiAgICAgIC8vICAgICB0b2RheTogNDUwLCAvLyDkvY7kuo7nm67moIfojIPlm7TkuIvpmZANCiAgICAgIC8vICAgICB5ZXN0ZXJkYXk6IDQ4MSwNCiAgICAgIC8vICAgICB1bml0OiAnbcKzL2gnLA0KICAgICAgLy8gICAgIHRhcmdldE1pbjogNDYwLA0KICAgICAgLy8gICAgIHRhcmdldE1heDogNTAwLA0KICAgICAgLy8gICAgIGNoYW5nZTogLTYuNDQsDQogICAgICAvLyAgICAgc3RhdHVzOiAnZGFuZ2VyJw0KICAgICAgLy8gICB9LA0KICAgICAgLy8gICB7DQogICAgICAvLyAgICAgbmFtZTogJ+mrmOeCieeFpOawlCcsDQogICAgICAvLyAgICAgdG9kYXk6IDk4NS4zLA0KICAgICAgLy8gICAgIHllc3RlcmRheTogOTYyLjcsDQogICAgICAvLyAgICAgdW5pdDogJ23Csy90JywNCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDk1MC4wLA0KICAgICAgLy8gICAgIHRhcmdldE1heDogMTAwMC4wLA0KICAgICAgLy8gICAgIGNoYW5nZTogMi4zNSwNCiAgICAgIC8vICAgICBzdGF0dXM6ICdnb29kJw0KICAgICAgLy8gICB9LA0KICAgICAgLy8gICB7DQogICAgICAvLyAgICAgbmFtZTogJ+i9rOeCieeFpOawlCcsDQogICAgICAvLyAgICAgdG9kYXk6IDg1LjIsDQogICAgICAvLyAgICAgeWVzdGVyZGF5OiA4OC40LA0KICAgICAgLy8gICAgIHVuaXQ6ICdtwrMvdCcsDQogICAgICAvLyAgICAgdGFyZ2V0TWluOiA4MC4wLA0KICAgICAgLy8gICAgIHRhcmdldE1heDogOTAuMCwNCiAgICAgIC8vICAgICBjaGFuZ2U6IC0zLjYyLA0KICAgICAgLy8gICAgIHN0YXR1czogJ2dvb2QnDQogICAgICAvLyAgIH0sDQogICAgICAvLyAgIHsNCiAgICAgIC8vICAgICBuYW1lOiAn54Sm54KJ54Wk5rCUJywNCiAgICAgIC8vICAgICB0b2RheTogNDEuMywNCiAgICAgIC8vICAgICB5ZXN0ZXJkYXk6IDQyLjgsDQogICAgICAvLyAgICAgdW5pdDogJ23Csy90JywNCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDQwLjAsDQogICAgICAvLyAgICAgdGFyZ2V0TWF4OiA0NS4wLA0KICAgICAgLy8gICAgIGNoYW5nZTogLTMuNTAsDQogICAgICAvLyAgICAgc3RhdHVzOiAnZ29vZCcNCiAgICAgIC8vICAgfSwNCiAgICAgIC8vICAgew0KICAgICAgLy8gICAgIG5hbWU6ICfppbHlkozokrjmsb0nLA0KICAgICAgLy8gICAgIHRvZGF5OiAwLjUyLA0KICAgICAgLy8gICAgIHllc3RlcmRheTogMC41NCwNCiAgICAgIC8vICAgICB1bml0OiAndC90JywNCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDAuNTAsDQogICAgICAvLyAgICAgdGFyZ2V0TWF4OiAwLjU4LA0KICAgICAgLy8gICAgIGNoYW5nZTogLTMuNzAsDQogICAgICAvLyAgICAgc3RhdHVzOiAnZ29vZCcNCiAgICAgIC8vICAgfSwNCiAgICAgIC8vICAgew0KICAgICAgLy8gICAgIG5hbWU6ICfov4fng63okrjmsb0nLA0KICAgICAgLy8gICAgIHRvZGF5OiAwLjMzLA0KICAgICAgLy8gICAgIHllc3RlcmRheTogMC4zMSwNCiAgICAgIC8vICAgICB1bml0OiAndC90JywNCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDAuMzAsDQogICAgICAvLyAgICAgdGFyZ2V0TWF4OiAwLjM1LA0KICAgICAgLy8gICAgIGNoYW5nZTogNi40NSwNCiAgICAgIC8vICAgICBzdGF0dXM6ICd3YXJuaW5nJw0KICAgICAgLy8gICB9LA0KICAgICAgLy8gICB7DQogICAgICAvLyAgICAgbmFtZTogJ+S9juWOi+iSuOaxvScsDQogICAgICAvLyAgICAgdG9kYXk6IDAuMjEsDQogICAgICAvLyAgICAgeWVzdGVyZGF5OiAwLjIzLA0KICAgICAgLy8gICAgIHVuaXQ6ICd0L3QnLA0KICAgICAgLy8gICAgIHRhcmdldE1pbjogMC4yMCwNCiAgICAgIC8vICAgICB0YXJnZXRNYXg6IDAuMjUsDQogICAgICAvLyAgICAgY2hhbmdlOiAtOC43MCwNCiAgICAgIC8vICAgICBzdGF0dXM6ICdnb29kJw0KICAgICAgLy8gICB9LA0KICAgICAgLy8gICB7DQogICAgICAvLyAgICAgbmFtZTogJ+WkqeeEtuawlCcsDQogICAgICAvLyAgICAgdG9kYXk6IDI0LjMsDQogICAgICAvLyAgICAgeWVzdGVyZGF5OiAyNS4xLA0KICAgICAgLy8gICAgIHVuaXQ6ICdtwrMvdCcsDQogICAgICAvLyAgICAgdGFyZ2V0TWluOiAyMi4wLA0KICAgICAgLy8gICAgIHRhcmdldE1heDogMjYuMCwNCiAgICAgIC8vICAgICBjaGFuZ2U6IC0zLjE5LA0KICAgICAgLy8gICAgIHN0YXR1czogJ2dvb2QnDQogICAgICAvLyAgIH0sDQogICAgICAvLyAgIHsNCiAgICAgIC8vICAgICBuYW1lOiAn5Y6L57yp5aSp54S25rCUJywNCiAgICAgIC8vICAgICB0b2RheTogMi44NSwNCiAgICAgIC8vICAgICB5ZXN0ZXJkYXk6IDIuOTEsDQogICAgICAvLyAgICAgdW5pdDogJ23Csy90JywNCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDIuNzAsDQogICAgICAvLyAgICAgdGFyZ2V0TWF4OiAzLjAwLA0KICAgICAgLy8gICAgIGNoYW5nZTogLTIuMDYsDQogICAgICAvLyAgICAgc3RhdHVzOiAnZ29vZCcNCiAgICAgIC8vICAgfQ0KICAgICAgLy8gXSwNCiAgICAgIA0KICAgICAgLy8g6IO95rqQ5oyH5qCH5piv5ZCm6LaF5Ye655uu5qCH6IyD5Zu05qCH6K6wDQogICAgICBpc0VuZXJneUNoYXJ0V2FybmluZzogew0KICAgICAgICBlbGVjdHJpY2l0eTogZmFsc2UsDQogICAgICAgIHdhdGVyOiBmYWxzZSwNCiAgICAgICAgZ2FzOiBmYWxzZSwNCiAgICAgICAgc3RlYW06IGZhbHNlDQogICAgICB9LA0KICAgICAgDQogICAgICAvLyDog73mupDnm67moIfojIPlm7QNCiAgICAgIGVuZXJneVRhcmdldFJhbmdlczogew0KICAgICAgICBlbGVjdHJpY2l0eTogeyBtaW46IDgwMCwgbWF4OiA5MDAsIHVuaXQ6ICfljYPnk6bml7YnIH0sDQogICAgICAgIHdhdGVyOiB7IG1pbjogNTAsIG1heDogNjAsIHVuaXQ6ICflkKgnIH0sDQogICAgICAgIGdhczogeyBtaW46IDIwMCwgbWF4OiAyNDAsIHVuaXQ6ICfnq4vmlrnnsbMnIH0sDQogICAgICAgIHN0ZWFtOiB7IG1pbjogMjUsIG1heDogMzUsIHVuaXQ6ICflkKgnIH0NCiAgICAgIH0sDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOiuoeeul+aJgOaciemDqOmXqOeahOWujOaIkOeOh+aVsOaNru+8iOWPquaYvuekuuWIhuWOguWxgue6p++8iQ0KICAgIGRlcGFydG1lbnRDb21wbGV0aW9uUmF0ZXMoKSB7DQogICAgICAvLyDmjInpg6jpl6jliIbnu4TorqHnrpflrozmiJDnjofvvIzlj6rlpITnkIbmnIkiLSLnmoTliIbljoLlsYLnuqcNCiAgICAgIGNvbnN0IGRlcGFydG1lbnRSYXRlcyA9IHRoaXMuY29tcGxldGlvbkRhdGENCiAgICAgICAgLmZpbHRlcihkZXB0ID0+IGRlcHQuZGVwYXJ0bWVudC5pbmNsdWRlcygnLScpKSAvLyDlj6rpgInmi6nliIbljoLlsYLnuqcNCiAgICAgICAgLm1hcChkZXB0ID0+IHsNCiAgICAgICAgICBjb25zdCBpbmRpY2F0b3JzID0gZGVwdC5pbmRpY2F0b3JzDQogICAgICAgICAgbGV0IGNvbXBsZXRlZENvdW50ID0gMA0KICAgICAgICAgIGxldCB0b3RhbEluZGljYXRvcnMgPSBpbmRpY2F0b3JzLmxlbmd0aA0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOiuoeeul+W3suWujOaIkOaMh+agh+aVsOmHj++8jOS9v+eUqDTmnIjku73mlbDmja4NCiAgICAgICAgICBpbmRpY2F0b3JzLmZvckVhY2goaW5kaWNhdG9yID0+IHsNCiAgICAgICAgICAgIC8vIOS9v+eUqDTmnIjku73mlbDmja7vvIjnrKwz5Liq57Si5byV77yJDQogICAgICAgICAgICBjb25zdCBhY3R1YWwgPSBpbmRpY2F0b3IudmFsdWVzWzNdIC8vIOesrOWbm+WRqC805pyI5Lu955qE5a6e6ZmF5YC8DQogICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBpbmRpY2F0b3IudGFyZ2V0DQogICAgICAgICAgICANCiAgICAgICAgICAgIGNvbnN0IGlzQ29tcGxldGVkID0gaW5kaWNhdG9yLmlzSGlnaGVyQmV0dGVyIA0KICAgICAgICAgICAgICA/IGFjdHVhbCA+PSB0YXJnZXQgDQogICAgICAgICAgICAgIDogYWN0dWFsIDw9IHRhcmdldA0KICAgICAgICAgICAgDQogICAgICAgICAgICBpZiAoaXNDb21wbGV0ZWQpIGNvbXBsZXRlZENvdW50KysNCiAgICAgICAgICB9KQ0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOiuoeeul+WujOaIkOeOhw0KICAgICAgICAgIGNvbnN0IGNvbXBsZXRpb25SYXRlID0gKGNvbXBsZXRlZENvdW50IC8gdG90YWxJbmRpY2F0b3JzKSAqIDEwMA0KICAgICAgICAgIA0KICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICBkZXBhcnRtZW50OiBkZXB0LmRlcGFydG1lbnQuc3BsaXQoJy0nKVsxXSwgLy8g5Y+q5pi+56S65YiG5Y6C5ZCN56ewDQogICAgICAgICAgICBmdWxsRGVwYXJ0bWVudDogZGVwdC5kZXBhcnRtZW50LCAvLyDkv53lrZjlrozmlbTpg6jpl6jlkI3np7DnlKjkuo7mlbDmja7mn6Xor6INCiAgICAgICAgICAgIGNvbXBsZXRpb25SYXRlOiBwYXJzZUZsb2F0KGNvbXBsZXRpb25SYXRlLnRvRml4ZWQoMSkpLA0KICAgICAgICAgICAgdG90YWxJbmRpY2F0b3JzOiB0b3RhbEluZGljYXRvcnMsDQogICAgICAgICAgICBjb21wbGV0ZWRJbmRpY2F0b3JzOiBjb21wbGV0ZWRDb3VudA0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIA0KICAgICAgLy8g5oyJ5a6M5oiQ546H5LuO6auY5Yiw5L2O5o6S5bqPDQogICAgICByZXR1cm4gZGVwYXJ0bWVudFJhdGVzLnNvcnQoKGEsIGIpID0+IGIuY29tcGxldGlvblJhdGUgLSBhLmNvbXBsZXRpb25SYXRlKQ0KICAgIH0sDQogICAgDQogICAgLy8g6K6h566X5omA5pyJ5oyH5qCH55qE5a6M5oiQ546H77yINOaciOS7veaVsOaNru+8iQ0KICAgIGFsbEluZGljYXRvckNvbXBsZXRpb25SYXRlcygpIHsNCiAgICAgIGNvbnN0IGFsbEluZGljYXRvcnMgPSBbXQ0KICAgICAgDQogICAgICB0aGlzLmNvbXBsZXRpb25EYXRhLmZvckVhY2goZGVwdCA9PiB7DQogICAgICAgIGlmIChkZXB0LmRlcGFydG1lbnQuaW5jbHVkZXMoJy0nKSkgeyAvLyDlj6rpgInmi6nliIbljoLlsYLnuqcNCiAgICAgICAgICBjb25zdCBkZXBhcnRtZW50TmFtZSA9IGRlcHQuZGVwYXJ0bWVudC5zcGxpdCgnLScpWzFdDQogICAgICAgICAgDQogICAgICAgICAgZGVwdC5pbmRpY2F0b3JzLmZvckVhY2goaW5kaWNhdG9yID0+IHsNCiAgICAgICAgICAgIC8vIOebtOaOpeS9v+eUqDA05pyI5YiX55qE5a6M5oiQ546H5pWw5o2uDQogICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmnIDlkI7kuIDliJfvvIgwNOaciO+8ieeahOWujOaIkOeOh+aVsOaNrg0KICAgICAgICAgICAgY29uc3QgY29tcGxldGlvblJhdGVTdHIgPSBpbmRpY2F0b3IuY29tcGxldGlvblJhdGVzICYmIGluZGljYXRvci5jb21wbGV0aW9uUmF0ZXNbM107IC8vIDA05pyI5a6M5oiQ546HDQogICAgICAgICAgICBsZXQgY29tcGxldGlvblJhdGUgPSAwOw0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDlpoLmnpzmnInnm7TmjqXnmoTlrozmiJDnjofmlbDmja7vvIzliJnkvb/nlKjlroMNCiAgICAgICAgICAgIGlmIChjb21wbGV0aW9uUmF0ZVN0cikgew0KICAgICAgICAgICAgICAvLyDlsIbnmb7liIbmr5TlrZfnrKbkuLLovazmjaLkuLrmlbDlgLzvvIzkvovlpoIgIi0xLjMxJSIgLT4gLTEuMzENCiAgICAgICAgICAgICAgY29tcGxldGlvblJhdGUgPSBwYXJzZUZsb2F0KGNvbXBsZXRpb25SYXRlU3RyLnJlcGxhY2UoJyUnLCAnJykpOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgLy8g5aaC5p6c5rKh5pyJ55u05o6l5pWw5o2u77yM5YiZ55So5Y6f5p2l55qE5pa55rOV6K6h566XDQogICAgICAgICAgICAgIGNvbnN0IGFjdHVhbCA9IGluZGljYXRvci52YWx1ZXNbM107IC8vIDTmnIjku73lrp7pmYXlgLwNCiAgICAgICAgICAgICAgY29uc3QgdGFyZ2V0ID0gaW5kaWNhdG9yLnRhcmdldDsNCiAgICAgICAgICAgICAgDQogICAgICAgICAgICAgIGlmIChpbmRpY2F0b3IuaXNIaWdoZXJCZXR0ZXIpIHsNCiAgICAgICAgICAgICAgICAvLyDlpoLmnpzmjIfmoIfotorpq5jotorlpb3vvIzlrozmiJDnjocgPSDlrp7pmYXlgLwv55uu5qCH5YC8ICogMTAwJSAtIDEwMCUNCiAgICAgICAgICAgICAgICBjb21wbGV0aW9uUmF0ZSA9ICgoYWN0dWFsIC8gdGFyZ2V0KSAqIDEwMCkgLSAxMDA7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgLy8g5aaC5p6c5oyH5qCH6LaK5L2O6LaK5aW977yM5a6M5oiQ546HID0g55uu5qCH5YC8L+WunumZheWAvCAqIDEwMCUgLSAxMDAlDQogICAgICAgICAgICAgICAgY29tcGxldGlvblJhdGUgPSAoKHRhcmdldCAvIGFjdHVhbCkgKiAxMDApIC0gMTAwOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOWvueWujOaIkOeOh+i/m+ihjOWkhOeQhg0KICAgICAgICAgICAgLy8g6Z2e5pWw5a2X5a6M5oiQ546H6LCD5pW05Li6MA0KICAgICAgICAgICAgaWYgKGlzTmFOKGNvbXBsZXRpb25SYXRlKSB8fCAhaXNGaW5pdGUoY29tcGxldGlvblJhdGUpKSB7DQogICAgICAgICAgICAgIGNvbXBsZXRpb25SYXRlID0gMDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g6ZmQ5Yi25pyA5aSn5a6M5oiQ546H57ud5a+55YC85Li6MjAwJQ0KICAgICAgICAgICAgaWYgKE1hdGguYWJzKGNvbXBsZXRpb25SYXRlKSA+IDIwMCkgew0KICAgICAgICAgICAgICBjb21wbGV0aW9uUmF0ZSA9IGNvbXBsZXRpb25SYXRlID4gMCA/IDIwMCA6IC0yMDA7DQogICAgICAgICAgICB9DQogICAgICAgICAgICANCiAgICAgICAgICAgIGFsbEluZGljYXRvcnMucHVzaCh7DQogICAgICAgICAgICAgIGRlcGFydG1lbnQ6IGRlcGFydG1lbnROYW1lLA0KICAgICAgICAgICAgICBpbmRpY2F0b3I6IGluZGljYXRvci5uYW1lLA0KICAgICAgICAgICAgICBjb21wbGV0aW9uUmF0ZTogcGFyc2VGbG9hdChjb21wbGV0aW9uUmF0ZS50b0ZpeGVkKDEpKSwNCiAgICAgICAgICAgICAgdGFyZ2V0OiBpbmRpY2F0b3IudGFyZ2V0LA0KICAgICAgICAgICAgICBhY3R1YWw6IGluZGljYXRvci52YWx1ZXNbM10sIC8vIDTmnIjku73lrp7pmYXlgLwNCiAgICAgICAgICAgICAgdW5pdDogaW5kaWNhdG9yLnVuaXQsDQogICAgICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBpbmRpY2F0b3IuaXNIaWdoZXJCZXR0ZXINCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIA0KICAgICAgLy8g5oyJ5a6M5oiQ546H5LuO6auY5Yiw5L2O5o6S5bqPDQogICAgICByZXR1cm4gYWxsSW5kaWNhdG9ycy5zb3J0KChhLCBiKSA9PiBiLmNvbXBsZXRpb25SYXRlIC0gYS5jb21wbGV0aW9uUmF0ZSk7DQogICAgfSwNCiAgICANCiAgICAvLyDnm7TmjqXkvb/nlKjmiYDmnInmjIfmoIflrozmiJDnjofmlbDmja4NCiAgICBwYWdpbmF0ZWRJbmRpY2F0b3JSYXRlcygpIHsNCiAgICAgIHJldHVybiB0aGlzLmFsbEluZGljYXRvckNvbXBsZXRpb25SYXRlcw0KICAgIH0sDQogICAgDQogICAgLy8g6I635Y+W5LqL5Lia6YOo5YiX6KGo77yI55So5LqO562b6YCJ77yJDQogICAgYnVzaW5lc3NVbml0cygpIHsNCiAgICAgIGNvbnN0IHVuaXRzID0gbmV3IFNldCgpDQogICAgICB0aGlzLmNvbXBsZXRpb25EYXRhLmZvckVhY2goZGVwdCA9PiB7DQogICAgICAgIGlmIChkZXB0LmRlcGFydG1lbnQuaW5jbHVkZXMoJy0nKSkgew0KICAgICAgICAgIHVuaXRzLmFkZChkZXB0LmRlcGFydG1lbnQuc3BsaXQoJy0nKVswXSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB1bml0cy5hZGQoZGVwdC5kZXBhcnRtZW50KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgcmV0dXJuIEFycmF5LmZyb20odW5pdHMpDQogICAgfSwNCiAgICANCiAgICAvLyDojrflj5blvZPliY3pgInmi6nnmoTkuovkuJrpg6jkuIvnmoTliIbljoLliJfooagNCiAgICBjdXJyZW50QnVzaW5lc3NVbml0RGVwYXJ0bWVudHMoKSB7DQogICAgICBpZiAoIXRoaXMuY3VycmVudEJ1c2luZXNzVW5pdCkgcmV0dXJuIFtdDQogICAgICANCiAgICAgIHJldHVybiB0aGlzLmNvbXBsZXRpb25EYXRhDQogICAgICAgIC5maWx0ZXIoZGVwdCA9PiBkZXB0LmRlcGFydG1lbnQuc3RhcnRzV2l0aCh0aGlzLmN1cnJlbnRCdXNpbmVzc1VuaXQgKyAnLScpKQ0KICAgICAgICAubWFwKGRlcHQgPT4gKHsNCiAgICAgICAgICB2YWx1ZTogZGVwdC5kZXBhcnRtZW50LA0KICAgICAgICAgIGxhYmVsOiBkZXB0LmRlcGFydG1lbnQuc3BsaXQoJy0nKVsxXQ0KICAgICAgICB9KSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOiOt+WPluW9k+WJjemDqOmXqOeahOaMh+agh+WIl+ihqA0KICAgIGN1cnJlbnREZXBhcnRtZW50SW5kaWNhdG9ycygpIHsNCiAgICAgIGNvbnN0IGRlcHREYXRhID0gdGhpcy5jb21wbGV0aW9uRGF0YS5maW5kKGRlcHQgPT4gZGVwdC5kZXBhcnRtZW50ID09PSB0aGlzLmN1cnJlbnREZXBhcnRtZW50KQ0KICAgICAgcmV0dXJuIGRlcHREYXRhID8gZGVwdERhdGEuaW5kaWNhdG9ycyA6IFtdDQogICAgfSwNCiAgICANCiAgICAvLyDojrflj5bmiYDmnInliIbljoLliJfooajvvIjnlKjkuo7og73mupDmtojogJfor6bmg4XvvIkNCiAgICBhbGxGYWN0b3JpZXMoKSB7DQogICAgICByZXR1cm4gdGhpcy5jb21wbGV0aW9uRGF0YTEubWFwKGRlcHQgPT4gZGVwdC5kZXBhcnRtZW50KQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAvLyAvLyDnm5HlkKzkuovkuJrpg6jlj5jljJYNCiAgICAvLyBjdXJyZW50QnVzaW5lc3NVbml0KG5ld1ZhbCkgew0KICAgIC8vICAgaWYgKHRoaXMuY3VycmVudEJ1c2luZXNzVW5pdERlcGFydG1lbnRzLmxlbmd0aCA+IDApIHsNCiAgICAvLyAgICAgdGhpcy5jdXJyZW50RGVwYXJ0bWVudCA9IHRoaXMuY3VycmVudEJ1c2luZXNzVW5pdERlcGFydG1lbnRzWzBdLnZhbHVlDQogICAgLy8gICB9DQogICAgICANCiAgICAvLyAgIC8vIOWQr+WKqOiHquWKqOWIh+aNog0KICAgIC8vICAgdGhpcy5zdGFydEF1dG9Td2l0Y2hXaXRoaW5CdXNpbmVzc1VuaXQoKQ0KICAgIC8vIH0sDQogICAgDQogICAgLy8gLy8g55uR5ZCs6YOo6Zeo5Y+Y5YyW77yM6Ieq5Yqo6YCJ5oup56ys5LiA5Liq5oyH5qCHDQogICAgLy8gY3VycmVudERlcGFydG1lbnQobmV3VmFsKSB7DQogICAgLy8gICBpZiAodGhpcy5jdXJyZW50RGVwYXJ0bWVudEluZGljYXRvcnMubGVuZ3RoID4gMCkgew0KICAgIC8vICAgICB0aGlzLmN1cnJlbnRJbmRpY2F0b3IgPSB0aGlzLmN1cnJlbnREZXBhcnRtZW50SW5kaWNhdG9yc1swXS5uYW1lDQogICAgLy8gICB9DQogICAgLy8gICB0aGlzLmluaXRNb250aGx5VHJlbmRDaGFydCgpDQogICAgLy8gfSwNCiAgICANCiAgICAvLyAvLyDnm5HlkKzmjIfmoIflj5jljJbvvIzmm7TmlrDlm77ooagNCiAgICAvLyBjdXJyZW50SW5kaWNhdG9yKCkgew0KICAgIC8vICAgdGhpcy5pbml0TW9udGhseVRyZW5kQ2hhcnQoKQ0KICAgIC8vIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDliJ3lp4vljJblm77ooagNCiAgICB0aGlzLmluaXRDaGFydHMoKQ0KICAgIA0KICAgIC8vIOiuoeeul+acquWujOaIkOaMh+agh+aVsOaNrg0KICAgIHRoaXMuY2FsY3VsYXRlSW5jb21wbGV0ZURhdGEoKQ0KICAgIA0KICAgIC8vIOehruS/nWN1cnJlbnRFbmVyZ3lEZXB05pyJ5LiA5Liq5pyJ5pWI55qE5Yid5aeL5YC8DQogICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgdGhpcy5pbml0RW5lcmd5RGV0YWlsQ2hhcnRzKCkNCiAgICAgIGlmICh0aGlzLmFsbEZhY3RvcmllcyAmJiB0aGlzLmFsbEZhY3Rvcmllcy5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuY3VycmVudEVuZXJneURlcHQgPSB0aGlzLmFsbEZhY3Rvcmllc1swXQ0KICAgICAgICAvLyDph43mlrDliJ3lp4vljJbog73mupDor6bmg4Xlm77ooagNCiAgICAgICAgdGhpcy5pbml0RW5lcmd5RGV0YWlsQ2hhcnRzKCkNCiAgICAgIH0NCiAgICB9KQ0KICAgIHRoaXMubG9hZEV4Y2VsRnJvbVJlbW90ZSgpOw0KICAgIC8vIOWQr+WKqOa7muWKqOihqOagvA0KICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgIC8vIOWkjeWItuihqOagvOWGheWuueS7peWunueOsOaXoOe8nea7muWKqA0KICAgICAgY29uc3QgdGFibGUgPSB0aGlzLiRyZWZzLmluY29tcGxldGVUYWJsZQ0KICAgICAgaWYgKHRhYmxlICYmIHRoaXMuaW5jb21wbGV0ZURhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAvLyDojrflj5booajmoLzlhoXlrrnpg6jliIbvvIjkuI3ljIXlkKvooajlpLTvvIkNCiAgICAgICAgY29uc3QgdGFibGVCb2R5ID0gdGFibGUucXVlcnlTZWxlY3RvcignLmVsLXRhYmxlX19ib2R5LXdyYXBwZXInKQ0KICAgICAgICBpZiAodGFibGVCb2R5KSB7DQogICAgICAgICAgY29uc3Qgb3JpZ2luYWxDb250ZW50ID0gdGFibGVCb2R5LmlubmVySFRNTA0KICAgICAgICAgIC8vIOWcqOihqOagvOWGheWuueWQjumdoua3u+WKoOS4gOS7veebuOWQjOeahOWGheWuue+8jOiAjOS4jeaYr+aVtOS4quihqOagvA0KICAgICAgICAgIHRhYmxlQm9keS5pbm5lckhUTUwgKz0gb3JpZ2luYWxDb250ZW50DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuc3RhcnRUYWJsZVNjcm9sbCgpDQogICAgfSkNCiAgICANCiAgICAvLyDlkK/liqjkuovkuJrpg6jlhoXpg6joh6rliqjliIfmjaINCiAgICB0aGlzLnN0YXJ0QXV0b1N3aXRjaFdpdGhpbkJ1c2luZXNzVW5pdCgpDQogICAgDQogICAgLy8g5ZCv5Yqo5LqL5Lia6YOo5YiH5o2i77yI5q+PMzDnp5LliIfmjaLkuIDmrKHvvIkNCiAgICB0aGlzLnN0YXJ0QnVzaW5lc3NVbml0U3dpdGNoKCkNCiAgICANCiAgICAvLyDlkK/liqjmjIfmoIfljaHniYfoh6rliqjmu5rliqgNCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICB0aGlzLmluaXRJbmRpY2F0b3JDYXJkc1Njcm9sbCgpDQogICAgICANCiAgICAgIC8vIOS4uuaMh+agh+WNoeeJh+WuueWZqOa3u+WKoOm8oOagh+aCrOWBnOS6i+S7tuWkhOeQhg0KICAgICAgY29uc3QgY2FyZHNDb250YWluZXIgPSB0aGlzLiRyZWZzLmluZGljYXRvckNhcmRzQ29udGFpbmVyDQogICAgICBpZiAoY2FyZHNDb250YWluZXIpIHsNCiAgICAgICAgY2FyZHNDb250YWluZXIuYWRkRXZlbnRMaXN0ZW5lcignbW91c2VvdmVyJywgKCkgPT4gew0KICAgICAgICAgIHRoaXMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxQYXVzZWQgPSB0cnVlDQogICAgICAgIH0pDQogICAgICAgIA0KICAgICAgICBjYXJkc0NvbnRhaW5lci5hZGRFdmVudExpc3RlbmVyKCdtb3VzZW91dCcsICgpID0+IHsNCiAgICAgICAgICB0aGlzLmluZGljYXRvckNhcmRzU2Nyb2xsUGF1c2VkID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9KQ0KICAgIA0KICAgIC8vIOebkeWQrOeql+WPo+Wkp+Wwj+WPmOWMlu+8jOmHjeaWsOa4suafk+WbvuihqA0KICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLnJlc2l6ZUNoYXJ0cykNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDmuIXpmaTlrprml7blmagNCiAgICB0aGlzLnN0b3BBdXRvU3dpdGNoKCkNCiAgICANCiAgICBpZiAodGhpcy5zY3JvbGxUaW1lcikgew0KICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnNjcm9sbFRpbWVyKQ0KICAgICAgDQogICAgICAvLyDnp7vpmaTooajmoLzmu5rliqjnmoTkuovku7bnm5HlkKzlmagNCiAgICAgIGNvbnN0IHRhYmxlID0gdGhpcy4kcmVmcy5pbmNvbXBsZXRlVGFibGUNCiAgICAgIGlmICh0YWJsZSkgew0KICAgICAgICBjb25zdCB0YWJsZUJvZHkgPSB0YWJsZS5xdWVyeVNlbGVjdG9yKCcuZWwtdGFibGVfX2JvZHktd3JhcHBlcicpDQogICAgICAgIGlmICh0YWJsZUJvZHkgJiYgdGhpcy50YWJsZU1vdXNlRW50ZXJIYW5kbGVyICYmIHRoaXMudGFibGVNb3VzZUxlYXZlSGFuZGxlcikgew0KICAgICAgICAgIHRhYmxlQm9keS5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZWVudGVyJywgdGhpcy50YWJsZU1vdXNlRW50ZXJIYW5kbGVyKTsNCiAgICAgICAgICB0YWJsZUJvZHkucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2VsZWF2ZScsIHRoaXMudGFibGVNb3VzZUxlYXZlSGFuZGxlcik7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogICAgDQogICAgaWYgKHRoaXMuYnVzaW5lc3NVbml0VGltZXIpIHsNCiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5idXNpbmVzc1VuaXRUaW1lcikNCiAgICB9DQogICAgDQogICAgaWYgKHRoaXMuZW5lcmd5RGVwdFRpbWVyKSB7DQogICAgICBjbGVhckludGVydmFsKHRoaXMuZW5lcmd5RGVwdFRpbWVyKQ0KICAgIH0NCiAgICANCiAgICBpZiAodGhpcy5jb21wbGV0aW9uQ2hhcnRUaW1lcikgew0KICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmNvbXBsZXRpb25DaGFydFRpbWVyKQ0KICAgIH0NCiAgICANCiAgICBpZiAodGhpcy5pbmRpY2F0b3JDYXJkc1Njcm9sbFRpbWVyKSB7DQogICAgICBjbGVhckludGVydmFsKHRoaXMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxUaW1lcikNCiAgICB9DQogICAgDQogICAgLy8g56e76Zmk56qX5Y+j5aSn5bCP5Y+Y5YyW55uR5ZCsDQogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQ2hhcnRzKQ0KICAgIA0KICAgIC8vIOenu+mZpOaMh+agh+WNoeeJh+WuueWZqOeahOS6i+S7tuebkeWQrOWZqA0KICAgIGNvbnN0IGNhcmRzQ29udGFpbmVyID0gdGhpcy4kcmVmcy5pbmRpY2F0b3JDYXJkc0NvbnRhaW5lcg0KICAgIGlmIChjYXJkc0NvbnRhaW5lcikgew0KICAgICAgY2FyZHNDb250YWluZXIucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2VvdmVyJywgKCkgPT4gew0KICAgICAgICB0aGlzLmluZGljYXRvckNhcmRzU2Nyb2xsUGF1c2VkID0gdHJ1ZQ0KICAgICAgfSkNCiAgICAgIA0KICAgICAgY2FyZHNDb250YWluZXIucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2VvdXQnLCAoKSA9PiB7DQogICAgICAgIHRoaXMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxQYXVzZWQgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9DQogICAgDQogICAgLy8g6ZSA5q+B5Zu+6KGo5a6e5L6LDQogICAgdGhpcy5kaXNwb3NlQ2hhcnRzKCkNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICAgIHRoaXMuZ2V0RGF0ZVVwZGF0ZUxpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5Yid5aeL5YyW5omA5pyJ5Zu+6KGoDQoNCiAgICBsb2FkRXhjZWxGcm9tUmVtb3RlKCkgew0KICAgICAgdGhpcy5leGNlbExvYWRpbmcgPSB0cnVlOw0KICAgICAgY29uc3QgdXJsID0NCiAgICAgICAgImh0dHBzOi8veWR4dC5jaXRpY3N0ZWVsLmNvbTo4MDk5L21pbmlvL3hjdGcvdGVtcC9qbeWFs+mUruaKgOW+hOaMh+aghy54bHN4IjsNCiAgICAgIGF4aW9zKHsNCiAgICAgICAgbWV0aG9kOiAiZ2V0IiwNCiAgICAgICAgdXJsLA0KICAgICAgICByZXNwb25zZVR5cGU6ICJhcnJheWJ1ZmZlciIsDQogICAgICB9KQ0KICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gbmV3IFVpbnQ4QXJyYXkocmVzcG9uc2UuZGF0YSk7DQogICAgICAgICAgY29uc3Qgd29ya2Jvb2sgPSBYTFNYLnJlYWQoZGF0YSwgeyB0eXBlOiAiYXJyYXkiIH0pOw0KICAgICAgICAgIGNvbnN0IGZpcnN0U2hlZXQgPSB3b3JrYm9vay5TaGVldHNbd29ya2Jvb2suU2hlZXROYW1lc1swXV07DQogICAgICAgICAgY29uc3QganNvbkRhdGEgPSBYTFNYLnV0aWxzLnNoZWV0X3RvX2pzb24oZmlyc3RTaGVldCwgew0KICAgICAgICAgICAgaGVhZGVyOiAxLA0KICAgICAgICAgICAgcmFuZ2U6IDEsDQogICAgICAgICAgfSk7DQogICAgICAgICAgY29uc29sZS5sb2coanNvbkRhdGEpOw0KICAgICAgICAgIHRoaXMucHJvY2Vzc0V4Y2VsRGF0YShqc29uRGF0YSk7DQogICAgICAgICAgdGhpcy5leGNlbExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuWKoOi9vUV4Y2Vs5paH5Lu25aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliqDovb1FeGNlbOaWh+S7tuWksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICAgIHRoaXMuZXhjZWxMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0RGV0YWlsKCl7DQogICAgICBkaW1lbnNpb25hbGl0eWxpc3RQZXJtaXNzaW9uTGlzdCgpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5hZG1pblNob3cgPSByZXMubXNnOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXREYXRlVXBkYXRlTGlzdCgpew0KICAgICAgZGF0ZVVwZGF0ZUxpc3QoKS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMua2V5RW5lcmd5SW5kaWNhdG9ycyA9IHJlcy5kYXRhOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIA0KICAgIHByb2Nlc3NFeGNlbERhdGEoanNvbkRhdGEpIHsNCiAgICAgIGlmICghanNvbkRhdGEgfHwganNvbkRhdGEubGVuZ3RoIDwgMikgcmV0dXJuOw0KICAgICAgY29uc3QgaGVhZGVycyA9IGpzb25EYXRhWzBdOw0KICAgICAgY29uc3QgY29sSW5kZXhlcyA9IHsNCiAgICAgICAgZmFjdG9yeTogaGVhZGVycy5pbmRleE9mKCLliIbljoIiKSwNCiAgICAgICAgbmFtZTogaGVhZGVycy5pbmRleE9mKCLmjIfmoIflkI3np7AiKSwNCiAgICAgICAgdGFyZ2V0OiBoZWFkZXJzLmluZGV4T2YoIuebruaghyIpLA0KICAgICAgICB1bml0OiBoZWFkZXJzLmluZGV4T2YoIuWNleS9jSIpLA0KICAgICAgICBqYW46IGhlYWRlcnMuaW5kZXhPZigiMDHmnIjlrp7nu6kiKSwNCiAgICAgICAgamFuU3RhdHVzOiBoZWFkZXJzLmluZGV4T2YoIjAx5pyI5a6e57upIikgKyAxLA0KICAgICAgICBmZWI6IGhlYWRlcnMuaW5kZXhPZigiMDLmnIjlrp7nu6kiKSwNCiAgICAgICAgZmViU3RhdHVzOiBoZWFkZXJzLmluZGV4T2YoIjAy5pyI5a6e57upIikgKyAxLA0KICAgICAgICBtYXI6IGhlYWRlcnMuaW5kZXhPZigiMDPmnIjlrp7nu6kiKSwNCiAgICAgICAgbWFyU3RhdHVzOiBoZWFkZXJzLmluZGV4T2YoIjAz5pyI5a6e57upIikgKyAxLA0KICAgICAgICBhcHI6IGhlYWRlcnMuaW5kZXhPZigiMDTmnIjlrp7nu6kiKSwNCiAgICAgICAgYXByU3RhdHVzOiBoZWFkZXJzLmluZGV4T2YoIjA05pyI5a6e57upIikgKyAxLA0KICAgICAgICBtYXk6IGhlYWRlcnMuaW5kZXhPZigiMDXmnIjlrp7nu6kiKSwNCiAgICAgICAgbWF5U3RhdHVzOiBoZWFkZXJzLmluZGV4T2YoIjA15pyI5a6e57upIikgKyAxLA0KICAgICAgfTsNCg0KICAgICAgY29uc3QgZGF0YVJvd3MgPSBqc29uRGF0YS5zbGljZSgxKTsNCiAgICAgIGNvbnN0IHRlY2hJbmRpY2F0b3JzID0gZGF0YVJvd3MNCiAgICAgICAgLm1hcCgocm93KSA9PiB7DQogICAgICAgICAgaWYgKCFyb3cgfHwgcm93Lmxlbmd0aCA9PT0gMCkgcmV0dXJuIG51bGw7DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgcm93W2NvbEluZGV4ZXMuZmFjdG9yeV0gPT09IHVuZGVmaW5lZCB8fA0KICAgICAgICAgICAgcm93W2NvbEluZGV4ZXMubmFtZV0gPT09IHVuZGVmaW5lZA0KICAgICAgICAgICkNCiAgICAgICAgICAgIHJldHVybiBudWxsOw0KDQogICAgICAgICAgY29uc3QgaW5kaWNhdG9yID0gew0KICAgICAgICAgICAgZmFjdG9yeTogcm93W2NvbEluZGV4ZXMuZmFjdG9yeV0gfHwgIiIsDQogICAgICAgICAgICBuYW1lOiByb3dbY29sSW5kZXhlcy5uYW1lXSB8fCAiIiwNCiAgICAgICAgICAgIHRhcmdldDoNCiAgICAgICAgICAgICAgcm93W2NvbEluZGV4ZXMudGFyZ2V0XSAhPT0gdW5kZWZpbmVkDQogICAgICAgICAgICAgICAgPyBTdHJpbmcocm93W2NvbEluZGV4ZXMudGFyZ2V0XSkNCiAgICAgICAgICAgICAgICA6ICIiLA0KICAgICAgICAgICAgdW5pdDogcm93W2NvbEluZGV4ZXMudW5pdF0gfHwgIiIsDQogICAgICAgICAgICBqYW46DQogICAgICAgICAgICAgIHJvd1tjb2xJbmRleGVzLmphbl0gIT09IHVuZGVmaW5lZA0KICAgICAgICAgICAgICAgID8gU3RyaW5nKHJvd1tjb2xJbmRleGVzLmphbl0pDQogICAgICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICAgIGphblN0YXR1czogcm93W2NvbEluZGV4ZXMuamFuU3RhdHVzXSA9PT0gMS4wID8gMSA6IDAsDQogICAgICAgICAgICBmZWI6DQogICAgICAgICAgICAgIHJvd1tjb2xJbmRleGVzLmZlYl0gIT09IHVuZGVmaW5lZA0KICAgICAgICAgICAgICAgID8gU3RyaW5nKHJvd1tjb2xJbmRleGVzLmZlYl0pDQogICAgICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICAgIGZlYlN0YXR1czogcm93W2NvbEluZGV4ZXMuZmViU3RhdHVzXSA9PT0gMS4wID8gMSA6IDAsDQogICAgICAgICAgICBtYXI6DQogICAgICAgICAgICAgIHJvd1tjb2xJbmRleGVzLm1hcl0gIT09IHVuZGVmaW5lZA0KICAgICAgICAgICAgICAgID8gU3RyaW5nKHJvd1tjb2xJbmRleGVzLm1hcl0pDQogICAgICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICAgIG1hclN0YXR1czogcm93W2NvbEluZGV4ZXMubWFyU3RhdHVzXSA9PT0gMS4wID8gMSA6IDAsDQogICAgICAgICAgICBhcHI6DQogICAgICAgICAgICAgIHJvd1tjb2xJbmRleGVzLmFwcl0gIT09IHVuZGVmaW5lZA0KICAgICAgICAgICAgICAgID8gU3RyaW5nKHJvd1tjb2xJbmRleGVzLmFwcl0pDQogICAgICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICAgIGFwclN0YXR1czogcm93W2NvbEluZGV4ZXMuYXByU3RhdHVzXSA9PT0gMS4wID8gMSA6IDAsDQogICAgICAgICAgICBtYXk6DQogICAgICAgICAgICAgIHJvd1tjb2xJbmRleGVzLm1heV0gIT09IHVuZGVmaW5lZA0KICAgICAgICAgICAgICAgID8gU3RyaW5nKHJvd1tjb2xJbmRleGVzLm1heV0pDQogICAgICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICAgIG1heVN0YXR1czogcm93W2NvbEluZGV4ZXMubWF5U3RhdHVzXSA9PT0gMS4wID8gMSA6IDAsDQogICAgICAgICAgfTsNCg0KICAgICAgICAgIGluZGljYXRvci5tb250aGx5RGF0YSA9IFsNCiAgICAgICAgICAgIHBhcnNlRmxvYXQoaW5kaWNhdG9yLmphbikgfHwgMCwNCiAgICAgICAgICAgIHBhcnNlRmxvYXQoaW5kaWNhdG9yLmZlYikgfHwgMCwNCiAgICAgICAgICAgIHBhcnNlRmxvYXQoaW5kaWNhdG9yLm1hcikgfHwgMCwNCiAgICAgICAgICAgIHBhcnNlRmxvYXQoaW5kaWNhdG9yLmFwcikgfHwgMCwNCiAgICAgICAgICAgIHBhcnNlRmxvYXQoaW5kaWNhdG9yLm1heSkgfHwgMCwNCiAgICAgICAgICBdOw0KDQogICAgICAgICAgcmV0dXJuIGluZGljYXRvcjsNCiAgICAgICAgfSkNCiAgICAgICAgLmZpbHRlcihCb29sZWFuKTsNCg0KICAgICAgLy8g5aSE55CG55u45ZCM5YiG5Y6C5ZKM5oyH5qCH5ZCN56ew55qE5pWw5o2u77yM56Gu5L+d5pWw5o2u5LiA6Ie05oCnDQogICAgICBjb25zdCB1bmlxdWVLZXlzID0gbmV3IE1hcCgpOw0KICAgICAgY29uc3QgdW5pcXVlSW5kaWNhdG9ycyA9IFtdOw0KDQogICAgICAvLyDlhYjlpITnkIbnm7jlkIzmjIfmoIfnmoTlkIjlubYNCiAgICAgIHRlY2hJbmRpY2F0b3JzLmZvckVhY2goKGluZGljYXRvcikgPT4gew0KICAgICAgICBjb25zdCBrZXkgPSBgJHtpbmRpY2F0b3IuZmFjdG9yeX1fJHtpbmRpY2F0b3IubmFtZX1fJHtpbmRpY2F0b3IudW5pdH1gOw0KICAgICAgICBpZiAoIXVuaXF1ZUtleXMuaGFzKGtleSkpIHsNCiAgICAgICAgICB1bmlxdWVLZXlzLnNldChrZXksIHVuaXF1ZUluZGljYXRvcnMubGVuZ3RoKTsNCiAgICAgICAgICB1bmlxdWVJbmRpY2F0b3JzLnB1c2goaW5kaWNhdG9yKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIHRoaXMudGVjaEluZGljYXRvcnMgPSB1bmlxdWVJbmRpY2F0b3JzOw0KICAgIH0sDQogICAgDQogICAgaW5pdENoYXJ0cygpIHsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8g5Yid5aeL5YyW6YOo6Zeo5a6M5oiQ546H5p+x54q25Zu+DQogICAgICAgIHRoaXMuaW5pdERlcGFydG1lbnRDb21wbGV0aW9uQ2hhcnQoKQ0KICAgICAgICANCiAgICAgICAgLy8g6K6+572u6buY6K6k6YOo6Zeo5ZKM5oyH5qCHDQogICAgICAgIGlmICh0aGlzLmN1cnJlbnRCdXNpbmVzc1VuaXREZXBhcnRtZW50cy5sZW5ndGggPiAwICYmICF0aGlzLmN1cnJlbnREZXBhcnRtZW50KSB7DQogICAgICAgICAgdGhpcy5jdXJyZW50RGVwYXJ0bWVudCA9IHRoaXMuY3VycmVudEJ1c2luZXNzVW5pdERlcGFydG1lbnRzWzBdLnZhbHVlDQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIGlmICh0aGlzLmN1cnJlbnREZXBhcnRtZW50SW5kaWNhdG9ycy5sZW5ndGggPiAwICYmICF0aGlzLmN1cnJlbnRJbmRpY2F0b3IpIHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRJbmRpY2F0b3IgPSB0aGlzLmN1cnJlbnREZXBhcnRtZW50SW5kaWNhdG9yc1swXS5uYW1lDQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIC8vIOWIneWni+WMluaciOW6pui2i+WKv+aKmOe6v+Wbvg0KICAgICAgICB0aGlzLmluaXRNb250aGx5VHJlbmRDaGFydCgpDQogICAgICAgIA0KICAgICAgICAvLyDorr7nva7og73mupDpg6jpl6jpu5jorqTlgLzlubbliJ3lp4vljJbog73mupDlrZDlm77ooagNCiAgICAgICAgaWYgKHRoaXMuYWxsRmFjdG9yaWVzICYmIHRoaXMuYWxsRmFjdG9yaWVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICBpZiAoIXRoaXMuY3VycmVudEVuZXJneURlcHQpIHsNCiAgICAgICAgICAgIHRoaXMuY3VycmVudEVuZXJneURlcHQgPSB0aGlzLmFsbEZhY3Rvcmllc1swXQ0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyDliJ3lp4vljJbog73mupDlrZDlm77ooagNCiAgICAgICAgICB0aGlzLmluaXRFbmVyZ3lEZXRhaWxDaGFydHMoKQ0KICAgICAgICB9DQogICAgICAgIA0KICAgICAgICAvLyDlkK/liqjkuovkuJrpg6jlhoXpg6joh6rliqjliIfmjaINCiAgICAgICAgdGhpcy5zdGFydEF1dG9Td2l0Y2hXaXRoaW5CdXNpbmVzc1VuaXQoKQ0KICAgICAgICANCiAgICAgICAgLy8g5ZCv5Yqo5LqL5Lia6YOo5YiH5o2i77yI5q+PMzDnp5LliIfmjaLkuIDmrKHvvIkNCiAgICAgICAgdGhpcy5zdGFydEJ1c2luZXNzVW5pdFN3aXRjaCgpDQogICAgICAgIA0KICAgICAgICAvLyDlkK/liqjog73mupDpg6jpl6joh6rliqjliIfmjaINCiAgICAgICAgdGhpcy5zdGFydEVuZXJneURlcHRTd2l0Y2goKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOWQr+WKqOS6i+S4mumDqOWGhemDqOiHquWKqOWIh+aNog0KICAgIHN0YXJ0QXV0b1N3aXRjaFdpdGhpbkJ1c2luZXNzVW5pdCgpIHsNCiAgICAgIC8vIOWBnOatouS5i+WJjeeahOiHquWKqOWIh+aNog0KICAgICAgdGhpcy5zdG9wQXV0b1N3aXRjaCgpDQogICAgICANCiAgICAgIC8vIOWQr+WKqOaWsOeahOiHquWKqOWIh+aNog0KICAgICAgdGhpcy50cmVuZENoYXJ0VGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICAgIHRoaXMuc3dpdGNoRGVwYXJ0bWVudFdpdGhpbkJ1c2luZXNzVW5pdCgpDQogICAgICB9LCAzMDAwKSAvLyDliIbljoLliIfmjaLpopHnjofkuLoz56eSDQogICAgfSwNCiAgICANCiAgICAvLyDlnKjlvZPliY3kuovkuJrpg6jlhoXliIfmjaLpg6jpl6gNCiAgICBzd2l0Y2hEZXBhcnRtZW50V2l0aGluQnVzaW5lc3NVbml0KCkgew0KICAgICAgY29uc3QgZGVwYXJ0bWVudHMgPSB0aGlzLmN1cnJlbnRCdXNpbmVzc1VuaXREZXBhcnRtZW50cw0KICAgICAgaWYgKGRlcGFydG1lbnRzLmxlbmd0aCA8PSAxKSByZXR1cm4NCiAgICAgIA0KICAgICAgY29uc3QgY3VycmVudEluZGV4ID0gZGVwYXJ0bWVudHMuZmluZEluZGV4KGRlcHQgPT4gZGVwdC52YWx1ZSA9PT0gdGhpcy5jdXJyZW50RGVwYXJ0bWVudCkNCiAgICAgIGNvbnN0IG5leHRJbmRleCA9IChjdXJyZW50SW5kZXggKyAxKSAlIGRlcGFydG1lbnRzLmxlbmd0aA0KICAgICAgdGhpcy5jdXJyZW50RGVwYXJ0bWVudCA9IGRlcGFydG1lbnRzW25leHRJbmRleF0udmFsdWUNCiAgICB9LA0KICAgIA0KICAgIC8vIOWkhOeQhuiHquWKqOWIh+aNouW8gOWFs+WPmOWMlg0KICAgIGhhbmRsZUF1dG9Td2l0Y2hDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICAvLyDlkK/liqjoh6rliqjliIfmjaINCiAgICAgICAgdGhpcy5zdGFydEF1dG9Td2l0Y2hXaXRoaW5CdXNpbmVzc1VuaXQoKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5YGc5q2i6Ieq5Yqo5YiH5o2iDQogICAgICAgIHRoaXMuc3RvcEF1dG9Td2l0Y2goKQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g5ZCv5Yqo6Ieq5Yqo5YiH5o2iDQogICAgc3RhcnRBdXRvU3dpdGNoKCkgew0KICAgICAgaWYgKHRoaXMudHJlbmRDaGFydFRpbWVyKSB7DQogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy50cmVuZENoYXJ0VGltZXIpDQogICAgICB9DQogICAgICANCiAgICAgIHRoaXMudHJlbmRDaGFydFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICB0aGlzLnN3aXRjaERlcGFydG1lbnQoKQ0KICAgICAgfSwgMzAwMCkNCiAgICB9LA0KICAgIA0KICAgIC8vIOWBnOatouiHquWKqOWIh+aNog0KICAgIHN0b3BBdXRvU3dpdGNoKCkgew0KICAgICAgaWYgKHRoaXMudHJlbmRDaGFydFRpbWVyKSB7DQogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy50cmVuZENoYXJ0VGltZXIpDQogICAgICAgIHRoaXMudHJlbmRDaGFydFRpbWVyID0gbnVsbA0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g5YiH5o2i5a6M5oiQ546H5Zu+6KGo6aG156CBDQogICAgY2hhbmdlUGFnZShwYWdlKSB7DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gcGFnZQ0KICAgICAgdGhpcy5pbml0RGVwYXJ0bWVudENvbXBsZXRpb25DaGFydCgpDQogICAgfSwNCiAgICANCiAgICAvLyDorqHnrpfmnKrlrozmiJDmjIfmoIfmlbDmja7vvIznrZvpgInotJ/mlbDlrozmiJDnjocNCiAgICBjYWxjdWxhdGVJbmNvbXBsZXRlRGF0YSgpIHsNCiAgICAgIGNvbnN0IGluY29tcGxldGUgPSBbXTsNCiAgICAgIA0KICAgICAgdGhpcy5jb21wbGV0aW9uRGF0YS5mb3JFYWNoKGRlcHQgPT4gew0KICAgICAgICBkZXB0LmluZGljYXRvcnMuZm9yRWFjaChpbmRpY2F0b3IgPT4gew0KICAgICAgICAgIC8vIOebtOaOpeS9v+eUqDA05pyI5YiX55qE5a6M5oiQ546H5pWw5o2uDQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ5pyA5ZCO5LiA5YiX77yIMDTmnIjvvInnmoTlrozmiJDnjofmlbDmja4NCiAgICAgICAgICBjb25zdCBjb21wbGV0aW9uUmF0ZVN0ciA9IGluZGljYXRvci5jb21wbGV0aW9uUmF0ZXMgJiYgaW5kaWNhdG9yLmNvbXBsZXRpb25SYXRlc1szXTsgLy8gMDTmnIjlrozmiJDnjocNCiAgICAgICAgICBsZXQgY29tcGxldGlvblJhdGUgPSAwOw0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOWmguaenOacieebtOaOpeeahOWujOaIkOeOh+aVsOaNru+8jOWImeS9v+eUqOWugw0KICAgICAgICAgIGlmIChjb21wbGV0aW9uUmF0ZVN0cikgew0KICAgICAgICAgICAgLy8g5bCG55m+5YiG5q+U5a2X56ym5Liy6L2s5o2i5Li65pWw5YC877yM5L6L5aaCICItMS4zMSUiIC0+IC0xLjMxDQogICAgICAgICAgICBjb21wbGV0aW9uUmF0ZSA9IHBhcnNlRmxvYXQoY29tcGxldGlvblJhdGVTdHIucmVwbGFjZSgnJScsICcnKSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWmguaenOayoeacieebtOaOpeaVsOaNru+8jOWImeeUqOWOn+adpeeahOaWueazleiuoeeulw0KICAgICAgICAgICAgY29uc3QgYWN0dWFsID0gaW5kaWNhdG9yLnZhbHVlc1szXTsgLy8gNOaciOS7veWunumZheWAvA0KICAgICAgICAgICAgY29uc3QgdGFyZ2V0ID0gaW5kaWNhdG9yLnRhcmdldDsNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgaWYgKGluZGljYXRvci5pc0hpZ2hlckJldHRlcikgew0KICAgICAgICAgICAgICAvLyDlpoLmnpzmjIfmoIfotorpq5jotorlpb3vvIzlrozmiJDnjocgPSDlrp7pmYXlgLwv55uu5qCH5YC8ICogMTAwJSAtIDEwMCUNCiAgICAgICAgICAgICAgY29tcGxldGlvblJhdGUgPSAoKGFjdHVhbCAvIHRhcmdldCkgKiAxMDApIC0gMTAwOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgLy8g5aaC5p6c5oyH5qCH6LaK5L2O6LaK5aW977yM5a6M5oiQ546HID0g55uu5qCH5YC8L+WunumZheWAvCAqIDEwMCUgLSAxMDAlDQogICAgICAgICAgICAgIGNvbXBsZXRpb25SYXRlID0gKCh0YXJnZXQgLyBhY3R1YWwpICogMTAwKSAtIDEwMDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgDQogICAgICAgICAgLy8g5Y+q5re75Yqg5a6M5oiQ546H5Li66LSf5pWw55qE6K6w5b2VDQogICAgICAgICAgaWYgKGNvbXBsZXRpb25SYXRlIDwgMCkgew0KICAgICAgICAgICAgaW5jb21wbGV0ZS5wdXNoKHsNCiAgICAgICAgICAgICAgZGVwYXJ0bWVudDogZGVwdC5kZXBhcnRtZW50LmluY2x1ZGVzKCctJykgPyBkZXB0LmRlcGFydG1lbnQuc3BsaXQoJy0nKVsxXSA6IGRlcHQuZGVwYXJ0bWVudCwNCiAgICAgICAgICAgICAgaW5kaWNhdG9yOiBpbmRpY2F0b3IubmFtZSwNCiAgICAgICAgICAgICAgdGFyZ2V0OiBpbmRpY2F0b3IudGFyZ2V0LA0KICAgICAgICAgICAgICBhY3R1YWw6IGluZGljYXRvci52YWx1ZXNbM10sIC8vIDTmnIjku73lrp7pmYXlgLwNCiAgICAgICAgICAgICAgdW5pdDogaW5kaWNhdG9yLnVuaXQsDQogICAgICAgICAgICAgIGNvbXBsZXRpb25SYXRlOiBwYXJzZUZsb2F0KGNvbXBsZXRpb25SYXRlLnRvRml4ZWQoMSkpDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgICANCiAgICAgIC8vIOaMieWujOaIkOeOh+S7juS9juWIsOmrmOaOkuW6jw0KICAgICAgdGhpcy5pbmNvbXBsZXRlRGF0YSA9IGluY29tcGxldGUuc29ydCgoYSwgYikgPT4gYS5jb21wbGV0aW9uUmF0ZSAtIGIuY29tcGxldGlvblJhdGUpOw0KICAgIH0sDQogICAgDQogICAgLy8g5ZCv5Yqo6KGo5qC85rua5YqoDQogICAgc3RhcnRUYWJsZVNjcm9sbCgpIHsNCiAgICAgIGlmICh0aGlzLmluY29tcGxldGVEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgY29uc3QgdGFibGUgPSB0aGlzLiRyZWZzLmluY29tcGxldGVUYWJsZQ0KICAgICAgICANCiAgICAgICAgLy8g5aSN5Yi26KGo5qC85YaF5a655Lul5a6e546w5peg57yd5rua5YqoDQogICAgICAgIGlmICh0YWJsZSkgew0KICAgICAgICAgIC8vIOa4hemZpOS5i+WJjeeahOa7muWKqOWumuaXtuWZqA0KICAgICAgICAgIGlmICh0aGlzLnNjcm9sbFRpbWVyKSB7DQogICAgICAgICAgICBjbGVhckludGVydmFsKHRoaXMuc2Nyb2xsVGltZXIpDQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIC8vIOiOt+WPluihqOagvOWGheWuuemDqOWIhu+8iOS4jeWMheWQq+ihqOWktO+8iQ0KICAgICAgICAgIGNvbnN0IHRhYmxlQm9keSA9IHRhYmxlLnF1ZXJ5U2VsZWN0b3IoJy5lbC10YWJsZV9fYm9keS13cmFwcGVyJykNCiAgICAgICAgICBpZiAodGFibGVCb2R5KSB7DQogICAgICAgICAgICAvLyDliJvlu7rmlrDnmoTmu5rliqjlrprml7blmagNCiAgICAgICAgICAgIGxldCBzY3JvbGxUb3AgPSAwDQogICAgICAgICAgICB0aGlzLnNjcm9sbFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAgICAgICAvLyDlpoLmnpzmmoLlgZzmu5rliqjvvIzliJnkuI3miafooYzmu5rliqjmk43kvZwNCiAgICAgICAgICAgICAgaWYgKHRoaXMudGFibGVTY3JvbGxQYXVzZWQpIHJldHVybjsNCiAgICAgICAgICAgICAgDQogICAgICAgICAgICAgIHNjcm9sbFRvcCsrDQogICAgICAgICAgICAgIGlmIChzY3JvbGxUb3AgPj0gdGFibGVCb2R5LnNjcm9sbEhlaWdodCAvIDIpIHsNCiAgICAgICAgICAgICAgICBzY3JvbGxUb3AgPSAwDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgdGFibGVCb2R5LnNjcm9sbFRvcCA9IHNjcm9sbFRvcA0KICAgICAgICAgICAgfSwgdGhpcy5zY3JvbGxTcGVlZCkNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5Yib5bu66byg5qCH5LqL5Lu25aSE55CG5Ye95pWwDQogICAgICAgICAgICB0aGlzLnRhYmxlTW91c2VFbnRlckhhbmRsZXIgPSAoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMudGFibGVTY3JvbGxQYXVzZWQgPSB0cnVlOw0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgdGhpcy50YWJsZU1vdXNlTGVhdmVIYW5kbGVyID0gKCkgPT4gew0KICAgICAgICAgICAgICB0aGlzLnRhYmxlU2Nyb2xsUGF1c2VkID0gZmFsc2U7DQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgDQogICAgICAgICAgICAvLyDmt7vliqDpvKDmoIfmgqzlgZzkuovku7blpITnkIYNCiAgICAgICAgICAgIHRhYmxlQm9keS5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWVudGVyJywgdGhpcy50YWJsZU1vdXNlRW50ZXJIYW5kbGVyKTsNCiAgICAgICAgICAgIHRhYmxlQm9keS5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWxlYXZlJywgdGhpcy50YWJsZU1vdXNlTGVhdmVIYW5kbGVyKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOWIh+aNoumDqOmXqOi2i+WKv+Wbvg0KICAgIHN3aXRjaERlcGFydG1lbnQoKSB7DQogICAgICBjb25zdCBjdXJyZW50SW5kZXggPSB0aGlzLmRlcGFydG1lbnRzLmluZGV4T2YodGhpcy5jdXJyZW50RGVwYXJ0bWVudCkNCiAgICAgIGNvbnN0IG5leHRJbmRleCA9IChjdXJyZW50SW5kZXggKyAxKSAlIHRoaXMuZGVwYXJ0bWVudHMubGVuZ3RoDQogICAgICB0aGlzLmN1cnJlbnREZXBhcnRtZW50ID0gdGhpcy5kZXBhcnRtZW50c1tuZXh0SW5kZXhdDQogICAgfSwNCiAgICANCiAgICAvLyDliJ3lp4vljJbpg6jpl6jlrozmiJDnjofmn7Hnirblm74gLSDkv67mlLnkuLrmqKrlkJHmn7Hnirblm74NCiAgICBpbml0RGVwYXJ0bWVudENvbXBsZXRpb25DaGFydCgpIHsNCiAgICAgIGNvbnN0IGNoYXJ0RG9tID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2RlcGFydG1lbnRDb21wbGV0aW9uQ2hhcnQnKQ0KICAgICAgY29uc3QgY2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pDQogICAgICANCiAgICAgIC8vIOiuoeeul+m7mOiupOaYvuekuueahOeZvuWIhuavlA0KICAgICAgY29uc3QgZGVmYXVsdERpc3BsYXlQZXJjZW50ID0gTWF0aC5taW4oNiAvIHRoaXMuYWxsSW5kaWNhdG9yQ29tcGxldGlvblJhdGVzLmxlbmd0aCAqIDEwMCwgMTAwKQ0KICAgICAgDQogICAgICAvLyDkvb/nlKjmjIfmoIfnuqfliKvnmoTlrozmiJDnjofmlbDmja4NCiAgICAgIGNvbnN0IGxhYmVscyA9IHRoaXMucGFnaW5hdGVkSW5kaWNhdG9yUmF0ZXMubWFwKGl0ZW0gPT4gew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGRlcHROYW1lOiBpdGVtLmRlcGFydG1lbnQsDQogICAgICAgICAgaW5kaWNhdG9yTmFtZTogaXRlbS5pbmRpY2F0b3IsDQogICAgICAgICAgZnVsbFRleHQ6IGAke2l0ZW0uZGVwYXJ0bWVudH1cbiR7aXRlbS5pbmRpY2F0b3J9YA0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgY29uc3QgcmF0ZXMgPSB0aGlzLnBhZ2luYXRlZEluZGljYXRvclJhdGVzLm1hcChpdGVtID0+IGl0ZW0uY29tcGxldGlvblJhdGUpDQogICAgICANCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogew0KICAgICAgICAgICAgdHlwZTogJ3NoYWRvdycNCiAgICAgICAgICB9LA0KICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICBjb25zdCBkYXRhID0gcGFyYW1zWzBdDQogICAgICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5wYWdpbmF0ZWRJbmRpY2F0b3JSYXRlc1tkYXRhLmRhdGFJbmRleF0NCiAgICAgICAgICAgIGNvbnN0IGFjdHVhbFRleHQgPSBpdGVtLmlzSGlnaGVyQmV0dGVyIA0KICAgICAgICAgICAgICA/IGDlrp7pmYXlgLw6ICR7aXRlbS5hY3R1YWx9JHtpdGVtLnVuaXR9YA0KICAgICAgICAgICAgICA6IGDlrp7pmYXlgLw6ICR7aXRlbS5hY3R1YWx9JHtpdGVtLnVuaXR9IGANCiAgICAgICAgICAgIHJldHVybiBgJHtpdGVtLmRlcGFydG1lbnR9LSR7aXRlbS5pbmRpY2F0b3J9PGJyLz4NCiAgICAgICAgICAgICAgICAgICAg55uu5qCH5YC8OiAke2l0ZW0udGFyZ2V0fSR7aXRlbS51bml0fTxici8+DQogICAgICAgICAgICAgICAgICAgICR7YWN0dWFsVGV4dH08YnIvPg0KICAgICAgICAgICAgICAgICAgICDlrozmiJDnjoc6ICR7aXRlbS5jb21wbGV0aW9uUmF0ZX0lYA0KICAgICAgICAgIH0uYmluZCh0aGlzKQ0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7DQogICAgICAgICAgbGVmdDogJzAnLCAvLyDlt6bnp7szMHB4DQogICAgICAgICAgcmlnaHQ6ICcwJywNCiAgICAgICAgICBib3R0b206ICczJScsDQogICAgICAgICAgdG9wOiAnMyUnLA0KICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICBkYXRhWm9vbTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICdzbGlkZXInLA0KICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgIHlBeGlzSW5kZXg6IFswXSwNCiAgICAgICAgICAgIHN0YXJ0OiAwLA0KICAgICAgICAgICAgZW5kOiBkZWZhdWx0RGlzcGxheVBlcmNlbnQsDQogICAgICAgICAgICB3aWR0aDogMTAsDQogICAgICAgICAgICBoYW5kbGVTaXplOiAyMCwNCiAgICAgICAgICAgIHNob3dEZXRhaWw6IGZhbHNlLA0KICAgICAgICAgICAgem9vbUxvY2s6IGZhbHNlLA0KICAgICAgICAgICAgbW92ZU9uTW91c2VXaGVlbDogdHJ1ZSwNCiAgICAgICAgICAgIHByZXZlbnREZWZhdWx0TW91c2VNb3ZlOiB0cnVlDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAnaW5zaWRlJywNCiAgICAgICAgICAgIHlBeGlzSW5kZXg6IFswXSwNCiAgICAgICAgICAgIHN0YXJ0OiAwLA0KICAgICAgICAgICAgZW5kOiBkZWZhdWx0RGlzcGxheVBlcmNlbnQsDQogICAgICAgICAgICB6b29tTG9jazogZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIHRvb2xib3g6IHsNCiAgICAgICAgICBmZWF0dXJlOiB7DQogICAgICAgICAgICBkYXRhWm9vbTogew0KICAgICAgICAgICAgICB5QXhpc0luZGV4OiAnbm9uZScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICByZXN0b3JlOiB7fSwNCiAgICAgICAgICAgIHNhdmVBc0ltYWdlOiB7fQ0KICAgICAgICAgIH0sDQogICAgICAgICAgcmlnaHQ6IDEwLA0KICAgICAgICAgIHRvcDogMA0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+WujOaIkOeOhyglKScsDQogICAgICAgICAgbWluOiAtMzAsIC8vIOiwg+aVtOacgOWwj+WAvOS4ui0zMCXvvIzotrPlpJ/mmL7npLrotJ/mlbDlrozmiJDnjocNCiAgICAgICAgICBtYXg6IDMwLCAvLyDosIPmlbTmnIDlpKflgLzkuLozMCXvvIzkvb/lm77ooajmm7TogZrnhKYNCiAgICAgICAgICBheGlzTGluZTogew0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzU0NzBjNicNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNwbGl0TGluZTogew0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIHR5cGU6ICdkYXNoZWQnLA0KICAgICAgICAgICAgICBjb2xvcjogJyNFMEU2RjEnDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogbGFiZWxzLm1hcChpdGVtID0+IGl0ZW0uZnVsbFRleHQpLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbih2YWx1ZSkgew0KICAgICAgICAgICAgICByZXR1cm4gdmFsdWU7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgY29sb3I6ICcjMzMzJywNCiAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDE2LA0KICAgICAgICAgICAgbWFyZ2luOiAxMiwNCiAgICAgICAgICAgIHJpY2g6IHsNCiAgICAgICAgICAgICAgZGVwdDogew0KICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJywNCiAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAyMA0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICBpbmRpY2F0b3I6IHsNCiAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAyMA0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGluZTogew0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzU0NzBjNicNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICflrozmiJDnjocnLA0KICAgICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgICBkYXRhOiByYXRlcywNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICAgICAgLy8g5qC55o2u5a6M5oiQ546H6K6+572u5LiN5ZCM6aKc6ImyDQogICAgICAgICAgICAgICAgaWYgKHBhcmFtcy5kYXRhID49IDEwMCkgew0KICAgICAgICAgICAgICAgICAgcmV0dXJuICcjNTQ3MGM2JyAvLyDok53oibLvvIzotoXov4cxMDAlDQogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChwYXJhbXMuZGF0YSA+PSA5MCkgew0KICAgICAgICAgICAgICAgICAgcmV0dXJuICcjOTFjYzc1JyAgLy8g57u/6Imy77yM5a6M5oiQ546H6auYDQogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChwYXJhbXMuZGF0YSA+PSA3MCkgew0KICAgICAgICAgICAgICAgICAgcmV0dXJuICcjZmFjODU4JyAgLy8g6buE6Imy77yM5a6M5oiQ546H5Lit562JDQogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChwYXJhbXMuZGF0YSA+PSAwKSB7DQogICAgICAgICAgICAgICAgICByZXR1cm4gJyNlZTY2NjYnICAvLyDnuqLoibLvvIzlrozmiJDnjofkvY4NCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgcmV0dXJuICcjZmY1MjUyJyAgLy8g5pu05rex55qE57qi6Imy77yM6LSf5pWw5a6M5oiQ546HDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgcG9zaXRpb246ICdyaWdodCcsDQogICAgICAgICAgICAgIGZvcm1hdHRlcjogJ3tjfSUnLA0KICAgICAgICAgICAgICBjb2xvcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICAgICAgLy8g6LSf5pWw5a6M5oiQ546H55So55m96Imy5paH5a2X77yM5pu05Yqg5piO5pi+DQogICAgICAgICAgICAgICAgcmV0dXJuIHBhcmFtcy5kYXRhIDwgMCA/ICcjZmZmZmZmJyA6ICcjMzMzMzMzJzsNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLA0KICAgICAgICAgICAgICBkaXN0YW5jZTogMTUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBiYXJXaWR0aDogJzUwJScsIC8vIOafseWtkOabtOe7hg0KICAgICAgICAgICAgYmFyQ2F0ZWdvcnlHYXA6ICc0MCUnLCAvLyDlop7liqDmn7HlrZDpl7Tot50NCiAgICAgICAgICAgIGFuaW1hdGlvbkRlbGF5OiBmdW5jdGlvbihpZHgpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIGlkeCAqIDEwMCArIDEwMA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgYW5pbWF0aW9uRWFzaW5nOiAnZWxhc3RpY091dCcsDQogICAgICAgIGFuaW1hdGlvbkRlbGF5VXBkYXRlOiBmdW5jdGlvbihpZHgpIHsNCiAgICAgICAgICByZXR1cm4gaWR4ICogNQ0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIGNoYXJ0LnNldE9wdGlvbihvcHRpb24pDQogICAgICANCiAgICAgIC8vIOS/neWtmGRhdGFab29t54q25oCB77yM6Ziy5q2i5rua5Yqo5p2h5Zue5by5DQogICAgICBjaGFydC5vbignZGF0YXpvb20nLCBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgY29uc3QgeyBzdGFydCwgZW5kIH0gPSBwYXJhbXMNCiAgICAgICAgb3B0aW9uLmRhdGFab29tWzBdLnN0YXJ0ID0gc3RhcnQNCiAgICAgICAgb3B0aW9uLmRhdGFab29tWzBdLmVuZCA9IGVuZA0KICAgICAgICBvcHRpb24uZGF0YVpvb21bMV0uc3RhcnQgPSBzdGFydA0KICAgICAgICBvcHRpb24uZGF0YVpvb21bMV0uZW5kID0gZW5kDQogICAgICAgIGNoYXJ0LnNldE9wdGlvbihvcHRpb24pDQogICAgICB9KQ0KICAgICAgDQogICAgICAvLyDliJvlu7rkuovku7blpITnkIblmagNCiAgICAgIHRoaXMuY2hhcnRNb3VzZU92ZXJIYW5kbGVyID0gKCkgPT4gew0KICAgICAgICB0aGlzLmNvbXBsZXRpb25DaGFydFNjcm9sbFBhdXNlZCA9IHRydWUNCiAgICAgIH0NCiAgICAgIA0KICAgICAgdGhpcy5jaGFydE1vdXNlT3V0SGFuZGxlciA9ICgpID0+IHsNCiAgICAgICAgdGhpcy5jb21wbGV0aW9uQ2hhcnRTY3JvbGxQYXVzZWQgPSBmYWxzZQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDmt7vliqDpvKDmoIfmgqzlgZzkuovku7bvvIzmmoLlgZzoh6rliqjmu5rliqgNCiAgICAgIGNoYXJ0RG9tLmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlb3ZlcicsIHRoaXMuY2hhcnRNb3VzZU92ZXJIYW5kbGVyKQ0KICAgICAgDQogICAgICAvLyDmt7vliqDpvKDmoIfnprvlvIDkuovku7bvvIzmgaLlpI3oh6rliqjmu5rliqgNCiAgICAgIGNoYXJ0RG9tLmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlb3V0JywgdGhpcy5jaGFydE1vdXNlT3V0SGFuZGxlcikNCiAgICAgIA0KICAgICAgLy8g56qX5Y+j5aSn5bCP5Y+Y5YyW5pe26Ieq5Yqo6LCD5pW05Zu+6KGo5aSn5bCPDQogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgZnVuY3Rpb24oKSB7DQogICAgICAgIGNoYXJ0LnJlc2l6ZSgpDQogICAgICB9KQ0KICAgICAgDQogICAgICAvLyDkv53lrZjlm77ooajlrp7kvovku6Xkvr/lkI7nu63kvb/nlKgNCiAgICAgIHRoaXMuY29tcGxldGlvbkNoYXJ0ID0gY2hhcnQNCiAgICB9LA0KICAgIA0KICAgIC8vIOWQr+WKqOWujOaIkOeOh+WbvuihqOiHquWKqOa7muWKqA0KICAgIHN0YXJ0Q29tcGxldGlvbkNoYXJ0U2Nyb2xsKCkgew0KICAgICAgaWYgKHRoaXMuY29tcGxldGlvbkNoYXJ0VGltZXIpIHsNCiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmNvbXBsZXRpb25DaGFydFRpbWVyKQ0KICAgICAgfQ0KICAgICAgDQogICAgICB0aGlzLmNvbXBsZXRpb25DaGFydFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICBpZiAodGhpcy5jb21wbGV0aW9uQ2hhcnRTY3JvbGxQYXVzZWQpIHJldHVybg0KICAgICAgICANCiAgICAgICAgaWYgKCF0aGlzLmNvbXBsZXRpb25DaGFydCkgcmV0dXJuDQogICAgICAgIA0KICAgICAgICBjb25zdCBvcHRpb24gPSB0aGlzLmNvbXBsZXRpb25DaGFydC5nZXRPcHRpb24oKQ0KICAgICAgICBsZXQgc3RhcnQgPSBvcHRpb24uZGF0YVpvb21bMF0uc3RhcnQNCiAgICAgICAgbGV0IGVuZCA9IG9wdGlvbi5kYXRhWm9vbVswXS5lbmQNCiAgICAgICAgY29uc3Qgc3RlcCA9IDAuMSAvLyDmr4/mrKHmu5rliqjnmoTnmb7liIbmr5TvvIzmlLnkuLowLjHvvIzkvb/mu5rliqjpnZ7luLjnvJPmhaINCiAgICAgICAgY29uc3QgcmFuZ2UgPSBlbmQgLSBzdGFydCAvLyDlvZPliY3mmL7npLrnmoTojIPlm7QNCiAgICAgICAgDQogICAgICAgIC8vIOagueaNrua7muWKqOaWueWQkeiwg+aVtOa7muWKqOS9jee9rg0KICAgICAgICBpZiAodGhpcy5jb21wbGV0aW9uQ2hhcnRTY3JvbGxEaXJlY3Rpb24gPT09ICdkb3duJykgew0KICAgICAgICAgIC8vIOWQkeS4i+a7muWKqA0KICAgICAgICAgIGlmIChlbmQgPCAxMDApIHsNCiAgICAgICAgICAgIHN0YXJ0ICs9IHN0ZXANCiAgICAgICAgICAgIGVuZCArPSBzdGVwDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOW3suWIsOW6lemDqO+8jOaUueWPmOaWueWQkQ0KICAgICAgICAgICAgdGhpcy5jb21wbGV0aW9uQ2hhcnRTY3JvbGxEaXJlY3Rpb24gPSAndXAnDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWQkeS4iua7muWKqA0KICAgICAgICAgIGlmIChzdGFydCA+IDApIHsNCiAgICAgICAgICAgIHN0YXJ0IC09IHN0ZXANCiAgICAgICAgICAgIGVuZCAtPSBzdGVwDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOW3suWIsOmhtumDqO+8jOaUueWPmOaWueWQkQ0KICAgICAgICAgICAgdGhpcy5jb21wbGV0aW9uQ2hhcnRTY3JvbGxEaXJlY3Rpb24gPSAnZG93bicNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIC8vIOabtOaWsOa7muWKqOS9jee9rg0KICAgICAgICBvcHRpb24uZGF0YVpvb21bMF0uc3RhcnQgPSBzdGFydA0KICAgICAgICBvcHRpb24uZGF0YVpvb21bMF0uZW5kID0gZW5kDQogICAgICAgIG9wdGlvbi5kYXRhWm9vbVsxXS5zdGFydCA9IHN0YXJ0DQogICAgICAgIG9wdGlvbi5kYXRhWm9vbVsxXS5lbmQgPSBlbmQNCiAgICAgICAgdGhpcy5jb21wbGV0aW9uQ2hhcnQuc2V0T3B0aW9uKG9wdGlvbikNCiAgICAgIH0sIDMwMCkgLy8g5rua5Yqo6Ze06ZqU5pS55Li6MzAw5q+r56eS77yM6L+b5LiA5q2l5YeP5oWi5rua5Yqo6YCf5bqmDQogICAgfSwNCiAgICANCiAgICAvLyDliJ3lp4vljJbmnIjluqbotovlir/mipjnur/lm74gLSDmmL7npLrljZXmnaHmipjnur8NCiAgICBpbml0TW9udGhseVRyZW5kQ2hhcnQoKSB7DQogICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdtb250aGx5VHJlbmRDaGFydCcpDQogICAgICBjb25zdCBjaGFydCA9IGVjaGFydHMuaW5pdChjaGFydERvbSkNCiAgICAgIA0KICAgICAgLy8g5p+l5om+5b2T5YmN6YOo6Zeo55qE5pWw5o2uDQogICAgICBjb25zdCBkZXB0RGF0YSA9IHRoaXMuY29tcGxldGlvbkRhdGEuZmluZChkZXB0ID0+IGRlcHQuZGVwYXJ0bWVudCA9PT0gdGhpcy5jdXJyZW50RGVwYXJ0bWVudCB8fCBkZXB0LmRlcGFydG1lbnQuc3RhcnRzV2l0aCh0aGlzLmN1cnJlbnREZXBhcnRtZW50KSkNCiAgICAgIA0KICAgICAgaWYgKCFkZXB0RGF0YSB8fCAhdGhpcy5jdXJyZW50SW5kaWNhdG9yKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDmn6Xmib7lvZPliY3mjIfmoIcNCiAgICAgIGNvbnN0IGluZGljYXRvciA9IGRlcHREYXRhLmluZGljYXRvcnMuZmluZChpbmQgPT4gaW5kLm5hbWUgPT09IHRoaXMuY3VycmVudEluZGljYXRvcikNCiAgICAgIA0KICAgICAgaWYgKCFpbmRpY2F0b3IpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOWHhuWkh+aciOS7veaVsOaNrg0KICAgICAgY29uc3QgbW9udGhzID0gWycx5pyIJywgJzLmnIgnLCAnM+aciCcsICc05pyIJ10NCiAgICAgIA0KICAgICAgLy8g6K6h566X5pWw5o2u6IyD5Zu077yM5Lul5L6/6K6+572ueei9tOiMg+WbtOS9v+aKmOe6v+WxheS4rQ0KICAgICAgY29uc3QgdmFsdWVzID0gaW5kaWNhdG9yLnZhbHVlcw0KICAgICAgY29uc3QgbWluID0gTWF0aC5taW4oLi4udmFsdWVzKQ0KICAgICAgY29uc3QgbWF4ID0gTWF0aC5tYXgoLi4udmFsdWVzKQ0KICAgICAgY29uc3QgcmFuZ2UgPSBtYXggLSBtaW4NCiAgICAgIA0KICAgICAgLy8g6K6+572ueei9tOiMg+WbtO+8jOaJqeWkpzIwJeeahOiMg+WbtOS9v+azouWKqOeci+i1t+adpeabtOaYjuaYvg0KICAgICAgY29uc3QgeU1pbiA9IG1pbiAtIHJhbmdlICogMC40DQogICAgICBjb25zdCB5TWF4ID0gbWF4ICsgcmFuZ2UgKiAwLjQNCiAgICAgIA0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0aXRsZTogew0KICAgICAgICAgIHRleHQ6IGAke3RoaXMuY3VycmVudERlcGFydG1lbnR9IC0gJHt0aGlzLmN1cnJlbnRJbmRpY2F0b3J9YCwNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGZvbnRTaXplOiAxNg0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHBhcmFtcykgew0KICAgICAgICAgICAgcmV0dXJuIGAke3BhcmFtc1swXS5uYW1lfTxici8+JHtpbmRpY2F0b3IubmFtZX06ICR7cGFyYW1zWzBdLnZhbHVlfSAke2luZGljYXRvci51bml0fTxici8+55uu5qCH5YC8OiAke2luZGljYXRvci50YXJnZXR9ICR7aW5kaWNhdG9yLnVuaXR9YA0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc1BvaW50ZXI6IHsNCiAgICAgICAgICAgIHR5cGU6ICdjcm9zcycsDQogICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjNmE3OTg1Jw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogew0KICAgICAgICAgIGxlZnQ6ICc1JScsDQogICAgICAgICAgcmlnaHQ6ICc1JScsDQogICAgICAgICAgYm90dG9tOiAnMTAlJywNCiAgICAgICAgICB0b3A6ICc2MHB4JywNCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGJvdW5kYXJ5R2FwOiBmYWxzZSwNCiAgICAgICAgICBkYXRhOiBtb250aHMsDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICB3aWR0aDogMg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgIG5hbWU6IGluZGljYXRvci51bml0LA0KICAgICAgICAgIG1pbjogeU1pbiwNCiAgICAgICAgICBtYXg6IHlNYXgsDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHZhbHVlKSB7DQogICAgICAgICAgICAgIHJldHVybiB2YWx1ZS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgdHlwZTogJ2Rhc2hlZCcNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgd2lkdGg6IDINCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6IGluZGljYXRvci5uYW1lLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgZGF0YTogaW5kaWNhdG9yLnZhbHVlcywNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwgLy8g5bmz5ruR5puy57q/DQogICAgICAgICAgICBzeW1ib2w6ICdlbXB0eUNpcmNsZScsDQogICAgICAgICAgICBzeW1ib2xTaXplOiAxMCwNCiAgICAgICAgICAgIGVtcGhhc2lzOiB7DQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICAgIHNoYWRvd0JsdXI6IDEwLA0KICAgICAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSg2NCwgMTU4LCAyNTUsIDAuNSknDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIHNjYWxlOiB0cnVlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzQwOUVGRicsDQogICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIHdpZHRoOiA0LA0KICAgICAgICAgICAgICBzaGFkb3dDb2xvcjogJ3JnYmEoMCwgMCwgMCwgMC4zKScsDQogICAgICAgICAgICAgIHNoYWRvd0JsdXI6IDEwLA0KICAgICAgICAgICAgICBzaGFkb3dPZmZzZXRZOiA1DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFsNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICBvZmZzZXQ6IDAsDQogICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoNjQsIDE1OCwgMjU1LCAwLjcpJw0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAwLjUsDQogICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoNjQsIDE1OCwgMjU1LCAwLjMpJw0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAxLA0KICAgICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDY0LCAxNTgsIDI1NSwgMC4xKScNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIF0pDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbWFya0xpbmU6IHsNCiAgICAgICAgICAgICAgc2lsZW50OiB0cnVlLA0KICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgICBjb2xvcjogJyNGNTZDNkMnLA0KICAgICAgICAgICAgICAgIHR5cGU6ICdkYXNoZWQnLA0KICAgICAgICAgICAgICAgIHdpZHRoOiAyDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIGRhdGE6IFsNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICB5QXhpczogaW5kaWNhdG9yLnRhcmdldCwNCiAgICAgICAgICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICAgICAgICAgIGZvcm1hdHRlcjogYOebruagh+WAvDogJHtpbmRpY2F0b3IudGFyZ2V0fWAsDQogICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAnaW5zaWRlRW5kVG9wJywNCiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLA0KICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDI0NSwgMTA4LCAxMDgsIDAuMiknLA0KICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBbMiwgNF0sDQogICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogMg0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgXQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGFuaW1hdGlvbkR1cmF0aW9uOiAyMDAwLA0KICAgICAgICAgICAgYW5pbWF0aW9uRWFzaW5nOiAnZWxhc3RpY091dCcsDQogICAgICAgICAgICBhbmltYXRpb25EZWxheTogZnVuY3Rpb24gKGlkeCkgew0KICAgICAgICAgICAgICByZXR1cm4gaWR4ICogMjAwOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+ebruagh+WAvCcsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhOiBBcnJheShtb250aHMubGVuZ3RoKS5maWxsKGluZGljYXRvci50YXJnZXQpLA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnI0Y1NkM2QycsDQogICAgICAgICAgICAgIHR5cGU6ICdkYXNoZWQnLA0KICAgICAgICAgICAgICB3aWR0aDogMg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHN5bWJvbDogJ25vbmUnDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICBkYXRhOiBbaW5kaWNhdG9yLm5hbWUsICfnm67moIflgLwnXSwNCiAgICAgICAgICBib3R0b206ICcwJScNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgDQogICAgICBjaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQogICAgDQogICAgLy8g5Yid5aeL5YyW6IO95rqQ5raI6ICX5a2Q5Zu+6KGoDQogICAgaW5pdEVuZXJneURldGFpbENoYXJ0cygpIHsNCiAgICAgIC8vIOajgOafpeW9k+WJjemAieaLqeeahOmDqOmXqOaVsOaNruaYr+WQpuacieaViA0KICAgICAgaWYgKCF0aGlzLmN1cnJlbnRFbmVyZ3lEZXB0IHx8ICF0aGlzLmZhY3RvcnlFbmVyZ3lEYXRhKSByZXR1cm47DQogICAgICANCiAgICAgIC8vIOmHjee9ruitpuWRiueKtuaAgQ0KICAgICAgZm9yIChsZXQga2V5IGluIHRoaXMuaXNFbmVyZ3lDaGFydFdhcm5pbmcpIHsNCiAgICAgICAgdGhpcy5pc0VuZXJneUNoYXJ0V2FybmluZ1trZXldID0gZmFsc2U7DQogICAgICB9DQogICAgICANCiAgICAgIC8vIOS4uuavj+enjeiDvea6kOexu+Wei+WIm+W7uuWNleeLrOeahOWbvuihqA0KICAgICAgdGhpcy5lbmVyZ3lUeXBlcy5mb3JFYWNoKHR5cGUgPT4gew0KICAgICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdlbmVyZ3lTdWJjaGFydF8nICsgdHlwZS52YWx1ZSk7DQogICAgICAgIGlmICghY2hhcnREb20pIHJldHVybjsNCiAgICAgICAgDQogICAgICAgIC8vIOa4hemZpOS5i+WJjeeahOWunuS+iw0KICAgICAgICBjb25zdCBleGlzdGluZ0NoYXJ0ID0gZWNoYXJ0cy5nZXRJbnN0YW5jZUJ5RG9tKGNoYXJ0RG9tKTsNCiAgICAgICAgaWYgKGV4aXN0aW5nQ2hhcnQpIHsNCiAgICAgICAgICBleGlzdGluZ0NoYXJ0LmRpc3Bvc2UoKTsNCiAgICAgICAgfQ0KICAgICAgICANCiAgICAgICAgY29uc3QgY2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pOw0KICAgICAgICANCiAgICAgICAgY29uc3QgZGF0YSA9IHRoaXMuZmFjdG9yeUVuZXJneURhdGFbdHlwZS52YWx1ZV1bdGhpcy5jdXJyZW50RW5lcmd5RGVwdF0gfHwgW107DQogICAgICAgIGNvbnN0IG1vbnRocyA9IHRoaXMuZmFjdG9yeUVuZXJneURhdGEubW9udGhzOw0KICAgICAgICBjb25zdCB0YXJnZXRSYW5nZSA9IHRoaXMuZW5lcmd5VGFyZ2V0UmFuZ2VzW3R5cGUudmFsdWVdOw0KICAgICAgICANCiAgICAgICAgLy8g6K6h566X5pWw5o2u55qE5pyA5bCP5YC85ZKM5pyA5aSn5YC877yM5Lul5L6/6K6+572ueei9tOeahOiMg+WbtA0KICAgICAgICBjb25zdCBtaW5WYWx1ZSA9IE1hdGgubWluKC4uLmRhdGEpOw0KICAgICAgICBjb25zdCBtYXhWYWx1ZSA9IE1hdGgubWF4KC4uLmRhdGEpOw0KICAgICAgICBjb25zdCB2YWx1ZVJhbmdlID0gbWF4VmFsdWUgLSBtaW5WYWx1ZTsNCiAgICAgICAgDQogICAgICAgIC8vIOiuvue9rnnovbTnmoTmnIDlsI/lgLzlkozmnIDlpKflgLzvvIzku6Xkvb/mipjnur/lsYXkuK3lubblop7liqDms6LliqjmhJ8NCiAgICAgICAgLy8g6YCa6L+H57yp5bCPeei9tOiMg+WbtOS9v+aKmOe6v+aYvuW+l+abtOWKoOabsuaKmA0KICAgICAgICBjb25zdCB5TWluID0gbWluVmFsdWUgLSB2YWx1ZVJhbmdlICogMC4zOyAvLyDkuIvmlrnpooTnlZkzMCXnmoTnqbrpl7QNCiAgICAgICAgY29uc3QgeU1heCA9IG1heFZhbHVlICsgdmFsdWVSYW5nZSAqIDAuMzsgLy8g5LiK5pa56aKE55WZMzAl55qE56m66Ze0DQogICAgICAgIA0KICAgICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBwYXJhbXNbMF0udmFsdWU7DQogICAgICAgICAgICAgIHJldHVybiBgJHtwYXJhbXNbMF0ubmFtZX08YnIvPiR7dHlwZS5sYWJlbH06ICR7dmFsdWV9ICR7dGFyZ2V0UmFuZ2UudW5pdH1gOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZ3JpZDogew0KICAgICAgICAgICAgbGVmdDogJzEwJScsDQogICAgICAgICAgICByaWdodDogJzUlJywNCiAgICAgICAgICAgIGJvdHRvbTogJzE1JScsDQogICAgICAgICAgICB0b3A6ICcxNSUnLA0KICAgICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlDQogICAgICAgICAgfSwNCiAgICAgICAgICB4QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICAgIGRhdGE6IG1vbnRocywNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgIGNvbG9yOiAnIzMzMzMzMycgLy8g6buR6Imy5Z2Q5qCH6L20DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjMzMzMzMzJyAvLyDpu5HoibLlnZDmoIfovbTmloflrZcNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBib3VuZGFyeUdhcDogZmFsc2UgLy8g6K6p5puy57q/5LuO5Z2Q5qCH6L205byA5aeLDQogICAgICAgICAgfSwNCiAgICAgICAgICB5QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIG5hbWU6IHRhcmdldFJhbmdlLnVuaXQsDQogICAgICAgICAgICBtaW46IHlNaW4sIC8vIOiuvue9ruacgOWwj+WAvOS9v+aKmOe6v+WxheS4rQ0KICAgICAgICAgICAgbWF4OiB5TWF4LCAvLyDorr7nva7mnIDlpKflgLzkvb/mipjnur/lsYXkuK0NCiAgICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgIGNvbG9yOiAnIzMzMzMzMycgLy8g6buR6Imy5Z2Q5qCH6L20DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjMzMzMzMzJywgLy8g6buR6Imy5Z2Q5qCH6L205paH5a2XDQogICAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUpIHsNCiAgICAgICAgICAgICAgICAvLyDkv53nlZnpgILlvZPnmoTlsI/mlbDkvY3mlbDku6Xpgb/lhY3ov4fluqbmi6XmjKQNCiAgICAgICAgICAgICAgICBpZiAodmFsdWUgPj0gMTAwKSB7DQogICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUudG9GaXhlZCgwKTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlLnRvRml4ZWQoMSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc3BsaXRMaW5lOiB7DQogICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgIHR5cGU6ICdkYXNoZWQnLA0KICAgICAgICAgICAgICAgIGNvbG9yOiAnI0UwRTZGMScNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIG5hbWU6IHR5cGUubGFiZWwsDQogICAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgICAgZGF0YTogZGF0YSwNCiAgICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDkvb/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgICAgc21vb3RoTW9ub3RvbmU6ICdub25lJywgLy8g5LiN5L+d5oyB5Y2V6LCD5oCn77yM5YWB6K645pu05aSa5rOi5YqoDQogICAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICAgIHN5bWJvbFNpemU6IDgsDQogICAgICAgICAgICAgIHNhbXBsaW5nOiAnYXZlcmFnZScsIC8vIOS9v+eUqOW5s+Wdh+mHh+agtw0KICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgICB3aWR0aDogNCwgLy8g5Yqg57KX57q/5p2hDQogICAgICAgICAgICAgICAgY29sb3I6IHR5cGUuY29sb3IgLy8g5L2/55So5pu05rex55qE6aKc6ImyDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICAgIGNvbG9yOiB0eXBlLmNvbG9yLA0KICAgICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAyLA0KICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2ZmZicsDQogICAgICAgICAgICAgICAgc2hhZG93Q29sb3I6ICdyZ2JhKDAsIDAsIDAsIDAuMyknLA0KICAgICAgICAgICAgICAgIHNoYWRvd0JsdXI6IDUNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAzLA0KICAgICAgICAgICAgICAgICAgc2hhZG93Qmx1cjogMTANCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgICAgd2lkdGg6IDYgLy8g6byg5qCH5oKs5YGc5pe257q/5p2h5pu057KXDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICBhcmVhU3R5bGU6IHsNCiAgICAgICAgICAgICAgICBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbDQogICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgIG9mZnNldDogMCwNCiAgICAgICAgICAgICAgICAgICAgY29sb3I6IHRoaXMuaGV4VG9SZ2JhKHR5cGUuY29sb3IsIDAuNikgLy8g5pu06auY55qE6YCP5piO5bqm5L2/6aKc6Imy5pu05rexDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICBvZmZzZXQ6IDEsDQogICAgICAgICAgICAgICAgICAgIGNvbG9yOiB0aGlzLmhleFRvUmdiYSh0eXBlLmNvbG9yLCAwLjEpDQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgXSkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfTsNCiAgICAgICAgDQogICAgICAgIGNoYXJ0LnNldE9wdGlvbihvcHRpb24pOw0KICAgICAgfSk7DQogICAgfSwNCiAgICANCiAgICAvLyDpopzoibLovazmjaLovoXliqnlh73mlbANCiAgICBoZXhUb1JnYmEoaGV4LCBhbHBoYSkgew0KICAgICAgY29uc3QgciA9IHBhcnNlSW50KGhleC5zbGljZSgxLCAzKSwgMTYpOw0KICAgICAgY29uc3QgZyA9IHBhcnNlSW50KGhleC5zbGljZSgzLCA1KSwgMTYpOw0KICAgICAgY29uc3QgYiA9IHBhcnNlSW50KGhleC5zbGljZSg1LCA3KSwgMTYpOw0KICAgICAgcmV0dXJuIGByZ2JhKCR7cn0sICR7Z30sICR7Yn0sICR7YWxwaGF9KWA7DQogICAgfSwNCiAgICANCiAgICAvLyDlkK/liqjog73mupDpg6jpl6joh6rliqjliIfmjaINCiAgICBzdGFydEVuZXJneURlcHRTd2l0Y2goKSB7DQogICAgICAvLyDlgZzmraLkuYvliY3nmoTog73mupDpg6jpl6jliIfmjaINCiAgICAgIGlmICh0aGlzLmVuZXJneURlcHRUaW1lcikgew0KICAgICAgICBjbGVhckludGVydmFsKHRoaXMuZW5lcmd5RGVwdFRpbWVyKQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDlkK/liqjmlrDnmoTog73mupDpg6jpl6jliIfmjaINCiAgICAgIHRoaXMuZW5lcmd5RGVwdFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICB0aGlzLnN3aXRjaEVuZXJneURlcHQoKQ0KICAgICAgfSwgMTAwMDApIC8vIOavjzEw56eS5YiH5o2i5LiA5qyh6YOo6Zeo77yM5Y6f5p2l5pivNTAwMA0KICAgIH0sDQogICAgDQogICAgLy8g5YiH5o2i6IO95rqQ6YOo6ZeoDQogICAgc3dpdGNoRW5lcmd5RGVwdCgpIHsNCiAgICAgIGNvbnN0IGN1cnJlbnRJbmRleCA9IHRoaXMuYWxsRmFjdG9yaWVzLmluZGV4T2YodGhpcy5jdXJyZW50RW5lcmd5RGVwdCkNCiAgICAgIGNvbnN0IG5leHRJbmRleCA9IChjdXJyZW50SW5kZXggKyAxKSAlIHRoaXMuYWxsRmFjdG9yaWVzLmxlbmd0aA0KICAgICAgdGhpcy5jdXJyZW50RW5lcmd5RGVwdCA9IHRoaXMuYWxsRmFjdG9yaWVzW25leHRJbmRleF0NCiAgICAgIHRoaXMuaW5pdEVuZXJneURldGFpbENoYXJ0cygpDQogICAgfSwNCiAgICANCiAgICAvLyDnqpflj6PlpKflsI/lj5jljJbml7bph43mlrDmuLLmn5Plm77ooagNCiAgICByZXNpemVDaGFydHMoKSB7DQogICAgICBjb25zdCBjaGFydElkcyA9IFsNCiAgICAgICAgJ2RlcGFydG1lbnRDb21wbGV0aW9uQ2hhcnQnLA0KICAgICAgICAnbW9udGhseVRyZW5kQ2hhcnQnDQogICAgICBdDQogICAgICANCiAgICAgIGNoYXJ0SWRzLmZvckVhY2goaWQgPT4gew0KICAgICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGlkKQ0KICAgICAgICBpZiAoY2hhcnREb20pIHsNCiAgICAgICAgICBjb25zdCBjaGFydCA9IGVjaGFydHMuZ2V0SW5zdGFuY2VCeURvbShjaGFydERvbSkNCiAgICAgICAgICBpZiAoY2hhcnQpIHsNCiAgICAgICAgICAgIGNoYXJ0LnJlc2l6ZSgpDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgDQogICAgICAvLyDosIPmlbTog73mupDlrZDlm77ooajlpKflsI8NCiAgICAgIHRoaXMuZW5lcmd5VHlwZXMuZm9yRWFjaCh0eXBlID0+IHsNCiAgICAgICAgY29uc3QgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnZW5lcmd5U3ViY2hhcnRfJyArIHR5cGUudmFsdWUpDQogICAgICAgIGlmIChjaGFydERvbSkgew0KICAgICAgICAgIGNvbnN0IGNoYXJ0ID0gZWNoYXJ0cy5nZXRJbnN0YW5jZUJ5RG9tKGNoYXJ0RG9tKQ0KICAgICAgICAgIGlmIChjaGFydCkgew0KICAgICAgICAgICAgY2hhcnQucmVzaXplKCkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICANCiAgICAvLyDplIDmr4Hlm77ooajlrp7kvosNCiAgICBkaXNwb3NlQ2hhcnRzKCkgew0KICAgICAgY29uc3QgY2hhcnRJZHMgPSBbDQogICAgICAgICdkZXBhcnRtZW50Q29tcGxldGlvbkNoYXJ0JywNCiAgICAgICAgJ21vbnRobHlUcmVuZENoYXJ0Jw0KICAgICAgXQ0KICAgICAgDQogICAgICBjaGFydElkcy5mb3JFYWNoKGlkID0+IHsNCiAgICAgICAgY29uc3QgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChpZCkNCiAgICAgICAgaWYgKGNoYXJ0RG9tKSB7DQogICAgICAgICAgLy8g56e76Zmk5LqL5Lu255uR5ZCs5ZmoDQogICAgICAgICAgaWYgKGlkID09PSAnZGVwYXJ0bWVudENvbXBsZXRpb25DaGFydCcgJiYgdGhpcy5jaGFydE1vdXNlT3ZlckhhbmRsZXIgJiYgdGhpcy5jaGFydE1vdXNlT3V0SGFuZGxlcikgew0KICAgICAgICAgICAgY2hhcnREb20ucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2VvdmVyJywgdGhpcy5jaGFydE1vdXNlT3ZlckhhbmRsZXIpDQogICAgICAgICAgICBjaGFydERvbS5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZW91dCcsIHRoaXMuY2hhcnRNb3VzZU91dEhhbmRsZXIpDQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIGNvbnN0IGNoYXJ0ID0gZWNoYXJ0cy5nZXRJbnN0YW5jZUJ5RG9tKGNoYXJ0RG9tKQ0KICAgICAgICAgIGlmIChjaGFydCkgew0KICAgICAgICAgICAgY2hhcnQuZGlzcG9zZSgpDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgDQogICAgICAvLyDmuIXpmaTlrozmiJDnjoflm77ooajlvJXnlKgNCiAgICAgIHRoaXMuY29tcGxldGlvbkNoYXJ0ID0gbnVsbA0KICAgICAgDQogICAgICAvLyDplIDmr4Hog73mupDlrZDlm77ooagNCiAgICAgIHRoaXMuZW5lcmd5VHlwZXMuZm9yRWFjaCh0eXBlID0+IHsNCiAgICAgICAgY29uc3QgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnZW5lcmd5U3ViY2hhcnRfJyArIHR5cGUudmFsdWUpDQogICAgICAgIGlmIChjaGFydERvbSkgew0KICAgICAgICAgIGNvbnN0IGNoYXJ0ID0gZWNoYXJ0cy5nZXRJbnN0YW5jZUJ5RG9tKGNoYXJ0RG9tKQ0KICAgICAgICAgIGlmIChjaGFydCkgew0KICAgICAgICAgICAgY2hhcnQuZGlzcG9zZSgpDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgDQoNCiAgICAvLyDlkK/liqjkuovkuJrpg6jliIfmjaINCiAgICBzdGFydEJ1c2luZXNzVW5pdFN3aXRjaCgpIHsNCiAgICAgIC8vIOWBnOatouS5i+WJjeeahOS6i+S4mumDqOWIh+aNog0KICAgICAgaWYgKHRoaXMuYnVzaW5lc3NVbml0VGltZXIpIHsNCiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmJ1c2luZXNzVW5pdFRpbWVyKQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDlkK/liqjmlrDnmoTkuovkuJrpg6jliIfmjaINCiAgICAgIHRoaXMuYnVzaW5lc3NVbml0VGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICAgIHRoaXMuc3dpdGNoQnVzaW5lc3NVbml0KCkNCiAgICAgIH0sIDMwMDAwKSAvLyDkuovkuJrpg6jliIfmjaLpopHnjofkuLozMOenkg0KICAgIH0sDQogICAgDQogICAgLy8g5YiH5o2i5LqL5Lia6YOoDQogICAgc3dpdGNoQnVzaW5lc3NVbml0KCkgew0KICAgICAgY29uc3QgY3VycmVudEluZGV4ID0gdGhpcy5kZXBhcnRtZW50cy5pbmRleE9mKHRoaXMuY3VycmVudEJ1c2luZXNzVW5pdCkNCiAgICAgIGNvbnN0IG5leHRJbmRleCA9IChjdXJyZW50SW5kZXggKyAxKSAlIHRoaXMuZGVwYXJ0bWVudHMubGVuZ3RoDQogICAgICB0aGlzLmN1cnJlbnRCdXNpbmVzc1VuaXQgPSB0aGlzLmRlcGFydG1lbnRzW25leHRJbmRleF0NCiAgICB9LA0KICAgIA0KICAgIC8vIOWIneWni+WMluWFs+mUruiDvea6kOaMh+agh+WvueavlOWbvg0KICAgIGluaXRLZXlFbmVyZ3lJbmRpY2F0b3JzQ2hhcnQoKSB7DQogICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdrZXlFbmVyZ3lJbmRpY2F0b3JzQ2hhcnQnKQ0KICAgICAgY29uc3QgY2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pDQogICAgICANCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogew0KICAgICAgICAgICAgdHlwZTogJ3NoYWRvdycNCiAgICAgICAgICB9LA0KICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICBjb25zdCBkYXRhSW5kZXggPSBwYXJhbXNbMF0uZGF0YUluZGV4DQogICAgICAgICAgICBjb25zdCBpbmRpY2F0b3IgPSB0aGlzLmtleUVuZXJneUluZGljYXRvcnNbZGF0YUluZGV4XQ0KICAgICAgICAgICAgY29uc3QgY2hhbmdlVGV4dCA9IGluZGljYXRvci5jaGFuZ2UgPiAwID8gYCske2luZGljYXRvci5jaGFuZ2V9JWAgOiBgJHtpbmRpY2F0b3IuY2hhbmdlfSVgDQogICAgICAgICAgICBjb25zdCBjaGFuZ2VDb2xvciA9IGluZGljYXRvci5zdGF0dXMgPT09ICdnb29kJyA/ICcjNjdDMjNBJyA6IGluZGljYXRvci5zdGF0dXMgPT09ICd3YXJuaW5nJyA/ICcjRTZBMjNDJyA6ICcjRjU2QzZDJw0KICAgICAgICAgICAgDQogICAgICAgICAgICByZXR1cm4gYA0KICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJmb250LXdlaWdodDpib2xkIj4ke2luZGljYXRvci5uYW1lfTwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2PuS7iuaXpTogJHtpbmRpY2F0b3IudG9kYXl9ICR7aW5kaWNhdG9yLnVuaXR9PC9kaXY+DQogICAgICAgICAgICAgIDxkaXY+5pio5pelOiAke2luZGljYXRvci55ZXN0ZXJkYXl9ICR7aW5kaWNhdG9yLnVuaXR9PC9kaXY+DQogICAgICAgICAgICAgIDxkaXY+5Y+Y5YyWOiA8c3BhbiBzdHlsZT0iY29sb3I6JHtjaGFuZ2VDb2xvcn0iPiR7Y2hhbmdlVGV4dH08L3NwYW4+PC9kaXY+DQogICAgICAgICAgICBgDQogICAgICAgICAgfS5iaW5kKHRoaXMpDQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICBsZWZ0OiAnMyUnLA0KICAgICAgICAgIHJpZ2h0OiAnNCUnLA0KICAgICAgICAgIGJvdHRvbTogJzMlJywNCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGRhdGE6IHRoaXMua2V5RW5lcmd5SW5kaWNhdG9ycy5tYXAoaXRlbSA9PiBpdGVtLm5hbWUpDQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUpIHsNCiAgICAgICAgICAgICAgaWYgKHZhbHVlID49IDEwMCkgew0KICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZS50b0ZpeGVkKDApDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlLnRvRml4ZWQoMSkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+S7iuaXpeWAvCcsDQogICAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICAgIGRhdGE6IHRoaXMua2V5RW5lcmd5SW5kaWNhdG9ycy5tYXAoKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0udG9kYXksDQogICAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgICAgICBjb2xvcjogaXRlbS5zdGF0dXMgPT09ICdnb29kJyA/ICcjNjdDMjNBJyA6IGl0ZW0uc3RhdHVzID09PSAnd2FybmluZycgPyAnI0U2QTIzQycgOiAnI0Y1NkM2QycNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pLA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgcG9zaXRpb246ICd0b3AnLA0KICAgICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHBhcmFtcykgew0KICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gcGFyYW1zLmRhdGFJbmRleA0KICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmtleUVuZXJneUluZGljYXRvcnNbaW5kZXhdLnRvZGF5DQogICAgICAgICAgICAgIH0uYmluZCh0aGlzKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGJhcldpZHRoOiAnMzAlJw0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+aYqOaXpeWAvCcsDQogICAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICAgIGRhdGE6IHRoaXMua2V5RW5lcmd5SW5kaWNhdG9ycy5tYXAoaXRlbSA9PiBpdGVtLnllc3RlcmRheSksDQogICAgICAgICAgICBiYXJXaWR0aDogJzMwJScsDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOTA5Mzk5JywNCiAgICAgICAgICAgICAgb3BhY2l0eTogMC41DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICBkYXRhOiBbJ+S7iuaXpeWAvCcsICfmmKjml6XlgLwnXSwNCiAgICAgICAgICBib3R0b206IDANCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgDQogICAgICBjaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQogICAgDQogICAgLy8g5qC55o2u57Si5byV5L2N572u6K6h566X5riQ5Y+Y6aKc6Imy5qC35byPDQogICAgZ2V0R3JhZGllbnRTdHlsZShpbmRleCwgdG90YWwpIHsNCiAgICAgIC8vIOiuoeeul+ivpeaMh+agh+WcqOaVtOS9k+S4reeahOebuOWvueS9jee9ru+8iDAtMeS5i+mXtO+8iQ0KICAgICAgY29uc3QgcG9zaXRpb24gPSBpbmRleCAvICh0b3RhbCAtIDEpOw0KICAgICAgDQogICAgICAvLyDlrprkuYnnu7/oibLliLDnuqLoibLnmoTmuJDlj5jpopzoibLmlbDnu4QNCiAgICAgIGNvbnN0IGNvbG9yU3RvcHMgPSBbDQogICAgICAgICcjNTJjNDFhJywgLy8g5Lqu57u/6ImyDQogICAgICAgICcjODVjZTYxJywgLy8g57u/6ImyDQogICAgICAgICcjYjNlMTlkJywgLy8g5rWF57u/6ImyDQogICAgICAgICcjZDRmOGJlJywgLy8g6Z2e5bi45rWF55qE57u/6ImyDQogICAgICAgICcjZmFmZjcyJywgLy8g6buE6ImyDQogICAgICAgICcjZmFkYjE0JywgLy8g6YeR6buE6ImyDQogICAgICAgICcjZmZhOTQwJywgLy8g5qmZ6ImyDQogICAgICAgICcjZmE4YzE2JywgLy8g5rex5qmZ6ImyDQogICAgICAgICcjZmY3ODc1JywgLy8g5rWF57qi6ImyDQogICAgICAgICcjZmY0ZDRmJywgLy8g57qi6ImyDQogICAgICAgICcjZjUyMjJkJywgLy8g5Lqu57qi6ImyDQogICAgICAgICcjY2YxMzIyJyAgLy8g5rex57qi6ImyDQogICAgICBdOw0KICAgICAgDQogICAgICAvLyDmoLnmja7kvY3nva7lnKjmuJDlj5joibLkuYvpl7Tmj5LlgLzojrflj5bpopzoibINCiAgICAgIC8vIOaJvuWIsOWvueW6lOeahOminOiJsuWMuumXtA0KICAgICAgY29uc3Qgc2VnbWVudENvdW50ID0gY29sb3JTdG9wcy5sZW5ndGggLSAxOw0KICAgICAgY29uc3Qgc2VnbWVudCA9IE1hdGgubWluKE1hdGguZmxvb3IocG9zaXRpb24gKiBzZWdtZW50Q291bnQpLCBzZWdtZW50Q291bnQgLSAxKTsNCiAgICAgIGNvbnN0IGxvY2FsUG9zaXRpb24gPSAocG9zaXRpb24gKiBzZWdtZW50Q291bnQpIC0gc2VnbWVudDsgLy8g5Zyo5b2T5YmN5Yy66Ze05YaF55qE55u45a+55L2N572uICgwLTEpDQogICAgICANCiAgICAgIC8vIOiOt+WPluWMuumXtOeahOi1t+atouminOiJsg0KICAgICAgY29uc3Qgc3RhcnRDb2xvciA9IGNvbG9yU3RvcHNbc2VnbWVudF07DQogICAgICBjb25zdCBlbmRDb2xvciA9IGNvbG9yU3RvcHNbc2VnbWVudCArIDFdOw0KICAgICAgDQogICAgICAvLyDlnKjkuKTkuKrpopzoibLkuYvpl7Tmj5LlgLwNCiAgICAgIGNvbnN0IGJnQ29sb3IgPSB0aGlzLmludGVycG9sYXRlQ29sb3JzKHN0YXJ0Q29sb3IsIGVuZENvbG9yLCBsb2NhbFBvc2l0aW9uKTsNCiAgICAgIA0KICAgICAgLy8g6K6h566X5paH5a2X6aKc6ImyDQogICAgICAvLyDmj5Dlj5ZSR0LlubborqHnrpfkuq7luqYNCiAgICAgIGxldCByID0gcGFyc2VJbnQoYmdDb2xvci5zbGljZSgxLCAzKSwgMTYpOw0KICAgICAgbGV0IGcgPSBwYXJzZUludChiZ0NvbG9yLnNsaWNlKDMsIDUpLCAxNik7DQogICAgICBsZXQgYiA9IHBhcnNlSW50KGJnQ29sb3Iuc2xpY2UoNSwgNyksIDE2KTsNCiAgICAgIGNvbnN0IGJyaWdodG5lc3MgPSAociAqIDI5OSArIGcgKiA1ODcgKyBiICogMTE0KSAvIDEwMDA7DQogICAgICANCiAgICAgIC8vIOS6ruW6puWkp+S6jjE0MOS9v+eUqOa3seiJsuaWh+Wtl++8jOWQpuWImeS9v+eUqOa1heiJsuaWh+Wtlw0KICAgICAgbGV0IHRleHRDb2xvciA9IGJyaWdodG5lc3MgPiAxNDAgPyAnIzMzMzMzMycgOiAnI2ZmZmZmZic7DQogICAgICANCiAgICAgIC8vIOiuvue9rui+ueahhuminOiJsu+8iOavlOiDjOaZr+iJsueojea3se+8iQ0KICAgICAgY29uc3QgYm9yZGVyQ29sb3IgPSB0aGlzLmFkanVzdENvbG9yKGJnQ29sb3IsIC0yMCk7DQogICAgICANCiAgICAgIHJldHVybiB7DQogICAgICAgIGJhY2tncm91bmRDb2xvcjogYmdDb2xvciwNCiAgICAgICAgY29sb3I6IHRleHRDb2xvciwNCiAgICAgICAgYm9yZGVyVG9wQ29sb3I6IGJvcmRlckNvbG9yDQogICAgICB9Ow0KICAgIH0sDQogICAgDQogICAgLy8g6aKc6Imy5o+S5YC85Ye95pWwDQogICAgaW50ZXJwb2xhdGVDb2xvcnMoY29sb3IxLCBjb2xvcjIsIGZhY3Rvcikgew0KICAgICAgLy8g6Kej5p6Q6aKc6ImyDQogICAgICBsZXQgcjEgPSBwYXJzZUludChjb2xvcjEuc2xpY2UoMSwgMyksIDE2KTsNCiAgICAgIGxldCBnMSA9IHBhcnNlSW50KGNvbG9yMS5zbGljZSgzLCA1KSwgMTYpOw0KICAgICAgbGV0IGIxID0gcGFyc2VJbnQoY29sb3IxLnNsaWNlKDUsIDcpLCAxNik7DQogICAgICANCiAgICAgIGxldCByMiA9IHBhcnNlSW50KGNvbG9yMi5zbGljZSgxLCAzKSwgMTYpOw0KICAgICAgbGV0IGcyID0gcGFyc2VJbnQoY29sb3IyLnNsaWNlKDMsIDUpLCAxNik7DQogICAgICBsZXQgYjIgPSBwYXJzZUludChjb2xvcjIuc2xpY2UoNSwgNyksIDE2KTsNCiAgICAgIA0KICAgICAgLy8g57q/5oCn5o+S5YC8DQogICAgICBsZXQgciA9IE1hdGgucm91bmQocjEgKyBmYWN0b3IgKiAocjIgLSByMSkpOw0KICAgICAgbGV0IGcgPSBNYXRoLnJvdW5kKGcxICsgZmFjdG9yICogKGcyIC0gZzEpKTsNCiAgICAgIGxldCBiID0gTWF0aC5yb3VuZChiMSArIGZhY3RvciAqIChiMiAtIGIxKSk7DQogICAgICANCiAgICAgIC8vIOi9rOWbnuWNgeWFrei/m+WItg0KICAgICAgcmV0dXJuIGAjJHtyLnRvU3RyaW5nKDE2KS5wYWRTdGFydCgyLCAnMCcpfSR7Zy50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKX0ke2IudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJyl9YDsNCiAgICB9LA0KICAgIA0KICAgIC8vIOiOt+WPluW+veeroOagt+W8jw0KICAgIGdldEJhZGdlU3R5bGUoaW5kZXgsIHRvdGFsKSB7DQogICAgICAvLyDojrflj5blvZPliY3ljaHniYfnmoTog4zmma/oibINCiAgICAgIGNvbnN0IHBvc2l0aW9uID0gaW5kZXggLyAodG90YWwgLSAxKTsNCiAgICAgIGNvbnN0IGNvbG9yU3RvcHMgPSBbDQogICAgICAgICcjNTJjNDFhJywgLy8g5Lqu57u/6ImyDQogICAgICAgICcjODVjZTYxJywgLy8g57u/6ImyDQogICAgICAgICcjYjNlMTlkJywgLy8g5rWF57u/6ImyDQogICAgICAgICcjZDRmOGJlJywgLy8g6Z2e5bi45rWF55qE57u/6ImyDQogICAgICAgICcjZmFmZjcyJywgLy8g6buE6ImyDQogICAgICAgICcjZmFkYjE0JywgLy8g6YeR6buE6ImyDQogICAgICAgICcjZmZhOTQwJywgLy8g5qmZ6ImyDQogICAgICAgICcjZmE4YzE2JywgLy8g5rex5qmZ6ImyDQogICAgICAgICcjZmY3ODc1JywgLy8g5rWF57qi6ImyDQogICAgICAgICcjZmY0ZDRmJywgLy8g57qi6ImyDQogICAgICAgICcjZjUyMjJkJywgLy8g5Lqu57qi6ImyDQogICAgICAgICcjY2YxMzIyJyAgLy8g5rex57qi6ImyDQogICAgICBdOw0KICAgICAgDQogICAgICBjb25zdCBzZWdtZW50Q291bnQgPSBjb2xvclN0b3BzLmxlbmd0aCAtIDE7DQogICAgICBjb25zdCBzZWdtZW50ID0gTWF0aC5taW4oTWF0aC5mbG9vcihwb3NpdGlvbiAqIHNlZ21lbnRDb3VudCksIHNlZ21lbnRDb3VudCAtIDEpOw0KICAgICAgY29uc3QgbG9jYWxQb3NpdGlvbiA9IChwb3NpdGlvbiAqIHNlZ21lbnRDb3VudCkgLSBzZWdtZW50Ow0KICAgICAgDQogICAgICBjb25zdCBzdGFydENvbG9yID0gY29sb3JTdG9wc1tzZWdtZW50XTsNCiAgICAgIGNvbnN0IGVuZENvbG9yID0gY29sb3JTdG9wc1tzZWdtZW50ICsgMV07DQogICAgICANCiAgICAgIGNvbnN0IGJnQ29sb3IgPSB0aGlzLmludGVycG9sYXRlQ29sb3JzKHN0YXJ0Q29sb3IsIGVuZENvbG9yLCBsb2NhbFBvc2l0aW9uKTsNCiAgICAgIA0KICAgICAgLy8g6K6h566X5paH5a2X6aKc6ImyDQogICAgICBsZXQgciA9IHBhcnNlSW50KGJnQ29sb3Iuc2xpY2UoMSwgMyksIDE2KTsNCiAgICAgIGxldCBnID0gcGFyc2VJbnQoYmdDb2xvci5zbGljZSgzLCA1KSwgMTYpOw0KICAgICAgbGV0IGIgPSBwYXJzZUludChiZ0NvbG9yLnNsaWNlKDUsIDcpLCAxNik7DQogICAgICBjb25zdCBicmlnaHRuZXNzID0gKHIgKiAyOTkgKyBnICogNTg3ICsgYiAqIDExNCkgLyAxMDAwOw0KICAgICAgDQogICAgICAvLyDmoLnmja7og4zmma/kuq7luqborr7nva7lvr3nq6DmoLflvI8NCiAgICAgIGlmIChicmlnaHRuZXNzID4gMTQwKSB7DQogICAgICAgIC8vIOa1heiJsuiDjOaZr+S9v+eUqOa3seiJsui+ueahhueahOW+veeroA0KICAgICAgICBjb25zdCBiYWRnZUJnQ29sb3IgPSB0aGlzLmFkanVzdENvbG9yKGJnQ29sb3IsIC00MCk7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBiYWRnZUJnQ29sb3IsDQogICAgICAgICAgY29sb3I6ICcjZmZmZmZmJywNCiAgICAgICAgICBib3hTaGFkb3c6ICcwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpJw0KICAgICAgICB9Ow0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5rex6Imy6IOM5pmv5L2/55So5rWF6Imy5b6956ugDQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjI1KScsDQogICAgICAgICAgY29sb3I6ICcjZmZmZmZmJywNCiAgICAgICAgICBib3hTaGFkb3c6ICcwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjIpJw0KICAgICAgICB9Ow0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g6aKc6Imy6LCD5pW06L6F5Yqp5Ye95pWwDQogICAgYWRqdXN0Q29sb3IoY29sb3IsIGFtb3VudCkgew0KICAgICAgLy8g5aaC5p6c5piv55m96Imy54m55q6K5aSE55CGDQogICAgICBpZiAoY29sb3IgPT09ICcjZmZmZmZmJykgew0KICAgICAgICByZXR1cm4gJyNlMGUwZTAnOw0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDlsIbpopzoibLovazmjaLkuLpSR0INCiAgICAgIGxldCByID0gcGFyc2VJbnQoY29sb3Iuc2xpY2UoMSwgMyksIDE2KTsNCiAgICAgIGxldCBnID0gcGFyc2VJbnQoY29sb3Iuc2xpY2UoMywgNSksIDE2KTsNCiAgICAgIGxldCBiID0gcGFyc2VJbnQoY29sb3Iuc2xpY2UoNSwgNyksIDE2KTsNCiAgICAgIA0KICAgICAgLy8g6LCD5pW05Lqu5bqmDQogICAgICByID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMjU1LCByICsgYW1vdW50KSk7DQogICAgICBnID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMjU1LCBnICsgYW1vdW50KSk7DQogICAgICBiID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMjU1LCBiICsgYW1vdW50KSk7DQogICAgICANCiAgICAgIC8vIOi9rOWbnuWNgeWFrei/m+WItg0KICAgICAgcmV0dXJuIGAjJHtyLnRvU3RyaW5nKDE2KS5wYWRTdGFydCgyLCAnMCcpfSR7Zy50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKX0ke2IudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJyl9YDsNCiAgICB9LA0KICAgIA0KICAgIC8vIOWIneWni+WMluaMh+agh+WNoeeJh+a7muWKqA0KICAgIGluaXRJbmRpY2F0b3JDYXJkc1Njcm9sbCgpIHsNCiAgICAgIC8vIOa4hemZpOS5i+WJjeeahOa7muWKqOWumuaXtuWZqA0KICAgICAgaWYgKHRoaXMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxUaW1lcikgew0KICAgICAgICBjbGVhckludGVydmFsKHRoaXMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxUaW1lcik7DQogICAgICB9DQogICAgICANCiAgICAgIGNvbnN0IGNvbnRhaW5lciA9IHRoaXMuJHJlZnMuaW5kaWNhdG9yQ2FyZHNDb250YWluZXI7DQogICAgICBpZiAoIWNvbnRhaW5lcikgcmV0dXJuOw0KICAgICAgDQogICAgICAvLyDorqHnrpfmu5rliqjmiYDpnIDlj4LmlbANCiAgICAgIGxldCBzY3JvbGxUb3AgPSAwOw0KICAgICAgY29uc3Qgc2Nyb2xsSGVpZ2h0ID0gY29udGFpbmVyLnNjcm9sbEhlaWdodDsNCiAgICAgIGNvbnN0IGNsaWVudEhlaWdodCA9IGNvbnRhaW5lci5jbGllbnRIZWlnaHQ7DQogICAgICBjb25zdCBtYXhTY3JvbGwgPSBzY3JvbGxIZWlnaHQgLSBjbGllbnRIZWlnaHQ7DQogICAgICANCiAgICAgIC8vIOWmguaenOWGheWuueS4jei2s+S7pea7muWKqO+8jOebtOaOpei/lOWbng0KICAgICAgaWYgKG1heFNjcm9sbCA8PSAwKSByZXR1cm47DQogICAgICANCiAgICAgIC8vIOa7muWKqOatpemVv+WSjOmAn+W6pg0KICAgICAgY29uc3Qgc3RlcCA9IDAuNTsgLy8g5rua5Yqo5q2l6ZW/5pu05bCP77yM5L2/5rua5Yqo5pu05bmz5ruRDQogICAgICBjb25zdCBzY3JvbGxJbnRlcnZhbCA9IDIwOyAvLyDmu5rliqjmm7TmlrDpopHnjofmm7Tpq5jvvIzmm7TmtYHnlYUNCiAgICAgIA0KICAgICAgdGhpcy5pbmRpY2F0b3JDYXJkc1Njcm9sbFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAvLyDpvKDmoIfmgqzlgZzml7bmmoLlgZzmu5rliqgNCiAgICAgICAgaWYgKHRoaXMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxQYXVzZWQpIHJldHVybjsNCiAgICAgICAgDQogICAgICAgIHNjcm9sbFRvcCArPSBzdGVwOw0KICAgICAgICANCiAgICAgICAgLy8g5b2T5rua5Yqo5Yiw5bqV6YOo5pe277yM5b+r6YCf5Zue5Yiw6aG26YOo5bm257un57ut5rua5YqoDQogICAgICAgIGlmIChzY3JvbGxUb3AgPj0gbWF4U2Nyb2xsKSB7DQogICAgICAgICAgLy8g6YeN572u5rua5Yqo5L2N572u5Yiw6aG26YOoDQogICAgICAgICAgc2Nyb2xsVG9wID0gMDsNCiAgICAgICAgICBjb250YWluZXIuc2Nyb2xsVG9wID0gMDsNCiAgICAgICAgICANCiAgICAgICAgICAvLyDnn63mmoLmmoLlgZzkuIDkuIvvvIzorqnnlKjmiLfog73nnIvliLDlm57liLDpobbpg6jnmoTov4fnqIsNCiAgICAgICAgICB0aGlzLmluZGljYXRvckNhcmRzU2Nyb2xsUGF1c2VkID0gdHJ1ZTsNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxQYXVzZWQgPSBmYWxzZTsNCiAgICAgICAgICB9LCAxMDAwKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb250YWluZXIuc2Nyb2xsVG9wID0gc2Nyb2xsVG9wOw0KICAgICAgICB9DQogICAgICB9LCBzY3JvbGxJbnRlcnZhbCk7DQogICAgfSwNCg0KICAgIC8vIOihqOWktOagt+W8jw0KICAgIGhlYWRlckNlbGxTdHlsZSgpIHsNCiAgICAgIHJldHVybiB7DQogICAgICAgIGJhY2tncm91bmRDb2xvcjogIiNmNWY3ZmEiLA0KICAgICAgICBjb2xvcjogIiM2MDYyNjYiLA0KICAgICAgICBmb250V2VpZ2h0OiAiYm9sZCIsDQogICAgICB9Ow0KICAgIH0sDQoNCiAgICAgICAgLy8g5pi+56S65oyH5qCH6K+m5oOFDQogICAgICAgIHNob3dEZXRhaWxzKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50SW5kaWNhdG9yID0gcm93Ow0KICAgICAgdGhpcy5kZXRhaWxEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0RGV0YWlsQ2hhcnQoKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDliJ3lp4vljJbor6bmg4Xlm77ooagNCiAgICBpbml0RGV0YWlsQ2hhcnQoKSB7DQogICAgICBpZiAoIXRoaXMuY3VycmVudEluZGljYXRvcikgcmV0dXJuOw0KDQogICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCJpbmRpY2F0b3JDaGFydCIpOw0KICAgICAgY29uc3QgbXlDaGFydCA9IGVjaGFydHMuaW5pdChjaGFydERvbSk7DQoNCiAgICAgIGNvbnN0IG1vbnRocyA9IFsiMeaciCIsICIy5pyIIiwgIjPmnIgiLCAiNOaciCIsICI15pyIIl07DQogICAgICANCiAgICAgIC8vIOeUn+aIkOazouWKqOi+g+Wkp+eahOaVsOaNru+8jOehruS/neWbtOe7leebruagh+WAvOacieaYjuaYvui1t+S8jw0KICAgICAgY29uc3QgdGFyZ2V0VmFsdWUgPSBwYXJzZUZsb2F0KHRoaXMuY3VycmVudEluZGljYXRvci50YXJnZXQpIHx8IDEwMDsNCiAgICAgIA0KICAgICAgLy8g5L2/55So5a6e6ZmF5pWw5o2u77yM5aaC5p6c5rKh5pyJ5YiZ55Sf5oiQ5rOi5Yqo5pWw5o2uDQogICAgICBsZXQgYWN0dWFsRGF0YSA9IFtdOw0KICAgICAgaWYgKHRoaXMuY3VycmVudEluZGljYXRvci5tb250aGx5RGF0YSAmJiB0aGlzLmN1cnJlbnRJbmRpY2F0b3IubW9udGhseURhdGEubGVuZ3RoID09PSA1KSB7DQogICAgICAgIGFjdHVhbERhdGEgPSB0aGlzLmN1cnJlbnRJbmRpY2F0b3IubW9udGhseURhdGE7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDnlJ/miJDms6LliqjmlbDmja7vvIznoa7kv53mnInotbfkvI8NCiAgICAgICAgY29uc3QgZmx1Y3R1YXRpb25SYW5nZSA9IHRhcmdldFZhbHVlICogMC4zOyAvLyDms6LliqjojIPlm7TkuLrnm67moIflgLznmoQzMCUNCiAgICAgICAgYWN0dWFsRGF0YSA9IG1vbnRocy5tYXAoKCkgPT4gew0KICAgICAgICAgIGNvbnN0IGZsdWN0dWF0aW9uID0gKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMiAqIGZsdWN0dWF0aW9uUmFuZ2U7DQogICAgICAgICAgcmV0dXJuIE1hdGgubWF4KDAsIHRhcmdldFZhbHVlICsgZmx1Y3R1YXRpb24pOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g55uu5qCH5YC857q/DQogICAgICBjb25zdCB0YXJnZXREYXRhID0gQXJyYXkoNSkuZmlsbCh0YXJnZXRWYWx1ZSk7DQoNCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgdGl0bGU6IHsNCiAgICAgICAgICB0ZXh0OiBgJHt0aGlzLmN1cnJlbnRJbmRpY2F0b3IubmFtZX3mnIjluqbotovlir9gLA0KICAgICAgICAgIGxlZnQ6ICJjZW50ZXIiLA0KICAgICAgICB9LA0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogImF4aXMiLA0KICAgICAgICAgIGF4aXNQb2ludGVyOiB7DQogICAgICAgICAgICB0eXBlOiAiY3Jvc3MiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgIGNvbnN0IGFjdHVhbFZhbHVlID0gcGFyYW1zWzBdLnZhbHVlLnRvRml4ZWQoMik7DQogICAgICAgICAgICBjb25zdCB0YXJnZXRWYWx1ZSA9IHBhcmFtc1sxXS52YWx1ZS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgY29uc3QgZGlmZiA9IChhY3R1YWxWYWx1ZSAtIHRhcmdldFZhbHVlKS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgLy8g5L2/55So57uf5LiA55qE55m96Imy5pi+56S65beu5YC8DQogICAgICAgICAgICBjb25zdCBkaWZmQ29sb3IgPSAnY29sb3I6I2ZmZmZmZic7DQogICAgICAgICAgICANCiAgICAgICAgICAgIHJldHVybiBgJHtwYXJhbXNbMF0ubmFtZX08YnIvPg0KICAgICAgICAgICAgICAgICAgICR7cGFyYW1zWzBdLm1hcmtlcn0gJHtwYXJhbXNbMF0uc2VyaWVzTmFtZX06ICR7YWN0dWFsVmFsdWV9PGJyLz4NCiAgICAgICAgICAgICAgICAgICAke3BhcmFtc1sxXS5tYXJrZXJ9ICR7cGFyYW1zWzFdLnNlcmllc05hbWV9OiAke3RhcmdldFZhbHVlfTxici8+DQogICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9IiR7ZGlmZkNvbG9yfSI+5beu5YC8OiAke2RpZmZ9PC9zcGFuPmA7DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICBkYXRhOiBbIuWunumZheWAvCIsICLnm67moIflgLwiXSwNCiAgICAgICAgICBib3R0b206IDEwLA0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7DQogICAgICAgICAgbGVmdDogIjMlIiwNCiAgICAgICAgICByaWdodDogIjQlIiwNCiAgICAgICAgICBib3R0b206ICIxNSUiLA0KICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZSwNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAiY2F0ZWdvcnkiLA0KICAgICAgICAgIGJvdW5kYXJ5R2FwOiBmYWxzZSwNCiAgICAgICAgICBkYXRhOiBtb250aHMsDQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogInZhbHVlIiwNCiAgICAgICAgICBuYW1lOiB0aGlzLmN1cnJlbnRJbmRpY2F0b3IudW5pdCwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGZvcm1hdHRlcjogInt2YWx1ZX0iLA0KICAgICAgICAgIH0sDQogICAgICAgICAgc2NhbGU6IHRydWUsIC8vIOe8qeaUvlnovbTku6XnqoHlh7rmmL7npLrmlbDmja7ms6LliqgNCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIuWunumZheWAvCIsDQogICAgICAgICAgICB0eXBlOiAibGluZSIsDQogICAgICAgICAgICBkYXRhOiBhY3R1YWxEYXRhLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAiIzQwOUVGRiIsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIHdpZHRoOiAzLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHN5bWJvbDogImNpcmNsZSIsDQogICAgICAgICAgICBzeW1ib2xTaXplOiA4LA0KICAgICAgICAgICAgbWFya1BvaW50OiB7DQogICAgICAgICAgICAgIGRhdGE6IFsNCiAgICAgICAgICAgICAgICB7IHR5cGU6ICJtYXgiLCBuYW1lOiAi5pyA5aSn5YC8IiB9LA0KICAgICAgICAgICAgICAgIHsgdHlwZTogIm1pbiIsIG5hbWU6ICLmnIDlsI/lgLwiIH0NCiAgICAgICAgICAgICAgXQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIuebruagh+WAvCIsDQogICAgICAgICAgICB0eXBlOiAibGluZSIsDQogICAgICAgICAgICBkYXRhOiB0YXJnZXREYXRhLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAiI0Y1NkM2QyIsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIHdpZHRoOiAyLA0KICAgICAgICAgICAgICB0eXBlOiAiZGFzaGVkIiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBtYXJrTGluZTogew0KICAgICAgICAgICAgICBkYXRhOiBbeyB0eXBlOiAiYXZlcmFnZSIsIG5hbWU6ICLnm67moIflgLwiIH1dLA0KICAgICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICAgIGZvcm1hdHRlcjogIuebruagh+WAvDoge2N9Ig0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgIH07DQoNCiAgICAgIG15Q2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7DQoNCiAgICAgIC8vIOWTjeW6lOW8j+WkhOeQhg0KICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoInJlc2l6ZSIsICgpID0+IHsNCiAgICAgICAgbXlDaGFydC5yZXNpemUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDlhbPpl63lr7nor53moYYNCiAgICBoYW5kbGVEaWFsb2dDbG9zZSgpIHsNCiAgICAgIHRoaXMuZGV0YWlsRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5jdXJyZW50SW5kaWNhdG9yID0gbnVsbDsNCiAgICAgIC8vIOa4hemZpOWbvuihqOWunuS+iw0KICAgICAgY29uc3QgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgiaW5kaWNhdG9yQ2hhcnQiKTsNCiAgICAgIGlmIChjaGFydERvbSkgew0KICAgICAgICBjb25zdCBjaGFydCA9IGVjaGFydHMuZ2V0SW5zdGFuY2VCeURvbShjaGFydERvbSk7DQogICAgICAgIGlmIChjaGFydCkgew0KICAgICAgICAgIGNoYXJ0LmRpc3Bvc2UoKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgICAgIC8vIOWkhOeQhuihqOagvOWNleWFg+agvOWQiOW5tg0KICAgICAgICBvYmplY3RTcGFuTWV0aG9kKHsgcm93LCBjb2x1bW4sIHJvd0luZGV4LCBjb2x1bW5JbmRleCB9KSB7DQogICAgICAvLyDku4Xlr7nliIbljoLlkozmjIfmoIflkI3np7DliJfov5vooYzlkIjlubYNCiAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gMCB8fCBjb2x1bW5JbmRleCA9PT0gMSkgew0KICAgICAgICAvLyDojrflj5blvZPliY3ooYzmlbDmja4NCiAgICAgICAgY29uc3QgY3VycmVudFJvdyA9IHRoaXMudGVjaEluZGljYXRvcnNbcm93SW5kZXhdOw0KICAgICAgICBpZiAoIWN1cnJlbnRSb3cpIHJldHVybiB7IHJvd3NwYW46IDEsIGNvbHNwYW46IDEgfTsNCg0KICAgICAgICAvLyDliIbljoLliJflkIjlubblpITnkIYNCiAgICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAwKSB7DQogICAgICAgICAgLy8g5aaC5p6c5piv56ys5LiA6KGM5oiW6ICF5LiO5YmN5LiA6KGM5YiG5Y6C5LiN5ZCM77yM5YiZ6K6h566X5ZCI5bm26KGM5pWwDQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgcm93SW5kZXggPT09IDAgfHwNCiAgICAgICAgICAgIGN1cnJlbnRSb3cuZmFjdG9yeSAhPT0gdGhpcy50ZWNoSW5kaWNhdG9yc1tyb3dJbmRleCAtIDFdLmZhY3RvcnkNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIC8vIOiuoeeul+i/nue7reebuOWQjOWIhuWOgueahOihjOaVsA0KICAgICAgICAgICAgbGV0IHJvd3NwYW4gPSAxOw0KICAgICAgICAgICAgZm9yIChsZXQgaSA9IHJvd0luZGV4ICsgMTsgaSA8IHRoaXMudGVjaEluZGljYXRvcnMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgICAgaWYgKHRoaXMudGVjaEluZGljYXRvcnNbaV0uZmFjdG9yeSA9PT0gY3VycmVudFJvdy5mYWN0b3J5KSB7DQogICAgICAgICAgICAgICAgcm93c3BhbisrOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICByZXR1cm4geyByb3dzcGFuLCBjb2xzcGFuOiAxIH07DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWmguaenOW9k+WJjeihjOS4juWJjeS4gOihjOWIhuWOguebuOWQjO+8jOWImemakOiXj+W9k+WJjeWNleWFg+agvA0KICAgICAgICAgICAgcmV0dXJuIHsgcm93c3BhbjogMCwgY29sc3BhbjogMCB9Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOaMh+agh+WQjeensOWIl+WQiOW5tuWkhOeQhg0KICAgICAgICBpZiAoY29sdW1uSW5kZXggPT09IDEpIHsNCiAgICAgICAgICAvLyDlpoLmnpzmmK/nrKzkuIDooYzvvIzmiJbogIXkuI7liY3kuIDooYzliIbljoLkuI3lkIzvvIzmiJbogIXliIbljoLnm7jlkIzkvYbmjIfmoIflkI3np7DkuI3lkIwNCiAgICAgICAgICBpZiAoDQogICAgICAgICAgICByb3dJbmRleCA9PT0gMCB8fA0KICAgICAgICAgICAgY3VycmVudFJvdy5mYWN0b3J5ICE9PSB0aGlzLnRlY2hJbmRpY2F0b3JzW3Jvd0luZGV4IC0gMV0uZmFjdG9yeSB8fA0KICAgICAgICAgICAgY3VycmVudFJvdy5uYW1lICE9PSB0aGlzLnRlY2hJbmRpY2F0b3JzW3Jvd0luZGV4IC0gMV0ubmFtZQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgLy8g6K6h566X6L+e57ut55u45ZCM5YiG5Y6C5ZKM5oyH5qCH5ZCN56ew55qE6KGM5pWwDQogICAgICAgICAgICBsZXQgcm93c3BhbiA9IDE7DQogICAgICAgICAgICBmb3IgKGxldCBpID0gcm93SW5kZXggKyAxOyBpIDwgdGhpcy50ZWNoSW5kaWNhdG9ycy5sZW5ndGg7IGkrKykgew0KICAgICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgICAgdGhpcy50ZWNoSW5kaWNhdG9yc1tpXS5mYWN0b3J5ID09PSBjdXJyZW50Um93LmZhY3RvcnkgJiYNCiAgICAgICAgICAgICAgICB0aGlzLnRlY2hJbmRpY2F0b3JzW2ldLm5hbWUgPT09IGN1cnJlbnRSb3cubmFtZQ0KICAgICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgICByb3dzcGFuKys7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJldHVybiB7IHJvd3NwYW4sIGNvbHNwYW46IDEgfTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5aaC5p6c5b2T5YmN6KGM5LiO5YmN5LiA6KGM5YiG5Y6C5ZKM5oyH5qCH5ZCN56ew6YO955u45ZCM77yM5YiZ6ZqQ6JeP5b2T5YmN5Y2V5YWD5qC8DQogICAgICAgICAgICByZXR1cm4geyByb3dzcGFuOiAwLCBjb2xzcGFuOiAwIH07DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4geyByb3dzcGFuOiAxLCBjb2xzcGFuOiAxIH07DQogICAgfSwNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["dimensionalityOverview.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dimensionalityOverview.vue", "sourceRoot": "src/views/dataReport/form", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-title\">\r\n      <h2>技经能源指标报表</h2>\r\n    </div>\r\n\r\n\r\n        <!-- 能源指标部分 -->\r\n    <el-card class=\"box-card energy-section\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"clearfix section-header\">\r\n        <span>能源指标概览</span>\r\n      </div>\r\n      <div class=\"chart-container\">\r\n        <!-- 能源报表一行显示 -->\r\n        <div class=\"energy-charts-row\">\r\n          <!-- 部门能源消耗详情（原图四，现在放在第一位置） -->\r\n          <div class=\"chart-item energy-chart-half\">\r\n            <div class=\"chart-title\">\r\n              <span>部门能源消耗详情</span>\r\n              <div class=\"energy-dept-selector\">\r\n                <el-select v-model=\"currentEnergyDept\" size=\"small\" placeholder=\"选择部门\" @change=\"initEnergyDetailCharts\">\r\n                  <el-option\r\n                    v-for=\"factory in allFactories\"\r\n                    :key=\"factory\"\r\n                    :label=\"factory\"\r\n                    :value=\"factory\">\r\n                  </el-option>\r\n                </el-select>\r\n              </div>\r\n            </div>\r\n            <div class=\"energy-subchart-container\">\r\n              <div v-for=\"type in energyTypes\" :key=\"type.value\" \r\n                  class=\"energy-subchart\">\r\n                <div class=\"subchart-title\">{{ type.label }}</div>\r\n                <div :id=\"'energySubchart_' + type.value\" class=\"subchart\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 部门能源消耗趋势（原图一，现在放在第三位置） -->\r\n          <div class=\"chart-item energy-chart-half\">\r\n            <div class=\"chart-title\">\r\n              <span>能源数据监控</span>\r\n            </div>\r\n            <div class=\"key-indicators-container\">\r\n              <div \r\n                v-for=\"(indicator, index) in keyEnergyIndicators\" \r\n                :key=\"index\" \r\n                class=\"indicator-card\"\r\n                :class=\"[indicator.status, {'danger': indicator.change < -50 || indicator.change > 50}]\"\r\n              >\r\n                <div class=\"indicator-title\">{{ indicator.name }}</div>\r\n                <div class=\"indicator-value\">{{ indicator.today }} <span class=\"indicator-unit\">{{ indicator.unit }}</span></div>\r\n                <!-- <div class=\"indicator-target\">\r\n                  目标范围: {{ indicator.targetMin }} ~ {{ indicator.targetMax }} {{ indicator.unit }}\r\n                </div> -->\r\n                <div class=\"indicator-compare\">\r\n                  <span>昨日: {{ indicator.yesterday }}</span>\r\n                  <!-- <span \r\n                    class=\"indicator-change\" \r\n                    :class=\"{ 'up': indicator.change > 0, 'down': indicator.change < 0 }\"\r\n                  >\r\n                    {{ indicator.change > 0 ? '+' : '' }}{{ indicator.change }}%\r\n                  </span> -->\r\n                </div>\r\n                <div class=\"indicator-target\">\r\n                  <span \r\n                    class=\"indicator-change\" \r\n                    :class=\"{ 'up': indicator.change > 0, 'down': indicator.change < 0 }\"\r\n                  >\r\n                   环比变化:{{ indicator.change > 0 ? '+' : '' }}{{ indicator.change }}%\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <el-card class=\"box-card tech-economic-section\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>技经指标数据表格</span>\r\n        <el-button\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          style=\"float: right; margin-left: 10px\"\r\n          @click=\"loadExcelFromRemote\"\r\n          :loading=\"excelLoading\"\r\n        >\r\n          {{ excelLoading ? \"正在加载...\" : \"重新加载数据\" }}\r\n        </el-button>\r\n      </div>\r\n      <el-table\r\n        :data=\"techIndicators\"\r\n        border\r\n        style=\"width: 100%\"\r\n        :header-cell-style=\"headerCellStyle\"\r\n        :span-method=\"objectSpanMethod\"\r\n      >\r\n        <el-table-column\r\n          prop=\"factory\"\r\n          label=\"分厂\"\r\n          width=\"250\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"name\"\r\n          label=\"指标名称\"\r\n          width=\"280\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: '#409EFF',\r\n                fontWeight: 'bold',\r\n                backgroundColor: scope.row.highlight\r\n                  ? '#a9d3ff'\r\n                  : 'transparent',\r\n              }\"\r\n              >{{ scope.row.name }}</span\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"target\"\r\n          label=\"目标\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"unit\"\r\n          label=\"单位\"\r\n          width=\"150\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n        <!-- <el-table-column prop=\"jan\" label=\"01月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.janStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.jan }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"feb\" label=\"02月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.febStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.feb }}\r\n            </span>\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column prop=\"mar\" label=\"03月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.marStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.mar }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"apr\" label=\"04月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.aprStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.apr }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"may\" label=\"05月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.mayStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.may }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-view\"\r\n              circle\r\n              @click=\"showDetails(scope.row)\"\r\n            ></el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-card>\r\n\r\n        <!-- 指标详情对话框 -->\r\n    <el-dialog\r\n      title=\"指标详情\"\r\n      :visible.sync=\"detailDialogVisible\"\r\n      width=\"70%\"\r\n      :before-close=\"handleDialogClose\"\r\n    >\r\n      <div v-if=\"currentIndicator\">\r\n        <h3>{{ currentIndicator.name }} ({{ currentIndicator.unit }})</h3>\r\n        <div class=\"indicator-info\">\r\n          <p>分厂: {{ currentIndicator.factory }}</p>\r\n          <p>目标值: {{ currentIndicator.target }}</p>\r\n          <p>当前值: {{ currentIndicator.may }}</p>\r\n        </div>\r\n        <div id=\"indicatorChart\" style=\"width: 100%; height: 400px\"></div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport axios from \"axios\";\r\nimport * as XLSX from \"xlsx\";\r\nimport {\r\n  dimensionalitylistPermissionList\r\n} from \"@/api/tYjy/dimensionality\";\r\nimport { dateUpdateList } from \"@/api/tYjy/answer\";\r\nexport default {\r\n  name: 'DimensionalityOverview',\r\n  data() {\r\n    return {\r\n\r\n      // 技经指标数据\r\n      techIndicators: [],\r\n\r\n      detailDialogVisible: false,\r\n      currentIndicator: null,\r\n      // Excel文件加载状态\r\n      excelLoading: false,\r\n      adminShow:\"0\",\r\n\r\n      // 当前显示的部门（用于月度趋势图切换）\r\n      // currentBusinessUnit: '炼铁事业部', // 当前选择的事业部\r\n      // currentDepartment: '',\r\n      // currentIndicator: '',\r\n      // autoSwitchDepartment: true,\r\n      // 定时器\r\n      trendChartTimer: null, // 用于分厂切换\r\n      businessUnitTimer: null, // 用于事业部切换\r\n      scrollTimer: null,\r\n      tableScrollPaused: false, // 是否暂停表格自动滚动\r\n      completionChartTimer: null, // 用于完成率图表滚动\r\n      indicatorCardsScrollTimer: null, // 用于指标卡片滚动\r\n      completionChartScrollDirection: 'down', // 滚动方向：'up' 或 'down'\r\n      completionChartScrollPaused: false, // 是否暂停自动滚动\r\n      indicatorCardsScrollPaused: false, // 是否暂停指标卡片自动滚动\r\n  \r\n      energyDeptTimer: null, // 能源部门切换定时器\r\n      scrollSpeed: 50, // 滚动速度，数值越大速度越慢\r\n      \r\n      // 事件处理器引用\r\n      chartMouseOverHandler: null,\r\n      chartMouseOutHandler: null,\r\n      tableMouseEnterHandler: null, // 表格鼠标进入处理器\r\n      tableMouseLeaveHandler: null, // 表格鼠标离开处理器\r\n      \r\n      // 当前选择的能源类型和部门\r\n      currentEnergyType: 'electricity',\r\n      currentEnergyDept: '炼铁分厂', // 修改为分厂名称\r\n      \r\n      // 部门列表\r\n      departments: ['炼铁事业部', '炼钢事业部', '轧钢事业部', '马科托钢球', '特板事业部', '动力事业部', '物流事业部', '检修事业部'],\r\n      \r\n      // 指标完成情况数据 - 从图片中提取的数据\r\n      completionData: [\r\n        { department: '炼铁事业部-炼铁分厂', indicators: [\r\n          { name: '综合燃料比', target: 512.33, actual: 523.45, unit: 'kg/t', isHigherBetter: false, values: [520.26, 523.67, 523.45, 519.06], completionRates: ['-1.55%', '-2.00%', '-2.17%', '-1.31%'] },\r\n          { name: '工序能耗', target: 369.91, actual: 369.69, unit: 'kgCe/t', isHigherBetter: false, values: [369.74, 367.98, 369.69, 363.76], completionRates: ['0.05%', '0.52%', '0.06%', '1.66%'] },\r\n          { name: '400高炉工序能耗', target: 43.70, actual: 43.56, unit: 'kgCe/t', isHigherBetter: false, values: [43.67, 43.67, 43.56, 43.67], completionRates: ['0.07%', '0.07%', '0.32%', '0.07%'] },\r\n          { name: '360高炉工序能耗', target: 45.01, actual: 44.84, unit: 'kgCe/t', isHigherBetter: false, values: [45.00, 44.93, 44.84, 44.99], completionRates: ['0.02%', '0.07%', '0.38%', '0.04%'] }\r\n        ]},\r\n        { department: '炼铁事业部-烧结分厂', indicators: [\r\n          { name: '工序能耗', target: 154.65, actual: 154.56, unit: 'kgCe/t', isHigherBetter: false, values: [154.65, 154.91, 154.56, 154.57], completionRates: ['0.01%', '0.03%', '0.06%', '0.05%'] },\r\n          { name: '矿渣增幅', target: 16.00, actual: 15.80, unit: 'kgCe/t', isHigherBetter: false, values: [15.82, 15.94, 15.80, 15.85], completionRates: ['0.50%', '0.38%', '1.25%', '0.94%'] },\r\n        ]},\r\n        { department: '炼钢事业部-一炼钢', indicators: [\r\n          { name: '综合石灰消耗', target: 42.95, actual: 42.89, unit: 'kg/t', isHigherBetter: false, values: [42.94, 42.82, 42.89, 40.55], completionRates: ['0.02%', '0.30%', '0.14%', '5.59%'] },\r\n          { name: '氧气消耗', target: 44.56, actual: 44.45, unit: 'm3/t', isHigherBetter: false, values: [44.50, 44.34, 44.45, 44.37], completionRates: ['0.13%', '0.49%', '0.25%', '0.43%'] },\r\n          { name: '电炉工序能耗', target: 57.50, actual: 57.32, unit: 'kgCe/t', isHigherBetter: false, values: [57.34, 57.33, 57.32, 57.19], completionRates: ['0.28%', '0.30%', '0.31%', '0.54%'] },\r\n          { name: '钢铁料收得率', target: 91.50, actual: 91.32, unit: '%', isHigherBetter: true, values: [91.51, 91.32, 91.32, 91.32], completionRates: ['0.01%', '-0.20%', '-0.20%', '-0.20%'] }\r\n        ]},\r\n        { department: '炼钢事业部-二炼钢', indicators: [\r\n          { name: '综合石灰消耗', target: 50.00, actual: 48.95, unit: 'kg/t', isHigherBetter: false, values: [49.90, 49.40, 48.95, 49.00], completionRates: ['0.20%', '1.08%', '2.10%', '2.00%'] },\r\n          { name: '氧气消耗', target: 45.65, actual: 45.26, unit: 'm3/t', isHigherBetter: false, values: [45.49, 45.37, 45.26, 45.35], completionRates: ['0.35%', '0.39%', '0.85%', '0.66%'] },\r\n          { name: '转炉工序能耗', target: -28.50, actual: -29.52, unit: 'kgCe/t', isHigherBetter: true, values: [29.78, 29.57, 29.52, 29.61], completionRates: ['-0.98%', '-3.75%', '-3.58%', '-3.89%'] },\r\n          { name: '钢铁料收得率', target: 91.50, actual: 91.16, unit: '%', isHigherBetter: true, values: [91.50, 91.32, 91.16, 91.15], completionRates: ['0.00%', '-0.20%', '-0.37%', '-0.38%'] }\r\n        ]},\r\n        { department: '轧钢事业部-综合利用', indicators: [\r\n          { name: '废金金属量', target: 0.65, actual: 0.64, unit: '%', isHigherBetter: false, values: [0.65, 0.53, 0.64, 0.49], completionRates: ['0.00%', '18.40%', '1.54%', '-26.15%'] },\r\n          { name: '电耗', target: 22.33, actual: 23.81, unit: 'kWh/t', isHigherBetter: false, values: [22.33, 23.07, 23.81, 21.19], completionRates: ['0.45%', '-3.31%', '-6.62%', '5.11%'] }\r\n        ]},\r\n        { department: '轧钢事业部-一轧钢', indicators: [\r\n          { name: '热轧综合成材率', target: 96.35, actual: 96.22, unit: '%', isHigherBetter: true, values: [96.23, 96.25, 96.22, 96.24], completionRates: ['-0.12%', '-0.10%', '-0.13%', '-0.11%'] },\r\n          { name: '热轧钢材工序能耗', target: 58.55, actual: 58.51, unit: 'kgCe/t', isHigherBetter: false, values: [58.47, 58.52, 58.51, 58.45], completionRates: ['0.14%', '0.05%', '0.07%', '0.17%'] }\r\n        ]},\r\n        { department: '轧钢事业部-二轧钢', indicators: [\r\n          { name: '热轧综合成材率(大棒)', target: 95.37, actual: 95.37, unit: '%', isHigherBetter: true, values: [95.37, 95.22, 95.37, 95.37], completionRates: ['0.00%', '-0.15%', '0.00%', '0.00%'] },\r\n          { name: '热轧综合成材率(小棒)', target: 96.56, actual: 96.56, unit: '%', isHigherBetter: true, values: [96.39, 96.56, 96.56, 96.56], completionRates: ['-0.17%', '0.00%', '0.00%', '0.00%'] },\r\n          { name: '热轧钢材工序能耗(大棒)', target: 67.28, actual: 72.49, unit: 'kgCe/t', isHigherBetter: false, values: [71.35, 73.80, 72.49, 66.24], completionRates: ['-0.93%', '0.75%', '0.00%', '-0.25%'] },\r\n          { name: '热轧钢材工序能耗(小棒)', target: 42.05, actual: 42.02, unit: 'kgCe/t', isHigherBetter: false, values: [42.03, 42.05, 42.02, 45.68], completionRates: ['0.05%', '0.05%', '0.05%', '0.34%'] }\r\n        ]},\r\n        { department: '轧钢事业部-三轧钢', indicators: [\r\n          { name: '热轧综合成材率', target: 96.04, actual: 95.50, unit: '%', isHigherBetter: true, values: [95.76, 96.30, 95.50, 95.51], completionRates: ['-0.28%', '0.44%', '-0.50%', '-0.50%'] },\r\n          { name: '热轧钢材工序能耗', target: 56.31, actual: 54.67, unit: 'kgCe/t', isHigherBetter: false, values: [55.26, 56.34, 54.67, 55.19], completionRates: ['-0.79%', '0.71%', '-1.33%', '-1.18%'] }\r\n        ]},\r\n        { department: '轧钢事业部-特殊钢轧材', indicators: [\r\n          { name: '热轧钢材工序能耗', target: 67.04, actual: 68.64, unit: 'kgCe/t', isHigherBetter: false, values: [67.35, 64.09, 68.64, 64.77], completionRates: ['0.46%', '-3.26%', '0.00%', '-0.23%'] },\r\n          { name: '综合成材率', target: 96.73, actual: 96.73, unit: '%', isHigherBetter: true, values: [96.73, 96.79, 96.73, 96.45], completionRates: ['0.00%', '0.06%', '0.00%', '-0.28%'] }\r\n        ]},\r\n        { department: '轧钢事业部-棒材轧制厂', indicators: [\r\n          { name: '热轧钢材工序能耗(棒扁)', target: 56.93, actual: 61.81, unit: 'kgCe/t', isHigherBetter: false, values: [66.14, 60.00, 61.81, 59.96], completionRates: ['-0.91%', '-1.35%', '0.00%', '-0.24%'] },\r\n          { name: '热轧钢材工序能耗(大盘)', target: 57.08, actual: 61.28, unit: 'kgCe/t', isHigherBetter: false, values: [64.30, 60.29, 61.28, 60.02], completionRates: ['-0.19%', '-0.15%', '0.00%', '-0.26%'] },\r\n          { name: '综合成材率(棒扁轧材)', target: 96.45, actual: 96.12, unit: '%', isHigherBetter: true, values: [96.14, 96.11, 96.12, 96.03], completionRates: ['-0.31%', '-0.33%', '0.00%', '-0.29%'] },\r\n          { name: '综合成材率(大盘卷)', target: 95.85, actual: 95.84, unit: '%', isHigherBetter: true, values: [95.86, 95.90, 95.84, 95.87], completionRates: ['0.01%', '0.04%', '0.00%', '0.02%'] }\r\n        ]},\r\n        { department: '轧钢事业部-棒线材深加工(棒)', indicators: [\r\n          { name: '综合成材率', target: 92.60, actual: 92.60, unit: '%', isHigherBetter: true, values: [92.60, 92.60, 92.60, 92.60], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '轧钢事业部-棒线材深加工(线)', indicators: [\r\n          { name: '控线材综合成材率', target: 98.55, actual: 98.56, unit: '%', isHigherBetter: true, values: [98.55, 98.55, 98.56, 98.55], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '轧钢事业部-线材深加工', indicators: [\r\n          { name: '冷镦材综合成材率', target: 96.36, actual: 96.02, unit: '%', isHigherBetter: true, values: [96.36, 96.36, 96.02, 94.44], completionRates: ['0.00%', '0.00%', '0.00%', '-1.94%'] }\r\n        ]},\r\n        { department: '马科托钢球-马科托钢球', indicators: [\r\n          { name: '综合成材率', target: 93.19, actual: 93.61, unit: '%', isHigherBetter: true, values: [93.13, 93.54, 93.61, 93.80], completionRates: ['-0.06%', '0.42%', '0.00%', '0.20%'] },\r\n          { name: '氧气消耗', target: 44.64, actual: 44.63, unit: 'm3/t', isHigherBetter: false, values: [44.64, 44.64, 44.63, 44.64], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '特板事业部-特钢炼钢分厂', indicators: [\r\n          { name: '氧气消耗', target: 44.64, actual: 44.63, unit: 'm3/t', isHigherBetter: false, values: [44.64, 44.64, 44.63, 44.64], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] },\r\n          { name: '转炉工序能耗', target: -28.50, actual: -29.91, unit: 'kgCe/t', isHigherBetter: true, values: [28.93, 29.67, 29.91, 29.55], completionRates: ['-0.98%', '-3.75%', '-3.58%', '-3.89%'] },\r\n          { name: '钢铁料收得率', target: 91.60, actual: 91.27, unit: '%', isHigherBetter: true, values: [91.80, 91.31, 91.27, 91.27], completionRates: ['0.22%', '-0.33%', '-0.37%', '-0.37%'] },\r\n          { name: '综合石灰消耗', target: 43.67, actual: 46.07, unit: 'kg/t', isHigherBetter: false, values: [46.01, 47.19, 46.07, 43.97], completionRates: ['2.84%', '3.27%', '2.14%', '2.07%'] }\r\n        ]},\r\n        { department: '特板事业部-中板分厂', indicators: [\r\n          { name: '综合命中率', target: 98.62, actual: 98.63, unit: '%', isHigherBetter: true, values: [98.63, 98.65, 98.63, 98.63], completionRates: ['0.00%', '0.02%', '0.00%', '0.00%'] },\r\n          { name: '综合成材率', target: 92.55, actual: 92.04, unit: '%', isHigherBetter: true, values: [92.63, 92.03, 92.04, 92.65], completionRates: ['0.09%', '-0.51%', '-0.51%', '0.02%'] },\r\n          { name: '整客户交付率', target: 98.75, actual: 98.78, unit: '%', isHigherBetter: true, values: [98.75, 98.77, 98.78, 98.75], completionRates: ['0.00%', '0.02%', '0.00%', '0.00%'] },\r\n          { name: '热轧工序能耗', target: 45.02, actual: 43.32, unit: 'kgCe/t', isHigherBetter: false, values: [44.60, 44.15, 43.32, 43.80], completionRates: ['-0.93%', '-1.25%', '-1.68%', '-0.70%'] },\r\n          { name: '热装比', target: 75.00, actual: 75.85, unit: '%', isHigherBetter: true, values: [75.40, 77.64, 75.85, 74.05], completionRates: ['0.53%', '2.18%', '0.00%', '-1.95%'] }\r\n        ]},\r\n        { department: '特板事业部-厚板分厂', indicators: [\r\n          { name: '综合命中率', target: 97.49, actual: 97.27, unit: '%', isHigherBetter: true, values: [97.49, 97.53, 97.27, 97.52], completionRates: ['0.00%', '0.04%', '-0.26%', '0.05%'] },\r\n          { name: '综合成材率', target: 90.91, actual: 90.76, unit: '%', isHigherBetter: true, values: [90.41, 90.79, 90.76, 90.78], completionRates: ['-0.55%', '0.32%', '-0.26%', '0.02%'] },\r\n          { name: '整客户交付率', target: 96.34, actual: 96.34, unit: '%', isHigherBetter: true, values: [96.37, 96.35, 96.34, 96.31], completionRates: ['0.03%', '-0.02%', '0.00%', '-0.03%'] },\r\n          { name: '热轧工序能耗', target: 48.62, actual: 45.85, unit: 'kgCe/t', isHigherBetter: false, values: [46.27, 46.01, 45.85, 47.11], completionRates: ['-2.79%', '-2.56%', '-2.34%', '-1.54%'] },\r\n          { name: '热装比(200℃)', target: 50.00, actual: 31.23, unit: '%', isHigherBetter: true, values: [50.60, 51.28, 31.23, 50.28], completionRates: ['1.20%', '2.56%', '0.00%', '-1.56%'] }\r\n        ]},\r\n        { department: '特板事业部-钢材深加工', indicators: [\r\n          { name: '整客户交付率', target: 99.11, actual: 99.12, unit: '%', isHigherBetter: true, values: [99.11, 99.10, 99.12, 99.12], completionRates: ['0.00%', '-0.01%', '0.00%', '0.00%'] },\r\n          { name: '综合命中率', target: 99.73, actual: 99.75, unit: '%', isHigherBetter: true, values: [99.73, 99.74, 99.75, 99.74], completionRates: ['0.00%', '-0.01%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-热电', indicators: [\r\n          { name: '标煤产汽率', target: 10.85, actual: 10.90, unit: 't/tCe', isHigherBetter: true, values: [10.87, 10.89, 10.90, 10.92], completionRates: ['0.19%', '0.20%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-供电工区', indicators: [\r\n          { name: '供电功率因数', target: 95.00, actual: 98.00, unit: '%(95.3-100)', isHigherBetter: true, values: [98.66, 98.66, 98.00, 98.00], completionRates: ['3.20%', '3.20%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-水处理分厂', indicators: [\r\n          { name: '吨钢软水水', target: 1.62, actual: 1.63, unit: 'm3/t', isHigherBetter: false, values: [1.62, 1.61, 1.63, 1.69], completionRates: ['0.62%', '-0.62%', '0.00%', '0.62%'] },\r\n          { name: '吨钢热水处理量', target: 21.20, actual: 20.25, unit: 't/t钢', isHigherBetter: false, values: [19.89, 20.14, 20.25, 20.28], completionRates: ['-6.19%', '-3.57%', '0.00%', '1.40%'] }\r\n        ]},\r\n        { department: '动力事业部-制氧分厂', indicators: [\r\n          { name: '氧气就耗率', target: 0.02, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-煤气分厂', indicators: [\r\n          { name: '高炉煤气就耗率', target: 0.02, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '物流事业部-储运公司', indicators: [\r\n          { name: '360混匀矿水分合格率', target: 90.56, actual: 91.32, unit: '%', isHigherBetter: true, values: [90.63, 90.68, 91.32, 90.68], completionRates: ['0.07%', '0.05%', '0.00%', '0.00%'] },\r\n          { name: '混匀矿稳定率', target: 97.82, actual: 97.89, unit: '%', isHigherBetter: true, values: [97.83, 98.01, 97.89, 98.01], completionRates: ['0.18%', '0.17%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '检修事业部-检修分厂', indicators: [\r\n          { name: '热修机率', target: 0.10, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]}\r\n      ],\r\n      completionData1: \r\n      [\r\n      {department:'钙业分厂'},\r\n      {department:'矿渣微粉'},\r\n      {department:'烧结分厂-1#烧结'},\r\n      {department:'烧结分厂-2#烧结'},\r\n      {department:'炼铁分厂-1#高炉'},\r\n      {department:'炼铁分厂-2#高炉'},\r\n      {department:'炼铁分厂-3#高炉'},\r\n      {department:'炼铁分厂-小喷煤'},\r\n      {department:'炼铁分厂-大喷煤'},\r\n      {department:'一炼钢'},\r\n      {department:'二炼钢'},\r\n      {department:'一轧钢'},\r\n      {department:'二轧-大棒'},\r\n      {department:'二轧-小棒'},\r\n      {department:'特板炼钢'},\r\n      {department:'3500中板'},\r\n      {department:'4300厚板'},\r\n      {department:'4300水处理'},\r\n      {department:'热处理'},\r\n      {department:'高线分厂'},\r\n      {department:'线材深加工'},\r\n      {department:'棒材深加工'},\r\n      {department:'热电分厂-热电'},\r\n      {department:'热电分厂-亚临界'},\r\n      {department:'热电分厂-余热'},\r\n      {department:'热电分厂-鼓风'},\r\n      {department:'制氧分厂'},\r\n      {department:'制氧分厂-一期'},\r\n      {department:'制氧分厂-三期'},\r\n      {department:'制氧分厂-空压站'},\r\n      {department:'水处理-一期'},\r\n      {department:'水处理-二期'},\r\n      {department:'水处理-三期'},\r\n      {department:'煤气分厂'},\r\n      {department:'供电一区'},\r\n      {department:'兴澄钢球'},\r\n      {department:'兴澄港务'},\r\n      {department:'储运公司'},\r\n      {department:'综合利用'},\r\n      {department:'合金炉分厂'},\r\n      {department:'物管部'},\r\n      {department:'后勤部'},\r\n      {department:'其他'},\r\n      {department:'损耗'},\r\n      {department:'合计'},\r\n\r\n      ],\r\n      // 未完成指标数据\r\n      incompleteData: [],\r\n      \r\n      // 能源指标统计数据\r\n      energyStats: [\r\n        { title: '综合能耗', value: '5.21吨标煤/吨钢', change: -2.3, status: 'good' },\r\n        { title: '水资源消耗', value: '3.8立方米/吨钢', change: -1.5, status: 'good' },\r\n        { title: '电力消耗', value: '485千瓦时/吨钢', change: 0.8, status: 'warning' },\r\n        { title: '煤气回收率', value: '98.5%', change: 1.2, status: 'good' },\r\n        { title: '余热回收率', value: '75.2%', change: 2.5, status: 'good' },\r\n        { title: '二氧化碳排放', value: '1.85吨/吨钢', change: -3.2, status: 'good' }\r\n      ],\r\n      \r\n      // 能源消耗数据\r\n      energyConsumptionData: [\r\n        { name: '电力', value: 35 },\r\n        { name: '煤炭', value: 25 },\r\n        { name: '天然气', value: 15 },\r\n        { name: '蒸汽', value: 10 },\r\n        { name: '其他', value: 15 }\r\n      ],\r\n      \r\n      // 部门能源消耗数据（新增）- 按月统计\r\n      departmentEnergyData: {\r\n        months: ['1月', '2月', '3月', '4月'],\r\n        departments: ['炼铁事业部', '炼钢事业部', '轧钢事业部', '马科托钢球', '特板事业部', '动力事业部', '物流事业部', '检修事业部'],\r\n        electricity: { // 电力消耗 (万千瓦时)\r\n          '炼铁事业部': [1250, 1180, 1220, 1260],\r\n          '炼钢事业部': [1850, 1790, 1810, 1880],\r\n          '轧钢事业部': [1450, 1420, 1480, 1440],\r\n          '马科托钢球': [420, 410, 430, 425],\r\n          '特板事业部': [980, 950, 970, 990],\r\n          '动力事业部': [320, 310, 330, 325],\r\n          '物流事业部': [180, 175, 185, 182],\r\n          '检修事业部': [150, 145, 155, 152]\r\n        },\r\n        water: { // 水资源消耗 (万吨)\r\n          '炼铁事业部': [85, 82, 86, 88],\r\n          '炼钢事业部': [120, 115, 118, 122],\r\n          '轧钢事业部': [95, 92, 96, 94],\r\n          '马科托钢球': [28, 27, 29, 28.5],\r\n          '特板事业部': [65, 63, 66, 67],\r\n          '动力事业部': [180, 175, 185, 182],\r\n          '物流事业部': [15, 14, 16, 15.5],\r\n          '检修事业部': [12, 11.5, 12.5, 12.2]\r\n        },\r\n        gas: { // 天然气消耗 (万立方米)\r\n          '炼铁事业部': [320, 310, 325, 330],\r\n          '炼钢事业部': [480, 470, 485, 490],\r\n          '轧钢事业部': [380, 370, 385, 375],\r\n          '马科托钢球': [110, 105, 112, 108],\r\n          '特板事业部': [250, 245, 255, 260],\r\n          '动力事业部': [85, 82, 87, 86],\r\n          '物流事业部': [45, 43, 46, 44],\r\n          '检修事业部': [35, 34, 36, 35.5]\r\n        },\r\n        steam: { // 蒸汽消耗 (万吨)\r\n          '炼铁事业部': [45, 43, 46, 47],\r\n          '炼钢事业部': [65, 63, 66, 67],\r\n          '轧钢事业部': [52, 50, 53, 51],\r\n          '马科托钢球': [15, 14.5, 15.5, 15.2],\r\n          '特板事业部': [35, 34, 36, 37],\r\n          '动力事业部': [12, 11.5, 12.5, 12.2],\r\n          '物流事业部': [8, 7.8, 8.2, 8.1],\r\n          '检修事业部': [6, 5.8, 6.2, 6.1]\r\n        }\r\n      },\r\n      \r\n      // 能源类型选项\r\n      energyTypes: [\r\n        { label: '电力消耗', value: 'electricity', unit: '千瓦时', color: '#2f80ed' },\r\n        { label: '水资源消耗', value: 'water', unit: '吨', color: '#2d9cdb' },\r\n        { label: '天然气消耗', value: 'gas', unit: '立方米', color: '#1a73e8' },\r\n        { label: '蒸汽消耗', value: 'steam', unit: '吨', color: '#27ae60' }\r\n      ],\r\n      \r\n      // 能源成本数据\r\n      energyCostData: {\r\n        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\r\n        electricity: [320, 332, 301, 334, 390, 330, 320, 315, 310, 325, 315, 318],\r\n        coal: [220, 182, 191, 234, 290, 330, 310, 295, 300, 285, 270, 275],\r\n        gas: [150, 232, 201, 154, 190, 180, 165, 175, 190, 195, 205, 210],\r\n        steam: [98, 77, 101, 99, 120, 125, 110, 100, 105, 115, 110, 120]\r\n      },\r\n      \r\n      // 能源详情数据 - 当日消耗\r\n      energyDetailData: [\r\n        { \r\n          name: '转炉煤气', \r\n          category: 'gas',\r\n          value: 125.6, \r\n          unit: '万m³', \r\n          target: 130, \r\n          warning: 140, \r\n          danger: 150, \r\n          status: 'normal',\r\n          trend: -2.1 // 相比昨日变化百分比\r\n        },\r\n        { \r\n          name: '高炉煤气', \r\n          category: 'gas',\r\n          value: 287.3, \r\n          unit: '万m³', \r\n          target: 280, \r\n          warning: 300, \r\n          danger: 320, \r\n          status: 'warning',\r\n          trend: 5.3\r\n        },\r\n        { \r\n          name: '焦炉煤气', \r\n          category: 'gas',\r\n          value: 98.4, \r\n          unit: '万m³', \r\n          target: 100, \r\n          warning: 110, \r\n          danger: 120, \r\n          status: 'normal',\r\n          trend: -1.2\r\n        },\r\n        { \r\n          name: '天然气', \r\n          category: 'gas',\r\n          value: 45.7, \r\n          unit: '万m³', \r\n          target: 40, \r\n          warning: 45, \r\n          danger: 50, \r\n          status: 'danger',\r\n          trend: 12.5\r\n        },\r\n        { \r\n          name: '饱和蒸汽', \r\n          category: 'steam',\r\n          value: 56.2, \r\n          unit: '万吨', \r\n          target: 55, \r\n          warning: 60, \r\n          danger: 65, \r\n          status: 'normal',\r\n          trend: 1.8\r\n        },\r\n        { \r\n          name: '过热蒸汽', \r\n          category: 'steam',\r\n          value: 32.8, \r\n          unit: '万吨', \r\n          target: 30, \r\n          warning: 35, \r\n          danger: 40, \r\n          status: 'warning',\r\n          trend: 7.2\r\n        },\r\n        { \r\n          name: '工业用水', \r\n          category: 'water',\r\n          value: 142.5, \r\n          unit: '万吨', \r\n          target: 140, \r\n          warning: 150, \r\n          danger: 160, \r\n          status: 'normal',\r\n          trend: 1.5\r\n        },\r\n        { \r\n          name: '循环冷却水', \r\n          category: 'water',\r\n          value: 285.3, \r\n          unit: '万吨', \r\n          target: 280, \r\n          warning: 300, \r\n          danger: 320, \r\n          status: 'warning',\r\n          trend: 3.8\r\n        },\r\n        { \r\n          name: '高压电力', \r\n          category: 'electricity',\r\n          value: 1856.4, \r\n          unit: '万kWh', \r\n          target: 1800, \r\n          warning: 1900, \r\n          danger: 2000, \r\n          status: 'warning',\r\n          trend: 4.2\r\n        },\r\n        { \r\n          name: '中压电力', \r\n          category: 'electricity',\r\n          value: 945.2, \r\n          unit: '万kWh', \r\n          target: 950, \r\n          warning: 1000, \r\n          danger: 1050, \r\n          status: 'normal',\r\n          trend: -0.8\r\n        }\r\n      ],\r\n      \r\n      // 分厂能源消耗数据 - 按月统计\r\n      factoryEnergyData: {\r\n        months: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n        electricity: { // 电力消耗 (万千瓦时)\r\n          '钙业分厂':[1400884,1434005,1435766,1376319,1301095,1269630],\r\n          '矿渣微粉':[5805794,2131847,6089046,6100999,6417335,6478262],\r\n          '烧结分厂-1#烧结':[13117902,12943568,11061444,10812393,11623702,11032539],\r\n          '烧结分厂-2#烧结':[13033926,10436634,9287884,9769316,9879397,10565760],\r\n          '炼铁分厂-1#高炉':[0,0,0,0,0,0],\r\n          '炼铁分厂-2#高炉':[66940,0,0,0,0,0],\r\n          '炼铁分厂-3#高炉':[0,0,0,0,0,0],\r\n          '炼铁分厂-小喷煤':[0,0,0,0,0,0],\r\n          '一炼钢':[30768167,30125781,30297463,28980497,29774159,31343397],\r\n          '二炼钢':[22488495,21968943,21787916,22171067,21242115,21788119],\r\n          '一轧钢':[5795777,5452204,5711051,5648575,5496447,5733403],\r\n          '二轧-大棒':[3246250,3195091,3268836,3363082,3262553,3466935],\r\n          '二轧-小棒':[5059018,4954511,4811987,5053456,4687922,4852370],\r\n          '特板炼钢':[30387862,29842019,26431716,29469372,29271035,29035520],\r\n          '3500中板':[7706300,6644420,7397716,7328102,7206215,7421179],\r\n          '4300厚板':[13519112,12464662,10028536,12881286,12674940,13166679],\r\n          '热处理':[2813937,2726501,2275425,2384412,2206548,3135105],\r\n          '高线分厂':[7621452,7822538,7920470,7152446,7071412,7342066],\r\n          '线材深加工':[968654,878075,925284,902573,915434,915053],\r\n          '棒材深加工':[3330295,3280875,3350518,3481898,3304357,3326433],\r\n          '热电分厂-热电':[8709519,9757840,10102720,9660480,9307680,7473520],\r\n          '热电分厂-亚临界':[2842245,4042080,3634400,4309800,4358435,4033588],\r\n          '热电分厂-余热':[133500,247280,112640,112640,218560,136480],\r\n          '热电分厂-鼓风':[12146140,21995616,21969803,23377762,28428976,37036933],\r\n          '制氧分厂-一期':[26203549,26456523,20347464,26646735,25523888,27290746],\r\n          '制氧分厂-三期':[29239632,27886434,29274657,26601919,26162015,26865678],\r\n          '制氧分厂-空压站':[6092759,6483609,6455930,6661039,6369297,6464792],\r\n          '水处理-一期':[2467240,2470442,2515829,2549457,3222031,2884069],\r\n          '水处理-二期':[4951897,4902986,4843723,5040984,5021708,5263224],\r\n          '水处理-三期':[5224649,5320012,5060813,5407588,5488715,5816560],\r\n          '煤气分厂':[643132,693466,657052,659543,624830,620973],\r\n          '供电一区':[100415,103537,103906,133611,166027,180227],\r\n          '兴澄钢球':[1087981,840818,981751,1057275,909275,1188557],\r\n          '兴澄港务':[68023,59481,69918,63336,44541,65389],\r\n          '储运公司':[5759324,5859975,5352206,5316640,5378337,5644938],\r\n          '综合利用':[1046763,1048737,1103178,1006943,1082975,1068868],\r\n          '合金炉分厂':[2769092,4321005,3221559,4932761,4789800,5878980],\r\n          '物管部':[39902,43498,34953,29662,24373,24227],\r\n          '后勤部':[46436,51144,39739,36459,36817,36596],\r\n          '其他':[824375,775107,749943,688365,764337,978520],\r\n          '物管部':[1295140,664000,621920,541480,576620,836960],\r\n          '合计':[278822476,280325264,269335161,281710272,284833903,300662274]\r\n          // '炼铁分厂': [850, 820, 840, 860],\r\n          // '烧结分厂': [400, 380, 390, 410],\r\n          // '一炼钢': [950, 920, 940, 960],\r\n          // '二炼钢': [900, 880, 890, 920],\r\n          // '综合利用': [280, 270, 290, 275],\r\n          // '一轧钢': [380, 370, 390, 375],\r\n          // '二轧钢': [360, 350, 370, 355],\r\n          // '三轧钢': [340, 330, 350, 335],\r\n          // '特殊钢轧材': [320, 310, 330, 315],\r\n          // '棒材轧制厂': [300, 290, 310, 295],\r\n          // '棒线材深加工(棒)': [180, 170, 190, 175],\r\n          // '棒线材深加工(线)': [160, 150, 170, 155],\r\n          // '线材深加工': [140, 130, 150, 135],\r\n          // '马科托钢球': [420, 410, 430, 425],\r\n          // '特钢炼钢分厂': [380, 370, 390, 375],\r\n          // '中板分厂': [360, 350, 370, 355],\r\n          // '厚板分厂': [340, 330, 350, 335],\r\n          // '钢材深加工': [320, 310, 330, 315],\r\n          // '热电': [180, 170, 190, 175],\r\n          // '供电工区': [160, 150, 170, 155],\r\n          // '水处理分厂': [140, 130, 150, 135],\r\n          // '制氧分厂': [120, 110, 130, 115],\r\n          // '煤气分厂': [100, 90, 110, 95],\r\n          // '储运公司': [180, 175, 185, 182],\r\n          // '检修分厂': [150, 145, 155, 152]\r\n        },\r\n        water: { // 水资源消耗 (万吨)\r\n          '钙业分厂':[2517,2376,2253,2173,2259,2301],\r\n          '矿渣微粉':[2890,2591,2478,2358,2381,2256],\r\n          '烧结分厂-1#烧结':[41290,40443,39591,40136,40852,40146],\r\n          '烧结分厂-2#烧结':[71804,67539,69009,69947,72050,70344],\r\n          '炼铁分厂-1#高炉':[20870,21082,21231,26729,24702,28188],\r\n          '炼铁分厂-2#高炉':[61032,65615,56218,65690,63567,67033],\r\n          '炼铁分厂-3#高炉':[70604,69964,78613,85914,85358,99016],\r\n          '炼铁分厂-喷煤（0#2#高炉）':[2308,2457,2897,3017,2851,3597],\r\n          '一炼钢':[4631152,4529160,4609510,4645449,4536932,4618563],\r\n          '二炼钢':[9104629,8974584,8707241,8864449,8574110,8780493],\r\n          '一轧钢':[1613416,1566303,1603660,1604021,1574222,1641956],\r\n          '二轧-大棒':[1549976,1473652,1448998,1482840,1420848,1461097],\r\n          '二轧-小棒':[1761922,1636518,1599618,1707777,1611290,1566453],\r\n          '特板炼钢':[11065202,10861897,10078370,10798271,10539246,10414072],\r\n          '3500中板':[2528768,2531102,2577106,2614333,2509852,2746164],\r\n          '4300厚板':[53602,52973,47100,51727,48993,52439],\r\n          '4300水处理':[51957,59997,58691,55515,61891,66474],\r\n          '热处理':[15873,13257,11536,10213,10796,10018],\r\n          '高线分厂':[24783,21328,19082,23117,24548,23518],\r\n          '热电分厂-热电':[32654,35991,46976,34907,35831,30211],\r\n          '热电分厂-鼓风':[66781,60578,74506,84715,56847,60222],\r\n          '热电分厂-亚临界':[50702,42898,53360,80937,75090,83220],\r\n          '热电分厂-净水厂直供原水':[82710,66641,58670,59022,51034,51739],\r\n          '制氧分厂-一期':[48107,89953,40665,51367,56605,55866],\r\n          '制氧分厂-三期':[27136,13397,10726,21896,25682,11716],\r\n          '制氧分厂-空压站':[3928,3563,4385,4983,3343,3542],\r\n          '水处理-一期':[666457,684861,699017,706374,703497,737291],\r\n          '水处理-二期':[283733,290660,245217,243883,245300,285485],\r\n          '水处理-三期':[162012,140737,143298,211329,206303,245132],\r\n          '供电一区':[2273,2357,2502,2597,2385,2153],\r\n          '储运公司':[41906,40362,41980,42361,41963,40779],\r\n          '综合利用':[25117,24586,19931,25926,22555,15964],\r\n          '合金炉分厂':[939944,1406061,1129690,1591196,1392990,1834398],\r\n          '后勤综合楼':[606264,473027,520855,233384,168146,365997],\r\n          '公用':[79065,79871,79634,79871,80971,81341],\r\n          '合计':[35793384,35448381,34204614,35628424,34375290,35599184]\r\n          // '炼铁分厂': [55, 53, 56, 57],\r\n          // '烧结分厂': [30, 29, 31, 32],\r\n          // '一炼钢': [65, 63, 66, 67],\r\n          // '二炼钢': [60, 58, 61, 62],\r\n          // '综合利用': [20, 19, 21, 20.5],\r\n          // '一轧钢': [25, 24, 26, 25.5],\r\n          // '二轧钢': [23, 22, 24, 23.5],\r\n          // '三轧钢': [22, 21, 23, 22.5],\r\n          // '特殊钢轧材': [21, 20, 22, 21.5],\r\n          // '棒材轧制厂': [20, 19, 21, 20.5],\r\n          // '棒线材深加工(棒)': [12, 11, 13, 12.5],\r\n          // '棒线材深加工(线)': [11, 10, 12, 11.5],\r\n          // '线材深加工': [9, 8, 10, 9.5],\r\n          // '马科托钢球': [28, 27, 29, 28.5],\r\n          // '特钢炼钢分厂': [25, 24, 26, 25.5],\r\n          // '中板分厂': [23, 22, 24, 23.5],\r\n          // '厚板分厂': [22, 21, 23, 22.5],\r\n          // '钢材深加工': [21, 20, 22, 21.5],\r\n          // '热电': [65, 63, 67, 66],\r\n          // '供电工区': [45, 43, 47, 46],\r\n          // '水处理分厂': [70, 68, 72, 71],\r\n          // '制氧分厂': [55, 53, 57, 56],\r\n          // '煤气分厂': [45, 43, 47, 46],\r\n          // '储运公司': [15, 14, 16, 15.5],\r\n          // '检修分厂': [12, 11.5, 12.5, 12.2]\r\n        },\r\n        gas: {\r\n          '钙业分厂':[2080,14317,12875,6240,0,0],\r\n          '矿渣微粉':[0,0,0,0,0,0],\r\n          '烧结分厂-1#烧结':[928,358,1054,2476,1040,1040],\r\n          '烧结分厂-2#烧结':[13471,3440,11592,14448,19094,38272],\r\n          '炼铁分厂-1#高炉':[4264,2392,1832,3952,1456,2184],\r\n          '炼铁分厂-2#高炉':[3744,6344,14808,4104,3432,5616],\r\n          '炼铁分厂-3#高炉':[48880,74483,62541,92272,17992,47889],\r\n          '炼铁分厂-小喷煤':[936,1144,1040,1212,936,1040],\r\n          '炼铁分厂-大喷煤':[42432,35464,32200,20696,15288,17784],\r\n          '一炼钢':[321044,441179,438423,450993,381575,351275],\r\n          '二炼钢':[150844,208889,212605,212274,204372,195425],\r\n          '一轧钢':[2857,12084,12120,6818,6230,16217],\r\n          '二轧-大棒':[23824,30000,14520,10630,24036,24489],\r\n          '二轧-小棒':[16000,12487,8926,4555,16024,10496],\r\n          '特板炼钢':[154790,218832,190568,135757,143405,140886],\r\n          '3500中板':[619233,644217,767563,908906,890002,926636],\r\n          '4300厚板':[492656,621775,785247,500988,563886,792919],\r\n          '热处理':[1093740,1370389,1296365,1408988,1170360,1299076],\r\n          '高线分厂':[214,96,298,207,100,204],\r\n          '棒材深加工':[1133600,1177473,1139440,1214108,1141737,1153930],\r\n          '线材深加工':[614092,501810,509750,552280,559440,517720],\r\n          '热电分厂-热电':[15713,33472,183549,21279,82525,33965],\r\n          '热电分厂-鼓风':[58638,35678,204014,28303,60770,58240],\r\n          '热电分厂-亚临界':[94063,60326,27315,63941,90389,93837],\r\n          '煤气分厂':[6285,7289,7616,7537,7561,3075],\r\n          '兴澄钢球':[194970,206307,208890,225422,209463,215730],\r\n          '综合利用':[0,0,0,0,0,0],\r\n          '合计':[5109298,5720245,6145151,5898386,5611113,5947945]\r\n          \r\n           // 天然气消耗 (万立方米)\r\n          // '炼铁分厂': [220, 210, 225, 230],\r\n          // '烧结分厂': [100, 95, 105, 110],\r\n          // '一炼钢': [250, 240, 255, 260],\r\n          // '二炼钢': [230, 220, 235, 240],\r\n          // '综合利用': [75, 70, 78, 76],\r\n          // '一轧钢': [95, 90, 98, 96],\r\n          // '二轧钢': [90, 85, 93, 91],\r\n          // '三轧钢': [85, 80, 88, 86],\r\n          // '特殊钢轧材': [80, 75, 83, 81],\r\n          // '棒材轧制厂': [75, 70, 78, 76],\r\n          // '棒线材深加工(棒)': [45, 40, 48, 46],\r\n          // '棒线材深加工(线)': [40, 35, 43, 41],\r\n          // '线材深加工': [35, 30, 38, 36],\r\n          // '马科托钢球': [110, 105, 112, 108],\r\n          // '特钢炼钢分厂': [95, 90, 98, 96],\r\n          // '中板分厂': [90, 85, 93, 91],\r\n          // '厚板分厂': [85, 80, 88, 86],\r\n          // '钢材深加工': [80, 75, 83, 81],\r\n          // '热电': [35, 30, 38, 36],\r\n          // '供电工区': [25, 20, 28, 26],\r\n          // '水处理分厂': [20, 15, 23, 21],\r\n          // '制氧分厂': [15, 10, 18, 16],\r\n          // '煤气分厂': [10, 5, 13, 11],\r\n          // '储运公司': [45, 43, 46, 44],\r\n          // '检修分厂': [35, 34, 36, 35.5]\r\n        },\r\n        steam: { // 蒸汽消耗 (万吨)\r\n          '钙业分厂':[0,0,0,0,0,0],\r\n          '矿渣微粉':[0,0,0,0,0,0],\r\n          '烧结分厂-1#烧结':[0,0,0,0,0,0],\r\n          '烧结分厂-2#烧结':[2368,2379,1765,1615,1422,1663],\r\n          '炼铁分厂-1#高炉':[578,637,485,554,388,671],\r\n          '炼铁分厂-2#高炉':[295,141,109,265,419,312],\r\n          '炼铁分厂-3#高炉':[1445,2143,1633,124,0,127],\r\n          '一炼钢':[116,388,50,50,96,164],\r\n          '二炼钢':[12927,16443,17638,17071,16843,16351],\r\n          '一轧钢':[0,0,0,0,0,0],\r\n          '二轧-大棒':[209,222,253,133,198,116],\r\n          '二轧-小棒':[193,204,233,123,182,108],\r\n          '特板炼钢':[22968,22336,18819,21647,21604,21708],\r\n          '3500中板':[0,0,0,0,0,0],\r\n          '4300厚板':[0,0,0,0,0,0],\r\n          '高线分厂':[0,0,0,0,0,0],\r\n          '棒材深加工':[0,0,0,0,0,0],\r\n          '线材深加工':[2240,1616,1364,1121,1082,920],\r\n          '热电分厂-热电':[85199,90583,83075,93108,89615,95156],\r\n          '热电分厂-鼓风':[0,0,0,0,0,0],\r\n          '热电分厂-热水':[0,0,0,0,0,0],\r\n          '煤气分厂':[0,0,0,0,0,0],\r\n          '制氧分厂':[400,400,400,400,400,400],\r\n          '后勤部':[0,0,0,0,0,0],\r\n          '外销蒸汽':[32568,17534,27334,24311,21126,19504],\r\n          '综合利用':[216,31,16,19,32,46],\r\n          '损耗':[1270,1166,1088,907,838,825],\r\n          '合计':[162992,156223,154262,161448,154245,158071]\r\n          // '炼铁分厂': [30, 28, 31, 32],\r\n          // '烧结分厂': [15, 13, 16, 17],\r\n          // '一炼钢': [35, 33, 36, 37],\r\n          // '二炼钢': [30, 28, 31, 32],\r\n          // '综合利用': [10, 9, 11, 10.5],\r\n          // '一轧钢': [13, 12, 14, 13.5],\r\n          // '二轧钢': [12, 11, 13, 12.5],\r\n          // '三轧钢': [11, 10, 12, 11.5],\r\n          // '特殊钢轧材': [10, 9, 11, 10.5],\r\n          // '棒材轧制厂': [9, 8, 10, 9.5],\r\n          // '棒线材深加工(棒)': [5, 4, 6, 5.5],\r\n          // '棒线材深加工(线)': [4, 3, 5, 4.5],\r\n          // '线材深加工': [3, 2, 4, 3.5],\r\n          // '马科托钢球': [15, 14.5, 15.5, 15.2],\r\n          // '特钢炼钢分厂': [13, 12, 14, 13.5],\r\n          // '中板分厂': [12, 11, 13, 12.5],\r\n          // '厚板分厂': [11, 10, 12, 11.5],\r\n          // '钢材深加工': [10, 9, 11, 10.5],\r\n          // '热电': [5, 4, 6, 5.5],\r\n          // '供电工区': [3, 2, 4, 3.5],\r\n          // '水处理分厂': [2, 1, 3, 2.5],\r\n          // '制氧分厂': [1.5, 0.5, 2.5, 2],\r\n          // '煤气分厂': [1, 0.8, 1.2, 1.1],\r\n          // '储运公司': [8, 7.8, 8.2, 8.1],\r\n          // '检修分厂': [6, 5.8, 6.2, 6.1]\r\n        }\r\n      },\r\n      \r\n      // 关键能源指标当日值与昨日对比\r\n      keyEnergyIndicators: [],\r\n      // [\r\n      //   {\r\n      //     name: '氧气',\r\n      //     today: 46.80, // 超出目标范围上限\r\n      //     yesterday: 44.64,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 42.00,\r\n      //     targetMax: 45.00,\r\n      //     change: 4.84,\r\n      //     status: 'danger'\r\n      //   },\r\n      //   {\r\n      //     name: '氮气',\r\n      //     today: 14.82,\r\n      //     yesterday: 15.21,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 14.00,\r\n      //     targetMax: 16.00,\r\n      //     change: -2.56,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '氩气',\r\n      //     today: 0.85,\r\n      //     yesterday: 0.88,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 0.80,\r\n      //     targetMax: 0.90,\r\n      //     change: -3.41,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '空气',\r\n      //     today: 450, // 低于目标范围下限\r\n      //     yesterday: 481,\r\n      //     unit: 'm³/h',\r\n      //     targetMin: 460,\r\n      //     targetMax: 500,\r\n      //     change: -6.44,\r\n      //     status: 'danger'\r\n      //   },\r\n      //   {\r\n      //     name: '高炉煤气',\r\n      //     today: 985.3,\r\n      //     yesterday: 962.7,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 950.0,\r\n      //     targetMax: 1000.0,\r\n      //     change: 2.35,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '转炉煤气',\r\n      //     today: 85.2,\r\n      //     yesterday: 88.4,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 80.0,\r\n      //     targetMax: 90.0,\r\n      //     change: -3.62,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '焦炉煤气',\r\n      //     today: 41.3,\r\n      //     yesterday: 42.8,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 40.0,\r\n      //     targetMax: 45.0,\r\n      //     change: -3.50,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '饱和蒸汽',\r\n      //     today: 0.52,\r\n      //     yesterday: 0.54,\r\n      //     unit: 't/t',\r\n      //     targetMin: 0.50,\r\n      //     targetMax: 0.58,\r\n      //     change: -3.70,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '过热蒸汽',\r\n      //     today: 0.33,\r\n      //     yesterday: 0.31,\r\n      //     unit: 't/t',\r\n      //     targetMin: 0.30,\r\n      //     targetMax: 0.35,\r\n      //     change: 6.45,\r\n      //     status: 'warning'\r\n      //   },\r\n      //   {\r\n      //     name: '低压蒸汽',\r\n      //     today: 0.21,\r\n      //     yesterday: 0.23,\r\n      //     unit: 't/t',\r\n      //     targetMin: 0.20,\r\n      //     targetMax: 0.25,\r\n      //     change: -8.70,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '天然气',\r\n      //     today: 24.3,\r\n      //     yesterday: 25.1,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 22.0,\r\n      //     targetMax: 26.0,\r\n      //     change: -3.19,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '压缩天然气',\r\n      //     today: 2.85,\r\n      //     yesterday: 2.91,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 2.70,\r\n      //     targetMax: 3.00,\r\n      //     change: -2.06,\r\n      //     status: 'good'\r\n      //   }\r\n      // ],\r\n      \r\n      // 能源指标是否超出目标范围标记\r\n      isEnergyChartWarning: {\r\n        electricity: false,\r\n        water: false,\r\n        gas: false,\r\n        steam: false\r\n      },\r\n      \r\n      // 能源目标范围\r\n      energyTargetRanges: {\r\n        electricity: { min: 800, max: 900, unit: '千瓦时' },\r\n        water: { min: 50, max: 60, unit: '吨' },\r\n        gas: { min: 200, max: 240, unit: '立方米' },\r\n        steam: { min: 25, max: 35, unit: '吨' }\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算所有部门的完成率数据（只显示分厂层级）\r\n    departmentCompletionRates() {\r\n      // 按部门分组计算完成率，只处理有\"-\"的分厂层级\r\n      const departmentRates = this.completionData\r\n        .filter(dept => dept.department.includes('-')) // 只选择分厂层级\r\n        .map(dept => {\r\n          const indicators = dept.indicators\r\n          let completedCount = 0\r\n          let totalIndicators = indicators.length\r\n          \r\n          // 计算已完成指标数量，使用4月份数据\r\n          indicators.forEach(indicator => {\r\n            // 使用4月份数据（第3个索引）\r\n            const actual = indicator.values[3] // 第四周/4月份的实际值\r\n            const target = indicator.target\r\n            \r\n            const isCompleted = indicator.isHigherBetter \r\n              ? actual >= target \r\n              : actual <= target\r\n            \r\n            if (isCompleted) completedCount++\r\n          })\r\n          \r\n          // 计算完成率\r\n          const completionRate = (completedCount / totalIndicators) * 100\r\n          \r\n          return {\r\n            department: dept.department.split('-')[1], // 只显示分厂名称\r\n            fullDepartment: dept.department, // 保存完整部门名称用于数据查询\r\n            completionRate: parseFloat(completionRate.toFixed(1)),\r\n            totalIndicators: totalIndicators,\r\n            completedIndicators: completedCount\r\n          }\r\n        })\r\n      \r\n      // 按完成率从高到低排序\r\n      return departmentRates.sort((a, b) => b.completionRate - a.completionRate)\r\n    },\r\n    \r\n    // 计算所有指标的完成率（4月份数据）\r\n    allIndicatorCompletionRates() {\r\n      const allIndicators = []\r\n      \r\n      this.completionData.forEach(dept => {\r\n        if (dept.department.includes('-')) { // 只选择分厂层级\r\n          const departmentName = dept.department.split('-')[1]\r\n          \r\n          dept.indicators.forEach(indicator => {\r\n            // 直接使用04月列的完成率数据\r\n            // 检查是否有最后一列（04月）的完成率数据\r\n            const completionRateStr = indicator.completionRates && indicator.completionRates[3]; // 04月完成率\r\n            let completionRate = 0;\r\n            \r\n            // 如果有直接的完成率数据，则使用它\r\n            if (completionRateStr) {\r\n              // 将百分比字符串转换为数值，例如 \"-1.31%\" -> -1.31\r\n              completionRate = parseFloat(completionRateStr.replace('%', ''));\r\n            } else {\r\n              // 如果没有直接数据，则用原来的方法计算\r\n              const actual = indicator.values[3]; // 4月份实际值\r\n              const target = indicator.target;\r\n              \r\n              if (indicator.isHigherBetter) {\r\n                // 如果指标越高越好，完成率 = 实际值/目标值 * 100% - 100%\r\n                completionRate = ((actual / target) * 100) - 100;\r\n              } else {\r\n                // 如果指标越低越好，完成率 = 目标值/实际值 * 100% - 100%\r\n                completionRate = ((target / actual) * 100) - 100;\r\n              }\r\n            }\r\n            \r\n            // 对完成率进行处理\r\n            // 非数字完成率调整为0\r\n            if (isNaN(completionRate) || !isFinite(completionRate)) {\r\n              completionRate = 0;\r\n            }\r\n            \r\n            // 限制最大完成率绝对值为200%\r\n            if (Math.abs(completionRate) > 200) {\r\n              completionRate = completionRate > 0 ? 200 : -200;\r\n            }\r\n            \r\n            allIndicators.push({\r\n              department: departmentName,\r\n              indicator: indicator.name,\r\n              completionRate: parseFloat(completionRate.toFixed(1)),\r\n              target: indicator.target,\r\n              actual: indicator.values[3], // 4月份实际值\r\n              unit: indicator.unit,\r\n              isHigherBetter: indicator.isHigherBetter\r\n            });\r\n          });\r\n        }\r\n      });\r\n      \r\n      // 按完成率从高到低排序\r\n      return allIndicators.sort((a, b) => b.completionRate - a.completionRate);\r\n    },\r\n    \r\n    // 直接使用所有指标完成率数据\r\n    paginatedIndicatorRates() {\r\n      return this.allIndicatorCompletionRates\r\n    },\r\n    \r\n    // 获取事业部列表（用于筛选）\r\n    businessUnits() {\r\n      const units = new Set()\r\n      this.completionData.forEach(dept => {\r\n        if (dept.department.includes('-')) {\r\n          units.add(dept.department.split('-')[0])\r\n        } else {\r\n          units.add(dept.department)\r\n        }\r\n      })\r\n      return Array.from(units)\r\n    },\r\n    \r\n    // 获取当前选择的事业部下的分厂列表\r\n    currentBusinessUnitDepartments() {\r\n      if (!this.currentBusinessUnit) return []\r\n      \r\n      return this.completionData\r\n        .filter(dept => dept.department.startsWith(this.currentBusinessUnit + '-'))\r\n        .map(dept => ({\r\n          value: dept.department,\r\n          label: dept.department.split('-')[1]\r\n        }))\r\n    },\r\n    \r\n    // 获取当前部门的指标列表\r\n    currentDepartmentIndicators() {\r\n      const deptData = this.completionData.find(dept => dept.department === this.currentDepartment)\r\n      return deptData ? deptData.indicators : []\r\n    },\r\n    \r\n    // 获取所有分厂列表（用于能源消耗详情）\r\n    allFactories() {\r\n      return this.completionData1.map(dept => dept.department)\r\n    }\r\n  },\r\n  watch: {\r\n    // // 监听事业部变化\r\n    // currentBusinessUnit(newVal) {\r\n    //   if (this.currentBusinessUnitDepartments.length > 0) {\r\n    //     this.currentDepartment = this.currentBusinessUnitDepartments[0].value\r\n    //   }\r\n      \r\n    //   // 启动自动切换\r\n    //   this.startAutoSwitchWithinBusinessUnit()\r\n    // },\r\n    \r\n    // // 监听部门变化，自动选择第一个指标\r\n    // currentDepartment(newVal) {\r\n    //   if (this.currentDepartmentIndicators.length > 0) {\r\n    //     this.currentIndicator = this.currentDepartmentIndicators[0].name\r\n    //   }\r\n    //   this.initMonthlyTrendChart()\r\n    // },\r\n    \r\n    // // 监听指标变化，更新图表\r\n    // currentIndicator() {\r\n    //   this.initMonthlyTrendChart()\r\n    // }\r\n  },\r\n  mounted() {\r\n    // 初始化图表\r\n    this.initCharts()\r\n    \r\n    // 计算未完成指标数据\r\n    this.calculateIncompleteData()\r\n    \r\n    // 确保currentEnergyDept有一个有效的初始值\r\n    this.$nextTick(() => {\r\n      this.initEnergyDetailCharts()\r\n      if (this.allFactories && this.allFactories.length > 0) {\r\n        this.currentEnergyDept = this.allFactories[0]\r\n        // 重新初始化能源详情图表\r\n        this.initEnergyDetailCharts()\r\n      }\r\n    })\r\n    this.loadExcelFromRemote();\r\n    // 启动滚动表格\r\n    this.$nextTick(() => {\r\n      // 复制表格内容以实现无缝滚动\r\n      const table = this.$refs.incompleteTable\r\n      if (table && this.incompleteData.length > 0) {\r\n        // 获取表格内容部分（不包含表头）\r\n        const tableBody = table.querySelector('.el-table__body-wrapper')\r\n        if (tableBody) {\r\n          const originalContent = tableBody.innerHTML\r\n          // 在表格内容后面添加一份相同的内容，而不是整个表格\r\n          tableBody.innerHTML += originalContent\r\n        }\r\n      }\r\n      this.startTableScroll()\r\n    })\r\n    \r\n    // 启动事业部内部自动切换\r\n    this.startAutoSwitchWithinBusinessUnit()\r\n    \r\n    // 启动事业部切换（每30秒切换一次）\r\n    this.startBusinessUnitSwitch()\r\n    \r\n    // 启动指标卡片自动滚动\r\n    this.$nextTick(() => {\r\n      this.initIndicatorCardsScroll()\r\n      \r\n      // 为指标卡片容器添加鼠标悬停事件处理\r\n      const cardsContainer = this.$refs.indicatorCardsContainer\r\n      if (cardsContainer) {\r\n        cardsContainer.addEventListener('mouseover', () => {\r\n          this.indicatorCardsScrollPaused = true\r\n        })\r\n        \r\n        cardsContainer.addEventListener('mouseout', () => {\r\n          this.indicatorCardsScrollPaused = false\r\n        })\r\n      }\r\n    })\r\n    \r\n    // 监听窗口大小变化，重新渲染图表\r\n    window.addEventListener('resize', this.resizeCharts)\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    this.stopAutoSwitch()\r\n    \r\n    if (this.scrollTimer) {\r\n      clearInterval(this.scrollTimer)\r\n      \r\n      // 移除表格滚动的事件监听器\r\n      const table = this.$refs.incompleteTable\r\n      if (table) {\r\n        const tableBody = table.querySelector('.el-table__body-wrapper')\r\n        if (tableBody && this.tableMouseEnterHandler && this.tableMouseLeaveHandler) {\r\n          tableBody.removeEventListener('mouseenter', this.tableMouseEnterHandler);\r\n          tableBody.removeEventListener('mouseleave', this.tableMouseLeaveHandler);\r\n        }\r\n      }\r\n    }\r\n    \r\n    if (this.businessUnitTimer) {\r\n      clearInterval(this.businessUnitTimer)\r\n    }\r\n    \r\n    if (this.energyDeptTimer) {\r\n      clearInterval(this.energyDeptTimer)\r\n    }\r\n    \r\n    if (this.completionChartTimer) {\r\n      clearInterval(this.completionChartTimer)\r\n    }\r\n    \r\n    if (this.indicatorCardsScrollTimer) {\r\n      clearInterval(this.indicatorCardsScrollTimer)\r\n    }\r\n    \r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n    \r\n    // 移除指标卡片容器的事件监听器\r\n    const cardsContainer = this.$refs.indicatorCardsContainer\r\n    if (cardsContainer) {\r\n      cardsContainer.removeEventListener('mouseover', () => {\r\n        this.indicatorCardsScrollPaused = true\r\n      })\r\n      \r\n      cardsContainer.removeEventListener('mouseout', () => {\r\n        this.indicatorCardsScrollPaused = false\r\n      })\r\n    }\r\n    \r\n    // 销毁图表实例\r\n    this.disposeCharts()\r\n  },\r\n  created() {\r\n      this.getDateUpdateList()\r\n  },\r\n  methods: {\r\n    // 初始化所有图表\r\n\r\n    loadExcelFromRemote() {\r\n      this.excelLoading = true;\r\n      const url =\r\n        \"https://ydxt.citicsteel.com:8099/minio/xctg/temp/jm关键技径指标.xlsx\";\r\n      axios({\r\n        method: \"get\",\r\n        url,\r\n        responseType: \"arraybuffer\",\r\n      })\r\n        .then((response) => {\r\n          const data = new Uint8Array(response.data);\r\n          const workbook = XLSX.read(data, { type: \"array\" });\r\n          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];\r\n          const jsonData = XLSX.utils.sheet_to_json(firstSheet, {\r\n            header: 1,\r\n            range: 1,\r\n          });\r\n          console.log(jsonData);\r\n          this.processExcelData(jsonData);\r\n          this.excelLoading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"加载Excel文件失败:\", error);\r\n          this.$message.error(\"加载Excel文件失败，请稍后重试\");\r\n          this.excelLoading = false;\r\n        });\r\n    },\r\n    getDetail(){\r\n      dimensionalitylistPermissionList().then(res => {\r\n        this.adminShow = res.msg;\r\n      });\r\n    },\r\n    getDateUpdateList(){\r\n      dateUpdateList().then(res => {\r\n        this.keyEnergyIndicators = res.data;\r\n      });\r\n    },\r\n\r\n    \r\n    processExcelData(jsonData) {\r\n      if (!jsonData || jsonData.length < 2) return;\r\n      const headers = jsonData[0];\r\n      const colIndexes = {\r\n        factory: headers.indexOf(\"分厂\"),\r\n        name: headers.indexOf(\"指标名称\"),\r\n        target: headers.indexOf(\"目标\"),\r\n        unit: headers.indexOf(\"单位\"),\r\n        jan: headers.indexOf(\"01月实绩\"),\r\n        janStatus: headers.indexOf(\"01月实绩\") + 1,\r\n        feb: headers.indexOf(\"02月实绩\"),\r\n        febStatus: headers.indexOf(\"02月实绩\") + 1,\r\n        mar: headers.indexOf(\"03月实绩\"),\r\n        marStatus: headers.indexOf(\"03月实绩\") + 1,\r\n        apr: headers.indexOf(\"04月实绩\"),\r\n        aprStatus: headers.indexOf(\"04月实绩\") + 1,\r\n        may: headers.indexOf(\"05月实绩\"),\r\n        mayStatus: headers.indexOf(\"05月实绩\") + 1,\r\n      };\r\n\r\n      const dataRows = jsonData.slice(1);\r\n      const techIndicators = dataRows\r\n        .map((row) => {\r\n          if (!row || row.length === 0) return null;\r\n          if (\r\n            row[colIndexes.factory] === undefined ||\r\n            row[colIndexes.name] === undefined\r\n          )\r\n            return null;\r\n\r\n          const indicator = {\r\n            factory: row[colIndexes.factory] || \"\",\r\n            name: row[colIndexes.name] || \"\",\r\n            target:\r\n              row[colIndexes.target] !== undefined\r\n                ? String(row[colIndexes.target])\r\n                : \"\",\r\n            unit: row[colIndexes.unit] || \"\",\r\n            jan:\r\n              row[colIndexes.jan] !== undefined\r\n                ? String(row[colIndexes.jan])\r\n                : \"\",\r\n            janStatus: row[colIndexes.janStatus] === 1.0 ? 1 : 0,\r\n            feb:\r\n              row[colIndexes.feb] !== undefined\r\n                ? String(row[colIndexes.feb])\r\n                : \"\",\r\n            febStatus: row[colIndexes.febStatus] === 1.0 ? 1 : 0,\r\n            mar:\r\n              row[colIndexes.mar] !== undefined\r\n                ? String(row[colIndexes.mar])\r\n                : \"\",\r\n            marStatus: row[colIndexes.marStatus] === 1.0 ? 1 : 0,\r\n            apr:\r\n              row[colIndexes.apr] !== undefined\r\n                ? String(row[colIndexes.apr])\r\n                : \"\",\r\n            aprStatus: row[colIndexes.aprStatus] === 1.0 ? 1 : 0,\r\n            may:\r\n              row[colIndexes.may] !== undefined\r\n                ? String(row[colIndexes.may])\r\n                : \"\",\r\n            mayStatus: row[colIndexes.mayStatus] === 1.0 ? 1 : 0,\r\n          };\r\n\r\n          indicator.monthlyData = [\r\n            parseFloat(indicator.jan) || 0,\r\n            parseFloat(indicator.feb) || 0,\r\n            parseFloat(indicator.mar) || 0,\r\n            parseFloat(indicator.apr) || 0,\r\n            parseFloat(indicator.may) || 0,\r\n          ];\r\n\r\n          return indicator;\r\n        })\r\n        .filter(Boolean);\r\n\r\n      // 处理相同分厂和指标名称的数据，确保数据一致性\r\n      const uniqueKeys = new Map();\r\n      const uniqueIndicators = [];\r\n\r\n      // 先处理相同指标的合并\r\n      techIndicators.forEach((indicator) => {\r\n        const key = `${indicator.factory}_${indicator.name}_${indicator.unit}`;\r\n        if (!uniqueKeys.has(key)) {\r\n          uniqueKeys.set(key, uniqueIndicators.length);\r\n          uniqueIndicators.push(indicator);\r\n        }\r\n      });\r\n\r\n      this.techIndicators = uniqueIndicators;\r\n    },\r\n    \r\n    initCharts() {\r\n      this.$nextTick(() => {\r\n        // 初始化部门完成率柱状图\r\n        this.initDepartmentCompletionChart()\r\n        \r\n        // 设置默认部门和指标\r\n        if (this.currentBusinessUnitDepartments.length > 0 && !this.currentDepartment) {\r\n          this.currentDepartment = this.currentBusinessUnitDepartments[0].value\r\n        }\r\n        \r\n        if (this.currentDepartmentIndicators.length > 0 && !this.currentIndicator) {\r\n          this.currentIndicator = this.currentDepartmentIndicators[0].name\r\n        }\r\n        \r\n        // 初始化月度趋势折线图\r\n        this.initMonthlyTrendChart()\r\n        \r\n        // 设置能源部门默认值并初始化能源子图表\r\n        if (this.allFactories && this.allFactories.length > 0) {\r\n          if (!this.currentEnergyDept) {\r\n            this.currentEnergyDept = this.allFactories[0]\r\n          }\r\n          // 初始化能源子图表\r\n          this.initEnergyDetailCharts()\r\n        }\r\n        \r\n        // 启动事业部内部自动切换\r\n        this.startAutoSwitchWithinBusinessUnit()\r\n        \r\n        // 启动事业部切换（每30秒切换一次）\r\n        this.startBusinessUnitSwitch()\r\n        \r\n        // 启动能源部门自动切换\r\n        this.startEnergyDeptSwitch()\r\n      })\r\n    },\r\n    \r\n    // 启动事业部内部自动切换\r\n    startAutoSwitchWithinBusinessUnit() {\r\n      // 停止之前的自动切换\r\n      this.stopAutoSwitch()\r\n      \r\n      // 启动新的自动切换\r\n      this.trendChartTimer = setInterval(() => {\r\n        this.switchDepartmentWithinBusinessUnit()\r\n      }, 3000) // 分厂切换频率为3秒\r\n    },\r\n    \r\n    // 在当前事业部内切换部门\r\n    switchDepartmentWithinBusinessUnit() {\r\n      const departments = this.currentBusinessUnitDepartments\r\n      if (departments.length <= 1) return\r\n      \r\n      const currentIndex = departments.findIndex(dept => dept.value === this.currentDepartment)\r\n      const nextIndex = (currentIndex + 1) % departments.length\r\n      this.currentDepartment = departments[nextIndex].value\r\n    },\r\n    \r\n    // 处理自动切换开关变化\r\n    handleAutoSwitchChange(value) {\r\n      if (value) {\r\n        // 启动自动切换\r\n        this.startAutoSwitchWithinBusinessUnit()\r\n      } else {\r\n        // 停止自动切换\r\n        this.stopAutoSwitch()\r\n      }\r\n    },\r\n    \r\n    // 启动自动切换\r\n    startAutoSwitch() {\r\n      if (this.trendChartTimer) {\r\n        clearInterval(this.trendChartTimer)\r\n      }\r\n      \r\n      this.trendChartTimer = setInterval(() => {\r\n        this.switchDepartment()\r\n      }, 3000)\r\n    },\r\n    \r\n    // 停止自动切换\r\n    stopAutoSwitch() {\r\n      if (this.trendChartTimer) {\r\n        clearInterval(this.trendChartTimer)\r\n        this.trendChartTimer = null\r\n      }\r\n    },\r\n    \r\n    // 切换完成率图表页码\r\n    changePage(page) {\r\n      this.currentPage = page\r\n      this.initDepartmentCompletionChart()\r\n    },\r\n    \r\n    // 计算未完成指标数据，筛选负数完成率\r\n    calculateIncompleteData() {\r\n      const incomplete = [];\r\n      \r\n      this.completionData.forEach(dept => {\r\n        dept.indicators.forEach(indicator => {\r\n          // 直接使用04月列的完成率数据\r\n          // 检查是否有最后一列（04月）的完成率数据\r\n          const completionRateStr = indicator.completionRates && indicator.completionRates[3]; // 04月完成率\r\n          let completionRate = 0;\r\n          \r\n          // 如果有直接的完成率数据，则使用它\r\n          if (completionRateStr) {\r\n            // 将百分比字符串转换为数值，例如 \"-1.31%\" -> -1.31\r\n            completionRate = parseFloat(completionRateStr.replace('%', ''));\r\n          } else {\r\n            // 如果没有直接数据，则用原来的方法计算\r\n            const actual = indicator.values[3]; // 4月份实际值\r\n            const target = indicator.target;\r\n            \r\n            if (indicator.isHigherBetter) {\r\n              // 如果指标越高越好，完成率 = 实际值/目标值 * 100% - 100%\r\n              completionRate = ((actual / target) * 100) - 100;\r\n            } else {\r\n              // 如果指标越低越好，完成率 = 目标值/实际值 * 100% - 100%\r\n              completionRate = ((target / actual) * 100) - 100;\r\n            }\r\n          }\r\n          \r\n          // 只添加完成率为负数的记录\r\n          if (completionRate < 0) {\r\n            incomplete.push({\r\n              department: dept.department.includes('-') ? dept.department.split('-')[1] : dept.department,\r\n              indicator: indicator.name,\r\n              target: indicator.target,\r\n              actual: indicator.values[3], // 4月份实际值\r\n              unit: indicator.unit,\r\n              completionRate: parseFloat(completionRate.toFixed(1))\r\n            });\r\n          }\r\n        });\r\n      });\r\n      \r\n      // 按完成率从低到高排序\r\n      this.incompleteData = incomplete.sort((a, b) => a.completionRate - b.completionRate);\r\n    },\r\n    \r\n    // 启动表格滚动\r\n    startTableScroll() {\r\n      if (this.incompleteData.length > 0) {\r\n        const table = this.$refs.incompleteTable\r\n        \r\n        // 复制表格内容以实现无缝滚动\r\n        if (table) {\r\n          // 清除之前的滚动定时器\r\n          if (this.scrollTimer) {\r\n            clearInterval(this.scrollTimer)\r\n          }\r\n          \r\n          // 获取表格内容部分（不包含表头）\r\n          const tableBody = table.querySelector('.el-table__body-wrapper')\r\n          if (tableBody) {\r\n            // 创建新的滚动定时器\r\n            let scrollTop = 0\r\n            this.scrollTimer = setInterval(() => {\r\n              // 如果暂停滚动，则不执行滚动操作\r\n              if (this.tableScrollPaused) return;\r\n              \r\n              scrollTop++\r\n              if (scrollTop >= tableBody.scrollHeight / 2) {\r\n                scrollTop = 0\r\n              }\r\n              tableBody.scrollTop = scrollTop\r\n            }, this.scrollSpeed)\r\n            \r\n            // 创建鼠标事件处理函数\r\n            this.tableMouseEnterHandler = () => {\r\n              this.tableScrollPaused = true;\r\n            };\r\n            \r\n            this.tableMouseLeaveHandler = () => {\r\n              this.tableScrollPaused = false;\r\n            };\r\n            \r\n            // 添加鼠标悬停事件处理\r\n            tableBody.addEventListener('mouseenter', this.tableMouseEnterHandler);\r\n            tableBody.addEventListener('mouseleave', this.tableMouseLeaveHandler);\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 切换部门趋势图\r\n    switchDepartment() {\r\n      const currentIndex = this.departments.indexOf(this.currentDepartment)\r\n      const nextIndex = (currentIndex + 1) % this.departments.length\r\n      this.currentDepartment = this.departments[nextIndex]\r\n    },\r\n    \r\n    // 初始化部门完成率柱状图 - 修改为横向柱状图\r\n    initDepartmentCompletionChart() {\r\n      const chartDom = document.getElementById('departmentCompletionChart')\r\n      const chart = echarts.init(chartDom)\r\n      \r\n      // 计算默认显示的百分比\r\n      const defaultDisplayPercent = Math.min(6 / this.allIndicatorCompletionRates.length * 100, 100)\r\n      \r\n      // 使用指标级别的完成率数据\r\n      const labels = this.paginatedIndicatorRates.map(item => {\r\n        return {\r\n          deptName: item.department,\r\n          indicatorName: item.indicator,\r\n          fullText: `${item.department}\\n${item.indicator}`\r\n        }\r\n      })\r\n      const rates = this.paginatedIndicatorRates.map(item => item.completionRate)\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const data = params[0]\r\n            const item = this.paginatedIndicatorRates[data.dataIndex]\r\n            const actualText = item.isHigherBetter \r\n              ? `实际值: ${item.actual}${item.unit}`\r\n              : `实际值: ${item.actual}${item.unit} `\r\n            return `${item.department}-${item.indicator}<br/>\r\n                    目标值: ${item.target}${item.unit}<br/>\r\n                    ${actualText}<br/>\r\n                    完成率: ${item.completionRate}%`\r\n          }.bind(this)\r\n        },\r\n        grid: {\r\n          left: '0', // 左移30px\r\n          right: '0',\r\n          bottom: '3%',\r\n          top: '3%',\r\n          containLabel: true\r\n        },\r\n        dataZoom: [\r\n          {\r\n            type: 'slider',\r\n            show: true,\r\n            yAxisIndex: [0],\r\n            start: 0,\r\n            end: defaultDisplayPercent,\r\n            width: 10,\r\n            handleSize: 20,\r\n            showDetail: false,\r\n            zoomLock: false,\r\n            moveOnMouseWheel: true,\r\n            preventDefaultMouseMove: true\r\n          },\r\n          {\r\n            type: 'inside',\r\n            yAxisIndex: [0],\r\n            start: 0,\r\n            end: defaultDisplayPercent,\r\n            zoomLock: false\r\n          }\r\n        ],\r\n        toolbox: {\r\n          feature: {\r\n            dataZoom: {\r\n              yAxisIndex: 'none'\r\n            },\r\n            restore: {},\r\n            saveAsImage: {}\r\n          },\r\n          right: 10,\r\n          top: 0\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '完成率(%)',\r\n          min: -30, // 调整最小值为-30%，足够显示负数完成率\r\n          max: 30, // 调整最大值为30%，使图表更聚焦\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              type: 'dashed',\r\n              color: '#E0E6F1'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: labels.map(item => item.fullText),\r\n          axisLabel: {\r\n            formatter: function(value) {\r\n              return value;\r\n            },\r\n            color: '#333',\r\n            lineHeight: 16,\r\n            margin: 12,\r\n            rich: {\r\n              dept: {\r\n                fontWeight: 'bold',\r\n                lineHeight: 20\r\n              },\r\n              indicator: {\r\n                lineHeight: 20\r\n              }\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '完成率',\r\n            type: 'bar',\r\n            data: rates,\r\n            itemStyle: {\r\n              color: function(params) {\r\n                // 根据完成率设置不同颜色\r\n                if (params.data >= 100) {\r\n                  return '#5470c6' // 蓝色，超过100%\r\n                } else if (params.data >= 90) {\r\n                  return '#91cc75'  // 绿色，完成率高\r\n                } else if (params.data >= 70) {\r\n                  return '#fac858'  // 黄色，完成率中等\r\n                } else if (params.data >= 0) {\r\n                  return '#ee6666'  // 红色，完成率低\r\n                } else {\r\n                  return '#ff5252'  // 更深的红色，负数完成率\r\n                }\r\n              }\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'right',\r\n              formatter: '{c}%',\r\n              color: function(params) {\r\n                // 负数完成率用白色文字，更加明显\r\n                return params.data < 0 ? '#ffffff' : '#333333';\r\n              },\r\n              fontWeight: 'bold',\r\n              distance: 15\r\n            },\r\n            barWidth: '50%', // 柱子更细\r\n            barCategoryGap: '40%', // 增加柱子间距\r\n            animationDelay: function(idx) {\r\n              return idx * 100 + 100\r\n            }\r\n          }\r\n        ],\r\n        animationEasing: 'elasticOut',\r\n        animationDelayUpdate: function(idx) {\r\n          return idx * 5\r\n        }\r\n      }\r\n      \r\n      chart.setOption(option)\r\n      \r\n      // 保存dataZoom状态，防止滚动条回弹\r\n      chart.on('datazoom', function(params) {\r\n        const { start, end } = params\r\n        option.dataZoom[0].start = start\r\n        option.dataZoom[0].end = end\r\n        option.dataZoom[1].start = start\r\n        option.dataZoom[1].end = end\r\n        chart.setOption(option)\r\n      })\r\n      \r\n      // 创建事件处理器\r\n      this.chartMouseOverHandler = () => {\r\n        this.completionChartScrollPaused = true\r\n      }\r\n      \r\n      this.chartMouseOutHandler = () => {\r\n        this.completionChartScrollPaused = false\r\n      }\r\n      \r\n      // 添加鼠标悬停事件，暂停自动滚动\r\n      chartDom.addEventListener('mouseover', this.chartMouseOverHandler)\r\n      \r\n      // 添加鼠标离开事件，恢复自动滚动\r\n      chartDom.addEventListener('mouseout', this.chartMouseOutHandler)\r\n      \r\n      // 窗口大小变化时自动调整图表大小\r\n      window.addEventListener('resize', function() {\r\n        chart.resize()\r\n      })\r\n      \r\n      // 保存图表实例以便后续使用\r\n      this.completionChart = chart\r\n    },\r\n    \r\n    // 启动完成率图表自动滚动\r\n    startCompletionChartScroll() {\r\n      if (this.completionChartTimer) {\r\n        clearInterval(this.completionChartTimer)\r\n      }\r\n      \r\n      this.completionChartTimer = setInterval(() => {\r\n        if (this.completionChartScrollPaused) return\r\n        \r\n        if (!this.completionChart) return\r\n        \r\n        const option = this.completionChart.getOption()\r\n        let start = option.dataZoom[0].start\r\n        let end = option.dataZoom[0].end\r\n        const step = 0.1 // 每次滚动的百分比，改为0.1，使滚动非常缓慢\r\n        const range = end - start // 当前显示的范围\r\n        \r\n        // 根据滚动方向调整滚动位置\r\n        if (this.completionChartScrollDirection === 'down') {\r\n          // 向下滚动\r\n          if (end < 100) {\r\n            start += step\r\n            end += step\r\n          } else {\r\n            // 已到底部，改变方向\r\n            this.completionChartScrollDirection = 'up'\r\n          }\r\n        } else {\r\n          // 向上滚动\r\n          if (start > 0) {\r\n            start -= step\r\n            end -= step\r\n          } else {\r\n            // 已到顶部，改变方向\r\n            this.completionChartScrollDirection = 'down'\r\n          }\r\n        }\r\n        \r\n        // 更新滚动位置\r\n        option.dataZoom[0].start = start\r\n        option.dataZoom[0].end = end\r\n        option.dataZoom[1].start = start\r\n        option.dataZoom[1].end = end\r\n        this.completionChart.setOption(option)\r\n      }, 300) // 滚动间隔改为300毫秒，进一步减慢滚动速度\r\n    },\r\n    \r\n    // 初始化月度趋势折线图 - 显示单条折线\r\n    initMonthlyTrendChart() {\r\n      const chartDom = document.getElementById('monthlyTrendChart')\r\n      const chart = echarts.init(chartDom)\r\n      \r\n      // 查找当前部门的数据\r\n      const deptData = this.completionData.find(dept => dept.department === this.currentDepartment || dept.department.startsWith(this.currentDepartment))\r\n      \r\n      if (!deptData || !this.currentIndicator) {\r\n        return\r\n      }\r\n      \r\n      // 查找当前指标\r\n      const indicator = deptData.indicators.find(ind => ind.name === this.currentIndicator)\r\n      \r\n      if (!indicator) {\r\n        return\r\n      }\r\n      \r\n      // 准备月份数据\r\n      const months = ['1月', '2月', '3月', '4月']\r\n      \r\n      // 计算数据范围，以便设置y轴范围使折线居中\r\n      const values = indicator.values\r\n      const min = Math.min(...values)\r\n      const max = Math.max(...values)\r\n      const range = max - min\r\n      \r\n      // 设置y轴范围，扩大20%的范围使波动看起来更明显\r\n      const yMin = min - range * 0.4\r\n      const yMax = max + range * 0.4\r\n      \r\n      const option = {\r\n        title: {\r\n          text: `${this.currentDepartment} - ${this.currentIndicator}`,\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 16\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          formatter: function(params) {\r\n            return `${params[0].name}<br/>${indicator.name}: ${params[0].value} ${indicator.unit}<br/>目标值: ${indicator.target} ${indicator.unit}`\r\n          },\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        grid: {\r\n          left: '5%',\r\n          right: '5%',\r\n          bottom: '10%',\r\n          top: '60px',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: months,\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: indicator.unit,\r\n          min: yMin,\r\n          max: yMax,\r\n          axisLabel: {\r\n            formatter: function(value) {\r\n              return value.toFixed(2);\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              type: 'dashed'\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: indicator.name,\r\n            type: 'line',\r\n            data: indicator.values,\r\n            smooth: true, // 平滑曲线\r\n            symbol: 'emptyCircle',\r\n            symbolSize: 10,\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowColor: 'rgba(64, 158, 255, 0.5)'\r\n              },\r\n              scale: true\r\n            },\r\n            itemStyle: {\r\n              color: '#409EFF',\r\n              borderWidth: 2\r\n            },\r\n            lineStyle: {\r\n              width: 4,\r\n              shadowColor: 'rgba(0, 0, 0, 0.3)',\r\n              shadowBlur: 10,\r\n              shadowOffsetY: 5\r\n            },\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                {\r\n                  offset: 0,\r\n                  color: 'rgba(64, 158, 255, 0.7)'\r\n                },\r\n                {\r\n                  offset: 0.5,\r\n                  color: 'rgba(64, 158, 255, 0.3)'\r\n                },\r\n                {\r\n                  offset: 1,\r\n                  color: 'rgba(64, 158, 255, 0.1)'\r\n                }\r\n              ])\r\n            },\r\n            markLine: {\r\n              silent: true,\r\n              lineStyle: {\r\n                color: '#F56C6C',\r\n                type: 'dashed',\r\n                width: 2\r\n              },\r\n              data: [\r\n                {\r\n                  yAxis: indicator.target,\r\n                  label: {\r\n                    formatter: `目标值: ${indicator.target}`,\r\n                    position: 'insideEndTop',\r\n                    fontSize: 12,\r\n                    backgroundColor: 'rgba(245, 108, 108, 0.2)',\r\n                    padding: [2, 4],\r\n                    borderRadius: 2\r\n                  }\r\n                }\r\n              ]\r\n            },\r\n            animationDuration: 2000,\r\n            animationEasing: 'elasticOut',\r\n            animationDelay: function (idx) {\r\n              return idx * 200;\r\n            }\r\n          },\r\n          {\r\n            name: '目标值',\r\n            type: 'line',\r\n            data: Array(months.length).fill(indicator.target),\r\n            lineStyle: {\r\n              color: '#F56C6C',\r\n              type: 'dashed',\r\n              width: 2\r\n            },\r\n            symbol: 'none'\r\n          }\r\n        ],\r\n        legend: {\r\n          data: [indicator.name, '目标值'],\r\n          bottom: '0%'\r\n        }\r\n      }\r\n      \r\n      chart.setOption(option)\r\n    },\r\n    \r\n    // 初始化能源消耗子图表\r\n    initEnergyDetailCharts() {\r\n      // 检查当前选择的部门数据是否有效\r\n      if (!this.currentEnergyDept || !this.factoryEnergyData) return;\r\n      \r\n      // 重置警告状态\r\n      for (let key in this.isEnergyChartWarning) {\r\n        this.isEnergyChartWarning[key] = false;\r\n      }\r\n      \r\n      // 为每种能源类型创建单独的图表\r\n      this.energyTypes.forEach(type => {\r\n        const chartDom = document.getElementById('energySubchart_' + type.value);\r\n        if (!chartDom) return;\r\n        \r\n        // 清除之前的实例\r\n        const existingChart = echarts.getInstanceByDom(chartDom);\r\n        if (existingChart) {\r\n          existingChart.dispose();\r\n        }\r\n        \r\n        const chart = echarts.init(chartDom);\r\n        \r\n        const data = this.factoryEnergyData[type.value][this.currentEnergyDept] || [];\r\n        const months = this.factoryEnergyData.months;\r\n        const targetRange = this.energyTargetRanges[type.value];\r\n        \r\n        // 计算数据的最小值和最大值，以便设置y轴的范围\r\n        const minValue = Math.min(...data);\r\n        const maxValue = Math.max(...data);\r\n        const valueRange = maxValue - minValue;\r\n        \r\n        // 设置y轴的最小值和最大值，以使折线居中并增加波动感\r\n        // 通过缩小y轴范围使折线显得更加曲折\r\n        const yMin = minValue - valueRange * 0.3; // 下方预留30%的空间\r\n        const yMax = maxValue + valueRange * 0.3; // 上方预留30%的空间\r\n        \r\n        const option = {\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            formatter: function(params) {\r\n              const value = params[0].value;\r\n              return `${params[0].name}<br/>${type.label}: ${value} ${targetRange.unit}`;\r\n            }\r\n          },\r\n          grid: {\r\n            left: '10%',\r\n            right: '5%',\r\n            bottom: '15%',\r\n            top: '15%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: months,\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#333333' // 黑色坐标轴\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#333333' // 黑色坐标轴文字\r\n            },\r\n            boundaryGap: false // 让曲线从坐标轴开始\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: targetRange.unit,\r\n            min: yMin, // 设置最小值使折线居中\r\n            max: yMax, // 设置最大值使折线居中\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#333333' // 黑色坐标轴\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#333333', // 黑色坐标轴文字\r\n              formatter: function(value) {\r\n                // 保留适当的小数位数以避免过度拥挤\r\n                if (value >= 100) {\r\n                  return value.toFixed(0);\r\n                } else {\r\n                  return value.toFixed(1);\r\n                }\r\n              }\r\n            },\r\n            splitLine: {\r\n              lineStyle: {\r\n                type: 'dashed',\r\n                color: '#E0E6F1'\r\n              }\r\n            }\r\n          },\r\n          series: [\r\n            {\r\n              name: type.label,\r\n              type: 'line',\r\n              data: data,\r\n              smooth: true, // 使用平滑曲线\r\n              smoothMonotone: 'none', // 不保持单调性，允许更多波动\r\n              symbol: 'circle',\r\n              symbolSize: 8,\r\n              sampling: 'average', // 使用平均采样\r\n              lineStyle: {\r\n                width: 4, // 加粗线条\r\n                color: type.color // 使用更深的颜色\r\n              },\r\n              itemStyle: {\r\n                color: type.color,\r\n                borderWidth: 2,\r\n                borderColor: '#fff',\r\n                shadowColor: 'rgba(0, 0, 0, 0.3)',\r\n                shadowBlur: 5\r\n              },\r\n              emphasis: {\r\n                itemStyle: {\r\n                  borderWidth: 3,\r\n                  shadowBlur: 10\r\n                },\r\n                lineStyle: {\r\n                  width: 6 // 鼠标悬停时线条更粗\r\n                }\r\n              },\r\n              areaStyle: {\r\n                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                  {\r\n                    offset: 0,\r\n                    color: this.hexToRgba(type.color, 0.6) // 更高的透明度使颜色更深\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: this.hexToRgba(type.color, 0.1)\r\n                  }\r\n                ])\r\n              }\r\n            }\r\n          ]\r\n        };\r\n        \r\n        chart.setOption(option);\r\n      });\r\n    },\r\n    \r\n    // 颜色转换辅助函数\r\n    hexToRgba(hex, alpha) {\r\n      const r = parseInt(hex.slice(1, 3), 16);\r\n      const g = parseInt(hex.slice(3, 5), 16);\r\n      const b = parseInt(hex.slice(5, 7), 16);\r\n      return `rgba(${r}, ${g}, ${b}, ${alpha})`;\r\n    },\r\n    \r\n    // 启动能源部门自动切换\r\n    startEnergyDeptSwitch() {\r\n      // 停止之前的能源部门切换\r\n      if (this.energyDeptTimer) {\r\n        clearInterval(this.energyDeptTimer)\r\n      }\r\n      \r\n      // 启动新的能源部门切换\r\n      this.energyDeptTimer = setInterval(() => {\r\n        this.switchEnergyDept()\r\n      }, 10000) // 每10秒切换一次部门，原来是5000\r\n    },\r\n    \r\n    // 切换能源部门\r\n    switchEnergyDept() {\r\n      const currentIndex = this.allFactories.indexOf(this.currentEnergyDept)\r\n      const nextIndex = (currentIndex + 1) % this.allFactories.length\r\n      this.currentEnergyDept = this.allFactories[nextIndex]\r\n      this.initEnergyDetailCharts()\r\n    },\r\n    \r\n    // 窗口大小变化时重新渲染图表\r\n    resizeCharts() {\r\n      const chartIds = [\r\n        'departmentCompletionChart',\r\n        'monthlyTrendChart'\r\n      ]\r\n      \r\n      chartIds.forEach(id => {\r\n        const chartDom = document.getElementById(id)\r\n        if (chartDom) {\r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.resize()\r\n          }\r\n        }\r\n      })\r\n      \r\n      // 调整能源子图表大小\r\n      this.energyTypes.forEach(type => {\r\n        const chartDom = document.getElementById('energySubchart_' + type.value)\r\n        if (chartDom) {\r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.resize()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 销毁图表实例\r\n    disposeCharts() {\r\n      const chartIds = [\r\n        'departmentCompletionChart',\r\n        'monthlyTrendChart'\r\n      ]\r\n      \r\n      chartIds.forEach(id => {\r\n        const chartDom = document.getElementById(id)\r\n        if (chartDom) {\r\n          // 移除事件监听器\r\n          if (id === 'departmentCompletionChart' && this.chartMouseOverHandler && this.chartMouseOutHandler) {\r\n            chartDom.removeEventListener('mouseover', this.chartMouseOverHandler)\r\n            chartDom.removeEventListener('mouseout', this.chartMouseOutHandler)\r\n          }\r\n          \r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.dispose()\r\n          }\r\n        }\r\n      })\r\n      \r\n      // 清除完成率图表引用\r\n      this.completionChart = null\r\n      \r\n      // 销毁能源子图表\r\n      this.energyTypes.forEach(type => {\r\n        const chartDom = document.getElementById('energySubchart_' + type.value)\r\n        if (chartDom) {\r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.dispose()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n\r\n    // 启动事业部切换\r\n    startBusinessUnitSwitch() {\r\n      // 停止之前的事业部切换\r\n      if (this.businessUnitTimer) {\r\n        clearInterval(this.businessUnitTimer)\r\n      }\r\n      \r\n      // 启动新的事业部切换\r\n      this.businessUnitTimer = setInterval(() => {\r\n        this.switchBusinessUnit()\r\n      }, 30000) // 事业部切换频率为30秒\r\n    },\r\n    \r\n    // 切换事业部\r\n    switchBusinessUnit() {\r\n      const currentIndex = this.departments.indexOf(this.currentBusinessUnit)\r\n      const nextIndex = (currentIndex + 1) % this.departments.length\r\n      this.currentBusinessUnit = this.departments[nextIndex]\r\n    },\r\n    \r\n    // 初始化关键能源指标对比图\r\n    initKeyEnergyIndicatorsChart() {\r\n      const chartDom = document.getElementById('keyEnergyIndicatorsChart')\r\n      const chart = echarts.init(chartDom)\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const dataIndex = params[0].dataIndex\r\n            const indicator = this.keyEnergyIndicators[dataIndex]\r\n            const changeText = indicator.change > 0 ? `+${indicator.change}%` : `${indicator.change}%`\r\n            const changeColor = indicator.status === 'good' ? '#67C23A' : indicator.status === 'warning' ? '#E6A23C' : '#F56C6C'\r\n            \r\n            return `\r\n              <div style=\"font-weight:bold\">${indicator.name}</div>\r\n              <div>今日: ${indicator.today} ${indicator.unit}</div>\r\n              <div>昨日: ${indicator.yesterday} ${indicator.unit}</div>\r\n              <div>变化: <span style=\"color:${changeColor}\">${changeText}</span></div>\r\n            `\r\n          }.bind(this)\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.keyEnergyIndicators.map(item => item.name)\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            formatter: function(value) {\r\n              if (value >= 100) {\r\n                return value.toFixed(0)\r\n              } else {\r\n                return value.toFixed(1)\r\n              }\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '今日值',\r\n            type: 'bar',\r\n            data: this.keyEnergyIndicators.map((item, index) => {\r\n              return {\r\n                value: item.today,\r\n                itemStyle: {\r\n                  color: item.status === 'good' ? '#67C23A' : item.status === 'warning' ? '#E6A23C' : '#F56C6C'\r\n                }\r\n              }\r\n            }),\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: function(params) {\r\n                const index = params.dataIndex\r\n                return this.keyEnergyIndicators[index].today\r\n              }.bind(this)\r\n            },\r\n            barWidth: '30%'\r\n          },\r\n          {\r\n            name: '昨日值',\r\n            type: 'bar',\r\n            data: this.keyEnergyIndicators.map(item => item.yesterday),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#909399',\r\n              opacity: 0.5\r\n            }\r\n          }\r\n        ],\r\n        legend: {\r\n          data: ['今日值', '昨日值'],\r\n          bottom: 0\r\n        }\r\n      }\r\n      \r\n      chart.setOption(option)\r\n    },\r\n    \r\n    // 根据索引位置计算渐变颜色样式\r\n    getGradientStyle(index, total) {\r\n      // 计算该指标在整体中的相对位置（0-1之间）\r\n      const position = index / (total - 1);\r\n      \r\n      // 定义绿色到红色的渐变颜色数组\r\n      const colorStops = [\r\n        '#52c41a', // 亮绿色\r\n        '#85ce61', // 绿色\r\n        '#b3e19d', // 浅绿色\r\n        '#d4f8be', // 非常浅的绿色\r\n        '#faff72', // 黄色\r\n        '#fadb14', // 金黄色\r\n        '#ffa940', // 橙色\r\n        '#fa8c16', // 深橙色\r\n        '#ff7875', // 浅红色\r\n        '#ff4d4f', // 红色\r\n        '#f5222d', // 亮红色\r\n        '#cf1322'  // 深红色\r\n      ];\r\n      \r\n      // 根据位置在渐变色之间插值获取颜色\r\n      // 找到对应的颜色区间\r\n      const segmentCount = colorStops.length - 1;\r\n      const segment = Math.min(Math.floor(position * segmentCount), segmentCount - 1);\r\n      const localPosition = (position * segmentCount) - segment; // 在当前区间内的相对位置 (0-1)\r\n      \r\n      // 获取区间的起止颜色\r\n      const startColor = colorStops[segment];\r\n      const endColor = colorStops[segment + 1];\r\n      \r\n      // 在两个颜色之间插值\r\n      const bgColor = this.interpolateColors(startColor, endColor, localPosition);\r\n      \r\n      // 计算文字颜色\r\n      // 提取RGB并计算亮度\r\n      let r = parseInt(bgColor.slice(1, 3), 16);\r\n      let g = parseInt(bgColor.slice(3, 5), 16);\r\n      let b = parseInt(bgColor.slice(5, 7), 16);\r\n      const brightness = (r * 299 + g * 587 + b * 114) / 1000;\r\n      \r\n      // 亮度大于140使用深色文字，否则使用浅色文字\r\n      let textColor = brightness > 140 ? '#333333' : '#ffffff';\r\n      \r\n      // 设置边框颜色（比背景色稍深）\r\n      const borderColor = this.adjustColor(bgColor, -20);\r\n      \r\n      return {\r\n        backgroundColor: bgColor,\r\n        color: textColor,\r\n        borderTopColor: borderColor\r\n      };\r\n    },\r\n    \r\n    // 颜色插值函数\r\n    interpolateColors(color1, color2, factor) {\r\n      // 解析颜色\r\n      let r1 = parseInt(color1.slice(1, 3), 16);\r\n      let g1 = parseInt(color1.slice(3, 5), 16);\r\n      let b1 = parseInt(color1.slice(5, 7), 16);\r\n      \r\n      let r2 = parseInt(color2.slice(1, 3), 16);\r\n      let g2 = parseInt(color2.slice(3, 5), 16);\r\n      let b2 = parseInt(color2.slice(5, 7), 16);\r\n      \r\n      // 线性插值\r\n      let r = Math.round(r1 + factor * (r2 - r1));\r\n      let g = Math.round(g1 + factor * (g2 - g1));\r\n      let b = Math.round(b1 + factor * (b2 - b1));\r\n      \r\n      // 转回十六进制\r\n      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;\r\n    },\r\n    \r\n    // 获取徽章样式\r\n    getBadgeStyle(index, total) {\r\n      // 获取当前卡片的背景色\r\n      const position = index / (total - 1);\r\n      const colorStops = [\r\n        '#52c41a', // 亮绿色\r\n        '#85ce61', // 绿色\r\n        '#b3e19d', // 浅绿色\r\n        '#d4f8be', // 非常浅的绿色\r\n        '#faff72', // 黄色\r\n        '#fadb14', // 金黄色\r\n        '#ffa940', // 橙色\r\n        '#fa8c16', // 深橙色\r\n        '#ff7875', // 浅红色\r\n        '#ff4d4f', // 红色\r\n        '#f5222d', // 亮红色\r\n        '#cf1322'  // 深红色\r\n      ];\r\n      \r\n      const segmentCount = colorStops.length - 1;\r\n      const segment = Math.min(Math.floor(position * segmentCount), segmentCount - 1);\r\n      const localPosition = (position * segmentCount) - segment;\r\n      \r\n      const startColor = colorStops[segment];\r\n      const endColor = colorStops[segment + 1];\r\n      \r\n      const bgColor = this.interpolateColors(startColor, endColor, localPosition);\r\n      \r\n      // 计算文字颜色\r\n      let r = parseInt(bgColor.slice(1, 3), 16);\r\n      let g = parseInt(bgColor.slice(3, 5), 16);\r\n      let b = parseInt(bgColor.slice(5, 7), 16);\r\n      const brightness = (r * 299 + g * 587 + b * 114) / 1000;\r\n      \r\n      // 根据背景亮度设置徽章样式\r\n      if (brightness > 140) {\r\n        // 浅色背景使用深色边框的徽章\r\n        const badgeBgColor = this.adjustColor(bgColor, -40);\r\n        return {\r\n          backgroundColor: badgeBgColor,\r\n          color: '#ffffff',\r\n          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\r\n        };\r\n      } else {\r\n        // 深色背景使用浅色徽章\r\n        return {\r\n          backgroundColor: 'rgba(255, 255, 255, 0.25)',\r\n          color: '#ffffff',\r\n          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\r\n        };\r\n      }\r\n    },\r\n    \r\n    // 颜色调整辅助函数\r\n    adjustColor(color, amount) {\r\n      // 如果是白色特殊处理\r\n      if (color === '#ffffff') {\r\n        return '#e0e0e0';\r\n      }\r\n      \r\n      // 将颜色转换为RGB\r\n      let r = parseInt(color.slice(1, 3), 16);\r\n      let g = parseInt(color.slice(3, 5), 16);\r\n      let b = parseInt(color.slice(5, 7), 16);\r\n      \r\n      // 调整亮度\r\n      r = Math.max(0, Math.min(255, r + amount));\r\n      g = Math.max(0, Math.min(255, g + amount));\r\n      b = Math.max(0, Math.min(255, b + amount));\r\n      \r\n      // 转回十六进制\r\n      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;\r\n    },\r\n    \r\n    // 初始化指标卡片滚动\r\n    initIndicatorCardsScroll() {\r\n      // 清除之前的滚动定时器\r\n      if (this.indicatorCardsScrollTimer) {\r\n        clearInterval(this.indicatorCardsScrollTimer);\r\n      }\r\n      \r\n      const container = this.$refs.indicatorCardsContainer;\r\n      if (!container) return;\r\n      \r\n      // 计算滚动所需参数\r\n      let scrollTop = 0;\r\n      const scrollHeight = container.scrollHeight;\r\n      const clientHeight = container.clientHeight;\r\n      const maxScroll = scrollHeight - clientHeight;\r\n      \r\n      // 如果内容不足以滚动，直接返回\r\n      if (maxScroll <= 0) return;\r\n      \r\n      // 滚动步长和速度\r\n      const step = 0.5; // 滚动步长更小，使滚动更平滑\r\n      const scrollInterval = 20; // 滚动更新频率更高，更流畅\r\n      \r\n      this.indicatorCardsScrollTimer = setInterval(() => {\r\n        // 鼠标悬停时暂停滚动\r\n        if (this.indicatorCardsScrollPaused) return;\r\n        \r\n        scrollTop += step;\r\n        \r\n        // 当滚动到底部时，快速回到顶部并继续滚动\r\n        if (scrollTop >= maxScroll) {\r\n          // 重置滚动位置到顶部\r\n          scrollTop = 0;\r\n          container.scrollTop = 0;\r\n          \r\n          // 短暂暂停一下，让用户能看到回到顶部的过程\r\n          this.indicatorCardsScrollPaused = true;\r\n          setTimeout(() => {\r\n            this.indicatorCardsScrollPaused = false;\r\n          }, 1000);\r\n        } else {\r\n          container.scrollTop = scrollTop;\r\n        }\r\n      }, scrollInterval);\r\n    },\r\n\r\n    // 表头样式\r\n    headerCellStyle() {\r\n      return {\r\n        backgroundColor: \"#f5f7fa\",\r\n        color: \"#606266\",\r\n        fontWeight: \"bold\",\r\n      };\r\n    },\r\n\r\n        // 显示指标详情\r\n        showDetails(row) {\r\n      this.currentIndicator = row;\r\n      this.detailDialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.initDetailChart();\r\n      });\r\n    },\r\n\r\n    // 初始化详情图表\r\n    initDetailChart() {\r\n      if (!this.currentIndicator) return;\r\n\r\n      const chartDom = document.getElementById(\"indicatorChart\");\r\n      const myChart = echarts.init(chartDom);\r\n\r\n      const months = [\"1月\", \"2月\", \"3月\", \"4月\", \"5月\"];\r\n      \r\n      // 生成波动较大的数据，确保围绕目标值有明显起伏\r\n      const targetValue = parseFloat(this.currentIndicator.target) || 100;\r\n      \r\n      // 使用实际数据，如果没有则生成波动数据\r\n      let actualData = [];\r\n      if (this.currentIndicator.monthlyData && this.currentIndicator.monthlyData.length === 5) {\r\n        actualData = this.currentIndicator.monthlyData;\r\n      } else {\r\n        // 生成波动数据，确保有起伏\r\n        const fluctuationRange = targetValue * 0.3; // 波动范围为目标值的30%\r\n        actualData = months.map(() => {\r\n          const fluctuation = (Math.random() - 0.5) * 2 * fluctuationRange;\r\n          return Math.max(0, targetValue + fluctuation);\r\n        });\r\n      }\r\n      \r\n      // 目标值线\r\n      const targetData = Array(5).fill(targetValue);\r\n\r\n      const option = {\r\n        title: {\r\n          text: `${this.currentIndicator.name}月度趋势`,\r\n          left: \"center\",\r\n        },\r\n        tooltip: {\r\n          trigger: \"axis\",\r\n          axisPointer: {\r\n            type: \"cross\",\r\n          },\r\n          formatter: function(params) {\r\n            const actualValue = params[0].value.toFixed(2);\r\n            const targetValue = params[1].value.toFixed(2);\r\n            const diff = (actualValue - targetValue).toFixed(2);\r\n            // 使用统一的白色显示差值\r\n            const diffColor = 'color:#ffffff';\r\n            \r\n            return `${params[0].name}<br/>\r\n                   ${params[0].marker} ${params[0].seriesName}: ${actualValue}<br/>\r\n                   ${params[1].marker} ${params[1].seriesName}: ${targetValue}<br/>\r\n                   <span style=\"${diffColor}\">差值: ${diff}</span>`;\r\n          }\r\n        },\r\n        legend: {\r\n          data: [\"实际值\", \"目标值\"],\r\n          bottom: 10,\r\n        },\r\n        grid: {\r\n          left: \"3%\",\r\n          right: \"4%\",\r\n          bottom: \"15%\",\r\n          containLabel: true,\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          boundaryGap: false,\r\n          data: months,\r\n        },\r\n        yAxis: {\r\n          type: \"value\",\r\n          name: this.currentIndicator.unit,\r\n          axisLabel: {\r\n            formatter: \"{value}\",\r\n          },\r\n          scale: true, // 缩放Y轴以突出显示数据波动\r\n        },\r\n        series: [\r\n          {\r\n            name: \"实际值\",\r\n            type: \"line\",\r\n            data: actualData,\r\n            itemStyle: {\r\n              color: \"#409EFF\",\r\n            },\r\n            lineStyle: {\r\n              width: 3,\r\n            },\r\n            symbol: \"circle\",\r\n            symbolSize: 8,\r\n            markPoint: {\r\n              data: [\r\n                { type: \"max\", name: \"最大值\" },\r\n                { type: \"min\", name: \"最小值\" }\r\n              ]\r\n            }\r\n          },\r\n          {\r\n            name: \"目标值\",\r\n            type: \"line\",\r\n            data: targetData,\r\n            itemStyle: {\r\n              color: \"#F56C6C\",\r\n            },\r\n            lineStyle: {\r\n              width: 2,\r\n              type: \"dashed\",\r\n            },\r\n            markLine: {\r\n              data: [{ type: \"average\", name: \"目标值\" }],\r\n              label: {\r\n                formatter: \"目标值: {c}\"\r\n              }\r\n            }\r\n          },\r\n        ],\r\n      };\r\n\r\n      myChart.setOption(option);\r\n\r\n      // 响应式处理\r\n      window.addEventListener(\"resize\", () => {\r\n        myChart.resize();\r\n      });\r\n    },\r\n\r\n    // 关闭对话框\r\n    handleDialogClose() {\r\n      this.detailDialogVisible = false;\r\n      this.currentIndicator = null;\r\n      // 清除图表实例\r\n      const chartDom = document.getElementById(\"indicatorChart\");\r\n      if (chartDom) {\r\n        const chart = echarts.getInstanceByDom(chartDom);\r\n        if (chart) {\r\n          chart.dispose();\r\n        }\r\n      }\r\n    },\r\n        // 处理表格单元格合并\r\n        objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 仅对分厂和指标名称列进行合并\r\n      if (columnIndex === 0 || columnIndex === 1) {\r\n        // 获取当前行数据\r\n        const currentRow = this.techIndicators[rowIndex];\r\n        if (!currentRow) return { rowspan: 1, colspan: 1 };\r\n\r\n        // 分厂列合并处理\r\n        if (columnIndex === 0) {\r\n          // 如果是第一行或者与前一行分厂不同，则计算合并行数\r\n          if (\r\n            rowIndex === 0 ||\r\n            currentRow.factory !== this.techIndicators[rowIndex - 1].factory\r\n          ) {\r\n            // 计算连续相同分厂的行数\r\n            let rowspan = 1;\r\n            for (let i = rowIndex + 1; i < this.techIndicators.length; i++) {\r\n              if (this.techIndicators[i].factory === currentRow.factory) {\r\n                rowspan++;\r\n              } else {\r\n                break;\r\n              }\r\n            }\r\n            return { rowspan, colspan: 1 };\r\n          } else {\r\n            // 如果当前行与前一行分厂相同，则隐藏当前单元格\r\n            return { rowspan: 0, colspan: 0 };\r\n          }\r\n        }\r\n\r\n        // 指标名称列合并处理\r\n        if (columnIndex === 1) {\r\n          // 如果是第一行，或者与前一行分厂不同，或者分厂相同但指标名称不同\r\n          if (\r\n            rowIndex === 0 ||\r\n            currentRow.factory !== this.techIndicators[rowIndex - 1].factory ||\r\n            currentRow.name !== this.techIndicators[rowIndex - 1].name\r\n          ) {\r\n            // 计算连续相同分厂和指标名称的行数\r\n            let rowspan = 1;\r\n            for (let i = rowIndex + 1; i < this.techIndicators.length; i++) {\r\n              if (\r\n                this.techIndicators[i].factory === currentRow.factory &&\r\n                this.techIndicators[i].name === currentRow.name\r\n              ) {\r\n                rowspan++;\r\n              } else {\r\n                break;\r\n              }\r\n            }\r\n            return { rowspan, colspan: 1 };\r\n          } else {\r\n            // 如果当前行与前一行分厂和指标名称都相同，则隐藏当前单元格\r\n            return { rowspan: 0, colspan: 0 };\r\n          }\r\n        }\r\n      }\r\n      return { rowspan: 1, colspan: 1 };\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.page-title {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.page-title h2 {\r\n  font-size: 24px;\r\n  color: #303133;\r\n  margin: 0;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.box-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.section-header {\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n  color: #333;\r\n  padding: 5px 0;\r\n  border-bottom: 2px solid rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.tech-economic-section .section-header {\r\n  color: #4a6ee0;\r\n  border-bottom-color: #4a6ee0;\r\n}\r\n\r\n.energy-section .section-header {\r\n  color: #47b475;\r\n  border-bottom-color: #47b475;\r\n}\r\n\r\n.chart-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin: 0 -10px;\r\n}\r\n\r\n.top-charts-row,\r\n.energy-charts-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: nowrap;\r\n  width: 100%;\r\n  height: 600px!important;\r\n}\r\n\r\n.chart-item {\r\n  flex: 1;\r\n  min-width: 32%;\r\n  margin: 10px;\r\n  background-color: #fff;\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.chart-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.trend-controls {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.chart {\r\n  height: 340px;\r\n  width: 100%;\r\n}\r\n\r\n.scroll-table-container {\r\n  height: 340px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.scroll-table {\r\n  height: 100%;\r\n  overflow-y: hidden; /* 修改为hidden，使用JS控制滚动 */\r\n  scrollbar-width: thin;\r\n}\r\n\r\n.completion-rate {\r\n  font-weight: bold;\r\n}\r\n\r\n.completion-rate.red {\r\n  color: #F56C6C;\r\n}\r\n\r\n.completion-rate.deep-red {\r\n  color: #ff0000;\r\n  font-weight: bolder;\r\n}\r\n\r\n.completion-rate.green {\r\n  color: #67C23A;\r\n}\r\n\r\n.energy-stats {\r\n  padding: 10px;\r\n}\r\n\r\n.stat-card {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  border-radius: 6px;\r\n  text-align: center;\r\n  margin-bottom: 15px;\r\n  border-left: 4px solid #409EFF;\r\n  transition: all 0.3s;\r\n  height: 100px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stat-card.good {\r\n  border-left-color: #67C23A;\r\n}\r\n\r\n.stat-card.warning {\r\n  border-left-color: #E6A23C;\r\n}\r\n\r\n.stat-card.danger {\r\n  border-left-color: #F56C6C;\r\n}\r\n\r\n.stat-title {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  margin: 5px 0;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 动画效果 */\r\n.box-card {\r\n  animation: fadeIn 0.6s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.tech-economic-section {\r\n  animation-delay: 0.1s;\r\n  background-color: #e6f0ff; /* 修改技经指标部分的背景色，更深的蓝色背景 */\r\n}\r\n\r\n.energy-section {\r\n  animation-delay: 0.3s;\r\n  background-color: #e6fff0; /* 修改能源指标部分的背景色，更深的绿色背景 */\r\n}\r\n\r\n/* 修改卡片背景色 */\r\n.chart-item {\r\n  background-color: #ffffff; /* 白色背景 */\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 增强阴影 */\r\n  border: 1px solid rgba(0, 0, 0, 0.05); /* 添加细边框 */\r\n}\r\n\r\n/* 技经指标部分的卡片特殊样式 */\r\n.tech-economic-section .chart-item {\r\n  background-color: #f8faff; /* 浅蓝色调背景 */\r\n  border-top: 3px solid #4a6ee0; /* 蓝色上边框 */\r\n}\r\n\r\n/* 能源指标部分的卡片特殊样式 */\r\n.energy-section .chart-item {\r\n  background-color: #f8fff9; /* 浅绿色调背景 */\r\n  border-top: 3px solid #47b475; /* 绿色上边框 */\r\n}\r\n\r\n/* 媒体查询，适应不同屏幕尺寸 */\r\n@media screen and (max-width: 1600px) {\r\n  .top-charts-row,\r\n  .energy-charts-row {\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .chart-item {\r\n    min-width: 45%;\r\n    flex: 0 0 45%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .chart-item {\r\n    min-width: 100%;\r\n    flex: 0 0 100%;\r\n  }\r\n}\r\n\r\n/* 未完成指标表格样式 */\r\n.el-table {\r\n  border: none;\r\n}\r\n\r\n.el-table::before, .el-table::after {\r\n  content: none;\r\n}\r\n\r\n.el-table td.el-table__cell,\r\n.el-table th.el-table__cell {\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.el-table th.el-table__cell {\r\n  background-color: #f8f8f8;\r\n  color: #606266;\r\n  font-weight: bold;\r\n  font-size: 15px;\r\n}\r\n\r\n.el-table td.el-table__cell {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  padding: 8px 0;\r\n}\r\n\r\n.el-table__row:hover > td {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.energy-type-selector {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.energy-detail-container {\r\n  height: 340px;\r\n  overflow: auto;\r\n}\r\n\r\n.status-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-indicator.normal {\r\n  background-color: #f0f9eb;\r\n  color: #67C23A;\r\n}\r\n\r\n.status-indicator.warning {\r\n  background-color: #fdf6ec;\r\n  color: #E6A23C;\r\n}\r\n\r\n.status-indicator.danger {\r\n  background-color: #fef0f0;\r\n  color: #F56C6C;\r\n}\r\n\r\n.status-indicator i {\r\n  margin-left: 5px;\r\n  font-size: 16px;\r\n}\r\n\r\n.trend-value {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n}\r\n\r\n.trend-value.up {\r\n  color: #F56C6C;\r\n}\r\n\r\n.trend-value.down {\r\n  color: #67C23A;\r\n}\r\n\r\n.trend-value i {\r\n  margin-right: 3px;\r\n}\r\n\r\n.key-indicators-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  margin-top: 20px;\r\n  height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.indicator-card {\r\n  flex: 0 0 23%;\r\n  margin-bottom: 12px;\r\n  padding: 10px;\r\n  background-color: #f0f7ff;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  text-align: center;\r\n  position: relative;\r\n  border-left: 4px solid #409EFF;\r\n  transition: all 0.3s;\r\n  height: 125px;\r\n}\r\n\r\n.indicator-card.danger {\r\n  background-color: #dc143c; /* 猩红色 */\r\n  color: white;\r\n}\r\n\r\n.indicator-card.danger .indicator-title,\r\n.indicator-card.danger .indicator-value,\r\n.indicator-card.danger .indicator-target,\r\n.indicator-card.danger .indicator-unit,\r\n.indicator-card.danger .indicator-compare {\r\n  color: white;\r\n}\r\n\r\n.indicator-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.indicator-card.good {\r\n  border-left-color: #67C23A;\r\n  background-color: #f0fff5;\r\n}\r\n\r\n.indicator-card.warning {\r\n  border-left-color: #E6A23C;\r\n  background-color: #fffbf0;\r\n}\r\n\r\n.indicator-card.danger {\r\n  border-left-color: #F56C6C;\r\n}\r\n\r\n.indicator-title {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 3px;\r\n  font-weight: bold;\r\n}\r\n\r\n.indicator-value {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  margin: 3px 0;\r\n}\r\n\r\n.indicator-target {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.indicator-unit {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.indicator-compare {\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n\r\n.indicator-change.up {\r\n  color: #67C23A;\r\n}\r\n\r\n.indicator-change.down {\r\n  color: #F56C6C;\r\n}\r\n\r\n.energy-chart-half {\r\n  flex: 0 0 48%;\r\n  min-width: 48%;\r\n}\r\n\r\n.energy-subchart-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  height: 500px;\r\n}\r\n\r\n.energy-subchart {\r\n  flex: 0 0 48%;\r\n  height: 48%;\r\n  margin-bottom: 10px;\r\n  border-radius: 4px;\r\n  padding: 5px;\r\n  background-color: #f0f8ff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s;\r\n  border: 1px solid #d1e6ff;\r\n}\r\n\r\n.energy-chart-warning {\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n  border: 1px solid rgba(245, 108, 108, 0.3);\r\n}\r\n\r\n.subchart-title {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  text-align: center;\r\n  margin-bottom: 5px;\r\n  background-color: #e6f0ff;\r\n  padding: 3px 0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.subchart {\r\n  height: calc(100% - 0px);\r\n  width: 100%;\r\n}\r\n\r\n.chart-wrapper {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 400px;\r\n  overflow: hidden;\r\n}\r\n\r\n.chart {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n/* 指标卡片样式 */\r\n.indicator-cards-container {\r\n  height: 320px;\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  position: relative;\r\n  scrollbar-width: thin;\r\n  scrollbar-color: #e0e0e0 #f8f8f8;\r\n  padding: 6px 3px;\r\n}\r\n\r\n.indicator-cards-container::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.indicator-cards-container::-webkit-scrollbar-track {\r\n  background: #f8f8f8;\r\n}\r\n\r\n.indicator-cards-container::-webkit-scrollbar-thumb {\r\n  background-color: #e0e0e0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.indicator-cards-wrapper {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr); /* 一行显示3个 */\r\n  gap: 10px; /* 更小的间距 */\r\n  max-width: 100%;\r\n}\r\n\r\n.indicator-card-item {\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\r\n  padding: 8px;\r\n  transition: all 0.3s ease;\r\n  border-top: 3px solid #909399;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 110px; /* 更小的高度 */\r\n  width: 100%;\r\n  max-width: 100%;\r\n  margin: 0 auto; /* 居中显示 */\r\n}\r\n\r\n.indicator-card-item:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.indicator-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.indicator-name {\r\n  font-size: 13px;\r\n  font-weight: bold;\r\n  flex: 1;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.completion-badge {\r\n  background-color: #f0f0f0;\r\n  padding: 1px 5px;\r\n  border-radius: 6px;\r\n  font-size: 10px;\r\n  font-weight: bold;\r\n  min-width: 40px;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n  margin-left: 4px;\r\n}\r\n\r\n.indicator-card-body {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.indicator-department {\r\n  font-size: 11px;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  font-weight: 500;\r\n}\r\n\r\n.indicator-values {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 3px;\r\n}\r\n\r\n.value-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2px;\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n  padding: 1px 4px;\r\n  border-radius: 2px;\r\n}\r\n\r\n.value-label {\r\n  font-size: 10px;\r\n  font-weight: 500;\r\n}\r\n\r\n.value-number {\r\n  font-size: 11px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 不同完成率的卡片样式 */\r\n.indicator-card-item.excellent {\r\n  background-color: #67c23a;\r\n  border-top-color: #4d9e29;\r\n}\r\n\r\n.indicator-card-item.excellent .indicator-name,\r\n.indicator-card-item.excellent .indicator-department,\r\n.indicator-card-item.excellent .value-number,\r\n.indicator-card-item.excellent .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.excellent .completion-badge {\r\n  background-color: #4d9e29;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-good {\r\n  background-color: #85ce61;\r\n  border-top-color: #67c23a;\r\n}\r\n\r\n.indicator-card-item.very-good .indicator-name,\r\n.indicator-card-item.very-good .indicator-department,\r\n.indicator-card-item.very-good .value-number,\r\n.indicator-card-item.very-good .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-good .completion-badge {\r\n  background-color: #67c23a;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.good {\r\n  background-color: #b3e19d;\r\n  border-top-color: #85ce61;\r\n}\r\n\r\n.indicator-card-item.good .indicator-name,\r\n.indicator-card-item.good .indicator-department,\r\n.indicator-card-item.good .value-number {\r\n  color: #2e2e2e;\r\n}\r\n\r\n.indicator-card-item.good .completion-badge {\r\n  background-color: #85ce61;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.normal {\r\n  background-color: #f0f9eb;\r\n  border-top-color: #b3e19d;\r\n}\r\n\r\n.indicator-card-item.warning {\r\n  background-color: #fdf6ec;\r\n  border-top-color: #e6a23c;\r\n}\r\n\r\n.indicator-card-item.warning .completion-badge {\r\n  background-color: #e6a23c;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.bad {\r\n  background-color: #fef0f0;\r\n  border-top-color: #f56c6c;\r\n}\r\n\r\n.indicator-card-item.bad .completion-badge {\r\n  background-color: #f56c6c;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-bad {\r\n  background-color: #f56c6c;\r\n  border-top-color: #d63b3b;\r\n}\r\n\r\n.indicator-card-item.very-bad .indicator-name,\r\n.indicator-card-item.very-bad .indicator-department,\r\n.indicator-card-item.very-bad .value-number,\r\n.indicator-card-item.very-bad .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-bad .completion-badge {\r\n  background-color: #d63b3b;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.terrible {\r\n  background-color: #dc143c; /* 猩红色 */\r\n  border-top-color: #a00f2d;\r\n}\r\n\r\n.indicator-card-item.terrible .indicator-name,\r\n.indicator-card-item.terrible .indicator-department,\r\n.indicator-card-item.terrible .value-number,\r\n.indicator-card-item.terrible .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.terrible .completion-badge {\r\n  background-color: #a00f2d;\r\n  color: #ffffff;\r\n}\r\n</style>\r\n\r\n"]}]}