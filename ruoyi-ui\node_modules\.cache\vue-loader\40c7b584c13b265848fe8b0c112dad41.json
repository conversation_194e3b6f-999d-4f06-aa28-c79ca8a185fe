{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\index.vue?vue&type=style&index=0&id=682a92dc&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\index.vue", "mtime": 1756170476875}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDml6XmnJ/ojIPlm7TpgInmi6nlmajlrr3luqbkuI7lhbbku5bovpPlhaXmoYbkuIDoh7QgKi8NCi5lbC1kYXRlLWVkaXRvci5lbC1yYW5nZS1lZGl0b3Igew0KICB3aWR0aDogMjA1cHggIWltcG9ydGFudDsNCiAgZm9udC1zaXplOiAxMnB4ICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOiwg+aVtOaXpeacn+iMg+WbtOmAieaLqeWZqOWGhemDqOagt+W8jyAqLw0KLmVsLWRhdGUtZWRpdG9yLmVsLXJhbmdlLWVkaXRvciAuZWwtcmFuZ2UtaW5wdXQgew0KICB3aWR0aDogMzIlICFpbXBvcnRhbnQ7DQogIGZvbnQtc2l6ZTogMTJweCAhaW1wb3J0YW50Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5lbC1kYXRlLWVkaXRvci5lbC1yYW5nZS1lZGl0b3IgLmVsLXJhbmdlLXNlcGFyYXRvciB7DQogIHdpZHRoOiAyMCUgIWltcG9ydGFudDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBmb250LXNpemU6IDEycHggIWltcG9ydGFudDsNCiAgY29sb3I6ICNDMEM0Q0M7DQp9DQoNCi8qIOehruS/neaXpeacn+iMg+WbtOmAieaLqeWZqOeahOmrmOW6puS4juWFtuS7lui+k+WFpeahhuS4gOiHtCAqLw0KLmVsLWRhdGUtZWRpdG9yLmVsLXJhbmdlLWVkaXRvci5lbC1pbnB1dF9faW5uZXIgew0KICBoZWlnaHQ6IDI4cHggIWltcG9ydGFudDsNCiAgbGluZS1oZWlnaHQ6IDI4cHggIWltcG9ydGFudDsNCn0NCg0KLyog57yp55+t5Y+z5L6n55WZ55m9ICovDQouYXBwLWNvbnRhaW5lciB7DQogIHBhZGRpbmctcmlnaHQ6IDVweCAhaW1wb3J0YW50Ow0KfQ0KDQovKiDosIPmlbTooajljZXlrrnlmajlrr3luqYgKi8NCi5lbC1mb3JtLS1pbmxpbmUgew0KICBtYXgtd2lkdGg6IGNhbGMoMTAwJSAtIDEwcHgpICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOiwg+aVtOi+k+WFpeahhuS4iuS4i+mXtOi3neebuOetiSAqLw0KLmVsLWZvcm0tLWlubGluZSAuZWwtZm9ybS1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMThweCAhaW1wb3J0YW50Ow0KICBtYXJnaW4tdG9wOiAwICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOehruS/neesrOS4gOihjOayoeaciemineWklueahOS4iui+uei3nSAqLw0KLmVsLWZvcm0tLWlubGluZSAuZWwtZm9ybS1pdGVtOmZpcnN0LWNoaWxkIHsNCiAgbWFyZ2luLXRvcDogMCAhaW1wb3J0YW50Ow0KfQ0KDQovKiDnvKnlsI/ovpPlhaXmoYbmoIfpopjlrZfkvZMgKi8NCi5lbC1mb3JtLS1pbmxpbmUgLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICBmb250LXNpemU6IDEwcHggIWltcG9ydGFudDsNCn0NCg0KLyog5pu05by655qE6YCJ5oup5Zmo56Gu5L+d5a2X5L2T5qC35byP55Sf5pWIICovDQouYXBwLWNvbnRhaW5lciAuZWwtZm9ybS0taW5saW5lIC5lbC1mb3JtLWl0ZW0gLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICBmb250LXNpemU6IDEwcHggIWltcG9ydGFudDsNCiAgZm9udC13ZWlnaHQ6IG5vcm1hbCAhaW1wb3J0YW50Ow0KfQ0KDQovKiDlvLnnqpfmoIfpopjlsYXkuK0gKi8NCi5lbC1kaWFsb2dfX2hlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlciAhaW1wb3J0YW50Ow0KfQ0KDQouZWwtZGlhbG9nX19oZWFkZXIgLmVsLWRpYWxvZ19fdGl0bGUgew0KICB0ZXh0LWFsaWduOiBjZW50ZXIgIWltcG9ydGFudDsNCiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsNCiAgZGlzcGxheTogYmxvY2sgIWltcG9ydGFudDsNCn0NCg0KLyog5pu05by655qE6YCJ5oup5Zmo56Gu5L+d5by556qX5qCH6aKY5bGF5LitICovDQouZWwtZGlhbG9nIC5lbC1kaWFsb2dfX2hlYWRlciAuZWwtZGlhbG9nX190aXRsZSB7DQogIHRleHQtYWxpZ246IGNlbnRlciAhaW1wb3J0YW50Ow0KICB3aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KICBkaXNwbGF5OiBibG9jayAhaW1wb3J0YW50Ow0KICBtYXJnaW46IDAgYXV0byAhaW1wb3J0YW50Ow0KfQ0KDQovKiDkvb/nlKjmt7HluqbpgInmi6nlmajnoa7kv53moLflvI/nqb/pgI8gKi8NCjo6di1kZWVwIC5lbC1kaWFsb2dfX2hlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlciAhaW1wb3J0YW50Ow0KfQ0KDQo6OnYtZGVlcCAuZWwtZGlhbG9nX190aXRsZSB7DQogIHRleHQtYWxpZ246IGNlbnRlciAhaW1wb3J0YW50Ow0KICB3aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KICBkaXNwbGF5OiBibG9jayAhaW1wb3J0YW50Ow0KfQ0KDQovKiDmkJzntKLlm77moIfmoLflvI8gKi8NCi5zZWFyY2gtaWNvbiB7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgY29sb3I6ICM5MDkzOTk7DQogIHBhZGRpbmc6IDAgOHB4Ow0KICB0cmFuc2l0aW9uOiBjb2xvciAwLjNzOw0KfQ0KDQouc2VhcmNoLWljb246aG92ZXIgew0KICBjb2xvcjogIzQwOUVGRjsNCn0NCg0KLyog5pON5L2c5YiX5Zu65a6a5a695bqmICovDQouZWwtdGFibGUgLmZpeGVkLXdpZHRoIHsNCiAgd2lkdGg6IDIwMHB4Ow0KICBtaW4td2lkdGg6IDIwMHB4Ow0KfQ0KDQovKiDlsI/pl7Tot50gKi8NCi5lbC10YWJsZSAuc21hbGwtcGFkZGluZyAuY2VsbCB7DQogIHBhZGRpbmctbGVmdDogNXB4Ow0KICBwYWRkaW5nLXJpZ2h0OiA1cHg7DQp9DQoNCi8qIOWkhOe9muaOquaWvei+k+WFpeahhuagt+W8jyAqLw0KLm1lYXN1cmUtaW5wdXQtd3JhcHBlciB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgbWluLWhlaWdodDogNzhweDsNCiAgaGVpZ2h0OiA3OHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KfQ0KDQoubWVhc3VyZS10YWdzLWNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgZ2FwOiA4cHg7DQogIG1pbi1oZWlnaHQ6IDYycHg7DQogIG1heC1oZWlnaHQ6IDYycHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICBhbGlnbi1jb250ZW50OiBmbGV4LXN0YXJ0Ow0KfQ0KDQoubWVhc3VyZS10YWcgew0KICBtYXJnaW46IDA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgaGVpZ2h0OiAyOHB4Ow0KICBsaW5lLWhlaWdodDogMjZweDsNCiAgYm9yZGVyLXJhZGl1czogMTRweDsNCiAgcGFkZGluZzogMCAxMnB4Ow0KICBjdXJzb3I6IGRlZmF1bHQ7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5tZWFzdXJlLXRhZyAuZWwtaWNvbi1jbG9zZSB7DQogIG1hcmdpbi1sZWZ0OiA2cHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLm1lYXN1cmUtcGxhY2Vob2xkZXIgew0KICBjb2xvcjogI2MwYzRjYzsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogMS41Ow0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogOHB4Ow0KICBsZWZ0OiAxMnB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHVzZXItc2VsZWN0OiBub25lOw0KfQ0KDQoubWVhc3VyZS1wbGFjZWhvbGRlcjpob3ZlciB7DQogIGNvbG9yOiAjNDA5RUZGOw0KfQ0KDQoubWVhc3VyZS10ZXh0YXJlYSB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQoubWVhc3VyZS1idXR0b25zIHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB0b3A6IDhweDsNCiAgcmlnaHQ6IDhweDsNCiAgei1pbmRleDogMTA7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogNXB4Ow0KfQ0KDQoubWVhc3VyZS1idG4gew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZGNkZmU2Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbWluLXdpZHRoOiA1MHB4Ow0KICBoZWlnaHQ6IDI4cHg7DQogIGxpbmUtaGVpZ2h0OiAxOw0KICBib3gtc2hhZG93OiAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQoubWVhc3VyZS1idG46aG92ZXIgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7DQogIGJveC1zaGFkb3c6IDAgMnB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMTUpOw0KfQ0KDQovKiDpgInmi6nmjInpkq7moLflvI8gKi8NCi5tZWFzdXJlLWJ0bi5lbC1idXR0b24tLXByaW1hcnkgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDY0LCAxNTgsIDI1NSwgMC45KTsNCiAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOw0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5tZWFzdXJlLWJ0bi5lbC1idXR0b24tLXByaW1hcnk6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjNDA5RUZGOw0KICBib3JkZXItY29sb3I6ICM0MDlFRkY7DQp9DQoNCi8qIOa4heepuuaMiemSruagt+W8jyAqLw0KLm1lYXN1cmUtYnRuLmVsLWJ1dHRvbi0tZGFuZ2VyIHsNCiAgYmFja2dyb3VuZDogcmdiYSgyNDUsIDEwOCwgMTA4LCAwLjkpOw0KICBib3JkZXItY29sb3I6ICNGNTZDNkM7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLm1lYXN1cmUtYnRuLmVsLWJ1dHRvbi0tZGFuZ2VyOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogI0Y1NkM2QzsNCiAgYm9yZGVyLWNvbG9yOiAjRjU2QzZDOw0KfQ0KDQovKiDlj6ror7vlpITnvZrmjqrmlr3ovpPlhaXmoYbmoLflvI8gKi8NCi5tZWFzdXJlLXRleHRhcmVhLmVsLXRleHRhcmVhLmlzLWRpc2FibGVkIC5lbC10ZXh0YXJlYV9faW5uZXIsDQoubWVhc3VyZS10ZXh0YXJlYSAuZWwtdGV4dGFyZWFfX2lubmVyW3JlYWRvbmx5XSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGJvcmRlci1jb2xvcjogI2U0ZTdlZDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGN1cnNvcjogbm90LWFsbG93ZWQ7DQp9DQoNCi8qIOafpeivouihqOWNleagt+W8j+S8mOWMliAtIOavj+ihjDTkuKrovpPlhaXmoYYgKi8NCi5lbC1mb3JtLS1pbmxpbmUgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KfQ0KDQouZWwtZm9ybS0taW5saW5lIC5lbC1mb3JtLWl0ZW0gew0KICB3aWR0aDogY2FsYygyNSUgLSAxNXB4KTsNCiAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBmbGV4OiAwIDAgYXV0bzsNCn0NCg0KLyog5q+P6KGM56ysNOS4quWFg+e0oOS4jemcgOimgeWPs+i+uei3nSAqLw0KLmVsLWZvcm0tLWlubGluZSAuZWwtZm9ybS1pdGVtOm50aC1jaGlsZCg0bikgew0KICBtYXJnaW4tcmlnaHQ6IDA7DQp9DQoNCi8qIOaQnOe0ouaMiemSruWMuuWfn+WNleeLrOWkhOeQhiAqLw0KLmVsLWZvcm0tLWlubGluZSAuZWwtZm9ybS1pdGVtOmxhc3QtY2hpbGQgew0KICB3aWR0aDogYXV0bzsNCiAgbWFyZ2luLWxlZnQ6IGF1dG87DQogIG1hcmdpbi1yaWdodDogMDsNCn0NCg0KLyog57uf5LiA6L6T5YWl5qGG5a695bqmICovDQouZWwtZm9ybS0taW5saW5lIC5lbC1mb3JtLWl0ZW0gLmVsLWlucHV0IHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi8qIOe7n+S4gOmAieaLqeahhuWuveW6piAqLw0KLmVsLWZvcm0tLWlubGluZSAuZWwtZm9ybS1pdGVtIC5lbC1zZWxlY3Qgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLyog57uf5LiA5pel5pyf6YCJ5oup5Zmo5a695bqmICovDQouZWwtZm9ybS0taW5saW5lIC5lbC1mb3JtLWl0ZW0gLmVsLWRhdGUtZWRpdG9yIHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi8qIOWTjeW6lOW8j+iwg+aVtCAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDE0MDBweCkgew0KICAuZWwtZm9ybS0taW5saW5lIC5lbC1mb3JtLWl0ZW0gew0KICAgIHdpZHRoOiBjYWxjKDMzLjMzJSAtIDEzcHgpOw0KICB9DQogIC5lbC1mb3JtLS1pbmxpbmUgLmVsLWZvcm0taXRlbTpudGgtY2hpbGQoNG4pIHsNCiAgICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogIH0NCiAgLmVsLWZvcm0tLWlubGluZSAuZWwtZm9ybS1pdGVtOm50aC1jaGlsZCgzbikgew0KICAgIG1hcmdpbi1yaWdodDogMDsNCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogMTAwMHB4KSB7DQogIC5lbC1mb3JtLS1pbmxpbmUgLmVsLWZvcm0taXRlbSB7DQogICAgd2lkdGg6IGNhbGMoNTAlIC0gMTBweCk7DQogIH0NCiAgLmVsLWZvcm0tLWlubGluZSAuZWwtZm9ybS1pdGVtOm50aC1jaGlsZCgzbikgew0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgfQ0KICAuZWwtZm9ybS0taW5saW5lIC5lbC1mb3JtLWl0ZW06bnRoLWNoaWxkKDJuKSB7DQogICAgbWFyZ2luLXJpZ2h0OiAwOw0KICB9DQp9DQoNCi8qIOihqOagvOWuueWZqOagt+W8jyAqLw0KLnRhYmxlLWNvbnRhaW5lciB7DQogIHdpZHRoOiAxMDAlOw0KICBvdmVyZmxvdy14OiBhdXRvOw0KICBvdmVyZmxvdy15OiBoaWRkZW47DQp9DQoNCi8qIOWkhOe9muS+neaNrui+k+WFpeahhuagt+W8jyAqLw0KLmJhc2lzLWlucHV0LXdyYXBwZXIgew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi5iYXNpcy10ZXh0YXJlYSB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouYmFzaXMtYnV0dG9ucyB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiA1cHg7DQogIHJpZ2h0OiA1cHg7DQogIHotaW5kZXg6IDEwOw0KfQ0KDQouYmFzaXMtYnRuIHsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTsNCiAgYm9yZGVyOiAxcHggc29saWQgI2RjZGZlNjsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIG1pbi13aWR0aDogNTBweDsNCiAgaGVpZ2h0OiAyOHB4Ow0KICBsaW5lLWhlaWdodDogMTsNCiAgYm94LXNoYWRvdzogMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCn0NCg0KLmJhc2lzLWJ0bjpob3ZlciB7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsNCiAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7DQp9DQoNCi8qIOmAieaLqeaMiemSruagt+W8jyAqLw0KLmJhc2lzLWJ0bi5lbC1idXR0b24tLXByaW1hcnkgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDY0LCAxNTgsIDI1NSwgMC45KTsNCiAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOw0KICBjb2xvcjogd2hpdGU7DQp9DQoNCi5iYXNpcy1idG4uZWwtYnV0dG9uLS1wcmltYXJ5OmhvdmVyIHsNCiAgYmFja2dyb3VuZDogIzQwOUVGRjsNCiAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOw0KfQ0KDQovKiDmu5rliqjmnaHmoLflvI/kvJjljJYgKi8NCi50YWJsZS1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgaGVpZ2h0OiA4cHg7DQp9DQoNCi50YWJsZS1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgYmFja2dyb3VuZDogI2YxZjFmMTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQoudGFibGUtY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQ6ICNjMWMxYzE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLnRhYmxlLWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjYThhOGE4Ow0KfQ0KDQoNCg0KLyogRWxlbWVudCBVSSDlm7rlrprliJfmoLflvI/kvJjljJYgKi8NCjo6di1kZWVwIC5lbC10YWJsZV9fZml4ZWQtcmlnaHQgew0KICBib3gtc2hhZG93OiAtMnB4IDAgOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCn0NCg0KLyog5Y+q6K+76L6T5YWl5qGG5qC35byPICovDQoucmVhZG9ubHktaW5wdXQgOjp2LWRlZXAgLmVsLWlucHV0X19pbm5lciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmEgIWltcG9ydGFudDsNCiAgYm9yZGVyLWNvbG9yOiAjZTRlN2VkICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjOTA5Mzk5ICFpbXBvcnRhbnQ7DQogIGN1cnNvcjogbm90LWFsbG93ZWQgIWltcG9ydGFudDsNCn0NCg0KLnJlYWRvbmx5LWlucHV0IDo6di1kZWVwIC5lbC1pbnB1dF9faW5uZXI6aG92ZXIgew0KICBib3JkZXItY29sb3I6ICNlNGU3ZWQgIWltcG9ydGFudDsNCn0NCg0KLnJlYWRvbmx5LWlucHV0IDo6di1kZWVwIC5lbC1pbnB1dF9faW5uZXI6Zm9jdXMgew0KICBib3JkZXItY29sb3I6ICNlNGU3ZWQgIWltcG9ydGFudDsNCiAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy+CA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"100px\"\r\n    >\r\n    <el-form-item label=\"编号\" prop=\"serialNo\">\r\n        <el-input\r\n          v-model=\"queryParams.serialNo\"\r\n          placeholder=\"请输入编号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"申请部门\" prop=\"deptNo\">\r\n        <el-select\r\n          v-model=\"queryParams.deptNo\"\r\n          placeholder=\"请选择申请部门\"\r\n          clearable\r\n          size=\"small\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in getDepNameList\"\r\n            :key=\"item\"\r\n            :label=\"item\"\r\n            :value=\"item\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"填报人\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入填报人\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"确认人\" prop=\"confirmName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入确认人\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"确认部门\" prop=\"companyCode\">\r\n        <el-input\r\n          v-model=\"queryParams.companyCode\"\r\n          placeholder=\"请输入确认部门\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>     \r\n      <el-form-item label=\"供应商代码\" prop=\"suppId\">\r\n        <el-input\r\n          v-model=\"queryParams.suppId\"\r\n          placeholder=\"请输入供应商代码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        >\r\n          <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showSuppInfoDialogForQuery\"></i>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"供应商名称\" prop=\"suppName\">\r\n        <el-input\r\n          v-model=\"queryParams.suppName\"\r\n          placeholder=\"请输入供应商名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"处罚类型\" prop=\"punishmentType\">\r\n        <el-select v-model=\"queryParams.punishmentType\" placeholder=\"请选择处罚类型\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in punishmentTypeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"供应类型\" prop=\"suppType\">\r\n        <el-select v-model=\"queryParams.suppType\" placeholder=\"请选择物资、服务或工程\" clearable size=\"small\" @change=\"handleQueryMaterialOrServiceChange\">\r\n          <el-option\r\n            v-for=\"option in suppTypeOptions\"\r\n            :key=\"option.value\"\r\n            :label=\"option.label\"\r\n            :value=\"option.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"物料小类编码\" prop=\"itemNo\" v-if=\"queryParams.suppType === 'M'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemNo\"\r\n          placeholder=\"请输入物料小类编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        >\r\n          <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showMaterialInfoDialogForQuery\"></i>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"物料小类名称\" prop=\"itemName\" v-if=\"queryParams.suppType === 'M'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemName\"\r\n          placeholder=\"请输入物料小类名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"服务小类编码\" prop=\"itemNo\" v-if=\"queryParams.suppType === 'S'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemNo\"\r\n          placeholder=\"请输入服务小类编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        >\r\n          <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showServiceDialogForQuery\"></i>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"服务小类名称\" prop=\"itemName\" v-if=\"queryParams.suppType === 'S'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemName\"\r\n          placeholder=\"请输入服务小类名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"工程小类编码\" prop=\"itemNo\" v-if=\"queryParams.suppType === 'P'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemNo\"\r\n          placeholder=\"请输入工程小类编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        >\r\n          <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showProjectDialogForQuery\"></i>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"工程小类名称\" prop=\"itemName\" v-if=\"queryParams.suppType === 'P'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemName\"\r\n          placeholder=\"请输入工程小类名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      \r\n      <el-form-item label=\"状态\" prop=\"stateId\">\r\n        <div style=\"display: flex; align-items: center;\">\r\n          <el-select\r\n            v-model=\"queryParams.stateId\"\r\n            placeholder=\"请选择状态\"\r\n            :clearable=\"userGroup !== 'query'\"\r\n            :disabled=\"userGroup === 'query'\"\r\n            size=\"small\"\r\n            style=\"width: 180px;\"\r\n          >\r\n            <el-option\r\n              v-for=\"dict in statusOptions\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item label=\"事件发生时间\">\r\n        <el-date-picker\r\n          v-model=\"happenedTimeRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          format=\"yyyy/MM/dd\"\r\n          value-format=\"yyyy/MM/dd\"\r\n          size=\"small\"\r\n          clearable>\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"处罚执行时间\">\r\n        <el-date-picker\r\n          v-model=\"punishmentTimeRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          format=\"yyyy/MM/dd\"\r\n          value-format=\"yyyy/MM/dd\"\r\n          size=\"small\"\r\n          clearable>\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      \r\n      <el-form-item>\r\n        <el-button\r\n          type=\"cyan\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n        >搜索</el-button>\r\n        <el-button\r\n          icon=\"el-icon-refresh\"\r\n          size=\"mini\"\r\n          @click=\"resetQuery\"\r\n        >重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canAdd\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canEdit\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canConfirm\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleConfirm\"\r\n        >确认</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canDelete\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canExport\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col>\r\n\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <div class=\"table-container\">\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"punishmentList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"状态\" width=\"70\" align=\"center\" prop=\"stateId\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.stateId == 1\" type=\"info\">草稿</el-tag>\r\n            <el-tag v-if=\"scope.row.stateId == 2\" type=\"success\">确认</el-tag>         \r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"编号\" width=\"140\" align=\"center\" prop=\"serialNo\" />\r\n        <el-table-column label=\"申请部门\" width=\"150\" align=\"center\" prop=\"deptNo\" />\r\n        <el-table-column label=\"填报人\" width=\"120\" align=\"center\" prop=\"userName\" />\r\n        <el-table-column label=\"确认人\" width=\"120\" align=\"center\" prop=\"confirmName\" />\r\n        <el-table-column label=\"确认部门\" width=\"150\" align=\"center\" prop=\"companyCode\" />\r\n        <el-table-column label=\"供应商代码\" width=\"120\" align=\"center\" prop=\"suppId\" />\r\n        <el-table-column label=\"供应商名称\" min-width=\"230\" align=\"center\" prop=\"suppName\" />\r\n        <el-table-column label=\"供应类型\" width=\"80\" align=\"center\" prop=\"suppType\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.suppType == 'M'\">物资</span>\r\n            <span v-if=\"scope.row.suppType == 'S'\">服务</span>\r\n            <span v-if=\"scope.row.suppType == 'P'\">工程</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"物资/服务/工程代码\" width=\"150\" align=\"center\" prop=\"itemNo\" />\r\n        <el-table-column label=\"物资/服务/工程小类名称\" width=\"200\" align=\"center\" prop=\"itemName\" />\r\n        <el-table-column label=\"处罚类型\" width=\"80\" align=\"center\" prop=\"punishmentType\" :formatter=\"punishmentTypeFormat\" />\r\n        <el-table-column label=\"处罚事由\" min-width=\"200\" align=\"center\" prop=\"punishmentReason\" />\r\n        <el-table-column label=\"处罚依据\" min-width=\"300\" align=\"center\" prop=\"punishmentBasis\" />\r\n        <el-table-column label=\"处罚措施\" min-width=\"150\" align=\"center\" prop=\"punishmentMeasure\" />\r\n        <el-table-column label=\"事件发生时间\" width=\"100\" align=\"center\" prop=\"happenedTime\" :formatter=\"dateFormat\" />\r\n        <el-table-column label=\"处罚执行时间\" width=\"100\" align=\"center\" prop=\"punishmentTime\" :formatter=\"dateFormat\" />\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n          class-name=\"small-padding fixed-width\"\r\n          fixed=\"right\"\r\n          width=\"180\"\r\n        >\r\n        <template slot-scope=\"scope\">\r\n          <!-- 修改按钮：只有草稿状态(1)可以修改，且有修改权限 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 1 && permissions.canEdit\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            >修改</el-button\r\n          >\r\n          <!-- 确认按钮：只有草稿状态(1)可以确认，且有确认权限 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 1 && permissions.canConfirm\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleConfirm(scope.row)\"\r\n            >确认</el-button\r\n          >\r\n          <!-- 删除按钮：只有草稿状态(1)可以删除，且有删除权限 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 1 && permissions.canDelete\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n          <!-- 查看按钮：确认状态(2)只能查看，或者没有修改权限时显示查看 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 2 || (scope.row.stateId == 1 && !permissions.canEdit)\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n          >查看</el-button>\r\n        </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改供应商处罚记录对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"填报人\" prop=\"userName\">\r\n              <el-input\r\n                v-model=\"form.userName\"\r\n                placeholder=\"请输入填报人\"\r\n                readonly\r\n                class=\"readonly-input\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"确认部门\" prop=\"companyCode\">\r\n              <el-input\r\n                v-model=\"form.companyCode\"\r\n                placeholder=\"请输入确认部门\"\r\n                readonly\r\n                class=\"readonly-input\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"申请部门\" prop=\"deptNo\">\r\n              <el-select\r\n                v-model=\"form.deptNo\"\r\n                clearable\r\n                placeholder=\"请选择申请部门\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in getDepNameList\"\r\n                  :key=\"item\"\r\n                  :label=\"item\"\r\n                  :value=\"item\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应商代码\" prop=\"suppId\">\r\n              <el-input\r\n                v-model=\"form.suppId\"\r\n                placeholder=\"请输入供应商代码\"\r\n              >\r\n                <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showSuppInfoDialog\"></i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应商名称\" prop=\"suppName\">\r\n              <el-input v-model=\"form.suppName\" placeholder=\"请输入供应商名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"处罚类型\" prop=\"punishmentType\">\r\n              <el-select v-model=\"form.punishmentType\" placeholder=\"请选择处罚类型\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in punishmentTypeOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应类型\" prop=\"suppType\">\r\n              <el-select v-model=\"form.suppType\" placeholder=\"请选择涉及物资、服务或工程\" style=\"width: 100%\" @change=\"handleMaterialOrServiceChange\">\r\n                <el-option\r\n                  v-for=\"option in suppTypeOptions\"\r\n                  :key=\"option.value\"\r\n                  :label=\"option.label\"\r\n                  :value=\"option.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          \r\n          \r\n        </el-row>\r\n        <el-row :gutter=\"20\" v-if=\"form.suppType === 'M'\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物料小类编码\" prop=\"itemNo\" :required=\"true\">\r\n              <el-input v-model=\"form.itemNo\" placeholder=\"请输入物料小类编码\">\r\n                <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showMaterialInfoDialogForForm\"></i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物料小类名称\" prop=\"itemName\" :required=\"true\">\r\n              <el-input v-model=\"form.itemName\" placeholder=\"请输入物料小类名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\" v-if=\"form.suppType === 'S'\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务小类编码\" prop=\"itemNo\">\r\n              <el-input v-model=\"form.itemNo\" placeholder=\"请输入服务小类编码\">\r\n                <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showServiceDialogForForm\"></i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务小类名称\" prop=\"itemName\">\r\n              <el-input v-model=\"form.itemName\" placeholder=\"请输入服务小类名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" v-if=\"form.suppType === 'P'\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程小类编码\" prop=\"itemNo\">\r\n              <el-input v-model=\"form.itemNo\" placeholder=\"请输入工程小类编码\">\r\n                <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showProjectDialogForForm\"></i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程小类名称\" prop=\"itemName\">\r\n              <el-input v-model=\"form.itemName\" placeholder=\"请输入工程小类名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        \r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"事件发生时间\" prop=\"happenedTime\">\r\n              <el-date-picker\r\n                v-model=\"form.happenedTime\"\r\n                type=\"date\"\r\n                placeholder=\"请选择事件发生时间\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"处罚执行时间\" prop=\"punishmentTime\">\r\n              <el-date-picker\r\n                v-model=\"form.punishmentTime\"\r\n                type=\"date\"\r\n                placeholder=\"请选择处罚执行时间\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"处罚事由\" prop=\"punishmentReason\">\r\n              <el-input v-model=\"form.punishmentReason\" type=\"textarea\" placeholder=\"请输入处罚事由\" :rows=\"3\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"处罚依据\" prop=\"punishmentBasis\">\r\n              <div class=\"basis-input-wrapper\">\r\n                <el-input\r\n                  v-model=\"form.punishmentBasis\"\r\n                  type=\"textarea\"\r\n                  placeholder=\"请选择处罚依据\"\r\n                  :rows=\"3\"\r\n                  class=\"basis-textarea\"\r\n                  readonly\r\n                />\r\n                <div class=\"basis-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    @click=\"showPunishmentBasisDialog\"\r\n                    class=\"basis-btn\"\r\n                    size=\"small\"\r\n                    title=\"选择处罚依据\"\r\n                  >\r\n                    选择\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"处罚措施\" prop=\"punishmentMeasure\">\r\n              <div class=\"measure-input-wrapper\">\r\n                <!-- 标签显示区域 -->\r\n                <div class=\"measure-tags-container\" v-if=\"punishmentMeasureTags.length > 0\">\r\n                  <el-tag\r\n                    v-for=\"(tag, index) in punishmentMeasureTags\"\r\n                    :key=\"index\"\r\n                    :type=\"getTagType(tag.type)\"\r\n                    closable\r\n                    @close=\"removeMeasureTag(index)\"\r\n                    class=\"measure-tag\"\r\n                  >\r\n                    {{ tag.text }}\r\n                  </el-tag>\r\n                </div>\r\n\r\n                <!-- 隐藏的输入框用于存储完整文本 -->\r\n                <el-input\r\n                  v-model=\"form.punishmentMeasure\"\r\n                  type=\"hidden\"\r\n                />\r\n\r\n                <!-- 占位符显示区域 -->\r\n                <div\r\n                  v-if=\"punishmentMeasureTags.length === 0\"\r\n                  class=\"measure-placeholder\"\r\n                  @click=\"showPunishmentMeasureDialog\"\r\n                >\r\n                  请选择处罚措施（至少选一个）\r\n                </div>\r\n\r\n                <div class=\"measure-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    @click=\"showPunishmentMeasureDialog\"\r\n                    class=\"measure-btn\"\r\n                    size=\"small\"\r\n                    title=\"选择处罚措施\"\r\n                  >\r\n                    选择\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"!isViewMode\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 供应商信息查询弹窗 -->\r\n    <supp-info-dialog ref=\"suppInfoDialog\" @select=\"handleSuppSelect\" />\r\n\r\n    <!-- 物料信息查询弹窗 -->\r\n    <material-info-dialog ref=\"materialInfoDialog\" @select=\"handleMaterialSelect\" />\r\n\r\n    <!-- 服务查询弹窗 -->\r\n    <service-project-dialog ref=\"serviceDialog\" @select=\"handleServiceSelect\" />\r\n    <!-- 项目查询弹窗 -->\r\n    <project-dialog ref=\"projectDialog\" @select=\"handleProjectSelect\" />\r\n\r\n    <!-- 处罚措施选择弹窗 -->\r\n    <punishment-measure-dialog ref=\"punishmentMeasureDialog\" @select=\"handlePunishmentMeasureSelect\" />\r\n\r\n    <!-- 处罚依据选择弹窗 -->\r\n    <punishment-basis-dialog ref=\"punishmentBasisDialog\" @select=\"handlePunishmentBasisSelect\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPunishment, getPunishment, delPunishment, addPunishment, updatePunishment, exportPunishment, confirmPunishment, getUserCompany, getUserGroup } from \"@/api/suppPunishment/punishment\";\r\nimport { getDepNameList } from \"@/api/purchase/purdchaseFactoryStock\";\r\nimport { addDateRange } from \"@/utils/ruoyi\";\r\nimport SuppInfoDialog from \"./suppInfo-module.vue\";\r\nimport MaterialInfoDialog from \"./materialInfo-module.vue\";\r\nimport ServiceProjectDialog from \"./service-module.vue\";\r\nimport ProjectDialog from \"./project-module.vue\";\r\nimport PunishmentMeasureDialog from \"./punishmentMeasure-module.vue\";\r\nimport PunishmentBasisDialog from \"./punishmentBasis-module.vue\";\r\n\r\n\r\nexport default {\r\n  name: \"Punishment\",\r\n  components: {\r\n    SuppInfoDialog,\r\n    MaterialInfoDialog,\r\n    ServiceProjectDialog,\r\n    ProjectDialog,\r\n    PunishmentMeasureDialog,\r\n    PunishmentBasisDialog\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 供应商处罚记录表格数据\r\n      punishmentList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否为查看模式\r\n      isViewMode: false,\r\n      // 当前选择类型：form-表单选择，query-查询条件选择\r\n      currentSelectType: 'form',\r\n      // 用户分组权限\r\n      userGroup: '',\r\n      // 权限控制\r\n      permissions: {\r\n        canAdd: true,      // 新增权限\r\n        canEdit: true,     // 修改权限\r\n        canDelete: true,   // 删除权限\r\n        canConfirm: true,  // 确认权限\r\n        canExport: true    // 导出权限\r\n      },\r\n      // 处罚类型数据字典\r\n      punishmentTypeOptions: [],\r\n      // 状态数据字典\r\n      statusOptions: [],\r\n      // 物资或服务选项\r\n      suppTypeOptions: [\r\n        { value: 'M', label: '物资' },\r\n        { value: 'S', label: '服务' },\r\n        { value: 'P', label: '工程' }\r\n      ],\r\n      // 服务部门列表\r\n      getDepNameList: [],\r\n      // 事件发生时间范围\r\n      happenedTimeRange: [],\r\n      // 处罚执行时间范围\r\n      punishmentTimeRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        recCreator: null,\r\n        recCreateTime: null,\r\n        recRevisor: null,\r\n        recReviseTime: null,\r\n        userName: null,\r\n        serialNo: null,\r\n        companyCode: null,\r\n        deptNo: null,\r\n        suppId: null,\r\n        suppName: null,\r\n        itemNo: null,\r\n        itemName: null,\r\n        suppType: null,\r\n        punishmentType: null,\r\n        stateId: null,\r\n        punishmentReason: null,\r\n        punishmentBasis: null,\r\n        punishmentMeasure: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 处罚措施标签数组\r\n      punishmentMeasureTags: [],\r\n      // 表单校验\r\n      rules: {\r\n        suppId: [\r\n          { required: true, trigger: \"blur\", message: \"请输入供应商代码\" }\r\n        ],\r\n        suppName: [\r\n          { required: true, trigger: \"blur\", message: \"请输入供应商名称\" }\r\n        ],\r\n        suppType: [\r\n          { required: true, trigger: \"change\", message: \"请选择物资或服务\" }\r\n        ],\r\n        punishmentType: [\r\n          { required: true, trigger: \"change\", message: \"请选择处罚类型\" }\r\n        ],\r\n        deptNo: [\r\n          { required: true, trigger: \"change\", message: \"请选择申请部门\" }\r\n        ],\r\n        itemNo: [\r\n          {\r\n            required: false,\r\n            trigger: \"blur\",\r\n            validator: (rule, value, callback) => {\r\n              // 只有物资类型才必填\r\n              if (this.form.suppType === 'M') {\r\n                if (!value || value.trim() === '') {\r\n                  callback(new Error('请输入物料小类编码'));\r\n                } else {\r\n                  callback();\r\n                }\r\n              } else {\r\n                // 服务和工程类型不校验必填\r\n                callback();\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        itemName: [\r\n          {\r\n            required: false,\r\n            trigger: \"blur\",\r\n            validator: (rule, value, callback) => {\r\n              // 只有物资类型才必填\r\n              if (this.form.suppType === 'M') {\r\n                if (!value || value.trim() === '') {\r\n                  callback(new Error('请输入物料小类名称'));\r\n                } else {\r\n                  callback();\r\n                }\r\n              } else {\r\n                // 服务和工程类型不校验必填\r\n                callback();\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        happenedTime: [\r\n          { required: true, trigger: \"change\", message: \"请选择事件发生时间\" }\r\n        ],\r\n        punishmentTime: [\r\n          { required: true, trigger: \"change\", message: \"请选择处罚执行时间\" }\r\n        ],\r\n        punishmentReason: [\r\n          { required: true, trigger: \"blur\", message: \"请输入处罚事由\" }\r\n        ],\r\n        punishmentBasis: [\r\n          { required: true, trigger: \"blur\", message: \"请选择处罚依据\" }\r\n        ],\r\n        punishmentMeasure: [\r\n          { required: true, trigger: \"blur\", message: \"请选择处罚措施\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictData();\r\n    this.getDeptList();\r\n    this.getUserGroupPermissions();\r\n    // 不在这里调用getList()，而是在设置默认值后调用\r\n  },\r\n  methods: {\r\n    // 添加日期范围\r\n    addDateRange,\r\n    // 获取字典数据\r\n    getDictData() {\r\n      // 获取处罚类型\r\n      this.getDicts(\"supp_punishment_type\").then((response) => {\r\n        this.punishmentTypeOptions = response.data;\r\n      });\r\n      // 获取状态 - 如果字典不存在，使用手动定义的状态\r\n      this.getDicts(\"supp_punishment_status\").then((response) => {\r\n        this.statusOptions = response.data;\r\n      });\r\n    },\r\n    /** 查询供应商处罚记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n\r\n      console.log('事件发生时间范围:', this.happenedTimeRange);\r\n      console.log('处罚执行时间范围:', this.punishmentTimeRange);\r\n\r\n      // 手动构建时间范围参数\r\n      let params = { ...this.queryParams };\r\n      if (!params.params) {\r\n        params.params = {};\r\n      }\r\n\r\n      // 处理事件发生时间范围\r\n      if (this.happenedTimeRange && this.happenedTimeRange.length === 2) {\r\n        params.params[\"happenedTimeBeginTime\"] = this.happenedTimeRange[0];\r\n        params.params[\"happenedTimeEndTime\"] = this.happenedTimeRange[1];\r\n      }\r\n\r\n      // 处理处罚执行时间范围\r\n      if (this.punishmentTimeRange && this.punishmentTimeRange.length === 2) {\r\n        params.params[\"punishmentTimeBeginTime\"] = this.punishmentTimeRange[0];\r\n        params.params[\"punishmentTimeEndTime\"] = this.punishmentTimeRange[1];\r\n      }\r\n\r\n      console.log('最终参数:', params);\r\n\r\n      listPunishment(params).then(response => {\r\n        this.punishmentList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 查询服务部门列表 */\r\n    getDeptList() {\r\n      getDepNameList().then(response => {\r\n        this.getDepNameList = response.data;\r\n        // 直接执行查询，不设置默认申请部门\r\n        this.getList();\r\n      });\r\n    },\r\n\r\n    /** 获取用户分组权限 */\r\n    getUserGroupPermissions() {\r\n      getUserGroup().then(response => {\r\n        if (response.code === 200 && response.data) {\r\n          this.userGroup = response.data.userGroup;\r\n          this.setPermissions();\r\n        } else {\r\n          // 默认设置为查阅组权限（最严格）\r\n          this.userGroup = 'query';\r\n          this.setPermissions();\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取用户分组失败:', error);\r\n        // 默认设置为查阅组权限（最严格）\r\n        this.userGroup = 'query';\r\n        this.setPermissions();\r\n      });\r\n    },\r\n\r\n    /** 根据用户分组设置权限 */\r\n    setPermissions() {\r\n      switch (this.userGroup) {\r\n        case 'input':\r\n          // 填报组：隐藏确认、导出按钮\r\n          this.permissions = {\r\n            canAdd: true,\r\n            canEdit: true,\r\n            canDelete: true,\r\n            canConfirm: false,\r\n            canExport: false\r\n          };\r\n          break;\r\n        case 'confirm':\r\n          // 确认组：隐藏导出按钮\r\n          this.permissions = {\r\n            canAdd: true,\r\n            canEdit: true,\r\n            canDelete: true,\r\n            canConfirm: true,\r\n            canExport: false\r\n          };\r\n          break;\r\n        case 'query':\r\n          // 查阅组：隐藏新增、修改、确认、删除按钮\r\n          this.permissions = {\r\n            canAdd: false,\r\n            canEdit: false,\r\n            canDelete: false,\r\n            canConfirm: false,\r\n            canExport: true\r\n          };\r\n          // 设置查阅组默认状态为\"确认\"\r\n          this.setQueryGroupDefaults();\r\n          break;\r\n        case 'manage':\r\n          // 管理组：具备所有功能\r\n          this.permissions = {\r\n            canAdd: true,\r\n            canEdit: true,\r\n            canDelete: true,\r\n            canConfirm: true,\r\n            canExport: true\r\n          };\r\n          break;\r\n        default:\r\n          // 默认为查阅组权限\r\n          this.permissions = {\r\n            canAdd: false,\r\n            canEdit: false,\r\n            canDelete: false,\r\n            canConfirm: false,\r\n            canExport: true\r\n          };\r\n          // 设置查阅组默认状态为\"确认\"\r\n          this.setQueryGroupDefaults();\r\n      }\r\n    },\r\n\r\n    /** 设置查阅组默认值 */\r\n    setQueryGroupDefaults() {\r\n      // 设置状态默认为\"确认\"（值为2）\r\n      this.queryParams.stateId = '2';\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        recCreator: null,\r\n        recCreateTime: null,\r\n        recRevisor: null,\r\n        recReviseTime: null,\r\n        userName: null,\r\n        serialNo: null,\r\n        companyCode: null,\r\n        deptNo: null,\r\n        suppId: null,\r\n        suppName: null,\r\n        itemNo: null,\r\n        itemName: null,\r\n        punishmentType: null,\r\n        suppType: null,\r\n        happenedTime: null,\r\n        punishmentTime: null,\r\n        punishmentReason: null,\r\n        punishmentBasis: null,\r\n        punishmentMeasure: null\r\n      };\r\n      // 重置处罚措施标签\r\n      this.punishmentMeasureTags = [];\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 处罚类型字典翻译\r\n    punishmentTypeFormat(row, column) {\r\n      return this.selectDictLabel(this.punishmentTypeOptions, row.punishmentType);\r\n    },\r\n    // 状态字典翻译\r\n    stateFormat(row, column) {\r\n      return this.selectDictLabel(this.statusOptions, row.stateId);\r\n    },\r\n    // 日期格式化：将20250803转换为2025-08-03\r\n    dateFormat(row, column, cellValue) {\r\n      if (!cellValue) return '';\r\n      // 如果已经是正确格式，直接返回\r\n      if (cellValue.includes('-')) return cellValue;\r\n      // 将20250803格式转换为2025-08-03\r\n      if (cellValue.length === 8) {\r\n        const year = cellValue.substring(0, 4);\r\n        const month = cellValue.substring(4, 6);\r\n        const day = cellValue.substring(6, 8);\r\n        return `${year}-${month}-${day}`;\r\n      }\r\n      return cellValue;\r\n    },\r\n    // 日期格式转换：将20250803转换为2025/08/03（用于表单回显）\r\n    convertDateFormat(dateValue) {\r\n      if (!dateValue) return '';\r\n      // 如果已经是正确格式，直接返回\r\n      if (dateValue.includes('/') || dateValue.includes('-')) return dateValue;\r\n      // 将20250803格式转换为2025/08/03\r\n      if (dateValue.length === 8) {\r\n        const year = dateValue.substring(0, 4);\r\n        const month = dateValue.substring(4, 6);\r\n        const day = dateValue.substring(6, 8);\r\n        return `${year}/${month}/${day}`;\r\n      }\r\n      return dateValue;\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.happenedTimeRange = [];\r\n      this.punishmentTimeRange = [];\r\n      this.resetForm(\"queryForm\");\r\n\r\n      // 如果是查阅组，重置后需要重新设置默认状态\r\n      if (this.userGroup === 'query') {\r\n        this.setQueryGroupDefaults();\r\n      }\r\n\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.isViewMode = false;\r\n      // 自动填入当前登录用户\r\n      if (this.$store.getters.name) {\r\n        // 立即获取登录人信息\r\n        this.getUserCompanyInfo(this.$store.getters.name);\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加供应商处罚记录\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      let id;\r\n\r\n      if (row && row.id) {\r\n        // 单行修改：从表格行操作按钮调用\r\n        if (row.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能修改\");\r\n          return;\r\n        }\r\n        id = row.id;\r\n      } else {\r\n        // 批量修改：从顶部按钮调用\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.msgError(\"请选择要修改的记录\");\r\n          return;\r\n        }\r\n        if (this.ids.length > 1) {\r\n          this.msgError(\"修改操作只能选择一条记录\");\r\n          return;\r\n        }\r\n\r\n        // 根据选中的ID查找对应的记录\r\n        const selectedRow = this.punishmentList.find(item => item.id === this.ids[0]);\r\n        if (!selectedRow) {\r\n          this.msgError(\"无法找到选中的记录\");\r\n          return;\r\n        }\r\n        if (selectedRow.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能修改\");\r\n          return;\r\n        }\r\n        id = selectedRow.id;\r\n      }\r\n      console.log('修改操作 - id:', id);\r\n      console.log('修改操作 - row:', row);\r\n\r\n      if (!id) {\r\n        this.msgError(\"无法获取记录标识，请重新选择\");\r\n        return;\r\n      }\r\n\r\n      getPunishment(id).then(response => {\r\n        this.form = response.data;\r\n        // 转换日期格式：将20250803转换为2025/08/03\r\n        this.form.happenedTime = this.convertDateFormat(this.form.happenedTime);\r\n        this.form.punishmentTime = this.convertDateFormat(this.form.punishmentTime);\r\n        // 解析处罚措施为标签\r\n        this.parseMeasureTextToTags(this.form.punishmentMeasure);\r\n        this.isViewMode = false;\r\n        this.open = true;\r\n        this.title = \"修改供应商处罚记录\";\r\n      }).catch(error => {\r\n        console.error('获取详情失败:', error);\r\n        this.msgError(\"获取记录详情失败\");\r\n      });\r\n    },\r\n\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      console.log('查看操作 - id:', id);\r\n\r\n      if (!id) {\r\n        this.msgError(\"无法获取记录标识\");\r\n        return;\r\n      }\r\n\r\n      getPunishment(id).then(response => {\r\n        this.form = response.data;\r\n        // 转换日期格式：将20250803转换为2025/08/03\r\n        this.form.happenedTime = this.convertDateFormat(this.form.happenedTime);\r\n        this.form.punishmentTime = this.convertDateFormat(this.form.punishmentTime);\r\n        // 解析处罚措施为标签\r\n        this.parseMeasureTextToTags(this.form.punishmentMeasure);\r\n        this.isViewMode = true;\r\n        this.open = true;\r\n        this.title = \"查看供应商处罚记录\";\r\n      }).catch(error => {\r\n        console.error('获取详情失败:', error);\r\n        this.msgError(\"获取记录详情失败\");\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updatePunishment(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addPunishment(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 确认按钮操作 */\r\n    handleConfirm(row) {\r\n      let ids;\r\n\r\n      if (row && row.id) {\r\n        // 单行确认：从表格行操作按钮调用\r\n        if (row.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能确认\");\r\n          return;\r\n        }\r\n        ids = row.id;\r\n      } else {\r\n        // 批量确认：从顶部按钮调用\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.msgError(\"请选择要确认的记录\");\r\n          return;\r\n        }\r\n\r\n        // 检查所有选中记录的状态，只有草稿状态才能确认\r\n        const selectedRows = this.punishmentList.filter(item => this.ids.includes(item.id));\r\n        const hasNonDraftRecord = selectedRows.some(item => item.stateId != 1);\r\n        if (hasNonDraftRecord) {\r\n          this.msgError(\"只有草稿状态的记录才能确认\");\r\n          return;\r\n        }\r\n\r\n        ids = this.ids;\r\n      }\r\n      if (!ids || (Array.isArray(ids) && ids.length === 0)) {\r\n        this.msgError(\"请选择要确认的记录\");\r\n        return;\r\n      }\r\n\r\n      const confirmIds = Array.isArray(ids) ? ids.join(',') : ids;\r\n      this.$confirm('是否确认选中的处罚记录?', \"提示\", {\r\n          cancelButtonText: \"取消\",\r\n          confirmButtonText: \"确定\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          return confirmPunishment(confirmIds);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"确认成功\");\r\n        })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let ids;\r\n\r\n      if (row && row.id) {\r\n        // 单行删除：从表格行操作按钮调用\r\n        if (row.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能删除\");\r\n          return;\r\n        }\r\n        ids = row.id;\r\n      } else {\r\n        // 批量删除：从顶部按钮调用\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.msgError(\"请选择要删除的记录\");\r\n          return;\r\n        }\r\n\r\n        // 检查所有选中记录的状态，只有草稿状态才能删除\r\n        const selectedRows = this.punishmentList.filter(item => this.ids.includes(item.id));\r\n        const hasNonDraftRecord = selectedRows.some(item => item.stateId != 1);\r\n        if (hasNonDraftRecord) {\r\n          this.msgError(\"只有草稿状态的记录才能删除\");\r\n          return;\r\n        }\r\n\r\n        ids = this.ids;\r\n      }\r\n      if (!ids) {\r\n        this.msgError(\"无法获取记录标识，请重新选择\");\r\n        return;\r\n      }\r\n\r\n      this.$confirm('是否确认删除?', \"警告\", {\r\n          cancelButtonText: \"取消\",\r\n          confirmButtonText: \"确定\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delPunishment(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 显示供应商信息查询弹窗（表单用） */\r\n    showSuppInfoDialog() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.suppInfoDialog.show();\r\n    },\r\n    /** 显示供应商信息查询弹窗（查询条件用） */\r\n    showSuppInfoDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.suppInfoDialog.show();\r\n    },\r\n    /** 处理供应商选择 */\r\n    handleSuppSelect(suppInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的供应商选择\r\n        this.form.suppId = suppInfo.suppId;\r\n        this.form.suppName = suppInfo.suppName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的供应商选择\r\n        this.queryParams.suppId = suppInfo.suppId;\r\n        this.queryParams.suppName = suppInfo.suppName;\r\n      }\r\n    },\r\n    /** 显示物料信息查询弹窗（表单用） */\r\n    showMaterialInfoDialogForForm() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.materialInfoDialog.show();\r\n    },\r\n    /** 显示物料信息查询弹窗（查询条件用） */\r\n    showMaterialInfoDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.materialInfoDialog.show();\r\n    },\r\n    /** 处理物料选择 */\r\n    handleMaterialSelect(materialInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的物料选择\r\n        this.form.itemNo = materialInfo.itemId;\r\n        this.form.itemName = materialInfo.itemName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的物料选择\r\n        this.queryParams.itemNo = materialInfo.itemId;\r\n        this.queryParams.itemName = materialInfo.itemName;\r\n      }\r\n    },\r\n    /** 显示服务查询弹窗（表单用） */\r\n    showServiceDialogForForm() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.serviceDialog.show();\r\n    },\r\n    /** 显示服务项目查询弹窗（查询条件用） */\r\n    showServiceDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.serviceDialog.show();\r\n    },\r\n    /** 处理服务选择 */\r\n    handleServiceSelect(serviceInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的服务选择\r\n        this.form.itemNo = serviceInfo.serviceNo;\r\n        this.form.itemName = serviceInfo.serviceName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的服务选择\r\n        this.queryParams.itemNo = serviceInfo.serviceNo;\r\n        this.queryParams.itemName = serviceInfo.serviceName;\r\n      }\r\n    },\r\n    /** 显示项目查询弹窗（表单用） */\r\n    showProjectDialogForForm() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.projectDialog.show();\r\n    },\r\n    /** 显示项目查询弹窗（查询条件用） */\r\n    showProjectDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.projectDialog.show();\r\n    },\r\n    /** 处理项目选择 */\r\n    handleProjectSelect(projectInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的项目选择\r\n        this.form.itemNo = projectInfo.projectNo;\r\n        this.form.itemName = projectInfo.projectName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的项目选择\r\n        this.queryParams.itemNo = projectInfo.projectNo;\r\n        this.queryParams.itemName = projectInfo.projectName;\r\n      }\r\n    },\r\n    /** 处理服务项目选择 */\r\n    handleServiceProjectSelect(serviceInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的服务项目选择\r\n        this.form.itemNo = serviceInfo.serviceNo;\r\n        this.form.itemName = serviceInfo.serviceName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的服务项目选择\r\n        this.queryParams.itemNo = serviceInfo.serviceNo;\r\n        this.queryParams.itemName = serviceInfo.serviceName;\r\n      }\r\n    },\r\n    /** 处理物资或服务选择变化 */\r\n    handleMaterialOrServiceChange(value) {\r\n      // 清空相关字段\r\n      if (value === 'M' || value === 'S' || value === 'P') {\r\n        // 选择任何类型时，都清空itemNo和itemName，让用户重新选择\r\n        this.form.itemNo = null;\r\n        this.form.itemName = null;\r\n      } else {\r\n        // 未选择时，清空所有相关字段\r\n        this.form.itemNo = null;\r\n        this.form.itemName = null;\r\n      }\r\n\r\n      // 切换类型后，清除之前的验证错误信息\r\n      this.$nextTick(() => {\r\n        if (this.$refs.form) {\r\n          this.$refs.form.clearValidate(['itemNo', 'itemName']);\r\n        }\r\n      });\r\n    },\r\n    /** 处理查询条件中物资或服务选择变化 */\r\n    handleQueryMaterialOrServiceChange(value) {\r\n      // 清空查询条件中的相关字段\r\n      if (value === 'M' || value === 'S' || value === 'P') {\r\n        // 选择任何类型时，都清空itemNo和itemName，让用户重新选择\r\n        this.queryParams.itemNo = null;\r\n        this.queryParams.itemName = null;\r\n      } else {\r\n        // 未选择时，清空所有相关字段\r\n        this.queryParams.itemNo = null;\r\n        this.queryParams.itemName = null;\r\n      }\r\n    },\r\n    /** 显示处罚措施选择弹窗 */\r\n    showPunishmentMeasureDialog() {\r\n      this.$refs.punishmentMeasureDialog.show(this.form.punishmentMeasure);\r\n    },\r\n    /** 处理处罚措施选择 */\r\n    handlePunishmentMeasureSelect(measureText) {\r\n      this.form.punishmentMeasure = measureText;\r\n      this.parseMeasureTextToTags(measureText);\r\n    },\r\n\r\n    /** 显示处罚依据选择弹窗 */\r\n    showPunishmentBasisDialog() {\r\n      console.log('显示处罚依据弹窗，当前值：', this.form.punishmentBasis);\r\n      this.$refs.punishmentBasisDialog.show(this.form.punishmentBasis);\r\n    },\r\n\r\n    /** 处理处罚依据选择 */\r\n    handlePunishmentBasisSelect(basisText) {\r\n      this.form.punishmentBasis = basisText;\r\n    },\r\n\r\n    /** 解析处罚措施文本为标签 */\r\n    parseMeasureTextToTags(measureText) {\r\n      this.punishmentMeasureTags = [];\r\n      if (!measureText) return;\r\n\r\n      const measures = measureText.split('；').filter(item => item.trim());\r\n      measures.forEach(measure => {\r\n        const tag = this.createMeasureTag(measure.trim());\r\n        if (tag) {\r\n          this.punishmentMeasureTags.push(tag);\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 创建处罚措施标签 */\r\n    createMeasureTag(measureText) {\r\n      if (measureText.includes('处罚') && measureText.includes('元')) {\r\n        return { text: measureText, type: 'penalty' };\r\n      } else if (measureText === '降级') {\r\n        return { text: measureText, type: 'downgrade' };\r\n      } else if (measureText === '淘汰（禁用）') {\r\n        return { text: measureText, type: 'eliminate' };\r\n      } else if (measureText.includes('暂缓') && measureText.includes('月')) {\r\n        return { text: measureText, type: 'suspend' };\r\n      }\r\n      return null;\r\n    },\r\n\r\n    /** 获取标签类型对应的颜色 */\r\n    getTagType(type) {\r\n      const typeMap = {\r\n        'penalty': 'danger',\r\n        'downgrade': 'danger',\r\n        'eliminate': 'danger',\r\n        'suspend': 'danger'\r\n      };\r\n      return typeMap[type] || '';\r\n    },\r\n\r\n    /** 删除处罚措施标签 */\r\n    removeMeasureTag(index) {\r\n      this.punishmentMeasureTags.splice(index, 1);\r\n      this.updateMeasureText();\r\n    },\r\n\r\n    /** 更新处罚措施文本 */\r\n    updateMeasureText() {\r\n      const measureTexts = this.punishmentMeasureTags.map(tag => tag.text);\r\n      this.form.punishmentMeasure = measureTexts.join('；');\r\n    },\r\n\r\n\r\n    /** 根据填报人工号获取名字、单位信息 */\r\n    getUserCompanyInfo(userName) {\r\n      getUserCompany(userName).then(response => {\r\n        if (response.code === 200 && response.data) {\r\n          // 从SysUser对象中取rsDeptName作为确认部门和申请部门\r\n          const deptName = response.data.rsDeptName || '';\r\n          this.form.companyCode = deptName;  // 确认部门\r\n          this.form.deptNo = deptName;       // 申请部门，默认与确认部门一致\r\n          this.form.userName = response.data.nickName || '';\r\n\r\n        }\r\n      }).catch(error => {\r\n        console.warn('获取单位信息失败:', error);\r\n        // 不显示错误提示，避免影响用户体验\r\n      });\r\n    },\r\n\r\n\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$confirm('是否确认导出所有供应商处罚记录数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          // 手动构建时间范围参数\r\n          let params = { ...this.queryParams };\r\n          if (!params.params) {\r\n            params.params = {};\r\n          }\r\n\r\n          // 处理事件发生时间范围\r\n          if (this.happenedTimeRange && this.happenedTimeRange.length === 2) {\r\n            params.params[\"happenedTimeBeginTime\"] = this.happenedTimeRange[0];\r\n            params.params[\"happenedTimeEndTime\"] = this.happenedTimeRange[1];\r\n          }\r\n\r\n          // 处理处罚执行时间范围\r\n          if (this.punishmentTimeRange && this.punishmentTimeRange.length === 2) {\r\n            params.params[\"punishmentTimeBeginTime\"] = this.punishmentTimeRange[0];\r\n            params.params[\"punishmentTimeEndTime\"] = this.punishmentTimeRange[1];\r\n          }\r\n\r\n          return exportPunishment(params);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 日期范围选择器宽度与其他输入框一致 */\r\n.el-date-editor.el-range-editor {\r\n  width: 205px !important;\r\n  font-size: 12px !important;\r\n}\r\n\r\n/* 调整日期范围选择器内部样式 */\r\n.el-date-editor.el-range-editor .el-range-input {\r\n  width: 32% !important;\r\n  font-size: 12px !important;\r\n  text-align: center;\r\n}\r\n\r\n.el-date-editor.el-range-editor .el-range-separator {\r\n  width: 20% !important;\r\n  text-align: center;\r\n  font-size: 12px !important;\r\n  color: #C0C4CC;\r\n}\r\n\r\n/* 确保日期范围选择器的高度与其他输入框一致 */\r\n.el-date-editor.el-range-editor.el-input__inner {\r\n  height: 28px !important;\r\n  line-height: 28px !important;\r\n}\r\n\r\n/* 缩短右侧留白 */\r\n.app-container {\r\n  padding-right: 5px !important;\r\n}\r\n\r\n/* 调整表单容器宽度 */\r\n.el-form--inline {\r\n  max-width: calc(100% - 10px) !important;\r\n}\r\n\r\n/* 调整输入框上下间距相等 */\r\n.el-form--inline .el-form-item {\r\n  margin-bottom: 18px !important;\r\n  margin-top: 0 !important;\r\n}\r\n\r\n/* 确保第一行没有额外的上边距 */\r\n.el-form--inline .el-form-item:first-child {\r\n  margin-top: 0 !important;\r\n}\r\n\r\n/* 缩小输入框标题字体 */\r\n.el-form--inline .el-form-item__label {\r\n  font-size: 10px !important;\r\n}\r\n\r\n/* 更强的选择器确保字体样式生效 */\r\n.app-container .el-form--inline .el-form-item .el-form-item__label {\r\n  font-size: 10px !important;\r\n  font-weight: normal !important;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n.el-dialog__header {\r\n  text-align: center !important;\r\n}\r\n\r\n.el-dialog__header .el-dialog__title {\r\n  text-align: center !important;\r\n  width: 100% !important;\r\n  display: block !important;\r\n}\r\n\r\n/* 更强的选择器确保弹窗标题居中 */\r\n.el-dialog .el-dialog__header .el-dialog__title {\r\n  text-align: center !important;\r\n  width: 100% !important;\r\n  display: block !important;\r\n  margin: 0 auto !important;\r\n}\r\n\r\n/* 使用深度选择器确保样式穿透 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center !important;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center !important;\r\n  width: 100% !important;\r\n  display: block !important;\r\n}\r\n\r\n/* 搜索图标样式 */\r\n.search-icon {\r\n  cursor: pointer;\r\n  color: #909399;\r\n  padding: 0 8px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.search-icon:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 操作列固定宽度 */\r\n.el-table .fixed-width {\r\n  width: 200px;\r\n  min-width: 200px;\r\n}\r\n\r\n/* 小间距 */\r\n.el-table .small-padding .cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n}\r\n\r\n/* 处罚措施输入框样式 */\r\n.measure-input-wrapper {\r\n  position: relative;\r\n  min-height: 78px;\r\n  height: 78px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  padding: 8px 12px;\r\n  background-color: #fff;\r\n}\r\n\r\n.measure-tags-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  min-height: 62px;\r\n  max-height: 62px;\r\n  overflow-y: auto;\r\n  align-items: flex-start;\r\n  align-content: flex-start;\r\n}\r\n\r\n.measure-tag {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  height: 28px;\r\n  line-height: 26px;\r\n  border-radius: 14px;\r\n  padding: 0 12px;\r\n  cursor: default;\r\n  font-weight: 500;\r\n}\r\n\r\n.measure-tag .el-icon-close {\r\n  margin-left: 6px;\r\n  font-size: 12px;\r\n}\r\n\r\n.measure-placeholder {\r\n  color: #c0c4cc;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  position: absolute;\r\n  top: 8px;\r\n  left: 12px;\r\n  cursor: pointer;\r\n  user-select: none;\r\n}\r\n\r\n.measure-placeholder:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n.measure-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.measure-buttons {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.measure-btn {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  min-width: 50px;\r\n  height: 28px;\r\n  line-height: 1;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.measure-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 选择按钮样式 */\r\n.measure-btn.el-button--primary {\r\n  background: rgba(64, 158, 255, 0.9);\r\n  border-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.measure-btn.el-button--primary:hover {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n}\r\n\r\n/* 清空按钮样式 */\r\n.measure-btn.el-button--danger {\r\n  background: rgba(245, 108, 108, 0.9);\r\n  border-color: #F56C6C;\r\n  color: white;\r\n}\r\n\r\n.measure-btn.el-button--danger:hover {\r\n  background: #F56C6C;\r\n  border-color: #F56C6C;\r\n}\r\n\r\n/* 只读处罚措施输入框样式 */\r\n.measure-textarea.el-textarea.is-disabled .el-textarea__inner,\r\n.measure-textarea .el-textarea__inner[readonly] {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #606266;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 查询表单样式优化 - 每行4个输入框 */\r\n.el-form--inline {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: flex-start;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  width: calc(25% - 15px);\r\n  margin-right: 20px;\r\n  margin-bottom: 15px;\r\n  flex: 0 0 auto;\r\n}\r\n\r\n/* 每行第4个元素不需要右边距 */\r\n.el-form--inline .el-form-item:nth-child(4n) {\r\n  margin-right: 0;\r\n}\r\n\r\n/* 搜索按钮区域单独处理 */\r\n.el-form--inline .el-form-item:last-child {\r\n  width: auto;\r\n  margin-left: auto;\r\n  margin-right: 0;\r\n}\r\n\r\n/* 统一输入框宽度 */\r\n.el-form--inline .el-form-item .el-input {\r\n  width: 100%;\r\n}\r\n\r\n/* 统一选择框宽度 */\r\n.el-form--inline .el-form-item .el-select {\r\n  width: 100%;\r\n}\r\n\r\n/* 统一日期选择器宽度 */\r\n.el-form--inline .el-form-item .el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1400px) {\r\n  .el-form--inline .el-form-item {\r\n    width: calc(33.33% - 13px);\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(4n) {\r\n    margin-right: 20px;\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(3n) {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 1000px) {\r\n  .el-form--inline .el-form-item {\r\n    width: calc(50% - 10px);\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(3n) {\r\n    margin-right: 20px;\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(2n) {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  width: 100%;\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n}\r\n\r\n/* 处罚依据输入框样式 */\r\n.basis-input-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.basis-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.basis-buttons {\r\n  position: absolute;\r\n  top: 5px;\r\n  right: 5px;\r\n  z-index: 10;\r\n}\r\n\r\n.basis-btn {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  min-width: 50px;\r\n  height: 28px;\r\n  line-height: 1;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.basis-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 选择按钮样式 */\r\n.basis-btn.el-button--primary {\r\n  background: rgba(64, 158, 255, 0.9);\r\n  border-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.basis-btn.el-button--primary:hover {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n}\r\n\r\n/* 滚动条样式优化 */\r\n.table-container::-webkit-scrollbar {\r\n  height: 8px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n\r\n\r\n\r\n/* Element UI 固定列样式优化 */\r\n::v-deep .el-table__fixed-right {\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 只读输入框样式 */\r\n.readonly-input ::v-deep .el-input__inner {\r\n  background-color: #f5f7fa !important;\r\n  border-color: #e4e7ed !important;\r\n  color: #909399 !important;\r\n  cursor: not-allowed !important;\r\n}\r\n\r\n.readonly-input ::v-deep .el-input__inner:hover {\r\n  border-color: #e4e7ed !important;\r\n}\r\n\r\n.readonly-input ::v-deep .el-input__inner:focus {\r\n  border-color: #e4e7ed !important;\r\n  box-shadow: none !important;\r\n}\r\n</style>\r\n"]}]}