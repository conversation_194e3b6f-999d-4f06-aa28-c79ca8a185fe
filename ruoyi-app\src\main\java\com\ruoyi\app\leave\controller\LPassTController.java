package com.ruoyi.app.leave.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LPassT;
import com.ruoyi.app.leave.service.ILPassTService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 不返回主Controller
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@RestController
@RequestMapping("/leave/pass")
public class LPassTController extends BaseController
{
    @Autowired
    private ILPassTService lPassTService;

    /**
     * 查询不返回主列表
     */
    @PreAuthorize("@ss.hasPermi('leave:pass:list')")
    @GetMapping("/list")
    public TableDataInfo list(LPassT lPassT)
    {
        startPage();
        List<LPassT> list = lPassTService.selectLPassTList(lPassT);
        return getDataTable(list);
    }

    /**
     * 导出不返回主列表
     */
    @PreAuthorize("@ss.hasPermi('leave:pass:export')")
    @Log(title = "不返回主", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LPassT lPassT)
    {
        List<LPassT> list = lPassTService.selectLPassTList(lPassT);
        ExcelUtil<LPassT> util = new ExcelUtil<LPassT>(LPassT.class);
        return util.exportExcel(list, "pass");
    }

    /**
     * 获取不返回主详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:pass:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lPassTService.selectLPassTById(id));
    }

    /**
     * 新增不返回主
     */
    @PreAuthorize("@ss.hasPermi('leave:pass:add')")
    @Log(title = "不返回主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LPassT lPassT)
    {
        return toAjax(lPassTService.insertLPassT(lPassT));
    }

    /**
     * 修改不返回主
     */
    @PreAuthorize("@ss.hasPermi('leave:pass:edit')")
    @Log(title = "不返回主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LPassT lPassT)
    {
        return toAjax(lPassTService.updateLPassT(lPassT));
    }

    /**
     * 删除不返回主
     */
    @PreAuthorize("@ss.hasPermi('leave:pass:remove')")
    @Log(title = "不返回主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lPassTService.deleteLPassTByIds(ids));
    }
}
