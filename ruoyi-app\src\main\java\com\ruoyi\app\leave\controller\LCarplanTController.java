package com.ruoyi.app.leave.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LCarplanT;
import com.ruoyi.app.leave.service.ILCarplanTService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 跨区调拨主Controller
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@RestController
@RequestMapping("/leave/carPlan")
public class LCarplanTController extends BaseController
{
    @Autowired
    private ILCarplanTService lCarplanTService;

    /**
     * 查询跨区调拨主列表
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlan:list')")
    @GetMapping("/list")
    public TableDataInfo list(LCarplanT lCarplanT)
    {
        startPage();
        List<LCarplanT> list = lCarplanTService.selectLCarplanTList(lCarplanT);
        return getDataTable(list);
    }

    /**
     * 导出跨区调拨主列表
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlan:export')")
    @Log(title = "跨区调拨主", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LCarplanT lCarplanT)
    {
        List<LCarplanT> list = lCarplanTService.selectLCarplanTList(lCarplanT);
        ExcelUtil<LCarplanT> util = new ExcelUtil<LCarplanT>(LCarplanT.class);
        return util.exportExcel(list, "carPlan");
    }

    /**
     * 获取跨区调拨主详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lCarplanTService.selectLCarplanTById(id));
    }

    /**
     * 新增跨区调拨主
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlan:add')")
    @Log(title = "跨区调拨主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LCarplanT lCarplanT)
    {
        return toAjax(lCarplanTService.insertLCarplanT(lCarplanT));
    }

    /**
     * 修改跨区调拨主
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlan:edit')")
    @Log(title = "跨区调拨主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LCarplanT lCarplanT)
    {
        return toAjax(lCarplanTService.updateLCarplanT(lCarplanT));
    }

    /**
     * 删除跨区调拨主
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlan:remove')")
    @Log(title = "跨区调拨主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lCarplanTService.deleteLCarplanTByIds(ids));
    }
}
