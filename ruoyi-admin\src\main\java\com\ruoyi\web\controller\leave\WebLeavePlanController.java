package com.ruoyi.web.controller.leave;

import com.google.common.collect.Lists;
import com.ruoyi.app.dgcb.domain.ImportItemInfo;
import com.ruoyi.app.leave.domain.*;
import com.ruoyi.app.leave.dto.ApproveRequestDTO;
import com.ruoyi.app.leave.dto.LeaveUserPermitDto;
import com.ruoyi.app.leave.dto.LeaveUserQueryDto;
import com.ruoyi.app.leave.enums.LeavePlanStatusEnum;
import com.ruoyi.app.leave.enums.LeaveRoleEnum;
import com.ruoyi.app.leave.mapper.LeavePlanMapper;
import com.ruoyi.app.leave.mapper.LeavePlanMaterialMapper;
import com.ruoyi.app.leave.mapper.LeaveTaskMaterialMapper;
import com.ruoyi.app.leave.service.ILeavePlanService;
import com.ruoyi.app.leave.service.ILeavePlanMaterialService;
import com.ruoyi.app.leave.service.ILeaveLogService;
import com.ruoyi.app.leave.service.ILeaveTaskMaterialService;
import com.ruoyi.app.leave.service.impl.LeaveDeptAssignmentServiceImpl;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.web.service.TemplateMessageService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.TimerTask;
import java.util.stream.Collectors;

/**
 * 出门证计划申请Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/web/leave/plan")
public class WebLeavePlanController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(WebLeavePlanController.class);

    @Autowired
    private ILeavePlanService leavePlanService;
    
    @Autowired
    private ILeavePlanMaterialService leavePlanMaterialService;
    
    @Autowired
    private ILeaveLogService leaveLogService;
    
    @Autowired
    private SysUserMapper sysUserMapper;
    
    @Autowired
    private ISysRoleService sysRoleService;
    
    @Autowired
    private TemplateMessageService templateMessageService;

    @Autowired
    private LeaveDeptAssignmentServiceImpl leaveDeptAssignmentService;

    @Autowired
    private ILeaveTaskMaterialService leaveTaskMaterialService;

    @Autowired
    private LeavePlanMapper leavePlanMapper;

    @Autowired
    private LeavePlanMaterialMapper leavePlanMaterialMapper;

    /**
     * 查询出门证计划申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeavePlan leavePlan) {
        //获取当前用户
        String workNo = SecurityUtils.getUsername();
        
        // 首先检查用户是否拥有生产指挥中心审核人角色
        boolean isCenterApprover = sysRoleService.selectRoleExistByUserName(workNo, LeaveRoleEnum.CENTER_APPROVER.getCode());
        boolean isGuard = sysRoleService.selectRoleExistByUserName(workNo, LeaveRoleEnum.GUARD.getCode());
        
        // 如果是生产指挥中心审核人或门卫，可以看到所有计划，不需要添加部门限制
        if (isCenterApprover || isGuard) {
            // 生产指挥中心审核人或门卫可以看到所有计划，不需要添加任何限制
        } else {
            //定义分厂角色集合
            List<String> factoryRoleList = Arrays.asList(
                    LeaveRoleEnum.APPLICANT.getCode(),
                    LeaveRoleEnum.FACTORY_APPROVER.getCode(),
                    LeaveRoleEnum.FACTORY_SEC_APPROVER.getCode()
            );
            //检查用户是否属于分厂角色
            boolean isFactoryUser = sysRoleService.selectRoleExistByUserName(workNo, factoryRoleList);
            if (isFactoryUser) {
                //获取用户所属分厂id
                LeaveUserQueryDto query = new LeaveUserQueryDto();
                query.setUserName(workNo);
                LeaveUserPermitDto userInfo = leaveDeptAssignmentService.getUserInfo(query);

                if (userInfo == null || userInfo.getDeptId() == null) {
                    throw new RuntimeException("用户未分配部门，请联系管理员");
                }

                leavePlan.setSourceCompanyCode(userInfo.getDeptId().toString());
            }
        }
        startPage();
        List<LeavePlan> list = leavePlanService.selectLeavePlanList(leavePlan);
        for (LeavePlan planList : list) {
            leavePlanService.showBtn(planList);
        }
        return getDataTable(list);
    }

    /**
     * 查询任务物资列表
     */
    @GetMapping("/listTaskMaterial")
    public TableDataInfo listTaskMaterial(LeaveTaskMaterial leaveTaskMaterial) {

        List<LeaveTaskMaterial> leaveTaskMaterials = leaveTaskMaterialService.selectLeaveTaskMaterialList(leaveTaskMaterial);
        return getDataTable(leaveTaskMaterials);
    }



    /**
     * 导出出门证计划申请列表
     */
    @Log(title = "出门证计划申请", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeavePlan leavePlan)
    {
        List<LeavePlan> list = leavePlanService.selectLeavePlanList(leavePlan);
        ExcelUtil<LeavePlan> util = new ExcelUtil<LeavePlan>(LeavePlan.class);
        return util.exportExcel(list, "plan");
    }

    /**
     * 导出出门证计划申请列表
     */
    @Log(title = "导出物资模板", businessType = BusinessType.EXPORT)
    @GetMapping("/exportMaterialTemplate")
    public AjaxResult exportMaterialTemplate()
    {
        ExcelUtil<LeaveMaterialTemplate> util = new ExcelUtil<LeaveMaterialTemplate>(LeaveMaterialTemplate.class);
        return util.exportExcel(Lists.newArrayList(), "物资信息");
    }

    @Log(title = "物资导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importMaterialList")
    public AjaxResult importMaterialList(MultipartFile file) throws Exception {
        ExcelUtil<LeaveMaterialTemplate> util = new ExcelUtil<LeaveMaterialTemplate>(LeaveMaterialTemplate.class);
        List<LeaveMaterialTemplate> list = util.importExcel(file.getInputStream());

        return AjaxResult.success(list);
    }

    /**
     * 获取出门证计划申请详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leavePlanService.selectLeavePlanById(id));
    }

    /**
     * 新增出门证计划申请
     */
    @Log(title = "出门证计划申请", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional
   public AjaxResult add(@RequestBody LeavePlan leavePlan) {
        leavePlanService.insertLeavePlan(leavePlan);
        return AjaxResult.success();
   }

    /**
     * 修改出门证计划申请
     */
    @Log(title = "出门证计划申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeavePlan leavePlan)
    {
        return toAjax(leavePlanService.updateLeavePlan(leavePlan));
    }

    /**
     * 出门证详情
     * @param
     * @return
     */
    @GetMapping("/detail/{applyNo}")
    public AjaxResult detail(@PathVariable String applyNo)
    {
        return AjaxResult.success(leavePlanService.detail(applyNo, SecurityUtils.getUsername()));
    }

    /**
     * 删除出门证计划申请
     */
    @Log(title = "出门证计划申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leavePlanService.deleteLeavePlanByIds(ids));
    }


    //审批通过
    @PostMapping("/approve")
    public AjaxResult approve(@RequestBody ApproveRequestDTO approveRequestDTO)
    {
        approveRequestDTO.setApproverWorkNo(SecurityUtils.getUsername());
        SysUser sysUser = sysUserMapper.selectUserByUserName(SecurityUtils.getUsername());
        leavePlanService.approve(approveRequestDTO,sysUser);

        //获取出门证计划申请信息
        LeavePlan plan = leavePlanMapper.selectLeavePlanByApplyNo(String.valueOf(approveRequestDTO.getApplyNo()));
        //获取申请状态
        LeavePlanStatusEnum leavePlanStatusEnum = LeavePlanStatusEnum.getByCode(plan.getPlanStatus());
        // 如果计量物资且审批通过，调用handlePlan方法同步计量数据库数据
        if (plan.getMeasureFlag()  == 1 &&leavePlanStatusEnum == LeavePlanStatusEnum.APPROVAL_COMPLETE) {
            //添加物资信息
            List<LeavePlanMaterial> materials = leavePlanMaterialMapper.selectLeavePlanMaterialByApplyNo(plan.getApplyNo());
            plan.setMaterials(materials);
            leavePlanService.handlePlan(plan);
        }

        return AjaxResult.success(true);
    }

    //废弃
    @PostMapping("/discard")
    public AjaxResult discard(@RequestBody LeavePlan leavePlan)
    {
        SysUser sysUser = sysUserMapper.selectUserByUserName(SecurityUtils.getUsername());
        leavePlanService.discard(leavePlan.getApplyNo(), sysUser);
        return AjaxResult.success();
    }

    /**
     * 物资确认，将计划状态更新为已完成（7）
     */
    @PostMapping("/confirmMaterial")
    public AjaxResult confirmMaterial(@RequestBody(required = true)  java.util.Map<String, Object> params) {
        String applyNo = (String) params.get("applyNo");
        if (applyNo == null) {
            return AjaxResult.error("参数applyNo不能为空");
        }
        int result = leavePlanService.confirmMaterial(applyNo);
        return result > 0 ? AjaxResult.success() : AjaxResult.error("物资确认失败");
    }

//    //判断是否允许派车
//    @PostMapping("/isAllowPatch")
//    public AjaxResult isAllowPatch(@RequestBody LeavePlan leavePlan)
//    {
//        leavePlanService.isAllowPatch(leavePlan)
//        return ;
//    }

   
}
