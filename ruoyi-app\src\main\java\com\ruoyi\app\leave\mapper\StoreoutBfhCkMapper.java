package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.StoreoutBfhCkMeasure;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 备发货出库Mapper接口
 * 
 * <AUTHOR>
 */
public interface StoreoutBfhCkMapper 
{
    /**
     * 查询备发货出库
     * 
     * @param id 备发货出库主键
     * @return 备发货出库
     */
    @DataSource(DataSourceType.XCC1)
    public StoreoutBfhCkMeasure selectStoreoutBfhCkById(Long id);

    /**
     * 查询备发货出库列表
     * 
     * @param storeoutBfhCkMeasure 备发货出库
     * @return 备发货出库集合
     */
    @DataSource(DataSourceType.XCC1)
    public List<StoreoutBfhCkMeasure> selectStoreoutBfhCkList(StoreoutBfhCkMeasure storeoutBfhCkMeasure);

    /**
     * 新增备发货出库
     * 
     * @param storeoutBfhCkMeasure 备发货出库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int insertStoreoutBfhCk(StoreoutBfhCkMeasure storeoutBfhCkMeasure);

    /**
     * 修改备发货出库
     * 
     * @param storeoutBfhCkMeasure 备发货出库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int updateStoreoutBfhCk(StoreoutBfhCkMeasure storeoutBfhCkMeasure);

    /**
     * 删除备发货出库
     * 
     * @param id 备发货出库主键
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreoutBfhCkById(Long id);

    /**
     * 批量删除备发货出库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreoutBfhCkByIds(Long[] ids);

    /**
     * 根据匹配ID删除备发货出库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreoutBfhCkByMatchid(String matchid);
} 