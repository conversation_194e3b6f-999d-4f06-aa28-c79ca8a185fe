package com.ruoyi.app.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.app.domain.OutputForm;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 产量生产报表（生产指挥中心）Service接口
 * 
 * <AUTHOR>
 * @date 2021-04-21
 */
public interface IOutputFormService 
{
    /**
     * 查询产量生产报表（生产指挥中心）
     * 

     * @return 产量生产报表（生产指挥中心）
     */
    @DataSource(value = DataSourceType.XCPRODNEW)
    public List<Map<String, Object>>  selectOutputFormByItemName(OutputForm outputForm);

    /**
     * 查询产量生产报表（生产指挥中心）列表
     * 
     * @param outputForm 产量生产报表（生产指挥中心）
     * @return 产量生产报表（生产指挥中心）集合
     */
    @DataSource(value = DataSourceType.XCPRODNEW)
    public List<Map<String, Object>> selectOutputFormList(OutputForm outputForm);


}
