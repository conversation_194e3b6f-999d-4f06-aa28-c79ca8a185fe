{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\info\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\info\\index.vue", "mtime": 1756170476879}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuZW5kcy13aXRoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi51cmwuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi51cmwudG8tanNvbi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLnVybC1zZWFyY2gtcGFyYW1zLmpzIik7CnZhciBfaW5mbyA9IHJlcXVpcmUoIkAvYXBpL3N1cHBseS9pbmZvIik7CnZhciBfZmFjID0gcmVxdWlyZSgiQC9hcGkvc3VwcGx5L2ZhYyIpOwp2YXIgX2hlYWx0aCA9IHJlcXVpcmUoIkAvYXBpL3N1cHBseS9oZWFsdGgiKTsKdmFyIF9maWxlID0gcmVxdWlyZSgiQC9hcGkvc3VwcGx5L2ZpbGUiKTsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7CnZhciBfYXV0aCA9IHJlcXVpcmUoIkAvdXRpbHMvYXV0aCIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogJ1N1cHBseVVzZXJJbmZvJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBzdXBwbHlDb2RlOiAnJwogICAgICB9LAogICAgICB1c2VyTGlzdDogW10sCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlspfkvY3or4bliKvljaEKICAgICAgZmFjRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGZhY0Zvcm06IHt9LAogICAgICBmYWNGb3JtSXRlbXM6IFt7CiAgICAgICAgZmllbGQ6ICd1c2VyUG9zdCcsCiAgICAgICAgdGl0bGU6ICflspfkvY3lkI3np7AnLAogICAgICAgIHNwYW46IDI0LAogICAgICAgIGl0ZW1SZW5kZXI6IHsKICAgICAgICAgIG5hbWU6ICdWeGVUZXh0YXJlYScsCiAgICAgICAgICBwcm9wczogewogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeWyl+S9jeWQjeensCcsCiAgICAgICAgICAgIHJvd3M6IDIKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBmaWVsZDogJ3VzZXJGYWNDbGFzcycsCiAgICAgICAgdGl0bGU6ICflspfkvY3nj63nu4QnLAogICAgICAgIHNwYW46IDEyLAogICAgICAgIGl0ZW1SZW5kZXI6IHsKICAgICAgICAgIG5hbWU6ICdWeGVJbnB1dCcsCiAgICAgICAgICBwcm9wczogewogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeWyl+S9jeePree7hCcKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBmaWVsZDogJ3VzZXJEZXB0TmFtZScsCiAgICAgICAgdGl0bGU6ICfmiYDlsZ7pg6jpl6gnLAogICAgICAgIHNwYW46IDEyLAogICAgICAgIGl0ZW1SZW5kZXI6IHsKICAgICAgICAgIG5hbWU6ICdWeGVJbnB1dCcsCiAgICAgICAgICBwcm9wczogewogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeaJgOWxnumDqOmXqCcKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBmaWVsZDogJ3VzZXJGYWNXb3JrJywKICAgICAgICB0aXRsZTogJ+Wyl+S9jeaPj+i/sCcsCiAgICAgICAgc3BhbjogMTIsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZUlucHV0JywKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bKX5L2N5o+P6L+wJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGZpZWxkOiAndXNlclRpbWVCZWdpbicsCiAgICAgICAgdGl0bGU6ICflhaXljoLml7bpl7QnLAogICAgICAgIHNwYW46IDEyLAogICAgICAgIGl0ZW1SZW5kZXI6IHsKICAgICAgICAgIG5hbWU6ICdWeGVJbnB1dCcsCiAgICAgICAgICBwcm9wczogewogICAgICAgICAgICB0eXBlOiAnZGF0ZScsCiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6YCJ5oup5pel5pyfJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGZpZWxkOiAndXNlclRpbWVFbmQnLAogICAgICAgIHRpdGxlOiAn56a75Y6C5pe26Ze0JywKICAgICAgICBzcGFuOiAxMiwKICAgICAgICBpdGVtUmVuZGVyOiB7CiAgICAgICAgICBuYW1lOiAnVnhlSW5wdXQnLAogICAgICAgICAgcHJvcHM6IHsKICAgICAgICAgICAgdHlwZTogJ2RhdGUnLAogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+mAieaLqeaXpeacnycKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBmaWVsZDogJ3N0YXRlJywKICAgICAgICB0aXRsZTogJ+eKtuaAgScsCiAgICAgICAgc3BhbjogMjQsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZVNlbGVjdCcsCiAgICAgICAgICBvcHRpb25zOiBbewogICAgICAgICAgICBsYWJlbDogJ+i1t+iNiScsCiAgICAgICAgICAgIHZhbHVlOiAwCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGxhYmVsOiAn5YiG5Y6C5a6h5qC45Lq6JywKICAgICAgICAgICAgdmFsdWU6IDEKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICfkurrlipvotYTmupDpg6gnLAogICAgICAgICAgICB2YWx1ZTogMgogICAgICAgICAgfSwgewogICAgICAgICAgICBsYWJlbDogJ+mAgOWbnicsCiAgICAgICAgICAgIHZhbHVlOiAtMQogICAgICAgICAgfSwgewogICAgICAgICAgICBsYWJlbDogJ+emgeeUqCcsCiAgICAgICAgICAgIHZhbHVlOiAxMDEKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICflrqHmoLjpgJrov4cnLAogICAgICAgICAgICB2YWx1ZTogOTkKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICfliKDpmaQnLAogICAgICAgICAgICB2YWx1ZTogMTAyCiAgICAgICAgICB9XSwKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfV0sCiAgICAgIC8vIOWBpeW6t+S/oeaBrwogICAgICBoZWFsdGhEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgaGVhbHRoRm9ybToge30sCiAgICAgIGhlYWx0aEZvcm1JdGVtczogW3sKICAgICAgICBmaWVsZDogJ2hlYWxkYXRlJywKICAgICAgICB0aXRsZTogJ+S9k+ajgOaXpeacnycsCiAgICAgICAgc3BhbjogMTIsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZUlucHV0JywKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHR5cGU6ICdkYXRlJywKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfpgInmi6nml6XmnJ8nCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgZmllbGQ6ICdob3MnLAogICAgICAgIHRpdGxlOiAn5Yy76ZmiJywKICAgICAgICBzcGFuOiAxMiwKICAgICAgICBpdGVtUmVuZGVyOiB7CiAgICAgICAgICBuYW1lOiAnVnhlSW5wdXQnLAogICAgICAgICAgcHJvcHM6IHsKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXljLvpmaInCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgZmllbGQ6ICdoZWFsdHonLAogICAgICAgIHRpdGxlOiAn5L2T6YeNJywKICAgICAgICBzcGFuOiAxMiwKICAgICAgICBpdGVtUmVuZGVyOiB7CiAgICAgICAgICBuYW1lOiAnVnhlSW5wdXQnLAogICAgICAgICAgcHJvcHM6IHsKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXkvZPph40nCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgZmllbGQ6ICdoZWFsdHp6cycsCiAgICAgICAgdGl0bGU6ICfkvZPph43mjIfmlbAnLAogICAgICAgIHNwYW46IDEyLAogICAgICAgIGl0ZW1SZW5kZXI6IHsKICAgICAgICAgIG5hbWU6ICdWeGVJbnB1dCcsCiAgICAgICAgICBwcm9wczogewogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeS9k+mHjeaMh+aVsCcKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBmaWVsZDogJ2hlYWxwdHQnLAogICAgICAgIHRpdGxlOiAn6KGA57OWJywKICAgICAgICBzcGFuOiAxMiwKICAgICAgICBpdGVtUmVuZGVyOiB7CiAgICAgICAgICBuYW1lOiAnVnhlSW5wdXQnLAogICAgICAgICAgcHJvcHM6IHsKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXooYDns5YnCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgZmllbGQ6ICdoZWFsc3N5JywKICAgICAgICB0aXRsZTogJ+aUtue8qeWOiycsCiAgICAgICAgc3BhbjogMTIsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZUlucHV0JywKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5pS257yp5Y6LJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGZpZWxkOiAnaGVhbHN6eScsCiAgICAgICAgdGl0bGU6ICfoiJLlvKDljosnLAogICAgICAgIHNwYW46IDEyLAogICAgICAgIGl0ZW1SZW5kZXI6IHsKICAgICAgICAgIG5hbWU6ICdWeGVJbnB1dCcsCiAgICAgICAgICBwcm9wczogewogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeiIkuW8oOWOiycKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBmaWVsZDogJ2hlYWx6ZGdjJywKICAgICAgICB0aXRsZTogJ+aAu+iDhuWbuumGhycsCiAgICAgICAgc3BhbjogMTIsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZUlucHV0JywKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5oC76IOG5Zu66YaHJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGZpZWxkOiAnaGVhbGd5c3onLAogICAgICAgIHRpdGxlOiAn55SY5rK55LiJ6YWvJywKICAgICAgICBzcGFuOiAxMiwKICAgICAgICBpdGVtUmVuZGVyOiB7CiAgICAgICAgICBuYW1lOiAnVnhlSW5wdXQnLAogICAgICAgICAgcHJvcHM6IHsKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXnlJjmsrnkuInpha8nCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgZmllbGQ6ICdoZWFsZ2EnLAogICAgICAgIHRpdGxlOiAn6LC35rCo6YWw6L2s6IK96YW2JywKICAgICAgICBzcGFuOiAxMiwKICAgICAgICBpdGVtUmVuZGVyOiB7CiAgICAgICAgICBuYW1lOiAnVnhlSW5wdXQnLAogICAgICAgICAgcHJvcHM6IHsKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXosLfmsKjphbDovazogr3phbYnCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgZmllbGQ6ICdoZWFsZ2InLAogICAgICAgIHRpdGxlOiAn6LC35LiZ6L2s5rCo6YW2JywKICAgICAgICBzcGFuOiAxMiwKICAgICAgICBpdGVtUmVuZGVyOiB7CiAgICAgICAgICBuYW1lOiAnVnhlSW5wdXQnLAogICAgICAgICAgcHJvcHM6IHsKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXosLfkuJnovazmsKjphbYnCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgZmllbGQ6ICdoZWFsZ2MnLAogICAgICAgIHRpdGxlOiAn6LC36I2J6L2s5rCo6YW2JywKICAgICAgICBzcGFuOiAxMiwKICAgICAgICBpdGVtUmVuZGVyOiB7CiAgICAgICAgICBuYW1lOiAnVnhlSW5wdXQnLAogICAgICAgICAgcHJvcHM6IHsKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXosLfojYnovazmsKjphbYnCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgZmllbGQ6ICdoZWFsbnNkJywKICAgICAgICB0aXRsZTogJ+Wwv+e0oOawricsCiAgICAgICAgc3BhbjogMTIsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZUlucHV0JywKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bC/57Sg5rCuJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGZpZWxkOiAnaGVhbGpnJywKICAgICAgICB0aXRsZTogJ+iCjOmFkCcsCiAgICAgICAgc3BhbjogMTIsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZUlucHV0JywKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6IKM6YWQJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGZpZWxkOiAnaGVhbHhkJywKICAgICAgICB0aXRsZTogJ+W/g+eUteWbvicsCiAgICAgICAgc3BhbjogMTIsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZUlucHV0JywKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5b+D55S15Zu+JwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGZpZWxkOiAnaGVhbHhqJywKICAgICAgICB0aXRsZTogJ+Wwj+e7kycsCiAgICAgICAgc3BhbjogMjQsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZVRleHRhcmVhJywKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bCP57uTJywKICAgICAgICAgICAgcm93czogMgogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGZpZWxkOiAnaGVhbGp5JywKICAgICAgICB0aXRsZTogJ+W7uuiuricsCiAgICAgICAgc3BhbjogMjQsCiAgICAgICAgaXRlbVJlbmRlcjogewogICAgICAgICAgbmFtZTogJ1Z4ZVRleHRhcmVhJywKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bu66K6uJywKICAgICAgICAgICAgcm93czogMgogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGZpZWxkOiAnc3RhdGUnLAogICAgICAgIHRpdGxlOiAn54q25oCBJywKICAgICAgICBzcGFuOiAxMiwKICAgICAgICBpdGVtUmVuZGVyOiB7CiAgICAgICAgICBuYW1lOiAnVnhlU2VsZWN0JywKICAgICAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgICAgIGxhYmVsOiAn5q2j5bi4JywKICAgICAgICAgICAgdmFsdWU6IDEKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICfliKDpmaQnLAogICAgICAgICAgICB2YWx1ZTogMTAxCiAgICAgICAgICB9XSwKICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfV0sCiAgICAgIC8vIOmZhOS7tgogICAgICBmaWxlRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGZpbGVMaXN0OiBbXSwKICAgICAgdXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgJy93ZWIvc3VwcGx5L3VzZXJmaWxlL3VwbG9hZCcsCiAgICAgIC8vIOaWsOeahCBTRlRQIOS4iuS8oOaOpeWPowogICAgICBjdXJyZW50VXNlcklkOiBudWxsLAogICAgICBjdXJyZW50VXNlckluZm86IHt9LAogICAgICAvLyDmlrDlop7vvJrkv53lrZjlvZPliY3nlKjmiLfkv6Hmga8KICAgICAgLy8g5LiK5Lyg6YWN572uCiAgICAgIHVwbG9hZDogewogICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArICgwLCBfYXV0aC5nZXRUb2tlbikoKQogICAgICAgIH0KICAgICAgfSwKICAgICAgLy8g5paw5aKeL+e8lui+keS4u+ihqAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nVGl0bGU6ICcnLAogICAgICBmb3JtOiB7fSwKICAgICAgaW1wb3J0RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGltcG9ydFVybDogJy93ZWIvc3VwcGx5L3VzZXJpbmZvL2ltcG9ydCcKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgdXBsb2FkRGF0YTogZnVuY3Rpb24gdXBsb2FkRGF0YSgpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICB1c2VyaWQ6IHRoaXMuY3VycmVudFVzZXJJZCwKICAgICAgICB1c2VyY29kZTogdGhpcy5jdXJyZW50VXNlckluZm8udXNlckNvZGUsCiAgICAgICAgdXNlcm5hbWU6IHRoaXMuY3VycmVudFVzZXJJbmZvLnVzZXJOYW1lLAogICAgICAgIHN1cHBseWNvZGU6IHRoaXMuY3VycmVudFVzZXJJbmZvLnN1cHBseUNvZGUsCiAgICAgICAgc3VwcGx5bmFtZTogdGhpcy5jdXJyZW50VXNlckluZm8uc3VwcGx5TmFtZSwKICAgICAgICBpZGNhcmQ6IHRoaXMuY3VycmVudFVzZXJJbmZvLmlkY2FyZCwKICAgICAgICB1c2VyZGVwdG5hbWU6IHRoaXMuY3VycmVudFVzZXJJbmZvLnVzZXJEZXB0TmFtZQogICAgICB9OwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLy8g5p+l6K+i55So5oi35YiX6KGoCiAgICBoYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgICgwLCBfaW5mby5saXN0SW5mbykodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMudXNlckxpc3QgPSByZXMucm93czsKICAgICAgICBfdGhpcy50b3RhbCA9IHJlcy50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdXBwbHlDb2RlID0gJyc7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDmlrDlop4KICAgIGhhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gJ+aWsOWinuebuOWFs+aWueS6uuWRmCc7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8vIOe8lui+kQogICAgaGFuZGxlRWRpdDogZnVuY3Rpb24gaGFuZGxlRWRpdChyb3cpIHsKICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICfnvJbovpHnm7jlhbPmlrnkurrlkZgnOwogICAgICB0aGlzLmZvcm0gPSBPYmplY3QuYXNzaWduKHt9LCByb3cpOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8vIOWIoOmZpAogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTor6XmnaHmlbDmja7lkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAoMCwgX2luZm8uZGVsSW5mbykocm93LmlkKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsKICAgICAgICAgIF90aGlzMi5oYW5kbGVRdWVyeSgpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmj5DkuqTkuLvooagKICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICBpZiAodGhpcy5mb3JtLmlkKSB7CiAgICAgICAgKDAsIF9pbmZvLnVwZGF0ZUluZm8pKHRoaXMuZm9ybSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpczMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+u5pS55oiQ5YqfJyk7CiAgICAgICAgICBfdGhpczMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgX3RoaXMzLmhhbmRsZVF1ZXJ5KCk7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgKDAsIF9pbmZvLmFkZEluZm8pKHRoaXMuZm9ybSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpczMuJG1lc3NhZ2Uuc3VjY2Vzcygn5paw5aKe5oiQ5YqfJyk7CiAgICAgICAgICBfdGhpczMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgX3RoaXMzLmhhbmRsZVF1ZXJ5KCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvLyDlr7zlh7oKICAgIGhhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KCkgewogICAgICAoMCwgX2luZm8uZXhwb3J0SW5mbykodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgdmFyIGJsb2IgPSBuZXcgQmxvYihbcmVzXSwgewogICAgICAgICAgdHlwZTogJ2FwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbCcKICAgICAgICB9KTsKICAgICAgICB2YXIgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7CiAgICAgICAgdmFyIGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgbGluay5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnOwogICAgICAgIGxpbmsuaHJlZiA9IHVybDsKICAgICAgICBsaW5rLnNldEF0dHJpYnV0ZSgnZG93bmxvYWQnLCAn55u45YWz5pa55Lq65ZGY5pWw5o2uLnhsc3gnKTsKICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspOwogICAgICAgIGxpbmsuY2xpY2soKTsKICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGxpbmspOwogICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKHVybCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWvvOWFpQogICAgaGFuZGxlSW1wb3J0OiBmdW5jdGlvbiBoYW5kbGVJbXBvcnQoKSB7CiAgICAgIHRoaXMuaW1wb3J0RGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlSW1wb3J0U3VjY2VzczogZnVuY3Rpb24gaGFuZGxlSW1wb3J0U3VjY2VzcyhyZXNwb25zZSkgewogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflr7zlhaXmiJDlip8nKTsKICAgICAgICB0aGlzLmltcG9ydERpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+WvvOWFpeWksei0pScpOwogICAgICB9CiAgICB9LAogICAgLy8g5a+85YWl5YmN5qOA5p+l5paH5Lu257G75Z6LCiAgICBiZWZvcmVJbXBvcnRVcGxvYWQ6IGZ1bmN0aW9uIGJlZm9yZUltcG9ydFVwbG9hZChmaWxlKSB7CiAgICAgIHZhciBpc0V4Y2VsID0gZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJyB8fCBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCc7CiAgICAgIGlmICghaXNFeGNlbCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oEV4Y2Vs5paH5Lu277yBJyk7CiAgICAgIH0KICAgICAgcmV0dXJuIGlzRXhjZWw7CiAgICB9LAogICAgLy8g6ZmE5Lu25LiK5Lyg5YmN5qOA5p+l5paH5Lu257G75Z6LCiAgICBiZWZvcmVGaWxlVXBsb2FkOiBmdW5jdGlvbiBiZWZvcmVGaWxlVXBsb2FkKGZpbGUpIHsKICAgICAgLy8g5qOA5p+l5paH5Lu257G75Z6L5piv5ZCm5Li6UERGCiAgICAgIHZhciBpc1BERiA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3BkZicgfHwgZmlsZS5uYW1lLnRvTG93ZXJDYXNlKCkuZW5kc1dpdGgoJy5wZGYnKTsKICAgICAgaWYgKCFpc1BERikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oFBERuagvOW8j+aWh+S7tu+8gScpOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQoKICAgICAgLy8g5qOA5p+l5paH5Lu25aSn5bCP6ZmQ5Yi2CiAgICAgIHZhciBtYXhTaXplID0gNTAgKiAxMDI0ICogMTAyNDsgLy8gNTBNQgogICAgICBpZiAoZmlsZS5zaXplID4gbWF4U2l6ZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzUwTULvvIEnKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIHRydWU7CiAgICB9LAogICAgLy8g5bKX5L2N6K+G5Yir5Y2hCiAgICBvcGVuRmFjRGlhbG9nOiBmdW5jdGlvbiBvcGVuRmFjRGlhbG9nKHJvdykgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgKDAsIF9mYWMuZ2V0RmFjKShyb3cuaWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNC5mYWNGb3JtID0gcmVzLmRhdGEgfHwgewogICAgICAgICAgdXNlcklkOiByb3cuaWQKICAgICAgICB9OwogICAgICAgIF90aGlzNC5mYWNEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgfSk7CiAgICB9LAogICAgc3VibWl0RmFjOiBmdW5jdGlvbiBzdWJtaXRGYWMoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgYXBpID0gdGhpcy5mYWNGb3JtLmlkID8gX2ZhYy51cGRhdGVGYWMgOiBfZmFjLmFkZEZhYzsKICAgICAgYXBpKHRoaXMuZmFjRm9ybSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpOwogICAgICAgIF90aGlzNS5mYWNEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgX3RoaXM1LmhhbmRsZVF1ZXJ5KCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWBpeW6t+S/oeaBrwogICAgb3BlbkhlYWx0aERpYWxvZzogZnVuY3Rpb24gb3BlbkhlYWx0aERpYWxvZyhyb3cpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgICgwLCBfaGVhbHRoLmdldEhlYWx0aCkocm93LmlkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczYuaGVhbHRoRm9ybSA9IHJlcy5kYXRhIHx8IHsKICAgICAgICAgIHVzZXJpZDogcm93LmlkCiAgICAgICAgfTsKICAgICAgICBfdGhpczYuaGVhbHRoRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIHN1Ym1pdEhlYWx0aDogZnVuY3Rpb24gc3VibWl0SGVhbHRoKCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgdmFyIGFwaSA9IHRoaXMuaGVhbHRoRm9ybS5pZCA/IF9oZWFsdGgudXBkYXRlSGVhbHRoIDogX2hlYWx0aC5hZGRIZWFsdGg7CiAgICAgIGFwaSh0aGlzLmhlYWx0aEZvcm0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjmiJDlip8nKTsKICAgICAgICBfdGhpczcuaGVhbHRoRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIF90aGlzNy5oYW5kbGVRdWVyeSgpOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDpmYTku7bnrqHnkIYKICAgIG9wZW5GaWxlRGlhbG9nOiBmdW5jdGlvbiBvcGVuRmlsZURpYWxvZyhyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50VXNlcklkID0gcm93LmlkOwogICAgICB0aGlzLmN1cnJlbnRVc2VySW5mbyA9IHJvdzsgLy8g5L+d5a2Y5b2T5YmN55So5oi35L+h5oGvCiAgICAgIHRoaXMuZ2V0RmlsZUxpc3Qocm93LmlkKTsKICAgICAgdGhpcy5maWxlRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgZ2V0RmlsZUxpc3Q6IGZ1bmN0aW9uIGdldEZpbGVMaXN0KHVzZXJpZCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgKDAsIF9maWxlLmxpc3RGaWxlKSh7CiAgICAgICAgdXNlcmlkOiB1c2VyaWQKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM4LmZpbGVMaXN0ID0gcmVzLnJvd3M7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUZpbGVVcGxvYWRTdWNjZXNzOiBmdW5jdGlvbiBoYW5kbGVGaWxlVXBsb2FkU3VjY2VzcyhyZXNwb25zZSkgewogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofku7bkuIrkvKDmiJDlip8nKTsKICAgICAgICB0aGlzLmdldEZpbGVMaXN0KHRoaXMuY3VycmVudFVzZXJJZCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWh+S7tuS4iuS8oOWksei0pScpOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlRmlsZVVwbG9hZEVycm9yOiBmdW5jdGlvbiBoYW5kbGVGaWxlVXBsb2FkRXJyb3IoZXJyKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pTogJyArIChlcnIubWVzc2FnZSB8fCAn5pyq55+l6ZSZ6K+vJykpOwogICAgfSwKICAgIGRlbGV0ZUZpbGU6IGZ1bmN0aW9uIGRlbGV0ZUZpbGUocm93KSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTor6XpmYTku7blkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAoMCwgX2ZpbGUuZGVsRmlsZSkocm93LmlkKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzOS4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsKICAgICAgICAgIF90aGlzOS5nZXRGaWxlTGlzdChfdGhpczkuY3VycmVudFVzZXJJZCk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGRvd25sb2FkRmlsZTogZnVuY3Rpb24gZG93bmxvYWRGaWxlKHJvdykgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgLy8g6LCD55So5LiL6L295o6l5Y+j6I635Y+W5paH5Lu2VVJMCiAgICAgIF9yZXF1ZXN0LmRlZmF1bHQuZ2V0KCIvd2ViL3N1cHBseS91c2VyZmlsZS9kb3dubG9hZC8iLmNvbmNhdChyb3cuaWQpKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIC8vIOiOt+WPluWIsOaWh+S7tlVSTOWQju+8jOWcqOaWsOeql+WPo+S4reaJk+W8gOS4i+i9vQogICAgICAgICAgdmFyIGZpbGVVcmwgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgd2luZG93Lm9wZW4oZmlsZVVybCwgJ19ibGFuaycpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczAuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfkuIvovb3lpLHotKUnKTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIF90aGlzMC4kbWVzc2FnZS5lcnJvcign5LiL6L295aSx6LSlOiAnICsgZXJyb3IubWVzc2FnZSk7CiAgICAgIH0pOwogICAgfSwKICAgIHRhYmxlUm93Q2xhc3NOYW1lOiBmdW5jdGlvbiB0YWJsZVJvd0NsYXNzTmFtZShfcmVmKSB7CiAgICAgIHZhciByb3cgPSBfcmVmLnJvdywKICAgICAgICByb3dJbmRleCA9IF9yZWYucm93SW5kZXg7CiAgICAgIGlmIChyb3cuc3RhdGUgPT09IDEpIHsKICAgICAgICByZXR1cm4gJ3N1Y2Nlc3Mtcm93JzsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ2Rhbmdlci1yb3cnOwogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogIH0KfTs="}, {"version": 3, "names": ["_info", "require", "_fac", "_health", "_file", "_request", "_interopRequireDefault", "_auth", "name", "data", "queryParams", "pageNum", "pageSize", "supplyCode", "userList", "total", "facDialogVisible", "facForm", "facFormItems", "field", "title", "span", "itemRender", "props", "placeholder", "rows", "type", "options", "label", "value", "healthDialogVisible", "healthForm", "healthFormItems", "fileDialogVisible", "fileList", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "currentUserId", "currentUserInfo", "upload", "headers", "Authorization", "getToken", "dialogVisible", "dialogTitle", "form", "importDialogVisible", "importUrl", "computed", "uploadData", "userid", "usercode", "userCode", "username", "userName", "supplycode", "supplyname", "supplyName", "idcard", "userdeptname", "userDeptName", "methods", "handleQuery", "_this", "listInfo", "then", "res", "reset<PERSON><PERSON>y", "handleAdd", "handleEdit", "row", "Object", "assign", "handleDelete", "_this2", "$confirm", "delInfo", "id", "$message", "success", "submitForm", "_this3", "updateInfo", "addInfo", "handleExport", "exportInfo", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleImport", "handleImportSuccess", "response", "code", "error", "msg", "beforeImportUpload", "file", "isExcel", "beforeFileUpload", "isPDF", "toLowerCase", "endsWith", "maxSize", "size", "openFacDialog", "_this4", "getFac", "userId", "submitFac", "_this5", "api", "updateFac", "addFac", "openHealthDialog", "_this6", "getHealth", "submitHealth", "_this7", "updateHealth", "addHealth", "openFileDialog", "getFileList", "_this8", "listFile", "handleFileUploadSuccess", "handleFileUploadError", "err", "message", "deleteFile", "_this9", "delFile", "downloadFile", "_this0", "request", "get", "concat", "fileUrl", "open", "catch", "tableRowClassName", "_ref", "rowIndex", "state", "mounted"], "sources": ["src/views/supply/info/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 查询表单 -->\r\n    <el-form :inline=\"true\" :model=\"queryParams\" class=\"demo-form-inline\" @submit.native.prevent>\r\n      <el-form-item label=\"供应商代码\">\r\n        <el-input v-model=\"queryParams.supplyCode\" placeholder=\"请输入供应商代码\" clearable />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">查询</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 工具栏 -->\r\n    <div style=\"margin-bottom: 10px;\">\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n      <el-button type=\"success\" icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\r\n      <el-button type=\"warning\" icon=\"el-icon-download\" @click=\"handleExport\">导出</el-button>\r\n    </div>\r\n\r\n    <!-- 用户列表 -->\r\n    <el-table :data=\"userList\" border stripe style=\"width: 100%\">\r\n      <el-table-column prop=\"id\" label=\"ID\" width=\"60\" />\r\n      <el-table-column prop=\"supplyCode\" label=\"供应商代码\" />\r\n      <el-table-column prop=\"userName\" label=\"用户姓名\" />\r\n      <el-table-column prop=\"idcard\" label=\"身份证\" />\r\n      <el-table-column label=\"岗位识别卡\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFacDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"健康信息\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openHealthDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"附件\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFileDialog(scope.row)\">管理</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"operation-buttons\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadFile(scope.row)\"\r\n            >\r\n              下载\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"deleteFile(scope.row)\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <el-pagination\r\n      style=\"margin-top: 10px;\"\r\n      background\r\n      layout=\"total, prev, pager, next, jumper\"\r\n      :total=\"total\"\r\n      :page-size=\"queryParams.pageSize\"\r\n      :current-page.sync=\"queryParams.pageNum\"\r\n      @current-change=\"handleQuery\"\r\n    />\r\n\r\n    <!-- 新增/编辑主表弹窗 -->\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <el-form :model=\"form\" label-width=\"100px\">\r\n        <el-form-item label=\"供应商代码\">\r\n          <el-input v-model=\"form.supplyCode\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"供应商名称\">\r\n          <el-input v-model=\"form.supplyName\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户编号\">\r\n          <el-input v-model=\"form.userCode\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户姓名\">\r\n          <el-input v-model=\"form.userName\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证\">\r\n          <el-input v-model=\"form.idcard\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-select v-model=\"form.state\" placeholder=\"请选择\">\r\n            <el-option label=\"正常\" :value=\"1\" />\r\n            <el-option label=\"删除\" :value=\"101\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导入弹窗 -->\r\n    <el-dialog title=\"导入相关方人员\" :visible.sync=\"importDialogVisible\">\r\n      <el-upload\r\n        :action=\"importUrl\"\r\n        :show-file-list=\"false\"\r\n        :on-success=\"handleImportSuccess\"\r\n        :before-upload=\"beforeImportUpload\"\r\n        :headers=\"uploadHeaders\"\r\n      >\r\n        <el-button type=\"primary\">选择文件上传</el-button>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 岗位识别卡弹窗 -->\r\n    <vxe-modal v-model=\"facDialogVisible\" title=\"岗位识别卡\" width=\"700\" show-footer>\r\n      <vxe-form\r\n        :data=\"facForm\"\r\n        :items=\"facFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"facDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitFac\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 健康信息弹窗 -->\r\n    <vxe-modal v-model=\"healthDialogVisible\" title=\"健康信息\" width=\"800\" show-footer>\r\n      <vxe-form\r\n        :data=\"healthForm\"\r\n        :items=\"healthFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"healthDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitHealth\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 附件管理弹窗 -->\r\n    <el-dialog \r\n      :visible.sync=\"fileDialogVisible\" \r\n      title=\"附件管理\" \r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n    >\r\n      <!-- 上传区域 -->\r\n      <div class=\"upload-section\">\r\n        <div class=\"upload-header\">\r\n          <i class=\"el-icon-upload\"></i>\r\n          <span class=\"upload-title\">文件上传</span>\r\n        </div>\r\n        <div class=\"upload-content\">\r\n          <el-upload\r\n            ref=\"fileUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"upload.headers\"\r\n            :data=\"uploadData\"\r\n            :on-success=\"handleFileUploadSuccess\"\r\n            :on-error=\"handleFileUploadError\"\r\n            :before-upload=\"beforeFileUpload\"\r\n            :show-file-list=\"false\"\r\n            accept=\".pdf\"\r\n            drag\r\n            class=\"upload-dragger\"\r\n          >\r\n            <div class=\"upload-area\">\r\n              <i class=\"el-icon-upload upload-icon\"></i>\r\n              <div class=\"upload-text\">\r\n                <span class=\"upload-main-text\">将PDF文件拖到此处，或</span>\r\n                <em class=\"upload-click-text\">点击上传</em>\r\n              </div>\r\n              <div class=\"upload-tip\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>仅支持PDF格式文件，单个文件不超过50MB</span>\r\n              </div>\r\n              <div class=\"upload-limits\">\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span>文件格式：PDF</span>\r\n                </div>\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-files\"></i>\r\n                  <span>文件大小：≤ 50MB</span>\r\n                </div>\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>支持拖拽上传</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 文件列表 -->\r\n      <div class=\"file-list-section\">\r\n        <div class=\"file-list-header\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span class=\"file-list-title\">已上传文件</span>\r\n          <span class=\"file-count\">(共 {{fileList.length}} 个文件)</span>\r\n        </div>\r\n        <div class=\"file-list-content\">\r\n          <el-table \r\n            :data=\"fileList\" \r\n            style=\"width: 100%\"\r\n            :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n            :row-class-name=\"tableRowClassName\"\r\n          >\r\n            <el-table-column prop=\"filename\" label=\"文件名\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"file-info\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span class=\"file-name\">{{scope.row.filename}}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"format\" label=\"格式\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{scope.row.format}}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"state\" label=\"状态\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag \r\n                  size=\"mini\" \r\n                  :type=\"scope.row.state === 1 ? 'success' : 'danger'\"\r\n                >\r\n                  {{scope.row.state === 1 ? '正常' : '异常'}}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"operation-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-download\"\r\n                    @click=\"downloadFile(scope.row)\"\r\n                  >\r\n                    下载\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"deleteFile(scope.row)\"\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 弹窗底部 -->\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"fileDialogVisible = false\" icon=\"el-icon-close\">关闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listInfo, addInfo, updateInfo, delInfo, exportInfo } from '@/api/supply/info'\r\nimport { getFac, addFac, updateFac } from '@/api/supply/fac'\r\nimport { getHealth, addHealth, updateHealth } from '@/api/supply/health'\r\nimport { listFile, delFile } from '@/api/supply/file'\r\nimport request from '@/utils/request'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'SupplyUserInfo',\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        supplyCode: ''\r\n      },\r\n      userList: [],\r\n      total: 0,\r\n      // 岗位识别卡\r\n      facDialogVisible: false,\r\n      facForm: {},\r\n      facFormItems: [\r\n        { field: 'userPost', title: '岗位名称', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入岗位名称', rows: 2 } } },\r\n        { field: 'userFacClass', title: '岗位班组', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位班组' } } },\r\n        { field: 'userDeptName', title: '所属部门', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入所属部门' } } },\r\n        { field: 'userFacWork', title: '岗位描述', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位描述' } } },\r\n        { field: 'userTimeBegin', title: '入厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'userTimeEnd', title: '离厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 24,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '起草', value: 0 },\r\n              { label: '分厂审核人', value: 1 },\r\n              { label: '人力资源部', value: 2 },\r\n              { label: '退回', value: -1 },\r\n              { label: '禁用', value: 101 },\r\n              { label: '审核通过', value: 99 },\r\n              { label: '删除', value: 102 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 健康信息\r\n      healthDialogVisible: false,\r\n      healthForm: {},\r\n      healthFormItems: [\r\n        { field: 'healdate', title: '体检日期', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'hos', title: '医院', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入医院' } } },\r\n        { field: 'healtz', title: '体重', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重' } } },\r\n        { field: 'healtzzs', title: '体重指数', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重指数' } } },\r\n        { field: 'healptt', title: '血糖', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入血糖' } } },\r\n        { field: 'healssy', title: '收缩压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入收缩压' } } },\r\n        { field: 'healszy', title: '舒张压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入舒张压' } } },\r\n        { field: 'healzdgc', title: '总胆固醇', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入总胆固醇' } } },\r\n        { field: 'healgysz', title: '甘油三酯', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入甘油三酯' } } },\r\n        { field: 'healga', title: '谷氨酰转肽酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷氨酰转肽酶' } } },\r\n        { field: 'healgb', title: '谷丙转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷丙转氨酶' } } },\r\n        { field: 'healgc', title: '谷草转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷草转氨酶' } } },\r\n        { field: 'healnsd', title: '尿素氮', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入尿素氮' } } },\r\n        { field: 'healjg', title: '肌酐', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入肌酐' } } },\r\n        { field: 'healxd', title: '心电图', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入心电图' } } },\r\n        { field: 'healxj', title: '小结', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入小结', rows: 2 } } },\r\n        { field: 'healjy', title: '建议', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入建议', rows: 2 } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 12,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '正常', value: 1 },\r\n              { label: '删除', value: 101 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 附件\r\n      fileDialogVisible: false,\r\n      fileList: [],\r\n      uploadUrl: process.env.VUE_APP_BASE_API + '/web/supply/userfile/upload', // 新的 SFTP 上传接口\r\n      currentUserId: null,\r\n      currentUserInfo: {}, // 新增：保存当前用户信息\r\n      // 上传配置\r\n      upload: {\r\n        headers: { Authorization: 'Bearer ' + getToken() }\r\n      },\r\n      // 新增/编辑主表\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      form: {},\r\n      importDialogVisible: false,\r\n      importUrl: '/web/supply/userinfo/import'\r\n    }\r\n  },\r\n  computed: {\r\n    uploadData() {\r\n      return {\r\n        userid: this.currentUserId,\r\n        usercode: this.currentUserInfo.userCode,\r\n        username: this.currentUserInfo.userName,\r\n        supplycode: this.currentUserInfo.supplyCode,\r\n        supplyname: this.currentUserInfo.supplyName,\r\n        idcard: this.currentUserInfo.idcard,\r\n        userdeptname: this.currentUserInfo.userDeptName\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 查询用户列表\r\n    handleQuery() {\r\n      listInfo(this.queryParams).then(res => {\r\n        this.userList = res.rows\r\n        this.total = res.total\r\n      })\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.supplyCode = ''\r\n      this.handleQuery()\r\n    },\r\n    // 新增\r\n    handleAdd() {\r\n      this.dialogTitle = '新增相关方人员'\r\n      this.form = {}\r\n      this.dialogVisible = true\r\n    },\r\n    // 编辑\r\n    handleEdit(row) {\r\n      this.dialogTitle = '编辑相关方人员'\r\n      this.form = Object.assign({}, row)\r\n      this.dialogVisible = true\r\n    },\r\n    // 删除\r\n    handleDelete(row) {\r\n      this.$confirm('确定删除该条数据吗？', '提示', { type: 'warning' }).then(() => {\r\n        delInfo(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.handleQuery()\r\n        })\r\n      })\r\n    },\r\n    // 提交主表\r\n    submitForm() {\r\n      if (this.form.id) {\r\n        updateInfo(this.form).then(() => {\r\n          this.$message.success('修改成功')\r\n          this.dialogVisible = false\r\n          this.handleQuery()\r\n        })\r\n      } else {\r\n        addInfo(this.form).then(() => {\r\n          this.$message.success('新增成功')\r\n          this.dialogVisible = false\r\n          this.handleQuery()\r\n        })\r\n      }\r\n    },\r\n    // 导出\r\n    handleExport() {\r\n      exportInfo(this.queryParams).then(res => {\r\n        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })\r\n        const url = window.URL.createObjectURL(blob)\r\n        const link = document.createElement('a')\r\n        link.style.display = 'none'\r\n        link.href = url\r\n        link.setAttribute('download', '相关方人员数据.xlsx')\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        window.URL.revokeObjectURL(url)\r\n      })\r\n    },\r\n    // 导入\r\n    handleImport() {\r\n      this.importDialogVisible = true\r\n    },\r\n    handleImportSuccess(response) {\r\n      if (response.code === 200) {\r\n        this.$message.success('导入成功')\r\n        this.importDialogVisible = false\r\n        this.handleQuery()\r\n      } else {\r\n        this.$message.error(response.msg || '导入失败')\r\n      }\r\n    },\r\n    // 导入前检查文件类型\r\n    beforeImportUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件！')\r\n      }\r\n      return isExcel\r\n    },\r\n    // 附件上传前检查文件类型\r\n    beforeFileUpload(file) {\r\n      // 检查文件类型是否为PDF\r\n      const isPDF = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')\r\n      if (!isPDF) {\r\n        this.$message.error('只能上传PDF格式文件！')\r\n        return false\r\n      }\r\n      \r\n      // 检查文件大小限制\r\n      const maxSize = 50 * 1024 * 1024 // 50MB\r\n      if (file.size > maxSize) {\r\n        this.$message.error('文件大小不能超过50MB！')\r\n        return false\r\n      }\r\n      \r\n      return true\r\n    },\r\n    // 岗位识别卡\r\n    openFacDialog(row) {\r\n      getFac(row.id).then(res => {\r\n        this.facForm = res.data || { userId: row.id }\r\n        this.facDialogVisible = true\r\n      })\r\n    },\r\n    submitFac() {\r\n      const api = this.facForm.id ? updateFac : addFac\r\n      api(this.facForm).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.facDialogVisible = false\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    // 健康信息\r\n    openHealthDialog(row) {\r\n      getHealth(row.id).then(res => {\r\n        this.healthForm = res.data || { userid: row.id }\r\n        this.healthDialogVisible = true\r\n      })\r\n    },\r\n    submitHealth() {\r\n      const api = this.healthForm.id ? updateHealth : addHealth\r\n      api(this.healthForm).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.healthDialogVisible = false\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    // 附件管理\r\n    openFileDialog(row) {\r\n      this.currentUserId = row.id\r\n      this.currentUserInfo = row // 保存当前用户信息\r\n      this.getFileList(row.id)\r\n      this.fileDialogVisible = true\r\n    },\r\n    getFileList(userid) {\r\n      listFile({ userid }).then(res => {\r\n        this.fileList = res.rows\r\n      })\r\n    },\r\n    handleFileUploadSuccess(response) {\r\n      if (response.code === 200) {\r\n        this.$message.success('文件上传成功')\r\n        this.getFileList(this.currentUserId)\r\n      } else {\r\n        this.$message.error(response.msg || '文件上传失败')\r\n      }\r\n    },\r\n    handleFileUploadError(err) {\r\n      this.$message.error('文件上传失败: ' + (err.message || '未知错误'))\r\n    },\r\n    deleteFile(row) {\r\n      this.$confirm('确定删除该附件吗？', '提示', { type: 'warning' }).then(() => {\r\n        delFile(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getFileList(this.currentUserId)\r\n        })\r\n      })\r\n    },\r\n    downloadFile(row) {\r\n      // 调用下载接口获取文件URL\r\n      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          // 获取到文件URL后，在新窗口中打开下载\r\n          const fileUrl = response.data\r\n          window.open(fileUrl, '_blank')\r\n        } else {\r\n          this.$message.error(response.msg || '下载失败')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('下载失败: ' + error.message)\r\n      })\r\n    },\r\n    tableRowClassName({ row, rowIndex }) {\r\n      if (row.state === 1) {\r\n        return 'success-row'\r\n      } else {\r\n        return 'danger-row'\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleQuery()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.upload-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 25px;\r\n  border-radius: 8px;\r\n  margin-bottom: 25px;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.upload-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  color: #fff;\r\n}\r\n\r\n.upload-header i {\r\n  font-size: 20px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.upload-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.upload-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 180px;\r\n  border: 2px dashed rgba(255, 255, 255, 0.3);\r\n  border-radius: 8px;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.upload-content:hover {\r\n  border-color: rgba(255, 255, 255, 0.6);\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.upload-dragger {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 48px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.upload-text {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.upload-main-text {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 16px;\r\n}\r\n\r\n.upload-click-text {\r\n  color: #fff;\r\n  font-style: normal;\r\n  font-weight: 600;\r\n  text-decoration: underline;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 14px;\r\n}\r\n\r\n.upload-tip i {\r\n  margin-right: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.upload-limits {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  width: 100%;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 13px;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.limit-item {\r\n  display: flex;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 8px 12px;\r\n  border-radius: 20px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.limit-item:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.limit-item i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.file-list-section {\r\n  background-color: #f5f7fa;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.file-list-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  color: #606266;\r\n}\r\n\r\n.file-list-title {\r\n  margin-left: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-count {\r\n  margin-left: 10px;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.file-list-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  padding: 10px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-name {\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 150px;\r\n}\r\n\r\n.el-table .success-row {\r\n  background-color: #f0f9eb;\r\n}\r\n\r\n.el-table .danger-row {\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.operation-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.operation-buttons .el-button {\r\n  margin: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AA8RA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,KAAA;MACA;MACAC,gBAAA;MACAC,OAAA;MACAC,YAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;YAAAC,IAAA;UAAA;QAAA;MAAA,GACA;QAAAN,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAG,IAAA;YAAAF,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAG,IAAA;YAAAF,WAAA;UAAA;QAAA;MAAA,GACA;QACAL,KAAA;QACAC,KAAA;QACAC,IAAA;QACAC,UAAA;UACAd,IAAA;UACAmB,OAAA,GACA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,EACA;UACAN,KAAA;YAAAC,WAAA;UAAA;QACA;MACA,EACA;MACA;MACAM,mBAAA;MACAC,UAAA;MACAC,eAAA,GACA;QAAAb,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAG,IAAA;YAAAF,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;UAAA;QAAA;MAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;YAAAC,IAAA;UAAA;QAAA;MAAA,GACA;QAAAN,KAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;UAAAd,IAAA;UAAAe,KAAA;YAAAC,WAAA;YAAAC,IAAA;UAAA;QAAA;MAAA,GACA;QACAN,KAAA;QACAC,KAAA;QACAC,IAAA;QACAC,UAAA;UACAd,IAAA;UACAmB,OAAA,GACA;YAAAC,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,EACA;UACAN,KAAA;YAAAC,WAAA;UAAA;QACA;MACA,EACA;MACA;MACAS,iBAAA;MACAC,QAAA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,aAAA;MACAC,eAAA;MAAA;MACA;MACAC,MAAA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;MACA;MACA;MACAC,aAAA;MACAC,WAAA;MACAC,IAAA;MACAC,mBAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA;QACAC,MAAA,OAAAb,aAAA;QACAc,QAAA,OAAAb,eAAA,CAAAc,QAAA;QACAC,QAAA,OAAAf,eAAA,CAAAgB,QAAA;QACAC,UAAA,OAAAjB,eAAA,CAAA3B,UAAA;QACA6C,UAAA,OAAAlB,eAAA,CAAAmB,UAAA;QACAC,MAAA,OAAApB,eAAA,CAAAoB,MAAA;QACAC,YAAA,OAAArB,eAAA,CAAAsB;MACA;IACA;EACA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,cAAA,OAAAxD,WAAA,EAAAyD,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAnD,QAAA,GAAAsD,GAAA,CAAA3C,IAAA;QACAwC,KAAA,CAAAlD,KAAA,GAAAqD,GAAA,CAAArD,KAAA;MACA;IACA;IACAsD,UAAA,WAAAA,WAAA;MACA,KAAA3D,WAAA,CAAAG,UAAA;MACA,KAAAmD,WAAA;IACA;IACA;IACAM,SAAA,WAAAA,UAAA;MACA,KAAAxB,WAAA;MACA,KAAAC,IAAA;MACA,KAAAF,aAAA;IACA;IACA;IACA0B,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAA1B,WAAA;MACA,KAAAC,IAAA,GAAA0B,MAAA,CAAAC,MAAA,KAAAF,GAAA;MACA,KAAA3B,aAAA;IACA;IACA;IACA8B,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,KAAAC,QAAA;QAAAnD,IAAA;MAAA,GAAAyC,IAAA;QACA,IAAAW,aAAA,EAAAN,GAAA,CAAAO,EAAA,EAAAZ,IAAA;UACAS,MAAA,CAAAI,QAAA,CAAAC,OAAA;UACAL,MAAA,CAAAZ,WAAA;QACA;MACA;IACA;IACA;IACAkB,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAApC,IAAA,CAAAgC,EAAA;QACA,IAAAK,gBAAA,OAAArC,IAAA,EAAAoB,IAAA;UACAgB,MAAA,CAAAH,QAAA,CAAAC,OAAA;UACAE,MAAA,CAAAtC,aAAA;UACAsC,MAAA,CAAAnB,WAAA;QACA;MACA;QACA,IAAAqB,aAAA,OAAAtC,IAAA,EAAAoB,IAAA;UACAgB,MAAA,CAAAH,QAAA,CAAAC,OAAA;UACAE,MAAA,CAAAtC,aAAA;UACAsC,MAAA,CAAAnB,WAAA;QACA;MACA;IACA;IACA;IACAsB,YAAA,WAAAA,aAAA;MACA,IAAAC,gBAAA,OAAA7E,WAAA,EAAAyD,IAAA,WAAAC,GAAA;QACA,IAAAoB,IAAA,OAAAC,IAAA,EAAArB,GAAA;UAAA1C,IAAA;QAAA;QACA,IAAAgE,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,KAAA,CAAAC,OAAA;QACAJ,IAAA,CAAAK,IAAA,GAAAT,GAAA;QACAI,IAAA,CAAAM,YAAA;QACAL,QAAA,CAAAM,IAAA,CAAAC,WAAA,CAAAR,IAAA;QACAA,IAAA,CAAAS,KAAA;QACAR,QAAA,CAAAM,IAAA,CAAAG,WAAA,CAAAV,IAAA;QACAH,MAAA,CAAAC,GAAA,CAAAa,eAAA,CAAAf,GAAA;MACA;IACA;IACA;IACAgB,YAAA,WAAAA,aAAA;MACA,KAAA1D,mBAAA;IACA;IACA2D,mBAAA,WAAAA,oBAAAC,QAAA;MACA,IAAAA,QAAA,CAAAC,IAAA;QACA,KAAA7B,QAAA,CAAAC,OAAA;QACA,KAAAjC,mBAAA;QACA,KAAAgB,WAAA;MACA;QACA,KAAAgB,QAAA,CAAA8B,KAAA,CAAAF,QAAA,CAAAG,GAAA;MACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,OAAA,GAAAD,IAAA,CAAAvF,IAAA,mCAAAuF,IAAA,CAAAvF,IAAA;MACA,KAAAwF,OAAA;QACA,KAAAlC,QAAA,CAAA8B,KAAA;MACA;MACA,OAAAI,OAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAF,IAAA;MACA;MACA,IAAAG,KAAA,GAAAH,IAAA,CAAAvF,IAAA,0BAAAuF,IAAA,CAAAzG,IAAA,CAAA6G,WAAA,GAAAC,QAAA;MACA,KAAAF,KAAA;QACA,KAAApC,QAAA,CAAA8B,KAAA;QACA;MACA;;MAEA;MACA,IAAAS,OAAA;MACA,IAAAN,IAAA,CAAAO,IAAA,GAAAD,OAAA;QACA,KAAAvC,QAAA,CAAA8B,KAAA;QACA;MACA;MAEA;IACA;IACA;IACAW,aAAA,WAAAA,cAAAjD,GAAA;MAAA,IAAAkD,MAAA;MACA,IAAAC,WAAA,EAAAnD,GAAA,CAAAO,EAAA,EAAAZ,IAAA,WAAAC,GAAA;QACAsD,MAAA,CAAAzG,OAAA,GAAAmD,GAAA,CAAA3D,IAAA;UAAAmH,MAAA,EAAApD,GAAA,CAAAO;QAAA;QACA2C,MAAA,CAAA1G,gBAAA;MACA;IACA;IACA6G,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,GAAA,QAAA9G,OAAA,CAAA8D,EAAA,GAAAiD,cAAA,GAAAC,WAAA;MACAF,GAAA,MAAA9G,OAAA,EAAAkD,IAAA;QACA2D,MAAA,CAAA9C,QAAA,CAAAC,OAAA;QACA6C,MAAA,CAAA9G,gBAAA;QACA8G,MAAA,CAAA9D,WAAA;MACA;IACA;IACA;IACAkE,gBAAA,WAAAA,iBAAA1D,GAAA;MAAA,IAAA2D,MAAA;MACA,IAAAC,iBAAA,EAAA5D,GAAA,CAAAO,EAAA,EAAAZ,IAAA,WAAAC,GAAA;QACA+D,MAAA,CAAApG,UAAA,GAAAqC,GAAA,CAAA3D,IAAA;UAAA2C,MAAA,EAAAoB,GAAA,CAAAO;QAAA;QACAoD,MAAA,CAAArG,mBAAA;MACA;IACA;IACAuG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAP,GAAA,QAAAhG,UAAA,CAAAgD,EAAA,GAAAwD,oBAAA,GAAAC,iBAAA;MACAT,GAAA,MAAAhG,UAAA,EAAAoC,IAAA;QACAmE,MAAA,CAAAtD,QAAA,CAAAC,OAAA;QACAqD,MAAA,CAAAxG,mBAAA;QACAwG,MAAA,CAAAtE,WAAA;MACA;IACA;IACA;IACAyE,cAAA,WAAAA,eAAAjE,GAAA;MACA,KAAAjC,aAAA,GAAAiC,GAAA,CAAAO,EAAA;MACA,KAAAvC,eAAA,GAAAgC,GAAA;MACA,KAAAkE,WAAA,CAAAlE,GAAA,CAAAO,EAAA;MACA,KAAA9C,iBAAA;IACA;IACAyG,WAAA,WAAAA,YAAAtF,MAAA;MAAA,IAAAuF,MAAA;MACA,IAAAC,cAAA;QAAAxF,MAAA,EAAAA;MAAA,GAAAe,IAAA,WAAAC,GAAA;QACAuE,MAAA,CAAAzG,QAAA,GAAAkC,GAAA,CAAA3C,IAAA;MACA;IACA;IACAoH,uBAAA,WAAAA,wBAAAjC,QAAA;MACA,IAAAA,QAAA,CAAAC,IAAA;QACA,KAAA7B,QAAA,CAAAC,OAAA;QACA,KAAAyD,WAAA,MAAAnG,aAAA;MACA;QACA,KAAAyC,QAAA,CAAA8B,KAAA,CAAAF,QAAA,CAAAG,GAAA;MACA;IACA;IACA+B,qBAAA,WAAAA,sBAAAC,GAAA;MACA,KAAA/D,QAAA,CAAA8B,KAAA,eAAAiC,GAAA,CAAAC,OAAA;IACA;IACAC,UAAA,WAAAA,WAAAzE,GAAA;MAAA,IAAA0E,MAAA;MACA,KAAArE,QAAA;QAAAnD,IAAA;MAAA,GAAAyC,IAAA;QACA,IAAAgF,aAAA,EAAA3E,GAAA,CAAAO,EAAA,EAAAZ,IAAA;UACA+E,MAAA,CAAAlE,QAAA,CAAAC,OAAA;UACAiE,MAAA,CAAAR,WAAA,CAAAQ,MAAA,CAAA3G,aAAA;QACA;MACA;IACA;IACA6G,YAAA,WAAAA,aAAA5E,GAAA;MAAA,IAAA6E,MAAA;MACA;MACAC,gBAAA,CAAAC,GAAA,kCAAAC,MAAA,CAAAhF,GAAA,CAAAO,EAAA,GAAAZ,IAAA,WAAAyC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA;UACA,IAAA4C,OAAA,GAAA7C,QAAA,CAAAnG,IAAA;UACAkF,MAAA,CAAA+D,IAAA,CAAAD,OAAA;QACA;UACAJ,MAAA,CAAArE,QAAA,CAAA8B,KAAA,CAAAF,QAAA,CAAAG,GAAA;QACA;MACA,GAAA4C,KAAA,WAAA7C,KAAA;QACAuC,MAAA,CAAArE,QAAA,CAAA8B,KAAA,YAAAA,KAAA,CAAAkC,OAAA;MACA;IACA;IACAY,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAArF,GAAA,GAAAqF,IAAA,CAAArF,GAAA;QAAAsF,QAAA,GAAAD,IAAA,CAAAC,QAAA;MACA,IAAAtF,GAAA,CAAAuF,KAAA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAhG,WAAA;EACA;AACA", "ignoreList": []}]}