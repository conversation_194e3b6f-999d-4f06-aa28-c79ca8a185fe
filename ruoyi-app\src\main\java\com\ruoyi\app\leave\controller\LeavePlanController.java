package com.ruoyi.app.leave.controller;

import java.util.List;

import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.domain.LeavePlanMaterial;
import com.ruoyi.app.leave.dto.ApproveRequestDTO;
import com.ruoyi.app.leave.dto.LeaveStatusDTO;
import com.ruoyi.app.leave.enums.LeavePlanStatusEnum;
import com.ruoyi.app.leave.mapper.LeavePlanMapper;
import com.ruoyi.app.leave.mapper.LeavePlanMaterialMapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.app.v1.controller.AppBaseV1Controller;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.service.ILeavePlanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出门证计划申请Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/app/leave/plan")
public class LeavePlanController extends AppBaseV1Controller
{
    @Autowired
    private ILeavePlanService leavePlanService;

    @Autowired
    private LeavePlanMapper leavePlanMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private LeavePlanMaterialMapper leavePlanMaterialMapper;

    /**
     * 查询出门证计划申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeavePlan leavePlan)
    {
        startPage();
        List<LeavePlan> list = leavePlanService.selectLeavePlanList(leavePlan);
        return getDataTable(list);
    }

    /**
     * 导出出门证计划申请列表
     */
    @Log(title = "出门证计划申请", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeavePlan leavePlan)
    {
        List<LeavePlan> list = leavePlanService.selectLeavePlanList(leavePlan);
        ExcelUtil<LeavePlan> util = new ExcelUtil<LeavePlan>(LeavePlan.class);
        return util.exportExcel(list, "plan");
    }

    /**
     * 获取出门证计划申请详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leavePlanService.selectLeavePlanById(id));
    }

    /**
     * 新增出门证计划申请
     */
    @Log(title = "出门证计划申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeavePlan leavePlan)
    {
        leavePlanService.insertLeavePlan(leavePlan);
        return AjaxResult.success();
    }

    /**
     * 修改出门证计划申请
     */
    @Log(title = "出门证计划申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeavePlan leavePlan)
    {
        return toAjax(leavePlanService.updateLeavePlan(leavePlan));
    }

    /**
     * 出门证详情
     * @return
     */
    @GetMapping("/detail/{applyNo}")
    public AjaxResult detail(@PathVariable String applyNo)
    {
        return AjaxResult.success(leavePlanService.detail(applyNo, this.getWorkNo()));
    }

    /**
     * 删除出门证计划申请
     */
    @Log(title = "出门证计划申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leavePlanService.deleteLeavePlanByIds(ids));
    }

    //审批通过
    @PostMapping("/approve")
    public AjaxResult approve(ApproveRequestDTO approveRequestDTO)
    {
        SysUser sysUser = sysUserMapper.selectUserByUserName(this.getWorkNo());
        if (sysUser == null) {
            return AjaxResult.error("用户未登录或会话已过期");
        }
        // 设置审核人工号并传递用户对象到Service
        approveRequestDTO.setApproverWorkNo(sysUser.getUserName());
        leavePlanService.approve(approveRequestDTO, sysUser);

        //获取出门证计划申请信息
        LeavePlan plan = leavePlanMapper.selectLeavePlanByApplyNo(String.valueOf(approveRequestDTO.getApplyNo()));
        //获取申请状态
        LeavePlanStatusEnum leavePlanStatusEnum = LeavePlanStatusEnum.getByCode(plan.getPlanStatus());
        // 如果计量物资且审批通过，调用handlePlan方法同步计量数据库数据
        if (plan.getMeasureFlag()  == 1 &&leavePlanStatusEnum == LeavePlanStatusEnum.APPROVAL_COMPLETE) {
            //添加物资信息
            List<LeavePlanMaterial> materials = leavePlanMaterialMapper.selectLeavePlanMaterialByApplyNo(plan.getApplyNo());
            plan.setMaterials(materials);
            leavePlanService.handlePlan(plan);
        }

        return AjaxResult.success(true);
    }

    //废弃
    @PostMapping("/discard")
    public AjaxResult discard(LeavePlan leavePlan)
    {
        SysUser sysUser = sysUserMapper.selectUserByUserName(this.getWorkNo());
        leavePlanService.checkBeforeDiscard(leavePlan.getApplyNo(), sysUser);
        leavePlanService.discard(leavePlan.getApplyNo(), sysUser);
        return AjaxResult.success();
    }

    //待审批列表
    @GetMapping("/waitApproveList")
    public TableDataInfo waitApproveList(LeavePlan leavePlan)
    {
        String workNo = this.getWorkNo();
        TableDataInfo dataInfo = leavePlanService.waitApproveList(leavePlan,workNo);
        return getDataTable(dataInfo,pageNum,pageSize);
    }

    @GetMapping("/queryStatusList")
    public AjaxResult queryStatusList()
    {
        List<LeaveStatusDTO> leaveStatusDTOList = leavePlanService.queryStatusList();
        return AjaxResult.success(leaveStatusDTOList);
    }

    @GetMapping("/listForMiniApp")
    public TableDataInfo listForMiniApp(LeavePlan leavePlan)
    {
        String workNo = this.getWorkNo();
        TableDataInfo dataInfo = leavePlanService.selectListForMiniApp(leavePlan, workNo);
        return getDataTable(dataInfo,pageNum,pageSize);
    }


}
