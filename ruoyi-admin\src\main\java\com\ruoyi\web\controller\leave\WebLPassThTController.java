package com.ruoyi.web.controller.leave;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LPassThT;
import com.ruoyi.app.leave.service.ILPassThTService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 退货申请主Controller
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@RestController
@RequestMapping("/web/leave/passTh")
public class WebLPassThTController extends BaseController
{
    @Autowired
    private ILPassThTService lPassThTService;

    /**
     * 查询退货申请主列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LPassThT lPassThT)
    {
        startPage();
        List<LPassThT> list = lPassThTService.selectLPassThTList(lPassThT);
        return getDataTable(list);
    }

    /**
     * 导出退货申请主列表
     */
    @Log(title = "退货申请主", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LPassThT lPassThT)
    {
        List<LPassThT> list = lPassThTService.selectLPassThTList(lPassThT);
        ExcelUtil<LPassThT> util = new ExcelUtil<LPassThT>(LPassThT.class);
        return util.exportExcel(list, "passTh");
    }

    /**
     * 获取退货申请主详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lPassThTService.selectLPassThTById(id));
    }

    /**
     * 新增退货申请主
     */
    @Log(title = "退货申请主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LPassThT lPassThT)
    {
        return toAjax(lPassThTService.insertLPassThT(lPassThT));
    }

    /**
     * 修改退货申请主
     */
    @Log(title = "退货申请主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LPassThT lPassThT)
    {
        return toAjax(lPassThTService.updateLPassThT(lPassThT));
    }

    /**
     * 删除退货申请主
     */
    @Log(title = "退货申请主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lPassThTService.deleteLPassThTByIds(ids));
    }
}
