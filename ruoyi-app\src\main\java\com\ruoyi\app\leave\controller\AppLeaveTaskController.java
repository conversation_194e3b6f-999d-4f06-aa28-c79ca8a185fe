package com.ruoyi.app.leave.controller;

import java.util.List;

import com.ruoyi.app.leave.domain.*;
import com.ruoyi.app.leave.service.ILeaveLogService;
import com.ruoyi.app.leave.service.ILeavePlanService;
import com.ruoyi.app.leave.util.MeasureDesUtil;
import com.ruoyi.app.v1.controller.AppBaseV1Controller;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.service.ILeaveTaskService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出门证任务Controller
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/app/leave/task/plus")
public class AppLeaveTaskController extends AppBaseV1Controller
{
    @Autowired
    private ILeaveTaskService leaveTaskService;

    @Autowired
    private ILeavePlanService leavePlanService;

    @Autowired
    private ILeaveLogService leaveLogService;

    /**
     * 查询出门证任务列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeaveTask leaveTask)
    {
        startPage();
        List<LeaveTask> list = leaveTaskService.selectLeaveTaskList(leaveTask);
        return getDataTable(list);
    }

    /**
     * 导出出门证任务列表
     */
    @Log(title = "出门证任务", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveTask leaveTask)
    {
        List<LeaveTask> list = leaveTaskService.selectLeaveTaskList(leaveTask);
        ExcelUtil<LeaveTask> util = new ExcelUtil<LeaveTask>(LeaveTask.class);
        return util.exportExcel(list, "task");
    }

    /**
     * 获取出门证任务详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveTaskService.selectLeaveTaskById(id));
    }

    /**
     * 新增出门证任务
     */
    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveTask leaveTask)
    {
        return toAjax(leaveTaskService.insertLeaveTask(leaveTask));
    }

    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping("/taskAndMaterial")
    public AjaxResult addTaskAndMaterial(@RequestBody LeaveTask leaveTask)
    {

        String applyNo = leaveTask.getApplyNo();

        leavePlanService.updateIsSendCar(applyNo);
        String snowId = leaveTaskService.insertLeaveTaskAndMaterial(leaveTask);
        return new AjaxResult(200,"请求成功",snowId);
    }

    /**
     * 修改出门证任务
     */
    @Log(title = "出门证任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveTask leaveTask)
    {
        return toAjax(leaveTaskService.updateLeaveTask(leaveTask));
    }

    @Log(title = "出门证任务", businessType = BusinessType.UPDATE)
    @PutMapping("/taskEditUnload")
    public AjaxResult taskEditUnload(@RequestBody LeaveTask leaveTask)
    {
        String workNo = getWorkNo();
        leaveTask.setUnloadingWorkNo(workNo);

        return toAjax(leaveTaskService.updateLeaveTaskUnload(leaveTask));
    }


    /**
     * 删除出门证任务
     */
    @Log(title = "出门证任务", businessType = BusinessType.SELECT)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveTaskService.deleteLeaveTaskByIds(ids));
    }

    /**
     * 出门证二维码解密
     */
    @Log(title = "出门证任务", businessType = BusinessType.DELETE)
    @PostMapping("/handleLeaveTaskQrCode")
    public AjaxResult handleLeaveTaskQrCode(@RequestBody String decryptString)
    {

        String decryptQrCode = MeasureDesUtil.decrypt(decryptString);
        return new AjaxResult(200,"请求成功",decryptQrCode);
    }

    /**
     * 查询加工类型
     */
    @Log(title = "出门证任务", businessType = BusinessType.DELETE)
    @GetMapping("/getProcessList")
    public TableDataInfo getProcessList(DProcessTMeasure d_process_tMeasure)
    {
        startPage();
        List<DProcessTMeasure> list = leaveTaskService.selectProcessTypeListByValidFlag(d_process_tMeasure);
        return getDataTable(list);
    }

    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping("/taskEditStockOut")
    public AjaxResult taskEditStockOut(@RequestBody TaskEditStockOutVo taskEditStockOut)
    {


        LeaveLog leaveLog = taskEditStockOut.getLeaveLog();
        leaveLog.setCreateBy(getWorkNo());
        leaveLogService.insertLeaveLog(leaveLog);
        leaveTaskService.updateLeaveTask(taskEditStockOut.getLeaveTask());
        return new AjaxResult(200,"请求成功");
    }
}
