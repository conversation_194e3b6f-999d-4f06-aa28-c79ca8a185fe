package com.ruoyi.app.leave.service;

import java.util.Date;
import java.util.List;

import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.domain.LeaveTaskMaterial;
import com.ruoyi.app.leave.domain.StoreoutKqdbMeasure;

/**
 * 库区调拨出库Service接口
 * 
 * <AUTHOR>
 */
public interface IStoreoutKqdbService 
{
    /**
     * 查询库区调拨出库
     * 
     * @param id 库区调拨出库主键
     * @return 库区调拨出库
     */
    public StoreoutKqdbMeasure selectStoreoutKqdbById(Long id);

    /**
     * 查询库区调拨出库列表
     * 
     * @param storeoutKqdbMeasure 库区调拨出库
     * @return 库区调拨出库集合
     */
    public List<StoreoutKqdbMeasure> selectStoreoutKqdbList(StoreoutKqdbMeasure storeoutKqdbMeasure);

    /**
     * 新增库区调拨出库
     * 
     * @param storeoutKqdbMeasure 库区调拨出库
     * @return 结果
     */
    public int insertStoreoutKqdb(StoreoutKqdbMeasure storeoutKqdbMeasure);

    /**
     * 修改库区调拨出库
     * 
     * @param storeoutKqdbMeasure 库区调拨出库
     * @return 结果
     */
    public int updateStoreoutKqdb(StoreoutKqdbMeasure storeoutKqdbMeasure);

    /**
     * 批量删除库区调拨出库
     * 
     * @param ids 需要删除的库区调拨出库主键集合
     * @return 结果
     */
    public int deleteStoreoutKqdbByIds(Long[] ids);

    /**
     * 删除库区调拨出库信息
     * 
     * @param id 库区调拨出库主键
     * @return 结果
     */
    public int deleteStoreoutKqdbById(Long id);

    /**
     * 根据matchid删除库区调拨出库信息
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    public int deleteStoreoutKqdbByMatchid(String matchid);

    void handleKqdbStockOut(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial);


} 