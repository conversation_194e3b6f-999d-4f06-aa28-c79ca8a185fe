package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.StoreinKqdbMeasure;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 库区调拨入库Mapper接口
 * 
 * <AUTHOR>
 */
public interface StoreinKqdbMapper 
{
    /**
     * 查询库区调拨入库
     * 
     * @param id 库区调拨入库主键
     * @return 库区调拨入库
     */
    @DataSource(DataSourceType.XCC1)
    public StoreinKqdbMeasure selectStoreinKqdbById(Long id);

    /**
     * 查询库区调拨入库列表
     * 
     * @param storeinKqdbMeasure 库区调拨入库
     * @return 库区调拨入库集合
     */
    @DataSource(DataSourceType.XCC1)
    public List<StoreinKqdbMeasure> selectStoreinKqdbList(StoreinKqdbMeasure storeinKqdbMeasure);

    /**
     * 新增库区调拨入库
     * 
     * @param storeinKqdbMeasure 库区调拨入库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int insertStoreinKqdb(StoreinKqdbMeasure storeinKqdbMeasure);

    /**
     * 修改库区调拨入库
     * 
     * @param storeinKqdbMeasure 库区调拨入库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int updateStoreinKqdb(StoreinKqdbMeasure storeinKqdbMeasure);

    /**
     * 删除库区调拨入库
     * 
     * @param id 库区调拨入库主键
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreinKqdbById(Long id);

    /**
     * 批量删除库区调拨入库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreinKqdbByIds(Long[] ids);

    /**
     * 根据匹配ID删除库区调拨入库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreinKqdbByMatchid(String matchid);
} 