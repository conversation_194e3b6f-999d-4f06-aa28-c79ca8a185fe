{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\assess\\self\\user.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\assess\\self\\user.js", "mtime": 1756170476737}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listSelfAssessUser", "query", "request", "url", "method", "params", "listSelfAssessUserAll", "getSelfAssessUser", "addSelfAssessUser", "data", "updateSelfAssessUser", "delSelfAssessUser", "getSelfAssessUserByWorkNo", "getSelfAssessUserInfo", "listAvailable", "getReportDeptList", "getCheckDeptList", "getByWorkNoDeptId", "permissionRefresh"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/api/assess/self/user.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询绩效考核-干部自评人员配置列表\r\nexport function listSelfAssessUser(query) {\r\n  return request({\r\n    url: '/web/selfAssessUser/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询绩效考核-干部自评人员配置列表\r\nexport function listSelfAssessUserAll(query) {\r\n  return request({\r\n    url: '/web/selfAssessUser/listAll',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询绩效考核-干部自评人员配置详细\r\nexport function getSelfAssessUser(query) {\r\n  return request({\r\n    url: '/web/selfAssessUser/getInfo' ,\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 新增绩效考核-干部自评人员配置\r\nexport function addSelfAssessUser(data) {\r\n  return request({\r\n    url: '/web/selfAssessUser/insert',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改绩效考核-干部自评人员配置\r\nexport function updateSelfAssessUser(data) {\r\n  return request({\r\n    url: '/web/selfAssessUser/update',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除绩效考核-干部自评人员配置\r\nexport function delSelfAssessUser(data) {\r\n  return request({\r\n    url: '/web/selfAssessUser/delete',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 根据工号查询用户信息(弃用)\r\nexport function getSelfAssessUserByWorkNo(query) {\r\n  return request({\r\n    url: '/web/selfAssessUser/getInfoByWorkNo' ,\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n\r\n// 根据查询当前用户信息\r\nexport function getSelfAssessUserInfo() {\r\n  return request({\r\n    url: '/web/selfAssessUser/getSelfAssessUserInfo' ,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 查询绩效考核-干部自评人员配置列表(根据权限查询可配置人员)\r\nexport function listAvailable(query) {\r\n  return request({\r\n    url: '/web/selfAssessUser/listAvailable',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 获取用户部门（干部、一把手）\r\nexport function getReportDeptList() {\r\n  return request({\r\n    url: '/web/selfAssessUser/getReportDeptList' ,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 获取用户部门（一把手、条线领导）\r\nexport function getCheckDeptList() {\r\n  return request({\r\n    url: '/web/selfAssessUser/getCheckDeptList' ,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\nexport function getByWorkNoDeptId(query) {\r\n  return request({\r\n    url: '/web/selfAssessUser/getByWorkNoDeptId' ,\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 权限刷新\r\nexport function permissionRefresh() {\r\n  return request({\r\n    url: '/web/selfAssessUser/permissionRefresh',\r\n    method: 'post',\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,qBAAqBA,CAACL,KAAK,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,iBAAiBA,CAACN,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,iBAAiBA,CAACC,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,oBAAoBA,CAACD,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,iBAAiBA,CAACF,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,yBAAyBA,CAACX,KAAK,EAAE;EAC/C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAGA;AACO,SAASY,qBAAqBA,CAAA,EAAG;EACtC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,aAAaA,CAACb,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,iBAAiBA,CAAA,EAAG;EAClC,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASa,iBAAiBA,CAAChB,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASiB,iBAAiBA,CAAA,EAAG;EAClC,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}