<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="card-header" style="display: flex; align-items: center; justify-content: flex-start;">
        <h2>派车任务详情</h2>
        <el-tag size="medium" style="margin-left: 20px; margin-top: 10px;">
          任务状态： {{ getStatusText(taskInfoForm.taskStatus) }}
        </el-tag>
      </div>

      <!-- 任务流程图部分 -->
      <div class="section-container">
        <div class="section-title">任务流程</div>
        <div class="process-flow-container">
          <!-- <img style="width: 100%; max-height: 400px; object-fit: contain;" :src="require('@/assets/images/task-flow-chart.png')" /> -->
        </div>
      </div>

      <!-- 通行证二维码部分 -->
      <div class="section-container">
        <div class="section-title">通行证二维码</div>
        <div class="qrcode-container">
          <div ref="qrCode" class="qrcode"></div>
        </div>
      </div>

      <!-- 司机信息部分 -->
      <div class="section-container">
        <div class="section-title">司机信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">
            <template slot="label"><i class="el-icon-user"></i> 姓名</template>
            {{ taskInfoForm.driverName }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            <template slot="label"><i class="el-icon-mobile-phone"></i> 手机号</template>
            {{ taskInfoForm.mobilePhone }}
          </el-descriptions-item>

          <el-descriptions-item label="身份证号">
            <template slot="label"><i class="el-icon-document"></i> 身份证号</template>
            {{ taskInfoForm.idCardNo }}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            <template slot="label"><i class="el-icon-user"></i> 性别</template>
            {{ taskInfoForm.sex === 1 ? '男' : '女' }}
          </el-descriptions-item>

          <el-descriptions-item label="所属单位">
            <template slot="label"><i class="el-icon-office-building"></i> 所属单位</template>
            {{ taskInfoForm.companyName }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 司机照片和证件照片 -->
        <div class="driver-photos"
          v-if="driverInfo.photo || driverInfo.driverLicenseImgs || driverInfo.vehicleLicenseImgs">
          <div class="photo-item" v-if="driverInfo.photo">
            <h4><i class="el-icon-picture-outline"></i> 司机照片</h4>
            <div class="photo-container">
              <!-- <img :src="taskInfoForm.faceImg" alt="司机照片"> -->

              <el-image style="width: 200px; height: 200px" :src="taskInfoForm.faceImg" fit="contain" fallback=""
                :preview-src-list="[taskInfoForm.faceImg]">
                <template #error>
                  <div style="width: 100%; height: 100%;"></div> <!-- 空白区域 -->
                </template>
              </el-image>
            </div>
          </div>
          <div class="photo-item" v-if="driverInfo.driverLicenseImgs">
            <h4><i class="el-icon-picture-outline"></i> 驾驶证照片</h4>
            <div class="photo-container">
              <!-- <img :src="taskInfoForm.driverLicenseImg" alt="驾驶证照片"> -->

              <el-image style="width: 200px; height: 200px" :src="taskInfoForm.driverLicenseImg" fit="contain"
                fallback="" :preview-src-list="[taskInfoForm.driverLicenseImg]">
                <template #error>
                  <div style="width: 100%; height: 100%;"></div> <!-- 空白区域 -->
                </template>
              </el-image>
            </div>
          </div>
          <div class="photo-item" v-if="driverInfo.vehicleLicenseImgs">
            <h4><i class="el-icon-picture-outline"></i> 行驶证照片</h4>
            <div class="photo-container">
              <!-- <img :src="taskInfoForm.drivingLicenseImg" alt="行驶证照片"> -->

              <el-image style="width: 200px; height: 200px" :src="taskInfoForm.drivingLicenseImg" fit="contain"
                fallback="" :preview-src-list="[taskInfoForm.drivingLicenseImg]">
                <template #error>
                  <div style="width: 100%; height: 100%;"></div> <!-- 空白区域 -->
                </template>
              </el-image>
            </div>
          </div>
        </div>
      </div>

      <!-- 车辆信息部分 -->
      <div class="section-container">
        <div class="section-title">车辆信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="车牌号" v-if="taskInfoForm.carNum != null">
            <template slot="label"><i class="el-icon-truck"></i> 车牌号</template>
            <el-tag type="primary">{{ taskInfoForm.carNum }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="车牌颜色" v-if="taskInfoForm.licensePlateColor != null">
            <template slot="label"><i class="el-icon-takeaway-box"></i> 车牌颜色</template>
            {{ taskInfoForm.licensePlateColor }}
          </el-descriptions-item>

          <el-descriptions-item label="车辆道路运输证号" v-if="taskInfoForm.trailerId != null">
            <template slot="label"><i class="el-icon-document"></i> 运输证号</template>
            {{ taskInfoForm.trailerId }}
          </el-descriptions-item>
          <el-descriptions-item label="挂车号牌" v-if="taskInfoForm.trailerNumber">
            <template slot="label"><i class="el-icon-truck"></i> 挂车号牌</template>
            <el-tag type="info">{{ taskInfoForm.trailerNumber }}</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="挂车道路运输证号" v-if="taskInfoForm.trailerId">
            <template slot="label"><i class="el-icon-document"></i> 挂车运输证号</template>
            {{ taskInfoForm.trailerId }}
          </el-descriptions-item>
          <el-descriptions-item label="轴型" v-if="taskInfoForm.axisType != null">
            <template slot="label"><i class="el-icon-data-line"></i> 轴型</template>
            {{ taskInfoForm.axisType }}
          </el-descriptions-item>

          <el-descriptions-item label="货车自重" v-if="taskInfoForm.driverWeight != null">
            <template slot="label"><i class="el-icon-heavy-rain"></i> 货车自重</template>
            {{ taskInfoForm.driverWeight }} kg
          </el-descriptions-item>
          <el-descriptions-item label="车货总质量限值" v-if="taskInfoForm.maxWeight != null">
            <template slot="label"><i class="el-icon-opportunity"></i> 总质量限值</template>
            {{ taskInfoForm.maxWeight }} kg
          </el-descriptions-item>

          <el-descriptions-item label="车辆排放标准" v-if="taskInfoForm.vehicleEmissionStandards != null">
            <template slot="label"><i class="el-icon-magic-stick"></i> 排放标准</template>
            <el-tag :type="getEmissionStandardsTagType(taskInfoForm.vehicleEmissionStandards)">
              {{ getEmissionStandardsText(taskInfoForm.vehicleEmissionStandards) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发动机号" v-if="taskInfoForm.engineNumber != null">
            <template slot="label"><i class="el-icon-set-up"></i> 发动机号</template>
            {{ taskInfoForm.engineNumber }}
          </el-descriptions-item>

          <el-descriptions-item label="车辆识别代码" v-if="taskInfoForm.vinNumber != null">
            <template slot="label"><i class="el-icon-document-checked"></i> 车辆识别代码</template>
            {{ taskInfoForm.vinNumber }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 任务物资列表部分 -->
      <div class="section-container">
        <div class="section-title">物资列表</div>
        <el-table :data="taskMaterials" style="width: 100%" border @selection-change="handleSelectionChange">
          <!-- <el-table-column type="selection" width="55" v-if="measureFlag == 0">
          </el-table-column> -->
          <el-table-column type="index" width="50" label="序号">
          </el-table-column>
          <el-table-column prop="materialName" label="物资名称" width="150">
          </el-table-column>
          <el-table-column prop="materialSpec" label="物资型号规格" width="150">
          </el-table-column>
          <!-- v-if="measureFlag == 0" -->
          <el-table-column prop="planNum" label="计划数量" width="120">
          </el-table-column>
          <el-table-column prop="measureUnit" label="单位" width="120">
          </el-table-column>
          <el-table-column prop="doormanReceiveNum" label="门卫出厂确认数量" width="230"
            v-if="taskInfoForm.taskStatus >= 4 && measureFlag == 0 && taskInfoForm.taskType != 2">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.doormanReceiveNum" :min="0" controls-position="right"
                :disabled="!isdoorMan && taskInfoForm.taskStatus !== 4" />
            </template>
          </el-table-column>
          <el-table-column prop="doormanReceiveNumIn" label="门卫入厂确认数量" width="230"
            v-if="measureFlag == 0 && taskInfoForm.taskType !== 1 && taskInfoForm.taskStatus >= 5">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.doormanReceiveNumIn" :min="0" controls-position="right"
                :disabled="!isdoorMan && taskInfoForm.taskStatus !== 5" />
            </template>
          </el-table-column>
          <!-- v-if="measureFlag == 0 && taskInfoForm.taskType == 2" -->
          <el-table-column prop="doormanReceiveNum" label="分厂确认数量" width="230"
            v-if="measureFlag == 0 && taskInfoForm.taskStatus > 7 && (taskInfoForm.taskType == 3 || taskInfoForm.taskType == 2)">
            <!-- <template slot-scope="scope">
              <el-input v-model="scope.row.doormanReceiveNum" :min="0" controls-position="right" disabled />
            </template> -->
          </el-table-column>
          <el-table-column prop="remark" label="备注">
          </el-table-column>

          <!-- v-if="taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5 && (measureFlag == 0 && taskInfoForm.taskType == 2 && taskInfoForm.taskStatus == 7)" -->
          <!-- <el-table-column v-if="measureFlag == 0 && (taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5)"
            label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <div style="display: flex; flex-wrap: wrap; gap: 4px;">

                <div v-if="editingRow === scope.row">
                  <el-button size="mini" type="success" @click="saveDoorManRow(scope.row)">保存</el-button>
                  <el-button size="mini" @click="cancelDoorManEdit(scope.row)">取消</el-button>
                </div>

                <div v-else>
                  <el-button v-hasPermi="['leave:task:doorManConfirm']" size="mini" type="primary"
                    @click="editDoorManRow(scope.row)">门卫编辑</el-button>
                </div>
              </div>
            </template>
          </el-table-column> -->
        </el-table>

        <div class="btn-wrapper" v-if="measureFlag == 0 && taskInfoForm.taskStatus == 4">
          <el-button type="primary" size="medium" @click="saveDoorManRow" class="dispatch-btn">
            <!-- :disabled="!hasSelectedItems" -->
            门卫出厂确认
          </el-button>
        </div>
        <div class="btn-wrapper" v-if="measureFlag == 0 && taskInfoForm.taskStatus == 5">
          <el-button type="primary" size="medium" @click="saveDoorManRowIn" class="dispatch-btn">
            <!-- :disabled="!hasSelectedItems" -->
            门卫入厂确认
          </el-button>
        </div>
        <div class="button-container" v-if="measureFlag == 0 && taskInfoForm.taskStatus == 7">
          <el-button type="primary" @click="handleNonMeasureFactoryConfirm">
            分厂确认
          </el-button>
        </div>
      </div>

      <div class="section-container" v-if="measureFlag == 1">
        <div class="section-title">计量信息</div>
        <div class="info-footer" style="margin-top: 20px;" v-if="measureFlag == 1">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="皮重" :label-style="{ width: '200px' }" v-if="taskInfoForm.tare != null">
              {{ taskInfoForm.tare + ' 吨' }}
            </el-descriptions-item>
            <el-descriptions-item label="毛重" :label-style="{ width: '200px' }" v-if="taskInfoForm.gross != null">
              {{ taskInfoForm.gross + ' 吨' }}
            </el-descriptions-item>
            <el-descriptions-item label="净重" :label-style="{ width: '200px' }" v-if="taskInfoForm.netWeight != null">
              {{ taskInfoForm.netWeight + ' 吨' }}
            </el-descriptions-item>
            <el-descriptions-item label="皮重时间" :label-style="{ width: '200px' }" v-if="taskInfoForm.tareTime != null">
              {{ taskInfoForm.tareTime }}
            </el-descriptions-item>
            <el-descriptions-item label="毛重时间" :label-style="{ width: '200px' }" v-if="taskInfoForm.grossTime != null">
              {{ taskInfoForm.grossTime }}
            </el-descriptions-item>
            <el-descriptions-item label="净重时间" :label-style="{ width: '200px' }" v-if="taskInfoForm.netWeight != null">
              {{ taskInfoForm.grossTime }}
            </el-descriptions-item>
            <el-descriptions-item label="皮重（复磅）" :label-style="{ width: '200px' }" v-if="taskInfoForm.secTare != null">
              {{ taskInfoForm.secTare + ' 吨' }}
            </el-descriptions-item>
            <el-descriptions-item label="毛重（复磅）" :label-style="{ width: '200px' }" v-if="taskInfoForm.secGross != null">
              {{ taskInfoForm.secGross + ' 吨' }}
            </el-descriptions-item>
            <el-descriptions-item label="净重（复磅）" :label-style="{ width: '200px' }"
              v-if="taskInfoForm.secNetWeight != null">
              {{ taskInfoForm.secNetWeight + ' 吨' }}
            </el-descriptions-item>
            <el-descriptions-item label="皮重时间（复磅）" :label-style="{ width: '200px' }"
              v-if="taskInfoForm.secTareTime != null">
              {{ taskInfoForm.secTareTime }}
            </el-descriptions-item>
            <el-descriptions-item label="毛重时间（复磅）" :label-style="{ width: '200px' }"
              v-if="taskInfoForm.secGrossTime != null">
              {{ taskInfoForm.secGrossTime }}
            </el-descriptions-item>
            <el-descriptions-item label="净重时间（复磅）" :label-style="{ width: '200px' }"
              v-if="taskInfoForm.secNetWeightTime != null">
              {{ taskInfoForm.secNetWeightTime }}
            </el-descriptions-item>
          </el-descriptions>
          <!-- v-if="taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5" -->
          <div class="btn-wrapper" v-if="measureFlag == 1 && taskInfoForm.taskStatus == 4">
            <el-button type="primary" size="medium" @click="handleDoorManMeasureConfirm" class="dispatch-btn">
              门卫出厂确认
            </el-button>
          </div>
          <div class="btn-wrapper" v-if="measureFlag == 1 && taskInfoForm.taskStatus == 5">
            <el-button type="primary" size="medium" @click="handleDoorManMeasureConfirm" class="dispatch-btn">
              门卫入厂确认
            </el-button>
          </div>
          <!-- 新增分厂确认按钮 -->
          <!-- <div class="btn-wrapper">
            <el-button type="primary" size="medium" @click="openFactoryConfirmDialog" class="dispatch-btn">
              分厂确认
            </el-button>
          </div> -->
        </div>
      </div>

      <!-- 可编辑的出库信息表单 -->
      <div class="section-container" v-if="measureFlag == 1 && taskInfoForm.taskStatus == 2">
        <div class="section-title">出库信息</div>

        <el-form :model="factoryConfirmForm" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="计划号">
                <el-input v-model="factoryConfirmForm.planNo" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="车牌号">
                <el-input v-model="factoryConfirmForm.carNum" placeholder="请输入车牌号" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物资名称">
                <el-input :value="taskMaterials.map(item => item.materialName).join(' ')" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="皮重(t)">
                <el-input :value="taskInfoForm.tare" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="planForm.planType == 3">
            <el-col :span="12">
              <el-form-item label="物料规格">
                <el-input :value="taskMaterials.map(item => item.materialSpec).join(' ')" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="供货单位">
                <el-input v-model="factoryConfirmForm.sourceCompany" placeholder="请输入车牌号" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="收货单位">
                <el-input v-model="factoryConfirmForm.receiveCompany" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="规格" v-if="taskInfoForm.taskType">
                <el-input v-model="factoryConfirmForm.stockOutSpec1Length" placeholder="请输入规格"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="总数">
                <el-input v-model="factoryConfirmForm.stockOutTotal" placeholder="请输入总数"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="总数单位">
                <el-select v-model="factoryConfirmForm.stockOutTotalUnit" placeholder="请选择总数单位">
                  <el-option label="件" value="件"></el-option>
                  <el-option label="支" value="支"></el-option>
                  <el-option label="张" value="张"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="加工类型">
                <el-select v-model="factoryConfirmForm.stockOutProcessType" placeholder="请选择加工类型" filterable
                  :filter-method="filterProcessType">
                  <el-option v-for="item in filteredProcessTypeOptions" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="炉号/批号">
                <el-input v-model="factoryConfirmForm.stockOutHeatNo" placeholder="请输入炉号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钢种">
                <el-input v-model="factoryConfirmForm.stockOutSteelGrade" placeholder="请输入钢种"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="轴数">
                <el-select v-model="factoryConfirmForm.stockOutAxles" placeholder="请选择轴数">
                  <el-option label="2" value="2"></el-option>
                  <el-option label="3" value="3"></el-option>
                  <el-option label="4" value="4"></el-option>
                  <el-option label="5" value="5"></el-option>
                  <el-option label="6轴标准" value="6轴标准"></el-option>
                  <el-option label="6轴非标准" value="6轴非标准"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="出库备注">
            <el-input type="textarea" v-model="factoryConfirmForm.stockOutRemark" placeholder="请输入出库备注"></el-input>
          </el-form-item>
        </el-form>

        <div class="btn-wrapper">
          <el-button type="primary" @click="submitStockOutConfirm" size="medium" class="dispatch-btn">确认出库</el-button>
        </div>
      </div>

      <!-- 只读的出库信息表单 -->
      <div class="section-container"
        v-if="measureFlag == 1 && taskInfoForm.taskStatus > 2 && taskInfoForm.taskType !== 2 && taskInfoForm.isDirectSupply != 3">
        <div class="section-title">出库信息</div>

        <el-form :model="taskInfoForm" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="计划号">
                <el-input :value="taskInfoForm.planNo" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="车牌号">
                <el-input :value="taskInfoForm.carNum" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物资名称">
                <el-input :value="materialNames" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="皮重(t)">
                <el-input :value="taskInfoForm.tare" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="planForm.planType == 3">
            <el-col :span="12">
              <el-form-item label="物料规格">
                <el-input :value="materialSpecs" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="供货单位">
                <el-input :value="planForm.sourceCompany" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="收货单位">
                <el-input :value="planForm.receiveCompany" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="规格">
                <el-input :value="taskInfoForm.stockOutSpec1Length" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="总数">
                <el-input :value="taskInfoForm.stockOutTotals" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="taskInfoForm.taskType == 2">
            <el-col :span="12">
              <el-form-item label="加工类型">
                <el-input :value="taskInfoForm.stockOutProcessType" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="炉号/批号">
                <el-input :value="taskInfoForm.stockOutHeatNo" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="钢种">
                <el-input :value="taskInfoForm.stockOutSteelGrade" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="轴数">
                <el-input :value="taskInfoForm.stockOutAxles" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注">
            <el-input type="textarea" :value="taskInfoForm.stockOutRemark" disabled></el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- 可编辑的入库信息表单 -->
      <div class="section-container" v-if="measureFlag == 1 && taskInfoForm.taskStatus == 7">
        <div class="section-title">入库信息</div>

        <el-form :model="factoryConfirmForm" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="计划号">
                <el-input v-model="factoryConfirmForm.planNo" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="车牌号">
                <el-input v-model="factoryConfirmForm.carNum" placeholder="请输入车牌号" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物资名称">
                <el-input :value="materialNames" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- <div
                v-if="taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''">
                <el-form-item label="毛重(t)">
                  <el-input v-model="factoryConfirmForm.secGross" placeholder="" disabled></el-input>
                </el-form-item>
              </div>

              <div v-if="taskInfoForm.isDirectSupply == 1">
                <el-form-item label="毛重(t)">
                  <el-input v-model="factoryConfirmForm.gross" placeholder="" disabled></el-input>
                </el-form-item>
              </div> -->

              <el-form-item label="毛重(t)">
                <el-input :value="taskInfoForm.secGross" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="供货单位">
                <el-input v-model="factoryConfirmForm.sourceCompany" placeholder="请输入车牌号" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="收货单位">
                <el-input v-model="factoryConfirmForm.receiveCompany" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="加工类型">
                <el-select v-model="factoryConfirmForm.processType" placeholder="请选择加工类型" filterable
                  :filter-method="filterProcessType">
                  <el-option v-for="item in filteredProcessTypeOptions" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="钢种">
                <el-input v-model="factoryConfirmForm.steelGrade" placeholder="请输入钢种"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="规格">
                <el-input v-model="factoryConfirmForm.spec1Length" placeholder="请输入规格"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="总数">
                <el-input v-model="factoryConfirmForm.total" placeholder="请输入总数"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="总数单位">
                <el-select v-model="factoryConfirmForm.totalUnit" placeholder="请选择总数单位">
                  <el-option label="件" value="件"></el-option>
                  <el-option label="支" value="支"></el-option>
                  <el-option label="张" value="张"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="炉号/批号">
                <el-input v-model="factoryConfirmForm.heatNo" placeholder="请输入炉号/批号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="扣重(t)">
                <el-input v-model="factoryConfirmForm.deductWeight" placeholder="请输入扣重"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="出库备注">
            <el-input type="textarea" v-model="factoryConfirmForm.remark" placeholder="请输入出库备注"></el-input>
          </el-form-item>

          <el-form-item label="是否直供" v-if="taskInfoForm.taskType == 2">
            <el-checkbox v-model="factoryConfirmForm.showDropdown" @change="handleShowDropdownChange">是否直供</el-checkbox>
          </el-form-item>

          <el-form-item v-if="factoryConfirmForm.showDropdown" label="直供申请单号">
            <el-input v-model="factoryConfirmForm.extraOption" placeholder="请选择直供申请单号" readonly style="width: 300px;">
              <el-button slot="append" icon="el-icon-search" @click="openOptionDialog"></el-button>
            </el-input>
          </el-form-item>
        </el-form>

        <div class="btn-wrapper">
          <el-button type="primary" @click="submitFactoryConfirm" size="medium" class="dispatch-btn">确认入库</el-button>
        </div>
      </div>

      <!-- 只读的入库信息表单 -->
      <div class="section-container"
        v-if="measureFlag == 1 && taskInfoForm.taskStatus > 7 && taskInfoForm.taskType !== 1">
        <div class="section-title">入库信息</div>

        <el-form :model="taskInfoForm" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="计划号">
                <el-input :value="taskInfoForm.planNo" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="车牌号">
                <el-input :value="taskInfoForm.carNum" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物资名称">
                <el-input :value="materialNames" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- <div
                v-if="taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''">
                <el-form-item label="毛重(t)">
                  <el-input :value="taskInfoForm.secGross" disabled></el-input>
                </el-form-item>
              </div>

              <div v-if="taskInfoForm.isDirectSupply == 1">
                <el-form-item label="毛重(t)">
                  <el-input :value="taskInfoForm.gross" disabled></el-input>
                </el-form-item>
              </div> -->

              <el-form-item label="毛重(t)">
                <el-input :value="taskInfoForm.secGross" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="供货单位">
                <el-input :value="planForm.sourceCompany" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="收货单位">
                <el-input :value="planForm.receiveCompany" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="加工类型">
                <el-input :value="taskInfoForm.processType" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="钢种">
                <el-input :value="taskInfoForm.steelGrade" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="规格">
                <el-input :value="taskInfoForm.spec1Length" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="总数">
                <el-input :value="taskInfoForm.totals" disabled></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="炉号/批号">
                <el-input :value="taskInfoForm.heatNo" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="扣重">
                <el-input :value="taskInfoForm.deductWeight" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注">
            <el-input type="textarea" :value="taskInfoForm.remark" disabled></el-input>
          </el-form-item>


          <el-form-item v-if="taskInfoForm.directSupplyTaskNo" label="直供对应任务单号">
            <el-input :value="taskInfoForm.directSupplyTaskNo" disabled style="width: 300px;"></el-input>
            <el-button style="margin-left: 10px; font-size: 14px; padding: 5px 10px;" type="primary"
              @click="openNewTaskWindow">前往任务单号</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 日志列表部分 -->
      <div class="section-container">
        <div class="section-title">任务日志</div>
        <el-timeline>
          <el-timeline-item v-for="(log, index) in taskLogs" :key="index" :timestamp="log.createTime"
            :color="getLogColor(log)">
            {{ log.info }}
          </el-timeline-item>
        </el-timeline>
      </div>

      <div class="form-footer">
        <el-button @click="cancel">返 回</el-button>
      </div>
    </el-card>

    <!-- 选项弹窗 -->
    <el-dialog title="选择直供申请单号" :visible.sync="optionDialogVisible" width="1600px">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="计划号">
          <el-input v-model="searchForm.planNo" placeholder="请输入计划号"></el-input>
        </el-form-item>
        <el-form-item label="申请编号">
          <el-input v-model="searchForm.applyNo" placeholder="请输入申请编号"></el-input>
        </el-form-item>
        <el-form-item label="收货单位">
          <el-input v-model="searchForm.receiveCompany" placeholder="请输入收货单位"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchOptions">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button style="margin-left: 10px; font-size: 14px; padding: 5px 10px;" type="primary"
            @click="openNewWindow">直供对应任务号
          </el-button>
        </el-form-item>
      </el-form>
      <el-table :data="optionList" style="width: 100%" @selection-change="handleOptionSelection" ref="optionTable">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="planNo" label="计划号" width="150" />
        <el-table-column prop="applyNo" label="申请编号" width="150" />
        <el-table-column prop="materialName" label="物资名称" width="150" />
        <el-table-column prop="materialSpec" label="物料规格" width="120" />
        <el-table-column prop="sourceCompany" label="申请单位" width="150" />
        <el-table-column prop="receiveCompany" label="收货单位" width="150" />
        <el-table-column prop="plannedAmount" label="计划量/t" width="150" />
        <el-table-column prop="startTime" label="开始时间" width="160">
          <template slot-scope="scope">
            {{ parseTime(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="160">
          <template slot-scope="scope">
            {{ parseTime(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="planStatus" label="状态" width="150" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="optionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmOptionSelection">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTask, getTaskByTaskNo, getTaskmaterials, getProcessList, getDirectSupplyPlans, getDirectSupplyPlanAndTaskDetail, handleUnload, handleStockOut, isAllowDispatch, getPlanMaterials, editTaskmaterials, getTaskLogs, addLeaveLog, updateTask, addLeaveLogAndEditTaskMaterialsAndUpdateTask } from "@/api/leave/task";
import { detailPlan } from "@/api/leave/plan";
import { listXctgDriverCar, getXctgDriverCar, delXctgDriverCar, addXctgDriverCar, updateXctgDriverCar, exportXctgDriverCar } from "@/api/truck/common/xctgDriverCar";
import { Message } from "element-ui";
import QRCode from "qrcodejs2";


export default {
  name: "DispatchTaskDetail",
  data() {
    return {
      factoryConfirmDialogVisible: false,
      factoryConfirmForm: {
        companyName: '',
        taskNo: '',
        applyNo: '',
        planNo: '',
        taskType: null,
        unloadingWorkNo: '',
        unloadingTime: null,
        spec1Length: null,
        spec2Width: null,
        totals: '',
        total: '',
        totalUnit: '',
        processType: '',
        heatNo: '',
        steelGrade: '',
        axles: '',
        remark: '',
        taskStatus: 9, // 完成状态
        carNum: '', // 车牌号
        // 出库信息
        stockOutSpec1Length: null,
        stockOutSpec2Width: null,
        stockOutTotals: '',
        stockOutTotalUnit: '',
        stockOutTotal: '',
        stockOutProcessType: '',
        stockOutHeatNo: '',
        stockOutSteelGrade: '',
        stockOutAxles: '',
        stockOutRemark: '',
        handledMaterialName: '',
        sourceCompany: '',
        receiveCompany: '',
        showDropdown: false,
        extraOption: '',
        deductWeight: null, // 添加扣重字段
      },
      optionDialogVisible: false,
      searchForm: {
        planNo: '',
        applyNo: '',
        receiveCompany: ''
      },
      optionList: [],
      editDoorManStatus: false,
      editFactoryStatus: false,
      // 司机信息
      driverInfo: {
        id: 1,
        name: '王小明',
        idCard: '110101199001010001',
        phone: '13800138000',
        gender: '1',
        company: '北京运输有限公司',
        photo: 'https://via.placeholder.com/150',
        driverLicenseImgs: 'https://via.placeholder.com/300x200',
        vehicleLicenseImgs: 'https://via.placeholder.com/300x200'
      },

      // 车辆信息
      carInfo: {},

      // 任务物资列表
      taskMaterials: [],

      // 任务日志列表
      taskLogs: [],

      // 申请编号
      applyNo: null,

      isdoorMan: false,

      // 派车任务ID
      dispatchId: null,

      taskInfoForm: {},

      measureFlag: null,

      backupTaskMaterials: null,
      taskNo: null,

      selectedOption: null,

      planForm: {},

      processTypeOptions: [], // 动态加载的加工类型选项

      filteredProcessTypeOptions: [], // 过滤后的加工类型选项

      searchProcessTypeQuery: '',// 搜索框的值

      directSupplyPlanList: [], // 直供计划列表

      editingRow: null,

      selectedRows: [], // 添加选中行数据数组

      directSupplyParams: {}
    };
  },

  computed: {
    displayProcessTypeOptions() {
      return this.searchProcessTypeQuery ? this.filteredProcessTypeOptions : this.processTypeOptions;
    },

    // 是否有选中的项
    hasSelectedItems() {
      return this.selectedRows.length > 0;
    },

    // 添加计算属性
    materialNames() {
      return this.taskMaterials.map(item => item.materialName).join(' ');
    },

    materialSpecs() {
      return this.taskMaterials.map(item => item.materialSpec).join(' ');
    }
  },

  activated() {
    console.log("activated执行");
    this.resetTaskInfoForm();

    // 获取路由参数 - 支持两种方式：query参数和路径参数
    let taskNo = this.$route.params.taskNo || this.$route.query.taskNo;

    if (taskNo) {
      // 新的方式：通过taskNo获取所有参数
      this.taskNo = taskNo;
      console.log("taskNo", this.taskNo);
      this.validDoorMan();

      // 使用 async/await 确保按顺序执行
      this.initializeDataByTaskNo();
    } else {
      // 兼容旧的方式：从query参数获取
      const { dispatchId, applyNo, measureFlag, planType, taskNo: queryTaskNo } = this.$route.query;
      this.dispatchId = dispatchId;
      this.applyNo = applyNo;
      this.measureFlag = measureFlag;
      console.log("this.measureFlag", this.measureFlag)
      this.planType = planType;
      this.taskNo = queryTaskNo;
      console.log("taskNo", this.taskNo);
      this.validDoorMan();

      // 使用 async/await 确保按顺序执行
      this.initializeData();
    }
  },

  methods: {
    getDirectSupplyPlanAndTask() {


      let leaveTask0 = {
        taskNo: this.taskInfoForm.directSupplyTaskNo
      }

      getDirectSupplyPlanAndTaskDetail(leaveTask0).then(res => {
        console.log("getDirectSupplyPlanAndTaskDetail", res)
        if (res.code == 200) {
          this.directSupplyParams.dispatchId = res.rows[0].id;
          this.directSupplyParams.applyNo = res.rows[0].applyNo;
          this.directSupplyParams.taskNo = res.rows[0].taskNo;
          this.directSupplyParams.measureFlag = res.rows[1].measureFlag;
          this.directSupplyParams.planType = res.rows[1].planType;
        } else {
          this.$message.error(res.message || '获取计划列表失败');
        }
      }).catch(err => {
        console.error('getDirectSupplyPlanAndTaskDetail error:', err);
        this.$message.error('网络异常，稍后重试');
        throw err;
      });

    },

    validDoorMan() {
      this.$store.getters.roles.forEach(item => {
        if (item == 'leave.quard') {
          this.isdoorMan = true;
        }
      });
      console.log("isdoorMan", this.isdoorMan)
    },
    async initializeData() {
      try {
        // 等待所有异步操作完成
        await this.getTaskInfo();
        await this.getTaskmaterialList(this.taskNo);
        await this.getPlanInfo(this.applyNo);

        // 在所有数据加载完成后执行
        this.uploadFactoryConfirmForm();

        // 其他初始化操作
        this.getTaskLogList(this.taskNo);
        this.getProcessType();

        //查询直供对应计划、任务详情
        this.getDirectSupplyPlanAndTask();
      } catch (error) {
        console.error('Error initializing data:', error);
        this.$message.error('数据加载失败，请刷新页面重试');
      }
    },

    async initializeDataByTaskNo() {
      try {
        // 通过taskNo获取任务信息
        await this.getTaskInfoByTaskNo();

        // 通过applyNo获取计划信息
        await this.getPlanInfo(this.applyNo);

        // 获取任务物资列表
        await this.getTaskmaterialList(this.taskNo);

        // 在所有数据加载完成后执行
        this.uploadFactoryConfirmForm();

        // 其他初始化操作
        this.getTaskLogList(this.taskNo);
        this.getProcessType();

        //查询直供对应计划、任务详情
        this.getDirectSupplyPlanAndTask();
      } catch (error) {
        console.error('Error initializing data by taskNo:', error);
        this.$message.error('数据加载失败，请刷新页面重试');
      }
    },

    uploadFactoryConfirmForm() {
      // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn
      this.taskMaterials.forEach(item => {
        item.doormanReceiveNum = item.planNum;
        console.log("item.planType", this.planForm.planType);
        if (this.planForm.planType == 2 || this.planForm.planType == 3) {
          item.doormanReceiveNumIn = item.planNum;
        }
      });

      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');
      let materialSpecs = this.taskMaterials.map(item => item.materialSpec).join(' ');
      // 初始化表单数据
      this.factoryConfirmForm = {
        companyName: this.taskInfoForm.companyName,
        gross: this.taskInfoForm.gross,
        secGross: this.taskInfoForm.secGross,
        driverName: this.taskInfoForm.driverName,
        tare: this.taskInfoForm.tare,
        taskNo: this.taskNo,
        applyNo: this.applyNo,
        planNo: this.taskInfoForm.planNo,
        unloadingWorkNo: '',
        unloadingTime: new Date(),
        spec1Length: null,
        spec2Width: null,
        totals: '',
        total: '',
        totalUnit: '',
        processType: '',
        heatNo: '',
        steelGrade: '',
        axles: '',
        remark: '',
        taskStatus: 9,
        carNum: this.taskInfoForm.carNum, // 初始化车牌号
        handledMaterialName: handledMaterialName,
        materialSpecs: materialSpecs,
        sourceCompany: this.planForm.sourceCompany,
        receiveCompany: this.planForm.receiveCompany,
        showDropdown: false, // 是否启用额外选项
        extraOption: '', // 额外选项的值
        // 出库信息
        stockOutSpec1Length: null,
        stockOutSpec2Width: null,
        stockOutTotals: '',
        stockOutTotalUnit: '',
        stockOutTotal: '',
        stockOutProcessType: '',
        stockOutHeatNo: '',
        stockOutSteelGrade: '',
        stockOutAxles: '',
        stockOutRemark: '',
        deductWeight: null, // 添加扣重字段初始化
      };
    },

    openNewWindow() {
      const newWindowUrl = 'http://localhost/leave/leavePlanList'; // 替换为实际要跳转的页面 URL
      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL
    },
    //获取可以直供的计划
    async getDirectSupplyList() {
      try {
        let leavePlan = {
          sourceCompany: this.planForm.sourceCompany,
          planType: 3,
        }
        console.log("获取可以直供的计划", leavePlan)

        const res = await getDirectSupplyPlans(leavePlan);
        console.log("getDirectSupplyPlans", res)
        if (res.code == 200) {
          this.directSupplyPlanList = res.rows;
          // //查询每个计划的物资
          // for (const item of this.directSupplyPlanList) {
          //   console.log("item", item)
          //   let leavePlanMaterial = {
          //     applyNo: item.applyNo
          //   };
          //   const response = await getPlanMaterials(leavePlanMaterial);
          //   if (response.code == 200) {
          //     console.log("getPlanMaterials", response)
          //     item.materialName = response.rows[0].materialName;
          //     item.materialSpec = response.rows[0].materialSpec;
          //   } else {
          //     this.$message.error(response.message || '获取计划物资失败');
          //   }
          // }
        } else {
          this.$message.error(res.message || '获取计划列表失败');
        }
      } catch (err) {
        console.error('getDirectSupplyPlans error:', err);
        this.$message.error('网络异常，稍后重试');
        throw err;
      }
    },
    filterProcessType(query) {
      this.searchProcessTypeQuery = query;

      if (this.searchProcessTypeQuery) {
        console.log("processTypeOptions", this.processTypeOptions)

        this.filteredProcessTypeOptions = this.processTypeOptions.filter(item =>
          item.value.includes(query)
        );
      } else {

        this.filteredProcessTypeOptions = this.processTypeOptions;
      }
    },
    getProcessType() {
      getProcessList().then(res => {
        console.log("getProcessList", res)
        if (res.code == 200) {
          this.processTypeOptions = res.rows.map(item => ({
            value: item.processname,
            label: item.processname
          }));
          this.filteredProcessTypeOptions = this.processTypeOptions; // 初始化过滤后的选项
        } else {
          this.$message.error(res.message || '获取加工类型失败');
        }
      }).catch(err => {
        console.error('getProcessList error:', err);
        this.$message.error('网络异常，稍后重试');
      });
    },
    async getPlanInfo(applyNo) {
      try {
        const response = await detailPlan(applyNo);
        console.log("detailPlan", response);
        this.planForm = response.data;

        // 从计划信息中获取planType和measureFlag
        this.planType = this.planForm.planType;
        this.measureFlag = this.planForm.measureFlag;
        console.log("this.planType", this.planType);
        console.log("this.measureFlag", this.measureFlag);

        await this.getDirectSupplyList();
        return response;
      } catch (error) {
        console.error('getPlanInfo error:', error);
        throw error;
      }
    },
    openFactoryConfirmDialog() {
      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');
      // 初始化表单数据
      this.factoryConfirmForm = {
        companyName: this.taskInfoForm.companyName,
        gross: this.taskInfoForm.gross,
        secGross: this.taskInfoForm.secGross,
        tare: this.taskInfoForm.tare,
        taskNo: this.taskNo,
        applyNo: this.applyNo,
        planNo: this.taskInfoForm.planNo,
        unloadingWorkNo: '',
        unloadingTime: new Date(),
        spec1Length: null,
        spec2Width: null,
        totals: '',
        total: '',
        totalUnit: '',
        processType: '',
        heatNo: '',
        steelGrade: '',
        axles: '',
        remark: '',
        taskStatus: 9,
        carNum: this.taskInfoForm.carNum, // 初始化车牌号
        handledMaterialName: handledMaterialName,
        sourceCompany: this.planForm.sourceCompany,
        receiveCompany: this.planForm.receiveCompany,
        showDropdown: false, // 是否启用额外选项
        extraOption: '', // 额外选项的值
        // 出库信息
        stockOutSpec1Length: null,
        stockOutSpec2Width: null,
        stockOutTotals: '',
        stockOutTotalUnit: '',
        stockOutTotal: '',
        stockOutProcessType: '',
        stockOutHeatNo: '',
        stockOutSteelGrade: '',
        stockOutAxles: '',
        stockOutRemark: '',
        deductWeight: null, // 添加扣重字段初始化
      };
      this.factoryConfirmDialogVisible = true;
    },
    submitFactoryConfirm() {
      if (this.factoryConfirmForm.showDropdown == true) {
        if (this.factoryConfirmForm.extraOption == null || this.factoryConfirmForm.extraOption == '') {
          this.$message.error('请选择额外选项');
          return;
        }
      }

      let submitData = {};
      if (this.taskInfoForm.isDirectSupply == 3) {
        // 构建提交数据
        submitData = {
          leaveTask: {
            id: this.dispatchId,
            taskNo: this.taskNo,
            applyNo: this.applyNo,
            //入库信息
            spec1Length: this.factoryConfirmForm.spec1Length,
            spec2Width: this.factoryConfirmForm.spec2Width,
            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,
            processType: this.factoryConfirmForm.processType,
            heatNo: this.factoryConfirmForm.heatNo,
            steelGrade: this.factoryConfirmForm.steelGrade,
            axles: this.factoryConfirmForm.axles,
            remark: this.factoryConfirmForm.remark,
            carNum: this.taskInfoForm.carNum,
            driverName: this.taskInfoForm.driverName,
            isDirectSupply: 3,
            planNo: this.taskInfoForm.planNo,
            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段

            // 出库信息
            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,
            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,
            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,
            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,
            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,
            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,
            stockOutAxles: this.factoryConfirmForm.stockOutAxles,
            stockOutRemark: this.factoryConfirmForm.stockOutRemark,
            // 更改任务状态: 9
            // todo 任务状态如何变化
            taskStatus: 8,
            taskType: this.taskInfoForm.taskType,
          },
          leavePlan: this.planForm,
          leaveTaskMaterial: this.taskMaterials[0],
        };
      } else {
        // 构建提交数据
        submitData = {
          leaveTask: {
            id: this.dispatchId,
            taskNo: this.taskNo,
            applyNo: this.applyNo,
            planNo: this.taskInfoForm.planNo,
            //入库信息
            spec1Length: this.factoryConfirmForm.spec1Length,
            spec2Width: this.factoryConfirmForm.spec2Width,
            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,
            processType: this.factoryConfirmForm.processType,
            heatNo: this.factoryConfirmForm.heatNo,
            steelGrade: this.factoryConfirmForm.steelGrade,
            axles: this.factoryConfirmForm.axles,
            remark: this.factoryConfirmForm.remark,
            carNum: this.taskInfoForm.carNum,
            driverName: this.taskInfoForm.driverName,
            isDirectSupply: 0, // 默认不是直供
            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段
            directSupplyTaskNo: this.factoryConfirmForm.extraOption,
            // 出库信息
            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,
            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,
            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,
            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,
            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,
            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,
            stockOutAxles: this.factoryConfirmForm.stockOutAxles,
            stockOutRemark: this.factoryConfirmForm.stockOutRemark,
            // 更改任务状态: 9
            // todo 任务状态如何变化
            taskStatus: 8,
            taskType: this.taskInfoForm.taskType,
          },
          leavePlan: this.planForm,
          leaveTaskMaterial: this.taskMaterials[0],
        };
      }



      let directSupplyTask = {
        //taskNo后台雪花生成
        applyNo: this.factoryConfirmForm.extraOption,
        taskType: 3,
        taskStatus: 7,
        secGross: this.taskInfoForm.secGross,
        secGrossTime: this.taskInfoForm.secGrossTime,
        planNo: this.taskInfoForm.planNo,
        driverName: this.taskInfoForm.driverName,
        sex: this.taskInfoForm.sex,
        mobilePhone: this.taskInfoForm.mobilePhone,
        idCardNo: this.taskInfoForm.idCardNo,
        carNum: this.taskInfoForm.carNum,
        vehicleEmissionStandards: this.taskInfoForm.vehicleEmissionStandards,
        faceImg: this.taskInfoForm.faceImg,
        drivingLicenseImg: this.taskInfoForm.drivingLicenseImg,
        driverLicenseImg: this.taskInfoForm.driverLicenseImg,
        companyName: this.taskInfoForm.companyName,
        isDirectSupply: 3
      };

      let directSupplyTaskMaterialList = this.taskMaterials;

      if (this.factoryConfirmForm.showDropdown == true && this.factoryConfirmForm.extraOption != null && this.factoryConfirmForm.extraOption != '') {
        submitData.leaveTask.isDirectSupply = 1; // 设置为直供
        submitData.directSupplyTask = directSupplyTask;
        submitData.directSupplyTaskMaterialList = directSupplyTaskMaterialList;
      }

      handleUnload(submitData).then(res => {
        console.log("handleUnload", res)
        if (res.code == 200) {
          this.$message.success('确认入库成功');
          this.factoryConfirmDialogVisible = false;
          this.getTaskLogList(this.taskNo);
          this.getTaskInfo();
        } else {
          // 其他失败原因
          this.$message.error(res.message || '确认入库失败');
        }
      }).catch(err => {
        console.error('handleDirectSupply error:', err);
        this.$message.error('网络异常，稍后重试');
      });
    },

    submitStockOutConfirm() {

      // 判断用户角色权限
      const roles = this.$store.getters.roles;
      if (!roles.includes('leave.unloading')) {
        this.$message.error('您没有确认出库权限');
        return;
      }
      // 构建提交数据
      let submitData = {
        leaveTask: {
          //todo 计量系统补充信息待完善
          id: this.dispatchId,
          taskNo: this.taskNo,
          applyNo: this.applyNo,
          planNo: this.taskInfoForm.planNo,
          // 出库信息
          stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,
          stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,
          stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,
          stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,
          stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,
          stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,
          stockOutAxles: this.factoryConfirmForm.stockOutAxles,
          stockOutRemark: this.factoryConfirmForm.stockOutRemark,

          // 更改任务状态: 9
          taskStatus: 3,
          carNum: this.taskInfoForm.carNum,
        },
        leavePlan: this.planForm,
        leaveTaskMaterial: this.taskMaterials[0],
      };

      handleStockOut(submitData).then(res => {
        console.log("handleStockOut", res)
        if (res.code == 200) {
          this.$message.success('确认出库成功');
          this.factoryConfirmDialogVisible = false;
          this.getTaskLogList(this.taskNo);
          this.getTaskInfo();
        } else {
          // 其他失败原因
          this.$message.error(res.message || '确认出库失败');
        }
      }).catch(err => {
        console.error('handleDirectSupply error:', err);
        this.$message.error('网络异常，稍后重试');
      });
    },

    handleFactoryConfirm() {
      if (this.editFactoryStatus) {
        this.$message.warning('请先保存');
        return
      }


      //todo
      //生成派车日志
      let leaveTaskLog = {};
      leaveTaskLog.logType = 2;
      leaveTaskLog.taskNo = this.taskNo;
      leaveTaskLog.applyNo = this.applyNo;
      leaveTaskLog.info = '分厂确认数量';


      let factoryTaskInfo = {}
      //todo 出入场
      factoryTaskInfo.id = this.taskInfoForm.id
      factoryTaskInfo.unloadingWorkNo = '卸货人占位符'
      factoryTaskInfo.unloadingTime = new Date()
      factoryTaskInfo.taskStatus = 9

      let param = {};
      param.taskMaterialList = this.taskMaterials;
      param.leaveLog = leaveTaskLog;
      param.leaveTask = factoryTaskInfo;
      param.measureFlag = this.measureFlag;

      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {
        console.log("addLeaveLogAndEditTaskMaterialsAndUpdateTask", res)
        if (res.code == 200) {
          this.$message.success('分厂确认成功');
          this.getTaskLogList(this.taskNo);
          this.getTaskInfo();
        } else {
          // 其他失败原因
          this.$message.error(res.message || '分厂确认成功');
        }
      }).catch(err => {
        console.error('handleFactoryConfirm error:', err);
        this.$message.error('网络异常，稍后重试');
      });
    },


    handleDoorManConfirm() {
      if (this.editDoorManStatus) {
        this.$message.warning('请先保存');
        return
      }

      let leaveTaskLog = {};
      leaveTaskLog.logType = 2;
      leaveTaskLog.taskNo = this.taskNo;
      leaveTaskLog.applyNo = this.applyNo;
      leaveTaskLog.info = '门卫确认数量';



      let doorManTaskInfo = {}
      doorManTaskInfo.id = this.taskInfoForm.id
      if (this.taskInfoForm.taskType == 1) {
        doorManTaskInfo.taskStatus = 9
        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //离厂大门
      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {
        doorManTaskInfo.taskStatus = 7
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {
        doorManTaskInfo.taskStatus = 6
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {
        doorManTaskInfo.taskStatus = 5
        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //离厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {
        doorManTaskInfo.taskStatus = 7
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {
        doorManTaskInfo.taskStatus = 6
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      }

      let param = {};
      param.taskMaterialList = this.taskMaterials;
      param.leaveLog = leaveTaskLog;
      param.leaveTask = doorManTaskInfo;
      param.measureFlag = this.measureFlag;

      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {
        console.log("addLeaveLogAndEditTaskMaterialsAndUpdateTask", res)
        if (res.code == 200) {
          this.$message.success('门卫确认成功');
          this.getTaskLogList(this.taskNo);
          this.getTaskInfo();
        } else {
          // 其他失败原因
          this.$message.error(res.message || '门卫确认成功');
        }
      }).catch(err => {
        console.error('handleDoorManConfirm error:', err);
        this.$message.error('网络异常，稍后重试');
      });

      // this.taskMaterials.map(item => {
      //   editTaskmaterials(item);
      // })
      //todo
      // let leaveTaskLog = {};
      // leaveTaskLog.logType = 2;
      leaveTaskLog.taskNo = this.taskNo;
      leaveTaskLog.applyNo = this.applyNo;
      leaveTaskLog.info = '门卫确认数量';
      // addLeaveLog(leaveTaskLog);
      // this.getTaskLogList(this.taskNo);

      // let doorManTaskInfo = {}
      // doorManTaskInfo.id = this.taskInfoForm.id
      // if (this.taskInfoForm.taskType == 1) {
      //   doorManTaskInfo.taskStatus = 9
      //   doorManTaskInfo.leaveTime = new Date()
      //   //离厂大门
      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {
      //   doorManTaskInfo.taskStatus = 7
      //   doorManTaskInfo.enterTime = new Date()
      //   //出厂大门
      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {
      //   doorManTaskInfo.taskStatus = 6
      //   doorManTaskInfo.enterTime = new Date()
      //   //出厂大门
      // } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {
      //   doorManTaskInfo.taskStatus = 5
      //   doorManTaskInfo.leaveTime = new Date()
      //   //离厂大门
      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {
      //   doorManTaskInfo.taskStatus = 7
      //   doorManTaskInfo.enterTime = new Date()
      //   //出厂大门
      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {
      //   doorManTaskInfo.taskStatus = 6
      //   doorManTaskInfo.enterTime = new Date()
      //   //出厂大门
      // }
      // updateTask(doorManTaskInfo);
      // this.$message.success('门卫确认成功');

      // setTimeout(() => {
      //   this.getTaskInfo();
      // }, 500)

    },

    handleDoorManMeasureConfirm() {
      // 判断用户角色权限
      const roles = this.$store.getters.roles;
      if (!roles.includes('leave.guard')) {
        this.$message.error('您没有门卫出厂确认权限');
        return;
      }

      let leaveTaskLog = {};
      leaveTaskLog.logType = 2;
      leaveTaskLog.taskNo = this.taskNo;
      leaveTaskLog.applyNo = this.applyNo;
      if (this.taskInfoForm.taskStatus == 4) {
        leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');
      } else {
        leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');
      }

      let doorManTaskInfo = {}
      doorManTaskInfo.id = this.taskInfoForm.id
      if (this.taskInfoForm.taskType == 1) {
        doorManTaskInfo.taskStatus = 9
        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //离厂大门
      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {
        doorManTaskInfo.taskStatus = 7
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {
        doorManTaskInfo.taskStatus = 6
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {
        doorManTaskInfo.taskStatus = 5
        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //离厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {
        doorManTaskInfo.taskStatus = 7
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {
        doorManTaskInfo.taskStatus = 6
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      }

      let param = {};
      param.taskMaterialList = this.taskMaterials;
      param.leaveLog = leaveTaskLog;
      param.leaveTask = doorManTaskInfo;
      param.measureFlag = this.measureFlag;

      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {
        console.log("addLeaveLogAndEditTaskMaterialsAndUpdateTask", res)
        if (res.code == 200) {
          this.$message.success('门卫确认成功');
          this.getTaskLogList(this.taskNo);
          this.getTaskInfo();
        } else {
          // 其他失败原因
          this.$message.error(res.message || '门卫确认成功');
        }
      }).catch(err => {
        console.error('handleDoorManConfirm error:', err);
        this.$message.error('网络异常，稍后重试');
      });
      //todo

    },
    // 生成二维码
    creatQrCode() {
      if (this.taskInfoForm.qrCodeContent) {
        this.$refs.qrCode.innerHTML = "";
        var YSqrCode = new QRCode(this.$refs.qrCode, {
          text: this.taskInfoForm.qrCodeContent, // 需要转换为二维码的内容
          width: 120,
          height: 120,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H,
        });
      }
    },
    getTaskLogList(taskNo) {
      let taskLog = {};
      taskLog.taskNo = taskNo
      getTaskLogs(taskLog).then(response => {
        console.log("getTaskLogs", response);
        // this.taskLogs = response.rows;
        let logs = response.rows || [];
        // 找出包含"任务完成"的日志
        const finishedLogs = logs.filter(log => log.info && log.info.includes('任务完成'));
        const otherLogs = logs.filter(log => !(log.info && log.info.includes('任务完成')));
        // 先放"任务完成"，再放其他
        this.taskLogs = [...finishedLogs, ...otherLogs];
      })

    },
    async getTaskmaterialList(taskNo) {
      try {
        console.log("getTaskmaterialList");
        let leaveMaterial = {};
        leaveMaterial.taskNo = taskNo;
        const response = await getTaskmaterials(leaveMaterial);
        this.taskMaterials = response.rows;
        // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn
        this.taskMaterials.forEach(item => {
          item.doormanReceiveNum = item.planNum;
          console.log("item.planType", this.planForm.planType);
          if (this.planForm.planType == 2 || this.planForm.planType == 3) {
            item.doormanReceiveNumIn = item.planNum;
          }
        });
        console.log("taskMaterials", this.taskMaterials);
        return response;
      } catch (error) {
        console.error('getTaskmaterialList error:', error);
        throw error;
      }
    },
    editDoorManRow(row) {
      row._backup = JSON.parse(JSON.stringify(row));//深拷贝
      this.editingRow = row;
      this.editDoorManStatus = true;
      console.log("this.editDoorManRow", row);
    },
    editFactoryRow() {
      this.backupMaterials = JSON.parse(JSON.stringify(this.taskMaterials));//深拷贝
      this.editFactoryStatus = true;
    },
    cancelDoorManEdit(row) {
      //深拷贝
      if (row._backup) {
        // 恢复备份数据
        Object.assign(row, row._backup);
        delete row._backup; // 删除备份数据
      };
      this.editingRow = null; // 清空当前编辑行
      this.editDoorManStatus = false;
    },
    cancelFactoryEdit() {
      this.taskMaterials = JSON.parse(JSON.stringify(this.backupMaterials));//深拷贝
      console.log("this.taskMaterials", this.taskMaterials);
      this.editFactoryStatus = false;
    },

    saveDoorManRowIn() {
      // 判断用户角色权限
      const roles = this.$store.getters.roles;
      if (!roles.includes('leave.guard')) {
        this.$message.error('您没有门卫出厂确认权限');
        return;
      }

      if (this.taskMaterials.length == 0) {
        console.log("taskMaterials", this.taskMaterials);
        this.$message.warning('物资异常');
        return
      }

      // 校验doormanReceiveNumIn是否等于planNum
      for (const item of this.taskMaterials) {
        if (item.doormanReceiveNumIn !== item.planNum) {
          this.$message.warning(`物资"${item.materialName}"的门卫入厂确认数量(${item.doormanReceiveNumIn})与计划数量(${item.planNum})不一致，请检查`);
          return;
        }
      }

      let leaveTaskLog = {};
      leaveTaskLog.logType = 2;
      leaveTaskLog.taskNo = this.taskNo;
      leaveTaskLog.applyNo = this.applyNo;
      leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');

      let doorManTaskInfo = {}
      doorManTaskInfo.id = this.taskInfoForm.id;
      if (this.taskInfoForm.taskType == 1) {
        doorManTaskInfo.taskStatus = 9
        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //离厂大门
      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {
        doorManTaskInfo.taskStatus = 7
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {
        doorManTaskInfo.taskStatus = 6
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {
        doorManTaskInfo.taskStatus = 5
        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //离厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {
        doorManTaskInfo.taskStatus = 7
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {
        doorManTaskInfo.taskStatus = 6
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      }

      let param = {
        taskMaterialList: this.taskMaterials,
        leaveLog: leaveTaskLog,
        leaveTask: doorManTaskInfo,
        measureFlag: this.measureFlag
      };

      console.log("addLeaveLogAndEditTaskMaterialsAndUpdateTask", param, this.taskInfoForm.taskType);


      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {
        console.log("addLeaveLogAndEditTaskMaterialsAndUpdateTask", res)
        if (res.code == 200) {
          this.$message.success('门卫确认成功');
          this.getTaskLogList(this.taskNo);
          this.getTaskInfo();
        } else {
          // 其他失败原因
          this.$message.error(res.message || '门卫确认成功');
        }
      }).catch(err => {
        console.error('handleDoorManConfirm error:', err);
        this.$message.error('网络异常，稍后重试');
      });

      this.editDoorManStatus = false;
    },

    saveDoorManRow() {
      // 判断用户角色权限
      const roles = this.$store.getters.roles;
      console.log("roles", roles);
      if (!roles.includes('leave.guard')) {
        this.$message.error('您没有门卫出厂确认权限');
        return;
      }

      if (this.taskMaterials.length == 0) {
        console.log("taskMaterials", this.taskMaterials);
        this.$message.warning('物资异常');
        return
      }

      // 校验doormanReceiveNum是否等于planNum
      for (const item of this.taskMaterials) {
        if (item.doormanReceiveNum !== item.planNum) {
          this.$message.warning(`物资"${item.materialName}"的门卫确认数量(${item.doormanReceiveNum})与计划数量(${item.planNum})不一致，请检查`);
          return;
        }
      }

      let leaveTaskLog = {};
      leaveTaskLog.logType = 2;
      leaveTaskLog.taskNo = this.taskNo;
      leaveTaskLog.applyNo = this.applyNo;
      leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');

      let doorManTaskInfo = {}
      doorManTaskInfo.id = this.taskInfoForm.id
      if (this.taskInfoForm.taskType == 1) {
        doorManTaskInfo.taskStatus = 9
        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //离厂大门
      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {
        doorManTaskInfo.taskStatus = 7
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {
        doorManTaskInfo.taskStatus = 6
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {
        doorManTaskInfo.taskStatus = 5
        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //离厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {
        doorManTaskInfo.taskStatus = 7
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {
        doorManTaskInfo.taskStatus = 6
        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        //出厂大门
      }

      let param = {
        taskMaterialList: this.taskMaterials,
        leaveLog: leaveTaskLog,
        leaveTask: doorManTaskInfo,
        measureFlag: this.measureFlag
      };

      console.log("addLeaveLogAndEditTaskMaterialsAndUpdateTask", param, this.taskInfoForm.taskType);


      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {
        console.log("addLeaveLogAndEditTaskMaterialsAndUpdateTask", res)
        if (res.code == 200) {
          this.$message.success('门卫确认成功');
          this.getTaskLogList(this.taskNo);
          this.getTaskInfo();
        } else {
          // 其他失败原因
          this.$message.error(res.message || '门卫确认成功');
        }
      }).catch(err => {
        console.error('handleDoorManConfirm error:', err);
        this.$message.error('网络异常，稍后重试');
      });

      this.editDoorManStatus = false;
    },


    saveFactoryRow() {

      this.editFactoryStatus = false;
    },

    resetTaskInfoForm() {
      this.taskInfoForm = {};
    },

    async getTaskInfo() {
      try {
        const response = await getTask(this.dispatchId);
        this.taskInfoForm = response.data;
        console.log("this.taskInfoForm", this.taskInfoForm);
        if (this.taskInfoForm.licensePlateColor == 1) {
          this.taskInfoForm.licensePlateColor = '蓝色'
        } else if (this.taskInfoForm.licensePlateColor == 2) {
          this.taskInfoForm.licensePlateColor = '绿色'
        } else if (this.taskInfoForm.licensePlateColor == 3) {
          this.taskInfoForm.licensePlateColor = '黄'
        } else if (this.taskInfoForm.licensePlateColor == 4) {
          this.taskInfoForm.licensePlateColor = '黄绿色'
        }
        console.log("this.taskInfoForm", this.taskInfoForm);
        // 生成二维码
        this.$nextTick(() => {
          this.creatQrCode();
        });
        return response;
      } catch (error) {
        console.error('getTaskInfo error:', error);
        throw error;
      }
    },

    async getTaskInfoByTaskNo() {
      try {
        const response = await getTaskByTaskNo(this.taskNo);
        this.taskInfoForm = response.data;
        console.log("this.taskInfoForm", this.taskInfoForm);

        // 从返回的数据中获取所需的参数
        this.dispatchId = this.taskInfoForm.id;
        this.applyNo = this.taskInfoForm.applyNo;

        if (this.taskInfoForm.licensePlateColor == 1) {
          this.taskInfoForm.licensePlateColor = '蓝色'
        } else if (this.taskInfoForm.licensePlateColor == 2) {
          this.taskInfoForm.licensePlateColor = '绿色'
        } else if (this.taskInfoForm.licensePlateColor == 3) {
          this.taskInfoForm.licensePlateColor = '黄'
        } else if (this.taskInfoForm.licensePlateColor == 4) {
          this.taskInfoForm.licensePlateColor = '黄绿色'
        }
        console.log("this.taskInfoForm", this.taskInfoForm);
        // 生成二维码
        this.$nextTick(() => {
          this.creatQrCode();
        });
        return response;
      } catch (error) {
        console.error('getTaskInfoByTaskNo error:', error);
        throw error;
      }
    },


    getStatusText(standard) {
      const standardMap = {
        1: '待过皮重',
        2: '待装货',
        3: '待过毛重',
        4: '待出厂',
        5: '待返厂',
        6: '待过毛重(复磅)',
        7: '待卸货',
        8: '待过皮重(复磅)',
        9: '完成'
      };
      return standardMap[standard] || '未知';
    },

    //计划状态
    getPlanStatusText(standard) {
      const standardMap = {
        1: '待分厂审批',
        2: '待分厂复审',
        3: '待生产指挥中心审批',
        4: '审批完成',
        5: '已出厂',
        6: '部分收货',
        7: '已完成',
        11: '驳回',
        12: '废弃',
        13: '过期',
        '待分厂审批': '待分厂审批',
        '待分厂复审': '待分厂复审',
        '待生产指挥中心审批': '待生产指挥中心审批',
        '审批完成': '审批完成',
        '已出厂': '已出厂',
        '部分收货': '部分收货',
        '已完成': '已完成',
        '驳回': '驳回',
        '废弃': '废弃',
        '过期': '过期',
      };
      return standardMap[standard] || '未知';
    },
    // 获取排放标准文本
    getEmissionStandardsText(standard) {
      const standardMap = {
        1: '国五',
        2: '国六',
        3: '新能源'
      };
      return standardMap[standard] || '未知';
    },

    // 获取排放标准标签类型
    getEmissionStandardsTagType(standard) {
      const typeMap = {
        1: 'warning',  // 国五
        2: 'success',  // 国六
        3: 'primary'   // 新能源
      };
      return typeMap[standard] || 'info';
    },

    // 获取物资状态文本
    getMaterialStatusText(status) {
      const statusMap = {
        1: '待装载',
        2: '已装载',
        3: '已签收',
        4: '异常'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取物资状态标签类型
    getMaterialStatusType(status) {
      const typeMap = {
        1: 'info',     // 待装载
        2: 'warning',  // 已装载
        3: 'success',  // 已签收
        4: 'danger'    // 异常
      };
      return typeMap[status] || 'info';
    },

    // 获取日志颜色
    getLogColor(log) {
      const logTypeColorMap = {
        1: '#409EFF', // 创建
        2: '#E6A23C', // 更新
        3: '#67C23A', // 完成
        4: '#F56C6C', // 异常
        5: '#909399'  // 其他
      };
      return logTypeColorMap[log.type] || '#409EFF';
    },

    // 返回按钮
    cancel() {
      this.$router.go(-1);
    },

    // 获取任务详情数据
    getTaskDetail(dispatchId) {
      // 实际项目中这里需要调用API获取数据
      // getDispatchTaskDetail(dispatchId).then(response => {
      //   const { driverInfo, carInfo, taskMaterials, taskLogs } = response.data;
      //   this.driverInfo = driverInfo;
      //   this.carInfo = carInfo;
      //   this.taskMaterials = taskMaterials;
      //   this.taskLogs = taskLogs;
      // });
    },
    handleShowDropdownChange(val) {
      if (!val) {
        this.factoryConfirmForm.extraOption = '';
      }
    },
    openOptionDialog() {
      this.optionDialogVisible = true;
      this.loadOptions();
      // 重置选中状态
      this.selectedOption = null;
      this.$nextTick(() => {
        if (this.$refs.optionTable) {
          this.$refs.optionTable.clearSelection();
        }
      });
    },
    handleOptionSelection(selection) {
      // 只保留最后选中的一项
      if (selection.length > 1) {
        const lastSelected = selection[selection.length - 1];
        this.$refs.optionTable.clearSelection();
        this.$refs.optionTable.toggleRowSelection(lastSelected, true);
        this.selectedOption = lastSelected;
      } else {
        this.selectedOption = selection[0];
      }
    },
    confirmOptionSelection() {
      if (!this.selectedOption) {
        this.$message.warning('请选择一个选项');
        return;
      }

      this.factoryConfirmForm.extraOption = this.selectedOption.applyNo;

      // let dispatchInfo = {};
      // dispatchInfo.carNum = this.taskInfoForm.carNum;
      // dispatchInfo.isDirectSupply = 1;

      // isAllowDispatch(dispatchInfo).then(response => {
      //   let row = response.data;
      //   if (row > 0) {
      //     this.$message.error("当前车有正在执行的任务")
      //     return;
      //   } else {
      //     this.optionDialogVisible = false;
      //     this.$message.success('选项已确认');
      //   }
      //   console.log("this.isAllowDispatch", response);
      // }).catch(err => {
      //   console.error('dispatch error:', err);
      //   this.$message.error('网络异常，稍后重试');
      // });

      this.optionDialogVisible = false;
      this.$message.success('选项已确认');



    },
    loadOptions() {
      // 这里应该调用API获取leave_plan表的数据
      this.optionList = this.directSupplyPlanList; // 使用直供计划列表作为选项数据\
      this.optionList.forEach(item => {
        item.planStatus = this.getPlanStatusText(item.planStatus);
      });
      console.log("optionList", this.optionList)
    },
    getBusinessCategoryText(category) {
      const categoryMap = {
        1: '通用（出厂不返回）',
        11: '通用（出厂返回）',
        12: '委外加工（出厂返回）',
        21: '有计划量计量（跨区调拨）',
        22: '短期（跨区调拨）',
        23: '钢板（圆钢）（跨区调拨）',
        31: '通用（退货申请）'
      };
      return categoryMap[category] || '未知类型';
    },
    searchOptions() {
      // 取出并转小写
      const searchPlanNo = (this.searchForm.planNo || '').toLowerCase();
      const searchApplyNo = (this.searchForm.applyNo || '').toLowerCase();
      const searchReceiveCompany = (this.searchForm.receiveCompany || '').toLowerCase();

      // 过滤
      this.optionList = this.directSupplyPlanList.filter(item => {
        const planNo = (item.planNo || '').toString().toLowerCase();
        const applyNo = (item.applyNo || '').toString().toLowerCase();
        const receiveCompany = (item.receiveCompany || '').toString().toLowerCase();

        // 为空不作为条件
        const matchPlanNo = !searchPlanNo || planNo.includes(searchPlanNo);
        const matchApplyNo = !searchApplyNo || applyNo.includes(searchApplyNo);
        const matchReceiveCompany = !searchReceiveCompany || receiveCompany.includes(searchReceiveCompany);

        return matchPlanNo && matchApplyNo && matchReceiveCompany;
      });

      // 更新状态显示
      this.optionList.forEach(item => {
        item.planStatus = this.getPlanStatusText(item.planStatus);
      });
    },
    resetSearch() {
      this.searchForm = {
        planNo: '',
        applyNo: '',
        receiveCompany: ''
      };
      this.loadOptions(); // 重新加载所有数据
    },
    getTaskTypeText(type) {
      const typeMap = {
        1: '出厂',
        2: '返厂',
        3: '跨区调拨'
      };
      return typeMap[type] || '未知';
    },
    // // 判断行是否可选
    // isSelectable(row) {
    //   // 当门卫确认数量不为0时，该行可选
    //   return row.doormanReceiveNum > 0 && this.taskInfoForm.taskStatus !== 9;
    // },

    // 表格选择变化时的处理函数
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 处理非计量分厂确认
    handleNonMeasureFactoryConfirm() {
      const roles = this.$store.getters.roles;
      if (!roles.includes('leave.unloading')) {
        this.$message.error('您没有门卫出厂确认权限');
        return;
      }
      let isHandled = false;
      this.selectedRows.forEach(item => {
        if (item.doormanReceiveNum !== item.planNum) {
          this.$message.warning('门卫确认数量和计划数量不一致，请检查');
          isHandled = true;
        }
      });

      if (isHandled) {
        return;
      }

      // if (this.selectedRows.length === 0) {
      //   this.$message.warning('请选择需要确认的物资');
      //   return;
      // }

      // 生成派车日志
      let leaveTaskLog = {
        logType: 2,
        taskNo: this.taskNo,
        applyNo: this.applyNo,
        info: '分厂接收确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ')
      };

      // 构建任务信息
      let factoryTaskInfo = {
        id: this.taskInfoForm.id,
        unloadingWorkNo: '卸货人占位符',//后端updateLeaveTask方法
        unloadingTime: new Date(),
        taskStatus: 9
      };

      this.selectedRows.forEach(item => {
        // 设置非计量分厂确认数量
        item.factoryReceiveNum = item.doormanReceiveNum;
      });

      // 构建请求参数
      let param = {
        taskMaterialList: this.selectedRows, // 使用选中的行数据
        leaveLog: leaveTaskLog,
        leaveTask: factoryTaskInfo,
        measureFlag: this.measureFlag
      };

      // 发送请求
      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {
        if (res.code == 200) {
          this.$message.success('非计量分厂确认成功');
          this.getTaskLogList(this.taskNo);
          this.getTaskInfo();
          // 清空选中状态
          this.selectedRows = [];
        } else {
          this.$message.error(res.message || '非计量分厂确认失败');
        }
      }).catch(err => {
        console.error('handleNonMeasureFactoryConfirm error:', err);
        this.$message.error('网络异常，稍后重试');
      });
    },
    openNewTaskWindow() {
      console.log("openNewTaskWindow", this.directSupplyParams);
      let dispatchId = this.directSupplyParams.dispatchId;
      let applyNo = BigInt(this.directSupplyParams.applyNo);
      let measureFlag = this.directSupplyParams.measureFlag;
      let planType = this.directSupplyParams.planType;
      let taskNo = BigInt(this.directSupplyParams.taskNo);
      const url = `http://localhost/leave/plan/task?dispatchId=${dispatchId}&applyNo=${applyNo}&measureFlag=${measureFlag}&planType=${planType}&taskNo=${taskNo}`;
      window.open(url, '_blank');
    },
  }
};
</script>

<style scoped>
.btn-wrapper {
  display: flex;
  justify-content: center;
}

.dispatch-btn {
  margin-left: 15px;
}

.app-container {
  padding: 20px;
}

.qrcode-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.qrcode {
  margin: 0 auto;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-container {
  margin-bottom: 30px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #ebeef5;
}

.section-container:nth-child(1) {
  border-top: 4px solid #F56C6C;
  /* 通行证二维码模块 - 红色 */
}

.section-container:nth-child(2) {
  border-top: 4px solid #409EFF;
  /* 司机信息模块 - 蓝色 */
}

.section-container:nth-child(3) {
  border-top: 4px solid #67C23A;
  /* 车辆信息模块 - 绿色 */
}

.section-container:nth-child(4) {
  border-top: 4px solid #E6A23C;
  /* 物资列表模块 - 橙色 */
}

.section-container:nth-child(5) {
  border-top: 4px solid #909399;
  /* 日志列表模块 - 灰色 */
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  padding: 15px 20px;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
  position: relative;
  padding-left: 30px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: currentColor;
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 2px;
}

.section-container:nth-child(1) .section-title {
  color: #F56C6C;
}

.section-container:nth-child(2) .section-title {
  color: #409EFF;
}

.section-container:nth-child(3) .section-title {
  color: #67C23A;
}

.section-container:nth-child(4) .section-title {
  color: #E6A23C;
}

.section-container:nth-child(5) .section-title {
  color: #909399;
}

.section-container .el-descriptions,
.section-container .el-table,
.section-container .el-timeline {
  padding: 0 20px 20px;
}

.form-footer {
  margin-top: 30px;
  text-align: center;
}

.driver-photos {
  padding: 0 20px 20px;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.photo-item {
  width: 300px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.photo-item h4 {
  padding: 10px;
  background: #f5f7fa;
  margin: 0;
  border-bottom: 1px solid #ebeef5;
}

.photo-container {
  padding: 10px;
  display: flex;
  justify-content: center;
}

.photo-container img {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.button-container {
  margin-top: 20px;
  text-align: center;
}
</style>

<style lang="scss">
.el-table {
  border-radius: 4px;
  overflow: hidden;

  th {
    background-color: #fafafa !important;
    color: #606266;
    font-weight: bold;
  }

  td {
    padding: 12px 0;
  }
}



.el-timeline {
  padding: 20px !important;

  .el-timeline-item__node {
    width: 12px;
    height: 12px;
  }

  .el-timeline-item__content {
    padding: 0 0 0 25px;
  }
}

.el-descriptions {
  .el-descriptions-item__label {
    background-color: #fafafa;
  }
}

.el-tag {
  border-radius: 12px;
  padding: 0 10px;
}
</style>
