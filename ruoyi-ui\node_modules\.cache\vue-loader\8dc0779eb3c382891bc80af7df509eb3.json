{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue?vue&type=template&id=5f88274b", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue", "mtime": 1756170476813}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}