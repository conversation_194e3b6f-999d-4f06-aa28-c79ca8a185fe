package com.ruoyi.app.leave.mapper;

import com.ruoyi.app.leave.domain.LeavePlan;

import java.util.List;

/**
 * 出门证计划申请Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface LeavePlanMapper
{
    /**
     * 查询出门证计划申请
     *
     * @param id 出门证计划申请ID
     * @return 出门证计划申请
     */
    public LeavePlan selectLeavePlanById(Long id);

    /**
     * 查询出门证计划申请列表
     *
     * @param leavePlan 出门证计划申请
     * @return 出门证计划申请集合
     */
    public List<LeavePlan> selectLeavePlanList(LeavePlan leavePlan);

    /**
     * 新增出门证计划申请
     *
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    public int insertLeavePlan(LeavePlan leavePlan);

    /**
     * 修改出门证计划申请
     *
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    public int updateLeavePlan(LeavePlan leavePlan);

    /**
     * 删除出门证计划申请
     *
     * @param id 出门证计划申请ID
     * @return 结果
     */
    public int deleteLeavePlanById(Long id);

    /**
     * 批量删除出门证计划申请
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteLeavePlanByIds(Long[] ids);

    /**
     * 根据申请编号查询出门证计划申请
     *
     * @param applyNo 申请编号
     * @return 出门证计划申请
     */
    public LeavePlan selectLeavePlanByApplyNo(String applyNo);

    /**
     * 查询过期任务
     *
     * @param
     * @return
     */
    List<LeavePlan> selectExpiredList();

    /**
     * 更新过期任务
     *
     * @param leavePlan
     * @return
     */
    int updateExpiredLeavePlan(LeavePlan leavePlan);

    /**
     * 查询待审批列表
     */
    List<LeavePlan> selectWaitApproveList(String workNo);

    /**
     * 根据applyNo更新计划状态
     * @param applyNo 申请编号
     * @param planStatus 状态
     * @return 结果
     */
    int updatePlanStatusByApplyNo(@org.apache.ibatis.annotations.Param("applyNo") String applyNo, @org.apache.ibatis.annotations.Param("planStatus") Integer planStatus);
}
