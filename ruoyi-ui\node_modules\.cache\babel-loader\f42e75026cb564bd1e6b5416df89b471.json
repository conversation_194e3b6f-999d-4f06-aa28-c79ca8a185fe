{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue", "mtime": 1756170476870}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_task", "require", "_plan", "_xctgDriverCar", "_elementUi", "_qrcodejs", "_interopRequireDefault", "name", "data", "factoryConfirmDialogVisible", "factoryConfirmForm", "companyName", "taskNo", "applyNo", "planNo", "taskType", "unloadingWorkNo", "unloadingTime", "spec1Length", "spec2Width", "totals", "total", "totalUnit", "processType", "heatNo", "steelGrade", "axles", "remark", "taskStatus", "carNum", "stockOutSpec1Length", "stockOutSpec2Width", "stockOutTotals", "stockOutTotalUnit", "stockOutTotal", "stockOutProcessType", "stockOutHeatNo", "stockOutSteelGrade", "stockOutAxles", "stockOutRemark", "handledMaterialName", "sourceCompany", "receiveCompany", "showDropdown", "extraOption", "deductWeight", "optionDialogVisible", "searchForm", "optionList", "editDoorManStatus", "editFactoryStatus", "driverInfo", "id", "idCard", "phone", "gender", "company", "photo", "driverLicenseImgs", "vehicleLicenseImgs", "carInfo", "taskMaterials", "taskLogs", "isdoorMan", "dispatchId", "taskInfoForm", "measureFlag", "backupTaskMaterials", "selectedOption", "planForm", "processTypeOptions", "filteredProcessTypeOptions", "searchProcessTypeQuery", "directSupplyPlanList", "editingRow", "selectedRows", "directSupplyParams", "computed", "displayProcessTypeOptions", "hasSelectedItems", "length", "materialNames", "map", "item", "materialName", "join", "materialSpecs", "materialSpec", "activated", "console", "log", "resetTaskInfoForm", "$route", "params", "query", "validDoorMan", "initializeDataByTaskNo", "_this$$route$query", "planType", "queryTaskNo", "initializeData", "methods", "getDirectSupplyPlanAndTask", "_this", "leaveTask0", "directSupplyTaskNo", "getDirectSupplyPlanAndTaskDetail", "then", "res", "code", "rows", "$message", "error", "message", "catch", "err", "_this2", "$store", "getters", "roles", "for<PERSON>ach", "_this3", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "getTaskInfo", "getTaskmaterialList", "getPlanInfo", "uploadFactoryConfirmForm", "getTaskLogList", "getProcessType", "v", "a", "_this4", "_callee2", "_t2", "_context2", "getTaskInfoByTaskNo", "_this5", "doorman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planNum", "doormanReceiveNumIn", "gross", "secGross", "<PERSON><PERSON><PERSON>", "tare", "Date", "openNewWindow", "newWindowUrl", "window", "open", "getDirectSupplyList", "_this6", "_callee3", "leavePlan", "_t3", "_context3", "getDirectSupplyPlans", "filterProcessType", "filter", "value", "includes", "_this7", "getProcessList", "processname", "label", "_this8", "_callee4", "response", "_t4", "_context4", "detailPlan", "openFactoryConfirmDialog", "submitFactoryConfirm", "_this9", "submitData", "isDirectSupply", "leaveTask", "leaveTaskMaterial", "directSupplyTask", "secGrossTime", "sex", "mobilePhone", "idCardNo", "vehicleEmissionStandards", "faceImg", "drivingLicenseImg", "driverLicenseImg", "directSupplyTaskMaterialList", "handleUnload", "success", "submitStockOutConfirm", "_this0", "handleStockOut", "handleFactoryConfirm", "_this1", "warning", "leaveTaskLog", "logType", "info", "factoryTaskInfo", "param", "taskMaterialList", "leaveLog", "addLeaveLogAndEditTaskMaterialsAndUpdateTask", "handleDoorManConfirm", "_this10", "doorManTaskInfo", "leaveTime", "toISOString", "slice", "replace", "enterTime", "handleDoorManMeasureConfirm", "_this11", "creatQrCode", "qrCode<PERSON>ontent", "$refs", "qrCode", "innerHTML", "YSqrCode", "QRCode", "text", "width", "height", "colorDark", "colorLight", "correctLevel", "CorrectLevel", "H", "_this12", "taskLog", "getTaskLogs", "logs", "finishedLogs", "otherLogs", "concat", "_toConsumableArray2", "_this13", "_callee5", "leaveMaterial", "_t5", "_context5", "getTaskmaterials", "editDoorManRow", "row", "_backup", "JSON", "parse", "stringify", "editFactoryRow", "backupMaterials", "cancelDoorManEdit", "Object", "assign", "cancelFactoryEdit", "saveDoorManRowIn", "_this14", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "e", "f", "saveDoorManRow", "_this15", "_iterator2", "_step2", "saveFactoryRow", "_this16", "_callee6", "_t6", "_context6", "getTask", "licensePlateColor", "$nextTick", "_this17", "_callee7", "_t7", "_context7", "getTaskByTaskNo", "getStatusText", "standard", "standardMap", "getPlanStatusText", "getEmissionStandardsText", "getEmissionStandardsTagType", "typeMap", "getMaterialStatusText", "status", "statusMap", "getMaterialStatusType", "getLogColor", "logTypeColorMap", "type", "cancel", "$router", "go", "getTaskDetail", "handleShowDropdownChange", "val", "openOptionDialog", "_this18", "loadOptions", "optionTable", "clearSelection", "handleOptionSelection", "selection", "lastSelected", "toggleRowSelection", "confirmOptionSelection", "_this19", "planStatus", "getBusinessCategoryText", "category", "categoryMap", "searchOptions", "_this20", "searchPlanNo", "toLowerCase", "searchApplyNo", "searchReceiveCompany", "toString", "matchPlanNo", "matchApplyNo", "matchReceiveCompany", "resetSearch", "getTaskTypeText", "handleSelectionChange", "handleNonMeasureFactoryConfirm", "_this21", "isHandled", "factoryReceiveNum", "openNewTaskWindow", "BigInt", "url"], "sources": ["src/views/leave/plan/task.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\" style=\"display: flex; align-items: center; justify-content: flex-start;\">\r\n        <h2>派车任务详情</h2>\r\n        <el-tag size=\"medium\" style=\"margin-left: 20px; margin-top: 10px;\">\r\n          任务状态： {{ getStatusText(taskInfoForm.taskStatus) }}\r\n        </el-tag>\r\n      </div>\r\n\r\n      <!-- 任务流程图部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务流程</div>\r\n        <div class=\"process-flow-container\">\r\n          <!-- <img style=\"width: 100%; max-height: 400px; object-fit: contain;\" :src=\"require('@/assets/images/task-flow-chart.png')\" /> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 通行证二维码部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">通行证二维码</div>\r\n        <div class=\"qrcode-container\">\r\n          <div ref=\"qrCode\" class=\"qrcode\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 司机信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">司机信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"姓名\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 姓名</template>\r\n            {{ taskInfoForm.driverName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">\r\n            <template slot=\"label\"><i class=\"el-icon-mobile-phone\"></i> 手机号</template>\r\n            {{ taskInfoForm.mobilePhone }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"身份证号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 身份证号</template>\r\n            {{ taskInfoForm.idCardNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"性别\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 性别</template>\r\n            {{ taskInfoForm.sex === 1 ? '男' : '女' }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"所属单位\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 所属单位</template>\r\n            {{ taskInfoForm.companyName }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 司机照片和证件照片 -->\r\n        <div class=\"driver-photos\"\r\n          v-if=\"driverInfo.photo || driverInfo.driverLicenseImgs || driverInfo.vehicleLicenseImgs\">\r\n          <div class=\"photo-item\" v-if=\"driverInfo.photo\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 司机照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.faceImg\" alt=\"司机照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.faceImg\" fit=\"contain\" fallback=\"\"\r\n                :preview-src-list=\"[taskInfoForm.faceImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.driverLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 驾驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.driverLicenseImg\" alt=\"驾驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.driverLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.driverLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.vehicleLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 行驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.drivingLicenseImg\" alt=\"行驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.drivingLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.drivingLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 车辆信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">车辆信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"车牌号\" v-if=\"taskInfoForm.carNum != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 车牌号</template>\r\n            <el-tag type=\"primary\">{{ taskInfoForm.carNum }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车牌颜色\" v-if=\"taskInfoForm.licensePlateColor != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-takeaway-box\"></i> 车牌颜色</template>\r\n            {{ taskInfoForm.licensePlateColor }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆道路运输证号\" v-if=\"taskInfoForm.trailerId != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"挂车号牌\" v-if=\"taskInfoForm.trailerNumber\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 挂车号牌</template>\r\n            <el-tag type=\"info\">{{ taskInfoForm.trailerNumber }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"挂车道路运输证号\" v-if=\"taskInfoForm.trailerId\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 挂车运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"轴型\" v-if=\"taskInfoForm.axisType != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-data-line\"></i> 轴型</template>\r\n            {{ taskInfoForm.axisType }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"货车自重\" v-if=\"taskInfoForm.driverWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-heavy-rain\"></i> 货车自重</template>\r\n            {{ taskInfoForm.driverWeight }} kg\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车货总质量限值\" v-if=\"taskInfoForm.maxWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-opportunity\"></i> 总质量限值</template>\r\n            {{ taskInfoForm.maxWeight }} kg\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆排放标准\" v-if=\"taskInfoForm.vehicleEmissionStandards != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-magic-stick\"></i> 排放标准</template>\r\n            <el-tag :type=\"getEmissionStandardsTagType(taskInfoForm.vehicleEmissionStandards)\">\r\n              {{ getEmissionStandardsText(taskInfoForm.vehicleEmissionStandards) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"发动机号\" v-if=\"taskInfoForm.engineNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-set-up\"></i> 发动机号</template>\r\n            {{ taskInfoForm.engineNumber }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆识别代码\" v-if=\"taskInfoForm.vinNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document-checked\"></i> 车辆识别代码</template>\r\n            {{ taskInfoForm.vinNumber }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 任务物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"taskMaterials\" style=\"width: 100%\" border @selection-change=\"handleSelectionChange\">\r\n          <!-- <el-table-column type=\"selection\" width=\"55\" v-if=\"measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"150\">\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0\" -->\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫出厂确认数量\" width=\"230\"\r\n            v-if=\"taskInfoForm.taskStatus >= 4 && measureFlag == 0 && taskInfoForm.taskType != 2\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 4\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNumIn\" label=\"门卫入厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskType !== 1 && taskInfoForm.taskStatus >= 5\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNumIn\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 5\" />\r\n            </template>\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0 && taskInfoForm.taskType == 2\" -->\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"分厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskStatus > 7 && (taskInfoForm.taskType == 3 || taskInfoForm.taskType == 2)\">\r\n            <!-- <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\" disabled />\r\n            </template> -->\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\">\r\n          </el-table-column>\r\n\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5 && (measureFlag == 0 && taskInfoForm.taskType == 2 && taskInfoForm.taskStatus == 7)\" -->\r\n          <!-- <el-table-column v-if=\"measureFlag == 0 && (taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5)\"\r\n            label=\"操作\" width=\"200\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px;\">\r\n\r\n                <div v-if=\"editingRow === scope.row\">\r\n                  <el-button size=\"mini\" type=\"success\" @click=\"saveDoorManRow(scope.row)\">保存</el-button>\r\n                  <el-button size=\"mini\" @click=\"cancelDoorManEdit(scope.row)\">取消</el-button>\r\n                </div>\r\n\r\n                <div v-else>\r\n                  <el-button v-hasPermi=\"['leave:task:doorManConfirm']\" size=\"mini\" type=\"primary\"\r\n                    @click=\"editDoorManRow(scope.row)\">门卫编辑</el-button>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column> -->\r\n        </el-table>\r\n\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 4\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRow\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫出厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 5\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRowIn\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫入厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"button-container\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 7\">\r\n          <el-button type=\"primary\" @click=\"handleNonMeasureFactoryConfirm\">\r\n            分厂确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1\">\r\n        <div class=\"section-title\">计量信息</div>\r\n        <div class=\"info-footer\" style=\"margin-top: 20px;\" v-if=\"measureFlag == 1\">\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"皮重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tare != null\">\r\n              {{ taskInfoForm.tare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.gross != null\">\r\n              {{ taskInfoForm.gross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.netWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tareTime != null\">\r\n              {{ taskInfoForm.tareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.grossTime != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secTare != null\">\r\n              {{ taskInfoForm.secTare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secGross != null\">\r\n              {{ taskInfoForm.secGross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeight != null\">\r\n              {{ taskInfoForm.secNetWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secTareTime != null\">\r\n              {{ taskInfoForm.secTareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secGrossTime != null\">\r\n              {{ taskInfoForm.secGrossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeightTime != null\">\r\n              {{ taskInfoForm.secNetWeightTime }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5\" -->\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 4\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫出厂确认\r\n            </el-button>\r\n          </div>\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 5\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫入厂确认\r\n            </el-button>\r\n          </div>\r\n          <!-- 新增分厂确认按钮 -->\r\n          <!-- <div class=\"btn-wrapper\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"openFactoryConfirmDialog\" class=\"dispatch-btn\">\r\n              分厂确认\r\n            </el-button>\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可编辑的出库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 2\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialName).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialSpec).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\" v-if=\"taskInfoForm.taskType\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSpec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutTotal\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutTotalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutProcessType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutHeatNo\" placeholder=\"请输入炉号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSteelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutAxles\" placeholder=\"请选择轴数\">\r\n                  <el-option label=\"2\" value=\"2\"></el-option>\r\n                  <el-option label=\"3\" value=\"3\"></el-option>\r\n                  <el-option label=\"4\" value=\"4\"></el-option>\r\n                  <el-option label=\"5\" value=\"5\"></el-option>\r\n                  <el-option label=\"6轴标准\" value=\"6轴标准\"></el-option>\r\n                  <el-option label=\"6轴非标准\" value=\"6轴非标准\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.stockOutRemark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitStockOutConfirm\" size=\"medium\" class=\"dispatch-btn\">确认出库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的出库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 2 && taskInfoForm.taskType !== 2 && taskInfoForm.isDirectSupply != 3\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"materialSpecs\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.stockOutSpec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.stockOutTotals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.stockOutProcessType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.stockOutHeatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.stockOutSteelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-input :value=\"taskInfoForm.stockOutAxles\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.stockOutRemark\" disabled></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 可编辑的入库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 7\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.secGross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.gross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.processType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.steelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input v-model=\"factoryConfirmForm.spec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.total\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.totalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.heatNo\" placeholder=\"请输入炉号/批号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重(t)\">\r\n                <el-input v-model=\"factoryConfirmForm.deductWeight\" placeholder=\"请输入扣重\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.remark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"是否直供\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-checkbox v-model=\"factoryConfirmForm.showDropdown\" @change=\"handleShowDropdownChange\">是否直供</el-checkbox>\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"factoryConfirmForm.showDropdown\" label=\"直供申请单号\">\r\n            <el-input v-model=\"factoryConfirmForm.extraOption\" placeholder=\"请选择直供申请单号\" readonly style=\"width: 300px;\">\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openOptionDialog\"></el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitFactoryConfirm\" size=\"medium\" class=\"dispatch-btn\">确认入库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的入库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 7 && taskInfoForm.taskType !== 1\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.gross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.processType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.steelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.spec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.totals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.heatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重\">\r\n                <el-input :value=\"taskInfoForm.deductWeight\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.remark\" disabled></el-input>\r\n          </el-form-item>\r\n\r\n\r\n          <el-form-item v-if=\"taskInfoForm.directSupplyTaskNo\" label=\"直供对应任务单号\">\r\n            <el-input :value=\"taskInfoForm.directSupplyTaskNo\" disabled style=\"width: 300px;\"></el-input>\r\n            <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n              @click=\"openNewTaskWindow\">前往任务单号</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务日志</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in taskLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <div class=\"form-footer\">\r\n        <el-button @click=\"cancel\">返 回</el-button>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 选项弹窗 -->\r\n    <el-dialog title=\"选择直供申请单号\" :visible.sync=\"optionDialogVisible\" width=\"1600px\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"计划号\">\r\n          <el-input v-model=\"searchForm.planNo\" placeholder=\"请输入计划号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请编号\">\r\n          <el-input v-model=\"searchForm.applyNo\" placeholder=\"请输入申请编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"收货单位\">\r\n          <el-input v-model=\"searchForm.receiveCompany\" placeholder=\"请输入收货单位\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"searchOptions\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewWindow\">直供对应任务号\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table :data=\"optionList\" style=\"width: 100%\" @selection-change=\"handleOptionSelection\" ref=\"optionTable\">\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        <el-table-column prop=\"planNo\" label=\"计划号\" width=\"150\" />\r\n        <el-table-column prop=\"applyNo\" label=\"申请编号\" width=\"150\" />\r\n        <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\" />\r\n        <el-table-column prop=\"materialSpec\" label=\"物料规格\" width=\"120\" />\r\n        <el-table-column prop=\"sourceCompany\" label=\"申请单位\" width=\"150\" />\r\n        <el-table-column prop=\"receiveCompany\" label=\"收货单位\" width=\"150\" />\r\n        <el-table-column prop=\"plannedAmount\" label=\"计划量/t\" width=\"150\" />\r\n        <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"planStatus\" label=\"状态\" width=\"150\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"optionDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOptionSelection\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getTask, getTaskByTaskNo, getTaskmaterials, getProcessList, getDirectSupplyPlans, getDirectSupplyPlanAndTaskDetail, handleUnload, handleStockOut, isAllowDispatch, getPlanMaterials, editTaskmaterials, getTaskLogs, addLeaveLog, updateTask, addLeaveLogAndEditTaskMaterialsAndUpdateTask } from \"@/api/leave/task\";\r\nimport { detailPlan } from \"@/api/leave/plan\";\r\nimport { listXctgDriverCar, getXctgDriverCar, delXctgDriverCar, addXctgDriverCar, updateXctgDriverCar, exportXctgDriverCar } from \"@/api/truck/common/xctgDriverCar\";\r\nimport { Message } from \"element-ui\";\r\nimport QRCode from \"qrcodejs2\";\r\n\r\n\r\nexport default {\r\n  name: \"DispatchTaskDetail\",\r\n  data() {\r\n    return {\r\n      factoryConfirmDialogVisible: false,\r\n      factoryConfirmForm: {\r\n        companyName: '',\r\n        taskNo: '',\r\n        applyNo: '',\r\n        planNo: '',\r\n        taskType: null,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: null,\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9, // 完成状态\r\n        carNum: '', // 车牌号\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        handledMaterialName: '',\r\n        sourceCompany: '',\r\n        receiveCompany: '',\r\n        showDropdown: false,\r\n        extraOption: '',\r\n        deductWeight: null, // 添加扣重字段\r\n      },\r\n      optionDialogVisible: false,\r\n      searchForm: {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      },\r\n      optionList: [],\r\n      editDoorManStatus: false,\r\n      editFactoryStatus: false,\r\n      // 司机信息\r\n      driverInfo: {\r\n        id: 1,\r\n        name: '王小明',\r\n        idCard: '110101199001010001',\r\n        phone: '13800138000',\r\n        gender: '1',\r\n        company: '北京运输有限公司',\r\n        photo: 'https://via.placeholder.com/150',\r\n        driverLicenseImgs: 'https://via.placeholder.com/300x200',\r\n        vehicleLicenseImgs: 'https://via.placeholder.com/300x200'\r\n      },\r\n\r\n      // 车辆信息\r\n      carInfo: {},\r\n\r\n      // 任务物资列表\r\n      taskMaterials: [],\r\n\r\n      // 任务日志列表\r\n      taskLogs: [],\r\n\r\n      // 申请编号\r\n      applyNo: null,\r\n\r\n      isdoorMan: false,\r\n\r\n      // 派车任务ID\r\n      dispatchId: null,\r\n\r\n      taskInfoForm: {},\r\n\r\n      measureFlag: null,\r\n\r\n      backupTaskMaterials: null,\r\n      taskNo: null,\r\n\r\n      selectedOption: null,\r\n\r\n      planForm: {},\r\n\r\n      processTypeOptions: [], // 动态加载的加工类型选项\r\n\r\n      filteredProcessTypeOptions: [], // 过滤后的加工类型选项\r\n\r\n      searchProcessTypeQuery: '',// 搜索框的值\r\n\r\n      directSupplyPlanList: [], // 直供计划列表\r\n\r\n      editingRow: null,\r\n\r\n      selectedRows: [], // 添加选中行数据数组\r\n\r\n      directSupplyParams: {}\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    displayProcessTypeOptions() {\r\n      return this.searchProcessTypeQuery ? this.filteredProcessTypeOptions : this.processTypeOptions;\r\n    },\r\n\r\n    // 是否有选中的项\r\n    hasSelectedItems() {\r\n      return this.selectedRows.length > 0;\r\n    },\r\n\r\n    // 添加计算属性\r\n    materialNames() {\r\n      return this.taskMaterials.map(item => item.materialName).join(' ');\r\n    },\r\n\r\n    materialSpecs() {\r\n      return this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n    }\r\n  },\r\n\r\n  activated() {\r\n    console.log(\"activated执行\");\r\n    this.resetTaskInfoForm();\r\n\r\n    // 获取路由参数 - 支持两种方式：query参数和路径参数\r\n    let taskNo = this.$route.params.taskNo || this.$route.query.taskNo;\r\n\r\n    if (taskNo) {\r\n      // 新的方式：通过taskNo获取所有参数\r\n      this.taskNo = taskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeDataByTaskNo();\r\n    } else {\r\n      // 兼容旧的方式：从query参数获取\r\n      const { dispatchId, applyNo, measureFlag, planType, taskNo: queryTaskNo } = this.$route.query;\r\n      this.dispatchId = dispatchId;\r\n      this.applyNo = applyNo;\r\n      this.measureFlag = measureFlag;\r\n      console.log(\"this.measureFlag\", this.measureFlag)\r\n      this.planType = planType;\r\n      this.taskNo = queryTaskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeData();\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    getDirectSupplyPlanAndTask() {\r\n\r\n\r\n      let leaveTask0 = {\r\n        taskNo: this.taskInfoForm.directSupplyTaskNo\r\n      }\r\n\r\n      getDirectSupplyPlanAndTaskDetail(leaveTask0).then(res => {\r\n        console.log(\"getDirectSupplyPlanAndTaskDetail\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyParams.dispatchId = res.rows[0].id;\r\n          this.directSupplyParams.applyNo = res.rows[0].applyNo;\r\n          this.directSupplyParams.taskNo = res.rows[0].taskNo;\r\n          this.directSupplyParams.measureFlag = res.rows[1].measureFlag;\r\n          this.directSupplyParams.planType = res.rows[1].planType;\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getDirectSupplyPlanAndTaskDetail error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      });\r\n\r\n    },\r\n\r\n    validDoorMan() {\r\n      this.$store.getters.roles.forEach(item => {\r\n        if (item == 'leave.quard') {\r\n          this.isdoorMan = true;\r\n        }\r\n      });\r\n      console.log(\"isdoorMan\", this.isdoorMan)\r\n    },\r\n    async initializeData() {\r\n      try {\r\n        // 等待所有异步操作完成\r\n        await this.getTaskInfo();\r\n        await this.getTaskmaterialList(this.taskNo);\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    async initializeDataByTaskNo() {\r\n      try {\r\n        // 通过taskNo获取任务信息\r\n        await this.getTaskInfoByTaskNo();\r\n\r\n        // 通过applyNo获取计划信息\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 获取任务物资列表\r\n        await this.getTaskmaterialList(this.taskNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data by taskNo:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    uploadFactoryConfirmForm() {\r\n      // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n      this.taskMaterials.forEach(item => {\r\n        item.doormanReceiveNum = item.planNum;\r\n        console.log(\"item.planType\", this.planForm.planType);\r\n        if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n          item.doormanReceiveNumIn = item.planNum;\r\n        }\r\n      });\r\n\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      let materialSpecs = this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        driverName: this.taskInfoForm.driverName,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        materialSpecs: materialSpecs,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n    },\r\n\r\n    openNewWindow() {\r\n      const newWindowUrl = 'http://localhost/leave/leavePlanList'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    //获取可以直供的计划\r\n    async getDirectSupplyList() {\r\n      try {\r\n        let leavePlan = {\r\n          sourceCompany: this.planForm.sourceCompany,\r\n          planType: 3,\r\n        }\r\n        console.log(\"获取可以直供的计划\", leavePlan)\r\n\r\n        const res = await getDirectSupplyPlans(leavePlan);\r\n        console.log(\"getDirectSupplyPlans\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyPlanList = res.rows;\r\n          // //查询每个计划的物资\r\n          // for (const item of this.directSupplyPlanList) {\r\n          //   console.log(\"item\", item)\r\n          //   let leavePlanMaterial = {\r\n          //     applyNo: item.applyNo\r\n          //   };\r\n          //   const response = await getPlanMaterials(leavePlanMaterial);\r\n          //   if (response.code == 200) {\r\n          //     console.log(\"getPlanMaterials\", response)\r\n          //     item.materialName = response.rows[0].materialName;\r\n          //     item.materialSpec = response.rows[0].materialSpec;\r\n          //   } else {\r\n          //     this.$message.error(response.message || '获取计划物资失败');\r\n          //   }\r\n          // }\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      } catch (err) {\r\n        console.error('getDirectSupplyPlans error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      }\r\n    },\r\n    filterProcessType(query) {\r\n      this.searchProcessTypeQuery = query;\r\n\r\n      if (this.searchProcessTypeQuery) {\r\n        console.log(\"processTypeOptions\", this.processTypeOptions)\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions.filter(item =>\r\n          item.value.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions;\r\n      }\r\n    },\r\n    getProcessType() {\r\n      getProcessList().then(res => {\r\n        console.log(\"getProcessList\", res)\r\n        if (res.code == 200) {\r\n          this.processTypeOptions = res.rows.map(item => ({\r\n            value: item.processname,\r\n            label: item.processname\r\n          }));\r\n          this.filteredProcessTypeOptions = this.processTypeOptions; // 初始化过滤后的选项\r\n        } else {\r\n          this.$message.error(res.message || '获取加工类型失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getProcessList error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    async getPlanInfo(applyNo) {\r\n      try {\r\n        const response = await detailPlan(applyNo);\r\n        console.log(\"detailPlan\", response);\r\n        this.planForm = response.data;\r\n\r\n        // 从计划信息中获取planType和measureFlag\r\n        this.planType = this.planForm.planType;\r\n        this.measureFlag = this.planForm.measureFlag;\r\n        console.log(\"this.planType\", this.planType);\r\n        console.log(\"this.measureFlag\", this.measureFlag);\r\n\r\n        await this.getDirectSupplyList();\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getPlanInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    openFactoryConfirmDialog() {\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n      this.factoryConfirmDialogVisible = true;\r\n    },\r\n    submitFactoryConfirm() {\r\n      if (this.factoryConfirmForm.showDropdown == true) {\r\n        if (this.factoryConfirmForm.extraOption == null || this.factoryConfirmForm.extraOption == '') {\r\n          this.$message.error('请选择额外选项');\r\n          return;\r\n        }\r\n      }\r\n\r\n      let submitData = {};\r\n      if (this.taskInfoForm.isDirectSupply == 3) {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 3,\r\n            planNo: this.taskInfoForm.planNo,\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      } else {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            planNo: this.taskInfoForm.planNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 0, // 默认不是直供\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n            directSupplyTaskNo: this.factoryConfirmForm.extraOption,\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      }\r\n\r\n\r\n\r\n      let directSupplyTask = {\r\n        //taskNo后台雪花生成\r\n        applyNo: this.factoryConfirmForm.extraOption,\r\n        taskType: 3,\r\n        taskStatus: 7,\r\n        secGross: this.taskInfoForm.secGross,\r\n        secGrossTime: this.taskInfoForm.secGrossTime,\r\n        planNo: this.taskInfoForm.planNo,\r\n        driverName: this.taskInfoForm.driverName,\r\n        sex: this.taskInfoForm.sex,\r\n        mobilePhone: this.taskInfoForm.mobilePhone,\r\n        idCardNo: this.taskInfoForm.idCardNo,\r\n        carNum: this.taskInfoForm.carNum,\r\n        vehicleEmissionStandards: this.taskInfoForm.vehicleEmissionStandards,\r\n        faceImg: this.taskInfoForm.faceImg,\r\n        drivingLicenseImg: this.taskInfoForm.drivingLicenseImg,\r\n        driverLicenseImg: this.taskInfoForm.driverLicenseImg,\r\n        companyName: this.taskInfoForm.companyName,\r\n        isDirectSupply: 3\r\n      };\r\n\r\n      let directSupplyTaskMaterialList = this.taskMaterials;\r\n\r\n      if (this.factoryConfirmForm.showDropdown == true && this.factoryConfirmForm.extraOption != null && this.factoryConfirmForm.extraOption != '') {\r\n        submitData.leaveTask.isDirectSupply = 1; // 设置为直供\r\n        submitData.directSupplyTask = directSupplyTask;\r\n        submitData.directSupplyTaskMaterialList = directSupplyTaskMaterialList;\r\n      }\r\n\r\n      handleUnload(submitData).then(res => {\r\n        console.log(\"handleUnload\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认入库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认入库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    submitStockOutConfirm() {\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有确认出库权限');\r\n        return;\r\n      }\r\n      // 构建提交数据\r\n      let submitData = {\r\n        leaveTask: {\r\n          //todo 计量系统补充信息待完善\r\n          id: this.dispatchId,\r\n          taskNo: this.taskNo,\r\n          applyNo: this.applyNo,\r\n          planNo: this.taskInfoForm.planNo,\r\n          // 出库信息\r\n          stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n          stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n          stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n          stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n          stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n          stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n          stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n          stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n\r\n          // 更改任务状态: 9\r\n          taskStatus: 3,\r\n          carNum: this.taskInfoForm.carNum,\r\n        },\r\n        leavePlan: this.planForm,\r\n        leaveTaskMaterial: this.taskMaterials[0],\r\n      };\r\n\r\n      handleStockOut(submitData).then(res => {\r\n        console.log(\"handleStockOut\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认出库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认出库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    handleFactoryConfirm() {\r\n      if (this.editFactoryStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n\r\n      //todo\r\n      //生成派车日志\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '分厂确认数量';\r\n\r\n\r\n      let factoryTaskInfo = {}\r\n      //todo 出入场\r\n      factoryTaskInfo.id = this.taskInfoForm.id\r\n      factoryTaskInfo.unloadingWorkNo = '卸货人占位符'\r\n      factoryTaskInfo.unloadingTime = new Date()\r\n      factoryTaskInfo.taskStatus = 9\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = factoryTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '分厂确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n\r\n    handleDoorManConfirm() {\r\n      if (this.editDoorManStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n\r\n\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      // this.taskMaterials.map(item => {\r\n      //   editTaskmaterials(item);\r\n      // })\r\n      //todo\r\n      // let leaveTaskLog = {};\r\n      // leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n      // addLeaveLog(leaveTaskLog);\r\n      // this.getTaskLogList(this.taskNo);\r\n\r\n      // let doorManTaskInfo = {}\r\n      // doorManTaskInfo.id = this.taskInfoForm.id\r\n      // if (this.taskInfoForm.taskType == 1) {\r\n      //   doorManTaskInfo.taskStatus = 9\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n      //   doorManTaskInfo.taskStatus = 5\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // }\r\n      // updateTask(doorManTaskInfo);\r\n      // this.$message.success('门卫确认成功');\r\n\r\n      // setTimeout(() => {\r\n      //   this.getTaskInfo();\r\n      // }, 500)\r\n\r\n    },\r\n\r\n    handleDoorManMeasureConfirm() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      if (this.taskInfoForm.taskStatus == 4) {\r\n        leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      } else {\r\n        leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      }\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n      //todo\r\n\r\n    },\r\n    // 生成二维码\r\n    creatQrCode() {\r\n      if (this.taskInfoForm.qrCodeContent) {\r\n        this.$refs.qrCode.innerHTML = \"\";\r\n        var YSqrCode = new QRCode(this.$refs.qrCode, {\r\n          text: this.taskInfoForm.qrCodeContent, // 需要转换为二维码的内容\r\n          width: 120,\r\n          height: 120,\r\n          colorDark: \"#000000\",\r\n          colorLight: \"#ffffff\",\r\n          correctLevel: QRCode.CorrectLevel.H,\r\n        });\r\n      }\r\n    },\r\n    getTaskLogList(taskNo) {\r\n      let taskLog = {};\r\n      taskLog.taskNo = taskNo\r\n      getTaskLogs(taskLog).then(response => {\r\n        console.log(\"getTaskLogs\", response);\r\n        // this.taskLogs = response.rows;\r\n        let logs = response.rows || [];\r\n        // 找出包含\"任务完成\"的日志\r\n        const finishedLogs = logs.filter(log => log.info && log.info.includes('任务完成'));\r\n        const otherLogs = logs.filter(log => !(log.info && log.info.includes('任务完成')));\r\n        // 先放\"任务完成\"，再放其他\r\n        this.taskLogs = [...finishedLogs, ...otherLogs];\r\n      })\r\n\r\n    },\r\n    async getTaskmaterialList(taskNo) {\r\n      try {\r\n        console.log(\"getTaskmaterialList\");\r\n        let leaveMaterial = {};\r\n        leaveMaterial.taskNo = taskNo;\r\n        const response = await getTaskmaterials(leaveMaterial);\r\n        this.taskMaterials = response.rows;\r\n        // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n        this.taskMaterials.forEach(item => {\r\n          item.doormanReceiveNum = item.planNum;\r\n          console.log(\"item.planType\", this.planForm.planType);\r\n          if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n            item.doormanReceiveNumIn = item.planNum;\r\n          }\r\n        });\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskmaterialList error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    editDoorManRow(row) {\r\n      row._backup = JSON.parse(JSON.stringify(row));//深拷贝\r\n      this.editingRow = row;\r\n      this.editDoorManStatus = true;\r\n      console.log(\"this.editDoorManRow\", row);\r\n    },\r\n    editFactoryRow() {\r\n      this.backupMaterials = JSON.parse(JSON.stringify(this.taskMaterials));//深拷贝\r\n      this.editFactoryStatus = true;\r\n    },\r\n    cancelDoorManEdit(row) {\r\n      //深拷贝\r\n      if (row._backup) {\r\n        // 恢复备份数据\r\n        Object.assign(row, row._backup);\r\n        delete row._backup; // 删除备份数据\r\n      };\r\n      this.editingRow = null; // 清空当前编辑行\r\n      this.editDoorManStatus = false;\r\n    },\r\n    cancelFactoryEdit() {\r\n      this.taskMaterials = JSON.parse(JSON.stringify(this.backupMaterials));//深拷贝\r\n      console.log(\"this.taskMaterials\", this.taskMaterials);\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    saveDoorManRowIn() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNumIn是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNumIn !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫入厂确认数量(${item.doormanReceiveNumIn})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id;\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n    saveDoorManRow() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNum是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫确认数量(${item.doormanReceiveNum})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n\r\n    saveFactoryRow() {\r\n\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    resetTaskInfoForm() {\r\n      this.taskInfoForm = {};\r\n    },\r\n\r\n    async getTaskInfo() {\r\n      try {\r\n        const response = await getTask(this.dispatchId);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    async getTaskInfoByTaskNo() {\r\n      try {\r\n        const response = await getTaskByTaskNo(this.taskNo);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n\r\n        // 从返回的数据中获取所需的参数\r\n        this.dispatchId = this.taskInfoForm.id;\r\n        this.applyNo = this.taskInfoForm.applyNo;\r\n\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfoByTaskNo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    //计划状态\r\n    getPlanStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期',\r\n        '待分厂审批': '待分厂审批',\r\n        '待分厂复审': '待分厂复审',\r\n        '待生产指挥中心审批': '待生产指挥中心审批',\r\n        '审批完成': '审批完成',\r\n        '已出厂': '已出厂',\r\n        '部分收货': '部分收货',\r\n        '已完成': '已完成',\r\n        '驳回': '驳回',\r\n        '废弃': '废弃',\r\n        '过期': '过期',\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n    // 获取排放标准文本\r\n    getEmissionStandardsText(standard) {\r\n      const standardMap = {\r\n        1: '国五',\r\n        2: '国六',\r\n        3: '新能源'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取排放标准标签类型\r\n    getEmissionStandardsTagType(standard) {\r\n      const typeMap = {\r\n        1: 'warning',  // 国五\r\n        2: 'success',  // 国六\r\n        3: 'primary'   // 新能源\r\n      };\r\n      return typeMap[standard] || 'info';\r\n    },\r\n\r\n    // 获取物资状态文本\r\n    getMaterialStatusText(status) {\r\n      const statusMap = {\r\n        1: '待装载',\r\n        2: '已装载',\r\n        3: '已签收',\r\n        4: '异常'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取物资状态标签类型\r\n    getMaterialStatusType(status) {\r\n      const typeMap = {\r\n        1: 'info',     // 待装载\r\n        2: 'warning',  // 已装载\r\n        3: 'success',  // 已签收\r\n        4: 'danger'    // 异常\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#E6A23C', // 更新\r\n        3: '#67C23A', // 完成\r\n        4: '#F56C6C', // 异常\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.type] || '#409EFF';\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    // 获取任务详情数据\r\n    getTaskDetail(dispatchId) {\r\n      // 实际项目中这里需要调用API获取数据\r\n      // getDispatchTaskDetail(dispatchId).then(response => {\r\n      //   const { driverInfo, carInfo, taskMaterials, taskLogs } = response.data;\r\n      //   this.driverInfo = driverInfo;\r\n      //   this.carInfo = carInfo;\r\n      //   this.taskMaterials = taskMaterials;\r\n      //   this.taskLogs = taskLogs;\r\n      // });\r\n    },\r\n    handleShowDropdownChange(val) {\r\n      if (!val) {\r\n        this.factoryConfirmForm.extraOption = '';\r\n      }\r\n    },\r\n    openOptionDialog() {\r\n      this.optionDialogVisible = true;\r\n      this.loadOptions();\r\n      // 重置选中状态\r\n      this.selectedOption = null;\r\n      this.$nextTick(() => {\r\n        if (this.$refs.optionTable) {\r\n          this.$refs.optionTable.clearSelection();\r\n        }\r\n      });\r\n    },\r\n    handleOptionSelection(selection) {\r\n      // 只保留最后选中的一项\r\n      if (selection.length > 1) {\r\n        const lastSelected = selection[selection.length - 1];\r\n        this.$refs.optionTable.clearSelection();\r\n        this.$refs.optionTable.toggleRowSelection(lastSelected, true);\r\n        this.selectedOption = lastSelected;\r\n      } else {\r\n        this.selectedOption = selection[0];\r\n      }\r\n    },\r\n    confirmOptionSelection() {\r\n      if (!this.selectedOption) {\r\n        this.$message.warning('请选择一个选项');\r\n        return;\r\n      }\r\n\r\n      this.factoryConfirmForm.extraOption = this.selectedOption.applyNo;\r\n\r\n      // let dispatchInfo = {};\r\n      // dispatchInfo.carNum = this.taskInfoForm.carNum;\r\n      // dispatchInfo.isDirectSupply = 1;\r\n\r\n      // isAllowDispatch(dispatchInfo).then(response => {\r\n      //   let row = response.data;\r\n      //   if (row > 0) {\r\n      //     this.$message.error(\"当前车有正在执行的任务\")\r\n      //     return;\r\n      //   } else {\r\n      //     this.optionDialogVisible = false;\r\n      //     this.$message.success('选项已确认');\r\n      //   }\r\n      //   console.log(\"this.isAllowDispatch\", response);\r\n      // }).catch(err => {\r\n      //   console.error('dispatch error:', err);\r\n      //   this.$message.error('网络异常，稍后重试');\r\n      // });\r\n\r\n      this.optionDialogVisible = false;\r\n      this.$message.success('选项已确认');\r\n\r\n\r\n\r\n    },\r\n    loadOptions() {\r\n      // 这里应该调用API获取leave_plan表的数据\r\n      this.optionList = this.directSupplyPlanList; // 使用直供计划列表作为选项数据\\\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n      console.log(\"optionList\", this.optionList)\r\n    },\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用（出厂不返回）',\r\n        11: '通用（出厂返回）',\r\n        12: '委外加工（出厂返回）',\r\n        21: '有计划量计量（跨区调拨）',\r\n        22: '短期（跨区调拨）',\r\n        23: '钢板（圆钢）（跨区调拨）',\r\n        31: '通用（退货申请）'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n    searchOptions() {\r\n      // 取出并转小写\r\n      const searchPlanNo = (this.searchForm.planNo || '').toLowerCase();\r\n      const searchApplyNo = (this.searchForm.applyNo || '').toLowerCase();\r\n      const searchReceiveCompany = (this.searchForm.receiveCompany || '').toLowerCase();\r\n\r\n      // 过滤\r\n      this.optionList = this.directSupplyPlanList.filter(item => {\r\n        const planNo = (item.planNo || '').toString().toLowerCase();\r\n        const applyNo = (item.applyNo || '').toString().toLowerCase();\r\n        const receiveCompany = (item.receiveCompany || '').toString().toLowerCase();\r\n\r\n        // 为空不作为条件\r\n        const matchPlanNo = !searchPlanNo || planNo.includes(searchPlanNo);\r\n        const matchApplyNo = !searchApplyNo || applyNo.includes(searchApplyNo);\r\n        const matchReceiveCompany = !searchReceiveCompany || receiveCompany.includes(searchReceiveCompany);\r\n\r\n        return matchPlanNo && matchApplyNo && matchReceiveCompany;\r\n      });\r\n\r\n      // 更新状态显示\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n    },\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      };\r\n      this.loadOptions(); // 重新加载所有数据\r\n    },\r\n    getTaskTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return typeMap[type] || '未知';\r\n    },\r\n    // // 判断行是否可选\r\n    // isSelectable(row) {\r\n    //   // 当门卫确认数量不为0时，该行可选\r\n    //   return row.doormanReceiveNum > 0 && this.taskInfoForm.taskStatus !== 9;\r\n    // },\r\n\r\n    // 表格选择变化时的处理函数\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n\r\n    // 处理非计量分厂确认\r\n    handleNonMeasureFactoryConfirm() {\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n      let isHandled = false;\r\n      this.selectedRows.forEach(item => {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning('门卫确认数量和计划数量不一致，请检查');\r\n          isHandled = true;\r\n        }\r\n      });\r\n\r\n      if (isHandled) {\r\n        return;\r\n      }\r\n\r\n      // if (this.selectedRows.length === 0) {\r\n      //   this.$message.warning('请选择需要确认的物资');\r\n      //   return;\r\n      // }\r\n\r\n      // 生成派车日志\r\n      let leaveTaskLog = {\r\n        logType: 2,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        info: '分厂接收确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ')\r\n      };\r\n\r\n      // 构建任务信息\r\n      let factoryTaskInfo = {\r\n        id: this.taskInfoForm.id,\r\n        unloadingWorkNo: '卸货人占位符',//后端updateLeaveTask方法\r\n        unloadingTime: new Date(),\r\n        taskStatus: 9\r\n      };\r\n\r\n      this.selectedRows.forEach(item => {\r\n        // 设置非计量分厂确认数量\r\n        item.factoryReceiveNum = item.doormanReceiveNum;\r\n      });\r\n\r\n      // 构建请求参数\r\n      let param = {\r\n        taskMaterialList: this.selectedRows, // 使用选中的行数据\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: factoryTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      // 发送请求\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        if (res.code == 200) {\r\n          this.$message.success('非计量分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n          // 清空选中状态\r\n          this.selectedRows = [];\r\n        } else {\r\n          this.$message.error(res.message || '非计量分厂确认失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleNonMeasureFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    openNewTaskWindow() {\r\n      console.log(\"openNewTaskWindow\", this.directSupplyParams);\r\n      let dispatchId = this.directSupplyParams.dispatchId;\r\n      let applyNo = BigInt(this.directSupplyParams.applyNo);\r\n      let measureFlag = this.directSupplyParams.measureFlag;\r\n      let planType = this.directSupplyParams.planType;\r\n      let taskNo = BigInt(this.directSupplyParams.taskNo);\r\n      const url = `http://localhost/leave/plan/task?dispatchId=${dispatchId}&applyNo=${applyNo}&measureFlag=${measureFlag}&planType=${planType}&taskNo=${taskNo}`;\r\n      window.open(url, '_blank');\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.btn-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode {\r\n  margin: 0 auto;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 通行证二维码模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 司机信息模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 车辆信息模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 物资列表模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.driver-photos {\r\n  padding: 0 20px 20px;\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.photo-item {\r\n  width: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.photo-item h4 {\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.photo-container {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.photo-container img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  object-fit: contain;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 25px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy1BA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAC,sBAAA,CAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAM,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,2BAAA;MACAC,kBAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,QAAA;QACAC,eAAA;QACAC,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QAAA;QACAC,MAAA;QAAA;QACA;QACAC,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAC,mBAAA;QACAC,aAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAC,YAAA;MACA;MACAC,mBAAA;MACAC,UAAA;QACAjC,MAAA;QACAD,OAAA;QACA6B,cAAA;MACA;MACAM,UAAA;MACAC,iBAAA;MACAC,iBAAA;MACA;MACAC,UAAA;QACAC,EAAA;QACA7C,IAAA;QACA8C,MAAA;QACAC,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,KAAA;QACAC,iBAAA;QACAC,kBAAA;MACA;MAEA;MACAC,OAAA;MAEA;MACAC,aAAA;MAEA;MACAC,QAAA;MAEA;MACAjD,OAAA;MAEAkD,SAAA;MAEA;MACAC,UAAA;MAEAC,YAAA;MAEAC,WAAA;MAEAC,mBAAA;MACAvD,MAAA;MAEAwD,cAAA;MAEAC,QAAA;MAEAC,kBAAA;MAAA;;MAEAC,0BAAA;MAAA;;MAEAC,sBAAA;MAAA;;MAEAC,oBAAA;MAAA;;MAEAC,UAAA;MAEAC,YAAA;MAAA;;MAEAC,kBAAA;IACA;EACA;EAEAC,QAAA;IACAC,yBAAA,WAAAA,0BAAA;MACA,YAAAN,sBAAA,QAAAD,0BAAA,QAAAD,kBAAA;IACA;IAEA;IACAS,gBAAA,WAAAA,iBAAA;MACA,YAAAJ,YAAA,CAAAK,MAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAApB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;IACA;IAEAC,aAAA,WAAAA,cAAA;MACA,YAAAzB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAI,YAAA;MAAA,GAAAF,IAAA;IACA;EACA;EAEAG,SAAA,WAAAA,UAAA;IACAC,OAAA,CAAAC,GAAA;IACA,KAAAC,iBAAA;;IAEA;IACA,IAAA/E,MAAA,QAAAgF,MAAA,CAAAC,MAAA,CAAAjF,MAAA,SAAAgF,MAAA,CAAAE,KAAA,CAAAlF,MAAA;IAEA,IAAAA,MAAA;MACA;MACA,KAAAA,MAAA,GAAAA,MAAA;MACA6E,OAAA,CAAAC,GAAA,gBAAA9E,MAAA;MACA,KAAAmF,YAAA;;MAEA;MACA,KAAAC,sBAAA;IACA;MACA;MACA,IAAAC,kBAAA,QAAAL,MAAA,CAAAE,KAAA;QAAA9B,UAAA,GAAAiC,kBAAA,CAAAjC,UAAA;QAAAnD,OAAA,GAAAoF,kBAAA,CAAApF,OAAA;QAAAqD,WAAA,GAAA+B,kBAAA,CAAA/B,WAAA;QAAAgC,QAAA,GAAAD,kBAAA,CAAAC,QAAA;QAAAC,WAAA,GAAAF,kBAAA,CAAArF,MAAA;MACA,KAAAoD,UAAA,GAAAA,UAAA;MACA,KAAAnD,OAAA,GAAAA,OAAA;MACA,KAAAqD,WAAA,GAAAA,WAAA;MACAuB,OAAA,CAAAC,GAAA,0BAAAxB,WAAA;MACA,KAAAgC,QAAA,GAAAA,QAAA;MACA,KAAAtF,MAAA,GAAAuF,WAAA;MACAV,OAAA,CAAAC,GAAA,gBAAA9E,MAAA;MACA,KAAAmF,YAAA;;MAEA;MACA,KAAAK,cAAA;IACA;EACA;EAEAC,OAAA;IACAC,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,KAAA;MAGA,IAAAC,UAAA;QACA5F,MAAA,OAAAqD,YAAA,CAAAwC;MACA;MAEA,IAAAC,sCAAA,EAAAF,UAAA,EAAAG,IAAA,WAAAC,GAAA;QACAnB,OAAA,CAAAC,GAAA,qCAAAkB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAN,KAAA,CAAA3B,kBAAA,CAAAZ,UAAA,GAAA4C,GAAA,CAAAE,IAAA,IAAA1D,EAAA;UACAmD,KAAA,CAAA3B,kBAAA,CAAA/D,OAAA,GAAA+F,GAAA,CAAAE,IAAA,IAAAjG,OAAA;UACA0F,KAAA,CAAA3B,kBAAA,CAAAhE,MAAA,GAAAgG,GAAA,CAAAE,IAAA,IAAAlG,MAAA;UACA2F,KAAA,CAAA3B,kBAAA,CAAAV,WAAA,GAAA0C,GAAA,CAAAE,IAAA,IAAA5C,WAAA;UACAqC,KAAA,CAAA3B,kBAAA,CAAAsB,QAAA,GAAAU,GAAA,CAAAE,IAAA,IAAAZ,QAAA;QACA;UACAK,KAAA,CAAAQ,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,4CAAAG,GAAA;QACAZ,KAAA,CAAAQ,QAAA,CAAAC,KAAA;QACA,MAAAG,GAAA;MACA;IAEA;IAEApB,YAAA,WAAAA,aAAA;MAAA,IAAAqB,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,CAAAC,KAAA,CAAAC,OAAA,WAAArC,IAAA;QACA,IAAAA,IAAA;UACAiC,MAAA,CAAArD,SAAA;QACA;MACA;MACA0B,OAAA,CAAAC,GAAA,mBAAA3B,SAAA;IACA;IACAqC,cAAA,WAAAA,eAAA;MAAA,IAAAqB,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,EAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGAT,MAAA,CAAAW,WAAA;YAAA;cAAAH,QAAA,CAAAC,CAAA;cAAA,OACAT,MAAA,CAAAY,mBAAA,CAAAZ,MAAA,CAAA7G,MAAA;YAAA;cAAAqH,QAAA,CAAAC,CAAA;cAAA,OACAT,MAAA,CAAAa,WAAA,CAAAb,MAAA,CAAA5G,OAAA;YAAA;cAEA;cACA4G,MAAA,CAAAc,wBAAA;;cAEA;cACAd,MAAA,CAAAe,cAAA,CAAAf,MAAA,CAAA7G,MAAA;cACA6G,MAAA,CAAAgB,cAAA;;cAEA;cACAhB,MAAA,CAAAnB,0BAAA;cAAA2B,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAS,CAAA;cAEAjD,OAAA,CAAAuB,KAAA,6BAAAe,EAAA;cACAN,MAAA,CAAAV,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAAiB,QAAA,CAAAU,CAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IAEA;IAEA9B,sBAAA,WAAAA,uBAAA;MAAA,IAAA4C,MAAA;MAAA,WAAAlB,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAgB,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAAlB,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAe,SAAA;UAAA,kBAAAA,SAAA,CAAAb,CAAA;YAAA;cAAAa,SAAA,CAAAZ,CAAA;cAAAY,SAAA,CAAAb,CAAA;cAAA,OAGAU,MAAA,CAAAI,mBAAA;YAAA;cAAAD,SAAA,CAAAb,CAAA;cAAA,OAGAU,MAAA,CAAAN,WAAA,CAAAM,MAAA,CAAA/H,OAAA;YAAA;cAAAkI,SAAA,CAAAb,CAAA;cAAA,OAGAU,MAAA,CAAAP,mBAAA,CAAAO,MAAA,CAAAhI,MAAA;YAAA;cAEA;cACAgI,MAAA,CAAAL,wBAAA;;cAEA;cACAK,MAAA,CAAAJ,cAAA,CAAAI,MAAA,CAAAhI,MAAA;cACAgI,MAAA,CAAAH,cAAA;;cAEA;cACAG,MAAA,CAAAtC,0BAAA;cAAAyC,SAAA,CAAAb,CAAA;cAAA;YAAA;cAAAa,SAAA,CAAAZ,CAAA;cAAAW,GAAA,GAAAC,SAAA,CAAAL,CAAA;cAEAjD,OAAA,CAAAuB,KAAA,uCAAA8B,GAAA;cACAF,MAAA,CAAA7B,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAA+B,SAAA,CAAAJ,CAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEAN,wBAAA,WAAAA,yBAAA;MAAA,IAAAU,MAAA;MACA;MACA,KAAApF,aAAA,CAAA2D,OAAA,WAAArC,IAAA;QACAA,IAAA,CAAA+D,iBAAA,GAAA/D,IAAA,CAAAgE,OAAA;QACA1D,OAAA,CAAAC,GAAA,kBAAAuD,MAAA,CAAA5E,QAAA,CAAA6B,QAAA;QACA,IAAA+C,MAAA,CAAA5E,QAAA,CAAA6B,QAAA,SAAA+C,MAAA,CAAA5E,QAAA,CAAA6B,QAAA;UACAf,IAAA,CAAAiE,mBAAA,GAAAjE,IAAA,CAAAgE,OAAA;QACA;MACA;MAEA,IAAA3G,mBAAA,QAAAqB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MACA,IAAAC,aAAA,QAAAzB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAI,YAAA;MAAA,GAAAF,IAAA;MACA;MACA,KAAA3E,kBAAA;QACAC,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACA0I,KAAA,OAAApF,YAAA,CAAAoF,KAAA;QACAC,QAAA,OAAArF,YAAA,CAAAqF,QAAA;QACAC,UAAA,OAAAtF,YAAA,CAAAsF,UAAA;QACAC,IAAA,OAAAvF,YAAA,CAAAuF,IAAA;QACA5I,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAE,eAAA;QACAC,aAAA,MAAAwI,IAAA;QACAvI,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QAAA;QACAW,mBAAA,EAAAA,mBAAA;QACA8C,aAAA,EAAAA,aAAA;QACA7C,aAAA,OAAA4B,QAAA,CAAA5B,aAAA;QACAC,cAAA,OAAA2B,QAAA,CAAA3B,cAAA;QACAC,YAAA;QAAA;QACAC,WAAA;QAAA;QACA;QACAd,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAM,YAAA;MACA;IACA;IAEA6G,aAAA,WAAAA,cAAA;MACA,IAAAC,YAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,YAAA;IACA;IACA;IACAG,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MAAA,WAAArC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAmC,SAAA;QAAA,IAAAC,SAAA,EAAArD,GAAA,EAAAsD,GAAA;QAAA,WAAAtC,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAmC,SAAA;UAAA,kBAAAA,SAAA,CAAAjC,CAAA;YAAA;cAAAiC,SAAA,CAAAhC,CAAA;cAEA8B,SAAA;gBACAxH,aAAA,EAAAsH,MAAA,CAAA1F,QAAA,CAAA5B,aAAA;gBACAyD,QAAA;cACA;cACAT,OAAA,CAAAC,GAAA,cAAAuE,SAAA;cAAAE,SAAA,CAAAjC,CAAA;cAAA,OAEA,IAAAkC,0BAAA,EAAAH,SAAA;YAAA;cAAArD,GAAA,GAAAuD,SAAA,CAAAzB,CAAA;cACAjD,OAAA,CAAAC,GAAA,yBAAAkB,GAAA;cACA,IAAAA,GAAA,CAAAC,IAAA;gBACAkD,MAAA,CAAAtF,oBAAA,GAAAmC,GAAA,CAAAE,IAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACAiD,MAAA,CAAAhD,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;cACA;cAAAkD,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAAAiC,SAAA,CAAAhC,CAAA;cAAA+B,GAAA,GAAAC,SAAA,CAAAzB,CAAA;cAEAjD,OAAA,CAAAuB,KAAA,gCAAAkD,GAAA;cACAH,MAAA,CAAAhD,QAAA,CAAAC,KAAA;cAAA,MAAAkD,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAxB,CAAA;UAAA;QAAA,GAAAqB,QAAA;MAAA;IAGA;IACAK,iBAAA,WAAAA,kBAAAvE,KAAA;MACA,KAAAtB,sBAAA,GAAAsB,KAAA;MAEA,SAAAtB,sBAAA;QACAiB,OAAA,CAAAC,GAAA,4BAAApB,kBAAA;QAEA,KAAAC,0BAAA,QAAAD,kBAAA,CAAAgG,MAAA,WAAAnF,IAAA;UAAA,OACAA,IAAA,CAAAoF,KAAA,CAAAC,QAAA,CAAA1E,KAAA;QAAA,CACA;MACA;QAEA,KAAAvB,0BAAA,QAAAD,kBAAA;MACA;IACA;IACAmE,cAAA,WAAAA,eAAA;MAAA,IAAAgC,MAAA;MACA,IAAAC,oBAAA,IAAA/D,IAAA,WAAAC,GAAA;QACAnB,OAAA,CAAAC,GAAA,mBAAAkB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA4D,MAAA,CAAAnG,kBAAA,GAAAsC,GAAA,CAAAE,IAAA,CAAA5B,GAAA,WAAAC,IAAA;YAAA;cACAoF,KAAA,EAAApF,IAAA,CAAAwF,WAAA;cACAC,KAAA,EAAAzF,IAAA,CAAAwF;YACA;UAAA;UACAF,MAAA,CAAAlG,0BAAA,GAAAkG,MAAA,CAAAnG,kBAAA;QACA;UACAmG,MAAA,CAAA1D,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,0BAAAG,GAAA;QACAsD,MAAA,CAAA1D,QAAA,CAAAC,KAAA;MACA;IACA;IACAsB,WAAA,WAAAA,YAAAzH,OAAA;MAAA,IAAAgK,MAAA;MAAA,WAAAnD,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAiD,SAAA;QAAA,IAAAC,QAAA,EAAAC,GAAA;QAAA,WAAApD,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAiD,SAAA;UAAA,kBAAAA,SAAA,CAAA/C,CAAA;YAAA;cAAA+C,SAAA,CAAA9C,CAAA;cAAA8C,SAAA,CAAA/C,CAAA;cAAA,OAEA,IAAAgD,gBAAA,EAAArK,OAAA;YAAA;cAAAkK,QAAA,GAAAE,SAAA,CAAAvC,CAAA;cACAjD,OAAA,CAAAC,GAAA,eAAAqF,QAAA;cACAF,MAAA,CAAAxG,QAAA,GAAA0G,QAAA,CAAAvK,IAAA;;cAEA;cACAqK,MAAA,CAAA3E,QAAA,GAAA2E,MAAA,CAAAxG,QAAA,CAAA6B,QAAA;cACA2E,MAAA,CAAA3G,WAAA,GAAA2G,MAAA,CAAAxG,QAAA,CAAAH,WAAA;cACAuB,OAAA,CAAAC,GAAA,kBAAAmF,MAAA,CAAA3E,QAAA;cACAT,OAAA,CAAAC,GAAA,qBAAAmF,MAAA,CAAA3G,WAAA;cAAA+G,SAAA,CAAA/C,CAAA;cAAA,OAEA2C,MAAA,CAAAf,mBAAA;YAAA;cAAA,OAAAmB,SAAA,CAAAtC,CAAA,IACAoC,QAAA;YAAA;cAAAE,SAAA,CAAA9C,CAAA;cAAA6C,GAAA,GAAAC,SAAA,CAAAvC,CAAA;cAEAjD,OAAA,CAAAuB,KAAA,uBAAAgE,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAtC,CAAA;UAAA;QAAA,GAAAmC,QAAA;MAAA;IAGA;IACAK,wBAAA,WAAAA,yBAAA;MACA,IAAA3I,mBAAA,QAAAqB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MACA;MACA,KAAA3E,kBAAA;QACAC,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACA0I,KAAA,OAAApF,YAAA,CAAAoF,KAAA;QACAC,QAAA,OAAArF,YAAA,CAAAqF,QAAA;QACAE,IAAA,OAAAvF,YAAA,CAAAuF,IAAA;QACA5I,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAE,eAAA;QACAC,aAAA,MAAAwI,IAAA;QACAvI,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QAAA;QACAW,mBAAA,EAAAA,mBAAA;QACAC,aAAA,OAAA4B,QAAA,CAAA5B,aAAA;QACAC,cAAA,OAAA2B,QAAA,CAAA3B,cAAA;QACAC,YAAA;QAAA;QACAC,WAAA;QAAA;QACA;QACAd,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAM,YAAA;MACA;MACA,KAAApC,2BAAA;IACA;IACA2K,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAA3K,kBAAA,CAAAiC,YAAA;QACA,SAAAjC,kBAAA,CAAAkC,WAAA,iBAAAlC,kBAAA,CAAAkC,WAAA;UACA,KAAAmE,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MAEA,IAAAsE,UAAA;MACA,SAAArH,YAAA,CAAAsH,cAAA;QACA;QACAD,UAAA;UACAE,SAAA;YACApI,EAAA,OAAAY,UAAA;YACApD,MAAA,OAAAA,MAAA;YACAC,OAAA,OAAAA,OAAA;YACA;YACAK,WAAA,OAAAR,kBAAA,CAAAQ,WAAA;YACAC,UAAA,OAAAT,kBAAA,CAAAS,UAAA;YACAC,MAAA,OAAAV,kBAAA,CAAAW,KAAA,QAAAX,kBAAA,CAAAY,SAAA;YACAC,WAAA,OAAAb,kBAAA,CAAAa,WAAA;YACAC,MAAA,OAAAd,kBAAA,CAAAc,MAAA;YACAC,UAAA,OAAAf,kBAAA,CAAAe,UAAA;YACAC,KAAA,OAAAhB,kBAAA,CAAAgB,KAAA;YACAC,MAAA,OAAAjB,kBAAA,CAAAiB,MAAA;YACAE,MAAA,OAAAoC,YAAA,CAAApC,MAAA;YACA0H,UAAA,OAAAtF,YAAA,CAAAsF,UAAA;YACAgC,cAAA;YACAzK,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;YACA+B,YAAA,OAAAnC,kBAAA,CAAAmC,YAAA;YAAA;;YAEA;YACAf,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;YACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;YACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;YACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;YACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;YACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;YACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;YACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;YACA;YACA;YACAX,UAAA;YACAb,QAAA,OAAAkD,YAAA,CAAAlD;UACA;UACAkJ,SAAA,OAAA5F,QAAA;UACAoH,iBAAA,OAAA5H,aAAA;QACA;MACA;QACA;QACAyH,UAAA;UACAE,SAAA;YACApI,EAAA,OAAAY,UAAA;YACApD,MAAA,OAAAA,MAAA;YACAC,OAAA,OAAAA,OAAA;YACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;YACA;YACAI,WAAA,OAAAR,kBAAA,CAAAQ,WAAA;YACAC,UAAA,OAAAT,kBAAA,CAAAS,UAAA;YACAC,MAAA,OAAAV,kBAAA,CAAAW,KAAA,QAAAX,kBAAA,CAAAY,SAAA;YACAC,WAAA,OAAAb,kBAAA,CAAAa,WAAA;YACAC,MAAA,OAAAd,kBAAA,CAAAc,MAAA;YACAC,UAAA,OAAAf,kBAAA,CAAAe,UAAA;YACAC,KAAA,OAAAhB,kBAAA,CAAAgB,KAAA;YACAC,MAAA,OAAAjB,kBAAA,CAAAiB,MAAA;YACAE,MAAA,OAAAoC,YAAA,CAAApC,MAAA;YACA0H,UAAA,OAAAtF,YAAA,CAAAsF,UAAA;YACAgC,cAAA;YAAA;YACA1I,YAAA,OAAAnC,kBAAA,CAAAmC,YAAA;YAAA;YACA4D,kBAAA,OAAA/F,kBAAA,CAAAkC,WAAA;YACA;YACAd,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;YACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;YACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;YACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;YACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;YACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;YACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;YACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;YACA;YACA;YACAX,UAAA;YACAb,QAAA,OAAAkD,YAAA,CAAAlD;UACA;UACAkJ,SAAA,OAAA5F,QAAA;UACAoH,iBAAA,OAAA5H,aAAA;QACA;MACA;MAIA,IAAA6H,gBAAA;QACA;QACA7K,OAAA,OAAAH,kBAAA,CAAAkC,WAAA;QACA7B,QAAA;QACAa,UAAA;QACA0H,QAAA,OAAArF,YAAA,CAAAqF,QAAA;QACAqC,YAAA,OAAA1H,YAAA,CAAA0H,YAAA;QACA7K,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAyI,UAAA,OAAAtF,YAAA,CAAAsF,UAAA;QACAqC,GAAA,OAAA3H,YAAA,CAAA2H,GAAA;QACAC,WAAA,OAAA5H,YAAA,CAAA4H,WAAA;QACAC,QAAA,OAAA7H,YAAA,CAAA6H,QAAA;QACAjK,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QACAkK,wBAAA,OAAA9H,YAAA,CAAA8H,wBAAA;QACAC,OAAA,OAAA/H,YAAA,CAAA+H,OAAA;QACAC,iBAAA,OAAAhI,YAAA,CAAAgI,iBAAA;QACAC,gBAAA,OAAAjI,YAAA,CAAAiI,gBAAA;QACAvL,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACA4K,cAAA;MACA;MAEA,IAAAY,4BAAA,QAAAtI,aAAA;MAEA,SAAAnD,kBAAA,CAAAiC,YAAA,iBAAAjC,kBAAA,CAAAkC,WAAA,iBAAAlC,kBAAA,CAAAkC,WAAA;QACA0I,UAAA,CAAAE,SAAA,CAAAD,cAAA;QACAD,UAAA,CAAAI,gBAAA,GAAAA,gBAAA;QACAJ,UAAA,CAAAa,4BAAA,GAAAA,4BAAA;MACA;MAEA,IAAAC,kBAAA,EAAAd,UAAA,EAAA3E,IAAA,WAAAC,GAAA;QACAnB,OAAA,CAAAC,GAAA,iBAAAkB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAwE,MAAA,CAAAtE,QAAA,CAAAsF,OAAA;UACAhB,MAAA,CAAA5K,2BAAA;UACA4K,MAAA,CAAA7C,cAAA,CAAA6C,MAAA,CAAAzK,MAAA;UACAyK,MAAA,CAAAjD,WAAA;QACA;UACA;UACAiD,MAAA,CAAAtE,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,8BAAAG,GAAA;QACAkE,MAAA,CAAAtE,QAAA,CAAAC,KAAA;MACA;IACA;IAEAsF,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAEA;MACA,IAAAhF,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAiD,QAAA;QACA,KAAAzD,QAAA,CAAAC,KAAA;QACA;MACA;MACA;MACA,IAAAsE,UAAA;QACAE,SAAA;UACA;UACApI,EAAA,OAAAY,UAAA;UACApD,MAAA,OAAAA,MAAA;UACAC,OAAA,OAAAA,OAAA;UACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;UACA;UACAgB,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;UACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;UACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;UACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;UACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;UACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;UACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;UACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;UAEA;UACAX,UAAA;UACAC,MAAA,OAAAoC,YAAA,CAAApC;QACA;QACAoI,SAAA,OAAA5F,QAAA;QACAoH,iBAAA,OAAA5H,aAAA;MACA;MAEA,IAAA2I,oBAAA,EAAAlB,UAAA,EAAA3E,IAAA,WAAAC,GAAA;QACAnB,OAAA,CAAAC,GAAA,mBAAAkB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA0F,MAAA,CAAAxF,QAAA,CAAAsF,OAAA;UACAE,MAAA,CAAA9L,2BAAA;UACA8L,MAAA,CAAA/D,cAAA,CAAA+D,MAAA,CAAA3L,MAAA;UACA2L,MAAA,CAAAnE,WAAA;QACA;UACA;UACAmE,MAAA,CAAAxF,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,8BAAAG,GAAA;QACAoF,MAAA,CAAAxF,QAAA,CAAAC,KAAA;MACA;IACA;IAEAyF,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAAxJ,iBAAA;QACA,KAAA6D,QAAA,CAAA4F,OAAA;QACA;MACA;;MAGA;MACA;MACA,IAAAC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAhM,MAAA,QAAAA,MAAA;MACAgM,YAAA,CAAA/L,OAAA,QAAAA,OAAA;MACA+L,YAAA,CAAAE,IAAA;MAGA,IAAAC,eAAA;MACA;MACAA,eAAA,CAAA3J,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA2J,eAAA,CAAA/L,eAAA;MACA+L,eAAA,CAAA9L,aAAA,OAAAwI,IAAA;MACAsD,eAAA,CAAAnL,UAAA;MAEA,IAAAoL,KAAA;MACAA,KAAA,CAAAC,gBAAA,QAAApJ,aAAA;MACAmJ,KAAA,CAAAE,QAAA,GAAAN,YAAA;MACAI,KAAA,CAAAxB,SAAA,GAAAuB,eAAA;MACAC,KAAA,CAAA9I,WAAA,QAAAA,WAAA;MAEA,IAAAiJ,kDAAA,EAAAH,KAAA,EAAArG,IAAA,WAAAC,GAAA;QACAnB,OAAA,CAAAC,GAAA,iDAAAkB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA6F,MAAA,CAAA3F,QAAA,CAAAsF,OAAA;UACAK,MAAA,CAAAlE,cAAA,CAAAkE,MAAA,CAAA9L,MAAA;UACA8L,MAAA,CAAAtE,WAAA;QACA;UACA;UACAsE,MAAA,CAAA3F,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,gCAAAG,GAAA;QACAuF,MAAA,CAAA3F,QAAA,CAAAC,KAAA;MACA;IACA;IAGAoG,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,SAAApK,iBAAA;QACA,KAAA8D,QAAA,CAAA4F,OAAA;QACA;MACA;MAEA,IAAAC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAhM,MAAA,QAAAA,MAAA;MACAgM,YAAA,CAAA/L,OAAA,QAAAA,OAAA;MACA+L,YAAA,CAAAE,IAAA;MAIA,IAAAQ,eAAA;MACAA,eAAA,CAAAlK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACAuM,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAC,SAAA,OAAA9D,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAoJ,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAoJ,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAC,SAAA,OAAA9D,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;MACAA,KAAA,CAAAC,gBAAA,QAAApJ,aAAA;MACAmJ,KAAA,CAAAE,QAAA,GAAAN,YAAA;MACAI,KAAA,CAAAxB,SAAA,GAAA8B,eAAA;MACAN,KAAA,CAAA9I,WAAA,QAAAA,WAAA;MAEA,IAAAiJ,kDAAA,EAAAH,KAAA,EAAArG,IAAA,WAAAC,GAAA;QACAnB,OAAA,CAAAC,GAAA,iDAAAkB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAwG,OAAA,CAAAtG,QAAA,CAAAsF,OAAA;UACAgB,OAAA,CAAA7E,cAAA,CAAA6E,OAAA,CAAAzM,MAAA;UACAyM,OAAA,CAAAjF,WAAA;QACA;UACA;UACAiF,OAAA,CAAAtG,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,gCAAAG,GAAA;QACAkG,OAAA,CAAAtG,QAAA,CAAAC,KAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA4F,YAAA,CAAAhM,MAAA,QAAAA,MAAA;MACAgM,YAAA,CAAA/L,OAAA,QAAAA,OAAA;MACA+L,YAAA,CAAAE,IAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;IAEA;IAEAc,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAtG,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAiD,QAAA;QACA,KAAAzD,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,IAAA4F,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAhM,MAAA,QAAAA,MAAA;MACAgM,YAAA,CAAA/L,OAAA,QAAAA,OAAA;MACA,SAAAoD,YAAA,CAAArC,UAAA;QACAgL,YAAA,CAAAE,IAAA,yBAAAjJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;QACAuH,YAAA,CAAAE,IAAA,yBAAAjJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;MAEA,IAAAiI,eAAA;MACAA,eAAA,CAAAlK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACAuM,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAC,SAAA,OAAA9D,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAoJ,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAoJ,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAC,SAAA,OAAA9D,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;MACAA,KAAA,CAAAC,gBAAA,QAAApJ,aAAA;MACAmJ,KAAA,CAAAE,QAAA,GAAAN,YAAA;MACAI,KAAA,CAAAxB,SAAA,GAAA8B,eAAA;MACAN,KAAA,CAAA9I,WAAA,QAAAA,WAAA;MAEA,IAAAiJ,kDAAA,EAAAH,KAAA,EAAArG,IAAA,WAAAC,GAAA;QACAnB,OAAA,CAAAC,GAAA,iDAAAkB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAgH,OAAA,CAAA9G,QAAA,CAAAsF,OAAA;UACAwB,OAAA,CAAArF,cAAA,CAAAqF,OAAA,CAAAjN,MAAA;UACAiN,OAAA,CAAAzF,WAAA;QACA;UACA;UACAyF,OAAA,CAAA9G,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,gCAAAG,GAAA;QACA0G,OAAA,CAAA9G,QAAA,CAAAC,KAAA;MACA;MACA;IAEA;IACA;IACA8G,WAAA,WAAAA,YAAA;MACA,SAAA7J,YAAA,CAAA8J,aAAA;QACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,SAAA;QACA,IAAAC,QAAA,OAAAC,iBAAA,MAAAJ,KAAA,CAAAC,MAAA;UACAI,IAAA,OAAApK,YAAA,CAAA8J,aAAA;UAAA;UACAO,KAAA;UACAC,MAAA;UACAC,SAAA;UACAC,UAAA;UACAC,YAAA,EAAAN,iBAAA,CAAAO,YAAA,CAAAC;QACA;MACA;IACA;IACApG,cAAA,WAAAA,eAAA5H,MAAA;MAAA,IAAAiO,OAAA;MACA,IAAAC,OAAA;MACAA,OAAA,CAAAlO,MAAA,GAAAA,MAAA;MACA,IAAAmO,iBAAA,EAAAD,OAAA,EAAAnI,IAAA,WAAAoE,QAAA;QACAtF,OAAA,CAAAC,GAAA,gBAAAqF,QAAA;QACA;QACA,IAAAiE,IAAA,GAAAjE,QAAA,CAAAjE,IAAA;QACA;QACA,IAAAmI,YAAA,GAAAD,IAAA,CAAA1E,MAAA,WAAA5E,GAAA;UAAA,OAAAA,GAAA,CAAAoH,IAAA,IAAApH,GAAA,CAAAoH,IAAA,CAAAtC,QAAA;QAAA;QACA,IAAA0E,SAAA,GAAAF,IAAA,CAAA1E,MAAA,WAAA5E,GAAA;UAAA,SAAAA,GAAA,CAAAoH,IAAA,IAAApH,GAAA,CAAAoH,IAAA,CAAAtC,QAAA;QAAA;QACA;QACAqE,OAAA,CAAA/K,QAAA,MAAAqL,MAAA,KAAAC,mBAAA,CAAAzH,OAAA,EAAAsH,YAAA,OAAAG,mBAAA,CAAAzH,OAAA,EAAAuH,SAAA;MACA;IAEA;IACA7G,mBAAA,WAAAA,oBAAAzH,MAAA;MAAA,IAAAyO,OAAA;MAAA,WAAA3H,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAyH,SAAA;QAAA,IAAAC,aAAA,EAAAxE,QAAA,EAAAyE,GAAA;QAAA,WAAA5H,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAyH,SAAA;UAAA,kBAAAA,SAAA,CAAAvH,CAAA;YAAA;cAAAuH,SAAA,CAAAtH,CAAA;cAEA1C,OAAA,CAAAC,GAAA;cACA6J,aAAA;cACAA,aAAA,CAAA3O,MAAA,GAAAA,MAAA;cAAA6O,SAAA,CAAAvH,CAAA;cAAA,OACA,IAAAwH,sBAAA,EAAAH,aAAA;YAAA;cAAAxE,QAAA,GAAA0E,SAAA,CAAA/G,CAAA;cACA2G,OAAA,CAAAxL,aAAA,GAAAkH,QAAA,CAAAjE,IAAA;cACA;cACAuI,OAAA,CAAAxL,aAAA,CAAA2D,OAAA,WAAArC,IAAA;gBACAA,IAAA,CAAA+D,iBAAA,GAAA/D,IAAA,CAAAgE,OAAA;gBACA1D,OAAA,CAAAC,GAAA,kBAAA2J,OAAA,CAAAhL,QAAA,CAAA6B,QAAA;gBACA,IAAAmJ,OAAA,CAAAhL,QAAA,CAAA6B,QAAA,SAAAmJ,OAAA,CAAAhL,QAAA,CAAA6B,QAAA;kBACAf,IAAA,CAAAiE,mBAAA,GAAAjE,IAAA,CAAAgE,OAAA;gBACA;cACA;cACA1D,OAAA,CAAAC,GAAA,kBAAA2J,OAAA,CAAAxL,aAAA;cAAA,OAAA4L,SAAA,CAAA9G,CAAA,IACAoC,QAAA;YAAA;cAAA0E,SAAA,CAAAtH,CAAA;cAAAqH,GAAA,GAAAC,SAAA,CAAA/G,CAAA;cAEAjD,OAAA,CAAAuB,KAAA,+BAAAwI,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA9G,CAAA;UAAA;QAAA,GAAA2G,QAAA;MAAA;IAGA;IACAK,cAAA,WAAAA,eAAAC,GAAA;MACAA,GAAA,CAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAJ,GAAA;MACA,KAAAlL,UAAA,GAAAkL,GAAA;MACA,KAAA3M,iBAAA;MACAwC,OAAA,CAAAC,GAAA,wBAAAkK,GAAA;IACA;IACAK,cAAA,WAAAA,eAAA;MACA,KAAAC,eAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAnM,aAAA;MACA,KAAAX,iBAAA;IACA;IACAiN,iBAAA,WAAAA,kBAAAP,GAAA;MACA;MACA,IAAAA,GAAA,CAAAC,OAAA;QACA;QACAO,MAAA,CAAAC,MAAA,CAAAT,GAAA,EAAAA,GAAA,CAAAC,OAAA;QACA,OAAAD,GAAA,CAAAC,OAAA;MACA;MAAA;MACA,KAAAnL,UAAA;MACA,KAAAzB,iBAAA;IACA;IACAqN,iBAAA,WAAAA,kBAAA;MACA,KAAAzM,aAAA,GAAAiM,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAE,eAAA;MACAzK,OAAA,CAAAC,GAAA,4BAAA7B,aAAA;MACA,KAAAX,iBAAA;IACA;IAEAqN,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAjJ,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAiD,QAAA;QACA,KAAAzD,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,SAAAnD,aAAA,CAAAmB,MAAA;QACAS,OAAA,CAAAC,GAAA,uBAAA7B,aAAA;QACA,KAAAkD,QAAA,CAAA4F,OAAA;QACA;MACA;;MAEA;MAAA,IAAA8D,SAAA,OAAAC,2BAAA,CAAA/I,OAAA,EACA,KAAA9D,aAAA;QAAA8M,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAvI,CAAA,IAAA2I,IAAA;UAAA,IAAA1L,IAAA,GAAAwL,KAAA,CAAApG,KAAA;UACA,IAAApF,IAAA,CAAAiE,mBAAA,KAAAjE,IAAA,CAAAgE,OAAA;YACA,KAAApC,QAAA,CAAA4F,OAAA,kBAAAwC,MAAA,CAAAhK,IAAA,CAAAC,YAAA,+DAAA+J,MAAA,CAAAhK,IAAA,CAAAiE,mBAAA,sCAAA+F,MAAA,CAAAhK,IAAA,CAAAgE,OAAA;YACA;UACA;QACA;MAAA,SAAAhC,GAAA;QAAAsJ,SAAA,CAAAK,CAAA,CAAA3J,GAAA;MAAA;QAAAsJ,SAAA,CAAAM,CAAA;MAAA;MAEA,IAAAnE,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAhM,MAAA,QAAAA,MAAA;MACAgM,YAAA,CAAA/L,OAAA,QAAAA,OAAA;MACA+L,YAAA,CAAAE,IAAA,yBAAAjJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MAEA,IAAAiI,eAAA;MACAA,eAAA,CAAAlK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACAuM,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAC,SAAA,OAAA9D,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAoJ,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAoJ,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAC,SAAA,OAAA9D,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;QACAC,gBAAA,OAAApJ,aAAA;QACAqJ,QAAA,EAAAN,YAAA;QACApB,SAAA,EAAA8B,eAAA;QACApJ,WAAA,OAAAA;MACA;MAEAuB,OAAA,CAAAC,GAAA,iDAAAsH,KAAA,OAAA/I,YAAA,CAAAlD,QAAA;MAGA,IAAAoM,kDAAA,EAAAH,KAAA,EAAArG,IAAA,WAAAC,GAAA;QACAnB,OAAA,CAAAC,GAAA,iDAAAkB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA2J,OAAA,CAAAzJ,QAAA,CAAAsF,OAAA;UACAmE,OAAA,CAAAhI,cAAA,CAAAgI,OAAA,CAAA5P,MAAA;UACA4P,OAAA,CAAApI,WAAA;QACA;UACA;UACAoI,OAAA,CAAAzJ,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,gCAAAG,GAAA;QACAqJ,OAAA,CAAAzJ,QAAA,CAAAC,KAAA;MACA;MAEA,KAAA/D,iBAAA;IACA;IAEA+N,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAA1J,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA9B,OAAA,CAAAC,GAAA,UAAA6B,KAAA;MACA,KAAAA,KAAA,CAAAiD,QAAA;QACA,KAAAzD,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,SAAAnD,aAAA,CAAAmB,MAAA;QACAS,OAAA,CAAAC,GAAA,uBAAA7B,aAAA;QACA,KAAAkD,QAAA,CAAA4F,OAAA;QACA;MACA;;MAEA;MAAA,IAAAuE,UAAA,OAAAR,2BAAA,CAAA/I,OAAA,EACA,KAAA9D,aAAA;QAAAsN,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAN,CAAA,MAAAO,MAAA,GAAAD,UAAA,CAAAhJ,CAAA,IAAA2I,IAAA;UAAA,IAAA1L,IAAA,GAAAgM,MAAA,CAAA5G,KAAA;UACA,IAAApF,IAAA,CAAA+D,iBAAA,KAAA/D,IAAA,CAAAgE,OAAA;YACA,KAAApC,QAAA,CAAA4F,OAAA,kBAAAwC,MAAA,CAAAhK,IAAA,CAAAC,YAAA,mDAAA+J,MAAA,CAAAhK,IAAA,CAAA+D,iBAAA,sCAAAiG,MAAA,CAAAhK,IAAA,CAAAgE,OAAA;YACA;UACA;QACA;MAAA,SAAAhC,GAAA;QAAA+J,UAAA,CAAAJ,CAAA,CAAA3J,GAAA;MAAA;QAAA+J,UAAA,CAAAH,CAAA;MAAA;MAEA,IAAAnE,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAhM,MAAA,QAAAA,MAAA;MACAgM,YAAA,CAAA/L,OAAA,QAAAA,OAAA;MACA+L,YAAA,CAAAE,IAAA,yBAAAjJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MAEA,IAAAiI,eAAA;MACAA,eAAA,CAAAlK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACAuM,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAC,SAAA,OAAA9D,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAoJ,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAoJ,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAC,SAAA,OAAA9D,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAAzJ,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA0L,eAAA,CAAA1L,UAAA;QACA0L,eAAA,CAAAK,SAAA,OAAAlE,IAAA,GAAA+D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;QACAC,gBAAA,OAAApJ,aAAA;QACAqJ,QAAA,EAAAN,YAAA;QACApB,SAAA,EAAA8B,eAAA;QACApJ,WAAA,OAAAA;MACA;MAEAuB,OAAA,CAAAC,GAAA,iDAAAsH,KAAA,OAAA/I,YAAA,CAAAlD,QAAA;MAGA,IAAAoM,kDAAA,EAAAH,KAAA,EAAArG,IAAA,WAAAC,GAAA;QACAnB,OAAA,CAAAC,GAAA,iDAAAkB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAoK,OAAA,CAAAlK,QAAA,CAAAsF,OAAA;UACA4E,OAAA,CAAAzI,cAAA,CAAAyI,OAAA,CAAArQ,MAAA;UACAqQ,OAAA,CAAA7I,WAAA;QACA;UACA;UACA6I,OAAA,CAAAlK,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,gCAAAG,GAAA;QACA8J,OAAA,CAAAlK,QAAA,CAAAC,KAAA;MACA;MAEA,KAAA/D,iBAAA;IACA;IAGAmO,cAAA,WAAAA,eAAA;MAEA,KAAAlO,iBAAA;IACA;IAEAyC,iBAAA,WAAAA,kBAAA;MACA,KAAA1B,YAAA;IACA;IAEAmE,WAAA,WAAAA,YAAA;MAAA,IAAAiJ,OAAA;MAAA,WAAA3J,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAyJ,SAAA;QAAA,IAAAvG,QAAA,EAAAwG,GAAA;QAAA,WAAA3J,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAwJ,SAAA;UAAA,kBAAAA,SAAA,CAAAtJ,CAAA;YAAA;cAAAsJ,SAAA,CAAArJ,CAAA;cAAAqJ,SAAA,CAAAtJ,CAAA;cAAA,OAEA,IAAAuJ,aAAA,EAAAJ,OAAA,CAAArN,UAAA;YAAA;cAAA+G,QAAA,GAAAyG,SAAA,CAAA9I,CAAA;cACA2I,OAAA,CAAApN,YAAA,GAAA8G,QAAA,CAAAvK,IAAA;cACAiF,OAAA,CAAAC,GAAA,sBAAA2L,OAAA,CAAApN,YAAA;cACA,IAAAoN,OAAA,CAAApN,YAAA,CAAAyN,iBAAA;gBACAL,OAAA,CAAApN,YAAA,CAAAyN,iBAAA;cACA,WAAAL,OAAA,CAAApN,YAAA,CAAAyN,iBAAA;gBACAL,OAAA,CAAApN,YAAA,CAAAyN,iBAAA;cACA,WAAAL,OAAA,CAAApN,YAAA,CAAAyN,iBAAA;gBACAL,OAAA,CAAApN,YAAA,CAAAyN,iBAAA;cACA,WAAAL,OAAA,CAAApN,YAAA,CAAAyN,iBAAA;gBACAL,OAAA,CAAApN,YAAA,CAAAyN,iBAAA;cACA;cACAjM,OAAA,CAAAC,GAAA,sBAAA2L,OAAA,CAAApN,YAAA;cACA;cACAoN,OAAA,CAAAM,SAAA;gBACAN,OAAA,CAAAvD,WAAA;cACA;cAAA,OAAA0D,SAAA,CAAA7I,CAAA,IACAoC,QAAA;YAAA;cAAAyG,SAAA,CAAArJ,CAAA;cAAAoJ,GAAA,GAAAC,SAAA,CAAA9I,CAAA;cAEAjD,OAAA,CAAAuB,KAAA,uBAAAuK,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA7I,CAAA;UAAA;QAAA,GAAA2I,QAAA;MAAA;IAGA;IAEAtI,mBAAA,WAAAA,oBAAA;MAAA,IAAA4I,OAAA;MAAA,WAAAlK,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAgK,SAAA;QAAA,IAAA9G,QAAA,EAAA+G,GAAA;QAAA,WAAAlK,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAA+J,SAAA;UAAA,kBAAAA,SAAA,CAAA7J,CAAA;YAAA;cAAA6J,SAAA,CAAA5J,CAAA;cAAA4J,SAAA,CAAA7J,CAAA;cAAA,OAEA,IAAA8J,qBAAA,EAAAJ,OAAA,CAAAhR,MAAA;YAAA;cAAAmK,QAAA,GAAAgH,SAAA,CAAArJ,CAAA;cACAkJ,OAAA,CAAA3N,YAAA,GAAA8G,QAAA,CAAAvK,IAAA;cACAiF,OAAA,CAAAC,GAAA,sBAAAkM,OAAA,CAAA3N,YAAA;;cAEA;cACA2N,OAAA,CAAA5N,UAAA,GAAA4N,OAAA,CAAA3N,YAAA,CAAAb,EAAA;cACAwO,OAAA,CAAA/Q,OAAA,GAAA+Q,OAAA,CAAA3N,YAAA,CAAApD,OAAA;cAEA,IAAA+Q,OAAA,CAAA3N,YAAA,CAAAyN,iBAAA;gBACAE,OAAA,CAAA3N,YAAA,CAAAyN,iBAAA;cACA,WAAAE,OAAA,CAAA3N,YAAA,CAAAyN,iBAAA;gBACAE,OAAA,CAAA3N,YAAA,CAAAyN,iBAAA;cACA,WAAAE,OAAA,CAAA3N,YAAA,CAAAyN,iBAAA;gBACAE,OAAA,CAAA3N,YAAA,CAAAyN,iBAAA;cACA,WAAAE,OAAA,CAAA3N,YAAA,CAAAyN,iBAAA;gBACAE,OAAA,CAAA3N,YAAA,CAAAyN,iBAAA;cACA;cACAjM,OAAA,CAAAC,GAAA,sBAAAkM,OAAA,CAAA3N,YAAA;cACA;cACA2N,OAAA,CAAAD,SAAA;gBACAC,OAAA,CAAA9D,WAAA;cACA;cAAA,OAAAiE,SAAA,CAAApJ,CAAA,IACAoC,QAAA;YAAA;cAAAgH,SAAA,CAAA5J,CAAA;cAAA2J,GAAA,GAAAC,SAAA,CAAArJ,CAAA;cAEAjD,OAAA,CAAAuB,KAAA,+BAAA8K,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAApJ,CAAA;UAAA;QAAA,GAAAkJ,QAAA;MAAA;IAGA;IAGAI,aAAA,WAAAA,cAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAE,iBAAA,WAAAA,kBAAAF,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IACA;IACAG,wBAAA,WAAAA,yBAAAH,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAI,2BAAA,WAAAA,4BAAAJ,QAAA;MACA,IAAAK,OAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAL,QAAA;IACA;IAEA;IACAM,qBAAA,WAAAA,sBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAE,qBAAA,WAAAA,sBAAAF,MAAA;MACA,IAAAF,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAE,MAAA;IACA;IAEA;IACAG,WAAA,WAAAA,YAAAlN,GAAA;MACA,IAAAmN,eAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,eAAA,CAAAnN,GAAA,CAAAoN,IAAA;IACA;IAEA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAlP,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAmP,wBAAA,WAAAA,yBAAAC,GAAA;MACA,KAAAA,GAAA;QACA,KAAA1S,kBAAA,CAAAkC,WAAA;MACA;IACA;IACAyQ,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAAxQ,mBAAA;MACA,KAAAyQ,WAAA;MACA;MACA,KAAAnP,cAAA;MACA,KAAAuN,SAAA;QACA,IAAA2B,OAAA,CAAAtF,KAAA,CAAAwF,WAAA;UACAF,OAAA,CAAAtF,KAAA,CAAAwF,WAAA,CAAAC,cAAA;QACA;MACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA;MACA,IAAAA,SAAA,CAAA3O,MAAA;QACA,IAAA4O,YAAA,GAAAD,SAAA,CAAAA,SAAA,CAAA3O,MAAA;QACA,KAAAgJ,KAAA,CAAAwF,WAAA,CAAAC,cAAA;QACA,KAAAzF,KAAA,CAAAwF,WAAA,CAAAK,kBAAA,CAAAD,YAAA;QACA,KAAAxP,cAAA,GAAAwP,YAAA;MACA;QACA,KAAAxP,cAAA,GAAAuP,SAAA;MACA;IACA;IACAG,sBAAA,WAAAA,uBAAA;MACA,UAAA1P,cAAA;QACA,KAAA2C,QAAA,CAAA4F,OAAA;QACA;MACA;MAEA,KAAAjM,kBAAA,CAAAkC,WAAA,QAAAwB,cAAA,CAAAvD,OAAA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,KAAAiC,mBAAA;MACA,KAAAiE,QAAA,CAAAsF,OAAA;IAIA;IACAkH,WAAA,WAAAA,YAAA;MAAA,IAAAQ,OAAA;MACA;MACA,KAAA/Q,UAAA,QAAAyB,oBAAA;MACA,KAAAzB,UAAA,CAAAwE,OAAA,WAAArC,IAAA;QACAA,IAAA,CAAA6O,UAAA,GAAAD,OAAA,CAAA3B,iBAAA,CAAAjN,IAAA,CAAA6O,UAAA;MACA;MACAvO,OAAA,CAAAC,GAAA,oBAAA1C,UAAA;IACA;IACAiR,uBAAA,WAAAA,wBAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IACAE,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAC,YAAA,SAAAvR,UAAA,CAAAjC,MAAA,QAAAyT,WAAA;MACA,IAAAC,aAAA,SAAAzR,UAAA,CAAAlC,OAAA,QAAA0T,WAAA;MACA,IAAAE,oBAAA,SAAA1R,UAAA,CAAAL,cAAA,QAAA6R,WAAA;;MAEA;MACA,KAAAvR,UAAA,QAAAyB,oBAAA,CAAA6F,MAAA,WAAAnF,IAAA;QACA,IAAArE,MAAA,IAAAqE,IAAA,CAAArE,MAAA,QAAA4T,QAAA,GAAAH,WAAA;QACA,IAAA1T,OAAA,IAAAsE,IAAA,CAAAtE,OAAA,QAAA6T,QAAA,GAAAH,WAAA;QACA,IAAA7R,cAAA,IAAAyC,IAAA,CAAAzC,cAAA,QAAAgS,QAAA,GAAAH,WAAA;;QAEA;QACA,IAAAI,WAAA,IAAAL,YAAA,IAAAxT,MAAA,CAAA0J,QAAA,CAAA8J,YAAA;QACA,IAAAM,YAAA,IAAAJ,aAAA,IAAA3T,OAAA,CAAA2J,QAAA,CAAAgK,aAAA;QACA,IAAAK,mBAAA,IAAAJ,oBAAA,IAAA/R,cAAA,CAAA8H,QAAA,CAAAiK,oBAAA;QAEA,OAAAE,WAAA,IAAAC,YAAA,IAAAC,mBAAA;MACA;;MAEA;MACA,KAAA7R,UAAA,CAAAwE,OAAA,WAAArC,IAAA;QACAA,IAAA,CAAA6O,UAAA,GAAAK,OAAA,CAAAjC,iBAAA,CAAAjN,IAAA,CAAA6O,UAAA;MACA;IACA;IACAc,WAAA,WAAAA,YAAA;MACA,KAAA/R,UAAA;QACAjC,MAAA;QACAD,OAAA;QACA6B,cAAA;MACA;MACA,KAAA6Q,WAAA;IACA;IACAwB,eAAA,WAAAA,gBAAAjC,IAAA;MACA,IAAAP,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAO,IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACAkC,qBAAA,WAAAA,sBAAArB,SAAA;MACA,KAAAhP,YAAA,GAAAgP,SAAA;IACA;IAEA;IACAsB,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,OAAA;MACA,IAAA3N,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAiD,QAAA;QACA,KAAAzD,QAAA,CAAAC,KAAA;QACA;MACA;MACA,IAAAmO,SAAA;MACA,KAAAxQ,YAAA,CAAA6C,OAAA,WAAArC,IAAA;QACA,IAAAA,IAAA,CAAA+D,iBAAA,KAAA/D,IAAA,CAAAgE,OAAA;UACA+L,OAAA,CAAAnO,QAAA,CAAA4F,OAAA;UACAwI,SAAA;QACA;MACA;MAEA,IAAAA,SAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA,IAAAvI,YAAA;QACAC,OAAA;QACAjM,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAiM,IAAA,wBAAAjJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;;MAEA;MACA,IAAA0H,eAAA;QACA3J,EAAA,OAAAa,YAAA,CAAAb,EAAA;QACApC,eAAA;QAAA;QACAC,aAAA,MAAAwI,IAAA;QACA7H,UAAA;MACA;MAEA,KAAA+C,YAAA,CAAA6C,OAAA,WAAArC,IAAA;QACA;QACAA,IAAA,CAAAiQ,iBAAA,GAAAjQ,IAAA,CAAA+D,iBAAA;MACA;;MAEA;MACA,IAAA8D,KAAA;QACAC,gBAAA,OAAAtI,YAAA;QAAA;QACAuI,QAAA,EAAAN,YAAA;QACApB,SAAA,EAAAuB,eAAA;QACA7I,WAAA,OAAAA;MACA;;MAEA;MACA,IAAAiJ,kDAAA,EAAAH,KAAA,EAAArG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAqO,OAAA,CAAAnO,QAAA,CAAAsF,OAAA;UACA6I,OAAA,CAAA1M,cAAA,CAAA0M,OAAA,CAAAtU,MAAA;UACAsU,OAAA,CAAA9M,WAAA;UACA;UACA8M,OAAA,CAAAvQ,YAAA;QACA;UACAuQ,OAAA,CAAAnO,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA1B,OAAA,CAAAuB,KAAA,0CAAAG,GAAA;QACA+N,OAAA,CAAAnO,QAAA,CAAAC,KAAA;MACA;IACA;IACAqO,iBAAA,WAAAA,kBAAA;MACA5P,OAAA,CAAAC,GAAA,2BAAAd,kBAAA;MACA,IAAAZ,UAAA,QAAAY,kBAAA,CAAAZ,UAAA;MACA,IAAAnD,OAAA,GAAAyU,MAAA,MAAA1Q,kBAAA,CAAA/D,OAAA;MACA,IAAAqD,WAAA,QAAAU,kBAAA,CAAAV,WAAA;MACA,IAAAgC,QAAA,QAAAtB,kBAAA,CAAAsB,QAAA;MACA,IAAAtF,MAAA,GAAA0U,MAAA,MAAA1Q,kBAAA,CAAAhE,MAAA;MACA,IAAA2U,GAAA,kDAAApG,MAAA,CAAAnL,UAAA,eAAAmL,MAAA,CAAAtO,OAAA,mBAAAsO,MAAA,CAAAjL,WAAA,gBAAAiL,MAAA,CAAAjJ,QAAA,cAAAiJ,MAAA,CAAAvO,MAAA;MACAgJ,MAAA,CAAAC,IAAA,CAAA0L,GAAA;IACA;EACA;AACA", "ignoreList": []}]}