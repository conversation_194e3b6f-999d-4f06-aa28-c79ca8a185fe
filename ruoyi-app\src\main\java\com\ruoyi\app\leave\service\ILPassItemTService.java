package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LPassItemT;

/**
 * 不返回物资明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface ILPassItemTService 
{
    /**
     * 查询不返回物资明细
     * 
     * @param id 不返回物资明细ID
     * @return 不返回物资明细
     */
    public LPassItemT selectLPassItemTById(Long id);

    /**
     * 查询不返回物资明细列表
     * 
     * @param lPassItemT 不返回物资明细
     * @return 不返回物资明细集合
     */
    public List<LPassItemT> selectLPassItemTList(LPassItemT lPassItemT);

    /**
     * 新增不返回物资明细
     * 
     * @param lPassItemT 不返回物资明细
     * @return 结果
     */
    public int insertLPassItemT(LPassItemT lPassItemT);

    /**
     * 修改不返回物资明细
     * 
     * @param lPassItemT 不返回物资明细
     * @return 结果
     */
    public int updateLPassItemT(LPassItemT lPassItemT);

    /**
     * 批量删除不返回物资明细
     * 
     * @param ids 需要删除的不返回物资明细ID
     * @return 结果
     */
    public int deleteLPassItemTByIds(Long[] ids);

    /**
     * 删除不返回物资明细信息
     * 
     * @param id 不返回物资明细ID
     * @return 结果
     */
    public int deleteLPassItemTById(Long id);
}
