package com.ruoyi.app.leave.service;

import java.util.Date;
import java.util.List;
import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.domain.LeaveTaskMaterial;
import com.ruoyi.app.leave.domain.StoreoutWwjgMeasure;

/**
 * 外委加工出库Service接口
 * 
 * <AUTHOR>
 */
public interface IStoreoutWwjgService 
{
    /**
     * 查询外委加工出库
     * 
     * @param id 外委加工出库主键
     * @return 外委加工出库
     */
    public StoreoutWwjgMeasure selectStoreoutWwjgById(Long id);

    /**
     * 查询外委加工出库列表
     * 
     * @param storeoutWwjgMeasure 外委加工出库
     * @return 外委加工出库集合
     */
    public List<StoreoutWwjgMeasure> selectStoreoutWwjgList(StoreoutWwjgMeasure storeoutWwjgMeasure);

    /**
     * 新增外委加工出库
     * 
     * @param storeoutWwjgMeasure 外委加工出库
     * @return 结果
     */
    public int insertStoreoutWwjg(StoreoutWwjgMeasure storeoutWwjgMeasure);

    /**
     * 修改外委加工出库
     * 
     * @param storeoutWwjgMeasure 外委加工出库
     * @return 结果
     */
    public int updateStoreoutWwjg(StoreoutWwjgMeasure storeoutWwjgMeasure);

    /**
     * 批量删除外委加工出库
     * 
     * @param ids 需要删除的外委加工出库主键集合
     * @return 结果
     */
    public int deleteStoreoutWwjgByIds(Long[] ids);

    /**
     * 删除外委加工出库信息
     * 
     * @param id 外委加工出库主键
     * @return 结果
     */
    public int deleteStoreoutWwjgById(Long id);

    /**
     * 根据匹配ID删除外委加工出库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    public int deleteStoreoutWwjgByMatchid(String matchid);

    /**
     * 处理外委加工出库
     * 
     * @param nowDate 当前时间
     * @param leaveTask 任务信息
     * @param leavePlan 计划信息
     * @param leaveTaskMaterial 物料信息
     */
    void handleExternalProcessingStockOut(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial);
} 