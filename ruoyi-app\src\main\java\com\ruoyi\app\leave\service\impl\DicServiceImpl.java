package com.ruoyi.app.leave.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.DicMapper;
import com.ruoyi.app.leave.domain.DicMeasure;
import com.ruoyi.app.leave.service.IDicService;

/**
 * IC卡信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class DicServiceImpl implements IDicService 
{
    @Autowired
    private DicMapper dicMapper;

    /**
     * 查询IC卡信息
     * 
     * @param id IC卡信息主键
     * @return IC卡信息
     */
    @Override
    public DicMeasure selectDicById(Long id)
    {
        return dicMapper.selectDicById(id);
    }

    /**
     * 查询IC卡信息列表
     * 
     * @param dicMeasure IC卡信息
     * @return IC卡信息
     */
    @Override
    public List<DicMeasure> selectDicList(DicMeasure dicMeasure)
    {
        return dicMapper.selectDicList(dicMeasure);
    }

    /**
     * 新增IC卡信息
     * 
     * @param dicMeasure IC卡信息
     * @return 结果
     */
    @Override
    public int insertDic(DicMeasure dicMeasure)
    {
        return dicMapper.insertDic(dicMeasure);
    }

    /**
     * 修改IC卡信息
     * 
     * @param dicMeasure IC卡信息
     * @return 结果
     */
    @Override
    public int updateDic(DicMeasure dicMeasure)
    {
        return dicMapper.updateDic(dicMeasure);
    }

    /**
     * 批量删除IC卡信息
     * 
     * @param ids 需要删除的IC卡信息主键
     * @return 结果
     */
    @Override
    public int deleteDicByIds(Long[] ids)
    {
        return dicMapper.deleteDicByIds(ids);
    }

    /**
     * 删除IC卡信息信息
     * 
     * @param id IC卡信息主键
     * @return 结果
     */
    @Override
    public int deleteDicById(Long id)
    {
        return dicMapper.deleteDicById(id);
    }

    /**
     * 根据匹配ID删除IC卡信息
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @Override
    public int deleteDicByMatchid(String matchid)
    {
        return dicMapper.deleteDicByMatchid(matchid);
    }
} 