package com.ruoyi.app.leave.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LPassItemTMapper;
import com.ruoyi.app.leave.domain.LPassItemT;
import com.ruoyi.app.leave.service.ILPassItemTService;

/**
 * 不返回物资明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class LPassItemTServiceImpl implements ILPassItemTService 
{
    @Autowired
    private LPassItemTMapper lPassItemTMapper;

    /**
     * 查询不返回物资明细
     * 
     * @param id 不返回物资明细ID
     * @return 不返回物资明细
     */
    @Override
    public LPassItemT selectLPassItemTById(Long id)
    {
        return lPassItemTMapper.selectLPassItemTById(id);
    }

    /**
     * 查询不返回物资明细列表
     * 
     * @param lPassItemT 不返回物资明细
     * @return 不返回物资明细
     */
    @Override
    public List<LPassItemT> selectLPassItemTList(LPassItemT lPassItemT)
    {
        return lPassItemTMapper.selectLPassItemTList(lPassItemT);
    }

    /**
     * 新增不返回物资明细
     * 
     * @param lPassItemT 不返回物资明细
     * @return 结果
     */
    @Override
    public int insertLPassItemT(LPassItemT lPassItemT)
    {
        return lPassItemTMapper.insertLPassItemT(lPassItemT);
    }

    /**
     * 修改不返回物资明细
     * 
     * @param lPassItemT 不返回物资明细
     * @return 结果
     */
    @Override
    public int updateLPassItemT(LPassItemT lPassItemT)
    {
        return lPassItemTMapper.updateLPassItemT(lPassItemT);
    }

    /**
     * 批量删除不返回物资明细
     * 
     * @param ids 需要删除的不返回物资明细ID
     * @return 结果
     */
    @Override
    public int deleteLPassItemTByIds(Long[] ids)
    {
        return lPassItemTMapper.deleteLPassItemTByIds(ids);
    }

    /**
     * 删除不返回物资明细信息
     * 
     * @param id 不返回物资明细ID
     * @return 结果
     */
    @Override
    public int deleteLPassItemTById(Long id)
    {
        return lPassItemTMapper.deleteLPassItemTById(id);
    }
}
