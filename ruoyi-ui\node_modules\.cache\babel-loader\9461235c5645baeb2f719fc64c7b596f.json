{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\list.vue", "mtime": 1756170476766}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLnRvLWZpeGVkLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5zb21lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIik7CnZhciBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkU6L2phdmFfd29ya3NwYWNlL25ld193b3Jrc3BhY2UveGN0Zy9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyLmpzIikpOwp2YXIgX2luZm8gPSByZXF1aXJlKCJAL2FwaS9hc3Nlc3Mvc2VsZi9pbmZvIik7CnZhciBfdXNlciA9IHJlcXVpcmUoIkAvYXBpL2Fzc2Vzcy9zZWxmL3VzZXIiKTsKdmFyIF9pbmRleCA9IHJlcXVpcmUoIkAvdXRpbHMvaW5kZXgiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJTZWxmQXNzZXNzQ2hlY2siLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaYvuekuuWIl+ihqOS6i+S4mumDqOivhOWIhgogICAgICBzaG93QnVzaW5lc3M6IGZhbHNlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIGNoZWNrZWRUb3RhbDogMCwKICAgICAgLy8g57up5pWI6ICD5qC4LeW5sumDqOiHquivhOS6uuWRmOmFjee9ruihqOagvOaVsOaNrgogICAgICBsaXN0VG9DaGVjazogW10sCiAgICAgIGxpc3RDaGVja2VkOiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB3b3JrTm86IG51bGwsCiAgICAgICAgbmFtZTogbnVsbCwKICAgICAgICBkZXB0SWQ6IG51bGwsCiAgICAgICAgYXNzZXNzRGF0ZTogbnVsbAogICAgICB9LAogICAgICAvLyDor4TliIborrDlvZXmn6Xor6Llj4LmlbAKICAgICAgY2hlY2tlZFF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgd29ya05vOiBudWxsLAogICAgICAgIG5hbWU6IG51bGwsCiAgICAgICAgZGVwdElkOiBudWxsLAogICAgICAgIGFzc2Vzc0RhdGU6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICAvLyDpg6jpl6jpooblr7zliqDlh4/liIYKICAgICAgICBkZXB0QWRkU2NvcmU6IG51bGwsCiAgICAgICAgLy8g5LqL5Lia6YOo5Yqg5YeP5YiGCiAgICAgICAgYnVzaW5lc3NBZGRTY29yZTogbnVsbCwKICAgICAgICAvLyDpg6jpl6jpooblr7zor4TliIbnkIbnlLEKICAgICAgICBkZXB0U2NvcmVSZWFzb246IG51bGwsCiAgICAgICAgLy8g5LqL5Lia6YOo6K+E5YiG55CG55SxCiAgICAgICAgYnVzaW5lc3NTY29yZVJlYXNvbjogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHt9LAogICAgICBkZXB0T3B0aW9uczogW10sCiAgICAgIG9wZW5DaGVjazogZmFsc2UsCiAgICAgIGNoZWNrSW5mbzoge30sCiAgICAgIC8vIOWQiOW5tuWNleWFg+agvAogICAgICBzcGFuTGlzdDogW10sCiAgICAgIC8vIOW+heivhOWIhuagh+etvgogICAgICB0b0NoZWNrTGFiZWw6ICLlvoXor4TliIYoMCkiLAogICAgICAvLyDlv6vpgJ/or4TliIbmj5DkuqTnirbmgIEKICAgICAgc3VibWl0dGluZzogZmFsc2UsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5om56YeP5b+r6YCf6K+E5YiG5a+56K+d5qGG5pi+56S654q25oCBCiAgICAgIHF1aWNrU2NvcmVEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgLy8g5om56YeP5b+r6YCf6K+E5YiG6KGo5Y2V5Y+C5pWwCiAgICAgIGJhdGNoUXVpY2tTY29yZUZvcm06IHsKICAgICAgICBzY29yZTogdW5kZWZpbmVkLAogICAgICAgIGlkczogW10KICAgICAgfSwKICAgICAgLy8g5om56YeP5b+r6YCf6K+E5YiG6KGo5Y2V6aqM6K+B6KeE5YiZCiAgICAgIGJhdGNoUXVpY2tTY29yZVJ1bGVzOiB7CiAgICAgICAgc2NvcmU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor4TliIbkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfSwgewogICAgICAgICAgdHlwZTogJ251bWJlcicsCiAgICAgICAgICBtZXNzYWdlOiAi6K+E5YiG5b+F6aG75Li65pWw5a2XIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOaJuemHj+W/q+mAn+ivhOWIhuWvueivneahhgogICAgICBiYXRjaFF1aWNrU2NvcmVPcGVuOiBmYWxzZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmAieS4reeahOihjOaVsOaNrgogICAgICBzZWxlY3RlZFJvd3M6IFtdCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8vIOaYr+WQpuWPr+S7peaPkOS6pOaJuemHj+ivhOWIhu+8iOWfuuehgOajgOafpe+8iQogICAgY2FuU3VibWl0QmF0Y2hTY29yZTogZnVuY3Rpb24gY2FuU3VibWl0QmF0Y2hTY29yZSgpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSb3dzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIGZhbHNlOwoKICAgICAgLy8g5Z+656GA5qOA5p+l77ya5piv5ZCm5omA5pyJ6KGM6YO95aGr5YaZ5LqG5Yqg5YeP5YiGCiAgICAgIHZhciBfaXRlcmF0b3IgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKHRoaXMuc2VsZWN0ZWRSb3dzKSwKICAgICAgICBfc3RlcDsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvci5zKCk7ICEoX3N0ZXAgPSBfaXRlcmF0b3IubigpKS5kb25lOykgewogICAgICAgICAgdmFyIHJvdyA9IF9zdGVwLnZhbHVlOwogICAgICAgICAgaWYgKHJvdy5xdWlja0FkZFNjb3JlID09PSBudWxsIHx8IHJvdy5xdWlja0FkZFNjb3JlID09PSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2l0ZXJhdG9yLmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3IuZigpOwogICAgICB9CiAgICAgIHJldHVybiB0cnVlOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSA9ICgwLCBfaW5kZXguZm9ybWF0RGF0ZVltKShuZXcgRGF0ZSgpLmdldFRpbWUoKSk7CiAgICB0aGlzLmNoZWNrZWRRdWVyeVBhcmFtcy5hc3Nlc3NEYXRlID0gKDAsIF9pbmRleC5mb3JtYXREYXRlWW0pKG5ldyBEYXRlKCkuZ2V0VGltZSgpKTsKICAgIC8vIHRoaXMuZ2V0U2VsZkFzc2Vzc1VzZXIoKTsKICAgIHRoaXMuZ2V0Q2hlY2tEZXB0TGlzdCgpOwogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldENoZWNrZWRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDojrflj5bpg6jpl6jkv6Hmga8KICAgIGdldENoZWNrRGVwdExpc3Q6IGZ1bmN0aW9uIGdldENoZWNrRGVwdExpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgICgwLCBfdXNlci5nZXRDaGVja0RlcHRMaXN0KSgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGNvbnNvbGUubG9nKHJlcyk7CiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgewogICAgICAgICAgdmFyIGRlcHRPcHRpb25zID0gW107CiAgICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgIGRlcHRPcHRpb25zLnB1c2goewogICAgICAgICAgICAgIGRlcHROYW1lOiBpdGVtLmRlcHROYW1lLAogICAgICAgICAgICAgIGRlcHRJZDogaXRlbS5kZXB0SWQKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKICAgICAgICAgIF90aGlzLmRlcHRPcHRpb25zID0gZGVwdE9wdGlvbnM7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5p+l6K+i57up5pWI6ICD5qC4LeW5sumDqOiHquivhOW+heWuoeaguOWIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAoMCwgX2luZm8ubGlzdFRvQ2hlY2spKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLmxpc3RUb0NoZWNrID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpczIudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpczIudG9DaGVja0xhYmVsID0gIlx1NUY4NVx1OEJDNFx1NTIwNigiLmNvbmNhdChyZXNwb25zZS50b3RhbCwgIikiKTsKICAgICAgICBfdGhpczIubG9hZGluZyA9IGZhbHNlOwogICAgICAgIF90aGlzMi5zaG91bGRCdXNpbmVzc0Rpc3BsYXkoKTsKICAgICAgfSk7CiAgICB9LAogICAgc2hvdWxkQnVzaW5lc3NEaXNwbGF5OiBmdW5jdGlvbiBzaG91bGRCdXNpbmVzc0Rpc3BsYXkoKSB7CiAgICAgIHRoaXMuc2hvd0J1c2luZXNzID0gdGhpcy5saXN0VG9DaGVjay5zb21lKGZ1bmN0aW9uIChyb3cpIHsKICAgICAgICByZXR1cm4gcm93WyJzdGF0dXMiXSA9PSAnMic7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDojrflj5blt7LlrqHmoLjliJfooaggKi9nZXRDaGVja2VkTGlzdDogZnVuY3Rpb24gZ2V0Q2hlY2tlZExpc3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAoMCwgX2luZm8ubGlzdENoZWNrZWQpKHRoaXMuY2hlY2tlZFF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczMubGlzdENoZWNrZWQgPSByZXMucm93czsKICAgICAgICBfdGhpczMuY2hlY2tlZFRvdGFsID0gcmVzLnRvdGFsOwogICAgICAgIF90aGlzMy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBkZXB0QWRkU2NvcmU6IG51bGwsCiAgICAgICAgYnVzaW5lc3NBZGRTY29yZTogbnVsbCwKICAgICAgICBkZXB0U2NvcmVSZWFzb246IG51bGwsCiAgICAgICAgYnVzaW5lc3NTY29yZVJlYXNvbjogbnVsbAogICAgICB9OwogICAgICAvLyB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuY2hlY2tlZFF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICAvLyDlkIzmraXmkJzntKLmnaHku7YKICAgICAgdGhpcy5jaGVja2VkUXVlcnlQYXJhbXMubmFtZSA9IHRoaXMucXVlcnlQYXJhbXMubmFtZTsKICAgICAgdGhpcy5jaGVja2VkUXVlcnlQYXJhbXMuZGVwdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQ7CiAgICAgIHRoaXMuY2hlY2tlZFF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGU7CiAgICAgIHRoaXMuZ2V0Q2hlY2tlZExpc3QoKTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWuoeaJueivpuaDhQogICAgaGFuZGxlQ2hlY2tEZXRhaWw6IGZ1bmN0aW9uIGhhbmRsZUNoZWNrRGV0YWlsKHJvdykgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgKDAsIF9pbmZvLmdldEluZm8pKHsKICAgICAgICBpZDogcm93LmlkCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGNvbnNvbGUubG9nKHJlcyk7CiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgewogICAgICAgICAgX3RoaXM0LmNoZWNrSW5mbyA9IHJlcy5kYXRhOwogICAgICAgICAgdmFyIGxpc3QgPSBKU09OLnBhcnNlKHJlcy5kYXRhLmNvbnRlbnQpOwogICAgICAgICAgX3RoaXM0LmhhbmRsZVNwYW5MaXN0KGxpc3QpOwogICAgICAgICAgX3RoaXM0LmNoZWNrSW5mby5saXN0ID0gbGlzdDsKICAgICAgICB9CiAgICAgICAgX3RoaXM0Lm9wZW4gPSB0cnVlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlrqHmibnmj5DkuqQKICAgIGNoZWNrU3VibWl0OiBmdW5jdGlvbiBjaGVja1N1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIGlmICh0aGlzLnZlcmlmeSgpKSB7CiAgICAgICAgdmFyIHBvaW50ID0gdGhpcy5nZXREZXB0U2NvcmVGcm9tRm9ybSgpOwogICAgICAgIGlmICh0aGlzLmNoZWNrSW5mby5zdGF0dXMgPT0gJzInKSBwb2ludCA9IHRoaXMuZ2V0QnVzaW5lc3NTY29yZUZyb21Gb3JtKCk7CiAgICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6kJyArIHRoaXMuY2hlY2tJbmZvLm5hbWUgKyAn6K+E5YiG5Li677yaJyArIHBvaW50ICsgJ+WIhicsICfmj5DnpLonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXM1Lm9uQ2hlY2soKTsKICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICAgIH0KICAgIH0sCiAgICBvbkNoZWNrOiBmdW5jdGlvbiBvbkNoZWNrKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy5mb3JtLmlkID0gdGhpcy5jaGVja0luZm8uaWQ7CiAgICAgIHRoaXMuZm9ybS5zdGF0dXMgPSB0aGlzLmNoZWNrSW5mby5zdGF0dXM7CgogICAgICAvLyDorqHnrpfmnIDnu4jor4TliIYKICAgICAgaWYgKHRoaXMuY2hlY2tJbmZvLnN0YXR1cyA9PSAnMScpIHsKICAgICAgICB0aGlzLmZvcm0uZGVwdFNjb3JlID0gdGhpcy5nZXREZXB0U2NvcmVGcm9tRm9ybSgpOwogICAgICB9CiAgICAgIGlmICh0aGlzLmNoZWNrSW5mby5zdGF0dXMgPT0gJzInKSB7CiAgICAgICAgdGhpcy5mb3JtLmJ1c2luZXNzU2NvcmUgPSB0aGlzLmdldEJ1c2luZXNzU2NvcmVGcm9tRm9ybSgpOwogICAgICB9CiAgICAgICgwLCBfaW5mby5jaGVjaykodGhpcy5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMpOwogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzNi4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgbWVzc2FnZTogJ+aPkOS6pOaIkOWKnyEnCiAgICAgICAgICB9KTsKICAgICAgICAgIF90aGlzNi5yZXNldCgpOwogICAgICAgICAgX3RoaXM2Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgIF90aGlzNi5nZXRMaXN0KCk7CiAgICAgICAgICBfdGhpczYuZ2V0Q2hlY2tlZExpc3QoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM2LiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgICBtZXNzYWdlOiAn5pON5L2c5aSx6LSl77yM5peg5p2D6ZmQ5oiW5b2T5YmN5a6h5om554q25oCB5LiN5Yy56YWNJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDmlbDmja7pqozor4EKICAgIHZlcmlmeTogZnVuY3Rpb24gdmVyaWZ5KCkgewogICAgICBpZiAodGhpcy5jaGVja0luZm8uc3RhdHVzID09ICcxJyAmJiB0aGlzLmZvcm0uZGVwdEFkZFNjb3JlID09PSBudWxsKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICBtZXNzYWdlOiAn6K+35aGr5YaZ5Yqg5YeP5YiGJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBpZiAodGhpcy5jaGVja0luZm8uc3RhdHVzID09ICcyJyAmJiB0aGlzLmZvcm0uYnVzaW5lc3NBZGRTY29yZSA9PT0gbnVsbCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+Whq+WGmeWKoOWHj+WIhicKICAgICAgICB9KTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuY2hlY2tJbmZvLnN0YXR1cyA9PSAnMScgJiYgdGhpcy5mb3JtLmRlcHRBZGRTY29yZSAhPT0gMCAmJiAhdGhpcy5mb3JtLmRlcHRTY29yZVJlYXNvbikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgbWVzc2FnZTogJ+acieWKoOWHj+WIhuaXtuivt+Whq+WGmeWKoOWHj+WIhueQhueUsScKICAgICAgICB9KTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuY2hlY2tJbmZvLnN0YXR1cyA9PSAnMicgJiYgdGhpcy5mb3JtLmJ1c2luZXNzQWRkU2NvcmUgIT09IDAgJiYgIXRoaXMuZm9ybS5idXNpbmVzc1Njb3JlUmVhc29uKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICBtZXNzYWdlOiAn5pyJ5Yqg5YeP5YiG5pe26K+35aGr5YaZ5Yqg5YeP5YiG55CG55SxJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICByZXR1cm4gdHJ1ZTsKICAgIH0sCiAgICBoYW5kbGVMaXN0Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVMaXN0Q2hhbmdlKHR5cGUpIHsKICAgICAgY29uc29sZS5sb2codHlwZSk7CiAgICB9LAogICAgLy8g5aSE55CG5YiX6KGoCiAgICBoYW5kbGVTcGFuTGlzdDogZnVuY3Rpb24gaGFuZGxlU3Bhbkxpc3QoZGF0YSkgewogICAgICB2YXIgc3Bhbkxpc3QgPSBbXTsKICAgICAgdmFyIGZsYWcgPSAwOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICAvLyDnm7jlkIzogIPmoLjpobnlkIjlubYKICAgICAgICBpZiAoaSA9PSAwKSB7CiAgICAgICAgICBzcGFuTGlzdC5wdXNoKHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGlmIChkYXRhW2kgLSAxXS5pdGVtID09IGRhdGFbaV0uaXRlbSkgewogICAgICAgICAgICBzcGFuTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICAgIGNvbHNwYW46IDAKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHNwYW5MaXN0W2ZsYWddLnJvd3NwYW4gKz0gMTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHNwYW5MaXN0LnB1c2goewogICAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgICB9KTsKICAgICAgICAgICAgZmxhZyA9IGk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuc3Bhbkxpc3QgPSBzcGFuTGlzdDsKICAgIH0sCiAgICAvLyDlkIjlubbljZXlhYPmoLzmlrnms5UKICAgIG9iamVjdFNwYW5NZXRob2Q6IGZ1bmN0aW9uIG9iamVjdFNwYW5NZXRob2QoX3JlZikgewogICAgICB2YXIgcm93ID0gX3JlZi5yb3csCiAgICAgICAgY29sdW1uID0gX3JlZi5jb2x1bW4sCiAgICAgICAgcm93SW5kZXggPSBfcmVmLnJvd0luZGV4LAogICAgICAgIGNvbHVtbkluZGV4ID0gX3JlZi5jb2x1bW5JbmRleDsKICAgICAgLy8g56ys5LiA5YiX55u45ZCM6aG55ZCI5bm2CiAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gMCkgewogICAgICAgIHJldHVybiB0aGlzLnNwYW5MaXN0W3Jvd0luZGV4XTsKICAgICAgfQogICAgICAvLyDnsbvliKvml6DlhoXlrrkg5ZCI5bm2CiAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gMSkgewogICAgICAgIGlmICghcm93LmNhdGVnb3J5KSB7CiAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICBjb2xzcGFuOiAwCiAgICAgICAgICB9OwogICAgICAgIH0KICAgICAgfQogICAgICBpZiAoY29sdW1uSW5kZXggPT09IDIpIHsKICAgICAgICBpZiAoIXJvdy5jYXRlZ29yeSkgewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMgogICAgICAgICAgfTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvKiog5b+r6YCf6K+E5YiG5o+Q5LqkICovaGFuZGxlUXVpY2tTdWJtaXQ6IGZ1bmN0aW9uIGhhbmRsZVF1aWNrU3VibWl0KHJvdykgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgaWYgKCFyb3cucXVpY2tTY29yZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl6K+E5YiGJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOaPkOS6pOivpeivhOWIhuWQl++8nycsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczcuJHNldChyb3csICdzdWJtaXR0aW5nJywgdHJ1ZSk7CiAgICAgICAgdmFyIGRhdGEgPSB7CiAgICAgICAgICBpZDogcm93LmlkLAogICAgICAgICAgc2NvcmU6IHJvdy5xdWlja1Njb3JlLAogICAgICAgICAgdHlwZTogcm93LnR5cGUKICAgICAgICB9OwogICAgICAgICgwLCBfaW5mby5jaGVjaykoZGF0YSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgIF90aGlzNy4kbWVzc2FnZS5zdWNjZXNzKCfor4TliIbmj5DkuqTmiJDlip8nKTsKICAgICAgICAgIF90aGlzNy5nZXRMaXN0KCk7CiAgICAgICAgICBfdGhpczcuZ2V0Q2hlY2tlZExpc3QoKTsKICAgICAgICB9KS5maW5hbGx5KGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzNy4kc2V0KHJvdywgJ3N1Ym1pdHRpbmcnLCBmYWxzZSk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDpgInmi6nmnaHmlbDmlLnlj5ggKi9oYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uaWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNlbGVjdGVkUm93cyA9IHNlbGVjdGlvbjsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOaJuemHj+W/q+mAn+ivhOWIhuaMiemSruaTjeS9nCAqL2hhbmRsZUJhdGNoUXVpY2tTY29yZTogZnVuY3Rpb24gaGFuZGxlQmF0Y2hRdWlja1Njb3JlKCkgewogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqemcgOimgeivhOWIhueahOaVsOaNriIpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g6aqM6K+B6K+E5YiG5LiA6Ie05oCn5ZKM55CG55Sx5b+F5aGrCiAgICAgIHZhciB2YWxpZGF0aW9uUmVzdWx0ID0gdGhpcy52YWxpZGF0ZUJhdGNoUXVpY2tTY29yZSgpOwogICAgICBpZiAoIXZhbGlkYXRpb25SZXN1bHQuaXNWYWxpZCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHZhbGlkYXRpb25SZXN1bHQubWVzc2FnZSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuYmF0Y2hRdWlja1Njb3JlT3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOWPlua2iOaJuemHj+W/q+mAn+ivhOWIhuaTjeS9nCAqL2NhbmNlbEJhdGNoUXVpY2tTY29yZTogZnVuY3Rpb24gY2FuY2VsQmF0Y2hRdWlja1Njb3JlKCkgewogICAgICB0aGlzLmJhdGNoUXVpY2tTY29yZU9wZW4gPSBmYWxzZTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5om56YeP5b+r6YCf6K+E5YiGICovc3VibWl0QmF0Y2hRdWlja1Njb3JlOiBmdW5jdGlvbiBzdWJtaXRCYXRjaFF1aWNrU2NvcmUoKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICAvLyDlh4blpIfmj5DkuqTmlbDmja4KICAgICAgdmFyIHN1Ym1pdERhdGEgPSB0aGlzLnNlbGVjdGVkUm93cy5tYXAoZnVuY3Rpb24gKHJvdykgewogICAgICAgIHZhciBmaW5hbFNjb3JlOwogICAgICAgIGlmIChyb3cuc3RhdHVzID09ICcxJykgewogICAgICAgICAgLy8g6YOo6Zeo6aKG5a+86K+E5YiGID0g6Ieq6K+E5YiGICsg5Yqg5YeP5YiGCiAgICAgICAgICBmaW5hbFNjb3JlID0gX3RoaXM4LmdldERlcHRTY29yZShyb3cpOwogICAgICAgIH0gZWxzZSBpZiAocm93LnN0YXR1cyA9PSAnMicpIHsKICAgICAgICAgIC8vIOS6i+S4mumDqOivhOWIhiA9IOmDqOmXqOmihuWvvOivhOWIhiArIOWKoOWHj+WIhgogICAgICAgICAgZmluYWxTY29yZSA9IF90aGlzOC5nZXRCdXNpbmVzc1Njb3JlKHJvdyk7CiAgICAgICAgfQogICAgICAgIHJldHVybiB7CiAgICAgICAgICBpZDogcm93LmlkLAogICAgICAgICAgcXVpY2tTY29yZTogcGFyc2VGbG9hdChmaW5hbFNjb3JlKSwKICAgICAgICAgIHF1aWNrQWRkU2NvcmU6IHJvdy5xdWlja0FkZFNjb3JlLAogICAgICAgICAgcXVpY2tSZWFzb246IHJvdy5xdWlja1JlYXNvbgogICAgICAgIH07CiAgICAgIH0pOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmj5DkuqTpgInkuK3kurrlkZjnmoTlv6vpgJ/or4TliIbvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9pbmZvLmJhdGNoUXVpY2tTY29yZSkoc3VibWl0RGF0YSk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzOC4kbW9kYWwubXNnU3VjY2Vzcygi5om56YeP6K+E5YiG5oiQ5YqfIik7CiAgICAgICAgX3RoaXM4LmJhdGNoUXVpY2tTY29yZU9wZW4gPSBmYWxzZTsKICAgICAgICBfdGhpczguZ2V0TGlzdCgpOwogICAgICAgIF90aGlzOC5nZXRDaGVja2VkTGlzdCgpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOmqjOivgeaJuemHj+W/q+mAn+ivhOWIhiAqL3ZhbGlkYXRlQmF0Y2hRdWlja1Njb3JlOiBmdW5jdGlvbiB2YWxpZGF0ZUJhdGNoUXVpY2tTY29yZSgpIHsKICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCB0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGg7IGkrKykgewogICAgICAgIHZhciByb3cgPSB0aGlzLnNlbGVjdGVkUm93c1tpXTsKCiAgICAgICAgLy8g5qOA5p+l5piv5ZCm5aGr5YaZ5LqG5Yqg5YeP5YiG77yI5YWB6K645Li6MO+8iQogICAgICAgIGlmIChyb3cucXVpY2tBZGRTY29yZSA9PT0gbnVsbCB8fCByb3cucXVpY2tBZGRTY29yZSA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICBpc1ZhbGlkOiBmYWxzZSwKICAgICAgICAgICAgbWVzc2FnZTogIlx1N0IyQyIuY29uY2F0KGkgKyAxLCAiXHU4ODRDICIpLmNvbmNhdChyb3cubmFtZSwgIiBcdThCRjdcdTU4NkJcdTUxOTlcdTUyQTBcdTUxQ0ZcdTUyMDYiKQogICAgICAgICAgfTsKICAgICAgICB9CgogICAgICAgIC8vIOajgOafpeWKoOWHj+WIhuS4jeS4ujDml7bmmK/lkKbloavlhpnkuobnkIbnlLEKICAgICAgICBpZiAocGFyc2VGbG9hdChyb3cucXVpY2tBZGRTY29yZSkgIT09IDAgJiYgIXJvdy5xdWlja1JlYXNvbikgewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgaXNWYWxpZDogZmFsc2UsCiAgICAgICAgICAgIG1lc3NhZ2U6ICJcdTdCMkMiLmNvbmNhdChpICsgMSwgIlx1ODg0QyAiKS5jb25jYXQocm93Lm5hbWUsICIgXHU2NzA5XHU1MkEwXHU1MUNGXHU1MjA2XHU2NUY2XHU4QkY3XHU1ODZCXHU1MTk5XHU1MkEwXHU1MUNGXHU1MjA2XHU3NDA2XHU3NTMxIikKICAgICAgICAgIH07CiAgICAgICAgfQogICAgICB9CiAgICAgIHJldHVybiB7CiAgICAgICAgaXNWYWxpZDogdHJ1ZQogICAgICB9OwogICAgfSwKICAgIC8qKiDmn6XnnIvor4TliIborrDlvZXor6bmg4UgKi9oYW5kbGVDaGVja2VkRGV0YWlsOiBmdW5jdGlvbiBoYW5kbGVDaGVja2VkRGV0YWlsKHJvdykgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgKDAsIF9pbmZvLmdldEluZm8pKHsKICAgICAgICBpZDogcm93LmluZm9JZAogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMpOwogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzOS5jaGVja0luZm8gPSByZXMuZGF0YTsKICAgICAgICAgIHZhciBsaXN0ID0gSlNPTi5wYXJzZShyZXMuZGF0YS5jb250ZW50KTsKICAgICAgICAgIF90aGlzOS5oYW5kbGVTcGFuTGlzdChsaXN0KTsKICAgICAgICAgIF90aGlzOS5jaGVja0luZm8ubGlzdCA9IGxpc3Q7CiAgICAgICAgICBfdGhpczkub3BlbiA9IHRydWU7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBfdGhpczkuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluivpuaDheWksei0pScpOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDorqHnrpfpg6jpl6jpooblr7zor4TliIbvvIjlv6vpgJ/or4TliIbooajmoLznlKjvvIkKICAgIGdldERlcHRTY29yZTogZnVuY3Rpb24gZ2V0RGVwdFNjb3JlKHJvdykgewogICAgICB2YXIgc2VsZlNjb3JlID0gcGFyc2VGbG9hdChyb3cuc2VsZlNjb3JlKSB8fCAwOwogICAgICB2YXIgYWRkU2NvcmUgPSBwYXJzZUZsb2F0KHJvdy5xdWlja0FkZFNjb3JlKSB8fCAwOwogICAgICB2YXIgcmVzdWx0ID0gc2VsZlNjb3JlICsgYWRkU2NvcmU7CiAgICAgIC8vIOehruS/neivhOWIhuWcqDAtMTAw6IyD5Zu05YaFCiAgICAgIHJldHVybiBNYXRoLm1heCgwLCBNYXRoLm1pbigxMDAsIHJlc3VsdCkpLnRvRml4ZWQoMSk7CiAgICB9LAogICAgLy8g6K6h566X5LqL5Lia6YOo6K+E5YiG77yI5b+r6YCf6K+E5YiG6KGo5qC855So77yJCiAgICBnZXRCdXNpbmVzc1Njb3JlOiBmdW5jdGlvbiBnZXRCdXNpbmVzc1Njb3JlKHJvdykgewogICAgICB2YXIgZGVwdFNjb3JlID0gcGFyc2VGbG9hdChyb3cuZGVwdFNjb3JlKSB8fCAwOwogICAgICB2YXIgYWRkU2NvcmUgPSBwYXJzZUZsb2F0KHJvdy5xdWlja0FkZFNjb3JlKSB8fCAwOwogICAgICB2YXIgcmVzdWx0ID0gZGVwdFNjb3JlICsgYWRkU2NvcmU7CiAgICAgIC8vIOehruS/neivhOWIhuWcqDAtMTAw6IyD5Zu05YaFCiAgICAgIHJldHVybiBNYXRoLm1heCgwLCBNYXRoLm1pbigxMDAsIHJlc3VsdCkpLnRvRml4ZWQoMSk7CiAgICB9LAogICAgLy8g6K6h566X6YOo6Zeo6aKG5a+86K+E5YiG77yI6K+m57uG6K+E5YiG6KGo5Y2V55So77yJCiAgICBnZXREZXB0U2NvcmVGcm9tRm9ybTogZnVuY3Rpb24gZ2V0RGVwdFNjb3JlRnJvbUZvcm0oKSB7CiAgICAgIHZhciBzZWxmU2NvcmUgPSBwYXJzZUZsb2F0KHRoaXMuY2hlY2tJbmZvLnNlbGZTY29yZSkgfHwgMDsKICAgICAgdmFyIGFkZFNjb3JlID0gcGFyc2VGbG9hdCh0aGlzLmZvcm0uZGVwdEFkZFNjb3JlKSB8fCAwOwogICAgICB2YXIgcmVzdWx0ID0gc2VsZlNjb3JlICsgYWRkU2NvcmU7CiAgICAgIC8vIOehruS/neivhOWIhuWcqDAtMTAw6IyD5Zu05YaFCiAgICAgIHJldHVybiBNYXRoLm1heCgwLCBNYXRoLm1pbigxMDAsIHJlc3VsdCkpLnRvRml4ZWQoMSk7CiAgICB9LAogICAgLy8g6K6h566X5LqL5Lia6YOo6K+E5YiG77yI6K+m57uG6K+E5YiG6KGo5Y2V55So77yJCiAgICBnZXRCdXNpbmVzc1Njb3JlRnJvbUZvcm06IGZ1bmN0aW9uIGdldEJ1c2luZXNzU2NvcmVGcm9tRm9ybSgpIHsKICAgICAgdmFyIGRlcHRTY29yZSA9IHBhcnNlRmxvYXQodGhpcy5jaGVja0luZm8uZGVwdFNjb3JlKSB8fCAwOwogICAgICB2YXIgYWRkU2NvcmUgPSBwYXJzZUZsb2F0KHRoaXMuZm9ybS5idXNpbmVzc0FkZFNjb3JlKSB8fCAwOwogICAgICB2YXIgcmVzdWx0ID0gZGVwdFNjb3JlICsgYWRkU2NvcmU7CiAgICAgIC8vIOehruS/neivhOWIhuWcqDAtMTAw6IyD5Zu05YaFCiAgICAgIHJldHVybiBNYXRoLm1heCgwLCBNYXRoLm1pbigxMDAsIHJlc3VsdCkpLnRvRml4ZWQoMSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_info", "require", "_user", "_index", "name", "data", "loading", "showSearch", "showBusiness", "total", "checkedTotal", "listToCheck", "listChecked", "title", "open", "queryParams", "pageNum", "pageSize", "workNo", "deptId", "assessDate", "checkedQueryParams", "form", "id", "deptAddScore", "businessAddScore", "deptScoreReason", "businessScoreReason", "rules", "deptOptions", "openCheck", "checkInfo", "spanList", "toCheckLabel", "submitting", "multipleSelection", "single", "multiple", "quickScoreDialogVisible", "batchQuickScoreForm", "score", "undefined", "ids", "batchQuickScoreRules", "required", "message", "trigger", "type", "batchQuickScoreOpen", "selectedRows", "computed", "canSubmitBatchScore", "length", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "n", "done", "row", "value", "quickAddScore", "err", "e", "f", "created", "formatDateYm", "Date", "getTime", "getCheckDeptList", "getList", "getCheckedList", "methods", "_this", "then", "res", "console", "log", "code", "for<PERSON>ach", "item", "push", "deptName", "_this2", "response", "rows", "concat", "shouldBusinessDisplay", "some", "_this3", "cancel", "reset", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleCheckDetail", "_this4", "getInfo", "list", "JSON", "parse", "content", "handleSpanList", "checkSubmit", "_this5", "verify", "point", "getDeptScoreFromForm", "status", "getBusinessScoreFromForm", "$confirm", "confirmButtonText", "cancelButtonText", "onCheck", "catch", "_this6", "deptScore", "businessScore", "check", "$message", "handleListChange", "flag", "i", "rowspan", "colspan", "objectSpanMethod", "_ref", "column", "rowIndex", "columnIndex", "category", "handleQuickSubmit", "_this7", "quickScore", "warning", "$set", "success", "finally", "handleSelectionChange", "selection", "map", "handleBatchQuickScore", "$modal", "msgError", "validationResult", "validateBatchQuickScore", "<PERSON><PERSON><PERSON><PERSON>", "cancelBatchQuickScore", "submitBatchQuickScore", "_this8", "submitData", "finalScore", "getDeptScore", "getBusinessScore", "parseFloat", "quickReason", "confirm", "batchQuickScore", "msgSuccess", "handleCheckedDetail", "_this9", "infoId", "error", "selfScore", "addScore", "result", "Math", "max", "min", "toFixed"], "sources": ["src/views/assess/self/check/list.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.assessDate\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-M\"\r\n              format=\"yyyy 年 M 月\"\r\n              placeholder=\"选择考核年月\"\r\n              :clearable=\"false\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\">\r\n              <el-option\r\n                v-for=\"item in deptOptions\"\r\n                :key=\"item.deptId\"\r\n                :label=\"item.deptName\"\r\n                :value=\"item.deptId\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      \r\n      <!-- 待评分列表 -->\r\n      <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n            <i class=\"el-icon-s-order\"></i>\r\n            {{ toCheckLabel }}\r\n          </span>\r\n        </div>\r\n        \r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              @click=\"handleBatchQuickScore\"\r\n            >批量快速评分</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n          <el-table v-loading=\"loading\" :data=\"listToCheck\" @selection-change=\"handleSelectionChange\">\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n            <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n            <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" ></el-table-column>\r\n            <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\"></el-table-column>\r\n            <el-table-column label=\"部门领导评分\" align=\"center\" prop=\"deptScore\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.status == '1'\" style=\"font-weight: bold; color: #409EFF;\">{{ getDeptScore(scope.row) }}</span>\r\n                <span v-else-if=\"scope.row.deptScore\">{{ scope.row.deptScore }}</span>\r\n                <span v-else></span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column v-if=\"showBusiness\" label=\"事业部评分\" align=\"center\" prop=\"businessScore\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.status == '2'\" style=\"font-weight: bold; color: #409EFF;\">{{ getBusinessScore(scope.row) }}</span>\r\n                <span v-else-if=\"scope.row.businessScore\">{{ scope.row.businessScore }}</span>\r\n                <span v-else></span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"加减分\" align=\"center\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number \r\n                  v-if=\"scope.row.status == '1' || scope.row.status == '2'\"\r\n                  v-model=\"scope.row.quickAddScore\" \r\n                  :min=\"-100\" \r\n                  :max=\"100\" \r\n                  size=\"mini\"\r\n                  :precision=\"1\"\r\n                  style=\"width: 100px\"\r\n                  placeholder=\"加减分\">\r\n                </el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"加减分原因\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input \r\n                  v-model=\"scope.row.quickReason\"\r\n                  type=\"textarea\"\r\n                  :autosize=\"{ minRows: 1, maxRows: 4}\"\r\n                  size=\"mini\"\r\n                  style=\"width: 150px\"\r\n                  placeholder=\"请输入加减分原因\">\r\n                </el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column label=\"运改组织部评分\" align=\"center\" prop=\"organizationScore\">\r\n                <el-input-number \r\n                  v-if=\"scope.row.status == '3'\"\r\n                  v-model=\"scope.row.quickScore\" \r\n                  :min=\"0\" \r\n                  :max=\"100\" \r\n                  size=\"mini\"\r\n                  style=\"width: 120px\"\r\n                  placeholder=\"请输入分数\">\r\n                </el-input-number>\r\n                <span v-else-if=\"scope.row.businessScore\">{{ businessScore }}</span>\r\n                <span v-else></span>\r\n            </el-table-column> -->\r\n            <!-- <el-table-column label=\"快速评分\" align=\"center\" width=\"280\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number \r\n                  v-model=\"scope.row.quickScore\" \r\n                  :min=\"0\" \r\n                  :max=\"100\" \r\n                  size=\"mini\"\r\n                  style=\"width: 120px\"\r\n                  placeholder=\"请输入分数\">\r\n                </el-input-number>\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"handleQuickSubmit(scope.row)\"\r\n                  :loading=\"scope.row.submitting\"\r\n                  style=\"margin-left: 10px\">\r\n                  提交评分\r\n                </el-button>\r\n              </template>\r\n            </el-table-column> -->\r\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleCheckDetail(scope.row)\"\r\n                >详细评分</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 评分记录 -->\r\n      <el-card class=\"box-card\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n            <i class=\"el-icon-document\"></i>\r\n            评分记录({{ checkedTotal }})\r\n          </span>\r\n        </div>\r\n        \r\n        <el-table v-loading=\"loading\" :data=\"listChecked\">\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n          <el-table-column label=\"职务\" align=\"center\" prop=\"job\" width=\"150\"/>\r\n          <el-table-column label=\"评分类型\" align=\"center\" prop=\"type\" >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag v-if=\"scope.row.type == '1'\" type=\"primary\" size=\"small\">部门领导评分</el-tag>\r\n              <el-tag v-if=\"scope.row.type == '2'\" type=\"warning\" size=\"small\">事业部领导评分</el-tag>\r\n              <el-tag v-if=\"scope.row.type == '3'\" type=\"success\" size=\"small\">运改组织部审核</el-tag>\r\n              <el-tag v-if=\"scope.row.type == '4'\" type=\"info\" size=\"small\">条线领导评分</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"评分时间\" align=\"center\" prop=\"createTime\" width=\"160\"/>\r\n          <el-table-column label=\"评分\" align=\"center\" prop=\"score\" width=\"100\"/>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleCheckedDetail(scope.row)\"\r\n              >查看详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        \r\n        <pagination\r\n          v-show=\"checkedTotal>0\"\r\n          :total=\"checkedTotal\"\r\n          :page.sync=\"checkedQueryParams.pageNum\"\r\n          :limit.sync=\"checkedQueryParams.pageSize\"\r\n          @pagination=\"getCheckedList\"\r\n          style=\"margin-top: 20px;\"\r\n        />\r\n      </el-card>\r\n\r\n          <el-dialog\r\n      :visible.sync=\"open\"\r\n      fullscreen\r\n      class=\"assessment-detail-dialog\">\r\n      <div class=\"detail-container\">\r\n        <div class=\"detail-header\">\r\n          <h2 style=\"text-align: center; color: #303133; margin-bottom: 20px;\">\r\n            <i class=\"el-icon-document\"></i>\r\n            月度业绩考核表\r\n          </h2>\r\n          <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n            <el-descriptions class=\"margin-top\" :column=\"3\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-user\"></i> 姓名\r\n                </template>\r\n                {{ checkInfo.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-office-building\"></i> 部门\r\n                </template>\r\n                {{ checkInfo.deptName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-date\"></i> 考核年月\r\n                </template>\r\n                {{ checkInfo.assessDate }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </el-card>\r\n        </div>\r\n        \r\n        <el-card shadow=\"never\" class=\"assessment-table-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n              <i class=\"el-icon-s-data\"></i>\r\n              考核详情\r\n            </span>\r\n          </div>\r\n          <el-table v-loading=\"loading\" :data=\"checkInfo.list\"\r\n            :span-method=\"objectSpanMethod\" border stripe>\r\n            <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n            <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"150\"/>\r\n            <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"180\"/>\r\n            <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n            <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" />\r\n            <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"150\" />\r\n            <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\" width=\"180\" />\r\n          </el-table>\r\n        </el-card>\r\n        \r\n        <el-card shadow=\"never\" class=\"signature-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              评分记录\r\n            </span>\r\n          </div>\r\n          <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" label-position=\"left\">\r\n            <!-- 自评分 -->\r\n            <el-form-item>\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  自评分数 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.selfScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.name }}</span>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 部门领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.deptScore && checkInfo.deptUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  部门领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.deptScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.deptUserName }}</span>\r\n                <div v-if=\"checkInfo.deptScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.deptScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 事业部领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.businessUserName && checkInfo.businessScore\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  事业部领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.businessScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.businessUserName }}</span>\r\n                <div v-if=\"checkInfo.businessScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.businessScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 运改组织部评分 -->\r\n            <el-form-item v-if=\"checkInfo.organizationScore && checkInfo.organizationUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  运改组织部评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.organizationScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.organizationUserName }}</span>\r\n                <div v-if=\"checkInfo.organizationScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.organizationScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 当前状态评分输入 -->\r\n            <el-form-item v-if=\"checkInfo.status == '1'\" label=\"加减分：\">\r\n              <el-input-number v-model=\"form.deptAddScore\" :min=\"-100\" :max=\"100\" :precision=\"1\" placeholder=\"请输入加减分\" style=\"width: 150px;\" />\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '1'\" label=\"部门领导评分：\">\r\n              <span style=\"font-weight: bold; color: #409EFF; font-size: 16px;\">{{ getDeptScoreFromForm() }}分</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '1'\" label=\"加减分理由：\">\r\n              <el-input type=\"textarea\" :autosize=\"{ minRows: 2, maxRows: 4}\" v-model=\"form.deptScoreReason\" placeholder=\"请输入加减分理由\" style=\"width: 400px;\" />\r\n            </el-form-item>\r\n            \r\n            <el-form-item v-if=\"checkInfo.status == '2'\" label=\"加减分：\">\r\n              <el-input-number v-model=\"form.businessAddScore\" :min=\"-100\" :max=\"100\" :precision=\"1\" placeholder=\"请输入加减分\" style=\"width: 150px;\" />\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '2'\" label=\"事业部领导评分：\">\r\n              <span style=\"font-weight: bold; color: #409EFF; font-size: 16px;\">{{ getBusinessScoreFromForm() }}分</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '2'\" label=\"加减分理由：\">\r\n              <el-input type=\"textarea\" :autosize=\"{ minRows: 2, maxRows: 4}\" v-model=\"form.businessScoreReason\" placeholder=\"请输入加减分理由\" style=\"width: 400px;\" />\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n        \r\n        <div class=\"dialog-footer\" style=\"text-align: center; margin-top: 30px; padding: 20px;\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"checkSubmit\">\r\n            <i class=\"el-icon-check\"></i> 提 交\r\n          </el-button>\r\n          <el-button plain type=\"info\" size=\"medium\" @click=\"cancel\">\r\n            <i class=\"el-icon-close\"></i> 返 回\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n      <!-- 批量快速评分对话框 -->\r\n      <el-dialog :title=\"'批量快速评分确认'\" :visible.sync=\"batchQuickScoreOpen\" width=\"800px\" append-to-body>\r\n        <el-alert\r\n          title=\"请确认以下人员的评分信息\"\r\n          type=\"warning\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          class=\"mb20\"\r\n        />\r\n        <el-table :data=\"selectedRows\" size=\"small\" border>\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" />\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n          <el-table-column label=\"岗位\" align=\"center\" prop=\"job\" />\r\n          <el-table-column label=\"加减分\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.quickAddScore || 0 }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"最终评分\" align=\"center\" prop=\"quickScore\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.status == '1'\" style=\"font-weight: bold; color: #409EFF;\">{{ getDeptScore(scope.row) }}</span>\r\n              <span v-else-if=\"scope.row.status == '2'\" style=\"font-weight: bold; color: #409EFF;\">{{ getBusinessScore(scope.row) }}</span>\r\n              <span v-else :class=\"{'text-red': !scope.row.quickScore}\">{{ scope.row.quickScore || '未填写' }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"加减分理由\" align=\"center\" prop=\"quickReason\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.quickReason || '' }}</span>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitBatchQuickScore\" :disabled=\"!canSubmitBatchScore\">确 定</el-button>\r\n          <el-button @click=\"cancelBatchQuickScore\">取 消</el-button>\r\n          </div>\r\n      </el-dialog>\r\n\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { listToCheck, listChecked, getInfo, check, batchQuickScore } from \"@/api/assess/self/info\"\r\n  import { getCheckDeptList } from \"@/api/assess/self/user\";\r\n  import { formatDateYm } from \"@/utils/index\"\r\n\r\n  export default {\r\n    name: \"SelfAssessCheck\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 显示列表事业部评分\r\n        showBusiness:false,\r\n        // 总条数\r\n        total: 0,\r\n        checkedTotal: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        listToCheck: [],\r\n        listChecked: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null,\r\n          name:null,\r\n          deptId:null,\r\n          assessDate:null\r\n        },\r\n        // 评分记录查询参数\r\n        checkedQueryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null,\r\n          name:null,\r\n          deptId:null,\r\n          assessDate:null\r\n        },\r\n        // 表单参数\r\n        form: {\r\n          id:null,\r\n          // 部门领导加减分\r\n          deptAddScore:null,\r\n          // 事业部加减分\r\n          businessAddScore:null,\r\n          // 部门领导评分理由\r\n          deptScoreReason:null,\r\n          // 事业部评分理由\r\n          businessScoreReason:null,\r\n        },\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        deptOptions:[],\r\n        openCheck:false,\r\n        checkInfo:{},\r\n        // 合并单元格\r\n        spanList:[],\r\n        // 待评分标签\r\n        toCheckLabel:\"待评分(0)\",\r\n        // 快速评分提交状态\r\n        submitting: false,\r\n        // 选中数组\r\n        multipleSelection: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 批量快速评分对话框显示状态\r\n        quickScoreDialogVisible: false,\r\n        // 批量快速评分表单参数\r\n        batchQuickScoreForm: {\r\n          score: undefined,\r\n          ids: []\r\n        },\r\n        // 批量快速评分表单验证规则\r\n        batchQuickScoreRules: {\r\n          score: [\r\n            { required: true, message: \"评分不能为空\", trigger: \"blur\" },\r\n            { type: 'number', message: \"评分必须为数字\", trigger: \"blur\" }\r\n          ]\r\n        },\r\n        // 批量快速评分对话框\r\n        batchQuickScoreOpen: false,\r\n        // 选中数组\r\n        ids: [],\r\n        // 选中的行数据\r\n        selectedRows: [],\r\n      };\r\n    },\r\n      computed: {\r\n    // 是否可以提交批量评分（基础检查）\r\n    canSubmitBatchScore() {\r\n      if (this.selectedRows.length === 0) return false;\r\n      \r\n      // 基础检查：是否所有行都填写了加减分\r\n      for (let row of this.selectedRows) {\r\n        if (row.quickAddScore === null || row.quickAddScore === undefined) {\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  },\r\n    created() {\r\n      this.queryParams.assessDate = formatDateYm(new Date().getTime())\r\n      this.checkedQueryParams.assessDate = formatDateYm(new Date().getTime())\r\n      // this.getSelfAssessUser();\r\n      this.getCheckDeptList();\r\n      this.getList();\r\n      this.getCheckedList();\r\n    },\r\n    methods: {\r\n      // 获取部门信息\r\n      getCheckDeptList(){\r\n        getCheckDeptList().then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            let deptOptions = [];\r\n            res.data.forEach(item => {\r\n              deptOptions.push({\r\n                deptName:item.deptName,\r\n                deptId:item.deptId\r\n              })\r\n            })\r\n            this.deptOptions = deptOptions;\r\n          }\r\n        })\r\n      },\r\n      /** 查询绩效考核-干部自评待审核列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listToCheck(this.queryParams).then(response => {\r\n          this.listToCheck = response.rows;\r\n          this.total = response.total;\r\n          this.toCheckLabel = `待评分(${response.total})`\r\n          this.loading = false;\r\n          this.shouldBusinessDisplay();\r\n        });\r\n      },\r\n\r\n      shouldBusinessDisplay(){\r\n        this.showBusiness = this.listToCheck.some(row => row[\"status\"] == '2')\r\n      },\r\n      \r\n      /** 获取已审核列表 */\r\n      getCheckedList(){\r\n        this.loading = true;\r\n        listChecked(this.checkedQueryParams).then(res => {\r\n          this.listChecked = res.rows;\r\n          this.checkedTotal = res.total;\r\n          this.loading = false;\r\n        })\r\n      },\r\n\r\n      // 取消按钮\r\n      cancel() {\r\n        this.open = false;\r\n        this.reset();\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.form = {\r\n          id: null,\r\n          deptAddScore: null,\r\n          businessAddScore: null,\r\n          deptScoreReason: null,\r\n          businessScoreReason: null,\r\n        };\r\n        // this.resetForm(\"form\");\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.checkedQueryParams.pageNum = 1;\r\n        // 同步搜索条件\r\n        this.checkedQueryParams.name = this.queryParams.name;\r\n        this.checkedQueryParams.deptId = this.queryParams.deptId;\r\n        this.checkedQueryParams.assessDate = this.queryParams.assessDate;\r\n        this.getCheckedList();\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n\r\n      // 审批详情\r\n      handleCheckDetail(row){\r\n        getInfo({id:row.id}).then(res => {\r\n          console.log(res);\r\n          if(res.code == 200){\r\n            this.checkInfo = res.data;\r\n            let list = JSON.parse(res.data.content);\r\n            this.handleSpanList(list);\r\n            this.checkInfo.list = list;\r\n          }\r\n          this.open = true\r\n        })\r\n      },\r\n\r\n      // 审批提交\r\n      checkSubmit(){\r\n        if(this.verify()){\r\n          let point = this.getDeptScoreFromForm();\r\n          if(this.checkInfo.status == '2') point = this.getBusinessScoreFromForm();\r\n          this.$confirm('是否确认' + this.checkInfo.name + '评分为：' + point + '分', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.onCheck();\r\n          }).catch(() => {\r\n\r\n          });\r\n        }\r\n      },\r\n\r\n      onCheck(){\r\n        this.form.id = this.checkInfo.id;\r\n        this.form.status = this.checkInfo.status;\r\n        \r\n        // 计算最终评分\r\n        if(this.checkInfo.status == '1') {\r\n          this.form.deptScore = this.getDeptScoreFromForm();\r\n        }\r\n        if(this.checkInfo.status == '2') {\r\n          this.form.businessScore = this.getBusinessScoreFromForm();\r\n        }\r\n        \r\n        check(this.form).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n            this.reset();\r\n            this.open = false;\r\n            this.getList();\r\n            this.getCheckedList();\r\n          }else{\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '操作失败，无权限或当前审批状态不匹配'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n      // 数据验证\r\n      verify(){\r\n        if(this.checkInfo.status == '1' && this.form.deptAddScore === null){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请填写加减分'\r\n          });\r\n          return false;\r\n        }\r\n        if(this.checkInfo.status == '2' && this.form.businessAddScore === null){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请填写加减分'\r\n          });\r\n          return false;\r\n        }\r\n        if(this.checkInfo.status == '1' && this.form.deptAddScore !== 0 && !this.form.deptScoreReason){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '有加减分时请填写加减分理由'\r\n          });\r\n          return false;\r\n        } \r\n        if(this.checkInfo.status == '2' && this.form.businessAddScore !== 0 && !this.form.businessScoreReason){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '有加减分时请填写加减分理由'\r\n          });\r\n          return false;\r\n        }\r\n        return true;\r\n      },\r\n\r\n      handleListChange(type){\r\n        console.log(type)\r\n      },\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let spanList = [];\r\n        let flag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项合并\r\n          if(i == 0){\r\n            spanList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            if(data[i - 1].item == data[i].item){\r\n              spanList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              spanList[flag].rowspan += 1;\r\n            }else{\r\n              spanList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              flag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList = spanList;\r\n      },\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      /** 快速评分提交 */\r\n      handleQuickSubmit(row) {\r\n        if (!row.quickScore) {\r\n          this.$message.warning('请输入评分');\r\n          return;\r\n        }\r\n        this.$confirm('确认提交该评分吗？', \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          this.$set(row, 'submitting', true);\r\n          const data = {\r\n            id: row.id,\r\n            score: row.quickScore,\r\n            type: row.type\r\n          };\r\n          check(data).then(response => {\r\n            this.$message.success('评分提交成功');\r\n            this.getList();\r\n            this.getCheckedList();\r\n          }).finally(() => {\r\n            this.$set(row, 'submitting', false);\r\n          });\r\n        });\r\n      },\r\n\r\n      /** 选择条数改变 */\r\n      handleSelectionChange(selection) {\r\n        this.ids = selection.map(item => item.id)\r\n        this.selectedRows = selection\r\n        this.single = selection.length !== 1\r\n        this.multiple = !selection.length\r\n      },\r\n\r\n      /** 批量快速评分按钮操作 */\r\n      handleBatchQuickScore() {\r\n        if (this.ids.length === 0) {\r\n          this.$modal.msgError(\"请选择需要评分的数据\");\r\n          return;\r\n        }\r\n        \r\n        // 验证评分一致性和理由必填\r\n        const validationResult = this.validateBatchQuickScore();\r\n        if (!validationResult.isValid) {\r\n          this.$modal.msgError(validationResult.message);\r\n          return;\r\n        }\r\n\r\n        this.batchQuickScoreOpen = true;\r\n      },\r\n\r\n      /** 取消批量快速评分操作 */\r\n      cancelBatchQuickScore() {\r\n        this.batchQuickScoreOpen = false;\r\n      },\r\n\r\n      /** 提交批量快速评分 */\r\n      submitBatchQuickScore() {\r\n        // 准备提交数据\r\n        const submitData = this.selectedRows.map(row => {\r\n          let finalScore;\r\n          if (row.status == '1') {\r\n            // 部门领导评分 = 自评分 + 加减分\r\n            finalScore = this.getDeptScore(row);\r\n          } else if (row.status == '2') {\r\n            // 事业部评分 = 部门领导评分 + 加减分\r\n            finalScore = this.getBusinessScore(row);\r\n          }\r\n          \r\n          return {\r\n            id: row.id,\r\n            quickScore: parseFloat(finalScore),\r\n            quickAddScore: row.quickAddScore,\r\n            quickReason: row.quickReason\r\n          };\r\n        });\r\n\r\n        this.$modal.confirm('是否确认提交选中人员的快速评分？').then(() => {\r\n          return batchQuickScore(submitData);\r\n        }).then(() => {\r\n          this.$modal.msgSuccess(\"批量评分成功\");\r\n          this.batchQuickScoreOpen = false;\r\n          this.getList();\r\n          this.getCheckedList();\r\n        }).catch(() => {});\r\n      },\r\n\r\n      /** 验证批量快速评分 */\r\n      validateBatchQuickScore() {\r\n        for (let i = 0; i < this.selectedRows.length; i++) {\r\n          const row = this.selectedRows[i];\r\n          \r\n          // 检查是否填写了加减分（允许为0）\r\n          if (row.quickAddScore === null || row.quickAddScore === undefined) {\r\n            return {\r\n              isValid: false,\r\n              message: `第${i + 1}行 ${row.name} 请填写加减分`\r\n            };\r\n          }\r\n\r\n          // 检查加减分不为0时是否填写了理由\r\n          if (parseFloat(row.quickAddScore) !== 0 && !row.quickReason) {\r\n            return {\r\n              isValid: false,\r\n              message: `第${i + 1}行 ${row.name} 有加减分时请填写加减分理由`\r\n            };\r\n          }\r\n        }\r\n\r\n        return { isValid: true };\r\n      },\r\n\r\n      /** 查看评分记录详情 */\r\n      handleCheckedDetail(row) {\r\n        getInfo({id: row.infoId}).then(res => {\r\n          console.log(res);\r\n          if(res.code == 200){\r\n            this.checkInfo = res.data;\r\n            let list = JSON.parse(res.data.content);\r\n            this.handleSpanList(list);\r\n            this.checkInfo.list = list;\r\n            this.open = true;\r\n          }\r\n        }).catch(error => {\r\n          this.$message.error('获取详情失败');\r\n        });\r\n      },\r\n\r\n      // 计算部门领导评分（快速评分表格用）\r\n      getDeptScore(row) {\r\n        const selfScore = parseFloat(row.selfScore) || 0;\r\n        const addScore = parseFloat(row.quickAddScore) || 0;\r\n        const result = selfScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n\r\n      // 计算事业部评分（快速评分表格用）\r\n      getBusinessScore(row) {\r\n        const deptScore = parseFloat(row.deptScore) || 0;\r\n        const addScore = parseFloat(row.quickAddScore) || 0;\r\n        const result = deptScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n\r\n      // 计算部门领导评分（详细评分表单用）\r\n      getDeptScoreFromForm() {\r\n        const selfScore = parseFloat(this.checkInfo.selfScore) || 0;\r\n        const addScore = parseFloat(this.form.deptAddScore) || 0;\r\n        const result = selfScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n\r\n      // 计算事业部评分（详细评分表单用）\r\n      getBusinessScoreFromForm() {\r\n        const deptScore = parseFloat(this.checkInfo.deptScore) || 0;\r\n        const addScore = parseFloat(this.form.businessAddScore) || 0;\r\n        const result = deptScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n    }\r\n  };\r\n  </script>\r\n\r\n  <style scoped>\r\n  .assessment-detail-dialog .detail-container {\r\n    padding: 20px;\r\n    background-color: #f5f7fa;\r\n    min-height: 100vh;\r\n  }\r\n\r\n  .assessment-detail-dialog .detail-header h2 {\r\n    background: linear-gradient(135deg, #409EFF, #67C23A);\r\n    background-clip: text;\r\n    -webkit-background-clip: text;\r\n    color: transparent;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .assessment-detail-dialog .assessment-table-card {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .assessment-detail-dialog .signature-card {\r\n    background: #ffffff;\r\n  }\r\n\r\n  .signature-content {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .score-text {\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .separator {\r\n    color: #909399;\r\n    margin: 0 4px;\r\n  }\r\n\r\n  .signature-name {\r\n    color: #303133;\r\n  }\r\n\r\n  .reason-text {\r\n    width: 100%;\r\n    margin-top: 8px;\r\n    padding: 8px 12px;\r\n    background-color: #f8f9fa;\r\n    border-left: 3px solid #409EFF;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .reason-label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  .reason-content {\r\n    color: #303133;\r\n    line-height: 1.6;\r\n  }\r\n\r\n  .dialog-footer {\r\n    border-top: 1px solid #e4e7ed;\r\n    background-color: #ffffff;\r\n    border-radius: 0 0 6px 6px;\r\n  }\r\n\r\n  .assessment-detail-dialog .el-card {\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .assessment-detail-dialog .el-descriptions {\r\n    background-color: #ffffff;\r\n  }\r\n\r\n  .assessment-detail-dialog .el-table {\r\n    border-radius: 6px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .text-red {\r\n    color: #F56C6C;\r\n  }\r\n  </style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA2ZA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACAC,YAAA;MACA;MACAC,WAAA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAd,IAAA;QACAe,MAAA;QACAC,UAAA;MACA;MACA;MACAC,kBAAA;QACAL,OAAA;QACAC,QAAA;QACAC,MAAA;QACAd,IAAA;QACAe,MAAA;QACAC,UAAA;MACA;MACA;MACAE,IAAA;QACAC,EAAA;QACA;QACAC,YAAA;QACA;QACAC,gBAAA;QACA;QACAC,eAAA;QACA;QACAC,mBAAA;MACA;MACA;MACAC,KAAA,GACA;MACAC,WAAA;MACAC,SAAA;MACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,iBAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,uBAAA;MACA;MACAC,mBAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,GAAA;MACA;MACA;MACAC,oBAAA;QACAH,KAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,IAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAE,mBAAA;MACA;MACAN,GAAA;MACA;MACAO,YAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,SAAAF,YAAA,CAAAG,MAAA;;MAEA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAAN,YAAA;QAAAO,KAAA;MAAA;QAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA,IAAAD,GAAA,CAAAE,aAAA,aAAAF,GAAA,CAAAE,aAAA,KAAArB,SAAA;YACA;UACA;QACA;MAAA,SAAAsB,GAAA;QAAAV,SAAA,CAAAW,CAAA,CAAAD,GAAA;MAAA;QAAAV,SAAA,CAAAY,CAAA;MAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAnD,WAAA,CAAAK,UAAA,OAAA+C,mBAAA,MAAAC,IAAA,GAAAC,OAAA;IACA,KAAAhD,kBAAA,CAAAD,UAAA,OAAA+C,mBAAA,MAAAC,IAAA,GAAAC,OAAA;IACA;IACA,KAAAC,gBAAA;IACA,KAAAC,OAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAH,gBAAA,WAAAA,iBAAA;MAAA,IAAAI,KAAA;MACA,IAAAJ,sBAAA,IAAAK,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACA,IAAAlD,WAAA;UACA+C,GAAA,CAAAvE,IAAA,CAAA2E,OAAA,WAAAC,IAAA;YACApD,WAAA,CAAAqD,IAAA;cACAC,QAAA,EAAAF,IAAA,CAAAE,QAAA;cACAhE,MAAA,EAAA8D,IAAA,CAAA9D;YACA;UACA;UACAuD,KAAA,CAAA7C,WAAA,GAAAA,WAAA;QACA;MACA;IACA;IACA,uBACA0C,OAAA,WAAAA,QAAA;MAAA,IAAAa,MAAA;MACA,KAAA9E,OAAA;MACA,IAAAK,iBAAA,OAAAI,WAAA,EAAA4D,IAAA,WAAAU,QAAA;QACAD,MAAA,CAAAzE,WAAA,GAAA0E,QAAA,CAAAC,IAAA;QACAF,MAAA,CAAA3E,KAAA,GAAA4E,QAAA,CAAA5E,KAAA;QACA2E,MAAA,CAAAnD,YAAA,yBAAAsD,MAAA,CAAAF,QAAA,CAAA5E,KAAA;QACA2E,MAAA,CAAA9E,OAAA;QACA8E,MAAA,CAAAI,qBAAA;MACA;IACA;IAEAA,qBAAA,WAAAA,sBAAA;MACA,KAAAhF,YAAA,QAAAG,WAAA,CAAA8E,IAAA,WAAA7B,GAAA;QAAA,OAAAA,GAAA;MAAA;IACA;IAEA,cACAY,cAAA,WAAAA,eAAA;MAAA,IAAAkB,MAAA;MACA,KAAApF,OAAA;MACA,IAAAM,iBAAA,OAAAS,kBAAA,EAAAsD,IAAA,WAAAC,GAAA;QACAc,MAAA,CAAA9E,WAAA,GAAAgE,GAAA,CAAAU,IAAA;QACAI,MAAA,CAAAhF,YAAA,GAAAkE,GAAA,CAAAnE,KAAA;QACAiF,MAAA,CAAApF,OAAA;MACA;IACA;IAEA;IACAqF,MAAA,WAAAA,OAAA;MACA,KAAA7E,IAAA;MACA,KAAA8E,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAtE,IAAA;QACAC,EAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,mBAAA;MACA;MACA;IACA;IACA,aACAkE,WAAA,WAAAA,YAAA;MACA,KAAA9E,WAAA,CAAAC,OAAA;MACA,KAAAK,kBAAA,CAAAL,OAAA;MACA;MACA,KAAAK,kBAAA,CAAAjB,IAAA,QAAAW,WAAA,CAAAX,IAAA;MACA,KAAAiB,kBAAA,CAAAF,MAAA,QAAAJ,WAAA,CAAAI,MAAA;MACA,KAAAE,kBAAA,CAAAD,UAAA,QAAAL,WAAA,CAAAK,UAAA;MACA,KAAAoD,cAAA;MACA,KAAAD,OAAA;IACA;IACA,aACAuB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IAEA;IACAG,iBAAA,WAAAA,kBAAApC,GAAA;MAAA,IAAAqC,MAAA;MACA,IAAAC,aAAA;QAAA3E,EAAA,EAAAqC,GAAA,CAAArC;MAAA,GAAAoD,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAkB,MAAA,CAAAlE,SAAA,GAAA6C,GAAA,CAAAvE,IAAA;UACA,IAAA8F,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAzB,GAAA,CAAAvE,IAAA,CAAAiG,OAAA;UACAL,MAAA,CAAAM,cAAA,CAAAJ,IAAA;UACAF,MAAA,CAAAlE,SAAA,CAAAoE,IAAA,GAAAA,IAAA;QACA;QACAF,MAAA,CAAAnF,IAAA;MACA;IACA;IAEA;IACA0F,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAC,MAAA;QACA,IAAAC,KAAA,QAAAC,oBAAA;QACA,SAAA7E,SAAA,CAAA8E,MAAA,SAAAF,KAAA,QAAAG,wBAAA;QACA,KAAAC,QAAA,eAAAhF,SAAA,CAAA3B,IAAA,YAAAuG,KAAA;UACAK,iBAAA;UACAC,gBAAA;UACAlE,IAAA;QACA,GAAA4B,IAAA;UACA8B,MAAA,CAAAS,OAAA;QACA,GAAAC,KAAA,cAEA;MACA;IACA;IAEAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,KAAA9F,IAAA,CAAAC,EAAA,QAAAQ,SAAA,CAAAR,EAAA;MACA,KAAAD,IAAA,CAAAuF,MAAA,QAAA9E,SAAA,CAAA8E,MAAA;;MAEA;MACA,SAAA9E,SAAA,CAAA8E,MAAA;QACA,KAAAvF,IAAA,CAAA+F,SAAA,QAAAT,oBAAA;MACA;MACA,SAAA7E,SAAA,CAAA8E,MAAA;QACA,KAAAvF,IAAA,CAAAgG,aAAA,QAAAR,wBAAA;MACA;MAEA,IAAAS,WAAA,OAAAjG,IAAA,EAAAqD,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAqC,MAAA,CAAAI,QAAA;YACAzE,IAAA;YACAF,OAAA;UACA;UACAuE,MAAA,CAAAxB,KAAA;UACAwB,MAAA,CAAAtG,IAAA;UACAsG,MAAA,CAAA7C,OAAA;UACA6C,MAAA,CAAA5C,cAAA;QACA;UACA4C,MAAA,CAAAI,QAAA;YACAzE,IAAA;YACAF,OAAA;UACA;QACA;MACA;IACA;IAEA;IACA6D,MAAA,WAAAA,OAAA;MACA,SAAA3E,SAAA,CAAA8E,MAAA,gBAAAvF,IAAA,CAAAE,YAAA;QACA,KAAAgG,QAAA;UACAzE,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA,SAAAd,SAAA,CAAA8E,MAAA,gBAAAvF,IAAA,CAAAG,gBAAA;QACA,KAAA+F,QAAA;UACAzE,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA,SAAAd,SAAA,CAAA8E,MAAA,gBAAAvF,IAAA,CAAAE,YAAA,gBAAAF,IAAA,CAAAI,eAAA;QACA,KAAA8F,QAAA;UACAzE,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA,SAAAd,SAAA,CAAA8E,MAAA,gBAAAvF,IAAA,CAAAG,gBAAA,gBAAAH,IAAA,CAAAK,mBAAA;QACA,KAAA6F,QAAA;UACAzE,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA;IACA;IAEA4E,gBAAA,WAAAA,iBAAA1E,IAAA;MACA8B,OAAA,CAAAC,GAAA,CAAA/B,IAAA;IACA;IACA;IACAwD,cAAA,WAAAA,eAAAlG,IAAA;MACA,IAAA2B,QAAA;MACA,IAAA0F,IAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAtH,IAAA,CAAA+C,MAAA,EAAAuE,CAAA;QACA;QACA,IAAAA,CAAA;UACA3F,QAAA,CAAAkD,IAAA;YACA0C,OAAA;YACAC,OAAA;UACA;QACA;UACA,IAAAxH,IAAA,CAAAsH,CAAA,MAAA1C,IAAA,IAAA5E,IAAA,CAAAsH,CAAA,EAAA1C,IAAA;YACAjD,QAAA,CAAAkD,IAAA;cACA0C,OAAA;cACAC,OAAA;YACA;YACA7F,QAAA,CAAA0F,IAAA,EAAAE,OAAA;UACA;YACA5F,QAAA,CAAAkD,IAAA;cACA0C,OAAA;cACAC,OAAA;YACA;YACAH,IAAA,GAAAC,CAAA;UACA;QACA;MACA;MACA,KAAA3F,QAAA,GAAAA,QAAA;IACA;IAEA;IACA8F,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAnE,GAAA,GAAAmE,IAAA,CAAAnE,GAAA;QAAAoE,MAAA,GAAAD,IAAA,CAAAC,MAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;QAAAC,WAAA,GAAAH,IAAA,CAAAG,WAAA;MACA;MACA,IAAAA,WAAA;QACA,YAAAlG,QAAA,CAAAiG,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,KAAAtE,GAAA,CAAAuE,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAAK,WAAA;QACA,KAAAtE,GAAA,CAAAuE,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IAEA,aACAO,iBAAA,WAAAA,kBAAAxE,GAAA;MAAA,IAAAyE,MAAA;MACA,KAAAzE,GAAA,CAAA0E,UAAA;QACA,KAAAd,QAAA,CAAAe,OAAA;QACA;MACA;MACA,KAAAxB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAlE,IAAA;MACA,GAAA4B,IAAA;QACA0D,MAAA,CAAAG,IAAA,CAAA5E,GAAA;QACA,IAAAvD,IAAA;UACAkB,EAAA,EAAAqC,GAAA,CAAArC,EAAA;UACAiB,KAAA,EAAAoB,GAAA,CAAA0E,UAAA;UACAvF,IAAA,EAAAa,GAAA,CAAAb;QACA;QACA,IAAAwE,WAAA,EAAAlH,IAAA,EAAAsE,IAAA,WAAAU,QAAA;UACAgD,MAAA,CAAAb,QAAA,CAAAiB,OAAA;UACAJ,MAAA,CAAA9D,OAAA;UACA8D,MAAA,CAAA7D,cAAA;QACA,GAAAkE,OAAA;UACAL,MAAA,CAAAG,IAAA,CAAA5E,GAAA;QACA;MACA;IACA;IAEA,aACA+E,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAlG,GAAA,GAAAkG,SAAA,CAAAC,GAAA,WAAA5D,IAAA;QAAA,OAAAA,IAAA,CAAA1D,EAAA;MAAA;MACA,KAAA0B,YAAA,GAAA2F,SAAA;MACA,KAAAxG,MAAA,GAAAwG,SAAA,CAAAxF,MAAA;MACA,KAAAf,QAAA,IAAAuG,SAAA,CAAAxF,MAAA;IACA;IAEA,iBACA0F,qBAAA,WAAAA,sBAAA;MACA,SAAApG,GAAA,CAAAU,MAAA;QACA,KAAA2F,MAAA,CAAAC,QAAA;QACA;MACA;;MAEA;MACA,IAAAC,gBAAA,QAAAC,uBAAA;MACA,KAAAD,gBAAA,CAAAE,OAAA;QACA,KAAAJ,MAAA,CAAAC,QAAA,CAAAC,gBAAA,CAAApG,OAAA;QACA;MACA;MAEA,KAAAG,mBAAA;IACA;IAEA,iBACAoG,qBAAA,WAAAA,sBAAA;MACA,KAAApG,mBAAA;IACA;IAEA,eACAqG,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,UAAA,QAAAtG,YAAA,CAAA4F,GAAA,WAAAjF,GAAA;QACA,IAAA4F,UAAA;QACA,IAAA5F,GAAA,CAAAiD,MAAA;UACA;UACA2C,UAAA,GAAAF,MAAA,CAAAG,YAAA,CAAA7F,GAAA;QACA,WAAAA,GAAA,CAAAiD,MAAA;UACA;UACA2C,UAAA,GAAAF,MAAA,CAAAI,gBAAA,CAAA9F,GAAA;QACA;QAEA;UACArC,EAAA,EAAAqC,GAAA,CAAArC,EAAA;UACA+G,UAAA,EAAAqB,UAAA,CAAAH,UAAA;UACA1F,aAAA,EAAAF,GAAA,CAAAE,aAAA;UACA8F,WAAA,EAAAhG,GAAA,CAAAgG;QACA;MACA;MAEA,KAAAb,MAAA,CAAAc,OAAA,qBAAAlF,IAAA;QACA,WAAAmF,qBAAA,EAAAP,UAAA;MACA,GAAA5E,IAAA;QACA2E,MAAA,CAAAP,MAAA,CAAAgB,UAAA;QACAT,MAAA,CAAAtG,mBAAA;QACAsG,MAAA,CAAA/E,OAAA;QACA+E,MAAA,CAAA9E,cAAA;MACA,GAAA2C,KAAA;IACA;IAEA,eACA+B,uBAAA,WAAAA,wBAAA;MACA,SAAAvB,CAAA,MAAAA,CAAA,QAAA1E,YAAA,CAAAG,MAAA,EAAAuE,CAAA;QACA,IAAA/D,GAAA,QAAAX,YAAA,CAAA0E,CAAA;;QAEA;QACA,IAAA/D,GAAA,CAAAE,aAAA,aAAAF,GAAA,CAAAE,aAAA,KAAArB,SAAA;UACA;YACA0G,OAAA;YACAtG,OAAA,WAAA0C,MAAA,CAAAoC,CAAA,iBAAApC,MAAA,CAAA3B,GAAA,CAAAxD,IAAA;UACA;QACA;;QAEA;QACA,IAAAuJ,UAAA,CAAA/F,GAAA,CAAAE,aAAA,YAAAF,GAAA,CAAAgG,WAAA;UACA;YACAT,OAAA;YACAtG,OAAA,WAAA0C,MAAA,CAAAoC,CAAA,iBAAApC,MAAA,CAAA3B,GAAA,CAAAxD,IAAA;UACA;QACA;MACA;MAEA;QAAA+I,OAAA;MAAA;IACA;IAEA,eACAa,mBAAA,WAAAA,oBAAApG,GAAA;MAAA,IAAAqG,MAAA;MACA,IAAA/D,aAAA;QAAA3E,EAAA,EAAAqC,GAAA,CAAAsG;MAAA,GAAAvF,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAkF,MAAA,CAAAlI,SAAA,GAAA6C,GAAA,CAAAvE,IAAA;UACA,IAAA8F,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAzB,GAAA,CAAAvE,IAAA,CAAAiG,OAAA;UACA2D,MAAA,CAAA1D,cAAA,CAAAJ,IAAA;UACA8D,MAAA,CAAAlI,SAAA,CAAAoE,IAAA,GAAAA,IAAA;UACA8D,MAAA,CAAAnJ,IAAA;QACA;MACA,GAAAqG,KAAA,WAAAgD,KAAA;QACAF,MAAA,CAAAzC,QAAA,CAAA2C,KAAA;MACA;IACA;IAEA;IACAV,YAAA,WAAAA,aAAA7F,GAAA;MACA,IAAAwG,SAAA,GAAAT,UAAA,CAAA/F,GAAA,CAAAwG,SAAA;MACA,IAAAC,QAAA,GAAAV,UAAA,CAAA/F,GAAA,CAAAE,aAAA;MACA,IAAAwG,MAAA,GAAAF,SAAA,GAAAC,QAAA;MACA;MACA,OAAAE,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,MAAAH,MAAA,GAAAI,OAAA;IACA;IAEA;IACAhB,gBAAA,WAAAA,iBAAA9F,GAAA;MACA,IAAAyD,SAAA,GAAAsC,UAAA,CAAA/F,GAAA,CAAAyD,SAAA;MACA,IAAAgD,QAAA,GAAAV,UAAA,CAAA/F,GAAA,CAAAE,aAAA;MACA,IAAAwG,MAAA,GAAAjD,SAAA,GAAAgD,QAAA;MACA;MACA,OAAAE,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,MAAAH,MAAA,GAAAI,OAAA;IACA;IAEA;IACA9D,oBAAA,WAAAA,qBAAA;MACA,IAAAwD,SAAA,GAAAT,UAAA,MAAA5H,SAAA,CAAAqI,SAAA;MACA,IAAAC,QAAA,GAAAV,UAAA,MAAArI,IAAA,CAAAE,YAAA;MACA,IAAA8I,MAAA,GAAAF,SAAA,GAAAC,QAAA;MACA;MACA,OAAAE,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,MAAAH,MAAA,GAAAI,OAAA;IACA;IAEA;IACA5D,wBAAA,WAAAA,yBAAA;MACA,IAAAO,SAAA,GAAAsC,UAAA,MAAA5H,SAAA,CAAAsF,SAAA;MACA,IAAAgD,QAAA,GAAAV,UAAA,MAAArI,IAAA,CAAAG,gBAAA;MACA,IAAA6I,MAAA,GAAAjD,SAAA,GAAAgD,QAAA;MACA;MACA,OAAAE,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,MAAAH,MAAA,GAAAI,OAAA;IACA;EACA;AACA", "ignoreList": []}]}