package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 退货申请物资明细对象 L_PASS_TH_ITEM_T
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public class LPassThItemT extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 本表主键 */
    private Long id;

    /** 头表主键 */
    @Excel(name = "头表主键")
    private Long pid;

    /** 物资编码 */
    @Excel(name = "物资编码")
    private String materialcode;

    /** 物资名称 */
    @Excel(name = "物资名称")
    private String materialname;

    /** 规格 */
    @Excel(name = "规格")
    private String materialspec;

    /** 型号 */
    @Excel(name = "型号")
    private String materialtype;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 数量 */
    @Excel(name = "数量")
    private String count;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 临时字段 */
    @Excel(name = "临时字段")
    private BigDecimal counttmp;

    /** 采购退货计划号 */
    @Excel(name = "采购退货计划号")
    private String planid;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPid(Long pid) 
    {
        this.pid = pid;
    }

    public Long getPid() 
    {
        return pid;
    }
    public void setMaterialcode(String materialcode) 
    {
        this.materialcode = materialcode;
    }

    public String getMaterialcode() 
    {
        return materialcode;
    }
    public void setMaterialname(String materialname) 
    {
        this.materialname = materialname;
    }

    public String getMaterialname() 
    {
        return materialname;
    }
    public void setMaterialspec(String materialspec) 
    {
        this.materialspec = materialspec;
    }

    public String getMaterialspec() 
    {
        return materialspec;
    }
    public void setMaterialtype(String materialtype) 
    {
        this.materialtype = materialtype;
    }

    public String getMaterialtype() 
    {
        return materialtype;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setCount(String count) 
    {
        this.count = count;
    }

    public String getCount() 
    {
        return count;
    }
    public void setWeight(BigDecimal weight) 
    {
        this.weight = weight;
    }

    public BigDecimal getWeight() 
    {
        return weight;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCounttmp(BigDecimal counttmp) 
    {
        this.counttmp = counttmp;
    }

    public BigDecimal getCounttmp() 
    {
        return counttmp;
    }
    public void setPlanid(String planid) 
    {
        this.planid = planid;
    }

    public String getPlanid() 
    {
        return planid;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pid", getPid())
            .append("materialcode", getMaterialcode())
            .append("materialname", getMaterialname())
            .append("materialspec", getMaterialspec())
            .append("materialtype", getMaterialtype())
            .append("unit", getUnit())
            .append("count", getCount())
            .append("weight", getWeight())
            .append("memo", getMemo())
            .append("counttmp", getCounttmp())
            .append("planid", getPlanid())
            .toString();
    }
}
