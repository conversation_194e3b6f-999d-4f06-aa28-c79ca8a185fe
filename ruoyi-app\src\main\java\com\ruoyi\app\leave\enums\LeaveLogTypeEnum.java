package com.ruoyi.app.leave.enums;

/**
 * 日志类型 1-计划申请单 2-计划任务 3-计划更新
 * code desc
 */
public enum LeaveLogTypeEnum {
    PLAN_APPLY(1, "计划申请单"),
    PLAN_TASK(2, "计划任务"),
    PLAN_UPDATE(3, "计划更新");

    private Integer code;
    private String desc;

    LeaveLogTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static LeaveLogTypeEnum getByCode(Integer code) {
        for (LeaveLogTypeEnum type : LeaveLogTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;

    }
}
