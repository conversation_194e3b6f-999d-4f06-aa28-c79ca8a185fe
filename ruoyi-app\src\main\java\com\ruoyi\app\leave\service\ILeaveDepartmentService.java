package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LeaveDepartment;

/**
 * 出门证部门（厂内单位）Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ILeaveDepartmentService 
{
    /**
     * 查询出门证部门（厂内单位）
     * 
     * @param id 出门证部门（厂内单位）ID
     * @return 出门证部门（厂内单位）
     */
    public LeaveDepartment selectLeaveDepartmentById(Long id);

    /**
     * 查询出门证部门（厂内单位）列表
     * 
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 出门证部门（厂内单位）集合
     */
    public List<LeaveDepartment> selectLeaveDepartmentList(LeaveDepartment leaveDepartment);

    /**
     * 新增出门证部门（厂内单位）
     * 
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 结果
     */
    public int insertLeaveDepartment(LeaveDepartment leaveDepartment,String workNo);

    /**
     * 修改出门证部门（厂内单位）
     * 
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 结果
     */
    public int updateLeaveDepartment(LeaveDepartment leaveDepartment,String workNo);

    /**
     * 作废出门证部门（厂内单位）
     *
     * @param leaveDepartment 出门证部门（厂内单位）
     * @return 结果
     */
    public int cancelLeaveDepartment(LeaveDepartment leaveDepartment,String workNo);

    /**
     * 批量删除出门证部门（厂内单位）
     * 
     * @param ids 需要删除的出门证部门（厂内单位）ID
     * @return 结果
     */
    public int deleteLeaveDepartmentByIds(Long[] ids);

    /**
     * 删除出门证部门（厂内单位）信息
     * 
     * @param id 出门证部门（厂内单位）ID
     * @return 结果
     */
    public int deleteLeaveDepartmentById(Long id);
}
