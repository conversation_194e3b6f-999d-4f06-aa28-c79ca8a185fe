package com.ruoyi.app.leave.service;

import java.util.Date;
import java.util.List;

import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.domain.LeaveTaskMaterial;
import com.ruoyi.app.leave.domain.StoreinKqdbMeasure;

/**
 * 库区调拨入库Service接口
 * 
 * <AUTHOR>
 */
public interface IStoreinKqdbService 
{
    /**
     * 查询库区调拨入库
     * 
     * @param id 库区调拨入库主键
     * @return 库区调拨入库
     */
    public StoreinKqdbMeasure selectStoreinKqdbById(Long id);

    /**
     * 查询库区调拨入库列表
     * 
     * @param storeinKqdbMeasure 库区调拨入库
     * @return 库区调拨入库集合
     */
    public List<StoreinKqdbMeasure> selectStoreinKqdbList(StoreinKqdbMeasure storeinKqdbMeasure);

    /**
     * 新增库区调拨入库
     * 
     * @param storeinKqdbMeasure 库区调拨入库
     * @return 结果
     */
    public int insertStoreinKqdb(StoreinKqdbMeasure storeinKqdbMeasure);

    /**
     * 修改库区调拨入库
     * 
     * @param storeinKqdbMeasure 库区调拨入库
     * @return 结果
     */
    public int updateStoreinKqdb(StoreinKqdbMeasure storeinKqdbMeasure);

    /**
     * 批量删除库区调拨入库
     * 
     * @param ids 需要删除的库区调拨入库主键集合
     * @return 结果
     */
    public int deleteStoreinKqdbByIds(Long[] ids);

    /**
     * 删除库区调拨入库信息
     * 
     * @param id 库区调拨入库主键
     * @return 结果
     */
    public int deleteStoreinKqdbById(Long id);

    /**
     * 根据matchid删除库区调拨入库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    public int deleteStoreinKqdbByMatchid(String matchid);

    public void handleKqdbStockIn(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial, String directSupplyPlanNo);
} 