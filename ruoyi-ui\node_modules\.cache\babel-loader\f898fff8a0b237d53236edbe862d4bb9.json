{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\organizationCheck.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\organizationCheck.vue", "mtime": 1756170476768}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_info", "require", "_user", "_index", "_dept", "_vueTreeselect", "_interopRequireDefault", "name", "components", "Treeselect", "data", "loading", "showSearch", "rejectOpen", "total", "checkedTotal", "listToCheck", "listChecked", "title", "open", "queryParams", "pageNum", "pageSize", "workNo", "deptId", "assessDate", "status", "postType", "checkedQueryParams", "form", "id", "deptScore", "businessScore", "leaderScore", "rules", "deptOptions", "openCheck", "checkInfo", "spanList", "toCheckLabel", "beAssessedList", "benefit", "userInfo", "benefitLinkFlag", "benefitDetail", "multipleSelection", "single", "multiple", "quickScoreDialogVisible", "batchQuickScoreForm", "score", "undefined", "ids", "batchQuickScoreRules", "required", "message", "trigger", "type", "batchQuickScoreOpen", "selectedRows", "computed", "canSubmitBatchScore", "length", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "n", "done", "row", "value", "quickScore", "err", "e", "f", "created", "formatDateYm", "Date", "getTime", "getTreeselect", "getList", "getCheckedList", "methods", "getBeAssessedList", "param", "_this", "listBeAssessed", "then", "res", "code", "for<PERSON>ach", "item", "concat", "_toConsumableArray2", "hrLateralAssessInfoList", "console", "log", "getSelfAssessUser", "_this2", "normalizer", "node", "children", "label", "deptName", "_this3", "listDept", "response", "handleTree", "_this4", "listInfo", "rows", "_this5", "cancel", "reset", "organizationScore", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleCheckDetail", "_this6", "getInfo", "list", "JSON", "parse", "content", "handleSpanList", "userId", "checkSubmit", "_this7", "verify", "check", "$message", "rejectClick", "rejectCancel", "rejectReason", "rejectSubmit", "_this8", "$confirm", "confirmButtonText", "cancelButtonText", "reject", "catch", "_this9", "rejectInfo", "handleListChange", "flag", "i", "push", "rowspan", "colspan", "objectSpanMethod", "_ref", "column", "rowIndex", "columnIndex", "category", "calScore", "Number", "categoryScore", "dePoints", "parseFloat", "selfScore", "toFixed", "Math", "abs", "batchCalScore", "previousScore", "benefitScore", "handleSelectionChange", "selection", "map", "handleBatchQuickScore", "_this0", "$modal", "msgError", "batchWithBenefitByIds", "cancelBatchQuickScore", "submitBatchQuickScore", "_this1", "validationResult", "validateBatchQuickScore", "<PERSON><PERSON><PERSON><PERSON>", "submitData", "quickReason", "confirm", "batchQuickScore", "msgSuccess", "previousScoreName", "handleCheckedDetail", "_this10", "infoId", "error"], "sources": ["src/views/assess/self/check/organizationCheck.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            :clearable=\"false\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input\r\n            v-model=\"queryParams.name\"\r\n            placeholder=\"请输入姓名\"\r\n            clearable\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"岗位类型\" prop=\"postType\">\r\n          <el-select v-model=\"queryParams.postType\" placeholder=\"请选择岗位类型\" clearable style=\"width: 150px;\">\r\n            <el-option label=\"技术\" value=\"0\"></el-option>\r\n            <el-option label=\"行政\" value=\"1\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    \r\n    <!-- 待评分列表 -->\r\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n          <i class=\"el-icon-s-order\"></i>\r\n          {{ toCheckLabel }}\r\n        </span>\r\n      </div>\r\n      \r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"success\"\r\n            plain\r\n            icon=\"el-icon-edit\"\r\n            size=\"mini\"\r\n            @click=\"handleBatchQuickScore\"\r\n          >批量快速评分</el-button>\r\n        </el-col>\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n        <el-table v-loading=\"loading\" :data=\"listToCheck\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" ></el-table-column>\r\n          <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.postType == '0' ? 'primary' : 'success'\" size=\"small\">\r\n                {{ scope.row.postType == '0' ? '技术' : '行政' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\"></el-table-column>\r\n          <el-table-column label=\"部门领导评分\" align=\"center\" prop=\"deptScore\"></el-table-column>\r\n          <el-table-column label=\"事业部评分\" align=\"center\" prop=\"businessScore\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.businessScore\">{{ scope.row.businessScore }}</span>\r\n              <span v-else>无</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"是否100%挂钩公司效益\" align=\"center\" prop=\"benefitLinkFlag\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column label=\"运改组织部评分\" align=\"center\" prop=\"organizationScore\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number \r\n                v-model=\"scope.row.quickScore\" \r\n                :min=\"0\" \r\n                :max=\"100\" \r\n                size=\"mini\"\r\n                style=\"width: 120px\"\r\n                placeholder=\"请输入分数\">\r\n              </el-input-number>\r\n            </template>\r\n          </el-table-column> -->\r\n          <!-- <el-table-column label=\"加减分原因\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input \r\n                v-model=\"scope.row.quickReason\"\r\n                type=\"textarea\"\r\n                :autosize=\"{ minRows: 1, maxRows: 4}\"\r\n                size=\"mini\"\r\n                style=\"width: 150px\"\r\n                placeholder=\"请输入加减分原因\">\r\n              </el-input>\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleCheckDetail(scope.row)\"\r\n              >详细评分</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 评分记录 -->\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n          <i class=\"el-icon-document\"></i>\r\n          评分记录({{ checkedTotal }})\r\n        </span>\r\n      </div>\r\n      \r\n      <el-table v-loading=\"loading\" :data=\"listChecked\">\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n        <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n        <el-table-column label=\"职务\" align=\"center\" prop=\"job\" width=\"150\"/>\r\n        <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.postType == '0' ? 'primary' : 'success'\" size=\"small\">\r\n              {{ scope.row.postType == '0' ? '技术' : '行政' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"评分类型\" align=\"center\" prop=\"type\" >\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.type == '1'\" type=\"primary\" size=\"small\">部门领导评分</el-tag>\r\n            <el-tag v-if=\"scope.row.type == '2'\" type=\"warning\" size=\"small\">事业部领导评分</el-tag>\r\n            <el-tag v-if=\"scope.row.type == '3'\" type=\"success\" size=\"small\">运改组织部审核</el-tag>\r\n            <el-tag v-if=\"scope.row.type == '4'\" type=\"info\" size=\"small\">条线领导评分</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"评分时间\" align=\"center\" prop=\"createTime\" width=\"160\"/>\r\n        <el-table-column label=\"评分\" align=\"center\" prop=\"score\" width=\"100\"/>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-view\"\r\n              @click=\"handleCheckedDetail(scope.row)\"\r\n            >查看详情</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"checkedTotal>0\"\r\n        :total=\"checkedTotal\"\r\n        :page.sync=\"checkedQueryParams.pageNum\"\r\n        :limit.sync=\"checkedQueryParams.pageSize\"\r\n        @pagination=\"getCheckedList\"\r\n        style=\"margin-top: 20px;\"\r\n      />\r\n    </el-card>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"open\"\r\n      fullscreen\r\n      class=\"assessment-detail-dialog\">\r\n      <div class=\"detail-container\">\r\n        <div class=\"detail-header\">\r\n          <h2 style=\"text-align: center; color: #303133; margin-bottom: 20px;\">\r\n            <i class=\"el-icon-document\"></i>\r\n            月度业绩考核表\r\n          </h2>\r\n          <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n            <el-descriptions class=\"margin-top\" :column=\"3\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-user\"></i> 姓名\r\n                </template>\r\n                {{ checkInfo.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-office-building\"></i> 部门\r\n                </template>\r\n                {{ checkInfo.deptName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-date\"></i> 考核年月\r\n                </template>\r\n                {{ checkInfo.assessDate }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </el-card>\r\n        </div>\r\n        \r\n        <el-card shadow=\"never\" class=\"assessment-table-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n              <i class=\"el-icon-s-data\"></i>\r\n              考核详情\r\n            </span>\r\n          </div>\r\n          <el-table v-loading=\"loading\" :data=\"checkInfo.list\"\r\n            :span-method=\"objectSpanMethod\" border stripe>\r\n            <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n            <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"150\"/>\r\n            <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"180\"/>\r\n            <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n            <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" >\r\n              <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex\">\r\n                    <el-popover\r\n                      placement=\"left\"\r\n                      width=\"636\"\r\n                      trigger=\"click\"\r\n                      :ref=\"'popover' + scope.$index\">\r\n                      <el-table :data=\"beAssessedList\">\r\n                        <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                        <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                        <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                    </el-popover>\r\n                    <span style=\"margin-left: 10px;\">{{ scope.row.performance }}</span>\r\n                  </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"150\" />\r\n            <el-table-column label=\"加减分理由\" align=\"center\" prop=\"pointsReason\" width=\"180\" />\r\n          </el-table>\r\n        </el-card>\r\n        \r\n        <el-card shadow=\"never\" class=\"signature-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              评分记录\r\n            </span>\r\n          </div>\r\n          <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" label-position=\"left\">\r\n            <!-- 自评分 -->\r\n            <el-form-item>\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  自评分数 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.selfScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.name }}</span>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 部门领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.deptScore && checkInfo.deptUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  部门领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.deptScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.deptUserName }}</span>\r\n                <div v-if=\"checkInfo.deptScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.deptScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 事业部领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.businessUserName && checkInfo.businessScore\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  事业部领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.businessScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.businessUserName }}</span>\r\n                <div v-if=\"checkInfo.businessScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.businessScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 运改组织部评分 -->\r\n            <el-form-item v-if=\"checkInfo.organizationScore && checkInfo.organizationUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  运改组织部评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.organizationScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.organizationUserName }}</span>\r\n                <div v-if=\"checkInfo.organizationScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.organizationScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 公司效益信息 -->\r\n            <el-form-item label=\"是否100%挂钩公司效益：\">\r\n              <el-tag :type=\"userInfo.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ userInfo.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n            </el-form-item>\r\n            \r\n            <!-- 当前状态评分输入 -->\r\n            <el-form-item v-if=\"checkInfo.status == '3'\" label=\"公司效益加减分：\">\r\n              <div style=\"display: flex; align-items: center; gap: 10px; margin-bottom: 10px;\">\r\n                <el-input-number v-model=\"benefit\" placeholder=\"请输入公司效益加减\" style=\"width: 200px;\" />\r\n                <span>分</span>\r\n                <el-button type=\"primary\" size=\"mini\" @click=\"calScore\">计 算</el-button>\r\n              </div>\r\n              <div v-if=\"benefitDetail\" class=\"benefit-detail\">\r\n                <i class=\"el-icon-info\"></i>\r\n                {{ benefitDetail }}\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '3'\" label=\"运改部/组织部审核：\">\r\n              <div style=\"display: flex; align-items: center; gap: 10px;\">\r\n                <el-input-number v-model=\"form.organizationScore\" :min=\"0\" :max=\"100\" placeholder=\"请输入评分\" style=\"width: 150px;\" />\r\n                <span>分</span>\r\n              </div>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n        \r\n        <div class=\"dialog-footer\" style=\"text-align: center; margin-top: 30px; padding: 20px;\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"checkSubmit\">\r\n            <i class=\"el-icon-check\"></i> 提 交\r\n          </el-button>\r\n          <el-button plain type=\"danger\" size=\"medium\" @click=\"rejectClick\">\r\n            <i class=\"el-icon-close\"></i> 退 回\r\n          </el-button>\r\n          <el-button plain type=\"info\" size=\"medium\" @click=\"cancel\">\r\n            <i class=\"el-icon-back\"></i> 取 消\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回弹出框 -->\r\n    <el-dialog title=\"退回\" :visible.sync=\"rejectOpen\" append-to-body center width=\"40%\">\r\n        <el-form label-width=\"150px\">\r\n            <el-form-item label=\"退回原因:\">\r\n                <el-input type=\"textarea\"\r\n                :autosize=\"{ minRows: 5}\" \r\n                v-model=\"checkInfo.rejectReason\" \r\n                placeholder=\"请输入内容\"></el-input>\r\n            </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\" style=\"text-align: center;\">\r\n            <el-button type=\"success\" plain @click=\"rejectSubmit\">提 交</el-button>\r\n            <el-button @click=\"rejectCancel\">取 消</el-button>\r\n        </div>\r\n    </el-dialog>\r\n\r\n      <!-- 批量快速评分对话框 -->\r\n      <el-dialog :title=\"'批量快速评分确认'\" :visible.sync=\"batchQuickScoreOpen\" width=\"1400px\" append-to-body>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <div class=\"benefit-score-container\" style=\"display: flex; align-items: center; justify-content: flex-start; margin-bottom: 8px;\">\r\n              <div class=\"benefit-input-group\" style=\"display: flex; align-items: center; height: 32px;\">\r\n                <span style=\"margin-right: 10px; white-space: nowrap; line-height: 32px;\">公司效益加减分:</span>\r\n                <el-input \r\n                  type=\"number\" \r\n                  v-model=\"benefit\" \r\n                  placeholder=\"请输入公司效益加减\" \r\n                  style=\"width: 180px; margin-right: 5px;\"\r\n                />\r\n                <span style=\"margin-right: 15px; line-height: 32px;\">分</span>\r\n                <el-button type=\"primary\" size=\"mini\" @click=\"batchCalScore\" style=\"height: 28px;\">计 算</el-button>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-alert\r\n          title=\"请确认以下人员的评分信息\"\r\n          type=\"warning\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          class=\"mb20\"\r\n        />\r\n        <el-table :data=\"selectedRows\" size=\"small\" border>\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" />\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n          <el-table-column label=\"岗位\" align=\"center\" prop=\"job\" />\r\n          <el-table-column label=\"岗位类型\" align=\"center\" prop=\"postType\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.postType == '0' ? 'primary' : 'success'\" size=\"small\">\r\n                {{ scope.row.postType == '0' ? '技术' : '行政' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"是否100%挂钩公司效益\" align=\"center\" prop=\"benefitLinkFlag\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'\">{{ scope.row.benefitLinkFlag == \"Y\" ? \"是\" : \" 否\" }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\" width=\"90px\"/>\r\n          <el-table-column label=\"部门评分\" align=\"center\" prop=\"deptScore\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.deptScore\">{{ scope.row.deptScore }}</span>\r\n              <span v-else>-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"事业部评分\" align=\"center\" prop=\"businessScore\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.businessScore\">{{ scope.row.businessScore }}</span>\r\n              <span v-else>-</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"效益加减分\" align=\"center\" prop=\"benefitScore\" width=\"90px\"/>\r\n          <el-table-column label=\"运改/组织部评分\" align=\"center\" prop=\"quickScore\" width=\"160px\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <span :class=\"{'text-red': !scope.row.quickScore}\">{{ scope.row.quickScore || '未计算' }}</span> -->\r\n               <el-input-number \r\n                v-model=\"scope.row.quickScore\" \r\n                :min=\"0\" \r\n                :max=\"100\" \r\n                size=\"mini\"\r\n                style=\"width: 120px\"\r\n                placeholder=\"请输入分数\">\r\n              </el-input-number>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"加减分理由\" align=\"center\" prop=\"quickReason\" width=\"180px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input \r\n                v-model=\"scope.row.quickReason\"\r\n                type=\"textarea\"\r\n                :autosize=\"{ minRows: 1, maxRows: 4}\"\r\n                size=\"mini\"\r\n                style=\"width: 150px\"\r\n                placeholder=\"请输入加减分原因\">\r\n              </el-input>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitBatchQuickScore\" :disabled=\"!canSubmitBatchScore\">确 定</el-button>\r\n          <el-button @click=\"cancelBatchQuickScore\">取 消</el-button>\r\n          </div>\r\n      </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listInfo, listChecked, getInfo, check, listBeAssessed, rejectInfo, batchQuickScore, batchWithBenefitByIds} from \"@/api/assess/self/info\";\r\nimport { getSelfAssessUser} from \"@/api/assess/self/user\";\r\nimport { formatDateYm } from \"@/utils/index\";\r\nimport { listDept } from \"@/api/assess/lateral/dept\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"OrganizationCheck\",\r\n  components: {\r\n    Treeselect\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 退回原因输入框\r\n      rejectOpen:false,\r\n      // 总条数\r\n      total: 0,\r\n      checkedTotal: 0,\r\n      // 绩效考核-干部自评人员配置表格数据\r\n      listToCheck: [],\r\n      listChecked: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        workNo: null,\r\n        name:null,\r\n        deptId:null,\r\n        assessDate:null,\r\n        status:\"3\",\r\n        postType:null\r\n      },\r\n      // 评分记录查询参数\r\n      checkedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        workNo: null,\r\n        name:null,\r\n        deptId:null,\r\n        assessDate:null,\r\n        postType:null\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        id:null,\r\n        // 部门领导评分\r\n        deptScore:null,\r\n        // 事业部评分\r\n        businessScore:null,\r\n        // 条线领导评分\r\n        leaderScore:null,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n      },\r\n      deptOptions:[],\r\n      openCheck:false,\r\n      checkInfo:{},\r\n      // 合并单元格\r\n      spanList:[],\r\n      // 待评分标签\r\n      toCheckLabel:\"待评分(0)\",\r\n      // 横向被考评信息\r\n      beAssessedList:[],\r\n      benefit:null,  // 公司效益分\r\n      userInfo:{\r\n        benefitLinkFlag:null\r\n      },\r\n      benefitDetail:\"\",  // 效益详细\r\n      // 选中数组\r\n      multipleSelection: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 批量快速评分对话框显示状态\r\n      quickScoreDialogVisible: false,\r\n      // 批量快速评分表单参数\r\n      batchQuickScoreForm: {\r\n        score: undefined,\r\n        ids: []\r\n      },\r\n      // 批量快速评分表单验证规则\r\n      batchQuickScoreRules: {\r\n        score: [\r\n          { required: true, message: \"评分不能为空\", trigger: \"blur\" },\r\n          { type: 'number', message: \"评分必须为数字\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 批量快速评分对话框\r\n      batchQuickScoreOpen: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的行数据\r\n      selectedRows: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 是否可以提交批量评分\r\n    canSubmitBatchScore() {\r\n      if (this.selectedRows.length === 0) return false;\r\n      \r\n      // 简单检查是否所有行都填写了评分\r\n      for (let row of this.selectedRows) {\r\n        if (!row.quickScore && row.quickScore !== 0) {\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  },\r\n  created() {\r\n    this.queryParams.assessDate = formatDateYm(new Date().getTime())\r\n    this.checkedQueryParams.assessDate = formatDateYm(new Date().getTime())\r\n    // this.getSelfAssessUser();\r\n    // this.getCheckDeptList();\r\n    this.getTreeselect();\r\n    this.getList();\r\n    this.getCheckedList();\r\n  },\r\n  methods: {\r\n    // 获取被考核信息\r\n    getBeAssessedList(param){\r\n      listBeAssessed(param).then(res =>{\r\n        let beAssessedList = [];\r\n        if(res.code == 200){\r\n          if(res.data.length > 0){\r\n            res.data.forEach(item => {\r\n              beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n            })\r\n            this.beAssessedList = beAssessedList;\r\n          }\r\n        }\r\n        console.log(beAssessedList)\r\n      })\r\n    },\r\n    // 获取被评分人员信息\r\n    getSelfAssessUser(param){\r\n      getSelfAssessUser(param).then(res =>{\r\n        if(res.code == 200){\r\n          this.userInfo = res.data;\r\n        }\r\n      })\r\n    },\r\n    /** 转换横向评价部门数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.deptId,\r\n        label: node.deptName,\r\n        children: node.children\r\n      };\r\n    },\r\n\t  /** 查询横向评价部门下拉树结构 */\r\n    getTreeselect() {\r\n      listDept().then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n      });\r\n    },\r\n    /** 查询绩效考核-干部自评待审核列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listInfo(this.queryParams).then(response => {\r\n        this.listToCheck = response.rows;\r\n        this.total = response.total;\r\n        this.toCheckLabel = `待评分(${response.total})`\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 获取已审核列表 */\r\n    getCheckedList(){\r\n      this.loading = true;\r\n      listChecked(this.checkedQueryParams).then(res => {\r\n        this.listChecked = res.rows;\r\n        this.checkedTotal = res.total;\r\n        this.loading = false;\r\n      })\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        organizationScore: null,\r\n      };\r\n      this.benefit = null;\r\n      this.benefitDetail = \"\";\r\n      this.userInfo = {\r\n        benefitLinkFlag:null\r\n      }\r\n      // this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.checkedQueryParams.pageNum = 1;\r\n      // 同步搜索条件\r\n      this.checkedQueryParams.name = this.queryParams.name;\r\n      this.checkedQueryParams.deptId = this.queryParams.deptId;\r\n      this.checkedQueryParams.assessDate = this.queryParams.assessDate;\r\n      this.checkedQueryParams.postType = this.queryParams.postType;\r\n      this.getList();\r\n      this.getCheckedList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    // 审批详情\r\n    handleCheckDetail(row){\r\n      getInfo({id:row.id}).then(res => {\r\n        console.log(res);\r\n        this.reset();\r\n        if(res.code == 200){\r\n          this.checkInfo = res.data;\r\n          let list = JSON.parse(res.data.content);\r\n          this.handleSpanList(list);\r\n          let param ={\r\n            deptId:res.data.deptId,\r\n            assessDate:res.data.assessDate\r\n          }\r\n          this.getBeAssessedList(param);  // 获取横向评分被考核数据\r\n          this.getSelfAssessUser({id:res.data.userId});  // 获取用户信息\r\n          this.checkInfo.list = list;\r\n        }\r\n        this.open = true\r\n      })\r\n    },\r\n\r\n    // 审批提交\r\n    checkSubmit(){\r\n      if(this.verify()){\r\n        this.form.id = this.checkInfo.id;\r\n        this.form.status = this.checkInfo.status;\r\n        check(this.form).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n            this.reset();\r\n            this.open = false;\r\n            this.getList();\r\n            this.getCheckedList();\r\n          }else{\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '操作失败，无权限或当前审批状态不匹配'\r\n            });\r\n          }\r\n        })\r\n      }else{\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写评分'\r\n        });\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 退回点击事件\r\n    rejectClick(){\r\n      this.rejectOpen = true;\r\n    },\r\n\r\n    rejectCancel(){\r\n      this.checkInfo.rejectReason = null;\r\n      this.rejectOpen = false;\r\n    },\r\n\r\n    // 退回\r\n    rejectSubmit(){\r\n      if(!this.checkInfo.rejectReason){\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写退回原因'\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm('确认后, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.reject();\r\n      }).catch(() => {\r\n          \r\n      });\r\n    },\r\n\r\n    reject(){\r\n      rejectInfo(this.checkInfo).then(res => {\r\n          this.reset();\r\n          this.checkInfo.rejectReason = null;\r\n          this.rejectOpen = false;\r\n          this.open = false;\r\n          this.getList();\r\n          this.getCheckedList();\r\n      })\r\n    },\r\n\r\n\r\n    // 数据验证\r\n    verify(){\r\n      if(!this.form.organizationScore) return false;\r\n      return true;\r\n    },\r\n\r\n    handleListChange(type){\r\n      console.log(type)\r\n    },\r\n    // 处理列表\r\n    handleSpanList(data){\r\n      let spanList = [];\r\n      let flag = 0;\r\n      for(let i = 0; i < data.length; i++){\r\n        // 相同考核项合并\r\n        if(i == 0){\r\n          spanList.push({\r\n            rowspan: 1,\r\n            colspan: 1\r\n          })\r\n        }else{\r\n          if(data[i - 1].item == data[i].item){\r\n            spanList.push({\r\n              rowspan: 0,\r\n              colspan: 0\r\n            })\r\n            spanList[flag].rowspan += 1;\r\n          }else{\r\n            spanList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            flag = i;\r\n          }\r\n        }\r\n      }\r\n      this.spanList = spanList;\r\n    },\r\n\r\n    // 合并单元格方法\r\n    objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 第一列相同项合并\r\n      if (columnIndex === 0) {\r\n        return this.spanList[rowIndex];\r\n      }\r\n      // 类别无内容 合并\r\n      if(columnIndex === 1){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          }\r\n        }\r\n      }\r\n      if(columnIndex === 2){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // 计算效益\r\n    calScore(){\r\n      if(this.benefit || this.benefit == 0){\r\n        let benefit = Number(this.benefit);\r\n        let categoryScore = 0;\r\n        if(this.userInfo.benefitLinkFlag == \"N\"){\r\n          this.checkInfo.list.forEach(row => {\r\n            console.log(row)\r\n            if(row.category == \"效益\"){\r\n              categoryScore += Number(row.dePoints);\r\n            }\r\n          })\r\n          this.form.organizationScore = parseFloat((this.checkInfo.selfScore + (benefit / 2) - (categoryScore / 2)).toFixed(1));\r\n          // this.benefitDetail = \"计算：\" + this.checkInfo.selfScore + \" + \" + (benefit / 2) + \" + \" + -(categoryScore / 2);\r\n          this.benefitDetail = `计算：${this.checkInfo.selfScore} ${benefit > 0 ? \"+\" : \"-\"} ${Math.abs(benefit / 2)} ${categoryScore > 0 ? \"-\" : \"+\"} ${Math.abs(categoryScore / 2)} = ${this.form.organizationScore}`\r\n        }else{\r\n          this.form.organizationScore = parseFloat((this.checkInfo.selfScore + benefit).toFixed(1));\r\n          this.benefitDetail = `计算：${this.checkInfo.selfScore} ${benefit > 0 ? \"+\" : \"-\"} ${Math.abs(benefit)} = ${this.form.organizationScore}`\r\n        }\r\n      }else{\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写公司效益加减分'\r\n        });\r\n        return;\r\n      }\r\n    },\r\n    // 批量计算\r\n    batchCalScore(){\r\n      if(this.benefit || this.benefit == 0){\r\n        let benefit = Number(this.benefit);\r\n        this.selectedRows.forEach(row => {\r\n          // 确定前一步评分：优先事业部评分，其次部门评分，最后自评分\r\n          let previousScore = 0;\r\n          if (row.businessScore !== null && row.businessScore !== undefined && row.businessScore !== '') {\r\n            // 有事业部评分，以事业部评分为基础\r\n            previousScore = Number(row.businessScore);\r\n          } else if (row.deptScore !== null && row.deptScore !== undefined && row.deptScore !== '') {\r\n            // 没有事业部评分，以部门评分为基础\r\n            previousScore = Number(row.deptScore);\r\n          } else {\r\n            // 都没有，以自评分为基础\r\n            previousScore = Number(row.selfScore);\r\n          }\r\n          \r\n          if(row.benefitLinkFlag == \"N\"){\r\n            row.quickScore = parseFloat((previousScore + (benefit / 2) - (Number(row.benefitScore) / 2)).toFixed(1));\r\n          }else{\r\n            row.quickScore = parseFloat((previousScore + benefit).toFixed(1));\r\n          }\r\n        })\r\n      }else{\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请填写公司效益加减分'\r\n        });\r\n        return;\r\n      }\r\n    },\r\n\r\n    /** 选择条数改变 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 批量快速评分按钮操作 */\r\n    handleBatchQuickScore() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请选择需要评分的数据\");\r\n        return;\r\n      }\r\n      batchWithBenefitByIds(this.ids).then(res => {\r\n        if(res.code == 200){\r\n          this.selectedRows = res.data;\r\n          this.batchQuickScoreOpen = true;\r\n        }\r\n      })\r\n      // // 检查是否有未填写评分的记录\r\n      // const emptyScores = this.selectedRows.filter(row => !row.quickScore);\r\n      // if (emptyScores.length > 0) {\r\n      //   this.$modal.msgError(`有${emptyScores.length}条记录未填写快速评分，请先填写评分`);\r\n      //   return;\r\n      // }\r\n    },\r\n\r\n    /** 取消批量快速评分操作 */\r\n    cancelBatchQuickScore() {\r\n      this.batchQuickScoreOpen = false;\r\n    },\r\n\r\n    /** 提交批量快速评分 */\r\n    submitBatchQuickScore() {\r\n      // 验证评分一致性和理由必填\r\n      const validationResult = this.validateBatchQuickScore();\r\n      if (!validationResult.isValid) {\r\n        this.$modal.msgError(validationResult.message);\r\n        return;\r\n      }\r\n\r\n      // 准备提交数据\r\n      const submitData = this.selectedRows.map(row => ({\r\n        id: row.id,\r\n        quickScore: row.quickScore,\r\n        quickReason: row.quickReason\r\n      }));\r\n\r\n      this.$modal.confirm('是否确认提交选中人员的快速评分？').then(() => {\r\n        return batchQuickScore(submitData);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"批量评分成功\");\r\n        this.batchQuickScoreOpen = false;\r\n        this.getList();\r\n        this.getCheckedList();\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 验证批量快速评分 */\r\n    validateBatchQuickScore() {\r\n      for (let i = 0; i < this.selectedRows.length; i++) {\r\n        const row = this.selectedRows[i];\r\n        \r\n        // 检查是否填写了评分\r\n        if (!row.quickScore && row.quickScore !== 0) {\r\n          return {\r\n            isValid: false,\r\n            message: `第${i + 1}行 ${row.name} 未填写评分，请先填写评分`\r\n          };\r\n        }\r\n\r\n        // 运改组织部评分时的验证\r\n        if (row.status == '3') {\r\n          let previousScore = null;\r\n          let previousScoreName = '';\r\n          \r\n          // 判断上一环节评分\r\n          if (row.businessScore !== null && row.businessScore !== undefined && row.businessScore !== '') {\r\n            // 有事业部评分，以事业部评分为准\r\n            previousScore = parseFloat(row.businessScore);\r\n            previousScoreName = '事业部领导评分';\r\n          } else if (row.deptScore !== null && row.deptScore !== undefined && row.deptScore !== '') {\r\n            // 没有事业部评分，以部门评分为准\r\n            previousScore = parseFloat(row.deptScore);\r\n            previousScoreName = '部门领导评分';\r\n          } else {\r\n            // 都没有，以自评分为准\r\n            previousScore = parseFloat(row.selfScore);\r\n            previousScoreName = '自评分';\r\n          }\r\n          \r\n          // 运改组织部评分与上一环节评分不一致时，加减分理由必填\r\n          if (parseFloat(row.quickScore) !== previousScore && !row.quickReason) {\r\n            return {\r\n              isValid: false,\r\n              message: `第${i + 1}行 ${row.name} 运改组织部评分(${row.quickScore}分)与${previousScoreName}(${previousScore}分)不一致，请填写加减分理由`\r\n            };\r\n          }\r\n        }\r\n      }\r\n\r\n      return { isValid: true };\r\n    },\r\n\r\n    /** 查看评分记录详情 */\r\n    handleCheckedDetail(row) {\r\n      getInfo({id: row.infoId}).then(res => {\r\n        console.log(res);\r\n        this.reset();\r\n        if(res.code == 200){\r\n          this.checkInfo = res.data;\r\n          let list = JSON.parse(res.data.content);\r\n          this.handleSpanList(list);\r\n          let param ={\r\n            deptId:res.data.deptId,\r\n            assessDate:res.data.assessDate\r\n          }\r\n          this.getBeAssessedList(param);  // 获取横向评分被考核数据\r\n          this.getSelfAssessUser({id:res.data.userId});  // 获取用户信息\r\n          this.checkInfo.list = list;\r\n          this.open = true;\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取详情失败');\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.assessment-detail-dialog .detail-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.assessment-detail-dialog .detail-header h2 {\r\n  background: linear-gradient(135deg, #409EFF, #67C23A);\r\n  background-clip: text;\r\n  -webkit-background-clip: text;\r\n  color: transparent;\r\n  font-weight: bold;\r\n}\r\n\r\n.assessment-detail-dialog .assessment-table-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.assessment-detail-dialog .signature-card {\r\n  background: #ffffff;\r\n}\r\n\r\n.signature-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.score-text {\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.separator {\r\n  color: #909399;\r\n  margin: 0 4px;\r\n}\r\n\r\n.signature-name {\r\n  color: #303133;\r\n}\r\n\r\n.reason-text {\r\n  width: 100%;\r\n  margin-top: 8px;\r\n  padding: 8px 12px;\r\n  background-color: #f8f9fa;\r\n  border-left: 3px solid #409EFF;\r\n  border-radius: 4px;\r\n}\r\n\r\n.reason-label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.reason-content {\r\n  color: #303133;\r\n  line-height: 1.6;\r\n}\r\n\r\n.benefit-detail {\r\n  color: #909399;\r\n  font-size: 13px;\r\n  padding: 8px 12px;\r\n  background-color: #f4f4f5;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #e6a23c;\r\n}\r\n\r\n.dialog-footer {\r\n  border-top: 1px solid #e4e7ed;\r\n  background-color: #ffffff;\r\n  border-radius: 0 0 6px 6px;\r\n}\r\n\r\n.assessment-detail-dialog .el-card {\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.assessment-detail-dialog .el-descriptions {\r\n  background-color: #ffffff;\r\n}\r\n\r\n.assessment-detail-dialog .el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.text-red {\r\n  color: #F56C6C;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA8dA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAC,sBAAA,CAAAL,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACAC,YAAA;MACA;MACAC,WAAA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAhB,IAAA;QACAiB,MAAA;QACAC,UAAA;QACAC,MAAA;QACAC,QAAA;MACA;MACA;MACAC,kBAAA;QACAP,OAAA;QACAC,QAAA;QACAC,MAAA;QACAhB,IAAA;QACAiB,MAAA;QACAC,UAAA;QACAE,QAAA;MACA;MACA;MACAE,IAAA;QACAC,EAAA;QACA;QACAC,SAAA;QACA;QACAC,aAAA;QACA;QACAC,WAAA;MACA;MACA;MACAC,KAAA,GACA;MACAC,WAAA;MACAC,SAAA;MACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,YAAA;MACA;MACAC,cAAA;MACAC,OAAA;MAAA;MACAC,QAAA;QACAC,eAAA;MACA;MACAC,aAAA;MAAA;MACA;MACAC,iBAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,uBAAA;MACA;MACAC,mBAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,GAAA;MACA;MACA;MACAC,oBAAA;QACAH,KAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,IAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAE,mBAAA;MACA;MACAN,GAAA;MACA;MACAO,YAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,SAAAF,YAAA,CAAAG,MAAA;;MAEA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAAN,YAAA;QAAAO,KAAA;MAAA;QAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA,KAAAD,GAAA,CAAAE,UAAA,IAAAF,GAAA,CAAAE,UAAA;YACA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAV,SAAA,CAAAW,CAAA,CAAAD,GAAA;MAAA;QAAAV,SAAA,CAAAY,CAAA;MAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAxD,WAAA,CAAAK,UAAA,OAAAoD,mBAAA,MAAAC,IAAA,GAAAC,OAAA;IACA,KAAAnD,kBAAA,CAAAH,UAAA,OAAAoD,mBAAA,MAAAC,IAAA,GAAAC,OAAA;IACA;IACA;IACA,KAAAC,aAAA;IACA,KAAAC,OAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,KAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,oBAAA,EAAAF,KAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAjD,cAAA;QACA,IAAAiD,GAAA,CAAAC,IAAA;UACA,IAAAD,GAAA,CAAA/E,IAAA,CAAAoD,MAAA;YACA2B,GAAA,CAAA/E,IAAA,CAAAiF,OAAA,WAAAC,IAAA;cACApD,cAAA,MAAAqD,MAAA,KAAAC,mBAAA,CAAA7B,OAAA,EAAAzB,cAAA,OAAAsD,mBAAA,CAAA7B,OAAA,EAAA2B,IAAA,CAAAG,uBAAA;YACA;YACAT,KAAA,CAAA9C,cAAA,GAAAA,cAAA;UACA;QACA;QACAwD,OAAA,CAAAC,GAAA,CAAAzD,cAAA;MACA;IACA;IACA;IACA0D,iBAAA,WAAAA,kBAAAb,KAAA;MAAA,IAAAc,MAAA;MACA,IAAAD,uBAAA,EAAAb,KAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAS,MAAA,CAAAzD,QAAA,GAAA+C,GAAA,CAAA/E,IAAA;QACA;MACA;IACA;IACA,mBACA0F,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAxC,MAAA;QACA,OAAAuC,IAAA,CAAAC,QAAA;MACA;MACA;QACAxE,EAAA,EAAAuE,IAAA,CAAA7E,MAAA;QACA+E,KAAA,EAAAF,IAAA,CAAAG,QAAA;QACAF,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,oBACAtB,aAAA,WAAAA,cAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,cAAA,IAAAlB,IAAA,WAAAmB,QAAA;QACAF,MAAA,CAAAtE,WAAA,GAAAsE,MAAA,CAAAG,UAAA,CAAAD,QAAA,CAAAjG,IAAA;MACA;IACA;IACA,uBACAuE,OAAA,WAAAA,QAAA;MAAA,IAAA4B,MAAA;MACA,KAAAlG,OAAA;MACA,IAAAmG,cAAA,OAAA1F,WAAA,EAAAoE,IAAA,WAAAmB,QAAA;QACAE,MAAA,CAAA7F,WAAA,GAAA2F,QAAA,CAAAI,IAAA;QACAF,MAAA,CAAA/F,KAAA,GAAA6F,QAAA,CAAA7F,KAAA;QACA+F,MAAA,CAAAtE,YAAA,yBAAAsD,MAAA,CAAAc,QAAA,CAAA7F,KAAA;QACA+F,MAAA,CAAAlG,OAAA;MACA;IACA;IACA,cACAuE,cAAA,WAAAA,eAAA;MAAA,IAAA8B,MAAA;MACA,KAAArG,OAAA;MACA,IAAAM,iBAAA,OAAAW,kBAAA,EAAA4D,IAAA,WAAAC,GAAA;QACAuB,MAAA,CAAA/F,WAAA,GAAAwE,GAAA,CAAAsB,IAAA;QACAC,MAAA,CAAAjG,YAAA,GAAA0E,GAAA,CAAA3E,KAAA;QACAkG,MAAA,CAAArG,OAAA;MACA;IACA;IAEA;IACAsG,MAAA,WAAAA,OAAA;MACA,KAAA9F,IAAA;MACA,KAAA+F,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAArF,IAAA;QACAC,EAAA;QACAqF,iBAAA;MACA;MACA,KAAA1E,OAAA;MACA,KAAAG,aAAA;MACA,KAAAF,QAAA;QACAC,eAAA;MACA;MACA;IACA;IACA,aACAyE,WAAA,WAAAA,YAAA;MACA,KAAAhG,WAAA,CAAAC,OAAA;MACA,KAAAO,kBAAA,CAAAP,OAAA;MACA;MACA,KAAAO,kBAAA,CAAArB,IAAA,QAAAa,WAAA,CAAAb,IAAA;MACA,KAAAqB,kBAAA,CAAAJ,MAAA,QAAAJ,WAAA,CAAAI,MAAA;MACA,KAAAI,kBAAA,CAAAH,UAAA,QAAAL,WAAA,CAAAK,UAAA;MACA,KAAAG,kBAAA,CAAAD,QAAA,QAAAP,WAAA,CAAAO,QAAA;MACA,KAAAsD,OAAA;MACA,KAAAC,cAAA;IACA;IACA,aACAmC,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IAEA;IACAG,iBAAA,WAAAA,kBAAAjD,GAAA;MAAA,IAAAkD,MAAA;MACA,IAAAC,aAAA;QAAA3F,EAAA,EAAAwC,GAAA,CAAAxC;MAAA,GAAA0D,IAAA,WAAAC,GAAA;QACAO,OAAA,CAAAC,GAAA,CAAAR,GAAA;QACA+B,MAAA,CAAAN,KAAA;QACA,IAAAzB,GAAA,CAAAC,IAAA;UACA8B,MAAA,CAAAnF,SAAA,GAAAoD,GAAA,CAAA/E,IAAA;UACA,IAAAgH,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAnC,GAAA,CAAA/E,IAAA,CAAAmH,OAAA;UACAL,MAAA,CAAAM,cAAA,CAAAJ,IAAA;UACA,IAAArC,KAAA;YACA7D,MAAA,EAAAiE,GAAA,CAAA/E,IAAA,CAAAc,MAAA;YACAC,UAAA,EAAAgE,GAAA,CAAA/E,IAAA,CAAAe;UACA;UACA+F,MAAA,CAAApC,iBAAA,CAAAC,KAAA;UACAmC,MAAA,CAAAtB,iBAAA;YAAApE,EAAA,EAAA2D,GAAA,CAAA/E,IAAA,CAAAqH;UAAA;UACAP,MAAA,CAAAnF,SAAA,CAAAqF,IAAA,GAAAA,IAAA;QACA;QACAF,MAAA,CAAArG,IAAA;MACA;IACA;IAEA;IACA6G,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAC,MAAA;QACA,KAAArG,IAAA,CAAAC,EAAA,QAAAO,SAAA,CAAAP,EAAA;QACA,KAAAD,IAAA,CAAAH,MAAA,QAAAW,SAAA,CAAAX,MAAA;QACA,IAAAyG,WAAA,OAAAtG,IAAA,EAAA2D,IAAA,WAAAC,GAAA;UACAO,OAAA,CAAAC,GAAA,CAAAR,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACAuC,MAAA,CAAAG,QAAA;cACA3E,IAAA;cACAF,OAAA;YACA;YACA0E,MAAA,CAAAf,KAAA;YACAe,MAAA,CAAA9G,IAAA;YACA8G,MAAA,CAAAhD,OAAA;YACAgD,MAAA,CAAA/C,cAAA;UACA;YACA+C,MAAA,CAAAG,QAAA;cACA3E,IAAA;cACAF,OAAA;YACA;UACA;QACA;MACA;QACA,KAAA6E,QAAA;UACA3E,IAAA;UACAF,OAAA;QACA;QACA;MACA;IACA;IAEA;IACA8E,WAAA,WAAAA,YAAA;MACA,KAAAxH,UAAA;IACA;IAEAyH,YAAA,WAAAA,aAAA;MACA,KAAAjG,SAAA,CAAAkG,YAAA;MACA,KAAA1H,UAAA;IACA;IAEA;IACA2H,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,UAAApG,SAAA,CAAAkG,YAAA;QACA,KAAAH,QAAA;UACA3E,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA,KAAAmF,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAnF,IAAA;MACA,GAAA+B,IAAA;QACAiD,MAAA,CAAAI,MAAA;MACA,GAAAC,KAAA,cAEA;IACA;IAEAD,MAAA,WAAAA,OAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,gBAAA,OAAA3G,SAAA,EAAAmD,IAAA,WAAAC,GAAA;QACAsD,MAAA,CAAA7B,KAAA;QACA6B,MAAA,CAAA1G,SAAA,CAAAkG,YAAA;QACAQ,MAAA,CAAAlI,UAAA;QACAkI,MAAA,CAAA5H,IAAA;QACA4H,MAAA,CAAA9D,OAAA;QACA8D,MAAA,CAAA7D,cAAA;MACA;IACA;IAGA;IACAgD,MAAA,WAAAA,OAAA;MACA,UAAArG,IAAA,CAAAsF,iBAAA;MACA;IACA;IAEA8B,gBAAA,WAAAA,iBAAAxF,IAAA;MACAuC,OAAA,CAAAC,GAAA,CAAAxC,IAAA;IACA;IACA;IACAqE,cAAA,WAAAA,eAAApH,IAAA;MACA,IAAA4B,QAAA;MACA,IAAA4G,IAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAzI,IAAA,CAAAoD,MAAA,EAAAqF,CAAA;QACA;QACA,IAAAA,CAAA;UACA7G,QAAA,CAAA8G,IAAA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA,IAAA5I,IAAA,CAAAyI,CAAA,MAAAvD,IAAA,IAAAlF,IAAA,CAAAyI,CAAA,EAAAvD,IAAA;YACAtD,QAAA,CAAA8G,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAhH,QAAA,CAAA4G,IAAA,EAAAG,OAAA;UACA;YACA/G,QAAA,CAAA8G,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAJ,IAAA,GAAAC,CAAA;UACA;QACA;MACA;MACA,KAAA7G,QAAA,GAAAA,QAAA;IACA;IAEA;IACAiH,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAlF,GAAA,GAAAkF,IAAA,CAAAlF,GAAA;QAAAmF,MAAA,GAAAD,IAAA,CAAAC,MAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;QAAAC,WAAA,GAAAH,IAAA,CAAAG,WAAA;MACA;MACA,IAAAA,WAAA;QACA,YAAArH,QAAA,CAAAoH,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,KAAArF,GAAA,CAAAsF,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAAK,WAAA;QACA,KAAArF,GAAA,CAAAsF,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IACA;IACAO,QAAA,WAAAA,SAAA;MACA,SAAApH,OAAA,SAAAA,OAAA;QACA,IAAAA,OAAA,GAAAqH,MAAA,MAAArH,OAAA;QACA,IAAAsH,aAAA;QACA,SAAArH,QAAA,CAAAC,eAAA;UACA,KAAAN,SAAA,CAAAqF,IAAA,CAAA/B,OAAA,WAAArB,GAAA;YACA0B,OAAA,CAAAC,GAAA,CAAA3B,GAAA;YACA,IAAAA,GAAA,CAAAsF,QAAA;cACAG,aAAA,IAAAD,MAAA,CAAAxF,GAAA,CAAA0F,QAAA;YACA;UACA;UACA,KAAAnI,IAAA,CAAAsF,iBAAA,GAAA8C,UAAA,OAAA5H,SAAA,CAAA6H,SAAA,GAAAzH,OAAA,OAAAsH,aAAA,MAAAI,OAAA;UACA;UACA,KAAAvH,aAAA,wBAAAiD,MAAA,MAAAxD,SAAA,CAAA6H,SAAA,OAAArE,MAAA,CAAApD,OAAA,uBAAAoD,MAAA,CAAAuE,IAAA,CAAAC,GAAA,CAAA5H,OAAA,YAAAoD,MAAA,CAAAkE,aAAA,uBAAAlE,MAAA,CAAAuE,IAAA,CAAAC,GAAA,CAAAN,aAAA,cAAAlE,MAAA,MAAAhE,IAAA,CAAAsF,iBAAA;QACA;UACA,KAAAtF,IAAA,CAAAsF,iBAAA,GAAA8C,UAAA,OAAA5H,SAAA,CAAA6H,SAAA,GAAAzH,OAAA,EAAA0H,OAAA;UACA,KAAAvH,aAAA,wBAAAiD,MAAA,MAAAxD,SAAA,CAAA6H,SAAA,OAAArE,MAAA,CAAApD,OAAA,uBAAAoD,MAAA,CAAAuE,IAAA,CAAAC,GAAA,CAAA5H,OAAA,UAAAoD,MAAA,MAAAhE,IAAA,CAAAsF,iBAAA;QACA;MACA;QACA,KAAAiB,QAAA;UACA3E,IAAA;UACAF,OAAA;QACA;QACA;MACA;IACA;IACA;IACA+G,aAAA,WAAAA,cAAA;MACA,SAAA7H,OAAA,SAAAA,OAAA;QACA,IAAAA,OAAA,GAAAqH,MAAA,MAAArH,OAAA;QACA,KAAAkB,YAAA,CAAAgC,OAAA,WAAArB,GAAA;UACA;UACA,IAAAiG,aAAA;UACA,IAAAjG,GAAA,CAAAtC,aAAA,aAAAsC,GAAA,CAAAtC,aAAA,KAAAmB,SAAA,IAAAmB,GAAA,CAAAtC,aAAA;YACA;YACAuI,aAAA,GAAAT,MAAA,CAAAxF,GAAA,CAAAtC,aAAA;UACA,WAAAsC,GAAA,CAAAvC,SAAA,aAAAuC,GAAA,CAAAvC,SAAA,KAAAoB,SAAA,IAAAmB,GAAA,CAAAvC,SAAA;YACA;YACAwI,aAAA,GAAAT,MAAA,CAAAxF,GAAA,CAAAvC,SAAA;UACA;YACA;YACAwI,aAAA,GAAAT,MAAA,CAAAxF,GAAA,CAAA4F,SAAA;UACA;UAEA,IAAA5F,GAAA,CAAA3B,eAAA;YACA2B,GAAA,CAAAE,UAAA,GAAAyF,UAAA,EAAAM,aAAA,GAAA9H,OAAA,OAAAqH,MAAA,CAAAxF,GAAA,CAAAkG,YAAA,OAAAL,OAAA;UACA;YACA7F,GAAA,CAAAE,UAAA,GAAAyF,UAAA,EAAAM,aAAA,GAAA9H,OAAA,EAAA0H,OAAA;UACA;QACA;MACA;QACA,KAAA/B,QAAA;UACA3E,IAAA;UACAF,OAAA;QACA;QACA;MACA;IACA;IAEA,aACAkH,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtH,GAAA,GAAAsH,SAAA,CAAAC,GAAA,WAAA/E,IAAA;QAAA,OAAAA,IAAA,CAAA9D,EAAA;MAAA;MACA,KAAA6B,YAAA,GAAA+G,SAAA;MACA,KAAA5H,MAAA,GAAA4H,SAAA,CAAA5G,MAAA;MACA,KAAAf,QAAA,IAAA2H,SAAA,CAAA5G,MAAA;IACA;IAEA,iBACA8G,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,SAAAzH,GAAA,CAAAU,MAAA;QACA,KAAAgH,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAC,2BAAA,OAAA5H,GAAA,EAAAoC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAmF,MAAA,CAAAlH,YAAA,GAAA8B,GAAA,CAAA/E,IAAA;UACAmK,MAAA,CAAAnH,mBAAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA,iBACAuH,qBAAA,WAAAA,sBAAA;MACA,KAAAvH,mBAAA;IACA;IAEA,eACAwH,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,gBAAA,QAAAC,uBAAA;MACA,KAAAD,gBAAA,CAAAE,OAAA;QACA,KAAAR,MAAA,CAAAC,QAAA,CAAAK,gBAAA,CAAA7H,OAAA;QACA;MACA;;MAEA;MACA,IAAAgI,UAAA,QAAA5H,YAAA,CAAAgH,GAAA,WAAArG,GAAA;QAAA;UACAxC,EAAA,EAAAwC,GAAA,CAAAxC,EAAA;UACA0C,UAAA,EAAAF,GAAA,CAAAE,UAAA;UACAgH,WAAA,EAAAlH,GAAA,CAAAkH;QACA;MAAA;MAEA,KAAAV,MAAA,CAAAW,OAAA,qBAAAjG,IAAA;QACA,WAAAkG,qBAAA,EAAAH,UAAA;MACA,GAAA/F,IAAA;QACA2F,MAAA,CAAAL,MAAA,CAAAa,UAAA;QACAR,MAAA,CAAAzH,mBAAA;QACAyH,MAAA,CAAAlG,OAAA;QACAkG,MAAA,CAAAjG,cAAA;MACA,GAAA4D,KAAA;IACA;IAEA,eACAuC,uBAAA,WAAAA,wBAAA;MACA,SAAAlC,CAAA,MAAAA,CAAA,QAAAxF,YAAA,CAAAG,MAAA,EAAAqF,CAAA;QACA,IAAA7E,GAAA,QAAAX,YAAA,CAAAwF,CAAA;;QAEA;QACA,KAAA7E,GAAA,CAAAE,UAAA,IAAAF,GAAA,CAAAE,UAAA;UACA;YACA8G,OAAA;YACA/H,OAAA,WAAAsC,MAAA,CAAAsD,CAAA,iBAAAtD,MAAA,CAAAvB,GAAA,CAAA/D,IAAA;UACA;QACA;;QAEA;QACA,IAAA+D,GAAA,CAAA5C,MAAA;UACA,IAAA6I,aAAA;UACA,IAAAqB,iBAAA;;UAEA;UACA,IAAAtH,GAAA,CAAAtC,aAAA,aAAAsC,GAAA,CAAAtC,aAAA,KAAAmB,SAAA,IAAAmB,GAAA,CAAAtC,aAAA;YACA;YACAuI,aAAA,GAAAN,UAAA,CAAA3F,GAAA,CAAAtC,aAAA;YACA4J,iBAAA;UACA,WAAAtH,GAAA,CAAAvC,SAAA,aAAAuC,GAAA,CAAAvC,SAAA,KAAAoB,SAAA,IAAAmB,GAAA,CAAAvC,SAAA;YACA;YACAwI,aAAA,GAAAN,UAAA,CAAA3F,GAAA,CAAAvC,SAAA;YACA6J,iBAAA;UACA;YACA;YACArB,aAAA,GAAAN,UAAA,CAAA3F,GAAA,CAAA4F,SAAA;YACA0B,iBAAA;UACA;;UAEA;UACA,IAAA3B,UAAA,CAAA3F,GAAA,CAAAE,UAAA,MAAA+F,aAAA,KAAAjG,GAAA,CAAAkH,WAAA;YACA;cACAF,OAAA;cACA/H,OAAA,WAAAsC,MAAA,CAAAsD,CAAA,iBAAAtD,MAAA,CAAAvB,GAAA,CAAA/D,IAAA,kDAAAsF,MAAA,CAAAvB,GAAA,CAAAE,UAAA,mBAAAqB,MAAA,CAAA+F,iBAAA,OAAA/F,MAAA,CAAA0E,aAAA;YACA;UACA;QACA;MACA;MAEA;QAAAe,OAAA;MAAA;IACA;IAEA,eACAO,mBAAA,WAAAA,oBAAAvH,GAAA;MAAA,IAAAwH,OAAA;MACA,IAAArE,aAAA;QAAA3F,EAAA,EAAAwC,GAAA,CAAAyH;MAAA,GAAAvG,IAAA,WAAAC,GAAA;QACAO,OAAA,CAAAC,GAAA,CAAAR,GAAA;QACAqG,OAAA,CAAA5E,KAAA;QACA,IAAAzB,GAAA,CAAAC,IAAA;UACAoG,OAAA,CAAAzJ,SAAA,GAAAoD,GAAA,CAAA/E,IAAA;UACA,IAAAgH,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAnC,GAAA,CAAA/E,IAAA,CAAAmH,OAAA;UACAiE,OAAA,CAAAhE,cAAA,CAAAJ,IAAA;UACA,IAAArC,KAAA;YACA7D,MAAA,EAAAiE,GAAA,CAAA/E,IAAA,CAAAc,MAAA;YACAC,UAAA,EAAAgE,GAAA,CAAA/E,IAAA,CAAAe;UACA;UACAqK,OAAA,CAAA1G,iBAAA,CAAAC,KAAA;UACAyG,OAAA,CAAA5F,iBAAA;YAAApE,EAAA,EAAA2D,GAAA,CAAA/E,IAAA,CAAAqH;UAAA;UACA+D,OAAA,CAAAzJ,SAAA,CAAAqF,IAAA,GAAAA,IAAA;UACAoE,OAAA,CAAA3K,IAAA;QACA;MACA,GAAA2H,KAAA,WAAAkD,KAAA;QACAF,OAAA,CAAA1D,QAAA,CAAA4D,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}