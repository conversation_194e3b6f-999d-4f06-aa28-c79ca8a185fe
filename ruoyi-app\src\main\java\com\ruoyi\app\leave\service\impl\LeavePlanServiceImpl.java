package com.ruoyi.app.leave.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.TimerTask;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.HashMap;
import java.util.Map;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.app.leave.domain.*;
import com.ruoyi.app.leave.dto.LeaveStatusDTO;
import com.ruoyi.app.leave.dto.LeaveUserPermitDto;
import com.ruoyi.app.leave.dto.LeaveUserQueryDto;
import com.ruoyi.app.leave.enums.LeaveAreaEnum;
import com.ruoyi.app.leave.enums.LeaveLogTypeEnum;
import com.ruoyi.app.leave.dto.ApproveRequestDTO;
import com.ruoyi.app.leave.enums.*;
import com.ruoyi.app.leave.mapper.*;
import com.ruoyi.app.leave.service.*;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.SnowFlakeUtil;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.web.service.TemplateMessageService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysRoleService;

import org.apache.commons.lang3.StringUtils;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.app.leave.enums.LeavePlanStatusEnum;
import com.ruoyi.app.leave.enums.LeavePlanTypeEnum;
import com.ruoyi.app.leave.enums.LeaveRoleEnum;
import com.ruoyi.app.v1.mapper.AppCommonV1Mapper;
import com.ruoyi.app.v2.service.IOfficialUserService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import static com.ruoyi.app.leave.enums.LeavePlanStatusEnum.*;

/**
 * 出门证计划申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeavePlanServiceImpl implements ILeavePlanService
{
    private static final Logger log = LoggerFactory.getLogger(LeavePlanServiceImpl.class);

    @Autowired
    private ILeavePlanMaterialService leavePlanMaterialService;

    @Autowired
    private ILeaveLogService leaveLogService;

    @Autowired
    private ILeaveTaskMaterialService leaveTaskMaterialService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private ILeaveDeptAssignmentService leaveDeptAssignmentService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private LeavePlanMapper leavePlanMapper;

    @Autowired
    private LeaveLogMapper leaveLogMapper;

    @Autowired
    private LeavePlanMaterialMapper leavePlanMaterialMapper;
    @Autowired
    private LeaveDeptAssignmentMapper leaveDeptAssignmentMapper;

    @Autowired
    private LeaveMaterialMapper leaveMaterialMapper;

    @Autowired
    private LeaveCustomerMapper leaveCustomerMapper;
    @Autowired
    private LeaveDepartmentMapper leaveDepartmentMapper;
    @Autowired
    private AppCommonV1Mapper commonMapper;

    @Autowired
    private IOfficialUserService officialUserService;

    @Autowired
    private TemplateMessageService templateMessageService;

    @Autowired
    private SysUserServiceImpl sysUserServiceImpl;

    @Autowired
    private LeaveMeasureMapper leaveMeasureMapper;

    @Autowired
    private LCarplanTMapper lCarplanTMapper;

    @Autowired
    private LCarplanItemTMapper lCarplanItemTMapper;

    @Autowired
    private LPassFhTMapper lPassFhTMapper;

    @Autowired
    private LPassFhItemTMapper lPassFhItemTMapper;

    @Autowired
    private LPassTMapper lPassTMapper;

    @Autowired
    private LPassItemTMapper lPassItemTMapper;

    @Autowired
    private LPassThTMapper lPassThTMapper;

    @Autowired
    private LPassThItemTMapper lPassThItemTMapper;

    @Autowired
    private LeaveTaskMapper leaveTaskMapper;

    /**
     * 查询出门证计划申请
     *
     * @param id 出门证计划申请ID
     * @return 出门证计划申请
     */
    @Override
    public LeavePlan selectLeavePlanById(Long id)
    {
        return leavePlanMapper.selectLeavePlanById(id);
    }

    /**
     * 查询出门证计划申请列表
     *
     * @param leavePlan 出门证计划申请
     * @return 出门证计划申请
     */
    @Override
    public List<LeavePlan> selectLeavePlanList(LeavePlan leavePlan)
    {
       List<LeavePlan> leavePlanList = leavePlanMapper.selectLeavePlanList(leavePlan);
        //设置申请人显示姓名
       for(LeavePlan PlanList:leavePlanList){
           SysUser createrUser = sysUserMapper.selectUserByUserName(PlanList.getCreateBy());
           if(Objects.nonNull(createrUser)){
               PlanList.setApplyWorkNo(createrUser.getNickName()+"("+PlanList.getCreateBy()+")");
           }else{
               PlanList.setApplyWorkNo(PlanList.getCreateBy());
           }
           }
       return leavePlanList;
    }

    public TableDataInfo selectListForMiniApp(LeavePlan leavePlan, String workNo)
    {
        //定义分厂角色集合
        List<String> factoryRoleList = Arrays.asList(
                LeaveRoleEnum.APPLICANT.getCode(),
                LeaveRoleEnum.FACTORY_APPROVER.getCode(),
                LeaveRoleEnum.FACTORY_SEC_APPROVER.getCode()
        );
        //检查用户是否属于分厂角色
        boolean isFactoryUser = sysRoleService.selectRoleExistByUserName(workNo, factoryRoleList);
        if (isFactoryUser) {
            //获取用户所属分厂id
            LeaveUserQueryDto query = new LeaveUserQueryDto();
            query.setUserName(workNo);
            LeaveUserPermitDto userInfo = leaveDeptAssignmentService.getUserInfo(query);

            if (userInfo == null || userInfo.getDeptId() == null) {
                throw new RuntimeException("用户未分配部门，请联系管理员");
            }
            leavePlan.setSourceCompanyCode(userInfo.getDeptId().toString());
        }
        this.startPage();
        List<LeavePlan> leaveList = leavePlanMapper.selectLeavePlanList(leavePlan);
        long total = new PageInfo(leaveList).getTotal();

        TableDataInfo rspDate = new TableDataInfo();
        rspDate.setCode(HttpStatus.SUCCESS);
        rspDate.setMsg("查询成功");
        rspDate.setRows(leaveList);
        rspDate.setTotal(total);
        return rspDate;
    }

    //审核单列表
    @Override
   public TableDataInfo waitApproveList(LeavePlan leavePlan,String workNo)
    {
        // 1. 权限判断
        boolean isCenterApprover = sysRoleService.selectRoleExistByUserName(workNo, LeaveRoleEnum.CENTER_APPROVER.getCode());
        boolean isFactoryApprover = sysRoleService.selectRoleExistByUserName(workNo, LeaveRoleEnum.FACTORY_APPROVER.getCode());
        boolean isFactorySecApprover = sysRoleService.selectRoleExistByUserName(workNo, LeaveRoleEnum.FACTORY_SEC_APPROVER.getCode());
        
        // 判断是否是任何一种审核员
        boolean isApprover = isCenterApprover || isFactoryApprover || isFactorySecApprover;

        // 生产指挥中心审核人可以看到所有出门证
        if (isCenterApprover) {
            // 不需要添加部门或用户限制，可以看到所有出门证
        }
        // 分厂审核人或复审人只能看到自己部门的出门证
        else if (isFactoryApprover || isFactorySecApprover) {
            // 获取当前用户部门信息
            LeaveUserQueryDto query = new LeaveUserQueryDto();
            query.setUserName(workNo);
            LeaveUserPermitDto userInfo = leaveDeptAssignmentService.getUserInfo(query);
            if (userInfo != null && userInfo.getDeptId() != null) {
                leavePlan.setSourceCompanyCode(String.valueOf(userInfo.getDeptId()));
            } else {
                log.error("获取用户部门信息失败");
            }
        }
        // 非审核员只能看到自己申请的出门证
        else {
            leavePlan.setApplyWorkNo(workNo);
        }

        if (Objects.nonNull(leavePlan.getWaitApprove()) && leavePlan.getWaitApprove()){
            List<Integer> statusList = Lists.newArrayList();
            
            // 根据角色添加对应的状态
            if(isFactoryApprover){
                statusList.add(WAIT_FACTORY_APPROVAL.getCode());
            }
            if (isFactorySecApprover){
                statusList.add(WAIT_PRODUCTION_APPROVAL.getCode());
            }
            if (isCenterApprover){
                statusList.add(WAIT_FACTORY_APPROVAL.getCode());
            }
            leavePlan.setStatusList(statusList);

        }
        this.startPage();
        List<LeavePlan> list = this.getLeaveList(leavePlan);
        long total = new PageInfo(list).getTotal();

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(total);
        return rspData;
    }

    private void startPage(){
        int pageNum = 1;
        int pageSize = 15;
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if(pageDomain.getPageNum() != null){
            pageNum = pageDomain.getPageNum();
        }
        if(pageDomain.getPageSize() != null){
            pageSize = pageDomain.getPageSize();
        }
        PageHelper.startPage(pageNum, pageSize);
    }

    private List<LeavePlan> getLeaveList(LeavePlan leavePlan){
        List<LeavePlan> list = leavePlanMapper.selectLeavePlanList(leavePlan);
        if(!CollectionUtils.isEmpty(list)){
            for(LeavePlan plan : list){
                plan.setStatusDesc(LeavePlanStatusEnum.getDescByCode(plan.getPlanStatus()));
            }
        }
        return list;
    }

    /**
     * 新增出门证计划申请
     *
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    @Override
    public void insertLeavePlan(LeavePlan leavePlan)
    {
        // 1. 权限校验
        String userName = SecurityUtils.getUsername();
        SysUser currentUser = sysUserMapper.selectUserByUserName(userName);

        // 1.1 校验当前用户是否有申请权限
//        if (!sysRoleService.selectRoleExistByUserName(userName, LeaveRoleEnum.APPLICANT.getCode())) {
//            throw new RuntimeException("您没有申请出门计划的权限");
//        }

        // 2. 设置申请基本信息
        String applyNo = SnowFlakeUtil.getLeavePlanApplyNoSnowFlakeId();
        leavePlan.setApplyNo(applyNo);
        leavePlan.setApplyWorkNo(userName);
        leavePlan.setApplyUserName(currentUser.getNickName());
        leavePlan.setApplyTime(DateUtils.getNowDate());
        leavePlan.setPlanStatus(WAIT_FACTORY_APPROVAL.getCode()); // 待分厂审批状态
        leavePlan.setCreateBy(userName);
        leavePlan.setCreateTime(DateUtils.getNowDate());

        if(StringUtils.isNotBlank(leavePlan.getSourceCompanyCode())){
            LeaveDepartment sourceCompany = leaveDepartmentMapper.selectLeaveDepartmentById(Long.valueOf(leavePlan.getSourceCompanyCode()));
            leavePlan.setSourceCompany(sourceCompany.getStoreName());
        }
        if(StringUtils.isNotBlank(leavePlan.getTargetCompanyCode())){
            LeaveDepartment targetCompany = leaveDepartmentMapper.selectLeaveDepartmentById(Long.valueOf(leavePlan.getTargetCompanyCode()));
            leavePlan.setTargetCompany(targetCompany.getStoreName());
        }
        if(StringUtils.isNotBlank(leavePlan.getReceiveCompanyCode())){
            //判断计划类型是否为跨区调拨，若是，则走department表，否则走customer表  
            if(leavePlan.getPlanType().equals(LeavePlanTypeEnum.CROSS_AREA_TRANSFER.getCode())){
                LeaveDepartment receiveCompany = leaveDepartmentMapper.selectLeaveDepartmentById(Long.valueOf(leavePlan.getReceiveCompanyCode()));
                leavePlan.setReceiveCompany(receiveCompany.getStoreName());
            }else{
                LeaveCustomer receiveCompany = leaveCustomerMapper.selectLeaveCustomerById(Long.valueOf(leavePlan.getReceiveCompanyCode()));
                leavePlan.setReceiveCompany(receiveCompany.getCustomerName());
            }
        }
        if(StringUtils.isNotBlank(leavePlan.getRefundDepartmentCode())){
            LeaveDepartment refundDepartment = leaveDepartmentMapper.selectLeaveDepartmentById(Long.valueOf(leavePlan.getRefundDepartmentCode()));
            leavePlan.setReceiveCompany(refundDepartment.getStoreName());
        }

        // 3. 插入主表数据
        leavePlanMapper.insertLeavePlan(leavePlan);

        // 4. 插入物资明细数据
        if (leavePlan.getMaterials() != null && !leavePlan.getMaterials().isEmpty()) {
            for (LeavePlanMaterial material : leavePlan.getMaterials()) {
                material.setApplyNo(applyNo);
                material.setCreateBy(userName);
                material.setCreateTime(DateUtils.getNowDate());
                leavePlanMaterialService.insertLeavePlanMaterial(material);
            }
        }

        // 5. 插入操作日志
        LeaveLog leaveLog = new LeaveLog();
        leaveLog.setLogType(LeaveLogTypeEnum.PLAN_APPLY.getCode());
        leaveLog.setApplyNo(applyNo);
        leaveLog.setInfo(leavePlan.getSourceCompany() + "\"" + currentUser.getNickName() + "\"申请了出门计划");
        leaveLog.setCreateBy(userName);
        leaveLog.setCreateTime(DateUtils.getNowDate());
        leaveLogMapper.insertLeaveLog(leaveLog);

        // 6. 发送消息通知分厂审核人员
        sendApproveMessage(LeaveRoleEnum.FACTORY_APPROVER.getCode(), leavePlan, "您有出门计划待审核");
    }

    private void sendApproveMessage(String roleCode, LeavePlan leavePlan, String message) {
        AsyncManager.me().execute(new TimerTask() {
            @Override
            public void run() {
                try {
                    //审核页面
                    String path = "packageC/pages/leave/detail?applyNo=" + leavePlan.getApplyNo();
                    String type = "出门证审批";
                    String time = DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss");

                    // 根据角色代码获取审核人员
                    List<SysUser> approvers = sysRoleService.selectSysUserListByRoleKey(roleCode);
                    //获取该部门的所有人员
                    LeaveDeptAssignment condition = new LeaveDeptAssignment();
                    condition.setDeptId(Long.valueOf(leavePlan.getSourceCompanyCode()));
                    List<LeaveDeptAssignment> deptAssignments = leaveDeptAssignmentMapper.selectLeaveDeptAssignmentList(condition);
                    //既是审核人员又是部门人员
                    Set<SysUser> deptApprovers = new LinkedHashSet<>(); // 使用Set去重
                    for (SysUser approver : approvers) {
                        for (LeaveDeptAssignment deptAssignment : deptAssignments) {
                            if (approver.getUserId().longValue() == deptAssignment.getUserId().longValue()) {
                                deptApprovers.add(approver);
                                break;
                            }
                        }
                    }
                    //发送消息
                    for (SysUser deptApprover : deptApprovers) {
                        String appOpenId = commonMapper.selectOpenIdByWorkNo(deptApprover.getUserName());
                        if (com.ruoyi.common.utils.StringUtils.isNotBlank(appOpenId)) {
                            String openId = officialUserService.getOpenIdsByOpenId(appOpenId);
                            if (com.ruoyi.common.utils.StringUtils.isNotBlank(openId)) {
                                templateMessageService.sendToDoMessage(time, type, message, openId, path);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("发送审核推送消息失败", e);
                }
            }
        });



    }

    //设置新增、修改、废弃按钮是否显示
    public void showBtn(LeavePlan leavePlan){
        //新增按钮分厂申请人显示
        String userName = SecurityUtils.getUsername();
        if (sysRoleService.selectRoleExistByUserName(userName, LeaveRoleEnum.APPLICANT.getCode())) {
            leavePlan.setShowAddBtn(true);
        }
        //修改按钮本人且驳回状态显示
        String createUserNo = extractCreateUserNo(leavePlan.getApplyWorkNo());
        if(userName.equals(createUserNo) && leavePlan.getPlanStatus().equals(11)){
            leavePlan.setShowModifyBtn(true);
        }
        //废弃按钮生产指挥中心显示
        if(sysRoleService.selectRoleExistByUserName(userName, LeaveRoleEnum.CENTER_APPROVER.getCode())){
            leavePlan.setShowCancelBtn(true);
        }
    }

    private String extractCreateUserNo(String applyWorkNo){
        int startIndex = applyWorkNo.indexOf('(');
        int endIndex = applyWorkNo.indexOf(')');
        if(startIndex != -1 && endIndex != -1 && endIndex > startIndex){
            return applyWorkNo.substring(startIndex + 1, endIndex);
        }
        return  applyWorkNo;
    }

    /**
     * 修改出门证计划申请
     *
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    @Override
    public int updateLeavePlan(LeavePlan leavePlan)
    {
        String userName = SecurityUtils.getUsername();
        SysUser currentUser = sysUserMapper.selectUserByUserName(userName);

        // 更新基本信息
        leavePlan.setUpdateBy(userName);
        leavePlan.setUpdateTime(DateUtils.getNowDate());

        // 更新关联信息
        if(StringUtils.isNotBlank(leavePlan.getSourceCompanyCode())){
            LeaveDepartment sourceCompany = leaveDepartmentMapper.selectLeaveDepartmentById(Long.valueOf(leavePlan.getSourceCompanyCode()));
            leavePlan.setSourceCompany(sourceCompany.getStoreName());
        }
        if(StringUtils.isNotBlank(leavePlan.getTargetCompanyCode())){
            LeaveDepartment targetCompany = leaveDepartmentMapper.selectLeaveDepartmentById(Long.valueOf(leavePlan.getTargetCompanyCode()));
            leavePlan.setTargetCompany(targetCompany.getStoreName());
        }
        if(StringUtils.isNotBlank(leavePlan.getReceiveCompanyCode())){
            LeaveCustomer receiveCompany = leaveCustomerMapper.selectLeaveCustomerById(Long.valueOf(leavePlan.getReceiveCompanyCode()));
            leavePlan.setReceiveCompany(receiveCompany.getCustomerName());
        }
        if(StringUtils.isNotBlank(leavePlan.getRefundDepartmentCode())){
            LeaveDepartment refundDepartment = leaveDepartmentMapper.selectLeaveDepartmentById(Long.valueOf(leavePlan.getRefundDepartmentCode()));
            leavePlan.setRefundDepartment(refundDepartment.getStoreName());
        }

        //更新状态为待分厂审核
        leavePlan.setPlanStatus(WAIT_FACTORY_APPROVAL.getCode());
        //通知分厂
        sendApproveMessage(LeaveRoleEnum.FACTORY_APPROVER.getCode(), leavePlan, "您有出门计划待审核");

        // 更新主表数据
        int rows = leavePlanMapper.updateLeavePlan(leavePlan);

        // 更新物资明细
        if (leavePlan.getMaterials() != null && !leavePlan.getMaterials().isEmpty()) {
            // 先删除原有的物资明细
            LeavePlanMaterial materialParam = new LeavePlanMaterial();
            materialParam.setApplyNo(leavePlan.getApplyNo());
            leavePlanMaterialMapper.deleteLeavePlanMaterialByApplyNo(leavePlan.getApplyNo());

            // 重新插入物资明细
            for (LeavePlanMaterial material : leavePlan.getMaterials()) {
                material.setApplyNo(leavePlan.getApplyNo());
                material.setCreateBy(userName);
                material.setCreateTime(DateUtils.getNowDate());
                leavePlanMaterialService.insertLeavePlanMaterial(material);
            }
        }

        // 添加操作日志
        LeaveLog leaveLog = new LeaveLog();
        leaveLog.setLogType(LeaveLogTypeEnum.PLAN_UPDATE.getCode());
        leaveLog.setApplyNo(leavePlan.getApplyNo());
        leaveLog.setInfo(currentUser.getNickName() + "修改了出门计划");
        leaveLog.setCreateBy(userName);
        leaveLog.setCreateTime(DateUtils.getNowDate());
        leaveLogMapper.insertLeaveLog(leaveLog);

        return rows;
    }

    /**
     * 批量删除出门证计划申请
     *
     * @param ids 需要删除的出门证计划申请ID
     * @return 结果
     */
    @Override
    public int deleteLeavePlanByIds(Long[] ids)
    {
        return leavePlanMapper.deleteLeavePlanByIds(ids);
    }

    /**
     * 删除出门证计划申请信息
     *
     * @param id 出门证计划申请ID
     * @return 结果
     */
    @Override
    public int deleteLeavePlanById(Long id)
    {
        return leavePlanMapper.deleteLeavePlanById(id);
    }

    @Override
    public LeavePlan detail(String applyNo) {
        return null;
    }

    @Override
    public LeavePlan detail(String applyNo,String workNo) {

       LeavePlan leavePlan = leavePlanMapper.selectLeavePlanByApplyNo(applyNo);
       if(leavePlan == null){
           throw new RuntimeException("出门证计划不存在");
       }
       //申请人
       leavePlan.setApplyUserName(leavePlan.getApplyUserName()+"("+leavePlan.getApplyWorkNo()+")");

       // 1. 查询物资明细
       List<LeavePlanMaterial> materials = leavePlanMaterialMapper.selectLeavePlanMaterialByApplyNo(applyNo);
       leavePlan.setMaterials(materials);

       // 2. 查询操作日志
       List<LeaveLog> leaveLogs = leaveLogMapper.selectLeaveLogByApplyNo(applyNo);
       leavePlan.setLeaveLogs(leaveLogs);

       //获取当前用户部门信息
        LeaveUserQueryDto userQuery = new LeaveUserQueryDto();
        userQuery.setUserName(workNo);
        LeaveUserPermitDto currentUser = leaveDeptAssignmentService.getUserInfo(userQuery);
        Long currentDeptId = currentUser != null ? currentUser.getDeptId() : null;

        //获取申请单所属部门ID
        Long applyDeptId = Long.parseLong(leavePlan.getSourceCompanyCode());

        boolean approveButtonShow = false;//审核按钮显示
        boolean rejectButtonShow = false;//驳回按钮显示
        boolean discardButtonShow = false;//废弃按钮显示
        LeavePlanStatusEnum leavePlanStatusEnum = LeavePlanStatusEnum.getByCode(leavePlan.getPlanStatus());
        //部门判断
        boolean deptMatch = currentDeptId != null && currentDeptId.equals(applyDeptId);
        switch(leavePlanStatusEnum){
            case WAIT_FACTORY_APPROVAL:
                if(deptMatch && sysRoleService.selectRoleExistByUserName(workNo,LeaveRoleEnum.FACTORY_APPROVER.getCode())){
                    approveButtonShow = true;
                    rejectButtonShow = true;
                    if(workNo.equals(leavePlan.getApplyWorkNo())){
                        discardButtonShow = true;
                    }
                }
                break;
            case WAIT_FACTORY_RECHECK:
                if(deptMatch && sysRoleService.selectRoleExistByUserName(workNo,LeaveRoleEnum.FACTORY_SEC_APPROVER.getCode())){
                    approveButtonShow = true;
                    rejectButtonShow = true;
                }
                break;
            case WAIT_PRODUCTION_APPROVAL:
                if(sysRoleService.selectRoleExistByUserName(workNo,LeaveRoleEnum.CENTER_APPROVER.getCode())){
                    approveButtonShow = true;
                    rejectButtonShow = true;
                    discardButtonShow = true;
                }
            default:
        }
        leavePlan.setApproveButtonShow(approveButtonShow);
        leavePlan.setRejectButtonShow(rejectButtonShow);
        leavePlan.setDiscardButtonShow(discardButtonShow);
       return leavePlan;
    }

    @Override
    public void updateIsSendCar(String applyNo) {
        LeavePlan leavePlan = leavePlanMapper.selectLeavePlanByApplyNo(applyNo);
        leavePlan.setIsSendCar("1");
        leavePlanMapper.updateLeavePlan(leavePlan);
    }


    /**
     * 审核通过
     */
    @Override
    public AjaxResult approve(ApproveRequestDTO approveRequestD,SysUser currentUser){
        try {
            if(approveRequestD.getApproveFlag() && StringUtils.isBlank(approveRequestD.getApproveContent())){
                approveRequestD.setApproveContent("同意");
            }
            if (!approveRequestD.getApproveFlag() && StringUtils.isBlank(approveRequestD.getApproveContent())){
                approveRequestD.setApproveContent("驳回");
            }
            //获取出门证计划申请信息
            LeavePlan plan = leavePlanMapper.selectLeavePlanByApplyNo(String.valueOf(approveRequestD.getApplyNo()));
            //获取申请状态
            LeavePlanStatusEnum leavePlanStatusEnum = LeavePlanStatusEnum.getByCode(plan.getPlanStatus());
            switch (leavePlanStatusEnum) {
                case WAIT_FACTORY_APPROVAL:
                    //分厂审核
                    //分厂审批通过
                    if (approveRequestD.getApproveFlag()) {
                        this.FactoryApprove(plan,approveRequestD,currentUser);
                    }else {
                        this.FactoryApprove(plan,approveRequestD,currentUser);
                    }
                    break;
                case WAIT_FACTORY_RECHECK:
                    //分厂复审
                    if (approveRequestD.getApproveFlag()) {
                        this.FactoryRecheck(plan,approveRequestD,currentUser);
                    }else {
                        this.FactoryRecheck(plan,approveRequestD,currentUser);
                    }
                    break;
                case WAIT_PRODUCTION_APPROVAL:
                    //生产指挥中心审批
                    if (approveRequestD.getApproveFlag()) {
                        this.CenterApprove(plan,approveRequestD,currentUser);
                    }else {
                        this.CenterApprove(plan,approveRequestD,currentUser);
                    }
                    break;
                default:
                    throw new RuntimeException("当前状态无法审批");
            }

            //通知
            LeavePlanStatusEnum planStatusEnum = LeavePlanStatusEnum.getByCode(plan.getPlanStatus());
            switch (planStatusEnum){
                case WAIT_FACTORY_APPROVAL:
                    if(!approveRequestD.getApproveFlag()){
                        this.sendApproveMessage(plan.getApplyWorkNo(),plan,"您的申请被驳回,理由为" + approveRequestD.getApproveContent());
                    }
                case WAIT_FACTORY_RECHECK:
                    if(approveRequestD.getApproveFlag()) {
                        this.sendApproveMessage(LeaveRoleEnum.FACTORY_SEC_APPROVER.getCode(), plan, "您有出门证计划待审核");
                    }else {
                        this.sendApproveMessage(plan.getApplyWorkNo(), plan, "您的申请被驳回" + approveRequestD.getApproveContent());
                    }
                    break;
                case WAIT_PRODUCTION_APPROVAL:
                    if(approveRequestD.getApproveFlag()) {
                        this.sendApproveMessage(LeaveRoleEnum.CENTER_APPROVER.getCode(), plan, "您有出门证计划待审核");
                    }else {
                        this.sendApproveMessage(plan.getApplyWorkNo(), plan, "您的申请被驳回" + approveRequestD.getApproveContent());
                    }
                    break;
                default:
            }
            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            throw new  RuntimeException("审核异常！");
        }
    }

    private String generatePlanNo(LeavePlanTypeEnum planTypeEnum, LeaveAreaEnum leaveAreaEnum){
        // 使用操作类型作为序列号的前缀
        String prefix = getPlanNoPrefix(planTypeEnum, leaveAreaEnum);
        String suffix = getPlanNoSuffix(planTypeEnum, leaveAreaEnum);

        // 格式化比较时间为字符串，用作序列号的一部分
        String currentDate = this.getCurrentDate();
        // 计算下一个序列值
        int nextValue = nextSequence(suffix, currentDate);
        // 将序列值格式化为5位数的字符串，不足部分前面补0
        String nextValueStr = String.format("%0" + 5 + "d", nextValue);
        // 拼接前缀、时间戳和序列值字符串，返回最终的序列号
        return prefix + currentDate + nextValueStr;
    }

    /**
     * 生成计划号
     */
    private String generatePlanNo(String applyNo) {
        //获取申请单
        LeavePlan leavePlan = leavePlanMapper.selectLeavePlanByApplyNo(applyNo);
        if(leavePlan == null){
            throw new RuntimeException("出门证计划不存在");
        }
        //获取计划类型
        Integer planType = leavePlan.getPlanType();
        LeavePlanTypeEnum planTypeEnum = LeavePlanTypeEnum.getByCode(planType);
        //获取申请单位实体
        LeaveDepartment applyDept = leaveDepartmentMapper.selectLeaveDepartmentById(Long.valueOf(leavePlan.getSourceCompanyCode()));
        LeaveAreaEnum leaveAreaEnum = LeaveAreaEnum.getByCode(applyDept.getArea());

        return generatePlanNo(planTypeEnum, leaveAreaEnum);
    }

    /**
     * 获取出门证的结算月份
     * @return
     */
    private String getCurrentDate(){
        // 创建日期格式化对象，用于格式化时间戳
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        // 获取当前日历对象
        Calendar calNow = Calendar.getInstance();
        // 结算日为26号
        Calendar calComp = (Calendar) calNow.clone();
        calComp.set(Calendar.DATE, 26);
        calComp.set(Calendar.HOUR, 0);
        calNow.set(Calendar.HOUR, 1);
        // 如果当前时间在比较时间之后，则将比较时间的月份加1
        if (calNow.after(calComp)) {
            calComp.set(Calendar.MONTH, calComp.get(Calendar.MONTH) + 1);
        }
        // 格式化比较时间为字符串，用作序列号的一部分
        String bgtime = dateFormat.format(calComp.getTime());
        return bgtime;
    }

    private int nextSequence(String suffix, String currentDate){
        //触发oracle存储过程 common1.t_kqdb_nextval_2  LeaveMeasureMapper中实现
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("name_var", suffix);
        paramMap.put("bgtime", currentDate);
        leaveMeasureMapper.nextSequence(paramMap);
        return (Integer) paramMap.get("result");
    }

    private String getPlanNoPrefix(LeavePlanTypeEnum planTypeEnum, LeaveAreaEnum leaveAreaEnum){
        switch (planTypeEnum) {
            case OUT_NO_RETURN:
                if(leaveAreaEnum == LeaveAreaEnum.BINJIANG){
                    return "B";
                }else{
                    return "B2";
                }
            case OUT_RETURN:
                if(leaveAreaEnum == LeaveAreaEnum.BINJIANG){
                    return "F";
                }else{
                    return "F2";
                }
            case CROSS_AREA_TRANSFER:
                if(leaveAreaEnum == LeaveAreaEnum.BINJIANG){
                    return "6";
                }else{
                    return "62";
                }
            case RETURN_APPLY:
                if(leaveAreaEnum == LeaveAreaEnum.BINJIANG){
                    return "T";
                }else{
                    throw new RuntimeException("未知的计划类型");
                }
            default:
                throw new RuntimeException("未知的计划类型");
        }
    }

    private String getPlanNoSuffix(LeavePlanTypeEnum planTypeEnum, LeaveAreaEnum leaveAreaEnum){
        switch (planTypeEnum) {
            case OUT_NO_RETURN:
                if(leaveAreaEnum == LeaveAreaEnum.BINJIANG){
                    return "cmzbfhhao";
                }else{
                    return "yscmzbfhhao";
                }
            case OUT_RETURN:
                if(leaveAreaEnum == LeaveAreaEnum.BINJIANG){
                    return "passfh";
                }else{
                    return "yspassfh";
                }
            case CROSS_AREA_TRANSFER:
                if(leaveAreaEnum == LeaveAreaEnum.BINJIANG){
                    return "kqdbid";
                }else{
                    return "yskqdbid";
                }
            case RETURN_APPLY:
                if(leaveAreaEnum == LeaveAreaEnum.BINJIANG){
                    return "thwz";
                }else{
                    throw new RuntimeException("未知的计划类型");
                }
            default:
                throw new RuntimeException("未知的计划类型");
        }
    }

    /**
     * 分厂领导审核
     */
    private void FactoryApprove(LeavePlan leavePlan, ApproveRequestDTO approveRequestDTO,SysUser currentUser){
        leavePlan.setFactoryApproveWorkNo(currentUser.getUserName());
        leavePlan.setFactoryApproveTime(DateUtils.getNowDate());
        leavePlan.setFactoryApproveContent(approveRequestDTO.getApproveContent());
        leavePlan.setFactoryApproveFlag(LeavePlanStatusEnum.getByBool(approveRequestDTO.getApproveFlag()).getCode());

        //判断是否需要复审
        if(approveRequestDTO.getApproveFlag()) {
            if (LeaveSecApproveLogEnum.YES.getCode().intValue() == leavePlan.getSecApproveFlag().intValue()) {
                leavePlan.setPlanStatus(LeavePlanStatusEnum.WAIT_FACTORY_RECHECK.getCode());
            } else {
                leavePlan.setPlanStatus(LeavePlanStatusEnum.WAIT_PRODUCTION_APPROVAL.getCode());
            }
        }else {
            leavePlan.setPlanStatus(LeavePlanStatusEnum.REJECT.getCode());
        }

        leavePlan.setFactorySecApproveWorkNo(approveRequestDTO.getApproverWorkNo());
        leavePlanMapper.updateLeavePlan(leavePlan);

        //日志
        LeaveLog leaveLog = new LeaveLog();
        if(approveRequestDTO.getApproveFlag()) {
            if (LeaveSecApproveLogEnum.YES.getCode().intValue() == leavePlan.getSecApproveFlag().intValue()) {
                leaveLog.setInfo("分厂审批人\""+ currentUser.getNickName()  + "\"审批通过,理由为：" +approveRequestDTO.getApproveContent()  + "，下一节点分厂复审审批" );
            } else {
                leaveLog.setInfo("分厂审批人\""+ currentUser.getNickName()  + "\"审批通过，理由为：" +approveRequestDTO.getApproveContent()  + "，下一节点生产指挥中心审批");
            }
        }else {
                leaveLog.setInfo("分厂审批人\""+ currentUser.getNickName()  + "\"驳回,理由为：" + approveRequestDTO.getApproveContent());
        }
        leaveLog.setApplyNo(leavePlan.getApplyNo());
        leaveLog.setLogType(LeaveLogTypeEnum.PLAN_APPLY.getCode());
        leaveLog.setCreateBy(currentUser.getUserName());
        leaveLog.setCreateTime(new Date());
        leaveLogMapper.insertLeaveLog(leaveLog);
    }


    /**
     * 分厂复审
     */
    private void FactoryRecheck(LeavePlan leavePlan,ApproveRequestDTO approveRequestDTO,SysUser currentUser){
        leavePlan.setFactorySecApproveWorkNo(currentUser.getUserName());
        leavePlan.setFactorySecApproveTime(DateUtils.getNowDate());
        leavePlan.setFactorySecApproveContent(approveRequestDTO.getApproveContent());
        leavePlan.setFactorySecApproveFlag(LeavePlanStatusEnum.getByBool(approveRequestDTO.getApproveFlag()).getCode());

        if(approveRequestDTO.getApproveFlag()){
            leavePlan.setPlanStatus(LeavePlanStatusEnum.WAIT_PRODUCTION_APPROVAL.getCode());
        }else {
            leavePlan.setPlanStatus(LeavePlanStatusEnum.REJECT.getCode());
        }

        leavePlan.setCenterApproveWorkNo(approveRequestDTO.getApproverWorkNo());
        leavePlan.setUpdateBy(currentUser.getUserName());
        leavePlan.setUpdateTime(new Date());
        leavePlanMapper.updateLeavePlan(leavePlan);

        //日志
        LeaveLog leaveLog = new LeaveLog();
        leaveLog.setApplyNo(leavePlan.getApplyNo());
        if(approveRequestDTO.getApproveFlag()) {
            leaveLog.setInfo("分厂复审人\""+ currentUser.getNickName()  + "\"审批通过,理由为：" +approveRequestDTO.getApproveContent()  + "，下一节点生产指挥中心审核");
        }else {
            leaveLog.setInfo("分厂复审人\""+ currentUser.getNickName()  + "\"驳回,理由为：" + approveRequestDTO.getApproveContent());
        }
        leaveLog.setCreateBy(currentUser.getUserName());
        leaveLog.setLogType(LeaveLogTypeEnum.PLAN_APPLY.getCode());
        leaveLog.setCreateTime(new Date());
        leaveLogMapper.insertLeaveLog(leaveLog);
    }


    /**
     * 生产指挥中心审核
     */
    private void CenterApprove(LeavePlan leavePlan,ApproveRequestDTO approveRequestDTO,SysUser currentUser) {
        leavePlan.setCenterApproveWorkNo(currentUser.getUserName());
        leavePlan.setCenterApproveFlag(LeavePlanStatusEnum.getByBool(approveRequestDTO.getApproveFlag()).getCode());
        leavePlan.setCenterApproveTime(DateUtils.getNowDate());
        leavePlan.setCenterApproveContent(approveRequestDTO.getApproveContent());

        if(approveRequestDTO.getApproveFlag()){
            String planNo = generatePlanNo(leavePlan.getApplyNo());
            leavePlan.setPlanNo(planNo);
            leavePlan.setPlanStatus(LeavePlanStatusEnum.APPROVAL_COMPLETE.getCode());
        }else {
            leavePlan.setPlanStatus(LeavePlanStatusEnum.REJECT.getCode());
        }

        leavePlan.setUpdateTime(DateUtils.getNowDate());
        leavePlan.setUpdateBy(currentUser.getUserName());
        leavePlanMapper.updateLeavePlan(leavePlan);

        //日志
        LeaveLog leaveLog = new LeaveLog();
        leaveLog.setApplyNo(leavePlan.getApplyNo());
        if(approveRequestDTO.getApproveFlag()) {
            leaveLog.setInfo("生产指挥中心\""+ currentUser.getNickName()+"\"审核通过,理由为：" +approveRequestDTO.getApproveContent());
        }else {
            leaveLog.setInfo("生产指挥中心\""+ currentUser.getNickName()+"\"驳回,理由为：" + approveRequestDTO.getApproveContent());
        }
        leaveLog.setApplyNo(leavePlan.getApplyNo());
        leaveLog.setLogType(LeaveLogTypeEnum.PLAN_APPLY.getCode());
        leaveLog.setCreateBy(currentUser.getUserName());
        leaveLog.setCreateTime(new Date());
        leaveLogMapper.insertLeaveLog(leaveLog);
    }

    //废弃
    public void discard(String applyNo,SysUser sysUser){
            LeavePlan plan = leavePlanMapper.selectLeavePlanByApplyNo(applyNo);
            plan.setPlanStatus(LeavePlanStatusEnum.DISCARD.getCode());
            plan.setUpdateTime(DateUtils.getNowDate());
            plan.setUpdateBy(sysUser.getUserName());
            leavePlanMapper.updateLeavePlan(plan);

            LeaveLog leaveLog = new LeaveLog();
            leaveLog.setApplyNo(applyNo);
            leaveLog.setInfo("生产指挥中心\"" + sysUser.getNickName() + "\"废弃该审批单");
            leaveLog.setCreateBy(sysUser.getUserName());
            leaveLog.setCreateTime(DateUtils.getNowDate());
            leaveLogMapper.insertLeaveLog(leaveLog);

    }

    //废弃前检查
    public void checkBeforeDiscard(String applyNo,SysUser sysUser){
        LeavePlan leavePlan = leavePlanMapper.selectLeavePlanByApplyNo(applyNo);
        if(WAIT_FACTORY_APPROVAL.getCode() != leavePlan.getPlanStatus()
            && LeavePlanStatusEnum.WAIT_PRODUCTION_APPROVAL.getCode() != leavePlan.getPlanStatus()) {
            throw new RuntimeException("当前出门单状态为" + LeavePlanStatusEnum.getDescByCode(leavePlan.getPlanStatus()) + ",无法进行废弃操作!");
        }
        if (!sysRoleService.selectRoleExistByUserName(sysUser.getUserName(), LeaveRoleEnum.CENTER_APPROVER.getCode())
            && !sysRoleService.selectRoleExistByUserName(sysUser.getUserName(), LeaveRoleEnum.APPLICANT.getCode())){
            throw new RuntimeException("当前用户没有权限，无法进行废弃操作！");
        }
    }

    public List<LeaveStatusDTO> queryStatusList(){
        List<LeaveStatusDTO> list = new ArrayList<>();
        list.add(new LeaveStatusDTO("全部", 0));
        List<LeavePlanStatusEnum> leavePlanStatusEnumList = Lists.newArrayList(WAIT_FACTORY_APPROVAL,
                WAIT_FACTORY_RECHECK,
                WAIT_PRODUCTION_APPROVAL,
                APPROVAL_COMPLETE,
                OUT_FACTORY,
                PART_RECEIPT,
                COMPLETE,
                REJECT,
                DISCARD,
                EXPIRE);
        for(LeavePlanStatusEnum leavePlanStatusEnum : leavePlanStatusEnumList){
            LeaveStatusDTO leaveStatusDTO = new LeaveStatusDTO();
            leaveStatusDTO.setName(leavePlanStatusEnum.getDesc());
            leaveStatusDTO.setValue(leavePlanStatusEnum.getCode());
            list.add(leaveStatusDTO);
        }
        return list;
    }
    /**
     * 处理出门证计划不同类型对应的相关数据
     * 根据planType对应处理不同表的数据
     * @param leavePlan 出门证计划
     */
    @Override
    @DataSource(DataSourceType.XCC1)
    @Transactional(rollbackFor = Exception.class)
    public void handlePlan(LeavePlan leavePlan) {
        if (leavePlan == null || leavePlan.getPlanType() == null) {
            return;
        }

        // 根据planStatus设置Validflag
        Long validFlag = 1L; // 默认值
        if (leavePlan.getPlanStatus() != null) {
            switch (leavePlan.getPlanStatus()) {
                case 4:
                    validFlag = 1L;
                    break;
                case 7:
                    validFlag = 8L;
                    break;
                case 12:
                    validFlag = 10L;
                    break;
                default:
                    validFlag = 1L;
            }
        }

        Integer planType = leavePlan.getPlanType();

        // 根据计划类型进行不同的处理
        switch (planType) {
            case 3: // 跨区调拨 LeavePlanTypeEnum.CROSS_AREA_TRANSFER
                handleCarPlan(leavePlan, validFlag);
                break;
            case 2: // 出厂返回 LeavePlanTypeEnum.OUT_RETURN
                handlePassFhPlan(leavePlan, validFlag);
                break;
            case 1: // 出厂不返回 LeavePlanTypeEnum.OUT_NO_RETURN
                handlePassPlan(leavePlan, validFlag);
                break;
            case 4: // 退货申请 LeavePlanTypeEnum.RETURN_APPLY
                handlePassThPlan(leavePlan, validFlag);
                break;
            default:
                break;
        }
    }

    /**
     * 处理跨区调拨计划
     * @param leavePlan 出门证计划
     * @param validFlag 有效标志
     */
    private void handleCarPlan(LeavePlan leavePlan, Long validFlag) {
        // 主表数据处理
        LCarplanT carplanT = new LCarplanT();
        // 获取当前最大ID并加1
        Long maxId = lCarplanTMapper.selectMaxId();
        carplanT.setId(maxId != null ? maxId + 1 : 1L);
        carplanT.setPlanid(leavePlan.getPlanNo());
        carplanT.setApplyid(leavePlan.getApplyNo());
        carplanT.setOperatype(6L); // 计划类型：6跨区调拨
        carplanT.setValidflag(validFlag); // 根据planStatus设置Validflag
        carplanT.setStorename(leavePlan.getSourceCompany());
        carplanT.setStorecode(leavePlan.getSourceCompanyCode());
        carplanT.setTargetname(leavePlan.getTargetCompany());
        carplanT.setTargetcode(leavePlan.getTargetCompanyCode());
        carplanT.setValidtime(leavePlan.getExpireTime());
        carplanT.setSupervisor(leavePlan.getMonitor());
        carplanT.setWzzgy(leavePlan.getSpecialManager());
        carplanT.setOutmemo(leavePlan.getReason());
        carplanT.setMeasureflag(leavePlan.getMeasureFlag() != null ? leavePlan.getMeasureFlag().longValue() : 0L);
        carplanT.setCreateman(leavePlan.getApplyWorkNo());
        carplanT.setCreatedate(leavePlan.getApplyTime());
        carplanT.setBegintime(leavePlan.getStartTime());
        carplanT.setEndtime(leavePlan.getEndTime());
        carplanT.setPlanamount(leavePlan.getPlannedAmount());
        carplanT.setAttachpath(leavePlan.getApplyFileUrl());

        // 根据是否已有ID判断是新增还是更新
        if (leavePlan.getId() != null) {
            // 查询已有记录并更新
            LCarplanT existingCarPlan = lCarplanTMapper.selectByPlanid(leavePlan.getPlanNo());
            if (existingCarPlan != null) {
                carplanT.setId(existingCarPlan.getId());
                lCarplanTMapper.updateLCarplanT(carplanT);
            } else {
                lCarplanTMapper.insertLCarplanT(carplanT);
            }
        } else {
            lCarplanTMapper.insertLCarplanT(carplanT);
        }

        // 物资列表处理
        List<LeavePlanMaterial> materials = leavePlan.getMaterials();
        if (materials != null && !materials.isEmpty()) {
            // 如果是更新操作，先删除原有的明细
            if (leavePlan.getId() != null) {
                LCarplanT existingCarPlan = lCarplanTMapper.selectByPlanid(leavePlan.getPlanNo());
                if (existingCarPlan != null) {
                    lCarplanItemTMapper.deleteByPid(existingCarPlan.getId());
                }
            }

            for (LeavePlanMaterial material : materials) {
                LCarplanItemT carplanItemT = new LCarplanItemT();
                carplanItemT.setPid(carplanT.getId());
                carplanItemT.setMaterialcode(material.getMaterialNo());
                carplanItemT.setMaterialname(material.getMaterialName());
                carplanItemT.setMaterialspec(material.getMaterialSpec());
                carplanItemT.setUnit(material.getMeasureUnit());
                carplanItemT.setCount(material.getPlanNum().toString());
                carplanItemT.setMemo(material.getRemark());

                lCarplanItemTMapper.insertLCarplanItemT(carplanItemT);
            }
        }
    }

    /**
     * 处理出厂返回计划
     * @param leavePlan 出门证计划
     * @param validFlag 有效标志
     */
    private void handlePassFhPlan(LeavePlan leavePlan, Long validFlag) {
        // 主表数据处理
        LPassFhT passFhT = new LPassFhT();
        // 获取当前最大ID并加1
        Long maxId = lPassFhTMapper.selectMaxId();
        passFhT.setId(maxId != null ? maxId + 1 : 1L);
        passFhT.setCmzhao(leavePlan.getPlanNo());
        passFhT.setApplyid(leavePlan.getApplyNo());
        passFhT.setValidflag(validFlag); // 根据planStatus设置Validflag
        passFhT.setSourcecode(leavePlan.getSourceCompanyCode());
        passFhT.setSourcename(leavePlan.getSourceCompany());
        passFhT.setTargetcode(leavePlan.getReceiveCompanyCode());
        passFhT.setTargetname(leavePlan.getReceiveCompany());
        passFhT.setValidtime(leavePlan.getExpireTime());
        passFhT.setSupervisor(leavePlan.getMonitor());
        passFhT.setWzzgy(leavePlan.getSpecialManager());
        passFhT.setOutmemo(leavePlan.getReason());
        passFhT.setMeasureflag(leavePlan.getMeasureFlag() != null ? leavePlan.getMeasureFlag().longValue() : 0L);
        passFhT.setCreateman(leavePlan.getApplyWorkNo());
        passFhT.setCreatedate(leavePlan.getApplyTime());
        passFhT.setPlanreturndate(leavePlan.getPlanReturnTime());
        passFhT.setAttachpath(leavePlan.getApplyFileUrl());

        // 根据是否已有ID判断是新增还是更新
        if (leavePlan.getId() != null) {
            // 查询已有记录并更新
            LPassFhT existingPassFhT = lPassFhTMapper.selectByCmzhao(leavePlan.getPlanNo());
            if (existingPassFhT != null) {
                passFhT.setId(existingPassFhT.getId());
                lPassFhTMapper.updateLPassFhT(passFhT);
            } else {
                lPassFhTMapper.insertLPassFhT(passFhT);
            }
        } else {
            lPassFhTMapper.insertLPassFhT(passFhT);
        }

        // 物资列表处理
        List<LeavePlanMaterial> materials = leavePlan.getMaterials();
        if (materials != null && !materials.isEmpty()) {
            // 如果是更新操作，先删除原有的明细
            if (leavePlan.getId() != null) {
                LPassFhT existingPassFhT = lPassFhTMapper.selectByCmzhao(leavePlan.getPlanNo());
                if (existingPassFhT != null) {
                    lPassFhItemTMapper.deleteByPid(existingPassFhT.getId());
                }
            }

            for (LeavePlanMaterial material : materials) {
                LPassFhItemT passFhItemT = new LPassFhItemT();
                passFhItemT.setPid(passFhT.getId());
                passFhItemT.setMaterialcode(material.getMaterialNo());
                passFhItemT.setMaterialname(material.getMaterialName());
                passFhItemT.setMaterialspec(material.getMaterialSpec());
                passFhItemT.setUnit(material.getMeasureUnit());
                passFhItemT.setCount(material.getPlanNum().toString());
                passFhItemT.setMemo(material.getRemark());

                lPassFhItemTMapper.insertLPassFhItemT(passFhItemT);
            }
        }
    }

    /**
     * 处理出厂不返回计划
     * @param leavePlan 出门证计划
     * @param validFlag 有效标志
     */
    private void handlePassPlan(LeavePlan leavePlan, Long validFlag) {
        // 主表数据处理
        LPassT passT = new LPassT();
        // 获取当前最大ID并加1
        Long maxId = lPassTMapper.selectMaxId();
        passT.setId(maxId != null ? maxId + 1 : 1L);
        passT.setCmzhao(leavePlan.getPlanNo());
        passT.setApplyid(leavePlan.getApplyNo());
        passT.setValidflag(validFlag); // 根据planStatus设置Validflag
        passT.setSourcecode(leavePlan.getSourceCompanyCode());
        passT.setSourcename(leavePlan.getSourceCompany());
        passT.setTargetcode(leavePlan.getReceiveCompanyCode());
        passT.setTargetname(leavePlan.getReceiveCompany());
        passT.setValidtime(leavePlan.getExpireTime());
        passT.setSupervisor(leavePlan.getMonitor());
        passT.setWzzgy(leavePlan.getSpecialManager());
        passT.setOutmemo(leavePlan.getReason());
        passT.setMeasureflag(leavePlan.getMeasureFlag() != null ? leavePlan.getMeasureFlag().longValue() : 0L);
        passT.setCreateman(leavePlan.getApplyWorkNo());
        passT.setCreatedate(leavePlan.getApplyTime());
        passT.setAttachpath(leavePlan.getApplyFileUrl());

        // 根据是否已有ID判断是新增还是更新
        if (leavePlan.getId() != null) {
            // 查询已有记录并更新
            LPassT existingPassT = lPassTMapper.selectByCmzhao(leavePlan.getPlanNo());
            if (existingPassT != null) {
                passT.setId(existingPassT.getId());
                lPassTMapper.updateLPassT(passT);
            } else {
                lPassTMapper.insertLPassT(passT);
            }
        } else {
            lPassTMapper.insertLPassT(passT);
        }

        // 物资列表处理
        List<LeavePlanMaterial> materials = leavePlan.getMaterials();
        if (materials != null && !materials.isEmpty()) {
            // 如果是更新操作，先删除原有的明细
            if (leavePlan.getId() != null) {
                LPassT existingPassT = lPassTMapper.selectByCmzhao(leavePlan.getPlanNo());
                if (existingPassT != null) {
                    lPassItemTMapper.deleteByPid(existingPassT.getId());
                }
            }

            for (LeavePlanMaterial material : materials) {
                LPassItemT passItemT = new LPassItemT();
                passItemT.setPid(passT.getId());
                passItemT.setMaterialcode(material.getMaterialNo());
                passItemT.setMaterialname(material.getMaterialName());
                passItemT.setMaterialspec(material.getMaterialSpec());
                passItemT.setUnit(material.getMeasureUnit());
                passItemT.setCount(material.getPlanNum().toString());
                passItemT.setMemo(material.getRemark());

                lPassItemTMapper.insertLPassItemT(passItemT);
            }
        }
    }

    /**
     * 处理退货申请计划
     * @param leavePlan 出门证计划
     * @param validFlag 有效标志
     */
    private void handlePassThPlan(LeavePlan leavePlan, Long validFlag) {
        // 主表数据处理
        LPassThT passThT = new LPassThT();
        // 获取当前最大ID并加1
        Long maxId = lPassThTMapper.selectMaxId();
        passThT.setId(maxId != null ? maxId + 1 : 1L);
        passThT.setCmzhao(leavePlan.getPlanNo());
        passThT.setApplyid(leavePlan.getApplyNo());
        passThT.setValidflag(validFlag); // 根据planStatus设置Validflag
        passThT.setSourcecode(leavePlan.getSourceCompanyCode());
        passThT.setSourcename(leavePlan.getSourceCompany());
        passThT.setTargetcode(leavePlan.getReceiveCompanyCode());
        passThT.setTargetname(leavePlan.getReceiveCompany());
        passThT.setValidtime(leavePlan.getExpireTime());
        passThT.setSupervisor(leavePlan.getMonitor());
        passThT.setWzzgy(leavePlan.getSpecialManager());
        passThT.setOutmemo(leavePlan.getReason());
        passThT.setMeasureflag(leavePlan.getMeasureFlag() != null ? leavePlan.getMeasureFlag().longValue() : 0L);
        passThT.setCreateman(leavePlan.getApplyWorkNo());
        passThT.setCreatedate(leavePlan.getApplyTime());
        passThT.setThdwname(leavePlan.getRefundDepartment());
        passThT.setThdwcode(leavePlan.getRefundDepartmentCode());
        passThT.setAttachpath(leavePlan.getApplyFileUrl());

        // 根据是否已有ID判断是新增还是更新
        if (leavePlan.getId() != null) {
            // 查询已有记录并更新
            LPassThT existingPassThT = lPassThTMapper.selectByCmzhao(leavePlan.getPlanNo());
            if (existingPassThT != null) {
                passThT.setId(existingPassThT.getId());
                lPassThTMapper.updateLPassThT(passThT);
            } else {
                lPassThTMapper.insertLPassThT(passThT);
            }
        } else {
            lPassThTMapper.insertLPassThT(passThT);
        }

        // 物资列表处理
        List<LeavePlanMaterial> materials = leavePlan.getMaterials();
        if (materials != null && !materials.isEmpty()) {
            // 如果是更新操作，先删除原有的明细
            if (leavePlan.getId() != null) {
                LPassThT existingPassThT = lPassThTMapper.selectByCmzhao(leavePlan.getPlanNo());
                if (existingPassThT != null) {
                    lPassThItemTMapper.deleteByPid(existingPassThT.getId());
                }
            }

            for (LeavePlanMaterial material : materials) {
                LPassThItemT passThItemT = new LPassThItemT();
                passThItemT.setPid(passThT.getId());
                passThItemT.setMaterialcode(material.getMaterialNo());
                passThItemT.setMaterialname(material.getMaterialName());
                passThItemT.setMaterialspec(material.getMaterialSpec());
                passThItemT.setUnit(material.getMeasureUnit());
                passThItemT.setCount(material.getPlanNum().toString());
                passThItemT.setMemo(material.getRemark());

                lPassThItemTMapper.insertLPassThItemT(passThItemT);
            }
        }
    }

    /**
     * 处理过期和完成的出门证计划
     * 检查所有未完成的出门证计划，根据有效期和任务完成情况更新状态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void leavePlanExpired() {
        // 查询所有未完成的出门证计划
        LeavePlan query = new LeavePlan();
        List<Integer> statusList = Arrays.asList(
            LeavePlanStatusEnum.APPROVAL_COMPLETE.getCode(),
            LeavePlanStatusEnum.OUT_FACTORY.getCode(),
            LeavePlanStatusEnum.PART_RECEIPT.getCode()
        );
        query.setStatusList(statusList);
        List<LeavePlan> leavePlans = leavePlanMapper.selectLeavePlanList(query);

        Date now = DateUtils.getNowDate();
        for (LeavePlan plan : leavePlans) {
            // 检查是否过期
            if (plan.getExpireTime() != null && plan.getExpireTime().before(now)) {
                // 检查是否有未完成的任务
                boolean hasUnfinishedTasks = checkUnfinishedTasks(plan);
                
                // 更新计划状态
                plan.setPlanStatus(hasUnfinishedTasks ? 
                    LeavePlanStatusEnum.EXPIRE.getCode() :
                    LeavePlanStatusEnum.COMPLETE.getCode());
                plan.setUpdateTime(now);
                plan.setUpdateBy("system");
                
                // 更新数据库
                leavePlanMapper.updateLeavePlan(plan);

                //如果完成则与计量交互
                if (plan.getPlanStatus() == 7) {
                    handlePlan(plan);
                }
                
                // 添加操作日志
                LeaveLog leaveLog = new LeaveLog();
                leaveLog.setApplyNo(plan.getApplyNo());
                leaveLog.setLogType(LeaveLogTypeEnum.PLAN_APPLY.getCode());
                leaveLog.setInfo("系统自动将出门证计划状态更新为" + 
                    (hasUnfinishedTasks ? "过期" : "完成"));
                leaveLog.setCreateBy("system");
                leaveLog.setCreateTime(now);
                leaveLogMapper.insertLeaveLog(leaveLog);
            }
        }
    }

    /**
     * 检查出门证计划是否有未完成的任务
     * @param plan 出门证计划
     * @return 是否有未完成的任务
     */
    private boolean checkUnfinishedTasks(LeavePlan plan) {
        // 查询该计划下的所有任务
        List<LeaveTask> tasks = leaveTaskMapper.selectByPlanNo(plan.getPlanNo());
        
        // 如果没有任务，则认为已完成
        if (tasks == null || tasks.isEmpty()) {
            return false;
        }

        if (plan.getPlanType() == 2  && plan.getMeasureFlag() == 0) {
            //出厂返回特殊情况处理
            LeavePlanMaterial leavePlanMaterial = new LeavePlanMaterial();
            leavePlanMaterial.setApplyNo(plan.getApplyNo());
            List<LeavePlanMaterial> leavePlanMaterials = leavePlanMaterialService.selectLeavePlanMaterialList(leavePlanMaterial);

            HashMap<Long, BigDecimal> hashMap = new HashMap<>();

            for (LeaveTask task : tasks) {
                if (task.getTaskType() == 2) {
                    LeaveTaskMaterial leaveTaskMaterial = new LeaveTaskMaterial();
                    leaveTaskMaterial.setTaskNo(task.getTaskNo());
                    List<LeaveTaskMaterial> leaveTaskMaterials = leaveTaskMaterialService.selectLeaveTaskMaterialList(leaveTaskMaterial);

                    for (LeaveTaskMaterial taskMaterial : leaveTaskMaterials) {
                        Long materialId = taskMaterial.getMaterialId();
                        BigDecimal planNum = taskMaterial.getPlanNum();
                        if (hashMap.containsKey(materialId)) {
                            BigDecimal bigDecimal = hashMap.get(materialId);
                            BigDecimal add = planNum.add(bigDecimal);
                            hashMap.put(materialId, add);
                        } else {
                            hashMap.put(materialId, planNum);
                        }
                    }
                }
            }

            for (LeavePlanMaterial planMaterial : leavePlanMaterials) {
                Long materialId = planMaterial.getMaterialId();
                BigDecimal planNum = planMaterial.getPlanNum();

                if (hashMap.containsKey(materialId)) {
                    BigDecimal bigDecimal = hashMap.get(materialId);
                    if (bigDecimal.compareTo(planNum) != 0) {
                        return true;
                    }
                } else {
                    return true;
                }
            }
        }

        if (plan.getPlanType() == 2 && plan.getMeasureFlag() == 1) {
            if (plan.getPlanStatus() != 7) {
                return true;
            }
        }
        // 检查是否所有任务都已完成
        for (LeaveTask task : tasks) {
            // 如果任何一个任务未完成，则返回true表示有未完成任务
            if (task.getTaskStatus() != null && task.getTaskStatus() != 9) { // 9表示完成状态
                return true;
            }
        }

        // 所有任务都已完成
        return false;
    }

    /**
     * 物资确认，将计划状态更新为已完成（7）
     * @param applyNo 申请编号
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmMaterial(String applyNo) {
        LeavePlan plan = leavePlanMapper.selectLeavePlanByApplyNo(applyNo);
        if (plan == null) {
            return 0;
        }
        // 只允许planStatus为5（已出厂）或6（部分收货）时确认
//        if (plan.getPlanStatus() != 5 && plan.getPlanStatus() != 6) {
//            return 0;
//        }
        //plan.setPlanStatus(LeavePlanStatusEnum.COMPLETE.getCode()); // 7-已完成
        //return leavePlanMapper.updateLeavePlan(plan);
        return leavePlanMapper.updatePlanStatusByApplyNo(applyNo, LeavePlanStatusEnum.COMPLETE.getCode());
    }
}
