<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.saleScore.mapper.TScoreItemMapper">
    
    <resultMap type="TScoreItem" id="TScoreItemResult">
        <result property="id"    column="id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="status"    column="status"    />
        <result property="scoreTime"    column="score_time"    />
        <result property="markNo"    column="mark_no"    />
        <result property="markName"    column="mark_name"    />
        <result property="userId"    column="user_id"    />
        <result property="scoreContent"    column="score_content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="scoreName"    column="score_name"    />
    </resultMap>

    <sql id="selectTScoreItemVo">
        select t.id, t.rule_id, t.status, t.score_time, t.mark_no, t.mark_name, t.user_id, t.score_content, t.create_time, t.update_time, t.delete_time,t1.score_name from t_score_item t
        left join t_score_rule t1 on t.rule_id = t1.id
    </sql>

    <select id="selectTScoreItemList" parameterType="TScoreItem" resultMap="TScoreItemResult">
        <include refid="selectTScoreItemVo"/>
        <where>
            1=1
            <if test="ruleId != null "> and t.rule_id = #{ruleId}</if>
            <if test="status != null "> and t.status = #{status}</if>
            <if test="scoreTime != null  and scoreTime != ''"> and t.score_time = #{scoreTime}</if>
            <if test="markNo != null  and markNo != ''"> and t.mark_no = #{markNo}</if>
            <if test="markName != null  and markName != ''"> and t.mark_name like concat('%', #{markName}, '%')</if>
            <if test="userId != null "> and t.user_id = #{userId}</if>
            <if test="scoreContent != null  and scoreContent != ''"> and t.score_content = #{scoreContent}</if>
             and t.delete_time is null
        </where>
        order by t.rule_id,t.score_time,t.mark_no,t.user_id,t.status
    </select>


    <select id="selectSelfItemList" parameterType="TScoreItem" resultType="Map">
        select  t1.id , t1.rule_id as ruleId, t1.status, t1.score_time as scoreTime, t1.mark_no as markNo, t1.mark_name as markName, t1.score_content as scoreContent,
        t1.user_id as userId ,t2.work_no as workNo ,t2.work_name as workName, t2.sale_name as saleName,t2.service_name as serviceName, t2.role_name as roleName,
        t3.score_name as scoreName
        from t_score_item t1
        left join t_score_user t2 on t1.user_id = t2.id
        left join t_score_rule t3 on t1.rule_id = t3.id
        <where>
            1=1
            <if test="workNo != null "> and t2.work_no = #{workNo}</if>
            <if test="status != null "> and t1.status = #{status}</if>
            <if test="scoreTime != null  and scoreTime != ''"> and t1.score_time = #{scoreTime}</if>
            <if test="markName != null  and markName != ''"> and t1.mark_name like concat('%', #{markName}, '%')</if>
            and t1.delete_time is null
        </where>
        order by t1.rule_id,t1.status,t1.mark_no
    </select>


    <select id="selectTScoreItemById" parameterType="Integer" resultMap="TScoreItemResult">
        <include refid="selectTScoreItemVo"/>
        where t.id = #{id} and t.delete_time is null
    </select>

    <select id="selectHQReport" parameterType="String" resultType="Map">
        select
        t1.id,
        t1.mark_no as markNo,
        t1.mark_name   as markName,
        t2.role_code   as roleCode,
        t2.role_name   as roleName,
        t2.sale_code   as saleCode,
        t2.sale_name   as saleName,
        t2.work_no   as workNo,
        t2.work_name   as workName,
        t2.service_code   as serviceCode,
        t2.service_name   as serviceName,
        t1.`status`,
        t1.score_time   as scoreTime,
        t1.score_content   as scoreContent
        from t_score_item t1 LEFT JOIN t_score_user t2 on t1.user_id = t2.id
        where 1 = 1 and t1.delete_time is null and t1.rule_id = 10 and t1.score_time = #{scoreTime}
        order by  t1.mark_no,t1.mark_name,t2.role_code desc,t2.sale_code desc,t2.service_code desc
    </select>

    <insert id="insertTScoreItem" parameterType="TScoreItem" useGeneratedKeys="true" keyProperty="id">
        insert into t_score_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">rule_id,</if>
            <if test="status != null">status,</if>
            <if test="scoreTime != null">score_time,</if>
            <if test="markNo != null">mark_no,</if>
            <if test="markName != null">mark_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="scoreContent != null">score_content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId},</if>
            <if test="status != null">#{status},</if>
            <if test="scoreTime != null">#{scoreTime},</if>
            <if test="markNo != null">#{markNo},</if>
            <if test="markName != null">#{markName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="scoreContent != null">#{scoreContent},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
         </trim>
    </insert>

    <update id="updateTScoreItem" parameterType="TScoreItem">
        update t_score_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="scoreTime != null">score_time = #{scoreTime},</if>
            <if test="markNo != null">mark_no = #{markNo},</if>
            <if test="markName != null">mark_name = #{markName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="scoreContent != null">score_content = #{scoreContent},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
        </trim>
        where id = #{id}
    </update>


    <update id="deleteTScoreItemById" parameterType="TScoreItem">
        update t_score_item set delete_time = #{deleteTime}   where id = #{id}
    </update>
    
</mapper>