package com.ruoyi.web.controller.leave;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LPassFhItemT;
import com.ruoyi.app.leave.service.ILPassFhItemTService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出厂返回物资明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@RestController
@RequestMapping("/web/leave/passFhItem")
public class WebLPassFhItemTController extends BaseController
{
    @Autowired
    private ILPassFhItemTService lPassFhItemTService;

    /**
     * 查询出厂返回物资明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LPassFhItemT lPassFhItemT)
    {
        startPage();
        List<LPassFhItemT> list = lPassFhItemTService.selectLPassFhItemTList(lPassFhItemT);
        return getDataTable(list);
    }

    /**
     * 导出出厂返回物资明细列表
     */
    @Log(title = "出厂返回物资明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LPassFhItemT lPassFhItemT)
    {
        List<LPassFhItemT> list = lPassFhItemTService.selectLPassFhItemTList(lPassFhItemT);
        ExcelUtil<LPassFhItemT> util = new ExcelUtil<LPassFhItemT>(LPassFhItemT.class);
        return util.exportExcel(list, "passFhItem");
    }

    /**
     * 获取出厂返回物资明细详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lPassFhItemTService.selectLPassFhItemTById(id));
    }

    /**
     * 新增出厂返回物资明细
     */
    @Log(title = "出厂返回物资明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LPassFhItemT lPassFhItemT)
    {
        return toAjax(lPassFhItemTService.insertLPassFhItemT(lPassFhItemT));
    }

    /**
     * 修改出厂返回物资明细
     */
    @Log(title = "出厂返回物资明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LPassFhItemT lPassFhItemT)
    {
        return toAjax(lPassFhItemTService.updateLPassFhItemT(lPassFhItemT));
    }

    /**
     * 删除出厂返回物资明细
     */
    @Log(title = "出厂返回物资明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lPassFhItemTService.deleteLPassFhItemTByIds(ids));
    }
}
