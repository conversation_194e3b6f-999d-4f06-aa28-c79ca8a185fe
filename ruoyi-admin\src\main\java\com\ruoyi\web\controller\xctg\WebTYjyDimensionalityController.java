package com.ruoyi.web.controller.xctg;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.eventTrack.domain.TEtEvent;
import com.ruoyi.app.v1.domain.*;
import com.ruoyi.app.v1.mapper.AppCommonV1Mapper;
import com.ruoyi.app.v1.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * TYjyDimensionalityController
 * 
 * <AUTHOR>
 * @date 2024-10-21
 */
@RestController
@RequestMapping("/web/TYjy/dimensionality")
public class WebTYjyDimensionalityController extends BaseController
{
    @Autowired
    private ITYjyDimensionalityService tYjyDimensionalityService;

    @Autowired
    private ITYjyPermissionService tYjyPermissionService;

    @Autowired
    private ITYjyFormService tYjyFormService;

    @Autowired
    private ITYjyDeptUserService tYjyDeptUserService;

    @Autowired
    private ITYjyDimensionalityPermissionService tYjyDimensionalityPermissionService;

    @Autowired
    private ITYjyDeptService tYjyDeptService;

    @Autowired
    private AppCommonV1Mapper appCommonMapper;
    /**
     * 查询TYjyDimensionality列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TYjyDimensionality tYjyDimensionality)
    {
        startPage();
        List<TYjyDimensionality> list = tYjyDimensionalityService.selectTYjyDimensionalityList(tYjyDimensionality);
        return getDataTable(list);
    }

//    @GetMapping("/rootList")
//    public TableDataInfo rootlist(TYjyDimensionality tYjyDimensionality)
//    {
////        startPage();
//        List<TYjyDimensionality> list = tYjyDimensionalityService.selectRootList(tYjyDimensionality);
//        return getDataTable(list);
//    }


    @GetMapping("/getList")
    public TableDataInfo addFormList(TYjyDimensionality tYjyDimensionality)
    {
        List<TYjyDimensionality> re=new ArrayList<>();
        List<TYjyDimensionality> search;
        List<TYjyDimensionality> list = tYjyDimensionalityService.selectRootList(tYjyDimensionality);
        for(TYjyDimensionality item:list)
        {
            search = tYjyDimensionalityService.getByRootId(item);
            Map<Long,List<TYjyDimensionality>> map=new HashMap<>();
            for(TYjyDimensionality item1:search)
            {
                if(map.containsKey(item1.getParentId()))
                {
                    map.get(item1.getParentId()).add(item1);
                }
                else
                {
                    List<TYjyDimensionality> insert=new ArrayList<>();
                    insert.add(item1);
                    map.put(item1.getParentId(),insert);
                }
            }
            re.add(tYjyDimensionalityService.getALlTree(item,map));
        }
        return getDataTable(re);
    }


//    @GetMapping("/getAllList")
//    public AjaxResult getAllList(TYjyDimensionality tYjyDimensionality)
//    {
//        List<TYjyDimensionalityRoot> re=new ArrayList<>();
//        List<TYjyDimensionality> list = tYjyDimensionalityService.selectRootList(tYjyDimensionality);
//        for(TYjyDimensionality item:list)
//        {
//            re.add(getALLRoot(item));
//        }
//        return AjaxResult.success(re);
//    }

    //根据部门权限获取所有的相关维度节点，不进行任何处理
//    @GetMapping("/getListWithDept")
//    public AjaxResult getListWithDept(TYjyDimensionality tYjyDimensionality)
//    {
//        tYjyDimensionality.setWorkNo(SecurityUtils.getUsername());
//        List<TYjyDimensionality> list = tYjyDimensionalityService.selectRootList(tYjyDimensionality);
//        return AjaxResult.success(list);
//    }




    @GetMapping("/deptList")
    public AjaxResult deptInfo()
    {
        return AjaxResult.success(tYjyDimensionalityService.getDeptInfoList());
    }

    /**
     * 导出TYjyDimensionality列表
     */
    @Log(title = "TYjyDimensionality", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TYjyDimensionality tYjyDimensionality)
    {
        List<TYjyDimensionality> list = tYjyDimensionalityService.selectTYjyDimensionalityList(tYjyDimensionality);
        ExcelUtil<TYjyDimensionality> util = new ExcelUtil<TYjyDimensionality>(TYjyDimensionality.class);
        return util.exportExcel(list, "dimensionality");
    }

    /**
     * 获取TYjyDimensionality详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tYjyDimensionalityService.selectTYjyDimensionalityById(id));
    }


    /**
     * 获取TYjyDimensionality详细信息,
     */
    @GetMapping("/rootbyid")
    public AjaxResult getRoot(Long id,String ruleType)
    {
        TYjyDimensionality tYjyDimensionality=tYjyDimensionalityService.selectTYjyDimensionalityById(id);
//        TYjyDimensionalityPermission tYjyDimensionalityPermission=new TYjyDimensionalityPermission();
//        tYjyDimensionalityPermission.setDimensionalityId(id);
//        tYjyDimensionalityPermission.setWorkNo(SecurityUtils.getUsername());
//        tYjyDimensionalityPermissionService
        if(Objects.isNull(tYjyDimensionality)) return AjaxResult.success();
        //重新优化管理逻辑，要考虑核心情况
        tYjyDimensionality.setCreateBy(SecurityUtils.getUsername());

//        List<TYjyDimensionality> list = tYjyDimensionalityService.getByRootId(tYjyDimensionality);
        List<TYjyDimensionality> list = tYjyDimensionalityService.getByRootIdForUser(tYjyDimensionality);
        TYjyDimensionalityRoot root=new TYjyDimensionalityRoot();
        root.setValue(tYjyDimensionality.getId());
        root.setLabel(tYjyDimensionality.getDimensionalityName());
        root.setIsUse(tYjyDimensionality.getIsUse());
        root.setRuleType(ruleType);
        Map<Long,List<TYjyDimensionalityRoot>> map=new HashMap<>();
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            add.setRuleType(item.getRuleType());
            if(map.containsKey(item.getParentId()))
            {
                map.get(item.getParentId()).add(add);
            }
            else
            {
                List<TYjyDimensionalityRoot> insert=new ArrayList<>();
                insert.add(add);
                map.put(item.getParentId(),insert);
            }
        }
        return AjaxResult.success(tYjyDimensionalityService.getTree(root,map));
    }


//    /**
//     * 获取TYjyDimensionality详细信息
//     */
//    @GetMapping("/rootbyidWithDept")
//    public AjaxResult rootbyidWithDept(Long id)
//    {
//        TYjyDimensionality tYjyDimensionality=tYjyDimensionalityService.selectTYjyDimensionalityById(id);
//        if(Objects.isNull(tYjyDimensionality)) return AjaxResult.success();
//        List<TYjyDimensionality> list = tYjyDimensionalityService.getByRootId(tYjyDimensionality);
//        TYjyDimensionalityRoot root=new TYjyDimensionalityRoot();
//        root.setValue(tYjyDimensionality.getId());
//        root.setLabel(tYjyDimensionality.getDimensionalityName());
//        root.setIsUse(tYjyDimensionality.getIsUse());
//        Map<Long,List<TYjyDimensionalityRoot>> map=new HashMap<>();
//        for(TYjyDimensionality item:list)
//        {
//            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
//            add.setValue(item.getId());
//            add.setLabel(item.getDimensionalityName());
//            add.setIsUse(item.getIsUse());
//            if(map.containsKey(item.getParentId()))
//            {
//                map.get(item.getParentId()).add(add);
//            }
//            else
//            {
//                List<TYjyDimensionalityRoot> insert=new ArrayList<>();
//                insert.add(add);
//                map.put(item.getParentId(),insert);
//            }
//        }
//        return AjaxResult.success(tYjyDimensionalityService.getTree(root,map));
//    }





    /**
     * 新增TYjyDimensionality
     */
    @Log(title = "TYjyDimensionality", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TYjyDimensionality tYjyDimensionality)
    {

        if(isexit(tYjyDimensionality)>0)
        {
            return AjaxResult.error("在同一父维度下，已存在相同名称的维度");
        }
        if(tYjyDimensionality.getParentId()==null)
        {
            tYjyDimensionality.setPath("");
        }
        else
        {
            TYjyDimensionality parent =tYjyDimensionalityService.selectTYjyDimensionalityById(tYjyDimensionality.getParentId());
            tYjyDimensionality.setPath(parent.getPath());
        }
        tYjyDimensionality.setCreateBy(SecurityUtils.getUsername());
        if(tYjyDimensionality.getDeptCode()!=null)
        {
            String[] test=tYjyDimensionality.getDeptCode().split(",");
            Long id=Long.valueOf(test[test.length-1]);
            TYjyDept tYjyDept= tYjyDeptService.selectTYjyDeptById(id);
            tYjyDimensionality.setDeptName(tYjyDept.getDeptName());
        }
        int re=tYjyDimensionalityService.insertTYjyDimensionality(tYjyDimensionality);
//        TYjyDimensionality search=new TYjyDimensionality();
//        search.setDimensionalityName(tYjyDimensionality.getDimensionalityName());
//        search.setParentId(tYjyDimensionality.getParentId());
//        search=tYjyDimensionalityService.selectTYjyDimensionalityList(search).get(0);
        TYjyDimensionality update=new TYjyDimensionality();
        update.setId(tYjyDimensionality.getId());
        update.setPath(tYjyDimensionality.getPath()+tYjyDimensionality.getId()+",");
        tYjyDimensionalityService.updateTYjyDimensionality(update);
        if(tYjyDimensionality.getParentId()==null)
        {
            SysUser user=SecurityUtils.getLoginUser().getUser();
            String workNo = user.getUserName();
            String userName = user.getNickName();
            TYjyDimensionalityPermission tYjyDimensionalityPermission=new TYjyDimensionalityPermission();
            tYjyDimensionalityPermission.setRuleType("4");
            tYjyDimensionalityPermission.setDimensionalityName(tYjyDimensionality.getDimensionalityName());
            tYjyDimensionalityPermission.setDimensionalityId(tYjyDimensionality.getId());
            tYjyDimensionalityPermission.setWorkNo(workNo);
            tYjyDimensionalityPermission.setUserName(userName);
            tYjyDimensionalityPermission.setPermissionPath(update.getPath());
            tYjyDimensionalityPermissionService.insertTYjyDimensionalityPermission(tYjyDimensionalityPermission);
            //部门管理员新增权限
            if(tYjyDimensionality.getDeptCode()!=null)
            {
                tYjyDimensionalityPermission = new TYjyDimensionalityPermission();
                tYjyDimensionalityPermission.setDimensionalityName(tYjyDimensionality.getDimensionalityName());
                tYjyDimensionalityPermission.setDimensionalityId(tYjyDimensionality.getId());
                tYjyDimensionalityPermission.setPath(tYjyDimensionality.getDeptCode());
                tYjyDimensionalityPermission.setPermissionPath(update.getPath());
                tYjyDimensionalityPermissionService.deleteDimenPermissionForDimenPermission(tYjyDimensionalityPermission);
                tYjyDimensionalityPermissionService.insertDimenPermissionForDimenPermission(tYjyDimensionalityPermission);
            }
        }
        return toAjax(re);
    }

//    /**
//     * 新增TYjyDimensionality
//     */
//    @PostMapping("/copytree")
//    public AjaxResult copytree(@RequestBody TYjyDimensionality tYjyDimensionality)
//    {
//
//        TYjyDimensionality originalTree=tYjyDimensionalityService.selectTYjyDimensionalityById(tYjyDimensionality.getId());
//        if(Objects.isNull(originalTree)) return AjaxResult.success();
//
//        List<TYjyDimensionality> list = tYjyDimensionalityService.getByRootId(tYjyDimensionality);
//        Map<Long,List<TYjyDimensionality>> map=new HashMap<>();
//
//        for(TYjyDimensionality item:list)
//        {
//            if(map.containsKey(item.getParentId()))
//            {
//                map.get(item.getParentId()).add(item);
//            }
//            else
//            {
//                List<TYjyDimensionality> insert=new ArrayList<>();
//                insert.add(item);
//                map.put(item.getParentId(),insert);
//            }
//        }
//
//        tYjyDimensionality=tYjyDimensionalityService.getTree(tYjyDimensionality,map);
//
//        if(tYjyDimensionality.getParentId()==null)
//        {
//            tYjyDimensionality.setPath("");
//        }
//        else
//        {
//            TYjyDimensionality parent =tYjyDimensionalityService.selectTYjyDimensionalityById(tYjyDimensionality.getParentId());
//            if(parent.getParentId()==null)
//            {
//                tYjyDimensionality.setPath(parent.getId()+",");
//            }
//            else
//            {
//                tYjyDimensionality.setPath(parent.getPath()+parent.getId()+",");
//            }
//
//        }
//        return toAjax(tYjyDimensionalityService.insertTYjyDimensionality(tYjyDimensionality));
//    }


    /**
     * 修改TYjyDimensionality
     */
    @Log(title = "TYjyDimensionality", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody TYjyDimensionality tYjyDimensionality)
    {
        TYjyDimensionality re = tYjyDimensionalityService.selectTYjyDimensionalityById(tYjyDimensionality.getId());
        if(!re.getDimensionalityName().equals(tYjyDimensionality.getDimensionalityName()))
        {
            if(isexit(tYjyDimensionality)>0)
            {
                return AjaxResult.error("在同一父维度下，已存在相同名称的维度");
            }
        }
        if(!re.getDeptCode().equals(tYjyDimensionality.getDeptCode()))
        {
            TYjyDimensionalityPermission tYjyDimensionalityPermission = new TYjyDimensionalityPermission();
            tYjyDimensionalityPermission.setDimensionalityName(re.getDimensionalityName());
            tYjyDimensionalityPermission.setDimensionalityId(re.getId());
            tYjyDimensionalityPermission.setPath(re.getDeptCode());
            tYjyDimensionalityPermissionService.deleteDimenPermissionForDimenPermission(tYjyDimensionalityPermission);
            tYjyDimensionalityPermission.setPath(tYjyDimensionality.getDeptCode());
            tYjyDimensionalityPermission.setDimensionalityName(tYjyDimensionality.getDimensionalityName());
            tYjyDimensionalityPermission.setPermissionPath(re.getPath());
            tYjyDimensionalityPermissionService.deleteDimenPermissionForDimenPermission(tYjyDimensionalityPermission);
            tYjyDimensionalityPermissionService.insertDimenPermissionForDimenPermission(tYjyDimensionalityPermission);
        }
        return toAjax(tYjyDimensionalityService.updateTYjyDimensionality(tYjyDimensionality));
    }



    @DeleteMapping("/delete/id")
    public AjaxResult delete(@PathVariable("id") Long id)
    {
        TYjyDimensionality tYjyDimensionality=new TYjyDimensionality();
        tYjyDimensionality.setId(id);
        List<TYjyDimensionality> list = tYjyDimensionalityService.getChildremById(tYjyDimensionality);
        TYjyDimensionality re = tYjyDimensionalityService.selectTYjyDimensionalityById(id);
        if(re.getParentId()==null)
        {
            if(re.getDeptCode()!=null)
            {
                TYjyDimensionalityPermission tYjyDimensionalityPermission = new TYjyDimensionalityPermission();
                tYjyDimensionalityPermission.setDimensionalityId(re.getId());
                tYjyDimensionalityPermission.setPath(re.getDeptCode());
                tYjyDimensionalityPermission.setDimensionalityName(re.getDimensionalityName());
                tYjyDimensionalityPermissionService.deleteDimenPermissionForDimenPermission(tYjyDimensionalityPermission);
            }
        }
        for(TYjyDimensionality item:list)
        {
            tYjyDimensionalityService.deleteTYjyDimensionalityById(item.getId());
            TYjyForm tYjyForm=new TYjyForm();
            tYjyForm.setDimensionalityId(item.getId());
            List<TYjyForm> dellist = tYjyFormService.selectTYjyFormList(tYjyForm);
            tYjyFormService.deleteTYjyFormBydimensionalityId(item.getId());
            for(TYjyForm item1:dellist)
            {
                TYjyPermission tYjyPermission=new TYjyPermission();
                tYjyPermission.setFormId(item1.getId());
                tYjyPermissionService.deleteTYjyPermissionByFormId(tYjyPermission);
            }
        }
        return toAjax(1);
    }
    /**
     * 删除TYjyDimensionality
     */
    @Log(title = "TYjyDimensionality", businessType = BusinessType.DELETE)
	@DeleteMapping("/delete/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        for(Long id :ids)
        {
            TYjyDimensionality tYjyDimensionality=new TYjyDimensionality();
            tYjyDimensionality.setId(id);
            List<TYjyDimensionality> list = tYjyDimensionalityService.getChildremById(tYjyDimensionality);
            TYjyDimensionality re = tYjyDimensionalityService.selectTYjyDimensionalityById(id);
            if(re.getParentId()==null)
            {
                if(re.getDeptCode()!=null)
                {
                    TYjyDimensionalityPermission tYjyDimensionalityPermission = new TYjyDimensionalityPermission();
                    tYjyDimensionalityPermission.setDimensionalityId(re.getId());
                    tYjyDimensionalityPermission.setPath(re.getDeptCode());
                    tYjyDimensionalityPermission.setDimensionalityName(re.getDimensionalityName());
                    tYjyDimensionalityPermissionService.deleteDimenPermissionForDimenPermission(tYjyDimensionalityPermission);
                }
            }
            for(TYjyDimensionality item:list)
            {
                tYjyDimensionalityService.deleteTYjyDimensionalityById(item.getId());
                TYjyForm tYjyForm=new TYjyForm();
                tYjyForm.setDimensionalityId(item.getId());
                List<TYjyForm> dellist = tYjyFormService.selectTYjyFormList(tYjyForm);
                tYjyFormService.deleteTYjyFormBydimensionalityId(item.getId());
                for(TYjyForm item1:dellist)
                {
                    TYjyPermission tYjyPermission=new TYjyPermission();
                    tYjyPermission.setFormId(item1.getId());
                    tYjyPermissionService.deleteTYjyPermissionByFormId(tYjyPermission);
                }
            }
        }
        return toAjax(1);
//        return toAjax(tYjyDimensionalityService.deleteTYjyDimensionalityByIds(ids));
    }


    public TYjyDimensionalityRoot getALLRoot(TYjyDimensionality tYjyDimensionality)
    {
        List<TYjyDimensionality> list = tYjyDimensionalityService.getByRootId(tYjyDimensionality);
        TYjyDimensionalityRoot root=new TYjyDimensionalityRoot();
        root.setValue(tYjyDimensionality.getId());
        root.setLabel(tYjyDimensionality.getDimensionalityName());
        root.setIsUse(tYjyDimensionality.getIsUse());
        Map<Long,List<TYjyDimensionalityRoot>> map=new HashMap<>();
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            if(map.containsKey(item.getParentId()))
            {
                map.get(item.getParentId()).add(add);
            }
            else
            {
                List<TYjyDimensionalityRoot> insert=new ArrayList<>();
                insert.add(add);
                map.put(item.getParentId(),insert);
            }
        }
        return tYjyDimensionalityService.getTree(root,map);
    }


    public int isexit(TYjyDimensionality tYjyDimensionality)
    {
        TYjyDimensionality search=new TYjyDimensionality();
        search.setParentId(tYjyDimensionality.getParentId());
        search.setDimensionalityName(tYjyDimensionality.getDimensionalityName());
        search.setDelFlag("0");
        return tYjyDimensionalityService.selectTYjyDimensionalityListIsExit(search).size();
    }



    //在新增部门配置后的代码

    //根据部门权限获取所有的相关维度根节点，不进行任何处理
    @GetMapping("/rootListWithDept")
    public TableDataInfo getrootListWithDept(TYjyDimensionality tYjyDimensionality)
    {
//        String workNo=SecurityUtils.getUsername();
//        TYjyDeptUser tYjyDeptUser=new TYjyDeptUser();
//        tYjyDeptUser.setWorkNo(workNo);
//        tYjyDeptUser.setRuleType("100");
//        List<TYjyDeptUser> searchrole = tYjyDeptUserService.selectTYjyDeptUserList(tYjyDeptUser);
//        if(searchrole.size()==0)
//        {
//            tYjyDimensionality.setCreateBy(workNo);
//        }

        String workNo=SecurityUtils.getUsername();
        tYjyDimensionality.setCreateBy(workNo);
        startPage();
        List<TYjyDimensionality> list = tYjyDimensionalityService.selectRootListWithDept(tYjyDimensionality);
        return getDataTable(list);
    }


    @GetMapping("/getListWithDept")
    public TableDataInfo getListWithDept(TYjyDimensionality tYjyDimensionality)
    {
        tYjyDimensionality.setCreateBy(SecurityUtils.getUsername());
        List<TYjyDimensionality> re=new ArrayList<>();
        List<TYjyDimensionality> search;
        List<TYjyDimensionality> list = tYjyDimensionalityService.selectRootListWithDept(tYjyDimensionality);
        for(TYjyDimensionality item:list)
        {
            search = tYjyDimensionalityService.getByRootId(item);
            Map<Long,List<TYjyDimensionality>> map=new HashMap<>();
            for(TYjyDimensionality item1:search)
            {
                if(map.containsKey(item1.getParentId()))
                {
                    map.get(item1.getParentId()).add(item1);
                }
                else
                {
                    List<TYjyDimensionality> insert=new ArrayList<>();
                    insert.add(item1);
                    map.put(item1.getParentId(),insert);
                }
            }
            re.add(tYjyDimensionalityService.getALlTree(item,map));
        }
        return getDataTable(re);
    }


    @GetMapping("/getAllListWithDept")
    public AjaxResult getAllListWithDept(TYjyDimensionality tYjyDimensionality)
    {
        String workNo=SecurityUtils.getUsername();
//        TYjyDeptUser tYjyDeptUser=new TYjyDeptUser();
//        tYjyDeptUser.setWorkNo(workNo);
//        tYjyDeptUser.setRuleType("100");
//        List<TYjyDeptUser> searchrole = tYjyDeptUserService.selectTYjyDeptUserList(tYjyDeptUser);
//        if(searchrole.size()==0)
//        {
//            tYjyDimensionality.setCreateBy(workNo);
//        }
        tYjyDimensionality.setCreateBy(workNo);
        List<TYjyDimensionalityRoot> re=new ArrayList<>();
        List<TYjyDimensionality> list = tYjyDimensionalityService.selectRootListWithDept(tYjyDimensionality);
        for(TYjyDimensionality item:list)
        {
            re.add(getALLRoot(item));
        }
        return AjaxResult.success(re);
    }


    @GetMapping("/getDimensionalityfordistribute")
    public AjaxResult getDimensionalityfordistribute(TYjyDimensionality tYjyDimensionality)
    {
        String workNo=SecurityUtils.getUsername();
        tYjyDimensionality.setCreateBy(workNo);
        List<TYjyDimensionality> list = tYjyDimensionalityService.getALLRootFordistribute(tYjyDimensionality);
        List<TYjyDimensionalityRoot> re=new ArrayList<>();
        Map<Long,TYjyDimensionalityRoot> root=new HashMap<>();
        Map<Long,Map<Long,List<TYjyDimensionalityRoot>>> map=new HashMap<>();
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            if(item.getParentId()==null)
            {
                root.put(item.getId(),add);
                Map<Long,List<TYjyDimensionalityRoot>> rootList=new HashMap<>();
                map.put(item.getId(),rootList);
            }
        }
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            if(item.getParentId()!=null)
            {
                if(map.get(Long.valueOf(item.getPath().split(",")[0])).containsKey(item.getParentId()))
                {
                    map.get(Long.valueOf(item.getPath().split(",")[0])).get(item.getParentId()).add(add);
                }
                else
                {
                    List<TYjyDimensionalityRoot> insert=new ArrayList<>();
                    insert.add(add);
                    map.get(Long.valueOf(item.getPath().split(",")[0])).put(item.getParentId(),insert);
                }
            }
        }
        for(Long item:root.keySet())
        {
            re.add(tYjyDimensionalityService.getTree(root.get(item),map.get(item)));
        }
        return AjaxResult.success(re);
    }



    @GetMapping("/getDimensionalityforanswer")
    public AjaxResult getDimensionalityforanswer(TYjyDimensionality tYjyDimensionality)
    {
        String workNo=SecurityUtils.getUsername();
        tYjyDimensionality.setCreateBy(workNo);
        List<TYjyDimensionality> list = tYjyDimensionalityService.getALLRootForAnswer(tYjyDimensionality);
        List<TYjyDimensionalityRoot> re=new ArrayList<>();
        Map<Long,TYjyDimensionalityRoot> root=new HashMap<>();
        Map<Long,Map<Long,List<TYjyDimensionalityRoot>>> map=new HashMap<>();
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            add.setDeptName(item.getDeptName());
            add.setDeptCode(item.getDeptCode());
            if(item.getParentId()==null)
            {
                root.put(item.getId(),add);
                Map<Long,List<TYjyDimensionalityRoot>> rootList=new HashMap<>();
                map.put(item.getId(),rootList);
            }
        }
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            add.setDeptName(item.getDeptName());
            add.setDeptCode(item.getDeptCode());
            if(item.getParentId()!=null)
            {
                if(map.get(Long.valueOf(item.getPath().split(",")[0])).containsKey(item.getParentId()))
                {
                    map.get(Long.valueOf(item.getPath().split(",")[0])).get(item.getParentId()).add(add);
                }
                else
                {
                    List<TYjyDimensionalityRoot> insert=new ArrayList<>();
                    insert.add(add);
                    map.get(Long.valueOf(item.getPath().split(",")[0])).put(item.getParentId(),insert);
                }
            }
        }
        for(Long item:root.keySet())
        {
            re.add(tYjyDimensionalityService.getTree(root.get(item),map.get(item)));
        }
        return AjaxResult.success(re);
    }

    /**
     *  如何指定改维度包含的权限，需要进一步的思考
     */
    @Log(title = "TYjyDimensionality", businessType = BusinessType.INSERT)
    @PostMapping("/addWithDept")
    public AjaxResult addWithDept(@RequestBody TYjyDimensionality tYjyDimensionality)
    {

        if(isexit(tYjyDimensionality)>0)
        {
            return AjaxResult.error("在同一父维度下，已存在相同名称的维度");
        }
        if(tYjyDimensionality.getParentId()==null)
        {
            tYjyDimensionality.setPath("");
        }
        else
        {
            TYjyDimensionality parent =tYjyDimensionalityService.selectTYjyDimensionalityById(tYjyDimensionality.getParentId());
            tYjyDimensionality.setPath(parent.getPath());
        }
        tYjyDimensionality.setCreateBy(SecurityUtils.getUsername());
        int re=tYjyDimensionalityService.insertTYjyDimensionality(tYjyDimensionality);
        TYjyDimensionality search=new TYjyDimensionality();
        search.setDimensionalityName(tYjyDimensionality.getDimensionalityName());
        search.setParentId(tYjyDimensionality.getParentId());
        search=tYjyDimensionalityService.selectTYjyDimensionalityList(search).get(0);
        TYjyDimensionality update=new TYjyDimensionality();
        update.setId(search.getId());
        update.setPath(search.getPath()+search.getId()+",");
        tYjyDimensionalityService.updateTYjyDimensionality(update);
        return toAjax(re);
    }



    @PostMapping("/exportStatistics")
    public void exportStatistics(HttpServletResponse response, String startDate,String endDate,Long rootId) throws IOException {
        tYjyDimensionalityService.export(response, startDate,endDate,rootId);
    }



    @GetMapping("/getStatusListWithadmin")
    public JSONObject getListWithStatus(TYjyDimensionality tYjyDimensionality)
    {
        String workNo=SecurityUtils.getUsername();
        tYjyDimensionality.setCreateBy(workNo);
        startPage();
        List<TYjyDimensionality> list = tYjyDimensionalityService.selectRootListWithDeptSuper(tYjyDimensionality);
        JSONObject jsonResult=new JSONObject();
        jsonResult.put("total",getDataTable(list).getTotal());
        jsonResult.put("rows",tYjyDimensionalityService.dealLsitWithStatusPlus(list,tYjyDimensionality.getFcDate()));
        return jsonResult;
    }

    @GetMapping("/getStatusListWithadminForCount")
    public JSONObject getStatusListWithadminForCount(TYjyDimensionality tYjyDimensionality)
    {
        String workNo=SecurityUtils.getUsername();
        tYjyDimensionality.setCreateBy(workNo);
        startPage();
        List<TYjyDimensionality> list = tYjyDimensionalityService.selectRootListWithDeptSuper(tYjyDimensionality);
        JSONObject jsonResult=new JSONObject();
        jsonResult.put("total",getDataTable(list).getTotal());

        List<JSONObject> rows=tYjyDimensionalityService.dealLsitWithsubmitPlus(list,tYjyDimensionality.getFcDate(),workNo);
//        jsonResult.put("rows",rows);
        jsonResult.put("size",rows.size());
        Integer sumNum=0;
        Integer finishNum=0;
        for(JSONObject item:rows)
        {
            sumNum=sumNum+Integer.valueOf(item.get("shouldCount").toString());
            finishNum=finishNum+Integer.valueOf(item.get("count").toString());
        }
        jsonResult.put("sumNum",sumNum);
        if(sumNum!=0)
        {
            BigDecimal decimal = new BigDecimal((100*(Float.valueOf(finishNum.toString())/Float.valueOf(sumNum.toString()))));
            BigDecimal roundedDecimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
            jsonResult.put("countRate",Float.valueOf(roundedDecimal.floatValue()).toString()+"%");
        }
        else
        {
            jsonResult.put("countRate","0");
        }
        return jsonResult;
    }

    @GetMapping("/getFormStatusListWithadmin")
    public JSONObject getFormStatusListWithadmin(String path,String fcDate,String frequency,String status)
    {
        startPage();
        List<TYjyForm> searchList=tYjyDimensionalityService.getFormStatusListWithadmin(path,fcDate,frequency,status);
        JSONObject jsonResult=new JSONObject();
        jsonResult.put("total",getDataTable(searchList).getTotal());
        List<JSONObject> array=new ArrayList<>();
        Map<String,Map<String,String>> dimensionalityMap=new HashMap<>();
        for(TYjyForm item:searchList)
        {
            JSONObject jsonDeal=new JSONObject();//还需要进一步的完善
            jsonDeal.put("id",item.getId());
//            jsonDeal.put("dimensionalityName",tYjyFormService.searchNames(item.getDimensionalityId()));
            jsonDeal.put("dimensionalityName",tYjyFormService.searchNames(item.getDimensionalityPath(),dimensionalityMap));
            jsonDeal.put("formQuestion",item.getFormQuestion());
            jsonDeal.put("formValue",item.getFormValue());
            jsonDeal.put("creatorNo",item.getCreatorNo());
            jsonResult.put("creatorName",item.getCreatorName());
            if(item.getStatus()==null)
            {
                jsonDeal.put("status","4");
            }
            else
            {
                jsonDeal.put("status",item.getStatus());
            }

            array.add(jsonDeal);
        }
        jsonResult.put("rows",array);
        return jsonResult;
//        return AjaxResult.success(tYjyDimensionalityService.getFormStatusListWithadmin(path,fcDate,frequency));
    }

    @GetMapping("/getStatusListWithsubmit")
    public JSONObject getStatusListWithsubmit(TYjyDimensionality tYjyDimensionality)
    {
        String workNo=SecurityUtils.getUsername();
        tYjyDimensionality.setCreateBy(workNo);
        startPage();
        List<TYjyDimensionality> list = tYjyDimensionalityService.getALLparentRootFordistributePlus(tYjyDimensionality);
        JSONObject jsonResult=new JSONObject();
        jsonResult.put("total",getDataTable(list).getTotal());
        jsonResult.put("rows",tYjyDimensionalityService.dealLsitWithsubmitPlus(list,tYjyDimensionality.getFcDate(),workNo));
        return jsonResult;
    }

    @GetMapping("/getStatusListWithsubmitForCount")
    public JSONObject getStatusListWithsubmitForCount(TYjyDimensionality tYjyDimensionality)
    {
        String workNo=SecurityUtils.getUsername();
        tYjyDimensionality.setCreateBy(workNo);
//        startPage();
        List<TYjyDimensionality> list = tYjyDimensionalityService.getALLparentRootFordistributePlus(tYjyDimensionality);
        JSONObject jsonResult=new JSONObject();
        jsonResult.put("total",getDataTable(list).getTotal());
        List<JSONObject> rows=tYjyDimensionalityService.dealLsitWithsubmitPlus(list,tYjyDimensionality.getFcDate(),workNo);
//        jsonResult.put("rows",rows);
        jsonResult.put("size",rows.size());
        Integer sumNum=0;
        Integer finishNum=0;
        for(JSONObject item:rows)
        {
            sumNum=sumNum+Integer.valueOf(item.get("shouldCount").toString());
            finishNum=finishNum+Integer.valueOf(item.get("count").toString());
        }
        jsonResult.put("sumNum",sumNum);
//        BigDecimal decimal = new BigDecimal((100*(Float.valueOf(finishNum.toString())/Float.valueOf(sumNum.toString()))));
//        BigDecimal roundedDecimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
//        jsonResult.put("countRate",Float.valueOf(roundedDecimal.floatValue()).toString()+"%");
        if(sumNum!=0)
        {
            BigDecimal decimal = new BigDecimal((100*(Float.valueOf(finishNum.toString())/Float.valueOf(sumNum.toString()))));
            BigDecimal roundedDecimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
            jsonResult.put("countRate",Float.valueOf(roundedDecimal.floatValue()).toString()+"%");
        }
        else
        {
            jsonResult.put("countRate","0");
        }
        return jsonResult;
    }

    @GetMapping("/getFormStatusListWithsubmit")
    public JSONObject getFormStatusListWithsubmit(String path,String fcDate,String frequency,String status)
    {
        String workNo=SecurityUtils.getUsername();
        startPage();
        List<TYjyForm> searchList=tYjyDimensionalityService.getFormStatusListWithsumbit(path,fcDate,frequency,status,workNo);
        JSONObject jsonResult=new JSONObject();
        jsonResult.put("total",getDataTable(searchList).getTotal());
        List<JSONObject> array=new ArrayList<>();
        Map<String,Map<String,String>> dimensionalityMap=new HashMap<>();
        for(TYjyForm item:searchList)
        {
            JSONObject jsonDeal=new JSONObject();//还需要进一步的完善
            jsonDeal.put("id",item.getId());
//            jsonDeal.put("dimensionalityName",tYjyFormService.searchNames(item.getDimensionalityId()));
            jsonDeal.put("dimensionalityName",tYjyFormService.searchNames(item.getDimensionalityPath(),dimensionalityMap));
            jsonDeal.put("formQuestion",item.getFormQuestion());
            jsonDeal.put("formValue",item.getFormValue());
            jsonDeal.put("creatorNo",item.getCreatorNo());
            jsonResult.put("creatorName",item.getCreatorName());
            if(item.getStatus()==null)
            {
                jsonDeal.put("status","4");
            }
            else
            {
                jsonDeal.put("status",item.getStatus());
            }

            array.add(jsonDeal);
        }
        jsonResult.put("rows",array);
        return jsonResult;
//        return AjaxResult.success(tYjyDimensionalityService.getFormStatusListWithadmin(path,fcDate,frequency));
    }

    @GetMapping("/getDeadlineDimensionality")
    public JSONObject getDeadlineDimensionality(TYjyDimensionality tYjyDimensionality)
    {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if(tYjyDimensionality.getFcDate()==null)
        {
            Date currentDate = new Date();
            calendar.setTime(currentDate);
            tYjyDimensionality.setFcDate(dateFormat.format(calendar.getTime()));
        }
        List<TYjyDimensionality> searchList=tYjyDimensionalityService.selectDeadlineForDimensionality(tYjyDimensionality);
        JSONObject jsonResult=new JSONObject();
        jsonResult.put("total",getDataTable(searchList).getTotal());
        List<JSONObject> array=new ArrayList<>();
        for(TYjyDimensionality item:searchList)
        {
            JSONObject jsonDeal=new JSONObject();//还需要进一步的完善
            jsonDeal.put("id",item.getId());
            jsonDeal.put("completionDate",item.getDeadlineTime());
            jsonDeal.put("name",item.getDimensionalityName());
            array.add(jsonDeal);
        }
        jsonResult.put("rows",array);
        return jsonResult;
    }

    @GetMapping("/getDeadlineDimensionalityForUser")
    public JSONObject getDeadlineDimensionalityForUser(TYjyDimensionality tYjyDimensionality)
    {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if(tYjyDimensionality.getFcDate()==null)
        {
            Date currentDate = new Date();
            calendar.setTime(currentDate);
            tYjyDimensionality.setFcDate(dateFormat.format(calendar.getTime()));
        }
        tYjyDimensionality.setCreateBy(SecurityUtils.getUsername());
        List<TYjyDimensionality> searchList=tYjyDimensionalityService.selectDeadlineForDimensionalityForUser(tYjyDimensionality);
        JSONObject jsonResult=new JSONObject();
        jsonResult.put("total",getDataTable(searchList).getTotal());
        List<JSONObject> array=new ArrayList<>();
        for(TYjyDimensionality item:searchList)
        {
            JSONObject jsonDeal=new JSONObject();//还需要进一步的完善
            jsonDeal.put("id",item.getId());
            jsonDeal.put("completionDate",item.getDeadlineTime());
            jsonDeal.put("name",item.getDimensionalityName());
            array.add(jsonDeal);
        }
        jsonResult.put("rows",array);
        return jsonResult;
    }


    @GetMapping("/getDeadlineDimensionalityForAdmin")
    public JSONObject getDeadlineDimensionalityForAdmin(TYjyDimensionality tYjyDimensionality)
    {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if(tYjyDimensionality.getFcDate()==null)
        {
            Date currentDate = new Date();
            calendar.setTime(currentDate);
            tYjyDimensionality.setFcDate(dateFormat.format(calendar.getTime()));
        }
        tYjyDimensionality.setCreateBy(SecurityUtils.getUsername());
        List<TYjyDimensionality> searchList=tYjyDimensionalityService.selectDeadlineForDimensionalityForAdmin(tYjyDimensionality);
        JSONObject jsonResult=new JSONObject();
        jsonResult.put("total",getDataTable(searchList).getTotal());
        List<JSONObject> array=new ArrayList<>();
        for(TYjyDimensionality item:searchList)
        {
            JSONObject jsonDeal=new JSONObject();//还需要进一步的完善
            jsonDeal.put("id",item.getId());
            jsonDeal.put("completionDate",item.getDeadlineTime());
            jsonDeal.put("name",item.getDimensionalityName());
            array.add(jsonDeal);
        }
        jsonResult.put("rows",array);
        return jsonResult;
    }


    @GetMapping("/dimensionalitylistPermissionList")
    public AjaxResult dimensionalitylistPermissionList(TYjyDimensionality tYjyDimensionality)
    {
        String workNo=SecurityUtils.getUsername();
        List<String> permissionList = appCommonMapper.getPermissionList(workNo);
        if(permissionList.contains("dimensionalitylistKanban"))
        {
            return AjaxResult.success("1");
        }
        else
        {
            return AjaxResult.success("0");
        }

    }


}
