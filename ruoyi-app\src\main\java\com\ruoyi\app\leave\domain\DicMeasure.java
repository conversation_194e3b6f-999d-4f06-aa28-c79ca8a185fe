package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * IC卡信息对象 d_ic_t
 * 
 * <AUTHOR>
 */
@Data
public class DicMeasure extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 有效标志 */
    @Excel(name = "有效标志")
    private Long validflag;

    /** 类型 */
    @Excel(name = "类型")
    private Long type;

    /** IC卡号 */
    @Excel(name = "IC卡号")
    private String icNo;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carno;

    /** 司机 */
    @Excel(name = "司机")
    private String driver;

    /** 司机编码 */
    @Excel(name = "司机编码")
    private String drivercode;

    /** 发卡人 */
    @Excel(name = "发卡人")
    private String fromman;

    /** 发卡人编码 */
    @Excel(name = "发卡人编码")
    private String frommancode;

    /** 发卡日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发卡日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date fromdata;

    /** 押金 */
    @Excel(name = "押金")
    private BigDecimal deposit;

    /** 退卡人 */
    @Excel(name = "退卡人")
    private String backman;

    /** 退卡人编码 */
    @Excel(name = "退卡人编码")
    private String backmancode;

    /** 退卡日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退卡日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date backdate;

    /** 单位编码 */
    @Excel(name = "单位编码")
    private String unitcode;

    /** 单位名称 */
    @Excel(name = "单位名称")
    private String unitname;

    /** 单位时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "单位时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date unittime;

    /** 单位人员 */
    @Excel(name = "单位人员")
    private String unitman;

    /** 运输商编码 */
    @Excel(name = "运输商编码")
    private String tcode;

    /** 运输商名称 */
    @Excel(name = "运输商名称")
    private String tname;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createman;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdate;

    /** 操作备注 */
    @Excel(name = "操作备注")
    private String operamemo;

    /** 卡等级 */
    @Excel(name = "卡等级")
    private Long cardLevel;

    /** IC卡模式 */
    @Excel(name = "IC卡模式")
    private Long icmode;

    /** 客户编码 */
    @Excel(name = "客户编码")
    private String customercode;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customername;

    /** 更新时间戳 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @Excel(name = "更新时间戳", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date uptimestamp;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uptime;

    /** 卡状态 */
    @Excel(name = "卡状态")
    private String cardState;

    /** 操作类型 */
    @Excel(name = "操作类型")
    private Long operatype;

    /** 匹配ID */
    @Excel(name = "匹配ID")
    private String matchid;

    /** 计划ID */
    @Excel(name = "计划ID")
    private String planid;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderno;

    /** 任务编码 */
    @Excel(name = "任务编码")
    private String taskcode;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialcode;

    /** 物料规格编码 */
    @Excel(name = "物料规格编码")
    private String materialspeccode;

    /** 船编码 */
    @Excel(name = "船编码")
    private String shipcode;

    /** 来源编码 */
    @Excel(name = "来源编码")
    private String sourcecode;

    /** 来源时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "来源时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sourcetime;

    /** 目标编码 */
    @Excel(name = "目标编码")
    private String targetcode;

    /** 目标时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "目标时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date targettime;

    /** 毛重 */
    @Excel(name = "毛重")
    private BigDecimal gross;

    /** 毛重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "毛重时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date grosstime;

    /** 毛重称重ID */
    @Excel(name = "毛重称重ID")
    private String grossweighid;

    /** 毛重称重 */
    @Excel(name = "毛重称重")
    private String grossweigh;

    /** 毛重操作员ID */
    @Excel(name = "毛重操作员ID")
    private String grossoperatorid;

    /** 毛重操作员 */
    @Excel(name = "毛重操作员")
    private String grossoperator;

    /** 皮重 */
    @Excel(name = "皮重")
    private BigDecimal tare;

    /** 皮重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "皮重时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date taretime;

    /** 皮重称重ID */
    @Excel(name = "皮重称重ID")
    private String tareweighid;

    /** 皮重称重 */
    @Excel(name = "皮重称重")
    private String tareweigh;

    /** 皮重操作员ID */
    @Excel(name = "皮重操作员ID")
    private String tareoperatorid;

    /** 皮重操作员 */
    @Excel(name = "皮重操作员")
    private String tareoperator;

    /** 扣重 */
    @Excel(name = "扣重")
    private BigDecimal deduction;

    /** 扣重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "扣重时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deductiontime;

    /** 扣重单位 */
    @Excel(name = "扣重单位")
    private String deductionunit;

    /** 入场门 */
    @Excel(name = "入场门")
    private String entergate;

    /** 数量 */
    @Excel(name = "数量")
    private String mcount;

    /** 回皮毛重 */
    @Excel(name = "回皮毛重")
    private BigDecimal bgross;

    /** 回皮皮重 */
    @Excel(name = "回皮皮重")
    private BigDecimal btare;

    /** 回皮毛重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "回皮毛重时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bgrosstime;

    /** 回皮毛重称重ID */
    @Excel(name = "回皮毛重称重ID")
    private String bgrossweighid;

    /** 回皮毛重称重 */
    @Excel(name = "回皮毛重称重")
    private String bgrossweigh;

    /** 回皮皮重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "回皮皮重时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date btaretime;

    /** 回皮皮重称重ID */
    @Excel(name = "回皮皮重称重ID")
    private String btareweighid;

    /** 回皮皮重称重 */
    @Excel(name = "回皮皮重称重")
    private String btareweigh;

    /** 回皮匹配ID */
    @Excel(name = "回皮匹配ID")
    private String bmatchid;

    /** 回皮归档编码 */
    @Excel(name = "回皮归档编码")
    private String barchcode;

    /** 删除标志 */
    @Excel(name = "删除标志")
    private String dflag;

    /** 回皮标志 */
    @Excel(name = "回皮标志")
    private String bflag;

    /** 发国标标志 */
    @Excel(name = "发国标标志")
    private String fgbflag;

    /** 发国标价格标志 */
    @Excel(name = "发国标价格标志")
    private String fgpriceflag;

    /** 扣重3 */
    @Excel(name = "扣重3")
    private BigDecimal deduction3;

    /** 扣重4 */
    @Excel(name = "扣重4")
    private BigDecimal deduction4;

    /** 扣重2 */
    @Excel(name = "扣重2")
    private BigDecimal deduction2;

    /** 扣重1 */
    @Excel(name = "扣重1")
    private BigDecimal deduction1;

    /** 扣重5 */
    @Excel(name = "扣重5")
    private BigDecimal deduction5;

    /** 库房 */
    @Excel(name = "库房")
    private String storeroom;

    /** 有效人 */
    @Excel(name = "有效人")
    private String validman;

    /** 有效人2 */
    @Excel(name = "有效人2")
    private String validman2;

    /** 物料标志 */
    @Excel(name = "物料标志")
    private String mflag;

    /** RFID ID */
    @Excel(name = "RFID ID")
    private Long rfidId;

    /** RFID编号 */
    @Excel(name = "RFID编号")
    private String rfidNo;

    /** 入场时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入场时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entertime;

    /** 发国标价格1 */
    @Excel(name = "发国标价格1")
    private BigDecimal fgprice1;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialname;

    /** 物料规格 */
    @Excel(name = "物料规格")
    private String materialspec;

    /** 船 */
    @Excel(name = "船")
    private String ship;

    /** 来源名称 */
    @Excel(name = "来源名称")
    private String sourcename;

    /** 目标名称 */
    @Excel(name = "目标名称")
    private String targetname;

    /** 发国标价格2 */
    @Excel(name = "发国标价格2")
    private BigDecimal fgprice2;

    /** 发国标签价日志ID */
    @Excel(name = "发国标签价日志ID")
    private String fgsignpricelogid;

    /** 发国标照片 */
    @Excel(name = "发国标照片")
    private String fgphoto;

    /** 审核标志 */
    @Excel(name = "审核标志")
    private String shflag;

    /** 皮重小时 */
    @Excel(name = "皮重小时")
    private Long tarehour;

    /** 计划数量 */
    @Excel(name = "计划数量")
    private BigDecimal plancount;

    /** 皮重日志ID */
    @Excel(name = "皮重日志ID")
    private String tarelogid;

    /** 毛重日志ID */
    @Excel(name = "毛重日志ID")
    private String grosslogid;

    /** 回皮毛重日志ID */
    @Excel(name = "回皮毛重日志ID")
    private String bgrosslogid;

    /** 回皮皮重日志ID */
    @Excel(name = "回皮皮重日志ID")
    private String btarelogid;

    /** 物料数量备注 */
    @Excel(name = "物料数量备注")
    private String msrmemo;

    /** 物料数量 */
    @Excel(name = "物料数量")
    private Long materialcount;

    /** 物料编号 */
    @Excel(name = "物料编号")
    private String matno;

    /** 报备标志 */
    @Excel(name = "报备标志")
    private Long bbflag;

    /** 报备人 */
    @Excel(name = "报备人")
    private String bbman;

    /** 直供计划ID */
    @Excel(name = "直供计划ID")
    private String zgplanid;

    /** 直供标志 */
    @Excel(name = "直供标志")
    private String zgflag;

    /** 直供目标时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "直供目标时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date zgtargettime;

    /** 回皮皮重操作员ID */
    @Excel(name = "回皮皮重操作员ID")
    private String btareoperatorid;

    /** 回皮皮重操作员 */
    @Excel(name = "回皮皮重操作员")
    private String btareoperator;

    /** 回皮毛重操作员ID */
    @Excel(name = "回皮毛重操作员ID")
    private String bgrossoperatorid;

    /** 回皮毛重操作员 */
    @Excel(name = "回皮毛重操作员")
    private String bgrossoperator;

    /** 走数 */
    @Excel(name = "走数")
    private String zoushu;

    /** 订单ID */
    @Excel(name = "订单ID")
    private String orderld;

    /** 发国标类型 */
    @Excel(name = "发国标类型")
    private String fgtype;

    /** 发国标成本类型 */
    @Excel(name = "发国标成本类型")
    private String fgcbtype;

    // getter和setter方法
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setValidflag(Long validflag) 
    {
        this.validflag = validflag;
    }

    public Long getValidflag() 
    {
        return validflag;
    }

    public Long getType() 
    {
        return type;
    }

    public void setType(Long type) 
    {
        this.type = type;
    }

    public String getIcNo() 
    {
        return icNo;
    }

    public void setIcNo(String icNo) 
    {
        this.icNo = icNo;
    }

    public String getCarno() 
    {
        return carno;
    }

    public void setCarno(String carno) 
    {
        this.carno = carno;
    }

    // ... 其他字段的getter和setter方法 ...

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("validflag", getValidflag())
            .append("type", getType())
            .append("icNo", getIcNo())
            .append("carno", getCarno())
            // ... 其他字段的toString ...
            .toString();
    }
} 