package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LPassT;

/**
 * 不返回主Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface ILPassTService 
{
    /**
     * 查询不返回主
     * 
     * @param id 不返回主ID
     * @return 不返回主
     */
    public LPassT selectLPassTById(Long id);

    /**
     * 查询不返回主列表
     * 
     * @param lPassT 不返回主
     * @return 不返回主集合
     */
    public List<LPassT> selectLPassTList(LPassT lPassT);

    /**
     * 新增不返回主
     * 
     * @param lPassT 不返回主
     * @return 结果
     */
    public int insertLPassT(LPassT lPassT);

    /**
     * 修改不返回主
     * 
     * @param lPassT 不返回主
     * @return 结果
     */
    public int updateLPassT(LPassT lPassT);

    /**
     * 批量删除不返回主
     * 
     * @param ids 需要删除的不返回主ID
     * @return 结果
     */
    public int deleteLPassTByIds(Long[] ids);

    /**
     * 删除不返回主信息
     * 
     * @param id 不返回主ID
     * @return 结果
     */
    public int deleteLPassTById(Long id);
}
