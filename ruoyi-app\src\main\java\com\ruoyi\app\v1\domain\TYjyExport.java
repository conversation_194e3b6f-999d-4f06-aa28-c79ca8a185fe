package com.ruoyi.app.v1.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * TYjyDimensionality对象 t_yjy_dimensionality
 * 
 * <AUTHOR>
 * @date 2024-10-21
 */
@Data
public class TYjyExport extends BaseEntity
{
    private Long formId;
    private Long dimensionalityId;
    private String path;
    private String question;
    private Double maximum;
    private Double minimum;
    private String frequency;
    private String formValue;
    private String formFile;
    private String fcDate;
    private String dateDesc;
    private String reason;
    private String measure;
    private List<String> labels;
    private String note;
    private String unit;

}
