package com.ruoyi.app.leave.enums;

/**
 * 是否复审
 * 1.是
 * 2.否
 */
public enum LeaveSecApproveLogEnum {
    YES(1, "是"),
    NO(2, "否");

    private final int code;
    private final String info;

    LeaveSecApproveLogEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {return code;}

    public String getInfo() {return info;}

    public static LeaveSecApproveLogEnum getByCode(int code) {
        for (LeaveSecApproveLogEnum leaveSecApproveLogEnum : LeaveSecApproveLogEnum.values()) {
            if (leaveSecApproveLogEnum.getCode() == code){
                return leaveSecApproveLogEnum;
            }
        }
        return null;
    }
}
