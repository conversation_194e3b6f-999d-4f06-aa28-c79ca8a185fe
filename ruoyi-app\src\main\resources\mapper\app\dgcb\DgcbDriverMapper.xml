<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.dgcb.mapper.DgcbDriverMapper">

    <resultMap type="DgcbDriver" id="DgcbDriverResult">
        <result property="id"    column="id"    />
        <result property="driverName"    column="driver_name"    />
        <result property="sex"    column="sex"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="idCardNo"    column="id_card_no"    />
        <result property="carNum"    column="car_num"    />
        <result property="carNumColor"    column="car_num_color"    />
        <result property="vehicleEmissionStandards"    column="vehicle_emission_standards"    />
        <result property="faceImg"    column="face_img"    />
        <result property="drivingLicenseImg"    column="driving_license_img"    />
        <result property="driverLicenseImg"    column="driver_license_img"    />
        <result property="companyName"    column="company_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <resultMap  type="XctgDriverUser" id="XctgDriverUserResult">
        <id column="id" property="id"/>
        <result column="open_id" property="openId"/>
        <result column="name" property="name"/>
        <result column="gender" property="gender"/>
        <result column="id_card" property="idCard"/>
        <result column="company" property="company"/>
        <result column="phone" property="phone"/>
        <result column="photo" property="photo"/>
        <result column="driver_license_imgs" property="driverLicenseImgs"/>
        <result column="vehicle_license_imgs" property="vehicleLicenseImgs"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>

    <resultMap  type="XctgDriverCar" id="XctgDriverCarResult">
        <id column="id" property="id"/>
        <result column="open_id" property="openId"/>
        <result column="car_number" property="carNumber"/>
        <result column="vehicle_license_imgs" property="vehicleLicenseImgs"/>
        <result column="license_plate_color" property="licensePlateColor"/>
        <result column="car_id" property="carId"/>
        <result column="trailer_number" property="trailerNumber"/>
        <result column="trailer_id" property="trailerId"/>
        <result column="axis_type" property="axisType"/>
        <result column="driver_weight" property="driverWeight"/>
        <result column="max_weight" property="maxWeight"/>
        <result column="vehicle_emission_standards" property="vehicleEmissionStandards"/>
        <result column="engine_number" property="engineNumber"/>
        <result column="vin_number" property="vinNumber"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>

    <sql id="selectDgcbDriverVo">
        select id, driver_name, sex, mobile_phone, id_card_no, car_num, car_num_color, vehicle_emission_standards, face_img, driving_license_img, driver_license_img, company_name, create_time, create_by, update_time, update_by from dgcb_driver
    </sql>

    <sql id="selectXctgDriverUser">
        select id, open_id, name, gender, id_card, company, phone, photo, driver_license_imgs, vehicle_license_imgs, create_time, update_time, delete_time from xctg_driver_user
    </sql>

    <select id="selectDgcbDriverList" parameterType="DgcbDriver" resultMap="DgcbDriverResult">
        <include refid="selectDgcbDriverVo"/>
        <where>
            <if test="driverName != null  and driverName != ''"> and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone like concat('%', #{mobilePhone}, '%')</if>
            <if test="idCardNo != null  and idCardNo != ''"> and id_card_no = #{idCardNo}</if>
            <if test="carNum != null  and carNum != ''"> and car_num = #{carNum}</if>
             <if test="carNumColor != null "> and car_num_color = #{carNumColor}</if>
            <if test="vehicleEmissionStandards != null "> and vehicle_emission_standards = #{vehicleEmissionStandards}</if>
            <if test="faceImg != null  and faceImg != ''"> and face_img = #{faceImg}</if>
            <if test="drivingLicenseImg != null  and drivingLicenseImg != ''"> and driving_license_img = #{drivingLicenseImg}</if>
            <if test="driverLicenseImg != null  and driverLicenseImg != ''"> and driver_license_img = #{driverLicenseImg}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="searchValue != null and searchValue != ''">and (driver_name like concat('%', #{searchValue}, '%' )
                or car_num like concat('%', #{searchValue}, '%' ))
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectXctgDriverUserList" parameterType="XctgDriverUser" resultMap="XctgDriverUserResult">
        <include refid="selectXctgDriverUser"/>
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="openId != null and openId != ''">
                AND open_id = #{openId}
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="gender != null and gender != ''">
                AND gender = #{gender}
            </if>
            <if test="idCard != null and idCard != ''">
                AND id_card = #{idCard}
            </if>
            <if test="company != null and company != ''">
                AND company LIKE CONCAT('%', #{company}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND phone = #{phone}
            </if>
            <if test="createTime != null">
                AND create_time &gt;= #{createTime}
            </if>
            <if test="updateTime != null">
                AND update_time &lt;= #{updateTime}
            </if>
            <if test="searchValue != null and searchValue != ''">and name like concat('%', #{searchValue}, '%' )
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectXctgDriverCarList" parameterType="XctgDriverCar" resultMap="XctgDriverCarResult">
        SELECT * FROM xctg_driver_car
        <where>
            <if test="openId != null and openId != ''"> AND open_id = #{openId} </if>
            <if test="carNumber != null and carNumber != ''"> AND car_number like concat('%', #{carNumber}, '%') </if>
            <if test="licensePlateColor != null and licensePlateColor != ''"> AND license_plate_color = #{licensePlateColor} </if>
            <if test="carId != null and carId != ''"> AND car_id = #{carId} </if>
            <if test="trailerNumber != null and trailerNumber != ''"> AND trailer_number = #{trailerNumber} </if>
            <if test="trailerId != null and trailerId != ''"> AND trailer_id = #{trailerId} </if>
            <if test="axisType != null and axisType != ''"> AND axis_type = #{axisType} </if>
            <if test="driverWeight != null"> AND driver_weight = #{driverWeight} </if>
            <if test="maxWeight != null"> AND max_weight = #{maxWeight} </if>
            <if test="vehicleEmissionStandards != null"> AND vehicle_emission_standards = #{vehicleEmissionStandards} </if>
            <if test="engineNumber != null and engineNumber != ''"> AND engine_number = #{engineNumber} </if>
            <if test="vinNumber != null and vinNumber != ''"> AND vin_number = #{vinNumber} </if>
            <if test="deleteTime == null"> AND delete_time IS NULL </if> <!-- 逻辑删除判断 -->
            <if test="searchValue != null and searchValue != ''">and car_number like concat('%', #{searchValue}, '%' )
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectDgcbDriverById" parameterType="Long" resultMap="DgcbDriverResult">
        <include refid="selectDgcbDriverVo"/>
        where id = #{id}
    </select>

    <insert id="insertDgcbDriver" parameterType="DgcbDriver" useGeneratedKeys="true" keyProperty="id">
        insert into dgcb_driver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="driverName != null">driver_name,</if>
            <if test="sex != null">sex,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="idCardNo != null">id_card_no,</if>
            <if test="carNum != null">car_num,</if>
            <if test="carNumColor != null">car_num_color,</if>
            <if test="vehicleEmissionStandards != null">vehicle_emission_standards,</if>
            <if test="faceImg != null">face_img,</if>
            <if test="drivingLicenseImg != null">driving_license_img,</if>
            <if test="driverLicenseImg != null">driver_license_img,</if>
            <if test="companyName != null">company_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="driverName != null">#{driverName},</if>
            <if test="sex != null">#{sex},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="idCardNo != null">#{idCardNo},</if>
            <if test="carNum != null">#{carNum},</if>
            <if test="carNumColor != null">#{carNumColor},</if>
            <if test="vehicleEmissionStandards != null">#{vehicleEmissionStandards},</if>
            <if test="faceImg != null">#{faceImg},</if>
            <if test="drivingLicenseImg != null">#{drivingLicenseImg},</if>
            <if test="driverLicenseImg != null">#{driverLicenseImg},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateDgcbDriver" parameterType="DgcbDriver">
        update dgcb_driver
        <trim prefix="SET" suffixOverrides=",">
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="idCardNo != null">id_card_no = #{idCardNo},</if>
            <if test="carNum != null">car_num = #{carNum},</if>
            <if test="carNumColor != null">car_num_color = #{carNumColor},</if>
            <if test="vehicleEmissionStandards != null">vehicle_emission_standards = #{vehicleEmissionStandards},</if>
            <if test="faceImg != null">face_img = #{faceImg},</if>
            <if test="drivingLicenseImg != null">driving_license_img = #{drivingLicenseImg},</if>
            <if test="driverLicenseImg != null">driver_license_img = #{driverLicenseImg},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDgcbDriverById" parameterType="Long">
        delete from dgcb_driver where id = #{id}
    </delete>

    <delete id="deleteDgcbDriverByIds" parameterType="String">
        delete from dgcb_driver where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
