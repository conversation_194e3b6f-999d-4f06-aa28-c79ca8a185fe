package com.ruoyi.app.leave.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.StoreoutBfhCkMeasure;
import com.ruoyi.app.leave.service.IStoreoutBfhCkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 备发货出库Controller
 * L_STOREOUT_BFH_CK_T 计量原表
 * <AUTHOR>
 */
@RestController
@RequestMapping("/leave/storeout")
public class StoreoutBfhCkController extends BaseController
{
    @Autowired
    private IStoreoutBfhCkService storeoutBfhCkService;

    /**
     * 查询备发货出库列表
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:list')")
    @GetMapping("/list")
    public TableDataInfo list(StoreoutBfhCkMeasure storeoutBfhCkMeasure)
    {
        startPage();
        List<StoreoutBfhCkMeasure> list = storeoutBfhCkService.selectStoreoutBfhCkList(storeoutBfhCkMeasure);
        return getDataTable(list);
    }

    /**
     * 导出备发货出库列表
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:export')")
    @Log(title = "备发货出库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StoreoutBfhCkMeasure storeoutBfhCkMeasure)
    {
        List<StoreoutBfhCkMeasure> list = storeoutBfhCkService.selectStoreoutBfhCkList(storeoutBfhCkMeasure);
        ExcelUtil<StoreoutBfhCkMeasure> util = new ExcelUtil<StoreoutBfhCkMeasure>(StoreoutBfhCkMeasure.class);
        util.exportExcel(list, "备发货出库数据");
    }

    /**
     * 获取备发货出库详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeoutBfhCkService.selectStoreoutBfhCkById(id));
    }

    /**
     * 新增备发货出库
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:add')")
    @Log(title = "备发货出库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StoreoutBfhCkMeasure storeoutBfhCkMeasure)
    {
        return toAjax(storeoutBfhCkService.insertStoreoutBfhCk(storeoutBfhCkMeasure));
    }

    /**
     * 修改备发货出库
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:edit')")
    @Log(title = "备发货出库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StoreoutBfhCkMeasure storeoutBfhCkMeasure)
    {
        return toAjax(storeoutBfhCkService.updateStoreoutBfhCk(storeoutBfhCkMeasure));
    }

    /**
     * 删除备发货出库
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:remove')")
    @Log(title = "备发货出库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(storeoutBfhCkService.deleteStoreoutBfhCkByIds(ids));
    }

    /**
     * 根据匹配ID删除备发货出库
     */
    @PreAuthorize("@ss.hasPermi('leave:storeout:remove')")
    @Log(title = "备发货出库", businessType = BusinessType.DELETE)
    @DeleteMapping("/matchid/{matchid}")
    public AjaxResult removeByMatchid(@PathVariable String matchid)
    {
        return toAjax(storeoutBfhCkService.deleteStoreoutBfhCkByMatchid(matchid));
    }
} 