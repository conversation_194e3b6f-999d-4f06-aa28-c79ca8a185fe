package com.ruoyi.app.leave.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LCarplanItemTMapper;
import com.ruoyi.app.leave.domain.LCarplanItemT;
import com.ruoyi.app.leave.service.ILCarplanItemTService;

/**
 * 跨区调拨物资明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class LCarplanItemTServiceImpl implements ILCarplanItemTService 
{
    @Autowired
    private LCarplanItemTMapper lCarplanItemTMapper;

    /**
     * 查询跨区调拨物资明细
     * 
     * @param id 跨区调拨物资明细ID
     * @return 跨区调拨物资明细
     */
    @Override
    public LCarplanItemT selectLCarplanItemTById(Long id)
    {
        return lCarplanItemTMapper.selectLCarplanItemTById(id);
    }

    /**
     * 查询跨区调拨物资明细列表
     * 
     * @param lCarplanItemT 跨区调拨物资明细
     * @return 跨区调拨物资明细
     */
    @Override
    public List<LCarplanItemT> selectLCarplanItemTList(LCarplanItemT lCarplanItemT)
    {
        return lCarplanItemTMapper.selectLCarplanItemTList(lCarplanItemT);
    }

    /**
     * 新增跨区调拨物资明细
     * 
     * @param lCarplanItemT 跨区调拨物资明细
     * @return 结果
     */
    @Override
    public int insertLCarplanItemT(LCarplanItemT lCarplanItemT)
    {
        return lCarplanItemTMapper.insertLCarplanItemT(lCarplanItemT);
    }

    /**
     * 修改跨区调拨物资明细
     * 
     * @param lCarplanItemT 跨区调拨物资明细
     * @return 结果
     */
    @Override
    public int updateLCarplanItemT(LCarplanItemT lCarplanItemT)
    {
        return lCarplanItemTMapper.updateLCarplanItemT(lCarplanItemT);
    }

    /**
     * 批量删除跨区调拨物资明细
     * 
     * @param ids 需要删除的跨区调拨物资明细ID
     * @return 结果
     */
    @Override
    public int deleteLCarplanItemTByIds(Long[] ids)
    {
        return lCarplanItemTMapper.deleteLCarplanItemTByIds(ids);
    }

    /**
     * 删除跨区调拨物资明细信息
     * 
     * @param id 跨区调拨物资明细ID
     * @return 结果
     */
    @Override
    public int deleteLCarplanItemTById(Long id)
    {
        return lCarplanItemTMapper.deleteLCarplanItemTById(id);
    }
}
