package com.ruoyi.web.controller.leave;

import com.ruoyi.app.leave.domain.LeaveLog;
import com.ruoyi.app.leave.service.ILeaveLogService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 出门证日志Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/web/leave/log")
public class WebLeaveLogController extends BaseController
{
    @Autowired
    private ILeaveLogService leaveLogService;

    /**
     * 查询出门证日志列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeaveLog leaveLog)
    {
        startPage();
        List<LeaveLog> list = leaveLogService.selectLeaveLogList(leaveLog);
        return getDataTable(list);
    }

    /**
     * 导出出门证日志列表
     */
    @Log(title = "出门证日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveLog leaveLog)
    {
        List<LeaveLog> list = leaveLogService.selectLeaveLogList(leaveLog);
        ExcelUtil<LeaveLog> util = new ExcelUtil<LeaveLog>(LeaveLog.class);
        return util.exportExcel(list, "log");
    }

    /**
     * 获取出门证日志详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveLogService.selectLeaveLogById(id));
    }

    /**
     * 新增出门证日志
     */
    @Log(title = "出门证日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveLog leaveLog)
    {
        return toAjax(leaveLogService.insertLeaveLog(leaveLog));
    }

    /**
     * 修改出门证日志
     */
    @Log(title = "出门证日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveLog leaveLog)
    {
        return toAjax(leaveLogService.updateLeaveLog(leaveLog));
    }

    /**
     * 删除出门证日志
     */
    @Log(title = "出门证日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveLogService.deleteLeaveLogByIds(ids));
    }


    @Log(title = "出门证日志", businessType = BusinessType.INSERT)
    @PostMapping("/handledLog")
    public AjaxResult addHandledLog(@RequestBody LeaveLog leaveLog)
    {
        leaveLog.setCreateBy(SecurityUtils.getUsername());
        return toAjax(leaveLogService.insertLeaveLog(leaveLog));
    }


}
