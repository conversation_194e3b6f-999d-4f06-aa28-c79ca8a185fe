package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LCarplanItemT;

/**
 * 跨区调拨物资明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface ILCarplanItemTService 
{
    /**
     * 查询跨区调拨物资明细
     * 
     * @param id 跨区调拨物资明细ID
     * @return 跨区调拨物资明细
     */
    public LCarplanItemT selectLCarplanItemTById(Long id);

    /**
     * 查询跨区调拨物资明细列表
     * 
     * @param lCarplanItemT 跨区调拨物资明细
     * @return 跨区调拨物资明细集合
     */
    public List<LCarplanItemT> selectLCarplanItemTList(LCarplanItemT lCarplanItemT);

    /**
     * 新增跨区调拨物资明细
     * 
     * @param lCarplanItemT 跨区调拨物资明细
     * @return 结果
     */
    public int insertLCarplanItemT(LCarplanItemT lCarplanItemT);

    /**
     * 修改跨区调拨物资明细
     * 
     * @param lCarplanItemT 跨区调拨物资明细
     * @return 结果
     */
    public int updateLCarplanItemT(LCarplanItemT lCarplanItemT);

    /**
     * 批量删除跨区调拨物资明细
     * 
     * @param ids 需要删除的跨区调拨物资明细ID
     * @return 结果
     */
    public int deleteLCarplanItemTByIds(Long[] ids);

    /**
     * 删除跨区调拨物资明细信息
     * 
     * @param id 跨区调拨物资明细ID
     * @return 结果
     */
    public int deleteLCarplanItemTById(Long id);
}
