package com.ruoyi.app.leave.domain;

import java.util.List;

//原计量任务类
public class LeavePlanTask {

    private static final long serialVersionUID = 1L;

//
//    /** 操作类型 */
//    private String actionCode;

    /** 计划类型 */
    private int operaType;

    /** 计划号 */
    private String planId;

    /** 来源编码 */
    private String sourceCode;

    /** 来源名称 */
    private String sourceName;

    /** 接收编码 */
    private String receiveCode;

    /* 接收单位 */
    private String receiveName;

    /** 去向编码 */
    private String targetCode;

    /** 去向名称 */
    private String targetName;

    /** 物资名称 */
    private String materialName;

    /** 物资规格 */
    private String materialSpec;

    /** 数量 */
    private String count;

    /** 创建时间 */
    private String createDate;

    /** 更新时间 */
    private String updateTime;

    /** 计划开始时间 */
    private String beginTime;

    /** 计划结束时间 */
    private String endTime;

    /** 计划量 */
    private int planAmount;

    /** 有效期 */
    private String validTime;

    /** 计划状态 */
    private int validFlag;

    /** 计量标记 */
    private int measureFlag;

    /** 备注 */
    private String memo;

    /** 出厂不返回原因 */
    private String outMemo;

    /** 预计返回日期 */
    private String planReturnDate;

    /** 物资明细 */
    private List<LeavePlanItem> planItemList;

    /** 业务id */
    private Integer businessId;

    /** 业务类型 (1、新增计划 2、修改计划 3、出厂 4、入厂 5、出库 6、入库)*/
    private int businessType;

    /** 炉号 */
    private String heatNo;

    /** 钢种 */
    private String steelGrade;

    /** 车号 */
    private String carNo;

    /** 验配ID */
    private String matchId;
    private String sFlag;
    /**
     * 件数
     */
    private String matNo;

    public String getsFlag() {
        return sFlag;
    }

    public void setsFlag(String sFlag) {
        this.sFlag = sFlag;
    }

    public String getMatchId() {
        return matchId;
    }

    public void setMatchId(String matchId) {
        this.matchId = matchId;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public String getHeatNo() {
        return heatNo;
    }

    public void setHeatNo(String heatNo) {
        this.heatNo = heatNo;
    }

    public String getSteelGrade() {
        return steelGrade;
    }

    public void setSteelGrade(String steelGrade) {
        this.steelGrade = steelGrade;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public int getOperaType() {
        return operaType;
    }

    public void setOperaType(int operaType) {
        this.operaType = operaType;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialSpec() {
        return materialSpec;
    }

    public void setMaterialSpec(String materialSpec) {
        this.materialSpec = materialSpec;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(int planAmount) {
        this.planAmount = planAmount;
    }

    public String getValidTime() {
        return validTime;
    }

    public void setValidTime(String validTime) {
        this.validTime = validTime;
    }

    public int getValidFlag() {
        return validFlag;
    }

    public void setValidFlag(int validFlag) {
        this.validFlag = validFlag;
    }

    public int getMeasureFlag() {
        return measureFlag;
    }

    public void setMeasureFlag(int measureFlag) {
        this.measureFlag = measureFlag;
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getOutMemo() {
        return outMemo;
    }

    public void setOutMemo(String outMemo) {
        this.outMemo = outMemo;
    }

    public String getPlanReturnDate() {
        return planReturnDate;
    }

    public void setPlanReturnDate(String planReturnDate) {
        this.planReturnDate = planReturnDate;
    }

    public List<LeavePlanItem> getPlanItemList() {
        return planItemList;
    }

    public void setPlanItemList(List<LeavePlanItem> planItemList) {
        this.planItemList = planItemList;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public int getBusinessType() {
        return businessType;
    }

    public void setBusinessType(int businessType) {
        this.businessType = businessType;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getReceiveCode() {
        return receiveCode;
    }

    public void setReceiveCode(String receiveCode) {
        this.receiveCode = receiveCode;
    }

    public String getTargetCode() {
        return targetCode;
    }

    public void setTargetCode(String targetCode) {
        this.targetCode = targetCode;
    }

    public String getMatNo() {
        return matNo;
    }

    public void setMatNo(String matNo) {
        this.matNo = matNo;
    }
}
