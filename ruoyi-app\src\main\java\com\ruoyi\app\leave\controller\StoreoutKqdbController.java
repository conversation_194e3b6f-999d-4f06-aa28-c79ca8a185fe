package com.ruoyi.app.leave.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.StoreoutKqdbMeasure;
import com.ruoyi.app.leave.service.IStoreoutKqdbService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 库区调拨出库Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/leave/storeoutkqdb")
public class StoreoutKqdbController extends BaseController
{
    @Autowired
    private IStoreoutKqdbService storeoutKqdbService;

    /**
     * 查询库区调拨出库列表
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeoutkqdb:list')")
    @GetMapping("/list")
    public TableDataInfo list(StoreoutKqdbMeasure storeoutKqdbMeasure)
    {
        startPage();
        List<StoreoutKqdbMeasure> list = storeoutKqdbService.selectStoreoutKqdbList(storeoutKqdbMeasure);
        return getDataTable(list);
    }

    /**
     * 导出库区调拨出库列表
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeoutkqdb:export')")
    @Log(title = "库区调拨出库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StoreoutKqdbMeasure storeoutKqdbMeasure)
    {
        List<StoreoutKqdbMeasure> list = storeoutKqdbService.selectStoreoutKqdbList(storeoutKqdbMeasure);
        ExcelUtil<StoreoutKqdbMeasure> util = new ExcelUtil<StoreoutKqdbMeasure>(StoreoutKqdbMeasure.class);
        util.exportEasyExcel(list, "库区调拨出库数据", StoreoutKqdbMeasure.class);
    }

    /**
     * 获取库区调拨出库详细信息
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeoutkqdb:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeoutKqdbService.selectStoreoutKqdbById(id));
    }

    /**
     * 新增库区调拨出库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeoutkqdb:add')")
    @Log(title = "库区调拨出库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StoreoutKqdbMeasure storeoutKqdbMeasure)
    {
        return toAjax(storeoutKqdbService.insertStoreoutKqdb(storeoutKqdbMeasure));
    }

    /**
     * 修改库区调拨出库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeoutkqdb:edit')")
    @Log(title = "库区调拨出库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StoreoutKqdbMeasure storeoutKqdbMeasure)
    {
        return toAjax(storeoutKqdbService.updateStoreoutKqdb(storeoutKqdbMeasure));
    }

    /**
     * 删除库区调拨出库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeoutkqdb:remove')")
    @Log(title = "库区调拨出库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(storeoutKqdbService.deleteStoreoutKqdbByIds(ids));
    }

    /**
     * 根据matchid删除库区调拨出库
     */
    @PreAuthorize("@ss.hasPermi('app:leave:storeoutkqdb:remove')")
    @Log(title = "库区调拨出库", businessType = BusinessType.DELETE)
    @DeleteMapping("/matchid/{matchid}")
    public AjaxResult removeByMatchid(@PathVariable String matchid)
    {
        return toAjax(storeoutKqdbService.deleteStoreoutKqdbByMatchid(matchid));
    }
} 