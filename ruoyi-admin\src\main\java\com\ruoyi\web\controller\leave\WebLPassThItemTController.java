package com.ruoyi.web.controller.leave;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LPassThItemT;
import com.ruoyi.app.leave.service.ILPassThItemTService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 退货申请物资明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@RestController
@RequestMapping("/web/leave/passThItem")
public class WebLPassThItemTController extends BaseController
{
    @Autowired
    private ILPassThItemTService lPassThItemTService;

    /**
     * 查询退货申请物资明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LPassThItemT lPassThItemT)
    {
        startPage();
        List<LPassThItemT> list = lPassThItemTService.selectLPassThItemTList(lPassThItemT);
        return getDataTable(list);
    }

    /**
     * 导出退货申请物资明细列表
     */
    @Log(title = "退货申请物资明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LPassThItemT lPassThItemT)
    {
        List<LPassThItemT> list = lPassThItemTService.selectLPassThItemTList(lPassThItemT);
        ExcelUtil<LPassThItemT> util = new ExcelUtil<LPassThItemT>(LPassThItemT.class);
        return util.exportExcel(list, "passThItem");
    }

    /**
     * 获取退货申请物资明细详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lPassThItemTService.selectLPassThItemTById(id));
    }

    /**
     * 新增退货申请物资明细
     */
    @Log(title = "退货申请物资明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LPassThItemT lPassThItemT)
    {
        return toAjax(lPassThItemTService.insertLPassThItemT(lPassThItemT));
    }

    /**
     * 修改退货申请物资明细
     */
    @Log(title = "退货申请物资明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LPassThItemT lPassThItemT)
    {
        return toAjax(lPassThItemTService.updateLPassThItemT(lPassThItemT));
    }

    /**
     * 删除退货申请物资明细
     */
    @Log(title = "退货申请物资明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lPassThItemTService.deleteLPassThItemTByIds(ids));
    }
}
