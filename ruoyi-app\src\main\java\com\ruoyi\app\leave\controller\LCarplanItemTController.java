package com.ruoyi.app.leave.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LCarplanItemT;
import com.ruoyi.app.leave.service.ILCarplanItemTService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 跨区调拨物资明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@RestController
@RequestMapping("/leave/carPlanItem")
public class LCarplanItemTController extends BaseController
{
    @Autowired
    private ILCarplanItemTService lCarplanItemTService;

    /**
     * 查询跨区调拨物资明细列表
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlanItem:list')")
    @GetMapping("/list")
    public TableDataInfo list(LCarplanItemT lCarplanItemT)
    {
        startPage();
        List<LCarplanItemT> list = lCarplanItemTService.selectLCarplanItemTList(lCarplanItemT);
        return getDataTable(list);
    }

    /**
     * 导出跨区调拨物资明细列表
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlanItem:export')")
    @Log(title = "跨区调拨物资明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LCarplanItemT lCarplanItemT)
    {
        List<LCarplanItemT> list = lCarplanItemTService.selectLCarplanItemTList(lCarplanItemT);
        ExcelUtil<LCarplanItemT> util = new ExcelUtil<LCarplanItemT>(LCarplanItemT.class);
        return util.exportExcel(list, "carPlanItem");
    }

    /**
     * 获取跨区调拨物资明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlanItem:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lCarplanItemTService.selectLCarplanItemTById(id));
    }

    /**
     * 新增跨区调拨物资明细
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlanItem:add')")
    @Log(title = "跨区调拨物资明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LCarplanItemT lCarplanItemT)
    {
        return toAjax(lCarplanItemTService.insertLCarplanItemT(lCarplanItemT));
    }

    /**
     * 修改跨区调拨物资明细
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlanItem:edit')")
    @Log(title = "跨区调拨物资明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LCarplanItemT lCarplanItemT)
    {
        return toAjax(lCarplanItemTService.updateLCarplanItemT(lCarplanItemT));
    }

    /**
     * 删除跨区调拨物资明细
     */
    @PreAuthorize("@ss.hasPermi('leave:carPlanItem:remove')")
    @Log(title = "跨区调拨物资明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lCarplanItemTService.deleteLCarplanItemTByIds(ids));
    }
}
