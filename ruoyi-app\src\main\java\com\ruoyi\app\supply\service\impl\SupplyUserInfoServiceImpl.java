package com.ruoyi.app.supply.service.impl;

import com.github.pagehelper.PageInfo;
import com.ruoyi.app.supply.domain.SupplyUserInfo;
import com.ruoyi.app.supply.mapper.SupplyUserInfoMapper;
import com.ruoyi.app.supply.service.ISupplyUserInfoService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 相关方人员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-3-22
 */
@Service
public class SupplyUserInfoServiceImpl implements ISupplyUserInfoService
{
    @Autowired
    private SupplyUserInfoMapper supplyUserInfoMapper;

    /**
     * 查询相关方人员信息
     *
     * @param id 人员ID
     * @return 人员信息
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public SupplyUserInfo selectSupplyUserInfoById(Integer id)
    {
        return supplyUserInfoMapper.selectSupplyUserInfoById(id);
    }

    /**
     * 查询人员信息列表
     *
     * @param supplyUserInfo 人员信息
     * @return 人员信息集合
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public List<SupplyUserInfo> selectSupplyUserInfoList(SupplyUserInfo supplyUserInfo)
    {
        return supplyUserInfoMapper.selectSupplyUserInfoList(supplyUserInfo);
    }

    /**
     * 查询人员信息分页列表
     *
     * @param supplyUserInfo 人员信息
     * @return 人员信息分页数据
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public TableDataInfo selectSupplyUserInfoTable(SupplyUserInfo supplyUserInfo) {
        List<SupplyUserInfo> list = supplyUserInfoMapper.selectSupplyUserInfoList(supplyUserInfo);
        long total = new PageInfo(list).getTotal();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(total);
        return rspData;
    }

    /**
     * 新增人员信息
     *
     * @param supplyUserInfo 人员信息
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int insertSupplyUserInfo(SupplyUserInfo supplyUserInfo)
    {
        return supplyUserInfoMapper.insertSupplyUserInfo(supplyUserInfo);
    }

    /**
     * 修改人员信息
     *
     * @param supplyUserInfo 人员信息
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int updateSupplyUserInfo(SupplyUserInfo supplyUserInfo)
    {
        return supplyUserInfoMapper.updateSupplyUserInfo(supplyUserInfo);
    }

    /**
     * 批量删除人员信息
     *
     * @param ids 需要删除的人员信息主键
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int deleteSupplyUserInfoByIds(Integer[] ids)
    {
        return supplyUserInfoMapper.deleteSupplyUserInfoByIds(ids);
    }

    /**
     * 删除人员信息信息
     *
     * @param id 人员信息主键
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SUPPLY)
    public int deleteSupplyUserInfoById(Integer id)
    {
        return supplyUserInfoMapper.deleteSupplyUserInfoById(id);
    }

} 