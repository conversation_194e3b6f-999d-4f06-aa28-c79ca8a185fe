package com.ruoyi.app.leave.service.impl;

import java.util.List;

import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.StoreinKqdbMapper;
import com.ruoyi.app.leave.domain.StoreinKqdbMeasure;
import com.ruoyi.app.leave.service.IStoreinKqdbService;
import com.ruoyi.app.leave.mapper.DicMapper;
import com.ruoyi.app.leave.domain.DicMeasure;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.domain.LeaveTaskMaterial;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 库区调拨入库Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class StoreinKqdbServiceImpl implements IStoreinKqdbService 
{
    @Autowired
    private StoreinKqdbMapper storeinKqdbMapper;

    @Autowired
    private DicMapper dicMapper;

    private static final Logger log = LoggerFactory.getLogger(StoreinKqdbServiceImpl.class);

    /**
     * 查询库区调拨入库
     * 
     * @param id 库区调拨入库主键
     * @return 库区调拨入库
     */
    @Override
    public StoreinKqdbMeasure selectStoreinKqdbById(Long id)
    {
        return storeinKqdbMapper.selectStoreinKqdbById(id);
    }

    /**
     * 查询库区调拨入库列表
     * 
     * @param storeinKqdbMeasure 库区调拨入库
     * @return 库区调拨入库
     */
    @Override
    public List<StoreinKqdbMeasure> selectStoreinKqdbList(StoreinKqdbMeasure storeinKqdbMeasure)
    {
        return storeinKqdbMapper.selectStoreinKqdbList(storeinKqdbMeasure);
    }

    /**
     * 新增库区调拨入库
     * 
     * @param storeinKqdbMeasure 库区调拨入库
     * @return 结果
     */
    @Override
    public int insertStoreinKqdb(StoreinKqdbMeasure storeinKqdbMeasure)
    {
        return storeinKqdbMapper.insertStoreinKqdb(storeinKqdbMeasure);
    }

    /**
     * 修改库区调拨入库
     * 
     * @param storeinKqdbMeasure 库区调拨入库
     * @return 结果
     */
    @Override
    public int updateStoreinKqdb(StoreinKqdbMeasure storeinKqdbMeasure)
    {
        return storeinKqdbMapper.updateStoreinKqdb(storeinKqdbMeasure);
    }

    /**
     * 批量删除库区调拨入库
     * 
     * @param ids 需要删除的库区调拨入库主键
     * @return 结果
     */
    @Override
    public int deleteStoreinKqdbByIds(Long[] ids)
    {
        return storeinKqdbMapper.deleteStoreinKqdbByIds(ids);
    }

    /**
     * 删除库区调拨入库信息
     * 
     * @param id 库区调拨入库主键
     * @return 结果
     */
    @Override
    public int deleteStoreinKqdbById(Long id)
    {
        return storeinKqdbMapper.deleteStoreinKqdbById(id);
    }

    /**
     * 根据matchid删除库区调拨入库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @Override
    public int deleteStoreinKqdbByMatchid(String matchid)
    {
        return storeinKqdbMapper.deleteStoreinKqdbByMatchid(matchid);
    }

    /**
     * 跨区调拨入库对接计量系统
     * @param nowDate 当前时间
     * @param leaveTask 任务对象
     * @param leavePlan 计划对象
     * @param leaveTaskMaterial 物资对象
     * @param directSupplyPlanNo 直供计划号（如有）
     */
    public void handleKqdbStockIn(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial, String directSupplyPlanNo) {
        // 1. 更新计量系统数据
        DicMeasure dicMeasure = new DicMeasure();
        dicMeasure.setCarno(leaveTask.getCarNum());
        dicMeasure.setTargettime(nowDate); // 收货时间
        dicMeasure.setDeduction(leaveTask.getDeductWeight()); // 总扣重（如有）
        dicMeasure.setDeductiontime(nowDate);

        if (leaveTask.getIsDirectSupply() == 3) {
            dicMeasure.setZgtargettime(nowDate); // 直供业务才需更新
        }
        dicMapper.updateDicByCarNo(dicMeasure);

        // 2. 创建跨区调拨入库记录
        StoreinKqdbMeasure storeinKqdbMeasure = new StoreinKqdbMeasure();
        storeinKqdbMeasure.setValidflag(1L);
        storeinKqdbMeasure.setCarno(leaveTask.getCarNum());
        storeinKqdbMeasure.setOperatype("6");
        storeinKqdbMeasure.setPlanid(leavePlan.getPlanNo());
        storeinKqdbMeasure.setMaterialname(leaveTaskMaterial.getMaterialName());
        storeinKqdbMeasure.setMaterialspec(leaveTaskMaterial.getMaterialSpec());
        storeinKqdbMeasure.setSourcename(leavePlan.getSourceCompany());
        storeinKqdbMeasure.setStorename(leavePlan.getReceiveCompany());
        storeinKqdbMeasure.setCreateman(SecurityUtils.getLoginUser().getUser().getNickName());
        storeinKqdbMeasure.setCreatedate(nowDate);
        storeinKqdbMeasure.setMemo(leaveTask.getRemark());

        // 根据车号获取matchid
        DicMeasure dicQuery = new DicMeasure();
        dicQuery.setCarno(leaveTask.getCarNum());
        java.util.List<DicMeasure> dicMeasures = dicMapper.selectDicList(dicQuery);
        if (dicMeasures.size() == 1 && dicMeasures.get(0) != null) {
            storeinKqdbMeasure.setMatchid(dicMeasures.get(0).getMatchid());
        } else {
            log.error("StoreinKqdbServiceImpl.handleKqdbStockIn 跨区调拨入库对接计量系统异常");
        }

        // 插入跨区调拨入库记录
        int i = storeinKqdbMapper.insertStoreinKqdb(storeinKqdbMeasure);
        if (i < 0) {
            log.error("StoreinKqdbServiceImpl.handleKqdbStockIn 跨区调拨入库对接计量系统异常");
        }
    }
} 