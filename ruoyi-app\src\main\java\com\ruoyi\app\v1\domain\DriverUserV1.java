package com.ruoyi.app.v1.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;


import java.math.BigDecimal;

/**
 * 提货司机 User
 *
 * <AUTHOR>
 * @date 2021-8-9
 */
public class DriverUserV1 extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String openId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 身份证号 从业资格证号 */
    @Excel(name = "身份证号 从业资格证号")
    private String idCard;

    /** $column.columnComment */
    @Excel(name = "手机号")
    private String phone;

    /** 性别 1男，2女 */
    @Excel(name = "性别 1男，2女")
    private String gender;

    /**单位 */
    @Excel(name = "单位")
    private String company;

    /** 照片 */
    @Excel(name = "照片")
    private String photo;

    /**驾驶证照片 */
    @Excel(name = "驾驶证照片")
    private String driverLicenseImgs;

    /** 车辆行驶证 */
    @Excel(name = "车辆行驶证")
    private String vehicleLicenseImgs;


    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setOpenId(String openId)
    {
        this.openId = openId;
    }

    public String getOpenId()
    {
        return openId;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setIdCard(String idCard)
    {
        this.idCard = idCard;
    }

    public String getIdCard()
    {
        return idCard;
    }
    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getPhone()
    {
        return phone;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getDriverLicenseImgs() {
        return driverLicenseImgs;
    }

    public void setDriverLicenseImgs(String driverLicenseImgs) {
        this.driverLicenseImgs = driverLicenseImgs;
    }

    public String getVehicleLicenseImgs() {
        return vehicleLicenseImgs;
    }

    public void setVehicleLicenseImgs(String vehicleLicenseImgs) {
        this.vehicleLicenseImgs = vehicleLicenseImgs;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("DriverUserV1{");
        sb.append("id=").append(id);
        sb.append(", openId='").append(openId).append('\'');
        sb.append(", name='").append(name).append('\'');
        sb.append(", idCard='").append(idCard).append('\'');
        sb.append(", phone='").append(phone).append('\'');
        sb.append(", gender='").append(gender).append('\'');
        sb.append(", company='").append(company).append('\'');
        sb.append(", photo='").append(photo).append('\'');
        sb.append(", driverLicenseImgs='").append(driverLicenseImgs).append('\'');
        sb.append(", vehicleLicenseImgs='").append(vehicleLicenseImgs).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
