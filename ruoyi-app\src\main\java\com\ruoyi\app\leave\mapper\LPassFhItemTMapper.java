package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.LPassFhItemT;

/**
 * 出厂返回物资明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface LPassFhItemTMapper 
{
    /**
     * 查询出厂返回物资明细
     * 
     * @param id 出厂返回物资明细ID
     * @return 出厂返回物资明细
     */
    public LPassFhItemT selectLPassFhItemTById(Long id);

    /**
     * 查询出厂返回物资明细列表
     * 
     * @param lPassFhItemT 出厂返回物资明细
     * @return 出厂返回物资明细集合
     */
    public List<LPassFhItemT> selectLPassFhItemTList(LPassFhItemT lPassFhItemT);

    /**
     * 新增出厂返回物资明细
     * 
     * @param lPassFhItemT 出厂返回物资明细
     * @return 结果
     */
    public int insertLPassFhItemT(LPassFhItemT lPassFhItemT);

    /**
     * 修改出厂返回物资明细
     * 
     * @param lPassFhItemT 出厂返回物资明细
     * @return 结果
     */
    public int updateLPassFhItemT(LPassFhItemT lPassFhItemT);

    /**
     * 删除出厂返回物资明细
     * 
     * @param id 出厂返回物资明细ID
     * @return 结果
     */
    public int deleteLPassFhItemTById(Long id);

    /**
     * 批量删除出厂返回物资明细
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteLPassFhItemTByIds(Long[] ids);

    /**
     * 根据主表ID删除明细
     * 
     * @param pid 主表ID
     * @return 结果
     */
    public int deleteByPid(Long pid);
}
