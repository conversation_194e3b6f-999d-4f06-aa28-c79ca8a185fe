package com.ruoyi.app.leave.task;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.ruoyi.app.leave.domain.LeavePushRecord;
import com.ruoyi.app.leave.enums.LeavePushTaskTypeEnum;
import com.ruoyi.app.leave.enums.LeavePushStatusEnum;
import com.ruoyi.app.leave.service.ILeavePushRecordService;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 推送重试定时任务
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Component
public class LeavePushRetryTask
{
    private static final Logger log = LoggerFactory.getLogger(LeavePushRetryTask.class);

    @Autowired
    private ILeavePushRecordService leavePushRecordService;

    /**
     * 每5分钟执行一次推送重试
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void retryPush()
    {
        log.info("开始执行重试同步任务");
        try
        {
            // 查询待重试的推送记录
            List<LeavePushRecord> records = leavePushRecordService.selectRetryPushRecords();
            if (StringUtils.isEmpty(records))
            {
                log.info("没有需要重试的推送记录");
                return;
            }

            for (LeavePushRecord record : records)
            {
                try
                {
                    // TODO: 调用推送接口
                    boolean success = doPush(record.getBusinessNo(), record.getType());

                    if (success)
                    {
                        // 推送成功，更新状态
                        leavePushRecordService.updatePushStatus(record.getId(), LeavePushStatusEnum.SUCCESS.getCode());
                        log.info("重试同步任务[{}]推送成功", record.getBusinessNo());
                    }
                    else
                    {
                        // 推送失败，更新重试次数和下次重试时间
                        leavePushRecordService.updatePushStatus(record.getId(), LeavePushStatusEnum.FAIL.getCode());
                        log.error("重试同步任务[{}]推送失败", record.getBusinessNo());
                    }
                }
                catch (Exception e)
                {
                    log.error("重试同步任务[{}]推送异常", record.getBusinessNo(), e);
                    // 推送异常，更新重试次数和下次重试时间
                    leavePushRecordService.updatePushStatus(record.getId(), LeavePushStatusEnum.FAIL.getCode());
                }
            }
        }
        catch (Exception e)
        {
            log.error("重试同步任务异常", e);
        }
        log.info("重试同步任务执行完成");
    }

    /**
     * 执行推送
     *
     * @param businessNo 业务单号
     * @param type 业务类型
     * @return 推送结果
     */
    private boolean doPush(String businessNo, Integer type)
    {
        LeavePushTaskTypeEnum typeEnum = LeavePushTaskTypeEnum.getByCode(type);
        switch (typeEnum) {
            case SYNC_METRIC:
                // return syncMetric(businessNo);
            case SYNC_LOGISTICS:
                // return syncLogistics(businessNo);
            case SYNC_SECURITY:
                // return syncSecurity(businessNo);
            default:
                throw new RuntimeException("不支持的业务类型");
        }
    }
}