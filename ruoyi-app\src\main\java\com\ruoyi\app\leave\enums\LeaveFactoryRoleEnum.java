package com.ruoyi.app.leave.enums;

import com.ruoyi.app.leave.dto.LeaveRole;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 分厂申请人:leave.applicant
 * 分厂审核人员:leave.factoryApprover
 * 分厂复审人员:leave.factorySecApprover
 * 生产指挥中心审核人员:leave.centerApprover
 * 分厂装货人员:leave.loading
 * 分厂卸货人员:leave.unloading
 * 门卫:leave.guard
 * 供应商:leave.provider
 *
 * <AUTHOR>
 * @date 2025/3/13 下午2:21
 */
public enum LeaveFactoryRoleEnum {
    APPLICANT("leave.applicant", "分厂申请人"),
    FACTORY_APPROVER("leave.factoryApprover", "分厂审核人员"),
    FACTORY_SEC_APPROVER("leave.factorySecApprover", "分厂复审人员"),
    LOADING("leave.loading", "分厂装货人员"),
    UNLOADING("leave.unloading", "分厂卸货人员");

    private final String code;
    private final String desc;

    LeaveFactoryRoleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static LeaveFactoryRoleEnum getByCode(String code) {
        for (LeaveFactoryRoleEnum value : LeaveFactoryRoleEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static LeaveFactoryRoleEnum getByDesc(String desc) {
        for (LeaveFactoryRoleEnum value : LeaveFactoryRoleEnum.values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }

    public static List<String> getCodeList() {
        List<String> result = new ArrayList<String>();
        for (LeaveFactoryRoleEnum value : LeaveFactoryRoleEnum.values()) {
            result.add(value.getCode());
        }
        return result;
    }

    public static String getDescByCode(String code) {
        if (Optional.ofNullable(code).isPresent()) {
            for (LeaveFactoryRoleEnum value : LeaveFactoryRoleEnum.values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
        }
        return "";
    }

    public static String getCodeByDesc(String desc) {
        if (Optional.ofNullable(desc).isPresent()) {
            for (LeaveFactoryRoleEnum value : LeaveFactoryRoleEnum.values()) {
                if (value.getDesc().equals(desc)) {
                    return value.getCode();
                }
            }
        }
        return "";
    }

    public static List<LeaveRole> getRoleList() {
        List<LeaveRole> list = new ArrayList<>();
        int index = 0;
        for (LeaveFactoryRoleEnum value : LeaveFactoryRoleEnum.values()) {
            LeaveRole role = new LeaveRole();
            role.setIndex(index++);
            role.setCode(value.getCode());
            role.setValue(value.getDesc());
            list.add(role);
        }
        return list;
    }
}

