package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LeaveLog;

/**
 * 出门证日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ILeaveLogService 
{
    /**
     * 查询出门证日志
     * 
     * @param id 出门证日志ID
     * @return 出门证日志
     */
    public LeaveLog selectLeaveLogById(Long id);

    /**
     * 查询出门证日志列表
     * 
     * @param leaveLog 出门证日志
     * @return 出门证日志集合
     */
    public List<LeaveLog> selectLeaveLogList(LeaveLog leaveLog);

    /**
     * 新增出门证日志
     * 
     * @param leaveLog 出门证日志
     * @return 结果
     */
    public int insertLeaveLog(LeaveLog leaveLog);

    /**
     * 修改出门证日志
     * 
     * @param leaveLog 出门证日志
     * @return 结果
     */
    public int updateLeaveLog(LeaveLog leaveLog);

    /**
     * 批量删除出门证日志
     * 
     * @param ids 需要删除的出门证日志ID
     * @return 结果
     */
    public int deleteLeaveLogByIds(Long[] ids);

    /**
     * 删除出门证日志信息
     * 
     * @param id 出门证日志ID
     * @return 结果
     */
    public int deleteLeaveLogById(Long id);

    LeaveLog handleLeaveLog(LeaveLog leaveLog);
}
