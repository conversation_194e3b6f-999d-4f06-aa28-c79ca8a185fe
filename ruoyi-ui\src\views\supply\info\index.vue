<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.native.prevent>
      <el-form-item label="供应商代码">
        <el-input v-model="queryParams.supplyCode" placeholder="请输入供应商代码" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <div style="margin-bottom: 10px;">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      <el-button type="success" icon="el-icon-upload" @click="handleImport">导入</el-button>
      <el-button type="warning" icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>

    <!-- 用户列表 -->
    <el-table :data="userList" border stripe style="width: 100%">
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="supplyCode" label="供应商代码" />
      <el-table-column prop="userName" label="用户姓名" />
      <el-table-column prop="idcard" label="身份证" />
      <el-table-column label="岗位识别卡">
        <template slot-scope="scope">
          <el-button size="mini" @click="openFacDialog(scope.row)">补充/编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column label="健康信息">
        <template slot-scope="scope">
          <el-button size="mini" @click="openHealthDialog(scope.row)">补充/编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column label="附件">
        <template slot-scope="scope">
          <el-button size="mini" @click="openFileDialog(scope.row)">管理</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-download"
              @click="downloadFile(scope.row)"
            >
              下载
            </el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="deleteFile(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      style="margin-top: 10px;"
      background
      layout="total, prev, pager, next, jumper"
      :total="total"
      :page-size="queryParams.pageSize"
      :current-page.sync="queryParams.pageNum"
      @current-change="handleQuery"
    />

    <!-- 新增/编辑主表弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <el-form :model="form" label-width="100px">
        <el-form-item label="供应商代码">
          <el-input v-model="form.supplyCode" />
        </el-form-item>
        <el-form-item label="供应商名称">
          <el-input v-model="form.supplyName" />
        </el-form-item>
        <el-form-item label="用户编号">
          <el-input v-model="form.userCode" />
        </el-form-item>
        <el-form-item label="用户姓名">
          <el-input v-model="form.userName" />
        </el-form-item>
        <el-form-item label="身份证">
          <el-input v-model="form.idcard" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="form.state" placeholder="请选择">
            <el-option label="正常" :value="1" />
            <el-option label="删除" :value="101" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 导入弹窗 -->
    <el-dialog title="导入相关方人员" :visible.sync="importDialogVisible">
      <el-upload
        :action="importUrl"
        :show-file-list="false"
        :on-success="handleImportSuccess"
        :before-upload="beforeImportUpload"
        :headers="uploadHeaders"
      >
        <el-button type="primary">选择文件上传</el-button>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 岗位识别卡弹窗 -->
    <vxe-modal v-model="facDialogVisible" title="岗位识别卡" width="700" show-footer>
      <vxe-form
        :data="facForm"
        :items="facFormItems"
        title-align="left"
        title-width="90"
        title-colon
        border
        size="small"
      />
      <template #footer>
        <vxe-button @click="facDialogVisible = false">取消</vxe-button>
        <vxe-button status="primary" @click="submitFac">保存</vxe-button>
      </template>
    </vxe-modal>

    <!-- 健康信息弹窗 -->
    <vxe-modal v-model="healthDialogVisible" title="健康信息" width="800" show-footer>
      <vxe-form
        :data="healthForm"
        :items="healthFormItems"
        title-align="left"
        title-width="90"
        title-colon
        border
        size="small"
      />
      <template #footer>
        <vxe-button @click="healthDialogVisible = false">取消</vxe-button>
        <vxe-button status="primary" @click="submitHealth">保存</vxe-button>
      </template>
    </vxe-modal>

    <!-- 附件管理弹窗 -->
    <el-dialog 
      :visible.sync="fileDialogVisible" 
      title="附件管理" 
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 上传区域 -->
      <div class="upload-section">
        <div class="upload-header">
          <i class="el-icon-upload"></i>
          <span class="upload-title">文件上传</span>
        </div>
        <div class="upload-content">
          <el-upload
            ref="fileUpload"
            :action="uploadUrl"
            :headers="upload.headers"
            :data="uploadData"
            :on-success="handleFileUploadSuccess"
            :on-error="handleFileUploadError"
            :before-upload="beforeFileUpload"
            :show-file-list="false"
            accept=".pdf"
            drag
            class="upload-dragger"
          >
            <div class="upload-area">
              <i class="el-icon-upload upload-icon"></i>
              <div class="upload-text">
                <span class="upload-main-text">将PDF文件拖到此处，或</span>
                <em class="upload-click-text">点击上传</em>
              </div>
              <div class="upload-tip">
                <i class="el-icon-info"></i>
                <span>仅支持PDF格式文件，单个文件不超过50MB</span>
              </div>
              <div class="upload-limits">
                <div class="limit-item">
                  <i class="el-icon-document"></i>
                  <span>文件格式：PDF</span>
                </div>
                <div class="limit-item">
                  <i class="el-icon-files"></i>
                  <span>文件大小：≤ 50MB</span>
                </div>
                <div class="limit-item">
                  <i class="el-icon-upload2"></i>
                  <span>支持拖拽上传</span>
                </div>
              </div>
            </div>
          </el-upload>
        </div>
      </div>

      <!-- 文件列表 -->
      <div class="file-list-section">
        <div class="file-list-header">
          <i class="el-icon-document"></i>
          <span class="file-list-title">已上传文件</span>
          <span class="file-count">(共 {{fileList.length}} 个文件)</span>
        </div>
        <div class="file-list-content">
          <el-table 
            :data="fileList" 
            style="width: 100%"
            :header-cell-style="{background:'#f5f7fa',color:'#606266'}"
            :row-class-name="tableRowClassName"
          >
            <el-table-column prop="filename" label="文件名" min-width="200">
              <template slot-scope="scope">
                <div class="file-info">
                  <i class="el-icon-document"></i>
                  <span class="file-name">{{scope.row.filename}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="format" label="格式" width="80" align="center">
              <template slot-scope="scope">
                <el-tag size="mini" type="info">{{scope.row.format}}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="state" label="状态" width="80" align="center">
              <template slot-scope="scope">
                <el-tag 
                  size="mini" 
                  :type="scope.row.state === 1 ? 'success' : 'danger'"
                >
                  {{scope.row.state === 1 ? '正常' : '异常'}}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center">
              <template slot-scope="scope">
                <div class="operation-buttons">
                  <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-download"
                    @click="downloadFile(scope.row)"
                  >
                    下载
                  </el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    @click="deleteFile(scope.row)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 弹窗底部 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="fileDialogVisible = false" icon="el-icon-close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, addInfo, updateInfo, delInfo, exportInfo } from '@/api/supply/info'
import { getFac, addFac, updateFac } from '@/api/supply/fac'
import { getHealth, addHealth, updateHealth } from '@/api/supply/health'
import { listFile, delFile } from '@/api/supply/file'
import request from '@/utils/request'
import { getToken } from '@/utils/auth'

export default {
  name: 'SupplyUserInfo',
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplyCode: ''
      },
      userList: [],
      total: 0,
      // 岗位识别卡
      facDialogVisible: false,
      facForm: {},
      facFormItems: [
        { field: 'userPost', title: '岗位名称', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入岗位名称', rows: 2 } } },
        { field: 'userFacClass', title: '岗位班组', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位班组' } } },
        { field: 'userDeptName', title: '所属部门', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入所属部门' } } },
        { field: 'userFacWork', title: '岗位描述', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位描述' } } },
        { field: 'userTimeBegin', title: '入厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },
        { field: 'userTimeEnd', title: '离厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },
        {
          field: 'state',
          title: '状态',
          span: 24,
          itemRender: {
            name: 'VxeSelect',
            options: [
              { label: '起草', value: 0 },
              { label: '分厂审核人', value: 1 },
              { label: '人力资源部', value: 2 },
              { label: '退回', value: -1 },
              { label: '禁用', value: 101 },
              { label: '审核通过', value: 99 },
              { label: '删除', value: 102 }
            ],
            props: { placeholder: '请选择' }
          }
        }
      ],
      // 健康信息
      healthDialogVisible: false,
      healthForm: {},
      healthFormItems: [
        { field: 'healdate', title: '体检日期', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },
        { field: 'hos', title: '医院', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入医院' } } },
        { field: 'healtz', title: '体重', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重' } } },
        { field: 'healtzzs', title: '体重指数', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重指数' } } },
        { field: 'healptt', title: '血糖', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入血糖' } } },
        { field: 'healssy', title: '收缩压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入收缩压' } } },
        { field: 'healszy', title: '舒张压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入舒张压' } } },
        { field: 'healzdgc', title: '总胆固醇', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入总胆固醇' } } },
        { field: 'healgysz', title: '甘油三酯', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入甘油三酯' } } },
        { field: 'healga', title: '谷氨酰转肽酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷氨酰转肽酶' } } },
        { field: 'healgb', title: '谷丙转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷丙转氨酶' } } },
        { field: 'healgc', title: '谷草转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷草转氨酶' } } },
        { field: 'healnsd', title: '尿素氮', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入尿素氮' } } },
        { field: 'healjg', title: '肌酐', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入肌酐' } } },
        { field: 'healxd', title: '心电图', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入心电图' } } },
        { field: 'healxj', title: '小结', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入小结', rows: 2 } } },
        { field: 'healjy', title: '建议', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入建议', rows: 2 } } },
        {
          field: 'state',
          title: '状态',
          span: 12,
          itemRender: {
            name: 'VxeSelect',
            options: [
              { label: '正常', value: 1 },
              { label: '删除', value: 101 }
            ],
            props: { placeholder: '请选择' }
          }
        }
      ],
      // 附件
      fileDialogVisible: false,
      fileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + '/web/supply/userfile/upload', // 新的 SFTP 上传接口
      currentUserId: null,
      currentUserInfo: {}, // 新增：保存当前用户信息
      // 上传配置
      upload: {
        headers: { Authorization: 'Bearer ' + getToken() }
      },
      // 新增/编辑主表
      dialogVisible: false,
      dialogTitle: '',
      form: {},
      importDialogVisible: false,
      importUrl: '/web/supply/userinfo/import'
    }
  },
  computed: {
    uploadData() {
      return {
        userid: this.currentUserId,
        usercode: this.currentUserInfo.userCode,
        username: this.currentUserInfo.userName,
        supplycode: this.currentUserInfo.supplyCode,
        supplyname: this.currentUserInfo.supplyName,
        idcard: this.currentUserInfo.idcard,
        userdeptname: this.currentUserInfo.userDeptName
      }
    }
  },
  methods: {
    // 查询用户列表
    handleQuery() {
      listInfo(this.queryParams).then(res => {
        this.userList = res.rows
        this.total = res.total
      })
    },
    resetQuery() {
      this.queryParams.supplyCode = ''
      this.handleQuery()
    },
    // 新增
    handleAdd() {
      this.dialogTitle = '新增相关方人员'
      this.form = {}
      this.dialogVisible = true
    },
    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑相关方人员'
      this.form = Object.assign({}, row)
      this.dialogVisible = true
    },
    // 删除
    handleDelete(row) {
      this.$confirm('确定删除该条数据吗？', '提示', { type: 'warning' }).then(() => {
        delInfo(row.id).then(() => {
          this.$message.success('删除成功')
          this.handleQuery()
        })
      })
    },
    // 提交主表
    submitForm() {
      if (this.form.id) {
        updateInfo(this.form).then(() => {
          this.$message.success('修改成功')
          this.dialogVisible = false
          this.handleQuery()
        })
      } else {
        addInfo(this.form).then(() => {
          this.$message.success('新增成功')
          this.dialogVisible = false
          this.handleQuery()
        })
      }
    },
    // 导出
    handleExport() {
      exportInfo(this.queryParams).then(res => {
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '相关方人员数据.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      })
    },
    // 导入
    handleImport() {
      this.importDialogVisible = true
    },
    handleImportSuccess(response) {
      if (response.code === 200) {
        this.$message.success('导入成功')
        this.importDialogVisible = false
        this.handleQuery()
      } else {
        this.$message.error(response.msg || '导入失败')
      }
    },
    // 导入前检查文件类型
    beforeImportUpload(file) {
      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      if (!isExcel) {
        this.$message.error('只能上传Excel文件！')
      }
      return isExcel
    },
    // 附件上传前检查文件类型
    beforeFileUpload(file) {
      // 检查文件类型是否为PDF
      const isPDF = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
      if (!isPDF) {
        this.$message.error('只能上传PDF格式文件！')
        return false
      }
      
      // 检查文件大小限制
      const maxSize = 50 * 1024 * 1024 // 50MB
      if (file.size > maxSize) {
        this.$message.error('文件大小不能超过50MB！')
        return false
      }
      
      return true
    },
    // 岗位识别卡
    openFacDialog(row) {
      getFac(row.id).then(res => {
        this.facForm = res.data || { userId: row.id }
        this.facDialogVisible = true
      })
    },
    submitFac() {
      const api = this.facForm.id ? updateFac : addFac
      api(this.facForm).then(() => {
        this.$message.success('保存成功')
        this.facDialogVisible = false
        this.handleQuery()
      })
    },
    // 健康信息
    openHealthDialog(row) {
      getHealth(row.id).then(res => {
        this.healthForm = res.data || { userid: row.id }
        this.healthDialogVisible = true
      })
    },
    submitHealth() {
      const api = this.healthForm.id ? updateHealth : addHealth
      api(this.healthForm).then(() => {
        this.$message.success('保存成功')
        this.healthDialogVisible = false
        this.handleQuery()
      })
    },
    // 附件管理
    openFileDialog(row) {
      this.currentUserId = row.id
      this.currentUserInfo = row // 保存当前用户信息
      this.getFileList(row.id)
      this.fileDialogVisible = true
    },
    getFileList(userid) {
      listFile({ userid }).then(res => {
        this.fileList = res.rows
      })
    },
    handleFileUploadSuccess(response) {
      if (response.code === 200) {
        this.$message.success('文件上传成功')
        this.getFileList(this.currentUserId)
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
    },
    handleFileUploadError(err) {
      this.$message.error('文件上传失败: ' + (err.message || '未知错误'))
    },
    deleteFile(row) {
      this.$confirm('确定删除该附件吗？', '提示', { type: 'warning' }).then(() => {
        delFile(row.id).then(() => {
          this.$message.success('删除成功')
          this.getFileList(this.currentUserId)
        })
      })
    },
    downloadFile(row) {
      // 调用下载接口获取文件URL
      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {
        if (response.code === 200) {
          // 获取到文件URL后，在新窗口中打开下载
          const fileUrl = response.data
          window.open(fileUrl, '_blank')
        } else {
          this.$message.error(response.msg || '下载失败')
        }
      }).catch(error => {
        this.$message.error('下载失败: ' + error.message)
      })
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.state === 1) {
        return 'success-row'
      } else {
        return 'danger-row'
      }
    }
  },
  mounted() {
    this.handleQuery()
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.upload-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 25px;
  border-radius: 8px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.upload-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  color: #fff;
}

.upload-header i {
  font-size: 20px;
  margin-right: 10px;
}

.upload-title {
  font-size: 18px;
  font-weight: 600;
}

.upload-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 180px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.upload-content:hover {
  border-color: rgba(255, 255, 255, 0.6);
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.upload-dragger {
  width: 100%;
  height: 100%;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.upload-icon {
  font-size: 48px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 15px;
}

.upload-text {
  margin-bottom: 15px;
}

.upload-main-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
}

.upload-click-text {
  color: #fff;
  font-style: normal;
  font-weight: 600;
  text-decoration: underline;
  cursor: pointer;
}

.upload-tip {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.upload-tip i {
  margin-right: 5px;
  font-size: 12px;
}

.upload-limits {
  margin-top: 20px;
  display: flex;
  justify-content: space-around;
  width: 100%;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  flex-wrap: wrap;
  gap: 15px;
}

.limit-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.limit-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.limit-item i {
  margin-right: 6px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.file-list-section {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-top: 20px;
}

.file-list-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  color: #606266;
}

.file-list-title {
  margin-left: 8px;
  font-size: 16px;
}

.file-count {
  margin-left: 10px;
  font-size: 14px;
  color: #909399;
}

.file-list-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 10px;
  border: 1px solid #ebeef5;
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.file-name {
  margin-left: 8px;
  font-size: 14px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}

.el-table .success-row {
  background-color: #f0f9eb;
}

.el-table .danger-row {
  background-color: #fef0f0;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.operation-buttons .el-button {
  margin: 0;
}
</style>
