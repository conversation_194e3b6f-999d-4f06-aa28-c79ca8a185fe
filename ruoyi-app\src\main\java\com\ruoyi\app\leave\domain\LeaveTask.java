package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出门证任务对象 leave_task
 *
 *
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
public class LeaveTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    private String licensePlateColor;

    /** 车辆道路运输证号 */
    private String carId;

    /** 挂车号牌 */
    private String trailerNumber;

    /** 挂车道路运输证号 */
    private String trailerId;

    /** 轴型 */
    private String axisType;

    /** 货车自重 */
    private BigDecimal driverWeight;

    /** 车货总质量限值 */
    private BigDecimal maxWeight;

    /** 发动机号 */
    private String engineNumber;

    /** 车辆识别代码 */
    private String vinNumber;

    /** 任务号 */
    @Excel(name = "任务号")
    private String taskNo;

    /** 申请单号 */
    @Excel(name = "申请单号")
    private String applyNo;

    /** 任务类型 1-出厂 2-返厂 3-跨区调拨 */
    @Excel(name = "任务类型 1-出厂 2-返厂 3-跨区调拨")
    private Integer taskType;

    /** 计划号 */
    @Excel(name = "计划号")
    private String planNo;

    /** 任务状态  1-待过皮重 2-待装货 3-待过毛重 4-待出厂 5-待返厂 6-待过毛重(复磅) 7-待卸货 8-待过皮重(复磅) 9-完成 */
    @Excel(name = "任务状态  1-待过皮重 2-待装货 3-待过毛重 4-待出厂 5-待返厂 6-待过毛重(复磅) 7-待卸货 8-待过皮重(复磅) 9-完成")
    private Integer taskStatus;

    /** 炉号 */
    @Excel(name = "炉号")
    private String heatNo;

    /** 钢种 */
    @Excel(name = "钢种")
    private String steelGrade;

    /** 离厂大门 任务类型1、3 */
    @Excel(name = "离厂大门 任务类型1、3")
    private Integer leaveDoor;

    /** 离厂时间 任务类型1、3 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "离厂时间 任务类型1、3", width = 30, dateFormat = "yyyy-MM-dd")
    private Date leaveTime;

    /** 进厂大门 任务类型2、3 */
    @Excel(name = "进厂大门 任务类型2、3")
    private Integer enterDoor;

    /** 进厂时间 任务类型2、3 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "进厂时间 任务类型2、3", width = 30, dateFormat = "yyyy-MM-dd")
    private Date enterTime;

    /** 装货人 */
    @Excel(name = "装货人")
    private String loadingWorkNo;

    /** 装货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "装货时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loadingTime;

    /** 卸货人 */
    @Excel(name = "卸货人")
    private String unloadingWorkNo;

    /** 卸货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "卸货时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date unloadingTime;

    /** 司机名称 */
    @Excel(name = "司机名称")
    private String driverName;

    /** 性别 1-男 2-女 */
    @Excel(name = "性别 1-男 2-女")
    private Integer sex;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobilePhone;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCardNo;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carNum;

    /** 车辆排放标准 1-国五 2-国六 3-新能源 */
    @Excel(name = "车辆排放标准 1-国五 2-国六 3-新能源")
    private Integer vehicleEmissionStandards;

    /** 人脸照片 */
    @Excel(name = "人脸照片")
    private String faceImg;

    /** 行驶证照片 */
    @Excel(name = "行驶证照片")
    private String drivingLicenseImg;

    /** 驾驶证照片 */
    @Excel(name = "驾驶证照片")
    private String driverLicenseImg;

    /** 公司名称  */
    @Excel(name = "公司名称 ")
    private String companyName;

    /** 毛重 */
    @Excel(name = "毛重")
    private BigDecimal gross;

    /** 皮重 */
    @Excel(name = "皮重")
    private BigDecimal tare;

    /** 净重 */
    @Excel(name = "净重")
    private BigDecimal netWeight;

    /** 净重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "净重时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date netWeightTime;

    /** 毛重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "毛重时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date grossTime;

    /** 皮重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "皮重时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tareTime;

    /** 毛重（复磅）跨区调拨专用 */
    @Excel(name = "毛重（复磅）跨区调拨专用")
    private BigDecimal secGross;

    /** 皮重（复磅）跨区调拨专用 */
    @Excel(name = "皮重（复磅）跨区调拨专用")
    private BigDecimal secTare;

    /** 净重（复磅）跨区调拨专用 */
    @Excel(name = "净重（复磅）跨区调拨专用")
    private BigDecimal secNetWeight;

    /** 净重时间（复磅）跨区调拨专用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "净重时间（复磅）跨区调拨专用", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date secNetWeightTime;

    /** 皮重时间（复磅）跨区调拨专用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "皮重时间（复磅）跨区调拨专用", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date secTareTime;

    /** 毛重时间（复磅）跨区调拨专用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "毛重时间（复磅）跨区调拨专用", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date secGrossTime;

    /** 二维码内容 */
    private String qrCodeContent;

     /** 规格1（长度） */
    @Excel(name = "规格1（长度）")
    private String spec1Length;

    /** 规格2（宽度） */
    @Excel(name = "规格2（宽度）")
    private BigDecimal spec2Width;

    /** 总数 */
    @Excel(name = "总数")
    private String totals;

    /** 加工类型 */
    @Excel(name = "加工类型")
    private String processType;

    /** 轴数 */
    @Excel(name = "轴数")
    private String axles;

    /** 出库备注 */
    @Excel(name = "备注-入库")
    private String remark;

    /** 是否直接供应 */
    @Excel(name = "是否直接供应")
    private Integer isDirectSupply;

    private String directSupplyTaskNo;

    /** 规格1（长度）-出库 */
    private BigDecimal stockOutSpec1Length;

    /** 规格2（宽度）-出库 */
    private BigDecimal stockOutSpec2Width;

    /** 总数 -出库 */
    private String stockOutTotals;

    /** 加工类型 -出库 */
    private String stockOutProcessType;

    /** 炉号 -出库 */
    private String stockOutHeatNo;

    /** 钢种 -出库 */
    private String stockOutSteelGrade;

    /** 轴数 -出库 */
    private String stockOutAxles;

    /** 备注 -出库 */
    private String stockOutRemark;

    /** 扣重 */
    @Excel(name = "扣重")
    private BigDecimal deductWeight;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTaskNo(String taskNo) 
    {
        this.taskNo = taskNo;
    }

    public String getTaskNo() 
    {
        return taskNo;
    }
    public void setApplyNo(String applyNo) 
    {
        this.applyNo = applyNo;
    }

    public String getApplyNo() 
    {
        return applyNo;
    }
    public void setTaskType(Integer taskType) 
    {
        this.taskType = taskType;
    }

    public Integer getTaskType() 
    {
        return taskType;
    }
    public void setPlanNo(String planNo) 
    {
        this.planNo = planNo;
    }

    public String getPlanNo() 
    {
        return planNo;
    }
    public void setTaskStatus(Integer taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public Integer getTaskStatus() 
    {
        return taskStatus;
    }
    public void setHeatNo(String heatNo) 
    {
        this.heatNo = heatNo;
    }

    public String getHeatNo() 
    {
        return heatNo;
    }
    public void setSteelGrade(String steelGrade) 
    {
        this.steelGrade = steelGrade;
    }

    public String getSteelGrade() 
    {
        return steelGrade;
    }
    public void setLeaveDoor(Integer leaveDoor) 
    {
        this.leaveDoor = leaveDoor;
    }

    public Integer getLeaveDoor() 
    {
        return leaveDoor;
    }
    public void setLeaveTime(Date leaveTime) 
    {
        this.leaveTime = leaveTime;
    }

    public Date getLeaveTime() 
    {
        return leaveTime;
    }
    public void setEnterDoor(Integer enterDoor) 
    {
        this.enterDoor = enterDoor;
    }

    public Integer getEnterDoor() 
    {
        return enterDoor;
    }
    public void setEnterTime(Date enterTime) 
    {
        this.enterTime = enterTime;
    }

    public Date getEnterTime() 
    {
        return enterTime;
    }
    public void setLoadingWorkNo(String loadingWorkNo) 
    {
        this.loadingWorkNo = loadingWorkNo;
    }

    public String getLoadingWorkNo() 
    {
        return loadingWorkNo;
    }
    public void setLoadingTime(Date loadingTime) 
    {
        this.loadingTime = loadingTime;
    }

    public Date getLoadingTime() 
    {
        return loadingTime;
    }
    public void setUnloadingWorkNo(String unloadingWorkNo) 
    {
        this.unloadingWorkNo = unloadingWorkNo;
    }

    public String getUnloadingWorkNo() 
    {
        return unloadingWorkNo;
    }
    public void setUnloadingTime(Date unloadingTime) 
    {
        this.unloadingTime = unloadingTime;
    }

    public Date getUnloadingTime() 
    {
        return unloadingTime;
    }
    public void setDriverName(String driverName) 
    {
        this.driverName = driverName;
    }

    public String getDriverName() 
    {
        return driverName;
    }
    public void setSex(Integer sex) 
    {
        this.sex = sex;
    }

    public Integer getSex() 
    {
        return sex;
    }
    public void setMobilePhone(String mobilePhone) 
    {
        this.mobilePhone = mobilePhone;
    }

    public String getMobilePhone() 
    {
        return mobilePhone;
    }
    public void setIdCardNo(String idCardNo) 
    {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() 
    {
        return idCardNo;
    }
    public void setCarNum(String carNum) 
    {
        this.carNum = carNum;
    }

    public String getCarNum() 
    {
        return carNum;
    }
    public void setVehicleEmissionStandards(Integer vehicleEmissionStandards) 
    {
        this.vehicleEmissionStandards = vehicleEmissionStandards;
    }

    public Integer getVehicleEmissionStandards() 
    {
        return vehicleEmissionStandards;
    }
    public void setFaceImg(String faceImg) 
    {
        this.faceImg = faceImg;
    }

    public String getFaceImg() 
    {
        return faceImg;
    }
    public void setDrivingLicenseImg(String drivingLicenseImg) 
    {
        this.drivingLicenseImg = drivingLicenseImg;
    }

    public String getDrivingLicenseImg() 
    {
        return drivingLicenseImg;
    }
    public void setDriverLicenseImg(String driverLicenseImg) 
    {
        this.driverLicenseImg = driverLicenseImg;
    }

    public String getDriverLicenseImg() 
    {
        return driverLicenseImg;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }

    public void setGross(BigDecimal gross)
    {
        this.gross = gross;
    }

    public BigDecimal getGross()
    {
        return gross;
    }
    public void setTare(BigDecimal tare)
    {
        this.tare = tare;
    }

    public BigDecimal getTare()
    {
        return tare;
    }
    public void setNetWeight(BigDecimal netWeight)
    {
        this.netWeight = netWeight;
    }

    public BigDecimal getNetWeight()
    {
        return netWeight;
    }
    public void setNetWeightTime(Date netWeightTime)
    {
        this.netWeightTime = netWeightTime;
    }

    public Date getNetWeightTime()
    {
        return netWeightTime;
    }
    public void setGrossTime(Date grossTime)
    {
        this.grossTime = grossTime;
    }

    public Date getGrossTime()
    {
        return grossTime;
    }
    public void setTareTime(Date tareTime)
    {
        this.tareTime = tareTime;
    }

    public Date getTareTime()
    {
        return tareTime;
    }
    public void setSecGross(BigDecimal secGross)
    {
        this.secGross = secGross;
    }

    public BigDecimal getSecGross()
    {
        return secGross;
    }
    public void setSecTare(BigDecimal secTare)
    {
        this.secTare = secTare;
    }

    public BigDecimal getSecTare()
    {
        return secTare;
    }
    public void setSecNetWeight(BigDecimal secNetWeight)
    {
        this.secNetWeight = secNetWeight;
    }

    public BigDecimal getSecNetWeight()
    {
        return secNetWeight;
    }
    public void setSecNetWeightTime(Date secNetWeightTime)
    {
        this.secNetWeightTime = secNetWeightTime;
    }

    public Date getSecNetWeightTime()
    {
        return secNetWeightTime;
    }
    public void setSecTareTime(Date secTareTime)
    {
        this.secTareTime = secTareTime;
    }

    public Date getSecTareTime()
    {
        return secTareTime;
    }
    public void setSecGrossTime(Date secGrossTime)
    {
        this.secGrossTime = secGrossTime;
    }

    public Date getSecGrossTime()
    {
        return secGrossTime;
    }

    public void setQrCodeContent(String qrCodeContent)
    {
        this.qrCodeContent = qrCodeContent;
    }

    public String getQrCodeContent()
    {
        return qrCodeContent;
    }

    public Integer getIsDirectSupply() {
        return isDirectSupply;
    }

    public void setIsDirectSupply(Integer isDirectSupply) {
        this.isDirectSupply = isDirectSupply;
    }

    public BigDecimal getStockOutSpec1Length() {
        return stockOutSpec1Length;
    }

    public void setStockOutSpec1Length(BigDecimal stockOutSpec1Length) {
        this.stockOutSpec1Length = stockOutSpec1Length;
    }

    public BigDecimal getStockOutSpec2Width() {
        return stockOutSpec2Width;
    }

    public void setStockOutSpec2Width(BigDecimal stockOutSpec2Width) {
        this.stockOutSpec2Width = stockOutSpec2Width;
    }

    public String getStockOutTotals() {
        return stockOutTotals;
    }

    public void setStockOutTotals(String stockOutTotals) {
        this.stockOutTotals = stockOutTotals;
    }

    public String getStockOutProcessType() {
        return stockOutProcessType;
    }

    public void setStockOutProcessType(String stockOutProcessType) {
        this.stockOutProcessType = stockOutProcessType;
    }

    public String getStockOutHeatNo() {
        return stockOutHeatNo;
    }

    public void setStockOutHeatNo(String stockOutHeatNo) {
        this.stockOutHeatNo = stockOutHeatNo;
    }

    public String getStockOutSteelGrade() {
        return stockOutSteelGrade;
    }

    public void setStockOutSteelGrade(String stockOutSteelGrade) {
        this.stockOutSteelGrade = stockOutSteelGrade;
    }

    public String getStockOutAxles() {
        return stockOutAxles;
    }

    public void setStockOutAxles(String stockOutAxles) {
        this.stockOutAxles = stockOutAxles;
    }

    public String getStockOutRemark() {
        return stockOutRemark;
    }

    public void setStockOutRemark(String stockOutRemark) {
        this.stockOutRemark = stockOutRemark;
    }

    public BigDecimal getDeductWeight() {
        return deductWeight;
    }

    public void setDeductWeight(BigDecimal deductWeight) {
        this.deductWeight = deductWeight;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskNo", getTaskNo())
            .append("applyNo", getApplyNo())
            .append("taskType", getTaskType())
            .append("planNo", getPlanNo())
            .append("taskStatus", getTaskStatus())
            .append("heatNo", getHeatNo())
            .append("steelGrade", getSteelGrade())
            .append("leaveDoor", getLeaveDoor())
            .append("leaveTime", getLeaveTime())
            .append("enterDoor", getEnterDoor())
            .append("enterTime", getEnterTime())
            .append("loadingWorkNo", getLoadingWorkNo())
            .append("loadingTime", getLoadingTime())
            .append("unloadingWorkNo", getUnloadingWorkNo())
            .append("unloadingTime", getUnloadingTime())
            .append("driverName", getDriverName())
            .append("sex", getSex())
            .append("mobilePhone", getMobilePhone())
            .append("idCardNo", getIdCardNo())
            .append("carNum", getCarNum())
            .append("vehicleEmissionStandards", getVehicleEmissionStandards())
            .append("faceImg", getFaceImg())
            .append("drivingLicenseImg", getDrivingLicenseImg())
            .append("driverLicenseImg", getDriverLicenseImg())
            .append("companyName", getCompanyName())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
