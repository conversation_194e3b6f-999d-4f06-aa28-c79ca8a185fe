package com.ruoyi.web.controller.leave;

import java.util.List;

import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LeaveMaterial;
import com.ruoyi.app.leave.service.ILeaveMaterialService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出门证物资Controller
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
@RestController
@RequestMapping("/web/leave/material")
public class WebLeaveMaterialController extends BaseController
{
    @Autowired
    private ILeaveMaterialService leaveMaterialService;

    /**
     * 查询出门证物资列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeaveMaterial leaveMaterial)
    {
        startPage();
        List<LeaveMaterial> list = leaveMaterialService.selectLeaveMaterialList(leaveMaterial);
        return getDataTable(list);
    }

    /**
     * 导出出门证物资列表
     */
    @Log(title = "出门证物资", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveMaterial leaveMaterial)
    {
        List<LeaveMaterial> list = leaveMaterialService.selectLeaveMaterialList(leaveMaterial);
        ExcelUtil<LeaveMaterial> util = new ExcelUtil<LeaveMaterial>(LeaveMaterial.class);
        return util.exportExcel(list, "material");
    }

    /**
     * 获取出门证物资详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveMaterialService.selectLeaveMaterialById(id));
    }

    /**
     * 新增出门证物资
     */
    @Log(title = "出门证物资", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveMaterial leaveMaterial)
    {
        return toAjax(leaveMaterialService.insertLeaveMaterial(leaveMaterial, SecurityUtils.getUsername()));
    }

    /**
     * 修改出门证物资
     */
    @Log(title = "出门证物资", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveMaterial leaveMaterial)
    {
        return toAjax(leaveMaterialService.updateLeaveMaterial(leaveMaterial));
    }

    /**
     * 删除出门证物资
     */
    @Log(title = "出门证物资", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveMaterialService.deleteLeaveMaterialByIds(ids));
    }
}
