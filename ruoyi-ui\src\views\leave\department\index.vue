<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <!-- <el-form-item label="状态" prop="validFlag">
        <el-input
          v-model="queryParams.validFlag"
          placeholder="请输入状态"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="厂内单位名称" prop="storeName">
        <el-input
          v-model="queryParams.storeName"
          placeholder="请输入厂内单位名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="拼音头缩写" prop="queryWord">
        <el-input
          v-model="queryParams.queryWord"
          placeholder="请输入拼音头缩写"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="单位名称" prop="unitName">
        <el-input
          v-model="queryParams.unitName"
          placeholder="请输入单位名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="库房位置" prop="position">
        <el-input
          v-model="queryParams.position"
          placeholder="请输入库房位置"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最小限制" prop="lowerLimit">
        <el-input
          v-model="queryParams.lowerLimit"
          placeholder="请输入最小限制"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最大限制" prop="upLimit">
        <el-input
          v-model="queryParams.upLimit"
          placeholder="请输入最大限制"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="备注信息" prop="memo">
        <el-input
          v-model="queryParams.memo"
          placeholder="请输入备注信息"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上级单位编码" prop="fid">
        <el-input
          v-model="queryParams.fid"
          placeholder="请输入上级单位编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上级单位名称" prop="fStoreName">
        <el-input
          v-model="queryParams.fStoreName"
          placeholder="请输入上级单位名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单位层级" prop="levelName">
        <el-input
          v-model="queryParams.levelName"
          placeholder="请输入单位层级"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="single"
          @click="handleCancel"
        >作废</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['leave:department:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
	  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="departmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="状态" align="center" prop="validFlag" /> -->
      <!-- <el-table-column label="厂内单位编码" align="center" prop="id" /> -->
      <el-table-column label="厂内单位名称" align="center" prop="storeName" />
      <el-table-column label="拼音头缩写" align="center" prop="queryWord" />
      <!-- <el-table-column label="区域" align="center" prop="area" >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.area == '1'" type="info">滨江</el-tag>
          <el-tag v-if="scope.row.area == '2'" type="info">杨市</el-tag>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库" align="center" prop="type" />
      <el-table-column label="单位名称" align="center" prop="unitName" />
      <el-table-column label="库房位置" align="center" prop="position" />
      <el-table-column label="最小限制" align="center" prop="lowerLimit" />
      <el-table-column label="最大限制" align="center" prop="upLimit" />
      <el-table-column label="备注信息" align="center" prop="memo" /> -->
      <!-- <el-table-column label="上级单位编码" align="center" prop="fid" />
      <el-table-column label="上级单位名称" align="center" prop="fStoreName" /> -->
      <el-table-column label="单位层级" align="center" prop="levelName" >
        <template slot-scope="scope">
             {{levelOptions[parseInt(scope.row.levelName)-1]['label']}}
            </template>
      </el-table-column>
      <el-table-column label="添加人" align="center" prop="createBy" />
      <el-table-column label="添加时间" align="center" prop="createTime" />
      <el-table-column label="修改人" align="center" prop="updateBy" />
      <el-table-column label="修改时间" align="center" prop="updateTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-lock"
            @click="handleCancel(scope.row)"
          >作废</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['leave:department:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改出门证部门（厂内单位）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <!-- <el-form-item label="状态" prop="validFlag">
          <el-input v-model="form.validFlag" placeholder="请输入状态" />
        </el-form-item>
        <el-form-item label="厂内单位编码" prop="id">
          <el-input v-model="form.id" placeholder="请输入厂内单位编码" />
        </el-form-item> -->
        <el-form-item label="厂内单位名称" prop="storeName">
          <el-input v-model="form.storeName" placeholder="请输入厂内单位名称" />
        </el-form-item>
        <el-form-item label="拼音头缩写" prop="queryWord">
          <el-input v-model="form.queryWord" placeholder="请输入拼音头缩写" />
        </el-form-item>
        <!-- <el-form-item label="区域" prop="area">
          <el-select v-model="form.area" placeholder="请选择区域">
                                <el-option
                                v-for="dict in areaOptions"
                                :key="dict.key"
                                :label="dict.label"
                                :value="dict.value">
                                <span style="float: left">{{ dict.label }}</span>
                                </el-option>
            </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库" prop="type">
          <el-select v-model="form.type" placeholder="请选择原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="单位名称" prop="unitName">
          <el-input v-model="form.unitName" placeholder="请输入单位名称" />
        </el-form-item>
        <el-form-item label="库房位置" prop="position">
          <el-input v-model="form.position" placeholder="请输入库房位置" />
        </el-form-item>
        <el-form-item label="最小限制" prop="lowerLimit">
          <el-input v-model="form.lowerLimit" placeholder="请输入最小限制" />
        </el-form-item>
        <el-form-item label="最大限制" prop="upLimit">
          <el-input v-model="form.upLimit" placeholder="请输入最大限制" />
        </el-form-item> -->
        <el-form-item label="备注信息" prop="memo">
          <el-input v-model="form.memo" placeholder="请输入备注信息" />
        </el-form-item>
        <!-- <el-form-item label="上级单位编码" prop="fid">
          <el-input v-model="form.fid" placeholder="请输入上级单位编码" />
        </el-form-item>
        <el-form-item label="上级单位名称" prop="fStoreName">
          <el-input v-model="form.fStoreName" placeholder="请输入上级单位名称" />
        </el-form-item> -->
        <el-form-item label="单位层级" prop="levelName">
           <el-select v-model="form.levelName" placeholder="请选择单位层级">
                                <el-option
                                v-for="dict in levelOptions"
                                :key="dict.key"
                                :label="dict.label"
                                :value="dict.value">
                                <span style="float: left">{{ dict.label }}</span>
                                </el-option>
            </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDepartment, getDepartment, delDepartment, addDepartment, updateDepartment,cancelDepartment, exportDepartment } from "@/api/leave/department";

export default {
  name: "Department",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出门证部门（厂内单位）表格数据
      departmentList: [],
      // 单位层级列表
      levelOptions:[
          {key:"1",label:"一级单位",value:1},
          {key:"2",label:"二级单位",value:2},
          {key:"3",label:"三级单位",value:3},
          {key:"4",label:"四级单位",value:4},
      ],
      // 区域列表
      areaOptions:[
          {key:"1",label:"滨江",value:1},
          {key:"2",label:"杨市",value:2},
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        validFlag: null,
        id: null,
        storeName: null,
        queryWord: null,
        type: null,
        unitName: null,
        position: null,
        lowerLimit: null,
        upLimit: null,
        memo: null,
        fid: null,
        fStoreName: null,
        levelName: null,
        area: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询出门证部门（厂内单位）列表 */
    getList() {
      this.loading = true;
      listDepartment(this.queryParams).then(response => {
        this.departmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        validFlag: null,
        id: null,
        storeName: null,
        queryWord: null,
        type: null,
        unitName: null,
        position: null,
        lowerLimit: null,
        upLimit: null,
        memo: null,
        fid: null,
        fStoreName: null,
        levelName: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        area:null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加出门证部门（厂内单位）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDepartment(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改出门证部门（厂内单位）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDepartment(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDepartment(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 作废按钮操作 */
    handleCancel(row) {
    const ids = row.id || this.ids;
    this.$confirm('是否确认作废?', "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
    .then(() => {
      return getDepartment(ids);
    })
    .then(response => {
      this.form = response.data;
      return cancelDepartment(this.form);
    })
    .then(() => {
      this.msgSuccess("作废成功");
      this.getList();
    })
  },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delDepartment(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有出门证部门（厂内单位）数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportDepartment(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
