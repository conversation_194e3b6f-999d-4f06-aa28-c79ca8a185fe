package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 不返回主对象 L_PASS_T
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public class LPassT extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 有效标记：1正常，2出厂，0作废，8完成，10，9 */
    @Excel(name = "有效标记：1正常，2出厂，0作废，8完成，10，9")
    private Long validflag;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialcode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialname;

    /** 规格 */
    @Excel(name = "规格")
    private String materialspec;

    /** 型号 */
    @Excel(name = "型号")
    private String materialtype;

    /** 发货编码 */
    @Excel(name = "发货编码")
    private String sourcecode;

    /** 发货单位 */
    @Excel(name = "发货单位")
    private String sourcename;

    /** 收货编码 */
    @Excel(name = "收货编码")
    private String targetcode;

    /** 收货单位 */
    @Excel(name = "收货单位")
    private String targetname;

    /** 数量 */
    @Excel(name = "数量")
    private Long count;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validtime;

    /** 申请单位编码 */
    @Excel(name = "申请单位编码")
    private String unitcode;

    /** 申请单位名称 */
    @Excel(name = "申请单位名称")
    private String unitname;

    /** 领导审核：0不通过，1未审核，2通过 */
    @Excel(name = "领导审核：0不通过，1未审核，2通过")
    private Long lflag;

    /** 领导 */
    @Excel(name = "领导")
    private String leader;

    /** 领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ldate;

    /** 生产中心审核：0不通过，1未审核，2通过 */
    @Excel(name = "生产中心审核：0不通过，1未审核，2通过")
    private Long pflag;

    /** 生产中心审核人 */
    @Excel(name = "生产中心审核人")
    private String productman;

    /** 生产中心审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生产中心审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pdate;

    /** 打印次数 */
    @Excel(name = "打印次数")
    private Long printnum;

    /** 添加人 */
    @Excel(name = "添加人")
    private String createman;

    /** 添加时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "添加时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdate;

    /** 修改人 */
    @Excel(name = "修改人")
    private String updateman;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedate;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 领导意见 */
    @Excel(name = "领导意见")
    private String leadermemo;

    /** 生产中心意见 */
    @Excel(name = "生产中心意见")
    private String productmemo;

    /** 车号 */
    @Excel(name = "车号")
    private String carno;

    /** 附件路径 */
    @Excel(name = "附件路径")
    private String attachpath;

    /** 出厂人 */
    @Excel(name = "出厂人")
    private String leaveman;

    /** 出厂日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出厂日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date leavedate;

    /** 出厂大门 */
    @Excel(name = "出厂大门")
    private String leavegate;

    /** 监装人 */
    @Excel(name = "监装人")
    private String supervisor;

    /** 出厂原因/不返回原因 */
    @Excel(name = "出厂原因/不返回原因")
    private String outmemo;

    /** 生产指挥中心打印出门证次数 */
    @Excel(name = "生产指挥中心打印出门证次数")
    private Long printnump;

    /** 出门证号字母+年份+7位数字 */
    @Excel(name = "出门证号字母+年份+7位数字")
    private String cmzhao;

    /** $column.columnComment */
    @Excel(name = "出门证号字母+年份+7位数字")
    private Long yxflag;

    /** 默认是3天，还有可能是4,5,6 天（可能性极小） */
    @Excel(name = "默认是3天，还有可能是4,5,6 天", readConverterExp = "可=能性极小")
    private Long yxday;

    /** 物资专管员 */
    @Excel(name = "物资专管员")
    private String wzzgy;

    /** 申请号 */
    @Excel(name = "申请号")
    private String applyid;

    /** 计量标记：默认为0 不计量，1为计量 */
    @Excel(name = "计量标记：默认为0 不计量，1为计量")
    private Long measureflag;

    /** 复审标记：不复审为0,三期复审为1，废钢供应复审为2，设备复审3，试样复审4 */
    @Excel(name = "复审标记：不复审为0,三期复审为1，废钢供应复审为2，设备复审3，试样复审4")
    private Long fsflag;

    /** 三期复审：0不复审，1未复审，2通过，3未通过 */
    @Excel(name = "三期复审：0不复审，1未复审，2通过，3未通过")
    private Long sqflag;

    /** 三期领导 */
    @Excel(name = "三期领导")
    private String sqleader;

    /** 三期领导意见 */
    @Excel(name = "三期领导意见")
    private String sqlmemo;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sqldate;

    /** 废钢供应复审：0不复审，1未复审，2通过，3未通过 */
    @Excel(name = "废钢供应复审：0不复审，1未复审，2通过，3未通过")
    private Long fgflag;

    /** 废钢供应领导 */
    @Excel(name = "废钢供应领导")
    private String fgleader;

    /** 废钢供应领导意见 */
    @Excel(name = "废钢供应领导意见")
    private String fglmemo;

    /** 废钢供应领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "废钢供应领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date fgldate;

    /** 设备审核：0不复审，1未复审，2通过，3未通过 */
    @Excel(name = "设备审核：0不复审，1未复审，2通过，3未通过")
    private Long sbflag;

    /** 设备领导 */
    @Excel(name = "设备领导")
    private String sbleader;

    /** 设备领导意见 */
    @Excel(name = "设备领导意见")
    private String sblmemo;

    /** 设备领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "设备领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sbldate;

    /** 试样审核标记：0不复审，1未复审，2通过，3未通过 */
    @Excel(name = "试样审核标记：0不复审，1未复审，2通过，3未通过")
    private Long syflag;

    /** 试样领导 */
    @Excel(name = "试样领导")
    private String syleader;

    /** 试样领导意见 */
    @Excel(name = "试样领导意见")
    private String sylmemo;

    /** 试样领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "试样领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date syldate;

    /** 0滨江厂区1花山厂区 */
    @Excel(name = "0滨江厂区1花山厂区")
    private Long hsflag;

    /** 0表示不需要一次多审核，1表示需要一次多审核（领导，生产指挥中心） */
    @Excel(name = "0表示不需要一次多审核，1表示需要一次多审核", readConverterExp = "领=导，生产指挥中心")
    private Long shmore;

    /** 修改有效期原因 */
    @Excel(name = "修改有效期原因")
    private String yxqmemo;

    /** 计划类型：不返回计量 */
    @Excel(name = "计划类型：不返回计量")
    private BigDecimal operatype;

    /** 作废原因 */
    @Excel(name = "作废原因")
    private String cancelmemo;

    /** 生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sxtime;

    /** 0可以打印1不可以打印 */
    @Excel(name = "0可以打印1不可以打印")
    private Long printflag;

    /** 审核补打时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核补打时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date printflagdate;

    /** 补打原因 */
    @Excel(name = "补打原因")
    private String bdmemo;

    /** 材料类型 */
    @Excel(name = "材料类型")
    private String mtype;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "材料类型", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uptime;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "材料类型", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uptimestamp;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setValidflag(Long validflag) 
    {
        this.validflag = validflag;
    }

    public Long getValidflag() 
    {
        return validflag;
    }
    public void setMaterialcode(String materialcode) 
    {
        this.materialcode = materialcode;
    }

    public String getMaterialcode() 
    {
        return materialcode;
    }
    public void setMaterialname(String materialname) 
    {
        this.materialname = materialname;
    }

    public String getMaterialname() 
    {
        return materialname;
    }
    public void setMaterialspec(String materialspec) 
    {
        this.materialspec = materialspec;
    }

    public String getMaterialspec() 
    {
        return materialspec;
    }
    public void setMaterialtype(String materialtype) 
    {
        this.materialtype = materialtype;
    }

    public String getMaterialtype() 
    {
        return materialtype;
    }
    public void setSourcecode(String sourcecode) 
    {
        this.sourcecode = sourcecode;
    }

    public String getSourcecode() 
    {
        return sourcecode;
    }
    public void setSourcename(String sourcename) 
    {
        this.sourcename = sourcename;
    }

    public String getSourcename() 
    {
        return sourcename;
    }
    public void setTargetcode(String targetcode) 
    {
        this.targetcode = targetcode;
    }

    public String getTargetcode() 
    {
        return targetcode;
    }
    public void setTargetname(String targetname) 
    {
        this.targetname = targetname;
    }

    public String getTargetname() 
    {
        return targetname;
    }
    public void setCount(Long count) 
    {
        this.count = count;
    }

    public Long getCount() 
    {
        return count;
    }
    public void setWeight(BigDecimal weight) 
    {
        this.weight = weight;
    }

    public BigDecimal getWeight() 
    {
        return weight;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setValidtime(Date validtime) 
    {
        this.validtime = validtime;
    }

    public Date getValidtime() 
    {
        return validtime;
    }
    public void setUnitcode(String unitcode) 
    {
        this.unitcode = unitcode;
    }

    public String getUnitcode() 
    {
        return unitcode;
    }
    public void setUnitname(String unitname) 
    {
        this.unitname = unitname;
    }

    public String getUnitname() 
    {
        return unitname;
    }
    public void setLflag(Long lflag) 
    {
        this.lflag = lflag;
    }

    public Long getLflag() 
    {
        return lflag;
    }
    public void setLeader(String leader) 
    {
        this.leader = leader;
    }

    public String getLeader() 
    {
        return leader;
    }
    public void setLdate(Date ldate) 
    {
        this.ldate = ldate;
    }

    public Date getLdate() 
    {
        return ldate;
    }
    public void setPflag(Long pflag) 
    {
        this.pflag = pflag;
    }

    public Long getPflag() 
    {
        return pflag;
    }
    public void setProductman(String productman) 
    {
        this.productman = productman;
    }

    public String getProductman() 
    {
        return productman;
    }
    public void setPdate(Date pdate) 
    {
        this.pdate = pdate;
    }

    public Date getPdate() 
    {
        return pdate;
    }
    public void setPrintnum(Long printnum) 
    {
        this.printnum = printnum;
    }

    public Long getPrintnum() 
    {
        return printnum;
    }
    public void setCreateman(String createman) 
    {
        this.createman = createman;
    }

    public String getCreateman() 
    {
        return createman;
    }
    public void setCreatedate(Date createdate) 
    {
        this.createdate = createdate;
    }

    public Date getCreatedate() 
    {
        return createdate;
    }
    public void setUpdateman(String updateman) 
    {
        this.updateman = updateman;
    }

    public String getUpdateman() 
    {
        return updateman;
    }
    public void setUpdatedate(Date updatedate) 
    {
        this.updatedate = updatedate;
    }

    public Date getUpdatedate() 
    {
        return updatedate;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setLeadermemo(String leadermemo) 
    {
        this.leadermemo = leadermemo;
    }

    public String getLeadermemo() 
    {
        return leadermemo;
    }
    public void setProductmemo(String productmemo) 
    {
        this.productmemo = productmemo;
    }

    public String getProductmemo() 
    {
        return productmemo;
    }
    public void setCarno(String carno) 
    {
        this.carno = carno;
    }

    public String getCarno() 
    {
        return carno;
    }
    public void setAttachpath(String attachpath) 
    {
        this.attachpath = attachpath;
    }

    public String getAttachpath() 
    {
        return attachpath;
    }
    public void setLeaveman(String leaveman) 
    {
        this.leaveman = leaveman;
    }

    public String getLeaveman() 
    {
        return leaveman;
    }
    public void setLeavedate(Date leavedate) 
    {
        this.leavedate = leavedate;
    }

    public Date getLeavedate() 
    {
        return leavedate;
    }
    public void setLeavegate(String leavegate) 
    {
        this.leavegate = leavegate;
    }

    public String getLeavegate() 
    {
        return leavegate;
    }
    public void setSupervisor(String supervisor) 
    {
        this.supervisor = supervisor;
    }

    public String getSupervisor() 
    {
        return supervisor;
    }
    public void setOutmemo(String outmemo) 
    {
        this.outmemo = outmemo;
    }

    public String getOutmemo() 
    {
        return outmemo;
    }
    public void setPrintnump(Long printnump) 
    {
        this.printnump = printnump;
    }

    public Long getPrintnump() 
    {
        return printnump;
    }
    public void setCmzhao(String cmzhao) 
    {
        this.cmzhao = cmzhao;
    }

    public String getCmzhao() 
    {
        return cmzhao;
    }
    public void setYxflag(Long yxflag) 
    {
        this.yxflag = yxflag;
    }

    public Long getYxflag() 
    {
        return yxflag;
    }
    public void setYxday(Long yxday) 
    {
        this.yxday = yxday;
    }

    public Long getYxday() 
    {
        return yxday;
    }
    public void setWzzgy(String wzzgy) 
    {
        this.wzzgy = wzzgy;
    }

    public String getWzzgy() 
    {
        return wzzgy;
    }
    public void setApplyid(String applyid) 
    {
        this.applyid = applyid;
    }

    public String getApplyid() 
    {
        return applyid;
    }
    public void setMeasureflag(Long measureflag) 
    {
        this.measureflag = measureflag;
    }

    public Long getMeasureflag() 
    {
        return measureflag;
    }
    public void setFsflag(Long fsflag) 
    {
        this.fsflag = fsflag;
    }

    public Long getFsflag() 
    {
        return fsflag;
    }
    public void setSqflag(Long sqflag) 
    {
        this.sqflag = sqflag;
    }

    public Long getSqflag() 
    {
        return sqflag;
    }
    public void setSqleader(String sqleader) 
    {
        this.sqleader = sqleader;
    }

    public String getSqleader() 
    {
        return sqleader;
    }
    public void setSqlmemo(String sqlmemo) 
    {
        this.sqlmemo = sqlmemo;
    }

    public String getSqlmemo() 
    {
        return sqlmemo;
    }
    public void setSqldate(Date sqldate) 
    {
        this.sqldate = sqldate;
    }

    public Date getSqldate() 
    {
        return sqldate;
    }
    public void setFgflag(Long fgflag) 
    {
        this.fgflag = fgflag;
    }

    public Long getFgflag() 
    {
        return fgflag;
    }
    public void setFgleader(String fgleader) 
    {
        this.fgleader = fgleader;
    }

    public String getFgleader() 
    {
        return fgleader;
    }
    public void setFglmemo(String fglmemo) 
    {
        this.fglmemo = fglmemo;
    }

    public String getFglmemo() 
    {
        return fglmemo;
    }
    public void setFgldate(Date fgldate) 
    {
        this.fgldate = fgldate;
    }

    public Date getFgldate() 
    {
        return fgldate;
    }
    public void setSbflag(Long sbflag) 
    {
        this.sbflag = sbflag;
    }

    public Long getSbflag() 
    {
        return sbflag;
    }
    public void setSbleader(String sbleader) 
    {
        this.sbleader = sbleader;
    }

    public String getSbleader() 
    {
        return sbleader;
    }
    public void setSblmemo(String sblmemo) 
    {
        this.sblmemo = sblmemo;
    }

    public String getSblmemo() 
    {
        return sblmemo;
    }
    public void setSbldate(Date sbldate) 
    {
        this.sbldate = sbldate;
    }

    public Date getSbldate() 
    {
        return sbldate;
    }
    public void setSyflag(Long syflag) 
    {
        this.syflag = syflag;
    }

    public Long getSyflag() 
    {
        return syflag;
    }
    public void setSyleader(String syleader) 
    {
        this.syleader = syleader;
    }

    public String getSyleader() 
    {
        return syleader;
    }
    public void setSylmemo(String sylmemo) 
    {
        this.sylmemo = sylmemo;
    }

    public String getSylmemo() 
    {
        return sylmemo;
    }
    public void setSyldate(Date syldate) 
    {
        this.syldate = syldate;
    }

    public Date getSyldate() 
    {
        return syldate;
    }
    public void setHsflag(Long hsflag) 
    {
        this.hsflag = hsflag;
    }

    public Long getHsflag() 
    {
        return hsflag;
    }
    public void setShmore(Long shmore) 
    {
        this.shmore = shmore;
    }

    public Long getShmore() 
    {
        return shmore;
    }
    public void setYxqmemo(String yxqmemo) 
    {
        this.yxqmemo = yxqmemo;
    }

    public String getYxqmemo() 
    {
        return yxqmemo;
    }
    public void setOperatype(BigDecimal operatype) 
    {
        this.operatype = operatype;
    }

    public BigDecimal getOperatype() 
    {
        return operatype;
    }
    public void setCancelmemo(String cancelmemo) 
    {
        this.cancelmemo = cancelmemo;
    }

    public String getCancelmemo() 
    {
        return cancelmemo;
    }
    public void setSxtime(Date sxtime) 
    {
        this.sxtime = sxtime;
    }

    public Date getSxtime() 
    {
        return sxtime;
    }
    public void setPrintflag(Long printflag) 
    {
        this.printflag = printflag;
    }

    public Long getPrintflag() 
    {
        return printflag;
    }
    public void setPrintflagdate(Date printflagdate) 
    {
        this.printflagdate = printflagdate;
    }

    public Date getPrintflagdate() 
    {
        return printflagdate;
    }
    public void setBdmemo(String bdmemo) 
    {
        this.bdmemo = bdmemo;
    }

    public String getBdmemo() 
    {
        return bdmemo;
    }
    public void setMtype(String mtype) 
    {
        this.mtype = mtype;
    }

    public String getMtype() 
    {
        return mtype;
    }
    public void setUptime(Date uptime) 
    {
        this.uptime = uptime;
    }

    public Date getUptime() 
    {
        return uptime;
    }
    public void setUptimestamp(Date uptimestamp) 
    {
        this.uptimestamp = uptimestamp;
    }

    public Date getUptimestamp() 
    {
        return uptimestamp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("validflag", getValidflag())
            .append("materialcode", getMaterialcode())
            .append("materialname", getMaterialname())
            .append("materialspec", getMaterialspec())
            .append("materialtype", getMaterialtype())
            .append("sourcecode", getSourcecode())
            .append("sourcename", getSourcename())
            .append("targetcode", getTargetcode())
            .append("targetname", getTargetname())
            .append("count", getCount())
            .append("weight", getWeight())
            .append("unit", getUnit())
            .append("validtime", getValidtime())
            .append("unitcode", getUnitcode())
            .append("unitname", getUnitname())
            .append("lflag", getLflag())
            .append("leader", getLeader())
            .append("ldate", getLdate())
            .append("pflag", getPflag())
            .append("productman", getProductman())
            .append("pdate", getPdate())
            .append("printnum", getPrintnum())
            .append("createman", getCreateman())
            .append("createdate", getCreatedate())
            .append("updateman", getUpdateman())
            .append("updatedate", getUpdatedate())
            .append("memo", getMemo())
            .append("leadermemo", getLeadermemo())
            .append("productmemo", getProductmemo())
            .append("carno", getCarno())
            .append("attachpath", getAttachpath())
            .append("leaveman", getLeaveman())
            .append("leavedate", getLeavedate())
            .append("leavegate", getLeavegate())
            .append("supervisor", getSupervisor())
            .append("outmemo", getOutmemo())
            .append("printnump", getPrintnump())
            .append("cmzhao", getCmzhao())
            .append("yxflag", getYxflag())
            .append("yxday", getYxday())
            .append("wzzgy", getWzzgy())
            .append("applyid", getApplyid())
            .append("measureflag", getMeasureflag())
            .append("fsflag", getFsflag())
            .append("sqflag", getSqflag())
            .append("sqleader", getSqleader())
            .append("sqlmemo", getSqlmemo())
            .append("sqldate", getSqldate())
            .append("fgflag", getFgflag())
            .append("fgleader", getFgleader())
            .append("fglmemo", getFglmemo())
            .append("fgldate", getFgldate())
            .append("sbflag", getSbflag())
            .append("sbleader", getSbleader())
            .append("sblmemo", getSblmemo())
            .append("sbldate", getSbldate())
            .append("syflag", getSyflag())
            .append("syleader", getSyleader())
            .append("sylmemo", getSylmemo())
            .append("syldate", getSyldate())
            .append("hsflag", getHsflag())
            .append("shmore", getShmore())
            .append("yxqmemo", getYxqmemo())
            .append("operatype", getOperatype())
            .append("cancelmemo", getCancelmemo())
            .append("sxtime", getSxtime())
            .append("printflag", getPrintflag())
            .append("printflagdate", getPrintflagdate())
            .append("bdmemo", getBdmemo())
            .append("mtype", getMtype())
            .append("uptime", getUptime())
            .append("uptimestamp", getUptimestamp())
            .toString();
    }
}
