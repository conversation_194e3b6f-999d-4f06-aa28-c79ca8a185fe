package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 跨区调拨主对象 L_CARPLAN_T
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public class LCarplanT extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 1是开始，2出厂，3进厂，8完成，0申请作废，9过期状态，10出门证作废 */
    @Excel(name = "1是开始，2出厂，3进厂，8完成，0申请作废，9过期状态，10出门证作废")
    private Long validflag;

    /** 0+id,默认12位（不足12自动补充） */
    @Excel(name = "0+id,默认12位", readConverterExp = "不=足12自动补充")
    private String planid;

    /** 计划类型：6跨区调拨 */
    @Excel(name = "计划类型：6跨区调拨")
    private Long operatype;

    /** 发货库房编码 */
    @Excel(name = "发货库房编码")
    private String storecode;

    /** 发货库房 */
    @Excel(name = "发货库房")
    private String storename;

    /** 发货库房库位 */
    @Excel(name = "发货库房库位")
    private String storepos;

    /** 收货库房编码 */
    @Excel(name = "收货库房编码")
    private String targetcode;

    /** 收货库房 */
    @Excel(name = "收货库房")
    private String targetname;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialcode;

    /** 物料 */
    @Excel(name = "物料")
    private String materialname;

    /** 规格 */
    @Excel(name = "规格")
    private String materialspeccode;

    /** 物料规格 */
    @Excel(name = "物料规格")
    private String materialspec;

    /** 型号 */
    @Excel(name = "型号")
    private String materialtype;

    /** 运输单位（根据卡上的单位） */
    @Excel(name = "运输单位", readConverterExp = "根=据卡上的单位")
    private String transitunit;

    /** 计划开始时间（默认当天，带分） */
    @Excel(name = "计划开始时间", readConverterExp = "默=认当天，带分")
    private Date begintime;

    /** 计划结束时间（默认当天）（根据时间自动完成） */
    @Excel(name = "计划结束时间", readConverterExp = "默=认当天")
    private Date endtime;

    /** 炉号 */
    @Excel(name = "炉号")
    private String heatno;

    /** 钢级 */
    @Excel(name = "钢级")
    private String steellevel;

    /** 钢种 */
    @Excel(name = "钢种")
    private String steelgrade;

    /** 计划量 */
    @Excel(name = "计划量")
    private BigDecimal planamount;

    /** 计划到期自动结束 */
    @Excel(name = "计划到期自动结束")
    private Long autoendflag;

    /** 备注信息 */
    @Excel(name = "备注信息")
    private String memo;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createman;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdate;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updateman;

    /** 更新时间 */
    @Excel(name = "更新时间")
    private String updatetime;

    /** 领导审核：0不通过，1未审核，2通过 */
    @Excel(name = "领导审核：0不通过，1未审核，2通过")
    private Long lflag;

    /** 领导 */
    @Excel(name = "领导")
    private String leader;

    /** 领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ldate;

    /** 生产中心审核：0不通过，1未审核，2通过 */
    @Excel(name = "生产中心审核：0不通过，1未审核，2通过")
    private Long pflag;

    /** 生产中心审核人 */
    @Excel(name = "生产中心审核人")
    private String productman;

    /** 生产中心审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生产中心审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pdate;

    /** 打印次数 */
    @Excel(name = "打印次数")
    private Long printnum;

    /** 领导意见 */
    @Excel(name = "领导意见")
    private String leadermemo;

    /** 生产中心意见 */
    @Excel(name = "生产中心意见")
    private String productmemo;

    /** 数量 */
    @Excel(name = "数量")
    private String count;

    /** 重量 */
    @Excel(name = "重量")
    private BigDecimal weight;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validtime;

    /** 出厂人 */
    @Excel(name = "出厂人")
    private String leaveman;

    /** 出厂日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出厂日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date leavedate;

    /** 出厂大门 */
    @Excel(name = "出厂大门")
    private String leavegate;

    /** 进厂人 */
    @Excel(name = "进厂人")
    private String enterman;

    /** 进厂时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "进厂时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date enterdate;

    /** 进厂大门 */
    @Excel(name = "进厂大门")
    private String entergate;

    /** 监装人 */
    @Excel(name = "监装人")
    private String supervisor;

    /** 出厂原因 */
    @Excel(name = "出厂原因")
    private String outmemo;

    /** 收货截止天数 */
    @Excel(name = "收货截止天数")
    private Long targetdeadline;

    /** 车号 */
    @Excel(name = "车号")
    private String carno;

    /** 附件路径 */
    @Excel(name = "附件路径")
    private String attachpath;

    /** 计量标记：1月计划计量，0不计量,2短期计量3：单厂区计量 */
    @Excel(name = "计量标记：1月计划计量，0不计量,2短期计量3：单厂区计量")
    private Long measureflag;

    /** 生产中心打印次数 */
    @Excel(name = "生产中心打印次数")
    private Long printnump;

    /** 默认为0,9为过期，10为警告 */
    @Excel(name = "默认为0,9为过期，10为警告")
    private Long yxflag;

    /** （针对不计量）有效天数：默认为3，还设定4,5,6天 */
    @Excel(name = "", readConverterExp = "针=对不计量")
    private Long yxday;

    /** 物资专管员 */
    @Excel(name = "物资专管员")
    private String wzzgy;

    /** 申请号（年份2位）+5位数字 */
    @Excel(name = "申请号", readConverterExp = "年=份2位")
    private String applyid;

    /** 0一般物资1特殊物资 */
    @Excel(name = "0一般物资1特殊物资")
    private Long wzflag;

    /** 0不加工，1加工未复审核，2通过，3不通过 */
    @Excel(name = "0不加工，1加工未复审核，2通过，3不通过")
    private Long jgflag;

    /** 加工领导 */
    @Excel(name = "加工领导")
    private String jgleader;

    /** 加工领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "加工领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date jgldate;

    /** 加工领导审核意见 */
    @Excel(name = "加工领导审核意见")
    private String jglmemo;

    /** 是否需要复审0不需要，1三期领导审，2，废钢供应领导审，3，设备管理领导复审，4式样领导复审 */
    @Excel(name = "是否需要复审0不需要，1三期领导审，2，废钢供应领导审，3，设备管理领导复审，4式样领导复审")
    private Long fsflag;

    /** 0不需审核，1未审核，2审核通过，3未通过 */
    @Excel(name = "0不需审核，1未审核，2审核通过，3未通过")
    private Long sqflag;

    /** 三期领导 */
    @Excel(name = "三期领导")
    private String sqleader;

    /** 三期领导意见 */
    @Excel(name = "三期领导意见")
    private String sqlmemo;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sqldate;

    /** 废钢领导：0不需审核，1未审核，2审核通过，3未通过 */
    @Excel(name = "废钢领导：0不需审核，1未审核，2审核通过，3未通过")
    private Long fgflag;

    /** 废钢供应领导 */
    @Excel(name = "废钢供应领导")
    private String fgleader;

    /** 废钢供应领导意见 */
    @Excel(name = "废钢供应领导意见")
    private String fglmemo;

    /** 废钢供应领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "废钢供应领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date fgldate;

    /** 设备领导审核：0不需审核，1未审核，2审核通过，3未通过 */
    @Excel(name = "设备领导审核：0不需审核，1未审核，2审核通过，3未通过")
    private Long sbflag;

    /** 设备领导 */
    @Excel(name = "设备领导")
    private String sbleader;

    /** 设备领导意见 */
    @Excel(name = "设备领导意见")
    private String sblmemo;

    /** 设备领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "设备领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sbldate;

    /** 试样领导审核：0不需审核，1未审核，2审核通过，3未通过 */
    @Excel(name = "试样领导审核：0不需审核，1未审核，2审核通过，3未通过")
    private Long syflag;

    /** 试样领导 */
    @Excel(name = "试样领导")
    private String syleader;

    /** 试样领导意见 */
    @Excel(name = "试样领导意见")
    private String sylmemo;

    /** 试样领导审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "试样领导审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date syldate;

    /** 0表示滨江厂区1表示花山厂区 */
    @Excel(name = "0表示滨江厂区1表示花山厂区")
    private Long hsflag;

    /** 0表示不可以多审核，1表示需要多审核 */
    @Excel(name = "0表示不可以多审核，1表示需要多审核")
    private Long shmore;

    /** 修改有效期原原因 */
    @Excel(name = "修改有效期原原因")
    private String yxqmemo;

    /** 临时使用 */
    @Excel(name = "临时使用")
    private BigDecimal countls;

    /** 门卫修正人：门卫点击错误后，信息恢复 */
    @Excel(name = "门卫修正人：门卫点击错误后，信息恢复")
    private String mwxzman;

    /** 门卫修正修正时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "门卫修正修正时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date mwxzdate;

    /** 手动完成人 */
    @Excel(name = "手动完成人")
    private String sdwcman;

    /** 手动完成原因 */
    @Excel(name = "手动完成原因")
    private String sdwcmemo;

    /** 手动完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "手动完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sdwcdate;

    /** 出门证作废原因 */
    @Excel(name = "出门证作废原因")
    private String cancelmemo;

    /** 更新时间戳 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间戳", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uptimestamp;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uptime;

    /** 0可以打印1不可以打印 */
    @Excel(name = "0可以打印1不可以打印")
    private Long printflag;

    /** 审核补打时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核补打时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date printflagdate;

    /** 申请补打原因 */
    @Excel(name = "申请补打原因")
    private String bdmemo;

    /** 物流物资名称 */
    @Excel(name = "物流物资名称")
    private String wlmaterialname;

    /** 1是开始，2出厂，3进厂，8完成，0申请作废，9过期状态，10出门证作废 */
    @Excel(name = "1是开始，2出厂，3进厂，8完成，0申请作废，9过期状态，10出门证作废")
    private Long oldValidflag;

    /** 卸货人 */
    @Excel(name = "卸货人")
    private String receiveman;

    /** 卸货单位 */
    @Excel(name = "卸货单位")
    private String receiveunit;

    /** 卸货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "卸货时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receivedate;

    /** 材料类型 */
    @Excel(name = "材料类型")
    private String mtype;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setValidflag(Long validflag) 
    {
        this.validflag = validflag;
    }

    public Long getValidflag() 
    {
        return validflag;
    }
    public void setPlanid(String planid) 
    {
        this.planid = planid;
    }

    public String getPlanid() 
    {
        return planid;
    }
    public void setOperatype(Long operatype) 
    {
        this.operatype = operatype;
    }

    public Long getOperatype() 
    {
        return operatype;
    }
    public void setStorecode(String storecode) 
    {
        this.storecode = storecode;
    }

    public String getStorecode() 
    {
        return storecode;
    }
    public void setStorename(String storename) 
    {
        this.storename = storename;
    }

    public String getStorename() 
    {
        return storename;
    }
    public void setStorepos(String storepos) 
    {
        this.storepos = storepos;
    }

    public String getStorepos() 
    {
        return storepos;
    }
    public void setTargetcode(String targetcode) 
    {
        this.targetcode = targetcode;
    }

    public String getTargetcode() 
    {
        return targetcode;
    }
    public void setTargetname(String targetname) 
    {
        this.targetname = targetname;
    }

    public String getTargetname() 
    {
        return targetname;
    }
    public void setMaterialcode(String materialcode) 
    {
        this.materialcode = materialcode;
    }

    public String getMaterialcode() 
    {
        return materialcode;
    }
    public void setMaterialname(String materialname) 
    {
        this.materialname = materialname;
    }

    public String getMaterialname() 
    {
        return materialname;
    }
    public void setMaterialspeccode(String materialspeccode) 
    {
        this.materialspeccode = materialspeccode;
    }

    public String getMaterialspeccode() 
    {
        return materialspeccode;
    }
    public void setMaterialspec(String materialspec) 
    {
        this.materialspec = materialspec;
    }

    public String getMaterialspec() 
    {
        return materialspec;
    }
    public void setMaterialtype(String materialtype) 
    {
        this.materialtype = materialtype;
    }

    public String getMaterialtype() 
    {
        return materialtype;
    }
    public void setTransitunit(String transitunit) 
    {
        this.transitunit = transitunit;
    }

    public String getTransitunit() 
    {
        return transitunit;
    }
    public void setBegintime(Date begintime) 
    {
        this.begintime = begintime;
    }

    public Date getBegintime() 
    {
        return begintime;
    }
    public void setEndtime(Date endtime) 
    {
        this.endtime = endtime;
    }

    public Date getEndtime() 
    {
        return endtime;
    }
    public void setHeatno(String heatno) 
    {
        this.heatno = heatno;
    }

    public String getHeatno() 
    {
        return heatno;
    }
    public void setSteellevel(String steellevel) 
    {
        this.steellevel = steellevel;
    }

    public String getSteellevel() 
    {
        return steellevel;
    }
    public void setSteelgrade(String steelgrade) 
    {
        this.steelgrade = steelgrade;
    }

    public String getSteelgrade() 
    {
        return steelgrade;
    }
    public void setPlanamount(BigDecimal planamount) 
    {
        this.planamount = planamount;
    }

    public BigDecimal getPlanamount() 
    {
        return planamount;
    }
    public void setAutoendflag(Long autoendflag) 
    {
        this.autoendflag = autoendflag;
    }

    public Long getAutoendflag() 
    {
        return autoendflag;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCreateman(String createman) 
    {
        this.createman = createman;
    }

    public String getCreateman() 
    {
        return createman;
    }
    public void setCreatedate(Date createdate) 
    {
        this.createdate = createdate;
    }

    public Date getCreatedate() 
    {
        return createdate;
    }
    public void setUpdateman(String updateman) 
    {
        this.updateman = updateman;
    }

    public String getUpdateman() 
    {
        return updateman;
    }

    public void setUpdatetime(String updatetime)
    {
        this.updatetime = updatetime;
    }

    public String getUpdatetime()
    {
        return updatetime;
    }
    public void setLflag(Long lflag) 
    {
        this.lflag = lflag;
    }

    public Long getLflag() 
    {
        return lflag;
    }
    public void setLeader(String leader) 
    {
        this.leader = leader;
    }

    public String getLeader() 
    {
        return leader;
    }
    public void setLdate(Date ldate) 
    {
        this.ldate = ldate;
    }

    public Date getLdate() 
    {
        return ldate;
    }
    public void setPflag(Long pflag) 
    {
        this.pflag = pflag;
    }

    public Long getPflag() 
    {
        return pflag;
    }
    public void setProductman(String productman) 
    {
        this.productman = productman;
    }

    public String getProductman() 
    {
        return productman;
    }
    public void setPdate(Date pdate) 
    {
        this.pdate = pdate;
    }

    public Date getPdate() 
    {
        return pdate;
    }
    public void setPrintnum(Long printnum) 
    {
        this.printnum = printnum;
    }

    public Long getPrintnum() 
    {
        return printnum;
    }
    public void setLeadermemo(String leadermemo) 
    {
        this.leadermemo = leadermemo;
    }

    public String getLeadermemo() 
    {
        return leadermemo;
    }
    public void setProductmemo(String productmemo) 
    {
        this.productmemo = productmemo;
    }

    public String getProductmemo() 
    {
        return productmemo;
    }
    public void setCount(String count) 
    {
        this.count = count;
    }

    public String getCount() 
    {
        return count;
    }
    public void setWeight(BigDecimal weight) 
    {
        this.weight = weight;
    }

    public BigDecimal getWeight() 
    {
        return weight;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setValidtime(Date validtime) 
    {
        this.validtime = validtime;
    }

    public Date getValidtime() 
    {
        return validtime;
    }
    public void setLeaveman(String leaveman) 
    {
        this.leaveman = leaveman;
    }

    public String getLeaveman() 
    {
        return leaveman;
    }
    public void setLeavedate(Date leavedate) 
    {
        this.leavedate = leavedate;
    }

    public Date getLeavedate() 
    {
        return leavedate;
    }
    public void setLeavegate(String leavegate) 
    {
        this.leavegate = leavegate;
    }

    public String getLeavegate() 
    {
        return leavegate;
    }
    public void setEnterman(String enterman) 
    {
        this.enterman = enterman;
    }

    public String getEnterman() 
    {
        return enterman;
    }
    public void setEnterdate(Date enterdate) 
    {
        this.enterdate = enterdate;
    }

    public Date getEnterdate() 
    {
        return enterdate;
    }
    public void setEntergate(String entergate) 
    {
        this.entergate = entergate;
    }

    public String getEntergate() 
    {
        return entergate;
    }
    public void setSupervisor(String supervisor) 
    {
        this.supervisor = supervisor;
    }

    public String getSupervisor() 
    {
        return supervisor;
    }
    public void setOutmemo(String outmemo) 
    {
        this.outmemo = outmemo;
    }

    public String getOutmemo() 
    {
        return outmemo;
    }
    public void setTargetdeadline(Long targetdeadline) 
    {
        this.targetdeadline = targetdeadline;
    }

    public Long getTargetdeadline() 
    {
        return targetdeadline;
    }
    public void setCarno(String carno) 
    {
        this.carno = carno;
    }

    public String getCarno() 
    {
        return carno;
    }
    public void setAttachpath(String attachpath) 
    {
        this.attachpath = attachpath;
    }

    public String getAttachpath() 
    {
        return attachpath;
    }
    public void setMeasureflag(Long measureflag) 
    {
        this.measureflag = measureflag;
    }

    public Long getMeasureflag() 
    {
        return measureflag;
    }
    public void setPrintnump(Long printnump) 
    {
        this.printnump = printnump;
    }

    public Long getPrintnump() 
    {
        return printnump;
    }
    public void setYxflag(Long yxflag) 
    {
        this.yxflag = yxflag;
    }

    public Long getYxflag() 
    {
        return yxflag;
    }
    public void setYxday(Long yxday) 
    {
        this.yxday = yxday;
    }

    public Long getYxday() 
    {
        return yxday;
    }
    public void setWzzgy(String wzzgy) 
    {
        this.wzzgy = wzzgy;
    }

    public String getWzzgy() 
    {
        return wzzgy;
    }
    public void setApplyid(String applyid) 
    {
        this.applyid = applyid;
    }

    public String getApplyid() 
    {
        return applyid;
    }
    public void setWzflag(Long wzflag) 
    {
        this.wzflag = wzflag;
    }

    public Long getWzflag() 
    {
        return wzflag;
    }
    public void setJgflag(Long jgflag) 
    {
        this.jgflag = jgflag;
    }

    public Long getJgflag() 
    {
        return jgflag;
    }
    public void setJgleader(String jgleader) 
    {
        this.jgleader = jgleader;
    }

    public String getJgleader() 
    {
        return jgleader;
    }
    public void setJgldate(Date jgldate) 
    {
        this.jgldate = jgldate;
    }

    public Date getJgldate() 
    {
        return jgldate;
    }
    public void setJglmemo(String jglmemo) 
    {
        this.jglmemo = jglmemo;
    }

    public String getJglmemo() 
    {
        return jglmemo;
    }
    public void setFsflag(Long fsflag) 
    {
        this.fsflag = fsflag;
    }

    public Long getFsflag() 
    {
        return fsflag;
    }
    public void setSqflag(Long sqflag) 
    {
        this.sqflag = sqflag;
    }

    public Long getSqflag() 
    {
        return sqflag;
    }
    public void setSqleader(String sqleader) 
    {
        this.sqleader = sqleader;
    }

    public String getSqleader() 
    {
        return sqleader;
    }
    public void setSqlmemo(String sqlmemo) 
    {
        this.sqlmemo = sqlmemo;
    }

    public String getSqlmemo() 
    {
        return sqlmemo;
    }
    public void setSqldate(Date sqldate) 
    {
        this.sqldate = sqldate;
    }

    public Date getSqldate() 
    {
        return sqldate;
    }
    public void setFgflag(Long fgflag) 
    {
        this.fgflag = fgflag;
    }

    public Long getFgflag() 
    {
        return fgflag;
    }
    public void setFgleader(String fgleader) 
    {
        this.fgleader = fgleader;
    }

    public String getFgleader() 
    {
        return fgleader;
    }
    public void setFglmemo(String fglmemo) 
    {
        this.fglmemo = fglmemo;
    }

    public String getFglmemo() 
    {
        return fglmemo;
    }
    public void setFgldate(Date fgldate) 
    {
        this.fgldate = fgldate;
    }

    public Date getFgldate() 
    {
        return fgldate;
    }
    public void setSbflag(Long sbflag) 
    {
        this.sbflag = sbflag;
    }

    public Long getSbflag() 
    {
        return sbflag;
    }
    public void setSbleader(String sbleader) 
    {
        this.sbleader = sbleader;
    }

    public String getSbleader() 
    {
        return sbleader;
    }
    public void setSblmemo(String sblmemo) 
    {
        this.sblmemo = sblmemo;
    }

    public String getSblmemo() 
    {
        return sblmemo;
    }
    public void setSbldate(Date sbldate) 
    {
        this.sbldate = sbldate;
    }

    public Date getSbldate() 
    {
        return sbldate;
    }
    public void setSyflag(Long syflag) 
    {
        this.syflag = syflag;
    }

    public Long getSyflag() 
    {
        return syflag;
    }
    public void setSyleader(String syleader) 
    {
        this.syleader = syleader;
    }

    public String getSyleader() 
    {
        return syleader;
    }
    public void setSylmemo(String sylmemo) 
    {
        this.sylmemo = sylmemo;
    }

    public String getSylmemo() 
    {
        return sylmemo;
    }
    public void setSyldate(Date syldate) 
    {
        this.syldate = syldate;
    }

    public Date getSyldate() 
    {
        return syldate;
    }
    public void setHsflag(Long hsflag) 
    {
        this.hsflag = hsflag;
    }

    public Long getHsflag() 
    {
        return hsflag;
    }
    public void setShmore(Long shmore) 
    {
        this.shmore = shmore;
    }

    public Long getShmore() 
    {
        return shmore;
    }
    public void setYxqmemo(String yxqmemo) 
    {
        this.yxqmemo = yxqmemo;
    }

    public String getYxqmemo() 
    {
        return yxqmemo;
    }
    public void setCountls(BigDecimal countls) 
    {
        this.countls = countls;
    }

    public BigDecimal getCountls() 
    {
        return countls;
    }
    public void setMwxzman(String mwxzman) 
    {
        this.mwxzman = mwxzman;
    }

    public String getMwxzman() 
    {
        return mwxzman;
    }
    public void setMwxzdate(Date mwxzdate) 
    {
        this.mwxzdate = mwxzdate;
    }

    public Date getMwxzdate() 
    {
        return mwxzdate;
    }
    public void setSdwcman(String sdwcman) 
    {
        this.sdwcman = sdwcman;
    }

    public String getSdwcman() 
    {
        return sdwcman;
    }
    public void setSdwcmemo(String sdwcmemo) 
    {
        this.sdwcmemo = sdwcmemo;
    }

    public String getSdwcmemo() 
    {
        return sdwcmemo;
    }
    public void setSdwcdate(Date sdwcdate) 
    {
        this.sdwcdate = sdwcdate;
    }

    public Date getSdwcdate() 
    {
        return sdwcdate;
    }
    public void setCancelmemo(String cancelmemo) 
    {
        this.cancelmemo = cancelmemo;
    }

    public String getCancelmemo() 
    {
        return cancelmemo;
    }
    public void setUptimestamp(Date uptimestamp) 
    {
        this.uptimestamp = uptimestamp;
    }

    public Date getUptimestamp() 
    {
        return uptimestamp;
    }
    public void setUptime(Date uptime) 
    {
        this.uptime = uptime;
    }

    public Date getUptime() 
    {
        return uptime;
    }
    public void setPrintflag(Long printflag) 
    {
        this.printflag = printflag;
    }

    public Long getPrintflag() 
    {
        return printflag;
    }
    public void setPrintflagdate(Date printflagdate) 
    {
        this.printflagdate = printflagdate;
    }

    public Date getPrintflagdate() 
    {
        return printflagdate;
    }
    public void setBdmemo(String bdmemo) 
    {
        this.bdmemo = bdmemo;
    }

    public String getBdmemo() 
    {
        return bdmemo;
    }
    public void setWlmaterialname(String wlmaterialname) 
    {
        this.wlmaterialname = wlmaterialname;
    }

    public String getWlmaterialname() 
    {
        return wlmaterialname;
    }
    public void setOldValidflag(Long oldValidflag) 
    {
        this.oldValidflag = oldValidflag;
    }

    public Long getOldValidflag() 
    {
        return oldValidflag;
    }
    public void setReceiveman(String receiveman) 
    {
        this.receiveman = receiveman;
    }

    public String getReceiveman() 
    {
        return receiveman;
    }
    public void setReceiveunit(String receiveunit) 
    {
        this.receiveunit = receiveunit;
    }

    public String getReceiveunit() 
    {
        return receiveunit;
    }
    public void setReceivedate(Date receivedate) 
    {
        this.receivedate = receivedate;
    }

    public Date getReceivedate() 
    {
        return receivedate;
    }
    public void setMtype(String mtype) 
    {
        this.mtype = mtype;
    }

    public String getMtype() 
    {
        return mtype;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("validflag", getValidflag())
            .append("planid", getPlanid())
            .append("operatype", getOperatype())
            .append("storecode", getStorecode())
            .append("storename", getStorename())
            .append("storepos", getStorepos())
            .append("targetcode", getTargetcode())
            .append("targetname", getTargetname())
            .append("materialcode", getMaterialcode())
            .append("materialname", getMaterialname())
            .append("materialspeccode", getMaterialspeccode())
            .append("materialspec", getMaterialspec())
            .append("materialtype", getMaterialtype())
            .append("transitunit", getTransitunit())
            .append("begintime", getBegintime())
            .append("endtime", getEndtime())
            .append("heatno", getHeatno())
            .append("steellevel", getSteellevel())
            .append("steelgrade", getSteelgrade())
            .append("planamount", getPlanamount())
            .append("autoendflag", getAutoendflag())
            .append("memo", getMemo())
            .append("createman", getCreateman())
            .append("createdate", getCreatedate())
            .append("updateman", getUpdateman())
            .append("lflag", getLflag())
            .append("leader", getLeader())
            .append("ldate", getLdate())
            .append("pflag", getPflag())
            .append("productman", getProductman())
            .append("pdate", getPdate())
            .append("printnum", getPrintnum())
            .append("leadermemo", getLeadermemo())
            .append("productmemo", getProductmemo())
            .append("count", getCount())
            .append("weight", getWeight())
            .append("unit", getUnit())
            .append("validtime", getValidtime())
            .append("leaveman", getLeaveman())
            .append("leavedate", getLeavedate())
            .append("leavegate", getLeavegate())
            .append("enterman", getEnterman())
            .append("enterdate", getEnterdate())
            .append("entergate", getEntergate())
            .append("supervisor", getSupervisor())
            .append("outmemo", getOutmemo())
            .append("targetdeadline", getTargetdeadline())
            .append("carno", getCarno())
            .append("attachpath", getAttachpath())
            .append("measureflag", getMeasureflag())
            .append("printnump", getPrintnump())
            .append("yxflag", getYxflag())
            .append("yxday", getYxday())
            .append("wzzgy", getWzzgy())
            .append("applyid", getApplyid())
            .append("wzflag", getWzflag())
            .append("jgflag", getJgflag())
            .append("jgleader", getJgleader())
            .append("jgldate", getJgldate())
            .append("jglmemo", getJglmemo())
            .append("fsflag", getFsflag())
            .append("sqflag", getSqflag())
            .append("sqleader", getSqleader())
            .append("sqlmemo", getSqlmemo())
            .append("sqldate", getSqldate())
            .append("fgflag", getFgflag())
            .append("fgleader", getFgleader())
            .append("fglmemo", getFglmemo())
            .append("fgldate", getFgldate())
            .append("sbflag", getSbflag())
            .append("sbleader", getSbleader())
            .append("sblmemo", getSblmemo())
            .append("sbldate", getSbldate())
            .append("syflag", getSyflag())
            .append("syleader", getSyleader())
            .append("sylmemo", getSylmemo())
            .append("syldate", getSyldate())
            .append("hsflag", getHsflag())
            .append("shmore", getShmore())
            .append("yxqmemo", getYxqmemo())
            .append("countls", getCountls())
            .append("mwxzman", getMwxzman())
            .append("mwxzdate", getMwxzdate())
            .append("sdwcman", getSdwcman())
            .append("sdwcmemo", getSdwcmemo())
            .append("sdwcdate", getSdwcdate())
            .append("cancelmemo", getCancelmemo())
            .append("uptimestamp", getUptimestamp())
            .append("uptime", getUptime())
            .append("printflag", getPrintflag())
            .append("printflagdate", getPrintflagdate())
            .append("bdmemo", getBdmemo())
            .append("wlmaterialname", getWlmaterialname())
            .append("oldValidflag", getOldValidflag())
            .append("receiveman", getReceiveman())
            .append("receiveunit", getReceiveunit())
            .append("receivedate", getReceivedate())
            .append("mtype", getMtype())
            .toString();
    }
}
