<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LCarplanTMapper">
    
    <resultMap type="LCarplanT" id="LCarplanTResult">
        <result property="id"    column="id"    />
        <result property="validflag"    column="validflag"    />
        <result property="planid"    column="planid"    />
        <result property="operatype"    column="operatype"    />
        <result property="storecode"    column="storecode"    />
        <result property="storename"    column="storename"    />
        <result property="storepos"    column="storepos"    />
        <result property="targetcode"    column="targetcode"    />
        <result property="targetname"    column="targetname"    />
        <result property="materialcode"    column="materialcode"    />
        <result property="materialname"    column="materialname"    />
        <result property="materialspeccode"    column="materialspeccode"    />
        <result property="materialspec"    column="materialspec"    />
        <result property="materialtype"    column="materialtype"    />
        <result property="transitunit"    column="transitunit"    />
        <result property="begintime"    column="begintime"    />
        <result property="endtime"    column="endtime"    />
        <result property="heatno"    column="heatno"    />
        <result property="steellevel"    column="steellevel"    />
        <result property="steelgrade"    column="steelgrade"    />
        <result property="planamount"    column="planamount"    />
        <result property="autoendflag"    column="autoendflag"    />
        <result property="memo"    column="memo"    />
        <result property="createman"    column="createman"    />
        <result property="createdate"    column="createdate"    />
        <result property="updateman"    column="updateman"    />
        <result property="updatetime"    column="updatetime"    />
        <result property="lflag"    column="lflag"    />
        <result property="leader"    column="leader"    />
        <result property="ldate"    column="ldate"    />
        <result property="pflag"    column="pflag"    />
        <result property="productman"    column="productman"    />
        <result property="pdate"    column="pdate"    />
        <result property="printnum"    column="printnum"    />
        <result property="leadermemo"    column="leadermemo"    />
        <result property="productmemo"    column="productmemo"    />
        <result property="count"    column="count"    />
        <result property="weight"    column="weight"    />
        <result property="unit"    column="unit"    />
        <result property="validtime"    column="validtime"    />
        <result property="leaveman"    column="leaveman"    />
        <result property="leavedate"    column="leavedate"    />
        <result property="leavegate"    column="leavegate"    />
        <result property="enterman"    column="enterman"    />
        <result property="enterdate"    column="enterdate"    />
        <result property="entergate"    column="entergate"    />
        <result property="supervisor"    column="supervisor"    />
        <result property="outmemo"    column="outmemo"    />
        <result property="targetdeadline"    column="targetdeadline"    />
        <result property="carno"    column="carno"    />
        <result property="attachpath"    column="attachpath"    />
        <result property="measureflag"    column="measureflag"    />
        <result property="printnump"    column="printnump"    />
        <result property="yxflag"    column="yxflag"    />
        <result property="yxday"    column="yxday"    />
        <result property="wzzgy"    column="wzzgy"    />
        <result property="applyid"    column="applyid"    />
        <result property="wzflag"    column="wzflag"    />
        <result property="jgflag"    column="jgflag"    />
        <result property="jgleader"    column="jgleader"    />
        <result property="jgldate"    column="jgldate"    />
        <result property="jglmemo"    column="jglmemo"    />
        <result property="fsflag"    column="fsflag"    />
        <result property="sqflag"    column="sqflag"    />
        <result property="sqleader"    column="sqleader"    />
        <result property="sqlmemo"    column="sqlmemo"    />
        <result property="sqldate"    column="sqldate"    />
        <result property="fgflag"    column="fgflag"    />
        <result property="fgleader"    column="fgleader"    />
        <result property="fglmemo"    column="fglmemo"    />
        <result property="fgldate"    column="fgldate"    />
        <result property="sbflag"    column="sbflag"    />
        <result property="sbleader"    column="sbleader"    />
        <result property="sblmemo"    column="sblmemo"    />
        <result property="sbldate"    column="sbldate"    />
        <result property="syflag"    column="syflag"    />
        <result property="syleader"    column="syleader"    />
        <result property="sylmemo"    column="sylmemo"    />
        <result property="syldate"    column="syldate"    />
        <result property="hsflag"    column="hsflag"    />
        <result property="shmore"    column="shmore"    />
        <result property="yxqmemo"    column="yxqmemo"    />
        <result property="countls"    column="countls"    />
        <result property="mwxzman"    column="mwxzman"    />
        <result property="mwxzdate"    column="mwxzdate"    />
        <result property="sdwcman"    column="sdwcman"    />
        <result property="sdwcmemo"    column="sdwcmemo"    />
        <result property="sdwcdate"    column="sdwcdate"    />
        <result property="cancelmemo"    column="cancelmemo"    />
        <result property="uptimestamp"    column="uptimestamp"    />
        <result property="uptime"    column="uptime"    />
        <result property="printflag"    column="printflag"    />
        <result property="printflagdate"    column="printflagdate"    />
        <result property="bdmemo"    column="bdmemo"    />
        <result property="wlmaterialname"    column="wlmaterialname"    />
        <result property="oldValidflag"    column="old_validflag"    />
        <result property="receiveman"    column="receiveman"    />
        <result property="receiveunit"    column="receiveunit"    />
        <result property="receivedate"    column="receivedate"    />
        <result property="mtype"    column="mtype"    />
    </resultMap>

    <sql id="selectLCarplanTVo">
        select id, validflag, planid, operatype, storecode, storename, storepos, targetcode, targetname, materialcode, materialname, materialspeccode, materialspec, materialtype, transitunit, begintime, endtime, heatno, steellevel, steelgrade, planamount, autoendflag, memo, createman, createdate, updateman, updatetime, lflag, leader, ldate, pflag, productman, pdate, printnum, leadermemo, productmemo, count, weight, unit, validtime, leaveman, leavedate, leavegate, enterman, enterdate, entergate, supervisor, outmemo, targetdeadline, carno, attachpath, measureflag, printnump, yxflag, yxday, wzzgy, applyid, wzflag, jgflag, jgleader, jgldate, jglmemo, fsflag, sqflag, sqleader, sqlmemo, sqldate, fgflag, fgleader, fglmemo, fgldate, sbflag, sbleader, sblmemo, sbldate, syflag, syleader, sylmemo, syldate, hsflag, shmore, yxqmemo, countls, mwxzman, mwxzdate, sdwcman, sdwcmemo, sdwcdate, cancelmemo, uptimestamp, uptime, printflag, printflagdate, bdmemo, wlmaterialname, old_validflag, receiveman, receiveunit, receivedate, mtype from L_CARPLAN_T
    </sql>

    <select id="selectLCarplanTList" parameterType="LCarplanT" resultMap="LCarplanTResult">
        <include refid="selectLCarplanTVo"/>
        <where>  
            <if test="validflag != null "> and validflag = #{validflag}</if>
            <if test="planid != null  and planid != ''"> and planid = #{planid}</if>
            <if test="operatype != null "> and operatype = #{operatype}</if>
            <if test="storecode != null  and storecode != ''"> and storecode = #{storecode}</if>
            <if test="storename != null  and storename != ''"> and storename like concat('%', #{storename}, '%')</if>
            <if test="storepos != null  and storepos != ''"> and storepos = #{storepos}</if>
            <if test="targetcode != null  and targetcode != ''"> and targetcode = #{targetcode}</if>
            <if test="targetname != null  and targetname != ''"> and targetname like concat('%', #{targetname}, '%')</if>
            <if test="materialcode != null  and materialcode != ''"> and materialcode = #{materialcode}</if>
            <if test="materialname != null  and materialname != ''"> and materialname like concat('%', #{materialname}, '%')</if>
            <if test="materialspeccode != null  and materialspeccode != ''"> and materialspeccode = #{materialspeccode}</if>
            <if test="materialspec != null  and materialspec != ''"> and materialspec = #{materialspec}</if>
            <if test="materialtype != null  and materialtype != ''"> and materialtype = #{materialtype}</if>
            <if test="transitunit != null  and transitunit != ''"> and transitunit = #{transitunit}</if>
            <if test="begintime != null "> and begintime = #{begintime}</if>
            <if test="endtime != null "> and endtime = #{endtime}</if>
            <if test="heatno != null  and heatno != ''"> and heatno = #{heatno}</if>
            <if test="steellevel != null  and steellevel != ''"> and steellevel = #{steellevel}</if>
            <if test="steelgrade != null  and steelgrade != ''"> and steelgrade = #{steelgrade}</if>
            <if test="planamount != null "> and planamount = #{planamount}</if>
            <if test="autoendflag != null "> and autoendflag = #{autoendflag}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="createman != null  and createman != ''"> and createman = #{createman}</if>
            <if test="createdate != null "> and createdate = #{createdate}</if>
            <if test="updateman != null  and updateman != ''"> and updateman = #{updateman}</if>
            <if test="updatetime != null "> and updatetime = #{updatetime}</if>
            <if test="lflag != null "> and lflag = #{lflag}</if>
            <if test="leader != null  and leader != ''"> and leader = #{leader}</if>
            <if test="ldate != null "> and ldate = #{ldate}</if>
            <if test="pflag != null "> and pflag = #{pflag}</if>
            <if test="productman != null  and productman != ''"> and productman = #{productman}</if>
            <if test="pdate != null "> and pdate = #{pdate}</if>
            <if test="printnum != null "> and printnum = #{printnum}</if>
            <if test="leadermemo != null  and leadermemo != ''"> and leadermemo = #{leadermemo}</if>
            <if test="productmemo != null  and productmemo != ''"> and productmemo = #{productmemo}</if>
            <if test="count != null  and count != ''"> and count = #{count}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="validtime != null "> and validtime = #{validtime}</if>
            <if test="leaveman != null  and leaveman != ''"> and leaveman = #{leaveman}</if>
            <if test="leavedate != null "> and leavedate = #{leavedate}</if>
            <if test="leavegate != null  and leavegate != ''"> and leavegate = #{leavegate}</if>
            <if test="enterman != null  and enterman != ''"> and enterman = #{enterman}</if>
            <if test="enterdate != null "> and enterdate = #{enterdate}</if>
            <if test="entergate != null  and entergate != ''"> and entergate = #{entergate}</if>
            <if test="supervisor != null  and supervisor != ''"> and supervisor = #{supervisor}</if>
            <if test="outmemo != null  and outmemo != ''"> and outmemo = #{outmemo}</if>
            <if test="targetdeadline != null "> and targetdeadline = #{targetdeadline}</if>
            <if test="carno != null  and carno != ''"> and carno = #{carno}</if>
            <if test="attachpath != null  and attachpath != ''"> and attachpath = #{attachpath}</if>
            <if test="measureflag != null "> and measureflag = #{measureflag}</if>
            <if test="printnump != null "> and printnump = #{printnump}</if>
            <if test="yxflag != null "> and yxflag = #{yxflag}</if>
            <if test="yxday != null "> and yxday = #{yxday}</if>
            <if test="wzzgy != null  and wzzgy != ''"> and wzzgy = #{wzzgy}</if>
            <if test="applyid != null  and applyid != ''"> and applyid = #{applyid}</if>
            <if test="wzflag != null "> and wzflag = #{wzflag}</if>
            <if test="jgflag != null "> and jgflag = #{jgflag}</if>
            <if test="jgleader != null  and jgleader != ''"> and jgleader = #{jgleader}</if>
            <if test="jgldate != null "> and jgldate = #{jgldate}</if>
            <if test="jglmemo != null  and jglmemo != ''"> and jglmemo = #{jglmemo}</if>
            <if test="fsflag != null "> and fsflag = #{fsflag}</if>
            <if test="sqflag != null "> and sqflag = #{sqflag}</if>
            <if test="sqleader != null  and sqleader != ''"> and sqleader = #{sqleader}</if>
            <if test="sqlmemo != null  and sqlmemo != ''"> and sqlmemo = #{sqlmemo}</if>
            <if test="sqldate != null "> and sqldate = #{sqldate}</if>
            <if test="fgflag != null "> and fgflag = #{fgflag}</if>
            <if test="fgleader != null  and fgleader != ''"> and fgleader = #{fgleader}</if>
            <if test="fglmemo != null  and fglmemo != ''"> and fglmemo = #{fglmemo}</if>
            <if test="fgldate != null "> and fgldate = #{fgldate}</if>
            <if test="sbflag != null "> and sbflag = #{sbflag}</if>
            <if test="sbleader != null  and sbleader != ''"> and sbleader = #{sbleader}</if>
            <if test="sblmemo != null  and sblmemo != ''"> and sblmemo = #{sblmemo}</if>
            <if test="sbldate != null "> and sbldate = #{sbldate}</if>
            <if test="syflag != null "> and syflag = #{syflag}</if>
            <if test="syleader != null  and syleader != ''"> and syleader = #{syleader}</if>
            <if test="sylmemo != null  and sylmemo != ''"> and sylmemo = #{sylmemo}</if>
            <if test="syldate != null "> and syldate = #{syldate}</if>
            <if test="hsflag != null "> and hsflag = #{hsflag}</if>
            <if test="shmore != null "> and shmore = #{shmore}</if>
            <if test="yxqmemo != null  and yxqmemo != ''"> and yxqmemo = #{yxqmemo}</if>
            <if test="countls != null "> and countls = #{countls}</if>
            <if test="mwxzman != null  and mwxzman != ''"> and mwxzman = #{mwxzman}</if>
            <if test="mwxzdate != null "> and mwxzdate = #{mwxzdate}</if>
            <if test="sdwcman != null  and sdwcman != ''"> and sdwcman = #{sdwcman}</if>
            <if test="sdwcmemo != null  and sdwcmemo != ''"> and sdwcmemo = #{sdwcmemo}</if>
            <if test="sdwcdate != null "> and sdwcdate = #{sdwcdate}</if>
            <if test="cancelmemo != null  and cancelmemo != ''"> and cancelmemo = #{cancelmemo}</if>
            <if test="uptimestamp != null "> and uptimestamp = #{uptimestamp}</if>
            <if test="uptime != null "> and uptime = #{uptime}</if>
            <if test="printflag != null "> and printflag = #{printflag}</if>
            <if test="printflagdate != null "> and printflagdate = #{printflagdate}</if>
            <if test="bdmemo != null  and bdmemo != ''"> and bdmemo = #{bdmemo}</if>
            <if test="wlmaterialname != null  and wlmaterialname != ''"> and wlmaterialname like concat('%', #{wlmaterialname}, '%')</if>
            <if test="oldValidflag != null "> and old_validflag = #{oldValidflag}</if>
            <if test="receiveman != null  and receiveman != ''"> and receiveman = #{receiveman}</if>
            <if test="receiveunit != null  and receiveunit != ''"> and receiveunit = #{receiveunit}</if>
            <if test="receivedate != null "> and receivedate = #{receivedate}</if>
            <if test="mtype != null  and mtype != ''"> and mtype = #{mtype}</if>
        </where>
    </select>
    
    <select id="selectLCarplanTById" parameterType="Long" resultMap="LCarplanTResult">
        <include refid="selectLCarplanTVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLCarplanT" parameterType="LCarplanT">
        insert into L_CARPLAN_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="validflag != null">validflag,</if>
            <if test="planid != null">planid,</if>
            <if test="operatype != null">operatype,</if>
            <if test="storecode != null">storecode,</if>
            <if test="storename != null">storename,</if>
            <if test="storepos != null">storepos,</if>
            <if test="targetcode != null">targetcode,</if>
            <if test="targetname != null">targetname,</if>
            <if test="materialcode != null">materialcode,</if>
            <if test="materialname != null">materialname,</if>
            <if test="materialspeccode != null">materialspeccode,</if>
            <if test="materialspec != null">materialspec,</if>
            <if test="materialtype != null">materialtype,</if>
            <if test="transitunit != null">transitunit,</if>
            <if test="begintime != null">begintime,</if>
            <if test="endtime != null">endtime,</if>
            <if test="heatno != null">heatno,</if>
            <if test="steellevel != null">steellevel,</if>
            <if test="steelgrade != null">steelgrade,</if>
            <if test="planamount != null">planamount,</if>
            <if test="autoendflag != null">autoendflag,</if>
            <if test="memo != null">memo,</if>
            <if test="createman != null">createman,</if>
            <if test="createdate != null">createdate,</if>
            <if test="updateman != null">updateman,</if>
            <if test="updatetime != null">updatetime,</if>
            <if test="lflag != null">lflag,</if>
            <if test="leader != null">leader,</if>
            <if test="ldate != null">ldate,</if>
            <if test="pflag != null">pflag,</if>
            <if test="productman != null">productman,</if>
            <if test="pdate != null">pdate,</if>
            <if test="printnum != null">printnum,</if>
            <if test="leadermemo != null">leadermemo,</if>
            <if test="productmemo != null">productmemo,</if>
            <if test="count != null">count,</if>
            <if test="weight != null">weight,</if>
            <if test="unit != null">unit,</if>
            <if test="validtime != null">validtime,</if>
            <if test="leaveman != null">leaveman,</if>
            <if test="leavedate != null">leavedate,</if>
            <if test="leavegate != null">leavegate,</if>
            <if test="enterman != null">enterman,</if>
            <if test="enterdate != null">enterdate,</if>
            <if test="entergate != null">entergate,</if>
            <if test="supervisor != null">supervisor,</if>
            <if test="outmemo != null">outmemo,</if>
            <if test="targetdeadline != null">targetdeadline,</if>
            <if test="carno != null">carno,</if>
            <if test="attachpath != null">attachpath,</if>
            <if test="measureflag != null">measureflag,</if>
            <if test="printnump != null">printnump,</if>
            <if test="yxflag != null">yxflag,</if>
            <if test="yxday != null">yxday,</if>
            <if test="wzzgy != null">wzzgy,</if>
            <if test="applyid != null">applyid,</if>
            <if test="wzflag != null">wzflag,</if>
            <if test="jgflag != null">jgflag,</if>
            <if test="jgleader != null">jgleader,</if>
            <if test="jgldate != null">jgldate,</if>
            <if test="jglmemo != null">jglmemo,</if>
            <if test="fsflag != null">fsflag,</if>
            <if test="sqflag != null">sqflag,</if>
            <if test="sqleader != null">sqleader,</if>
            <if test="sqlmemo != null">sqlmemo,</if>
            <if test="sqldate != null">sqldate,</if>
            <if test="fgflag != null">fgflag,</if>
            <if test="fgleader != null">fgleader,</if>
            <if test="fglmemo != null">fglmemo,</if>
            <if test="fgldate != null">fgldate,</if>
            <if test="sbflag != null">sbflag,</if>
            <if test="sbleader != null">sbleader,</if>
            <if test="sblmemo != null">sblmemo,</if>
            <if test="sbldate != null">sbldate,</if>
            <if test="syflag != null">syflag,</if>
            <if test="syleader != null">syleader,</if>
            <if test="sylmemo != null">sylmemo,</if>
            <if test="syldate != null">syldate,</if>
            <if test="hsflag != null">hsflag,</if>
            <if test="shmore != null">shmore,</if>
            <if test="yxqmemo != null">yxqmemo,</if>
            <if test="countls != null">countls,</if>
            <if test="mwxzman != null">mwxzman,</if>
            <if test="mwxzdate != null">mwxzdate,</if>
            <if test="sdwcman != null">sdwcman,</if>
            <if test="sdwcmemo != null">sdwcmemo,</if>
            <if test="sdwcdate != null">sdwcdate,</if>
            <if test="cancelmemo != null">cancelmemo,</if>
            <if test="uptimestamp != null">uptimestamp,</if>
            <if test="uptime != null">uptime,</if>
            <if test="printflag != null">printflag,</if>
            <if test="printflagdate != null">printflagdate,</if>
            <if test="bdmemo != null">bdmemo,</if>
            <if test="wlmaterialname != null">wlmaterialname,</if>
            <if test="oldValidflag != null">old_validflag,</if>
            <if test="receiveman != null">receiveman,</if>
            <if test="receiveunit != null">receiveunit,</if>
            <if test="receivedate != null">receivedate,</if>
            <if test="mtype != null">mtype,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="validflag != null">#{validflag},</if>
            <if test="planid != null">#{planid},</if>
            <if test="operatype != null">#{operatype},</if>
            <if test="storecode != null">#{storecode},</if>
            <if test="storename != null">#{storename},</if>
            <if test="storepos != null">#{storepos},</if>
            <if test="targetcode != null">#{targetcode},</if>
            <if test="targetname != null">#{targetname},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspeccode != null">#{materialspeccode},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="materialtype != null">#{materialtype},</if>
            <if test="transitunit != null">#{transitunit},</if>
            <if test="begintime != null">#{begintime},</if>
            <if test="endtime != null">#{endtime},</if>
            <if test="heatno != null">#{heatno},</if>
            <if test="steellevel != null">#{steellevel},</if>
            <if test="steelgrade != null">#{steelgrade},</if>
            <if test="planamount != null">#{planamount},</if>
            <if test="autoendflag != null">#{autoendflag},</if>
            <if test="memo != null">#{memo},</if>
            <if test="createman != null">#{createman},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="updateman != null">#{updateman},</if>
            <if test="updatetime != null">#{updatetime},</if>
            <if test="lflag != null">#{lflag},</if>
            <if test="leader != null">#{leader},</if>
            <if test="ldate != null">#{ldate},</if>
            <if test="pflag != null">#{pflag},</if>
            <if test="productman != null">#{productman},</if>
            <if test="pdate != null">#{pdate},</if>
            <if test="printnum != null">#{printnum},</if>
            <if test="leadermemo != null">#{leadermemo},</if>
            <if test="productmemo != null">#{productmemo},</if>
            <if test="count != null">#{count},</if>
            <if test="weight != null">#{weight},</if>
            <if test="unit != null">#{unit},</if>
            <if test="validtime != null">#{validtime},</if>
            <if test="leaveman != null">#{leaveman},</if>
            <if test="leavedate != null">#{leavedate},</if>
            <if test="leavegate != null">#{leavegate},</if>
            <if test="enterman != null">#{enterman},</if>
            <if test="enterdate != null">#{enterdate},</if>
            <if test="entergate != null">#{entergate},</if>
            <if test="supervisor != null">#{supervisor},</if>
            <if test="outmemo != null">#{outmemo},</if>
            <if test="targetdeadline != null">#{targetdeadline},</if>
            <if test="carno != null">#{carno},</if>
            <if test="attachpath != null">#{attachpath},</if>
            <if test="measureflag != null">#{measureflag},</if>
            <if test="printnump != null">#{printnump},</if>
            <if test="yxflag != null">#{yxflag},</if>
            <if test="yxday != null">#{yxday},</if>
            <if test="wzzgy != null">#{wzzgy},</if>
            <if test="applyid != null">#{applyid},</if>
            <if test="wzflag != null">#{wzflag},</if>
            <if test="jgflag != null">#{jgflag},</if>
            <if test="jgleader != null">#{jgleader},</if>
            <if test="jgldate != null">#{jgldate},</if>
            <if test="jglmemo != null">#{jglmemo},</if>
            <if test="fsflag != null">#{fsflag},</if>
            <if test="sqflag != null">#{sqflag},</if>
            <if test="sqleader != null">#{sqleader},</if>
            <if test="sqlmemo != null">#{sqlmemo},</if>
            <if test="sqldate != null">#{sqldate},</if>
            <if test="fgflag != null">#{fgflag},</if>
            <if test="fgleader != null">#{fgleader},</if>
            <if test="fglmemo != null">#{fglmemo},</if>
            <if test="fgldate != null">#{fgldate},</if>
            <if test="sbflag != null">#{sbflag},</if>
            <if test="sbleader != null">#{sbleader},</if>
            <if test="sblmemo != null">#{sblmemo},</if>
            <if test="sbldate != null">#{sbldate},</if>
            <if test="syflag != null">#{syflag},</if>
            <if test="syleader != null">#{syleader},</if>
            <if test="sylmemo != null">#{sylmemo},</if>
            <if test="syldate != null">#{syldate},</if>
            <if test="hsflag != null">#{hsflag},</if>
            <if test="shmore != null">#{shmore},</if>
            <if test="yxqmemo != null">#{yxqmemo},</if>
            <if test="countls != null">#{countls},</if>
            <if test="mwxzman != null">#{mwxzman},</if>
            <if test="mwxzdate != null">#{mwxzdate},</if>
            <if test="sdwcman != null">#{sdwcman},</if>
            <if test="sdwcmemo != null">#{sdwcmemo},</if>
            <if test="sdwcdate != null">#{sdwcdate},</if>
            <if test="cancelmemo != null">#{cancelmemo},</if>
            <if test="uptimestamp != null">#{uptimestamp},</if>
            <if test="uptime != null">#{uptime},</if>
            <if test="printflag != null">#{printflag},</if>
            <if test="printflagdate != null">#{printflagdate},</if>
            <if test="bdmemo != null">#{bdmemo},</if>
            <if test="wlmaterialname != null">#{wlmaterialname},</if>
            <if test="oldValidflag != null">#{oldValidflag},</if>
            <if test="receiveman != null">#{receiveman},</if>
            <if test="receiveunit != null">#{receiveunit},</if>
            <if test="receivedate != null">#{receivedate},</if>
            <if test="mtype != null">#{mtype},</if>
         </trim>
    </insert>

    <update id="updateLCarplanT" parameterType="LCarplanT">
        update L_CARPLAN_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="validflag != null">validflag = #{validflag},</if>
            <if test="planid != null">planid = #{planid},</if>
            <if test="operatype != null">operatype = #{operatype},</if>
            <if test="storecode != null">storecode = #{storecode},</if>
            <if test="storename != null">storename = #{storename},</if>
            <if test="storepos != null">storepos = #{storepos},</if>
            <if test="targetcode != null">targetcode = #{targetcode},</if>
            <if test="targetname != null">targetname = #{targetname},</if>
            <if test="materialcode != null">materialcode = #{materialcode},</if>
            <if test="materialname != null">materialname = #{materialname},</if>
            <if test="materialspeccode != null">materialspeccode = #{materialspeccode},</if>
            <if test="materialspec != null">materialspec = #{materialspec},</if>
            <if test="materialtype != null">materialtype = #{materialtype},</if>
            <if test="transitunit != null">transitunit = #{transitunit},</if>
            <if test="begintime != null">begintime = #{begintime},</if>
            <if test="endtime != null">endtime = #{endtime},</if>
            <if test="heatno != null">heatno = #{heatno},</if>
            <if test="steellevel != null">steellevel = #{steellevel},</if>
            <if test="steelgrade != null">steelgrade = #{steelgrade},</if>
            <if test="planamount != null">planamount = #{planamount},</if>
            <if test="autoendflag != null">autoendflag = #{autoendflag},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="createman != null">createman = #{createman},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
            <if test="updateman != null">updateman = #{updateman},</if>
            <if test="updatetime != null">updatetime = #{updatetime},</if>
            <if test="lflag != null">lflag = #{lflag},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="ldate != null">ldate = #{ldate},</if>
            <if test="pflag != null">pflag = #{pflag},</if>
            <if test="productman != null">productman = #{productman},</if>
            <if test="pdate != null">pdate = #{pdate},</if>
            <if test="printnum != null">printnum = #{printnum},</if>
            <if test="leadermemo != null">leadermemo = #{leadermemo},</if>
            <if test="productmemo != null">productmemo = #{productmemo},</if>
            <if test="count != null">count = #{count},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="validtime != null">validtime = #{validtime},</if>
            <if test="leaveman != null">leaveman = #{leaveman},</if>
            <if test="leavedate != null">leavedate = #{leavedate},</if>
            <if test="leavegate != null">leavegate = #{leavegate},</if>
            <if test="enterman != null">enterman = #{enterman},</if>
            <if test="enterdate != null">enterdate = #{enterdate},</if>
            <if test="entergate != null">entergate = #{entergate},</if>
            <if test="supervisor != null">supervisor = #{supervisor},</if>
            <if test="outmemo != null">outmemo = #{outmemo},</if>
            <if test="targetdeadline != null">targetdeadline = #{targetdeadline},</if>
            <if test="carno != null">carno = #{carno},</if>
            <if test="attachpath != null">attachpath = #{attachpath},</if>
            <if test="measureflag != null">measureflag = #{measureflag},</if>
            <if test="printnump != null">printnump = #{printnump},</if>
            <if test="yxflag != null">yxflag = #{yxflag},</if>
            <if test="yxday != null">yxday = #{yxday},</if>
            <if test="wzzgy != null">wzzgy = #{wzzgy},</if>
            <if test="applyid != null">applyid = #{applyid},</if>
            <if test="wzflag != null">wzflag = #{wzflag},</if>
            <if test="jgflag != null">jgflag = #{jgflag},</if>
            <if test="jgleader != null">jgleader = #{jgleader},</if>
            <if test="jgldate != null">jgldate = #{jgldate},</if>
            <if test="jglmemo != null">jglmemo = #{jglmemo},</if>
            <if test="fsflag != null">fsflag = #{fsflag},</if>
            <if test="sqflag != null">sqflag = #{sqflag},</if>
            <if test="sqleader != null">sqleader = #{sqleader},</if>
            <if test="sqlmemo != null">sqlmemo = #{sqlmemo},</if>
            <if test="sqldate != null">sqldate = #{sqldate},</if>
            <if test="fgflag != null">fgflag = #{fgflag},</if>
            <if test="fgleader != null">fgleader = #{fgleader},</if>
            <if test="fglmemo != null">fglmemo = #{fglmemo},</if>
            <if test="fgldate != null">fgldate = #{fgldate},</if>
            <if test="sbflag != null">sbflag = #{sbflag},</if>
            <if test="sbleader != null">sbleader = #{sbleader},</if>
            <if test="sblmemo != null">sblmemo = #{sblmemo},</if>
            <if test="sbldate != null">sbldate = #{sbldate},</if>
            <if test="syflag != null">syflag = #{syflag},</if>
            <if test="syleader != null">syleader = #{syleader},</if>
            <if test="sylmemo != null">sylmemo = #{sylmemo},</if>
            <if test="syldate != null">syldate = #{syldate},</if>
            <if test="hsflag != null">hsflag = #{hsflag},</if>
            <if test="shmore != null">shmore = #{shmore},</if>
            <if test="yxqmemo != null">yxqmemo = #{yxqmemo},</if>
            <if test="countls != null">countls = #{countls},</if>
            <if test="mwxzman != null">mwxzman = #{mwxzman},</if>
            <if test="mwxzdate != null">mwxzdate = #{mwxzdate},</if>
            <if test="sdwcman != null">sdwcman = #{sdwcman},</if>
            <if test="sdwcmemo != null">sdwcmemo = #{sdwcmemo},</if>
            <if test="sdwcdate != null">sdwcdate = #{sdwcdate},</if>
            <if test="cancelmemo != null">cancelmemo = #{cancelmemo},</if>
            <if test="uptimestamp != null">uptimestamp = #{uptimestamp},</if>
            <if test="uptime != null">uptime = #{uptime},</if>
            <if test="printflag != null">printflag = #{printflag},</if>
            <if test="printflagdate != null">printflagdate = #{printflagdate},</if>
            <if test="bdmemo != null">bdmemo = #{bdmemo},</if>
            <if test="wlmaterialname != null">wlmaterialname = #{wlmaterialname},</if>
            <if test="oldValidflag != null">old_validflag = #{oldValidflag},</if>
            <if test="receiveman != null">receiveman = #{receiveman},</if>
            <if test="receiveunit != null">receiveunit = #{receiveunit},</if>
            <if test="receivedate != null">receivedate = #{receivedate},</if>
            <if test="mtype != null">mtype = #{mtype},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLCarplanTById" parameterType="Long">
        delete from L_CARPLAN_T where id = #{id}
    </delete>

    <delete id="deleteLCarplanTByIds" parameterType="String">
        delete from L_CARPLAN_T where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectByPlanid" parameterType="String" resultMap="LCarplanTResult">
        select * from l_carplan_t where planid = #{planid}
    </select>
    
    <select id="selectMaxId" resultType="Long">
        select nvl(max(id), 0) from l_carplan_t
    </select>
    
</mapper>