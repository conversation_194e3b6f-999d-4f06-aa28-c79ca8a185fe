{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\list.vue?vue&type=style&index=0&id=37ed5c67&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\list.vue", "mtime": 1756170476796}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnJlZHRleHR7CiAgY29sb3I6IHJlZDsKfQo="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiOA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/assess/self/config/user", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"工号\" prop=\"workNo\">\r\n            <el-input\r\n              v-model=\"queryParams.workNo\"\r\n              placeholder=\"请输入工号\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n  \r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n  \r\n      <el-table v-loading=\"loading\" :data=\"selfAssessUserList\">\r\n        <!-- <el-table-column label=\"编号\" align=\"center\" prop=\"id\" /> -->\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n        <!-- <el-table-column label=\"身份\" align=\"center\" prop=\"assessRole\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ dicts.self_assess_role[scope.row.assessRole][\"label\"] }}\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column label=\"部门\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-for=\"item, index in scope.row.deptList\" v-bind:key=\"index\">\r\n              {{ scope.row.deptList.length > 1 && index + 1 != scope.row.deptList.length ? item.deptName + \", \" : item.deptName}}  \r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleConfig(scope.row)\"\r\n            >配置</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n  \r\n    </div>\r\n  </template>\r\n  \r\n  <script>\r\n  import { getToken } from \"@/utils/auth\";\r\n  import { listAvailable } from \"@/api/assess/self/user\";\r\n  import { listDept } from \"@/api/assess/lateral/dept\";\r\n  import Treeselect from \"@riophae/vue-treeselect\";\r\n  import \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n  \r\n  export default {\r\n    name: \"SelfAssessUserList\",\r\n    components: {\r\n      Treeselect\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        selfAssessUserList: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null, \r\n          name: null,\r\n          assessRole: null,\r\n          benefitLinkFlag: null,\r\n          averageLinkFlag: null\r\n        },\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 字典\r\n        dicts:{\r\n          self_assess_role:[],\r\n          sys_yes_no:[]\r\n        },\r\n        // 部门下拉树\r\n        deptOptions:[],\r\n        // 导入参数\r\n        upload: {\r\n          // 是否禁用上传\r\n          isUploading: false,\r\n          // 设置上传的请求头部\r\n          headers: { Authorization: 'Bearer ' + getToken() },\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/web/selfAssessUser/importInfo\",\r\n        },\r\n        // 导入结果\r\n        importRes:[],\r\n        openImportRes:false\r\n      };\r\n    },\r\n    created() {\r\n      this.getList();\r\n      this.getTreeselect();\r\n      this.getDicts(\"self_assess_role\").then(response => {\r\n        this.dicts.self_assess_role = this.formatterDict(response.data);\r\n      });\r\n      this.getDicts(\"sys_yes_no\").then(response => {\r\n        this.dicts.sys_yes_no = this.formatterDict(response.data);\r\n      });\r\n    },\r\n    methods: {\r\n      formatterDict(dict){\r\n        let result = []\r\n        dict.forEach(dict => {\r\n          result.push({\r\n            label:dict.dictLabel,\r\n            value:dict.dictValue\r\n          })\r\n        });\r\n        return result;\r\n      },\r\n      /** 查询绩效考核-干部自评人员配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listAvailable(this.queryParams).then(response => {\r\n          this.selfAssessUserList = response.data;\r\n          this.loading = false;\r\n        });\r\n      },\r\n      // 取消按钮\r\n      cancel() {\r\n        this.open = false;\r\n        this.reset();\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.form = {\r\n          id: null,\r\n          workNo: null,\r\n          name: null,\r\n          assessRole: null,\r\n          benefitLinkFlag: null,\r\n          averageLinkFlag: null\r\n        };\r\n        this.resetForm(\"form\");\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n  \r\n      /** 转换横向评价部门数据结构 */\r\n      normalizer(node) {\r\n        if (node.children && !node.children.length) {\r\n          delete node.children;\r\n        }\r\n        return {\r\n          id: node.deptId,\r\n          label: node.deptName,\r\n          children: node.children\r\n        };\r\n      },\r\n        /** 查询横向评价部门下拉树结构 */\r\n      getTreeselect() {\r\n        listDept().then(response => {\r\n          this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n        });\r\n      },\r\n  \r\n      /** 配置点击事件 */\r\n      handleConfig(row){\r\n        this.$router.push({\r\n          path:\"/assess/self/user/detail\",\r\n          query:{\r\n            userId:row.id\r\n          }\r\n        })\r\n      },\r\n  \r\n      \r\n    }\r\n  };\r\n  </script>\r\n  <style>\r\n  .redtext{\r\n    color: red;\r\n  }\r\n  </style>\r\n  "]}]}