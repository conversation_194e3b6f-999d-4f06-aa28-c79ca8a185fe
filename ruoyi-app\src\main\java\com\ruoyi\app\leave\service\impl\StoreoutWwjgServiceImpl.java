package com.ruoyi.app.leave.service.impl;

import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.domain.*;
import com.ruoyi.app.leave.mapper.DicMapper;
import com.ruoyi.app.leave.mapper.StoreoutWwjgMapper;
import com.ruoyi.app.leave.service.IStoreoutWwjgService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 外委加工出库Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class StoreoutWwjgServiceImpl implements IStoreoutWwjgService 
{
    @Autowired
    private StoreoutWwjgMapper storeoutWwjgMapper;

    @Autowired
    private DicMapper dicMapper;

    private static final Logger log = LoggerFactory.getLogger(StoreoutWwjgServiceImpl.class);

    /**
     * 查询外委加工出库
     * 
     * @param id 外委加工出库主键
     * @return 外委加工出库
     */
    @Override
    public StoreoutWwjgMeasure selectStoreoutWwjgById(Long id)
    {
        return storeoutWwjgMapper.selectStoreoutWwjgById(id);
    }

    /**
     * 查询外委加工出库列表
     * 
     * @param storeoutWwjgMeasure 外委加工出库
     * @return 外委加工出库
     */
    @Override
    public List<StoreoutWwjgMeasure> selectStoreoutWwjgList(StoreoutWwjgMeasure storeoutWwjgMeasure)
    {
        return storeoutWwjgMapper.selectStoreoutWwjgList(storeoutWwjgMeasure);
    }

    /**
     * 新增外委加工出库
     * 
     * @param storeoutWwjgMeasure 外委加工出库
     * @return 结果
     */
    @Override
    public int insertStoreoutWwjg(StoreoutWwjgMeasure storeoutWwjgMeasure)
    {
        return storeoutWwjgMapper.insertStoreoutWwjg(storeoutWwjgMeasure);
    }

    /**
     * 修改外委加工出库
     * 
     * @param storeoutWwjgMeasure 外委加工出库
     * @return 结果
     */
    @Override
    public int updateStoreoutWwjg(StoreoutWwjgMeasure storeoutWwjgMeasure)
    {
        return storeoutWwjgMapper.updateStoreoutWwjg(storeoutWwjgMeasure);
    }

    /**
     * 批量删除外委加工出库
     * 
     * @param ids 需要删除的外委加工出库主键
     * @return 结果
     */
    @Override
    public int deleteStoreoutWwjgByIds(Long[] ids)
    {
        return storeoutWwjgMapper.deleteStoreoutWwjgByIds(ids);
    }

    /**
     * 删除外委加工出库信息
     * 
     * @param id 外委加工出库主键
     * @return 结果
     */
    @Override
    public int deleteStoreoutWwjgById(Long id)
    {
        return storeoutWwjgMapper.deleteStoreoutWwjgById(id);
    }

    /**
     * 根据匹配ID删除外委加工出库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @Override
    public int deleteStoreoutWwjgByMatchid(String matchid)
    {
        return storeoutWwjgMapper.deleteStoreoutWwjgByMatchid(matchid);
    }

    @Override
    public void handleExternalProcessingStockOut(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial) {
        // 更新计量系统数据
        DicMeasure dicMeasure = new DicMeasure();

        dicMeasure.setCarno(leaveTask.getCarNum());
        dicMeasure.setOperatype(11L);  // 外委加工出库操作类型
        dicMeasure.setPlanid(leavePlan.getPlanNo());
        dicMeasure.setMaterialname(leaveTaskMaterial.getMaterialName());
        dicMeasure.setMaterialspec(leaveTaskMaterial.getMaterialSpec());
        dicMeasure.setSourcename(leavePlan.getSourceCompany());
        dicMeasure.setTargetname(leavePlan.getTargetCompany());
        dicMeasure.setSourcetime(nowDate);
        dicMeasure.setMflag("2");
        dicMeasure.setShflag("0");
        if (leaveTask.getStockOutSpec1Length() != null) {
            dicMeasure.setMsrmemo("炉号：" + leaveTask.getStockOutHeatNo() +
                    "钢种：" + leaveTask.getStockOutSteelGrade() +
                    "规格：" + leaveTask.getStockOutSpec1Length().toString() +
                    "数量：" + leaveTask.getStockOutTotals());
        } else {
            dicMeasure.setMsrmemo("炉号：" + leaveTask.getStockOutHeatNo() +
                    "钢种：" + leaveTask.getStockOutSteelGrade() +
                    "规格：" + "" +
                    "数量：" + leaveTask.getStockOutTotals());
        }
        dicMeasure.setMatno(leaveTask.getStockOutTotals());
        dicMeasure.setZoushu(leaveTask.getStockOutAxles());
        dicMapper.updateDicByCarNo(dicMeasure);

        // 创建外委加工出库记录
        StoreoutWwjgMeasure storeoutWwjgMeasure = new StoreoutWwjgMeasure();
        storeoutWwjgMeasure.setValidflag(1L);  // 有效标志
        storeoutWwjgMeasure.setCarno(leaveTask.getCarNum());  // 车号
        storeoutWwjgMeasure.setOperatype("11");  // 操作类型：外委加工出库
        storeoutWwjgMeasure.setPlanid(leavePlan.getPlanNo());  // 计划号
        storeoutWwjgMeasure.setSourcename(leavePlan.getSourceCompany());  // 发货单位
        storeoutWwjgMeasure.setTargetname(leavePlan.getTargetCompany());  // 收货单位
        storeoutWwjgMeasure.setMaterialname(leaveTaskMaterial.getMaterialName());  // 物料名称
        storeoutWwjgMeasure.setMaterialspec(leaveTaskMaterial.getMaterialSpec());  // 物料规格
        storeoutWwjgMeasure.setCreateman(SecurityUtils.getLoginUser().getUser().getNickName());  // 发货人
        storeoutWwjgMeasure.setCreatedate(nowDate);  // 发货时间
        storeoutWwjgMeasure.setHeatno(leaveTask.getStockOutHeatNo());  // 炉号
        storeoutWwjgMeasure.setSteelgrade(leaveTask.getStockOutSteelGrade());  // 钢种
        storeoutWwjgMeasure.setSpec(leaveTask.getStockOutSpec1Length() != null ? leaveTask.getStockOutSpec1Length().toString() : null);  // 规格

        storeoutWwjgMeasure.setMatno(leaveTask.getStockOutTotals());  // 件数/支数/张数
        storeoutWwjgMeasure.setJgstyle(leaveTask.getStockOutProcessType());  // 加工类型
        storeoutWwjgMeasure.setMemo(leaveTask.getStockOutRemark());  // 备注

        // 根据车号获取matchid
        DicMeasure dicQuery = new DicMeasure();
        dicQuery.setCarno(leaveTask.getCarNum());
        List<DicMeasure> dicMeasures = dicMapper.selectDicList(dicQuery);
        if (dicMeasures.size() == 1 && dicMeasures.get(0) != null) {
            storeoutWwjgMeasure.setMatchid(dicMeasures.get(0).getMatchid());
        } else {
            log.error("StoreoutWwjgServiceImpl.handleExternalProcessingStockOut 委外加工出库对接计量系统异常");
        }

        // 插入外委加工出库记录
        int i = storeoutWwjgMapper.insertStoreoutWwjg(storeoutWwjgMeasure);
        if (i < 0) {
            log.error("StoreoutWwjgServiceImpl.handleExternalProcessingStockOut 委外加工出库对接计量系统异常");
        }
    }
} 