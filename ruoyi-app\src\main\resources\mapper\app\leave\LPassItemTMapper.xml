<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LPassItemTMapper">
    
    <resultMap type="LPassItemT" id="LPassItemTResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="materialcode"    column="materialcode"    />
        <result property="materialname"    column="materialname"    />
        <result property="materialspec"    column="materialspec"    />
        <result property="materialtype"    column="materialtype"    />
        <result property="unit"    column="unit"    />
        <result property="count"    column="count"    />
        <result property="weight"    column="weight"    />
        <result property="memo"    column="memo"    />
        <result property="counttmp"    column="counttmp"    />
    </resultMap>

    <sql id="selectLPassItemTVo">
        select id, pid, materialcode, materialname, materialspec, materialtype, unit, count, weight, memo, counttmp from L_PASS_ITEM_T
    </sql>

    <select id="selectLPassItemTList" parameterType="LPassItemT" resultMap="LPassItemTResult">
        <include refid="selectLPassItemTVo"/>
        <where>  
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="materialcode != null  and materialcode != ''"> and materialcode = #{materialcode}</if>
            <if test="materialname != null  and materialname != ''"> and materialname like concat('%', #{materialname}, '%')</if>
            <if test="materialspec != null  and materialspec != ''"> and materialspec = #{materialspec}</if>
            <if test="materialtype != null  and materialtype != ''"> and materialtype = #{materialtype}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="count != null  and count != ''"> and count = #{count}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="counttmp != null "> and counttmp = #{counttmp}</if>
        </where>
    </select>
    
    <select id="selectLPassItemTById" parameterType="Long" resultMap="LPassItemTResult">
        <include refid="selectLPassItemTVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLPassItemT" parameterType="LPassItemT">
        insert into L_PASS_ITEM_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pid != null">pid,</if>
            <if test="materialcode != null">materialcode,</if>
            <if test="materialname != null">materialname,</if>
            <if test="materialspec != null">materialspec,</if>
            <if test="materialtype != null">materialtype,</if>
            <if test="unit != null">unit,</if>
            <if test="count != null">count,</if>
            <if test="weight != null">weight,</if>
            <if test="memo != null">memo,</if>
            <if test="counttmp != null">counttmp,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pid != null">#{pid},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="materialtype != null">#{materialtype},</if>
            <if test="unit != null">#{unit},</if>
            <if test="count != null">#{count},</if>
            <if test="weight != null">#{weight},</if>
            <if test="memo != null">#{memo},</if>
            <if test="counttmp != null">#{counttmp},</if>
         </trim>
    </insert>

    <update id="updateLPassItemT" parameterType="LPassItemT">
        update L_PASS_ITEM_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="materialcode != null">materialcode = #{materialcode},</if>
            <if test="materialname != null">materialname = #{materialname},</if>
            <if test="materialspec != null">materialspec = #{materialspec},</if>
            <if test="materialtype != null">materialtype = #{materialtype},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="count != null">count = #{count},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="counttmp != null">counttmp = #{counttmp},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLPassItemTById" parameterType="Long">
        delete from L_PASS_ITEM_T where id = #{id}
    </delete>

    <delete id="deleteLPassItemTByIds" parameterType="String">
        delete from L_PASS_ITEM_T where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByPid" parameterType="Long">
        delete from L_PASS_ITEM_T where pid = #{pid}
    </delete>
    
</mapper>