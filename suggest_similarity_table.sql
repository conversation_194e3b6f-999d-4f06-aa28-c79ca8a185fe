-- 建议相似度表
CREATE TABLE `suggest_similarity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `suggest_number` varchar(50) NOT NULL COMMENT '建议编号',
  `compared_suggest_number` varchar(50) NOT NULL COMMENT '比较建议编号',
  `suggest_similarity` decimal(5,4) NOT NULL COMMENT '相似度结果',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_suggest_number` (`suggest_number`),
  KEY `idx_compared_suggest_number` (`compared_suggest_number`),
  KEY `idx_similarity` (`suggest_similarity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='建议相似度表'; 