package com.ruoyi.framework.web.service;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.http.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class XctgMessageService
{
    /**
     * 配置文件中appid
     */
    @Value("${wx.safe.appid}")
    private String appid;

    /**
     * 配置文件中secret
     */
    @Value("${wx.safe.secret}")
    private String secret;

    /**
     * 获取access_token url
     */
    @Value("${wx.credentialUrl}")
    private String credentialUrl;

    /**
     * 推送消息 url
     */
    @Value("${wx.sendUrl}")
    private String sendUrl;

    @Autowired
    private RedisCache redisCache;

    public String getAccessToken() {
        String accessToken = "";
        // 获取access_token
        if (redisCache.getCacheObject("access_token") != null) {
            accessToken = redisCache.getCacheObject("access_token");
        } else {
            String rspStr = HttpUtils.sendGet(credentialUrl, "grant_type=client_credential&appid=" + appid + "&secret=" + secret, Constants.UTF8);
            JSONObject obj = JSONObject.parseObject(rspStr);
            accessToken = obj.getString("access_token");
            redisCache.setCacheObject("access_token", accessToken, 7000, TimeUnit.SECONDS);
        }
        return accessToken;
    }

    public void visitPlan(String title, String content, String name, String createTime, String visitNo, String openId) {
        String accessToken = getAccessToken();
        // 待审批消息推送
        String template_id = "YTbcItZ9KpVw6x_Oq_ZhjM1isTslskIiTSNZwXUQIsM";
        JSONObject postData = new JSONObject();
        postData.put("access_token", accessToken);
        postData.put("touser", openId);
        postData.put("template_id", template_id);

        JSONObject data = new JSONObject();
        JSONObject valueThing1 = new JSONObject();
        valueThing1.put("value", title);
        data.put("thing1", valueThing1);
        JSONObject valueThing3 = new JSONObject();
        // 字符串长度不允许超过20
        if (content.length() > 20) {
            content = content.substring(0, 18) + "……";
        }
        valueThing3.put("value", content);
        data.put("thing3", valueThing3);
        JSONObject valueName5 = new JSONObject();
        valueName5.put("value", name);
        data.put("name5", valueName5);
        JSONObject valueDate2 = new JSONObject();
        valueDate2.put("value", createTime);
        data.put("date2", valueDate2);
        postData.put("data", data);
        postData.put("page", "pages/visit/approve/approve?visitNo="+visitNo);
        // 推送消息类型 默认真机 发布时需要注释
//        postData.put("miniprogram_state", "developer");
        String rspStr = HttpUtils.sendPost(sendUrl+"?access_token=" + accessToken, postData.toJSONString());
        JSONObject obj = JSONObject.parseObject(rspStr);
    }

}
