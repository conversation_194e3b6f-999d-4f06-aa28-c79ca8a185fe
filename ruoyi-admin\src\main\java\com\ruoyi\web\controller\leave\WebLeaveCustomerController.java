package com.ruoyi.web.controller.leave;

import java.util.List;

import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LeaveCustomer;
import com.ruoyi.app.leave.service.ILeaveCustomerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出门证厂外客户Controller
 * 
 * <AUTHOR>
 * @date 2025-03-17
 */
@RestController
@RequestMapping("/web/leave/customer")
public class WebLeaveCustomerController extends BaseController
{
    @Autowired
    private ILeaveCustomerService leaveCustomerService;

    /**
     * 查询出门证厂外客户列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeaveCustomer leaveCustomer)
    {
        startPage();
        List<LeaveCustomer> list = leaveCustomerService.selectLeaveCustomerList(leaveCustomer);
        return getDataTable(list);
    }

    /**
     * 导出出门证厂外客户列表
     */
    @PreAuthorize("@ss.hasPermi('leave:customer:export')")
    @Log(title = "出门证厂外客户", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveCustomer leaveCustomer)
    {
        List<LeaveCustomer> list = leaveCustomerService.selectLeaveCustomerList(leaveCustomer);
        ExcelUtil<LeaveCustomer> util = new ExcelUtil<LeaveCustomer>(LeaveCustomer.class);
        return util.exportExcel(list, "customer");
    }

    /**
     * 获取出门证厂外客户详细信息
     */
    @PreAuthorize("@ss.hasPermi('leave:customer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveCustomerService.selectLeaveCustomerById(id));
    }

    /**
     * 新增出门证厂外客户
     */
    @PreAuthorize("@ss.hasPermi('leave:customer:add')")
    @Log(title = "出门证厂外客户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveCustomer leaveCustomer)
    {
        return toAjax(leaveCustomerService.insertLeaveCustomer(leaveCustomer, SecurityUtils.getUsername()));
    }

    /**
     * 修改出门证厂外客户
     */
    @PreAuthorize("@ss.hasPermi('leave:customer:edit')")
    @Log(title = "出门证厂外客户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveCustomer leaveCustomer)
    {
        return toAjax(leaveCustomerService.updateLeaveCustomer(leaveCustomer));
    }

    /**
     * 删除出门证厂外客户
     */
    @PreAuthorize("@ss.hasPermi('leave:customer:remove')")
    @Log(title = "出门证厂外客户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveCustomerService.deleteLeaveCustomerByIds(ids));
    }

    /**
     * 废弃出门证厂外客户
     */
    @Log(title = "废弃客户", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    public AjaxResult invalid(@PathVariable Long id) {
        return toAjax(leaveCustomerService.invalidLeaveCustomerByIds(id, SecurityUtils.getUsername()));
    }
}
