<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-row>
          <el-form-item label="考核年月" prop="assessDate">
            <el-date-picker
              v-model="queryParams.assessDate"
              type="month"
              value-format="yyyy-M"
              format="yyyy 年 M 月"
              placeholder="选择考核年月"
              :clearable="false">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="部门" prop="deptId">
            <el-select v-model="queryParams.deptId" placeholder="请选择部门">
              <el-option
                v-for="item in deptOptions"
                :key="item.deptId"
                :label="item.deptName"
                :value="item.deptId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      
      <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
        <el-tab-pane :label="toCheckLabel" name="toCheck">
          <el-row :gutter="10" class="mb8">
            <span style="font-weight: 600;margin-left: 24px;">待评分列表</span>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>
          <el-table v-loading="loading" :data="listToCheck">
            <el-table-column label="工号" align="center" prop="workNo" width="120"/>
            <el-table-column label="姓名" align="center" prop="name" width="120"/>
            <el-table-column label="部门" align="center" prop="deptName" ></el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleCheckDetail(scope.row)"
                >评分</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-tab-pane>
        <el-tab-pane :label="checkedLabel" name="checked">
          <el-row :gutter="10" class="mb8">
            <span style="font-weight: 600;margin-left: 24px;">已评分列表</span>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getCheckedList"></right-toolbar>
          </el-row>
          <el-table v-loading="loading" :data="listChecked">
            <el-table-column label="工号" align="center" prop="workNo" width="120"/>
            <el-table-column label="姓名" align="center" prop="name" width="120"/>
            <el-table-column label="部门" align="center" prop="deptName" />
            <el-table-column label="评分类型" align="center" prop="type" >
              <template slot-scope="scope">
                <span v-if="scope.row.type == '1'">部门领导评分</span>
                <span v-if="scope.row.type == '2'">事业部领导评分</span>
                <span v-if="scope.row.type == '3'">运改组织部审核</span>
                <span v-if="scope.row.type == '4'">条线领导评分</span>
              </template>
            </el-table-column>
            <el-table-column label="评分时间" align="center" prop="createTime" />
            <el-table-column label="评分" align="center" prop="score" />
          </el-table>
          <pagination
            v-show="checkedTotal>0"
            :total="checkedTotal"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getCheckedList"
          />
        </el-tab-pane>
      </el-tabs>

      <el-dialog
        :visible.sync="open"
        fullscreen>
        <h3 style="text-align: center;">月度业绩考核表</h3>
          <el-descriptions class="margin-top" :column="3">
            <el-descriptions-item>
              <template slot="label">
                姓名
              </template>
              {{ checkInfo.name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                部门
              </template>
              {{ checkInfo.deptName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                考核年月
              </template>
              {{ checkInfo.assessDate }}
            </el-descriptions-item>
          </el-descriptions>
          <el-table v-loading="loading" :data="checkInfo.list"
            :span-method="objectSpanMethod" border>
            <el-table-column label="类型" align="center" prop="item" width="120"/>
            <el-table-column label="指标" align="center" prop="category" width="150"/>
            <el-table-column label="目标" align="center" prop="target" width="180"/>
            <el-table-column label="评分标准" align="center" prop="standard" />
            <el-table-column label="完成实绩（若扣分，写明原因）" align="center" prop="performance" />
            <el-table-column label="加减分" align="center" prop="dePoints" width="150" />
            <el-table-column label="加减分原因" align="center" prop="pointsReason" width="180" />
          </el-table>
          <el-form size="small" :inline="false" label-width="200px" label-position="left" style="margin-top: 10px;">
            <el-form-item label="自评分数 / 签名：" prop="deptId">
              <span >{{ checkInfo.selfScore + " 分 / " + checkInfo.name }}</span>
            </el-form-item>
            <el-form-item v-if="checkInfo.deptScore && checkInfo.deptUserName" label="部门领导评分 / 签名：" prop="deptId">
              <span >{{ checkInfo.deptScore + " 分 / " + checkInfo.deptUserName }}</span>
            </el-form-item>
            <el-form-item v-if="checkInfo.businessUserName && checkInfo.businessScore" label="事业部领导评分 / 签名：" prop="deptId">
              <span>{{ checkInfo.businessScore + " 分 / " + checkInfo.businessUserName }}</span>
            </el-form-item>
            <!-- <el-form-item v-if="checkInfo.leaderScore && checkInfo.leaderName" label="总经理部领导评分 / 签名：" prop="deptId">
              <span >{{ checkInfo.leaderScore + " 分 / " + checkInfo.leaderName }}</span>
            </el-form-item> -->
            <el-form-item label="总经理部领导评分：">
              <div style="display: flex;width: 180px;">
                <el-input type="number" autosize v-model="form.leaderScore" placeholder="请输入评分" />
                <span>分</span>
              </div>
            </el-form-item>
            <el-form-item label="加减分理由：">
              <div style="display: flex;width: 180px;">
                <el-input type="textarea" autosize v-model="form.leaderReview" placeholder="请输入加减分理由" />
              </div>
            </el-form-item>
          </el-form>
          <div style="text-align: center;">
            <el-button type="primary" @click="checkSubmit">提 交</el-button>
            <el-button plain type="info" @click="cancel">返 回</el-button>
          </div>
      </el-dialog>

    </div>
  </template>

  <script>
  import { listLeaderToCheck, listChecked, getInfo, check } from "@/api/assess/self/info"
  import { getCheckDeptList } from "@/api/assess/self/user";
  import { formatDateYm } from "@/utils/index"

  export default {
    name: "SelfAssessLeaderCheck",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        checkedTotal: 0,
        // 绩效考核-干部自评人员配置表格数据
        listToCheck: [],
        listChecked: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          workNo: null,
          name:null,
          deptId:null,
          assessDate:null
        },
        // 表单参数
        form: {
          id:null,
          // 条线领导评分
          leaderScore:null,
        },
        // 表单校验
        rules: {
        },
        deptOptions:[],
        openCheck:false,
        checkInfo:{},
        // 合并单元格
        spanList:[],
        activeName:"toCheck",
        // 待评分标签
        toCheckLabel:"待评分(0)",
        // 已评分
        checkedLabel:"已评分(0)"
      };
    },
    created() {
      this.queryParams.assessDate = formatDateYm(new Date().getTime())
      // this.getSelfAssessUser();
      this.getCheckDeptList();
      this.getList();
      this.getCheckedList();
    },
    methods: {
      // 获取部门信息
      getCheckDeptList(){
        getCheckDeptList().then(res => {
          console.log(res)
          if(res.code == 200){
            let deptOptions = [];
            res.data.forEach(item => {
              deptOptions.push({
                deptName:item.deptName,
                deptId:item.deptId
              })
            })
            this.deptOptions = deptOptions;
          }
        })
      },
      /** 查询绩效考核-干部自评待审核列表 */
      getList() {
        this.loading = true;
        listLeaderToCheck(this.queryParams).then(response => {
          this.listToCheck = response.rows;
          this.total = response.total;
          this.toCheckLabel = `待评分(${response.total})`
          this.loading = false;
        });
      },
      /** 获取已审核列表 */
      getCheckedList(){
        this.loading = true;
        listChecked(this.queryParams).then(res => {
          this.listChecked = res.rows;
          this.checkedTotal = res.total;
          this.checkedLabel = `已评分(${res.total})`
          this.loading = false;
        })
      },
      // 标签页点击事件
      handleTabClick(e){
        let type = e.name;
        if(type == "checked"){
          this.getCheckedList();
        }else{
          this.getList();
        }
      },

      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          deptScore: null,
          businessScore: null,
          leaderScore: null,
        };
        // this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getCheckedList();
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },

      queryAll(){
        this.queryParams.pageNum = 1;
        this.getList();
        this.getCheckedList();
      },

      // 审批详情
      handleCheckDetail(row){
        getInfo({id:row.id}).then(res => {
          console.log(res);
          if(res.code == 200){
            this.checkInfo = res.data;
            let list = JSON.parse(res.data.content);
            this.handleSpanList(list);
            this.checkInfo.list = list;
          }
          this.open = true
        })
      },

      // 审批提交
      checkSubmit(){
        if(this.verify()){
          this.form.id = this.checkInfo.id;
          this.form.status = this.checkInfo.status;
          check(this.form).then(res => {
            console.log(res)
            if(res.code == 200){
              this.$message({
                type: 'success',
                message: '提交成功!'
              });
              this.reset();
              this.open = false;
              this.queryAll();
            }else{
              this.$message({
                type: 'warning',
                message: '操作失败，无权限或当前审批状态不匹配'
              });
            }
          })
        }else{
          this.$message({
            type: 'warning',
            message: '请填写评分'
          });
          return false;
        }
      },

      // 数据验证
      verify(){
        if(!this.form.leaderScore) return false;
        return true;
      },

      handleListChange(type){
        console.log(type)
      },
      // 处理列表
      handleSpanList(data){
        let spanList = [];
        let flag = 0;
        for(let i = 0; i < data.length; i++){
          // 相同考核项合并
          if(i == 0){
            spanList.push({
              rowspan: 1,
              colspan: 1
            })
          }else{
            if(data[i - 1].item == data[i].item){
              spanList.push({
                rowspan: 0,
                colspan: 0
              })
              spanList[flag].rowspan += 1;
            }else{
              spanList.push({
                rowspan: 1,
                colspan: 1
              })
              flag = i;
            }
          }
        }
        this.spanList = spanList;
      },

      // 合并单元格方法
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        // 第一列相同项合并
        if (columnIndex === 0) {
          return this.spanList[rowIndex];
        }
        // 类别无内容 合并
        if(columnIndex === 1){
          if(!row.category){
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        }
        if(columnIndex === 2){
          if(!row.category){
            return {
              rowspan: 1,
              colspan: 2
            }
          }
        }
      }
    }
  };
  </script>
