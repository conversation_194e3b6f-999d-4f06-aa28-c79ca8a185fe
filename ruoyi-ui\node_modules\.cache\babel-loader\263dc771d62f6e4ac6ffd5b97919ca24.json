{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue", "mtime": 1756197883972}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfcmVnZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvcmVnZW5lcmF0b3IuanMiKSk7CnZhciBfYXN5bmNUb0dlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkU6L2phdmFfd29ya3NwYWNlL25ld193b3Jrc3BhY2UveGN0Zy9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yLmpzIikpOwp2YXIgX29iamVjdFNwcmVhZDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkU6L2phdmFfd29ya3NwYWNlL25ld193b3Jrc3BhY2UveGN0Zy9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwp2YXIgX3NsaWNlZFRvQXJyYXkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvc2xpY2VkVG9BcnJheS5qcyIpKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5mcm9tLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zbGljZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRlc3QuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucGFkLXN0YXJ0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maW5kLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5zb21lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIik7CnZhciBfcGxhbiA9IHJlcXVpcmUoIkAvYXBpL2xlYXZlL3BsYW4iKTsKdmFyIF90YXNrID0gcmVxdWlyZSgiQC9hcGkvbGVhdmUvdGFzayIpOwp2YXIgX2RyaXZlciA9IHJlcXVpcmUoIkAvYXBpL2RnY2IvZHJpdmVyL2RyaXZlciIpOwp2YXIgX3NvcnRhYmxlanMgPSByZXF1aXJlKCJzb3J0YWJsZWpzIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiRGV0YWlsTGVhdmVQbGFuIiwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgLy8g6aqM6K+B6L2m54mM5Y+3CiAgICB2YXIgdmFsaWRhdGVDYXJOdW1iZXIgPSBmdW5jdGlvbiB2YWxpZGF0ZUNhck51bWJlcihydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgdmFyIHBhdHRlcm4gPSAvXlvkuqzmtKXmsqrmuJ3lhoDosavkupHovr3pu5HmuZjnmpbpsoHmlrDoi4/mtZnotaPphILmoYLnlJjmmYvokpnpmZXlkInpl73otLXnsqTpnZLol4/lt53lroHnkLzkvb/pooZBLVpdezF9W0EtWl17MX1bQS1aMC05XXs0fVtBLVowLTnmjILlraborabmuK/mvrNdezF9JC87CiAgICAgIGlmICghcGF0dGVybi50ZXN0KHZhbHVlKSkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5q2j56Gu55qE6L2m54mM5Y+3JykpOwogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCk7CiAgICAgIH0KICAgIH07CgogICAgLy8g6aqM6K+B5omL5py65Y+3CiAgICB2YXIgdmFsaWRhdGVQaG9uZSA9IGZ1bmN0aW9uIHZhbGlkYXRlUGhvbmUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIHZhciBwYXR0ZXJuID0gL14xWzMtOV1cZHs5fSQvOwogICAgICBpZiAoIXBhdHRlcm4udGVzdCh2YWx1ZSkpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpeato+ehrueahOaJi+acuuWPtycpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9OwoKICAgIC8vIOmqjOivgei6q+S7veivgeWPtwogICAgdmFyIHZhbGlkYXRlSWRDYXJkID0gZnVuY3Rpb24gdmFsaWRhdGVJZENhcmQocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIHZhciBwYXR0ZXJuID0gLyheXGR7MTV9JCl8KF5cZHsxOH0kKXwoXlxkezE3fShcZHxYfHgpJCkvOwogICAgICBpZiAoIXBhdHRlcm4udGVzdCh2YWx1ZSkpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpeato+ehrueahOi6q+S7veivgeWPtycpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9OwogICAgcmV0dXJuIHsKICAgICAgaXNUYXNrVHlwZUVkaXQ6IHRydWUsCiAgICAgIHZlaGljbGVFbWlzc2lvblN0YW5kYXJkc09wdGlvbnM6IFtdLAogICAgICB0YXNrVHlwZU9wdGlvbnM6IFtdLAogICAgICBjYXJMaXN0OiBbXSwKICAgICAgc2VhcmNoQ2FyUXVlcnk6ICcnLAogICAgICBmaWx0ZXJlZENhck9wdGlvbnM6IFtdLAogICAgICBkcml2ZXJMaXN0OiBbXSwKICAgICAgc2VhcmNoRHJpdmVyUXVlcnk6ICcnLAogICAgICBmaWx0ZXJlZERyaXZlck9wdGlvbnM6IFtdLAogICAgICAvL+WuoeaguOihqOWNlQogICAgICBhcHByb3ZlRm9ybTogewogICAgICAgIGFwcGx5Tm86IG51bGwsCiAgICAgICAgYXBwcm92ZUNvbnRlbnQ6ICcnLAogICAgICAgIC8v5a6h5qC45oSP6KeBCiAgICAgICAgYXBwcm92ZUZsYWc6IHRydWUgLy/lrqHmoLjnirbmgIEKICAgICAgfSwKICAgICAgLy8g5Zu+54mH5YiX6KGoCiAgICAgIGltYWdlTGlzdDogW10sCiAgICAgIC8vIOaWh+S7tuWIl+ihqAogICAgICBmaWxlTGlzdDogW10sCiAgICAgIC8vIOa0vui9puW8ueahhuWPr+ingeaApwogICAgICBkaXNwYXRjaERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICB0YXNrTGlzdEluZm86IFtdLAogICAgICAvLyDmtL7ovabooajljZXmlbDmja4KICAgICAgZGlzcGF0Y2hGb3JtOiB7CiAgICAgICAgLy8gY2FyTnVtYmVyOiAnJywKICAgICAgICAvLyBkcml2ZXJOYW1lOiAnJywKICAgICAgICAvLyBkcml2ZXJQaG9uZTogJycsCiAgICAgICAgLy8gZHJpdmVySWRDYXJkOiAnJwogICAgICB9LAogICAgICAvLyDmtL7ovabooajljZXpqozor4Hop4TliJkKICAgICAgZGlzcGF0Y2hSdWxlczogewogICAgICAgIGNhck51bWJlcjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpei9pueJjOWPtycsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9LCB7CiAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlkYXRlQ2FyTnVtYmVyLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgZHJpdmVyTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeWPuOacuuWnk+WQjScsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9LCB7CiAgICAgICAgICBtaW46IDIsCiAgICAgICAgICBtYXg6IDIwLAogICAgICAgICAgbWVzc2FnZTogJ+mVv+W6puWcqCAyIOWIsCAyMCDkuKrlrZfnrKYnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgZHJpdmVyUGhvbmU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXlj7jmnLrmiYvmnLrlj7cnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfSwgewogICAgICAgICAgdmFsaWRhdG9yOiB2YWxpZGF0ZVBob25lLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgZHJpdmVySWRDYXJkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl5Y+45py66Lqr5Lu96K+B5Y+3JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH0sIHsKICAgICAgICAgIHZhbGlkYXRvcjogdmFsaWRhdGVJZENhcmQsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XQogICAgICB9LAogICAgICAvLyDmtL7ovabliJfooajmlbDmja4KICAgICAgZGlzcGF0Y2hMaXN0OiBbewogICAgICAgIGlkOiAxLAogICAgICAgIGNhck51bWJlcjogJ+S6rEExMjM0NScsCiAgICAgICAgZHJpdmVyTmFtZTogJ+eOi+Wwj+aYjicsCiAgICAgICAgZHJpdmVyUGhvbmU6ICcxMzgwMDEzODAwMCcsCiAgICAgICAgZHJpdmVySWRDYXJkOiAnMTEwMTAxMTk5MDAxMDEwMDAxJywKICAgICAgICBkaXNwYXRjaFRpbWU6ICcyMDI1LTAzLTE4IDA5OjMwOjAwJywKICAgICAgICBzdGF0dXM6IDIsCiAgICAgICAgdGFyZVdlaWdodDogODUwMCwKICAgICAgICBncm9zc1dlaWdodDogMTU4MDAsCiAgICAgICAgcmVjaGVja2VkR3Jvc3NXZWlnaHQ6IDE1NzUwLAogICAgICAgIHJlY2hlY2tlZFRhcmVXZWlnaHQ6IDg0ODAKICAgICAgfSwgewogICAgICAgIGlkOiAyLAogICAgICAgIGNhck51bWJlcjogJ+S6rEI5ODc2NScsCiAgICAgICAgZHJpdmVyTmFtZTogJ+adjuWkp+WjricsCiAgICAgICAgZHJpdmVyUGhvbmU6ICcxMzkwMDEzOTAwMCcsCiAgICAgICAgZHJpdmVySWRDYXJkOiAnMTEwMTAxMTk5MTAyMDIwMDAyJywKICAgICAgICBkaXNwYXRjaFRpbWU6ICcyMDI1LTAzLTE5IDE0OjE1OjAwJywKICAgICAgICBzdGF0dXM6IDEsCiAgICAgICAgdGFyZVdlaWdodDogNzgwMCwKICAgICAgICBncm9zc1dlaWdodDogMTI2MDAsCiAgICAgICAgcmVjaGVja2VkR3Jvc3NXZWlnaHQ6IG51bGwsCiAgICAgICAgcmVjaGVja2VkVGFyZVdlaWdodDogbnVsbAogICAgICB9XSwKICAgICAgLy8g6K6h5YiS6K+m5oOF5L+h5oGvCiAgICAgIHBsYW5JbmZvOiB7fSwKICAgICAgYXBwbHlObzogbnVsbCwKICAgICAgdGFza1F1ZXJ5UGFyYW1zOiB7CiAgICAgICAgYXBwbHlObzogbnVsbAogICAgICB9LAogICAgICB0YXNrTWF0ZXJpYWxMaXN0OiBudWxsLAogICAgICAvLyDnianotYTpgInmi6nnm7jlhbPmlbDmja4KICAgICAgbWF0ZXJpYWxTZWxlY3Rpb25MaXN0OiBbewogICAgICAgIG1hdGVyaWFsSWQ6IG51bGwsCiAgICAgICAgbWF0ZXJpYWxOYW1lOiAnJywKICAgICAgICBtYXRlcmlhbFNwZWM6ICcnLAogICAgICAgIHBsYW5OdW06IDAsCiAgICAgICAgdXNlZE51bTogMCwKICAgICAgICByZW1haW5pbmdOdW06IDAsCiAgICAgICAgY3VycmVudE51bTogMAogICAgICB9XSwKICAgICAgYXZhaWxhYmxlTWF0ZXJpYWxzOiBbXSwKICAgICAgLy8g5Y+v6YCJ55qE54mp6LWE5YiX6KGoCiAgICAgIHRhc2tNYXRlcmlhbExpc3RNYXA6IG5ldyBNYXAoKSwKICAgICAgLy8g5bey5rS+6L2m55qE54mp6LWE5YiX6KGoCiAgICAgIHRhc2tNYXRlcmlhbE1hcDogbmV3IE1hcCgpIC8vIOWtmOWCqOaJgOacieS7u+WKoeeJqei1hOeahOaYoOWwhAogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDliKTmlq3mmK/lkKblj6/ku6XmtL7ovaYKICAgIGNhbkRpc3BhdGNoQ2FyOiBmdW5jdGlvbiBjYW5EaXNwYXRjaENhcigpIHsKICAgICAgLy8g5Yik5pat55Sz6K+35Y2V5piv5ZCm5bey6YCa6L+HCiAgICAgIC8vIGNvbnN0IGlzUGxhbkFwcHJvdmVkID0gdGhpcy5wbGFuSW5mby5wbGFuU3RhdHVzID09PSAyOwoKICAgICAgLy8gLy8g5aaC5p6c5piv6Z2e6K6h6YeP57G75Z6L77yM5LiU5bey57uP5rS+6L+H6L2m77yM5YiZ5LiN6IO95YaN5rS+6L2mCiAgICAgIC8vIGlmICh0aGlzLnBsYW5JbmZvLm1lYXN1cmVGbGFnICE9PSAxICYmIHRoaXMuZGlzcGF0Y2hMaXN0Lmxlbmd0aCA+IDApIHsKICAgICAgLy8gICByZXR1cm4gZmFsc2U7CiAgICAgIC8vIH0KCiAgICAgIHJldHVybiB0cnVlOwogICAgfSwKICAgIC8vIOm7mOiupOaYvuekuuWJjTUw5p2h77yM6Iul5pyJ5pCc57Si77yM5YiZ5pi+56S65pCc57Si5ZCO55qE5pWw5o2uCiAgICBkaXNwbGF5RHJpdmVyTGlzdE9wdGlvbnM6IGZ1bmN0aW9uIGRpc3BsYXlEcml2ZXJMaXN0T3B0aW9ucygpIHsKICAgICAgcmV0dXJuIHRoaXMuc2VhcmNoRHJpdmVyUXVlcnkgPyB0aGlzLmZpbHRlcmVkRHJpdmVyT3B0aW9ucyA6IHRoaXMuZHJpdmVyTGlzdC5zbGljZSgwLCA1MCk7CiAgICB9LAogICAgZGlzcGxheUNhckxpc3RPcHRpb25zOiBmdW5jdGlvbiBkaXNwbGF5Q2FyTGlzdE9wdGlvbnMoKSB7CiAgICAgIHJldHVybiB0aGlzLnNlYXJjaENhclF1ZXJ5ID8gdGhpcy5maWx0ZXJlZENhck9wdGlvbnMgOiB0aGlzLmNhckxpc3Quc2xpY2UoMCwgNTApOwogICAgfSwKICAgIGNhblNob3dNYXRlcmlhbENvbmZpcm06IGZ1bmN0aW9uIGNhblNob3dNYXRlcmlhbENvbmZpcm0oKSB7CiAgICAgIC8vIOWPquaciXBsYW5TdGF0dXPkuLo15oiWNuaXtuaYvuekuu+8iOW3suWHuuWOgi/pg6jliIbmlLbotKfvvInvvIzkuJTkuI3mmK/lt7LlrozmiJAv5bqf5byDL+mps+Wbni/ov4fmnJ8KICAgICAgcmV0dXJuIFs1LCA2XS5pbmNsdWRlcyh0aGlzLnBsYW5JbmZvLnBsYW5TdGF0dXMpOwogICAgfQogIH0sCiAgYWN0aXZhdGVkOiBmdW5jdGlvbiBhY3RpdmF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy5nZXREaWN0cygieGN0Z19kcml2ZXJfY2FyX2VtaXNzaW9uX3N0YW5kYXJkcyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkc09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJsZWF2ZV90YXNrX3R5cGUiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy50YXNrVHlwZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICBpZiAoX3RoaXMucGxhbkluZm8ucGxhblR5cGUgIT09IDMpIHsKICAgICAgICBfdGhpcy50YXNrVHlwZU9wdGlvbnMuc3BsaWNlKDIsIDEpOwogICAgICB9CiAgICB9KTsKICAgIC8vIOiOt+WPlui3r+eUseWPguaVsOS4reeahElECiAgICB2YXIgYXBwbHlObyA9IHRoaXMuJHJvdXRlLnBhcmFtcy5hcHBseU5vOwogICAgdGhpcy5hcHBseU5vID0gYXBwbHlObzsKICAgIHRoaXMudGFza1F1ZXJ5UGFyYW1zLmFwcGx5Tm8gPSBhcHBseU5vOwogICAgdGhpcy5hcHByb3ZlRm9ybS5hcHBseU5vID0gYXBwbHlObzsKICAgIGlmIChhcHBseU5vKSB7CiAgICAgICgwLCBfcGxhbi5kZXRhaWxQbGFuKShhcHBseU5vKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLnBsYW5JbmZvID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICBfdGhpcy50YXNrVHlwZUVkaXRVcGRhdGUoKTsKICAgICAgICBjb25zb2xlLmxvZygidGhpcy5wbGFuSW5mbyIsIF90aGlzLnBsYW5JbmZvKTsKICAgICAgICAvLyDop6PmnpDlm77niYflkozmlofku7bmlbDmja4KICAgICAgICBfdGhpcy5wYXJzZUltYWdlQW5kRmlsZURhdGEoKTsKICAgICAgfSk7CiAgICB9CiAgICA7CiAgICB0aGlzLmdldExpc3RUYXNrSW5mbygpOwogICAgdGhpcy5nZXREcml2ZXJMaXN0KCk7CiAgICB0aGlzLmdldENhckxpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZUFwcHJvdmU6IGZ1bmN0aW9uIGhhbmRsZUFwcHJvdmUoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLmFwcHJvdmVGb3JtLmFwcHJvdmVGbGFnID0gdHJ1ZTsKICAgICAgY29uc29sZS5sb2coInRoaXMuYXBwcm92ZUZvcm0iLCB0aGlzLmFwcHJvdmVGb3JtKTsKICAgICAgKDAsIF9wbGFuLmFwcHJvdmUpKHRoaXMuYXBwcm92ZUZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuoeaguOmAmui/hycpOwogICAgICAgIC8vIOi3s+i9rOWIsOWIl+ihqOmhtemdouW5tuWIt+aWsAogICAgICAgIF90aGlzMi4kcm91dGVyLnB1c2goewogICAgICAgICAgcGF0aDogIi9sZWF2ZS9sZWF2ZVBsYW5MaXN0IiwKICAgICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICAgIHQ6IERhdGUubm93KCksCiAgICAgICAgICAgIHJlZnJlc2g6IHRydWUgLy8g5re75Yqg5Yi35paw5qCH6K6wCiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcign5a6h5qC45aSx6LSlJyk7CiAgICAgICAgY29uc29sZS5lcnJvcignQXBwcm92YWwgZXJyb3I6JywgZXJyb3IpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVSZWplY3Q6IGZ1bmN0aW9uIGhhbmRsZVJlamVjdCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuYXBwcm92ZUZvcm0uYXBwcm92ZUZsYWcgPSBmYWxzZTsKICAgICAgY29uc29sZS5sb2coInRoaXMuYXBwcm92ZUZvcm0iLCB0aGlzLmFwcHJvdmVGb3JtKTsKICAgICAgKDAsIF9wbGFuLmFwcHJvdmUpKHRoaXMuYXBwcm92ZUZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mps+WbnuaIkOWKnycpOwogICAgICAgIC8vIOi3s+i9rOWIsOWIl+ihqOmhtemdouW5tuWIt+aWsAogICAgICAgIF90aGlzMy4kcm91dGVyLnB1c2goewogICAgICAgICAgcGF0aDogIi9sZWF2ZS9sZWF2ZVBsYW5MaXN0IiwKICAgICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICAgIHQ6IERhdGUubm93KCksCiAgICAgICAgICAgIHJlZnJlc2g6IHRydWUgLy8g5re75Yqg5Yi35paw5qCH6K6wCiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcign5a6h5qC45aSx6LSlJyk7CiAgICAgICAgY29uc29sZS5lcnJvcignQXBwcm92YWwgZXJyb3I6JywgZXJyb3IpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVEaXNjYXJkOiBmdW5jdGlvbiBoYW5kbGVEaXNjYXJkKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgKDAsIF9wbGFuLmRpc2NhcmQpKHRoaXMucGxhbkluZm8pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM0LiRtZXNzYWdlLnN1Y2Nlc3MoJ+W6n+W8g+aIkOWKnycpOwogICAgICAgIGlmICh3aW5kb3cuaGlzdG9yeS5sZW5ndGggPiAxKSB7CiAgICAgICAgICBfdGhpczQuJHJvdXRlci5nbygtMSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNC4kcm91dGVyLnB1c2goewogICAgICAgICAgICBwYXRoOiAiL2xlYXZlL2xlYXZlUGxhbkxpc3QiLAogICAgICAgICAgICBxdWVyeTogewogICAgICAgICAgICAgIHQ6IERhdGUubm93KCkKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBfdGhpczQuJG1lc3NhZ2UuZXJyb3IoJ+W6n+W8g+Wksei0pScpOwogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FwcHJvdmFsIGVycm9yOicsIGVycm9yKTsKICAgICAgfSk7CiAgICB9LAogICAgdGFza1R5cGVFZGl0VXBkYXRlOiBmdW5jdGlvbiB0YXNrVHlwZUVkaXRVcGRhdGUoKSB7CiAgICAgIGlmICh0aGlzLnBsYW5JbmZvLnBsYW5UeXBlICE9PSAyKSB7CiAgICAgICAgdGhpcy5pc1Rhc2tUeXBlRWRpdCA9IGZhbHNlOwogICAgICB9CiAgICB9LAogICAgZ2V0TGlzdFRhc2tJbmZvOiBmdW5jdGlvbiBnZXRMaXN0VGFza0luZm8oKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICAoMCwgX3Rhc2subGlzdFRhc2spKHRoaXMudGFza1F1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIGNvbnNvbGUubG9nKCJyZXNwb25zZS5kYXRhIiwgcmVzcG9uc2Uucm93cyk7CiAgICAgICAgX3RoaXM1LnRhc2tMaXN0SW5mbyA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgY29uc29sZS5sb2coInRoaXMudGFza0xpc3RJbmZvIiwgX3RoaXM1LnRhc2tMaXN0SW5mbyk7CiAgICAgICAgLy8g6I635Y+W5omA5pyJ5Lu75Yqh54mp6LWECiAgICAgICAgX3RoaXM1LmdldEFsbFRhc2tNYXRlcmlhbHMoKTsKICAgICAgfSk7CiAgICB9LAogICAgb3Blbk5ld0RyaXZlcldpbmRvdzogZnVuY3Rpb24gb3Blbk5ld0RyaXZlcldpbmRvdygpIHsKICAgICAgdmFyIG5ld1dpbmRvd1VybCA9ICdodHRwczovL3lkeHQuY2l0aWNzdGVlbC5jb206ODA5OS90cnVja01hbmFnZS94Y3RnRHJpdmVyVXNlcic7IC8vIOabv+aNouS4uuWunumZheimgei3s+i9rOeahOmhtemdoiBVUkwKICAgICAgd2luZG93Lm9wZW4obmV3V2luZG93VXJsLCAnX2JsYW5rJyk7IC8vIOaJk+W8gOaWsOeql+WPo+W5tui3s+i9rOiHs+aMh+WumiBVUkwKICAgIH0sCiAgICBvcGVuTmV3Q2FyV2luZG93OiBmdW5jdGlvbiBvcGVuTmV3Q2FyV2luZG93KCkgewogICAgICB2YXIgbmV3V2luZG93VXJsID0gJ2h0dHBzOi8veWR4dC5jaXRpY3N0ZWVsLmNvbTo4MDk5L3RydWNrTWFuYWdlL3hjdGdEcml2ZXJDYXInOyAvLyDmm7/mjaLkuLrlrp7pmYXopoHot7PovaznmoTpobXpnaIgVVJMCiAgICAgIHdpbmRvdy5vcGVuKG5ld1dpbmRvd1VybCwgJ19ibGFuaycpOyAvLyDmiZPlvIDmlrDnqpflj6Plubbot7Povazoh7PmjIflrpogVVJMCiAgICB9LAogICAgLy8gMeWbveS6lO+8jDLlm73lha3vvIwz5paw6IO95rqQ5a2X5YW457+76K+RCiAgICB2ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHNGb3JtYXQ6IGZ1bmN0aW9uIHZlaGljbGVFbWlzc2lvblN0YW5kYXJkc0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHNPcHRpb25zLCByb3cudmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzKTsKICAgIH0sCiAgICB0YXNrVHlwZUZvcm1hdDogZnVuY3Rpb24gdGFza1R5cGVGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuZ2V0VGFza1R5cGVUZXh0KHJvdy50YXNrVHlwZSk7CiAgICB9LAogICAgdGFza1N0YXR1c0Zvcm1hdDogZnVuY3Rpb24gdGFza1N0YXR1c0Zvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5nZXRTdGF0dXNUZXh0KHJvdy50YXNrU3RhdHVzKTsKICAgIH0sCiAgICAvKiog5p+l6K+i5Y+45py65L+h5oGv5YiX6KGoICovZ2V0Q2FyTGlzdDogZnVuY3Rpb24gZ2V0Q2FyTGlzdCgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIC8vIGxpc3RBbGxEcml2ZXIoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgLy8gICB0aGlzLmRyaXZlckxpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICAvLyAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAvLyB9KTsKICAgICAgKDAsIF9kcml2ZXIuZ2V0WGN0Z0RyaXZlckNhckxpc3RCeVBhZ2UpKCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczYuY2FyTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXM2LmZpbHRlcmVkQ2FyT3B0aW9ucyA9IF90aGlzNi5jYXJMaXN0OwogICAgICAgIF90aGlzNi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaQnOe0oui/h+a7pOmAu+i+kQogICAgZmlsdGVyQ2FyRGF0YTogZnVuY3Rpb24gZmlsdGVyQ2FyRGF0YShxdWVyeSkgewogICAgICB0aGlzLnNlYXJjaENhclF1ZXJ5ID0gcXVlcnk7CiAgICAgIGlmICh0aGlzLnNlYXJjaENhclF1ZXJ5KSB7CiAgICAgICAgdGhpcy5maWx0ZXJlZENhck9wdGlvbnMgPSB0aGlzLmNhckxpc3QuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICByZXR1cm4gaXRlbS5jYXJOdW1iZXIuaW5jbHVkZXMocXVlcnkpOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZmlsdGVyZWRDYXJPcHRpb25zID0gdGhpcy5jYXJMaXN0LnNsaWNlKDAsIDUwKTsKICAgICAgfQogICAgfSwKICAgIC8v6YCa6L+HZHJpdmVySWTojrflj5blj7jmnLrkv6Hmga8KICAgIGhhbmRsZURyaXZlckNoYW5nZTogZnVuY3Rpb24gaGFuZGxlRHJpdmVyQ2hhbmdlKCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgaWYgKHRoaXMuZGlzcGF0Y2hGb3JtLmRyaXZlcklkICE9IG51bGwpIHsKICAgICAgICB0aGlzLmRyaXZlckxpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgaWYgKGl0ZW0uaWQgPT0gX3RoaXM3LmRpc3BhdGNoRm9ybS5kcml2ZXJJZCkgewogICAgICAgICAgICBfdGhpczcuZGlzcGF0Y2hGb3JtLm5hbWUgPSBpdGVtLm5hbWU7CiAgICAgICAgICAgIF90aGlzNy5kaXNwYXRjaEZvcm0uaWRDYXJkID0gaXRlbS5pZENhcmQ7CiAgICAgICAgICAgIF90aGlzNy5kaXNwYXRjaEZvcm0uY29tcGFueSA9IGl0ZW0uY29tcGFueTsKICAgICAgICAgICAgX3RoaXM3LmRpc3BhdGNoRm9ybS5waG9uZSA9IGl0ZW0ucGhvbmU7CiAgICAgICAgICAgIF90aGlzNy5kaXNwYXRjaEZvcm0ucGhvdG8gPSBpdGVtLnBob3RvOwogICAgICAgICAgICBfdGhpczcuZGlzcGF0Y2hGb3JtLmZhY2VJbWdMaXN0ID0gaXRlbS5mYWNlSW1nTGlzdDsKICAgICAgICAgICAgX3RoaXM3LmRpc3BhdGNoRm9ybS5kcml2ZXJMaWNlbnNlSW1ncyA9IGl0ZW0uZHJpdmVyTGljZW5zZUltZ3M7CiAgICAgICAgICAgIF90aGlzNy5kaXNwYXRjaEZvcm0udmVoaWNsZUxpY2Vuc2VJbWdzID0gaXRlbS52ZWhpY2xlTGljZW5zZUltZ3M7CiAgICAgICAgICAgIF90aGlzNy5kaXNwYXRjaEZvcm0uc2V4ID0gaXRlbS5nZW5kZXI7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvL+mAmui/h2RyaXZlcklk6I635Y+W5Y+45py65L+h5oGvCiAgICBoYW5kbGVDYXJDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNhckNoYW5nZSgpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgIGNvbnNvbGUubG9nKCJoYW5kbGVDYXJDaGFuZ2UiKTsKICAgICAgaWYgKHRoaXMuZGlzcGF0Y2hGb3JtLmNhclVVSWQgIT0gbnVsbCkgewogICAgICAgIHRoaXMuY2FyTGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICBpZiAoaXRlbS5pZCA9PSBfdGhpczguZGlzcGF0Y2hGb3JtLmNhclVVSWQpIHsKICAgICAgICAgICAgX3RoaXM4LmRpc3BhdGNoRm9ybS5jYXJOdW1iZXIgPSBpdGVtLmNhck51bWJlcjsKICAgICAgICAgICAgaWYgKGl0ZW0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID09IDEpIHsKICAgICAgICAgICAgICBfdGhpczguZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9ICLlm73kupQiOwogICAgICAgICAgICB9IGVsc2UgaWYgKGl0ZW0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID09IDIpIHsKICAgICAgICAgICAgICBfdGhpczguZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9ICLlm73lha0iOwogICAgICAgICAgICB9IGVsc2UgaWYgKGl0ZW0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID09IDMpIHsKICAgICAgICAgICAgICBfdGhpczguZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9ICLmlrDog73mupAiOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIF90aGlzOC5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID0gIiI7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgX3RoaXM4LmRpc3BhdGNoRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9IGl0ZW0ubGljZW5zZVBsYXRlQ29sb3I7CiAgICAgICAgICAgIF90aGlzOC5kaXNwYXRjaEZvcm0uY2FySWQgPSBpdGVtLmNhcklkOwogICAgICAgICAgICBfdGhpczguZGlzcGF0Y2hGb3JtLnRyYWlsZXJOdW1iZXIgPSBpdGVtLnRyYWlsZXJOdW1iZXI7CiAgICAgICAgICAgIF90aGlzOC5kaXNwYXRjaEZvcm0udHJhaWxlcklkID0gaXRlbS50cmFpbGVySWQ7CiAgICAgICAgICAgIF90aGlzOC5kaXNwYXRjaEZvcm0uYXhpc1R5cGUgPSBpdGVtLmF4aXNUeXBlOwogICAgICAgICAgICBfdGhpczguZGlzcGF0Y2hGb3JtLmRyaXZlcldlaWdodCA9IGl0ZW0uZHJpdmVyV2VpZ2h0OwogICAgICAgICAgICBfdGhpczguZGlzcGF0Y2hGb3JtLm1heFdlaWdodCA9IGl0ZW0ubWF4V2VpZ2h0OwogICAgICAgICAgICBfdGhpczguZGlzcGF0Y2hGb3JtLmVuZ2luZU51bWJlciA9IGl0ZW0uZW5naW5lTnVtYmVyOwogICAgICAgICAgICBfdGhpczguZGlzcGF0Y2hGb3JtLnZpbk51bWJlciA9IGl0ZW0udmluTnVtYmVyOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOafpeivouWPuOacuuS/oeaBr+WIl+ihqCAqL2dldERyaXZlckxpc3Q6IGZ1bmN0aW9uIGdldERyaXZlckxpc3QoKSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICAvLyBsaXN0QWxsRHJpdmVyKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgIC8vICAgdGhpcy5kcml2ZXJMaXN0ID0gcmVzcG9uc2UuZGF0YTsKICAgICAgLy8gICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgLy8gfSk7CiAgICAgICgwLCBfZHJpdmVyLmdldFhjdGdEcml2ZXJVc2VyTGlzdEJ5UGFnZSkoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzOS5kcml2ZXJMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBjb25zb2xlLmxvZygidGhpcy5kcml2ZXJMaXN0IiwgX3RoaXM5LmRyaXZlckxpc3QpOwogICAgICAgIF90aGlzOS5maWx0ZXJlZERyaXZlck9wdGlvbnMgPSBfdGhpczkuZHJpdmVyTGlzdDsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5pCc57Si6L+H5ruk6YC76L6RCiAgICBmaWx0ZXJEcml2ZXJEYXRhOiBmdW5jdGlvbiBmaWx0ZXJEcml2ZXJEYXRhKHF1ZXJ5KSB7CiAgICAgIHRoaXMuc2VhcmNoRHJpdmVyUXVlcnkgPSBxdWVyeTsKICAgICAgaWYgKHRoaXMuc2VhcmNoRHJpdmVyUXVlcnkpIHsKICAgICAgICB0aGlzLmZpbHRlcmVkRHJpdmVyT3B0aW9ucyA9IHRoaXMuZHJpdmVyTGlzdC5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLmRyaXZlckluZm8uaW5jbHVkZXMocXVlcnkpOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZmlsdGVyZWREcml2ZXJPcHRpb25zID0gdGhpcy5kcml2ZXJMaXN0LnNsaWNlKDAsIDUwKTsKICAgICAgfQogICAgfSwKICAgIC8vIOino+aekOWbvueJh+WSjOaWh+S7tuaVsOaNrgogICAgcGFyc2VJbWFnZUFuZEZpbGVEYXRhOiBmdW5jdGlvbiBwYXJzZUltYWdlQW5kRmlsZURhdGEoKSB7CiAgICAgIC8vIOino+aekOWbvueJh+aVsOaNrgogICAgICBpZiAodGhpcy5wbGFuSW5mby5hcHBseUltZ1VybCkgewogICAgICAgIHRyeSB7CiAgICAgICAgICB0aGlzLmltYWdlTGlzdCA9IEpTT04ucGFyc2UodGhpcy5wbGFuSW5mby5hcHBseUltZ1VybCk7CiAgICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q5Zu+54mH5pWw5o2u5aSx6LSlOicsIGUpOwogICAgICAgICAgdGhpcy5pbWFnZUxpc3QgPSBbXTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOino+aekOaWh+S7tuaVsOaNrgogICAgICBpZiAodGhpcy5wbGFuSW5mby5hcHBseUZpbGVVcmwpIHsKICAgICAgICB0cnkgewogICAgICAgICAgdGhpcy5maWxlTGlzdCA9IEpTT04ucGFyc2UodGhpcy5wbGFuSW5mby5hcHBseUZpbGVVcmwpOwogICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOaWh+S7tuaVsOaNruWksei0pTonLCBlKTsKICAgICAgICAgIHRoaXMuZmlsZUxpc3QgPSBbXTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvLyDkuIvovb3mlofku7YKICAgIGRvd25sb2FkRmlsZTogZnVuY3Rpb24gZG93bmxvYWRGaWxlKHVybCwgZmlsZU5hbWUpIHsKICAgICAgaWYgKCF1cmwpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7bpk77mjqXml6DmlYgnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOWIm+W7uuS4gOS4qmHlhYPntKDnlKjkuo7kuIvovb0KICAgICAgdmFyIGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgIGxpbmsuaHJlZiA9IHVybDsKICAgICAgbGluay5kb3dubG9hZCA9IGZpbGVOYW1lIHx8ICfkuIvovb3mlofku7YnOwogICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspOwogICAgICBsaW5rLmNsaWNrKCk7CiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7CiAgICB9LAogICAgLy8g6I635Y+W6K6h5YiS57G75Z6L5paH5pysCiAgICBnZXRQbGFuVHlwZVRleHQ6IGZ1bmN0aW9uIGdldFBsYW5UeXBlVGV4dCh0eXBlKSB7CiAgICAgIHZhciB0eXBlTWFwID0gewogICAgICAgIDE6ICflh7rljoLkuI3ov5Tlm54nLAogICAgICAgIDI6ICflh7rljoLov5Tlm54nLAogICAgICAgIDM6ICfot6jljLrosIPmi6gnLAogICAgICAgIDQ6ICfpgIDotKfnlLPor7cnCiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfmnKrnn6XnsbvlnosnOwogICAgfSwKICAgIC8vIOiOt+WPluS4muWKoeexu+Wei+aWh+acrAogICAgZ2V0QnVzaW5lc3NDYXRlZ29yeVRleHQ6IGZ1bmN0aW9uIGdldEJ1c2luZXNzQ2F0ZWdvcnlUZXh0KGNhdGVnb3J5KSB7CiAgICAgIHZhciBjYXRlZ29yeU1hcCA9IHsKICAgICAgICAxOiAn6YCa55SoJywKICAgICAgICAxMTogJ+mAmueUqCcsCiAgICAgICAgMTI6ICflp5TlpJbliqDlt6UnLAogICAgICAgIDIxOiAn5pyJ6K6h5YiS6YeP6K6h6YePJywKICAgICAgICAyMjogJ+efreacnycsCiAgICAgICAgMjM6ICfpkqLmnb/vvIjlnIbpkqLvvIknLAogICAgICAgIDMxOiAn6YCa55SoJwogICAgICB9OwogICAgICByZXR1cm4gY2F0ZWdvcnlNYXBbY2F0ZWdvcnldIHx8ICfmnKrnn6XnsbvlnosnOwogICAgfSwKICAgIC8vIOiOt+WPlueJqei1hOexu+Wei+aWh+acrAogICAgZ2V0TWF0ZXJpYWxUeXBlVGV4dDogZnVuY3Rpb24gZ2V0TWF0ZXJpYWxUeXBlVGV4dCh0eXBlKSB7CiAgICAgIHZhciB0eXBlTWFwID0gewogICAgICAgIDE6ICfpkqLmnZAnLAogICAgICAgIDI6ICfpkqLmnb8nLAogICAgICAgIDM6ICflhbbku5YnCiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfmnKrnn6XnsbvlnosnOwogICAgfSwKICAgIC8vIOiOt+WPluiuoeWIkueKtuaAgeaWh+acrAogICAgZ2V0UGxhblN0YXR1c1RleHQ6IGZ1bmN0aW9uIGdldFBsYW5TdGF0dXNUZXh0KHN0YXR1cykgewogICAgICB2YXIgc3RhdHVzTWFwID0gewogICAgICAgIDE6ICflvoXliIbljoLlrqHmibknLAogICAgICAgIDI6ICflvoXliIbljoLlpI3lrqEnLAogICAgICAgIDM6ICflvoXnlJ/kuqfmjIfmjKXkuK3lv4PlrqHmibknLAogICAgICAgIDQ6ICflrqHmibnlrozmiJAnLAogICAgICAgIDU6ICflt7Llh7rljoInLAogICAgICAgIDY6ICfpg6jliIbmlLbotKcnLAogICAgICAgIDc6ICflt7LlrozmiJAnLAogICAgICAgIDExOiAn6amz5ZueJywKICAgICAgICAxMjogJ+W6n+W8gycsCiAgICAgICAgMTM6ICfov4fmnJ8nCiAgICAgIH07CiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAn5pyq55+l54q25oCBJzsKICAgIH0sCiAgICAvLyDojrflj5bml6Xlv5fpopzoibIKICAgIGdldExvZ0NvbG9yOiBmdW5jdGlvbiBnZXRMb2dDb2xvcihsb2cpIHsKICAgICAgdmFyIGxvZ1R5cGVDb2xvck1hcCA9IHsKICAgICAgICAxOiAnIzQwOUVGRicsCiAgICAgICAgLy8g5Yib5bu6CiAgICAgICAgMjogJyM2N0MyM0EnLAogICAgICAgIC8vIOWuoeaJuQogICAgICAgIDM6ICcjRTZBMjNDJywKICAgICAgICAvLyDmtYHovawKICAgICAgICA0OiAnI0Y1NkM2QycsCiAgICAgICAgLy8g6amz5ZueCiAgICAgICAgNTogJyM5MDkzOTknIC8vIOWFtuS7lgogICAgICB9OwogICAgICByZXR1cm4gbG9nVHlwZUNvbG9yTWFwW2xvZy5sb2dUeXBlXSB8fCAnIzQwOUVGRic7CiAgICB9LAogICAgLy8g6I635Y+W5rS+6L2m54q25oCB5paH5pysCiAgICBnZXREaXNwYXRjaFN0YXR1c1RleHQ6IGZ1bmN0aW9uIGdldERpc3BhdGNoU3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgdmFyIHN0YXR1c01hcCA9IHsKICAgICAgICAwOiAn5b6F5Ye65Y+RJywKICAgICAgICAxOiAn5bey5Ye65Y+RJywKICAgICAgICAyOiAn5bey5Yiw6L6+JywKICAgICAgICAzOiAn5bey5a6M5oiQJywKICAgICAgICA0OiAn5bey5Y+W5raIJwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ+acquefpeeKtuaAgSc7CiAgICB9LAogICAgLy8g6I635Y+W5rS+6L2m54q25oCB57G75Z6L77yI55So5LqO5qCH562+6aKc6Imy77yJCiAgICBnZXREaXNwYXRjaFN0YXR1c1R5cGU6IGZ1bmN0aW9uIGdldERpc3BhdGNoU3RhdHVzVHlwZShzdGF0dXMpIHsKICAgICAgdmFyIHN0YXR1c01hcCA9IHsKICAgICAgICAwOiAnaW5mbycsCiAgICAgICAgMTogJ3ByaW1hcnknLAogICAgICAgIDI6ICdzdWNjZXNzJywKICAgICAgICAzOiAnc3VjY2VzcycsCiAgICAgICAgNDogJ2RhbmdlcicKICAgICAgfTsKICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICdpbmZvJzsKICAgIH0sCiAgICAvLyDojrflj5borqHliJLnsbvlnovmoIfnrb7moLflvI8KICAgIGdldFBsYW5UeXBlVGFnVHlwZTogZnVuY3Rpb24gZ2V0UGxhblR5cGVUYWdUeXBlKHR5cGUpIHsKICAgICAgdmFyIHR5cGVNYXAgPSB7CiAgICAgICAgMTogJ3N1Y2Nlc3MnLAogICAgICAgIC8vIOWHuuWOguS4jei/lOWbngogICAgICAgIDI6ICd3YXJuaW5nJywKICAgICAgICAvLyDlh7rljoLov5Tlm54KICAgICAgICAzOiAnaW5mbycsCiAgICAgICAgLy8g6Leo5Yy66LCD5ouoCiAgICAgICAgNDogJ2RhbmdlcicgLy8g6YCA6LSn55Sz6K+3CiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICdpbmZvJzsKICAgIH0sCiAgICAvLyDojrflj5bnianotYTnsbvlnovmoIfnrb7moLflvI8KICAgIGdldE1hdGVyaWFsVHlwZVRhZ1R5cGU6IGZ1bmN0aW9uIGdldE1hdGVyaWFsVHlwZVRhZ1R5cGUodHlwZSkgewogICAgICB2YXIgdHlwZU1hcCA9IHsKICAgICAgICAxOiAncHJpbWFyeScsCiAgICAgICAgLy8g6ZKi5p2QCiAgICAgICAgMjogJ3N1Y2Nlc3MnLAogICAgICAgIC8vIOmSouadvwogICAgICAgIDM6ICdpbmZvJyAvLyDlhbbku5YKICAgICAgfTsKICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgJ2luZm8nOwogICAgfSwKICAgIC8vIOiOt+WPluS4muWKoeexu+Wei+agh+etvuagt+W8jwogICAgZ2V0QnVzaW5lc3NDYXRlZ29yeVRhZ1R5cGU6IGZ1bmN0aW9uIGdldEJ1c2luZXNzQ2F0ZWdvcnlUYWdUeXBlKGNhdGVnb3J5KSB7CiAgICAgIHZhciB0eXBlTWFwID0gewogICAgICAgICcxJzogJ3ByaW1hcnknLAogICAgICAgIC8vIOmAmueUqAogICAgICAgICcxMSc6ICdwcmltYXJ5JywKICAgICAgICAvLyDpgJrnlKgKICAgICAgICAnMTInOiAnd2FybmluZycsCiAgICAgICAgLy8g5aeU5aSW5Yqg5belCiAgICAgICAgJzIxJzogJ3N1Y2Nlc3MnLAogICAgICAgIC8vIOacieiuoeWIkumHj+iuoemHjwogICAgICAgICcyMic6ICdpbmZvJywKICAgICAgICAvLyDnn63mnJ8KICAgICAgICAnMjMnOiAnZGFuZ2VyJywKICAgICAgICAvLyDpkqLmnb/vvIjlnIbpkqLvvIkKICAgICAgICAnMzEnOiAncHJpbWFyeScgLy8g6YCa55SoCiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTWFwW2NhdGVnb3J5XSB8fCAnaW5mbyc7CiAgICB9LAogICAgLy8g5omT5byA5rS+6L2m5by55qGGCiAgICBvcGVuRGlzcGF0Y2hEaWFsb2c6IGZ1bmN0aW9uIG9wZW5EaXNwYXRjaERpYWxvZygpIHsKICAgICAgLy8g5Yid5aeL5YyW54mp6LWE5pWw5o2uCiAgICAgIHRoaXMuYXZhaWxhYmxlTWF0ZXJpYWxzID0gdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMgfHwgW107CiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLmF2YWlsYWJsZU1hdGVyaWFscyIsIHRoaXMuYXZhaWxhYmxlTWF0ZXJpYWxzKTsKCiAgICAgIC8vIOiOt+WPluW3sua0vui9pueahOeJqei1hOWIl+ihqO+8jOW5tuWcqOWbnuiwg+S4reWIneWni+WMliBtYXRlcmlhbFNlbGVjdGlvbkxpc3QKICAgICAgdGhpcy5nZXRUYXNrTWF0ZXJpYWxMaXN0QW5kSW5pdFNlbGVjdGlvbigpOwogICAgICAvLyDliKTmlq3pnZ7orqHph4/kuJR0YXNrVHlwZeS4ujHnmoTmg4XlhrUKICAgICAgaWYgKHRoaXMucGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMCkgewogICAgICAgIGlmICh0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSA9PSAxKSB7CiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7Lnu4/mnIl0YXNrVHlwZeS4ujHnmoTku7vliqEKICAgICAgICAgIHZhciBoYXNUeXBlMVRhc2sgPSB0aGlzLnRhc2tMaXN0SW5mby5zb21lKGZ1bmN0aW9uICh0YXNrKSB7CiAgICAgICAgICAgIHJldHVybiB0YXNrLnRhc2tUeXBlID09PSAxOwogICAgICAgICAgfSk7CiAgICAgICAgICBpZiAoaGFzVHlwZTFUYXNrKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6Z2e6K6h6YeP5Y+q6IO95rS+6L2m5Ye65Y6C5LiA5qyhJyk7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICAgIGNvbnNvbGUubG9nKCJoYXNUeXBlMVRhc2siLCBoYXNUeXBlMVRhc2spOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5Yik5pat55So5oi36KeS6Imy5p2D6ZmQCiAgICAgIHZhciByb2xlcyA9IHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZXM7CiAgICAgIGNvbnNvbGUubG9nKCJyb2xlcyIsIHJvbGVzKTsKICAgICAgaWYgKCFyb2xlcy5pbmNsdWRlcygnbGVhdmUuc3VwcGxpZXInKSAmJiAhcm9sZXMuaW5jbHVkZXMoJ2xlYXZlLmFwcGxpY2FudCcpKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5oKo5rKh5pyJ5rS+6L2m5p2D6ZmQJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLnBsYW5JbmZvLnBsYW5TdGF0dXMiLCB0aGlzLnBsYW5JbmZvLnBsYW5TdGF0dXMpOwogICAgICBpZiAoIVs0LCA1LCA2XS5pbmNsdWRlcyh0aGlzLnBsYW5JbmZvLnBsYW5TdGF0dXMpKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflvZPliY3nirbmgIHml6Dms5XmtL7ovaYnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgY29uc29sZS5sb2coIm9wZW5EaXNwYXRjaERpYWxvZyIsIHRoaXMudGFza0xpc3RJbmZvLmxlbmd0aCk7CiAgICAgIGlmICh0aGlzLnBsYW5JbmZvLmJ1c2luZXNzQ2F0ZWdvcnkgPT0gMjIgJiYgdGhpcy50YXNrTGlzdEluZm8ubGVuZ3RoID49IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+efreacn+iuoeWIkuWPquWFgeiuuOa0vuS4gOasoei9picpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAodGhpcy5wbGFuSW5mby5idXNpbmVzc0NhdGVnb3J5ID09IDIzICYmIHRoaXMudGFza0xpc3RJbmZvLmxlbmd0aCA+PSAxKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfpkqLmnb/vvIjlnIbpkqLvvInorqHliJLlj6rlhYHorrjmtL7kuIDmrKHovaYnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5kaXNwYXRjaEZvcm0gPSB7fTsKICAgICAgaWYgKHRoaXMucGxhbkluZm8ucGxhblR5cGUgPT0gMSkgewogICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlID0gMTsKICAgICAgfSBlbHNlIGlmICh0aGlzLnBsYW5JbmZvLnBsYW5UeXBlID09IDMpIHsKICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSA9IDM7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5wbGFuSW5mby5wbGFuVHlwZSA9PSA0KSB7CiAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPSAxOwogICAgICB9CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlKSwgdGhpcy5kaXNwYXRjaERpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8vIOaWsOWinuaWueazlQogICAgZ2V0VGFza01hdGVyaWFsTGlzdEFuZEluaXRTZWxlY3Rpb246IGZ1bmN0aW9uIGdldFRhc2tNYXRlcmlhbExpc3RBbmRJbml0U2VsZWN0aW9uKCkgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgLy8g5riF56m65bey55So5pWw6YeP5pig5bCECiAgICAgIHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5jbGVhcigpOwogICAgICAvLyDnu5/orqHmiYDmnInlt7LmtL7ovabnianotYQKICAgICAgdmFyIHR5cGUyTGlzdCA9IHRoaXMudGFza0xpc3RJbmZvLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnRhc2tUeXBlID09PSAyOwogICAgICB9KTsKICAgICAgY29uc29sZS5sb2coInR5cGUyTGlzdCIsIHR5cGUyTGlzdCk7CiAgICAgIGlmICghdHlwZTJMaXN0IHx8IHR5cGUyTGlzdC5sZW5ndGggPT09IDApIHsKICAgICAgICAvLyDliJ3lp4vljJYgbWF0ZXJpYWxTZWxlY3Rpb25MaXN077ya5YWo6YOo6YCJ5LiK5LiU5pWw6YeP5Li65Ymp5L2Z5pWw6YePCiAgICAgICAgdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QgPSAodGhpcy5wbGFuSW5mby5tYXRlcmlhbHMgfHwgW10pLm1hcChmdW5jdGlvbiAobWF0KSB7CiAgICAgICAgICAvLyBjb25zdCB1c2VkTnVtID0gKHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5nZXQobWF0Lm1hdGVyaWFsSWQpPy51c2VkTnVtKSB8fCAwOwogICAgICAgICAgLy8gY29uc3QgcmVtYWluaW5nTnVtID0gTWF0aC5tYXgoKG1hdC5wbGFuTnVtIHx8IDApIC0gdXNlZE51bSwgMCk7CiAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICBtYXRlcmlhbElkOiBtYXQubWF0ZXJpYWxJZCwKICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXQubWF0ZXJpYWxOYW1lLAogICAgICAgICAgICBtYXRlcmlhbFNwZWM6IG1hdC5tYXRlcmlhbFNwZWMsCiAgICAgICAgICAgIHBsYW5OdW06IG1hdC5wbGFuTnVtLAogICAgICAgICAgICB1c2VkTnVtOiAwLAogICAgICAgICAgICByZW1haW5pbmdOdW06IG1hdC5wbGFuTnVtLAogICAgICAgICAgICBjdXJyZW50TnVtOiBtYXQucGxhbk51bQogICAgICAgICAgfTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjb25zb2xlLmxvZygidGhpcy50YXNrTGlzdEluZm8iLCB0aGlzLnRhc2tMaXN0SW5mbyk7CiAgICAgICAgdHlwZTJMaXN0LmZvckVhY2goZnVuY3Rpb24gKHRhc2spIHsKICAgICAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgICAgIHRhc2tObzogdGFzay50YXNrTm8KICAgICAgICAgIH07CiAgICAgICAgICAoMCwgX3BsYW4ubGlzdFRhc2tNYXRlcmlhbCkocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICB2YXIgdGFza01hdGVyaWFscyA9IHJlc3BvbnNlLnJvd3MgfHwgW107CiAgICAgICAgICAgIHRhc2tNYXRlcmlhbHMuZm9yRWFjaChmdW5jdGlvbiAobWF0ZXJpYWwpIHsKICAgICAgICAgICAgICBpZiAoIV90aGlzMC50YXNrTWF0ZXJpYWxMaXN0TWFwLmhhcyhtYXRlcmlhbC5tYXRlcmlhbElkKSkgewogICAgICAgICAgICAgICAgX3RoaXMwLnRhc2tNYXRlcmlhbExpc3RNYXAuc2V0KG1hdGVyaWFsLm1hdGVyaWFsSWQsIHsKICAgICAgICAgICAgICAgICAgdGFza01hdGVyaWFsSW5mbzogbWF0ZXJpYWwsCiAgICAgICAgICAgICAgICAgIHVzZWROdW06IG1hdGVyaWFsLnBsYW5OdW0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB2YXIgZXhpc3RpbmdNYXRlcmlhbCA9IF90aGlzMC50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChtYXRlcmlhbC5tYXRlcmlhbElkKTsKICAgICAgICAgICAgICAgIGV4aXN0aW5nTWF0ZXJpYWwudXNlZE51bSArPSBtYXRlcmlhbC5wbGFuTnVtOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAvLyDlsIZ0YXNrTWF0ZXJpYWxMaXN0TWFw6L2s5o2i5Li65pWw57uE6ZuG5ZCICiAgICAgICAgICAgIF90aGlzMC50YXNrTWF0ZXJpYWxMaXN0ID0gQXJyYXkuZnJvbShfdGhpczAudGFza01hdGVyaWFsTGlzdE1hcCwgZnVuY3Rpb24gKF9yZWYpIHsKICAgICAgICAgICAgICB2YXIgX3JlZjIgPSAoMCwgX3NsaWNlZFRvQXJyYXkyLmRlZmF1bHQpKF9yZWYsIDIpLAogICAgICAgICAgICAgICAga2V5ID0gX3JlZjJbMF0sCiAgICAgICAgICAgICAgICB2YWx1ZSA9IF9yZWYyWzFdOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoewogICAgICAgICAgICAgICAgbWF0ZXJpYWxJZDoga2V5CiAgICAgICAgICAgICAgfSwgdmFsdWUpOwogICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIC8vIOWIneWni+WMliBtYXRlcmlhbFNlbGVjdGlvbkxpc3TvvJrlhajpg6jpgInkuIrkuJTmlbDph4/kuLrliankvZnmlbDph48KICAgICAgICAgICAgX3RoaXMwLm1hdGVyaWFsU2VsZWN0aW9uTGlzdCA9IChfdGhpczAucGxhbkluZm8ubWF0ZXJpYWxzIHx8IFtdKS5tYXAoZnVuY3Rpb24gKG1hdCkgewogICAgICAgICAgICAgIHZhciBfdGhpczAkdGFza01hdGVyaWFsTGk7CiAgICAgICAgICAgICAgdmFyIHVzZWROdW0gPSAoKF90aGlzMCR0YXNrTWF0ZXJpYWxMaSA9IF90aGlzMC50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChtYXQubWF0ZXJpYWxJZCkpID09PSBudWxsIHx8IF90aGlzMCR0YXNrTWF0ZXJpYWxMaSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXMwJHRhc2tNYXRlcmlhbExpLnVzZWROdW0pIHx8IDA7CiAgICAgICAgICAgICAgdmFyIHJlbWFpbmluZ051bSA9IE1hdGgubWF4KChtYXQucGxhbk51bSB8fCAwKSAtIHVzZWROdW0sIDApOwogICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICBtYXRlcmlhbElkOiBtYXQubWF0ZXJpYWxJZCwKICAgICAgICAgICAgICAgIG1hdGVyaWFsTmFtZTogbWF0Lm1hdGVyaWFsTmFtZSwKICAgICAgICAgICAgICAgIG1hdGVyaWFsU3BlYzogbWF0Lm1hdGVyaWFsU3BlYywKICAgICAgICAgICAgICAgIHBsYW5OdW06IG1hdC5wbGFuTnVtLAogICAgICAgICAgICAgICAgdXNlZE51bTogdXNlZE51bSwKICAgICAgICAgICAgICAgIHJlbWFpbmluZ051bTogcmVtYWluaW5nTnVtLAogICAgICAgICAgICAgICAgY3VycmVudE51bTogcmVtYWluaW5nTnVtCiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIF90aGlzMC5tYXRlcmlhbFNlbGVjdGlvbkxpc3QgPSBfdGhpczAubWF0ZXJpYWxTZWxlY3Rpb25MaXN0LmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgIHJldHVybiBpdGVtLnJlbWFpbmluZ051bSA+IDA7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0KCiAgICAgIC8vIOWIpOaWremdnuiuoemHj+S4lHRhc2tUeXBl5Li6MeeahOaDheWGtQogICAgICBpZiAodGhpcy5wbGFuSW5mby5tZWFzdXJlRmxhZyA9PSAwKSB7CiAgICAgICAgaWYgKHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlID09IDEpIHsKICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3sue7j+aciXRhc2tUeXBl5Li6MeeahOS7u+WKoQogICAgICAgICAgdmFyIGhhc1R5cGUxVGFzayA9IHRoaXMudGFza0xpc3RJbmZvLnNvbWUoZnVuY3Rpb24gKHRhc2spIHsKICAgICAgICAgICAgcmV0dXJuIHRhc2sudGFza1R5cGUgPT09IDE7CiAgICAgICAgICB9KTsKICAgICAgICAgIGlmIChoYXNUeXBlMVRhc2spIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfpnZ7orqHph4/lj6rog73mtL7ovablh7rljoLkuIDmrKEnKTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQogICAgICAgICAgY29uc29sZS5sb2coImhhc1R5cGUxVGFzayIsIGhhc1R5cGUxVGFzayk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy8g6YeN572u5rS+6L2m6KGo5Y2VCiAgICByZXNldERpc3BhdGNoRm9ybTogZnVuY3Rpb24gcmVzZXREaXNwYXRjaEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMuZGlzcGF0Y2hGb3JtICYmIHRoaXMuJHJlZnMuZGlzcGF0Y2hGb3JtLnJlc2V0RmllbGRzKCk7CiAgICAgIHRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0ID0gW3sKICAgICAgICBtYXRlcmlhbElkOiBudWxsLAogICAgICAgIG1hdGVyaWFsTmFtZTogJycsCiAgICAgICAgbWF0ZXJpYWxTcGVjOiAnJywKICAgICAgICBwbGFuTnVtOiAwLAogICAgICAgIHJlbWFpbmluZ051bTogMCwKICAgICAgICB1c2VkTnVtOiAwLAogICAgICAgIGN1cnJlbnROdW06IDAKICAgICAgfV07CiAgICB9LAogICAgLy8g6I635Y+W5bey5rS+6L2m55qE54mp6LWE5YiX6KGoCiAgICBnZXRUYXNrTWF0ZXJpYWxMaXN0OiBmdW5jdGlvbiBnZXRUYXNrTWF0ZXJpYWxMaXN0KCkgewogICAgICB2YXIgX3RoaXMxID0gdGhpczsKICAgICAgLy8g5LuOdGFza0xpc3RJbmZv5Lit6I635Y+W5bey5rS+6L2m55qE54mp6LWE5L+h5oGvCiAgICAgIHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5jbGVhcigpOwogICAgICB0aGlzLnRhc2tMaXN0SW5mby5mb3JFYWNoKGZ1bmN0aW9uICh0YXNrKSB7CiAgICAgICAgdmFyIHBhcmFtcyA9IHsKICAgICAgICAgIHRhc2tObzogdGFzay50YXNrTm8KICAgICAgICB9OwogICAgICAgICgwLCBfcGxhbi5saXN0VGFza01hdGVyaWFsKShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICBjb25zb2xlLmxvZygibGlzdFRhc2tNYXRlcmlhbCIsIHJlc3BvbnNlLnJvd3MpOwogICAgICAgICAgdmFyIHRhc2tNYXRlcmlhbHMgPSBbXTsKICAgICAgICAgIHRhc2tNYXRlcmlhbHMgPSByZXNwb25zZS5yb3dzOwogICAgICAgICAgdGFza01hdGVyaWFscy5mb3JFYWNoKGZ1bmN0aW9uIChtYXRlcmlhbCkgewogICAgICAgICAgICBpZiAoIV90aGlzMS50YXNrTWF0ZXJpYWxMaXN0TWFwLmhhcyhtYXRlcmlhbC5tYXRlcmlhbElkKSkgewogICAgICAgICAgICAgIF90aGlzMS50YXNrTWF0ZXJpYWxMaXN0TWFwLnNldChtYXRlcmlhbC5tYXRlcmlhbElkLCB7CiAgICAgICAgICAgICAgICB0YXNrTWF0ZXJpYWxJbmZvOiBtYXRlcmlhbCwKICAgICAgICAgICAgICAgIHVzZWROdW06IG1hdGVyaWFsLnBsYW5OdW0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB2YXIgZXhpc3RpbmdNYXRlcmlhbCA9IF90aGlzMS50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChtYXRlcmlhbC5tYXRlcmlhbElkKTsKICAgICAgICAgICAgICBleGlzdGluZ01hdGVyaWFsLnVzZWROdW0gKz0gbWF0ZXJpYWwucGxhbk51bTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgICAvLyDlsIZ0YXNrTWF0ZXJpYWxMaXN0TWFw6L2s5o2i5Li65pWw57uE6ZuG5ZCICiAgICAgICAgICBfdGhpczEudGFza01hdGVyaWFsTGlzdCA9IEFycmF5LmZyb20oX3RoaXMxLnRhc2tNYXRlcmlhbExpc3RNYXAsIGZ1bmN0aW9uIChfcmVmMykgewogICAgICAgICAgICB2YXIgX3JlZjQgPSAoMCwgX3NsaWNlZFRvQXJyYXkyLmRlZmF1bHQpKF9yZWYzLCAyKSwKICAgICAgICAgICAgICBrZXkgPSBfcmVmNFswXSwKICAgICAgICAgICAgICB2YWx1ZSA9IF9yZWY0WzFdOwogICAgICAgICAgICByZXR1cm4gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHsKICAgICAgICAgICAgICBtYXRlcmlhbElkOiBrZXkKICAgICAgICAgICAgfSwgdmFsdWUpOwogICAgICAgICAgfSk7CiAgICAgICAgICBjb25zb2xlLmxvZygidGFza01hdGVyaWFsQXJyYXkiLCBfdGhpczEudGFza01hdGVyaWFsTGlzdCk7CiAgICAgICAgICBjb25zb2xlLmxvZygidGFza01hdGVyaWFsTGlzdE1hcCIsIF90aGlzMS50YXNrTWF0ZXJpYWxMaXN0TWFwKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5re75Yqg54mp6LWE6KGMCiAgICBhZGRNYXRlcmlhbFJvdzogZnVuY3Rpb24gYWRkTWF0ZXJpYWxSb3coKSB7CiAgICAgIHRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0LnB1c2goewogICAgICAgIG1hdGVyaWFsSWQ6IG51bGwsCiAgICAgICAgbWF0ZXJpYWxOYW1lOiAnJywKICAgICAgICBtYXRlcmlhbFNwZWM6ICcnLAogICAgICAgIHBsYW5OdW06IDAsCiAgICAgICAgcmVtYWluaW5nTnVtOiAwLAogICAgICAgIHVzZWROdW06IDAsCiAgICAgICAgY3VycmVudE51bTogMAogICAgICB9KTsKICAgIH0sCiAgICAvLyDnp7vpmaTnianotYTooYwKICAgIHJlbW92ZU1hdGVyaWFsOiBmdW5jdGlvbiByZW1vdmVNYXRlcmlhbChpbmRleCkgewogICAgICB0aGlzLm1hdGVyaWFsU2VsZWN0aW9uTGlzdC5zcGxpY2UoaW5kZXgsIDEpOwogICAgfSwKICAgIC8vIOWkhOeQhueJqei1hOmAieaLqeWPmOWMlgogICAgaGFuZGxlTWF0ZXJpYWxDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZU1hdGVyaWFsQ2hhbmdlKHJvdywgaW5kZXgpIHsKICAgICAgY29uc29sZS5sb2coImhhbmRsZU1hdGVyaWFsQ2hhbmdlIiwgdGhpcy50YXNrTWF0ZXJpYWxMaXN0KTsKICAgICAgdmFyIHNlbGVjdGVkTWF0ZXJpYWwgPSB0aGlzLnRhc2tNYXRlcmlhbExpc3QuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm1hdGVyaWFsSWQgPT09IHJvdy5tYXRlcmlhbElkOwogICAgICB9KTsKICAgICAgaWYgKHNlbGVjdGVkTWF0ZXJpYWwpIHsKICAgICAgICByb3cudXNlZE51bSA9IHNlbGVjdGVkTWF0ZXJpYWwudXNlZE51bTsKICAgICAgfQogICAgICB2YXIgc2VsZWN0UGxhbk1hdGVyaWFsID0gdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm1hdGVyaWFsSWQgPT09IHJvdy5tYXRlcmlhbElkOwogICAgICB9KTsKICAgICAgaWYgKHNlbGVjdFBsYW5NYXRlcmlhbCkgewogICAgICAgIHJvdy5wbGFuTnVtID0gc2VsZWN0UGxhbk1hdGVyaWFsLnBsYW5OdW07CiAgICAgICAgcm93Lm1hdGVyaWFsTmFtZSA9IHNlbGVjdFBsYW5NYXRlcmlhbC5tYXRlcmlhbE5hbWU7CiAgICAgICAgcm93Lm1hdGVyaWFsU3BlYyA9IHNlbGVjdFBsYW5NYXRlcmlhbC5tYXRlcmlhbFNwZWM7CiAgICAgIH0KICAgICAgcm93LnJlbWFpbmluZ051bSA9IHJvdy5wbGFuTnVtIC0gcm93LnVzZWROdW07CiAgICAgIHJvdy5jdXJyZW50TnVtID0gcm93LnBsYW5OdW0gLSByb3cudXNlZE51bTsKICAgICAgY29uc29sZS5sb2coImhhbmRsZU1hdGVyaWFsQ2hhbmdlIiwgcm93LCBpbmRleCk7CiAgICB9LAogICAgLy8g6I635Y+W54mp6LWE5pyA5aSn5Y+v55So5pWw6YePCiAgICBnZXRNYXhBdmFpbGFibGVOdW06IGZ1bmN0aW9uIGdldE1heEF2YWlsYWJsZU51bShyb3cpIHsKICAgICAgaWYgKCFyb3cubWF0ZXJpYWxJZCkgcmV0dXJuIDA7CgogICAgICAvLyDku450YXNrTWF0ZXJpYWxMaXN0TWFw5Lit6I635Y+W5bey55So5pWw6YePCiAgICAgIHZhciBtYXRlcmlhbEluZm8gPSB0aGlzLnRhc2tNYXRlcmlhbExpc3RNYXAuZ2V0KHJvdy5tYXRlcmlhbElkKTsKICAgICAgdmFyIHVzZWROdW0gPSBtYXRlcmlhbEluZm8gPyBtYXRlcmlhbEluZm8udXNlZE51bSA6IDA7CiAgICAgIHJldHVybiByb3cucGxhbk51bSAtIHVzZWROdW07CiAgICB9LAogICAgLy8g5Yik5pat54mp6LWE5piv5ZCm5Y+v6YCJCiAgICBpc01hdGVyaWFsQXZhaWxhYmxlOiBmdW5jdGlvbiBpc01hdGVyaWFsQXZhaWxhYmxlKG1hdGVyaWFsKSB7CiAgICAgIC8vIOS7jnRhc2tNYXRlcmlhbExpc3RNYXDkuK3ojrflj5blt7LnlKjmlbDph48KICAgICAgLy8gY29uc3QgbWF0ZXJpYWxJbmZvID0gdGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChtYXRlcmlhbC5pZCk7CiAgICAgIC8vIGNvbnN0IHVzZWROdW0gPSBtYXRlcmlhbEluZm8gPyBtYXRlcmlhbEluZm8udXNlZE51bSA6IDA7CgogICAgICAvLyBsZXQgc2VsZWN0ZWQgPSBmYWxzZTsKCiAgICAgIC8vIHRoaXMuYXZhaWxhYmxlTWF0ZXJpYWxzLmZvckVhY2goaXRlbSA9PiB7CiAgICAgIC8vICAgaWYgKGl0ZW0ubWF0ZXJpYWxJZCA9PT0gbWF0ZXJpYWwubWF0ZXJpYWxJZCkgewogICAgICAvLyAgICAgc2VsZWN0ZWQgPSB0cnVlOwogICAgICAvLyAgIH0KICAgICAgLy8gfSk7CgogICAgICByZXR1cm4gdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3Quc29tZShmdW5jdGlvbiAocm93KSB7CiAgICAgICAgcmV0dXJuIHJvdy5tYXRlcmlhbElkID09PSBtYXRlcmlhbC5tYXRlcmlhbElkOwogICAgICB9KTsKICAgICAgOwogICAgfSwKICAgIC8vIOS/ruaUueaPkOS6pOa0vui9puihqOWNleaWueazlQogICAgc3VibWl0RGlzcGF0Y2hGb3JtOiBmdW5jdGlvbiBzdWJtaXREaXNwYXRjaEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgICAgdGhpcy4kcmVmcy5kaXNwYXRjaEZvcm0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAvLyDliKTmlq3pnZ7orqHph4/kuJR0YXNrVHlwZeS4ujHnmoTmg4XlhrUKICAgICAgICAgIGlmIChfdGhpczEwLnBsYW5JbmZvLm1lYXN1cmVGbGFnID09IDApIHsKICAgICAgICAgICAgaWYgKF90aGlzMTAuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlID09IDEpIHsKICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7Lnu4/mnIl0YXNrVHlwZeS4ujHnmoTku7vliqEKICAgICAgICAgICAgICB2YXIgaGFzVHlwZTFUYXNrID0gX3RoaXMxMC50YXNrTGlzdEluZm8uc29tZShmdW5jdGlvbiAodGFzaykgewogICAgICAgICAgICAgICAgcmV0dXJuIHRhc2sudGFza1R5cGUgPT09IDE7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgaWYgKGhhc1R5cGUxVGFzaykgewogICAgICAgICAgICAgICAgX3RoaXMxMC4kbWVzc2FnZS53YXJuaW5nKCfpnZ7orqHph4/lj6rog73mtL7ovablh7rljoLkuIDmrKEnKTsKICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KCiAgICAgICAgICAvLyDmlrDpm4blkIgKICAgICAgICAgIHZhciByZXN1bHRMaXN0ID0gW107CiAgICAgICAgICBjb25zb2xlLmxvZygidGhpcy5wbGFuSW5mby5tZWFzdXJlRmxhZyIsIF90aGlzMTAucGxhbkluZm8ubWVhc3VyZUZsYWcpOwogICAgICAgICAgY29uc29sZS5sb2coInRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlIiwgX3RoaXMxMC5kaXNwYXRjaEZvcm0udGFza1R5cGUpOwogICAgICAgICAgaWYgKF90aGlzMTAucGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMCAmJiBfdGhpczEwLmRpc3BhdGNoRm9ybS50YXNrVHlwZSA9PSAyKSB7CiAgICAgICAgICAgIF90aGlzMTAubWF0ZXJpYWxTZWxlY3Rpb25MaXN0LmZvckVhY2goZnVuY3Rpb24gKHNlbFJvdykgewogICAgICAgICAgICAgIC8vIOWcqCBwbGFuSW5mby5tYXRlcmlhbHMg5Lit5p+l5om+55u45ZCMIG1hdGVyaWFsSWQg55qE5YWD57SgCiAgICAgICAgICAgICAgdmFyIHBsYW5NYXRlcmlhbCA9IChfdGhpczEwLnBsYW5JbmZvLm1hdGVyaWFscyB8fCBbXSkuZmluZChmdW5jdGlvbiAobWF0KSB7CiAgICAgICAgICAgICAgICByZXR1cm4gbWF0Lm1hdGVyaWFsSWQgPT09IHNlbFJvdy5tYXRlcmlhbElkOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIGlmIChwbGFuTWF0ZXJpYWwpIHsKICAgICAgICAgICAgICAgIC8vIOa3seaLt+i0neS4gOS7ve+8jOmBv+WFjeW9seWTjeWOn+aVsOaNrgogICAgICAgICAgICAgICAgdmFyIG5ld0l0ZW0gPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHBsYW5NYXRlcmlhbCk7CiAgICAgICAgICAgICAgICBuZXdJdGVtLnBsYW5OdW0gPSBzZWxSb3cuY3VycmVudE51bTsgLy8g6K6+572u5Li65pys5qyh5pWw6YePCiAgICAgICAgICAgICAgICByZXN1bHRMaXN0LnB1c2gobmV3SXRlbSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIC8vIHJlc3VsdExpc3Qg5Y2z5Li65L2g6ZyA6KaB55qE5paw6ZuG5ZCICiAgICAgICAgICAgIGNvbnNvbGUubG9nKCd0aGlzLm1hdGVyaWFsU2VsZWN0aW9uTGlzdCcsIF90aGlzMTAubWF0ZXJpYWxTZWxlY3Rpb25MaXN0KTsKICAgICAgICAgICAgY29uc29sZS5sb2coJ3Jlc3VsdExpc3QnLCByZXN1bHRMaXN0KTsKCiAgICAgICAgICAgIC8vIOeJqei1hOagoemqjO+8muW/hemhu+acieeJqei1hAogICAgICAgICAgICBpZiAoIV90aGlzMTAubWF0ZXJpYWxTZWxlY3Rpb25MaXN0Lmxlbmd0aCkgewogICAgICAgICAgICAgIF90aGlzMTAuJG1lc3NhZ2Uud2FybmluZygn6K+36Iez5bCR6YCJ5oup5LiA56eN54mp6LWEJyk7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CgogICAgICAgICAgICAvLyDmoKHpqozmr4/kuIDooYznianotYQKICAgICAgICAgICAgdmFyIGhhc0ludmFsaWRNYXRlcmlhbCA9IF90aGlzMTAubWF0ZXJpYWxTZWxlY3Rpb25MaXN0LnNvbWUoZnVuY3Rpb24gKHJvdykgewogICAgICAgICAgICAgIC8vIOW/hemhu+mAieaLqeeJqei1hO+8jOaVsOmHjz4w77yM5LiU5pWw6YePPD3liankvZnmlbDph48KICAgICAgICAgICAgICByZXR1cm4gIXJvdy5tYXRlcmlhbElkIHx8IHJvdy5jdXJyZW50TnVtIDw9IDAgfHwgcm93LmN1cnJlbnROdW0gPiByb3cucmVtYWluaW5nTnVtOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgaWYgKGhhc0ludmFsaWRNYXRlcmlhbCkgewogICAgICAgICAgICAgIF90aGlzMTAuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup54mp6LWE5LiU5pys5qyh5pWw6YeP6ZyA5aSn5LqOMOS4lOS4jei2hei/h+WJqeS9meaVsOmHjycpOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY29uc29sZS5sb2coInRoaXMucGxhbkluZm8ubWF0ZXJpYWxzIiwgX3RoaXMxMC5wbGFuSW5mby5tYXRlcmlhbHMpOwogICAgICAgICAgICByZXN1bHRMaXN0ID0gX3RoaXMxMC5wbGFuSW5mby5tYXRlcmlhbHMgPyBfdGhpczEwLnBsYW5JbmZvLm1hdGVyaWFscy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBpdGVtKTsKICAgICAgICAgICAgfSkgOiBbXTsKICAgICAgICAgICAgY29uc29sZS5sb2coIjEyMzMyMSIsIHJlc3VsdExpc3QpOwogICAgICAgICAgfQogICAgICAgICAgaWYgKF90aGlzMTAucGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMSAmJiBfdGhpczEwLmRpc3BhdGNoRm9ybS50YXNrVHlwZSAhPT0gMikgewogICAgICAgICAgICBfdGhpczEwLmRpc3BhdGNoRm9ybS50YXNrU3RhdHVzID0gMTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzMTAuZGlzcGF0Y2hGb3JtLnRhc2tTdGF0dXMgPSA0OwogICAgICAgICAgfQogICAgICAgICAgaWYgKF90aGlzMTAuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlID09IDIpIHsKICAgICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0udGFza1N0YXR1cyA9IDU7CiAgICAgICAgICB9CgogICAgICAgICAgLy/mmK/lkKbnm7Tkvpvpu5jorqTkuLowCiAgICAgICAgICBfdGhpczEwLmRpc3BhdGNoRm9ybS5pc0RpcmVjdFN1cHBseSA9IDA7CiAgICAgICAgICAvLyB0b2RvIOS7u+WKoeeKtuaAgeehruiupAogICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0uYXBwbHlObyA9IF90aGlzMTAuYXBwbHlObzsKICAgICAgICAgIF90aGlzMTAuZGlzcGF0Y2hGb3JtLnBsYW5ObyA9IF90aGlzMTAucGxhbkluZm8ucGxhbk5vOwogICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0uY2FyTnVtID0gX3RoaXMxMC5kaXNwYXRjaEZvcm0uY2FyTnVtYmVyOwogICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0uY29tcGFueU5hbWUgPSBfdGhpczEwLmRpc3BhdGNoRm9ybS5jb21wYW55OwogICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0uZHJpdmVyTGljZW5zZUltZyA9IF90aGlzMTAuZGlzcGF0Y2hGb3JtLmRyaXZlckxpY2Vuc2VJbWdzOwogICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0uZHJpdmVyTmFtZSA9IF90aGlzMTAuZGlzcGF0Y2hGb3JtLm5hbWU7CiAgICAgICAgICBfdGhpczEwLmRpc3BhdGNoRm9ybS5tb2JpbGVQaG9uZSA9IF90aGlzMTAuZGlzcGF0Y2hGb3JtLnBob25lOwogICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0uZmFjZUltZyA9IF90aGlzMTAuZGlzcGF0Y2hGb3JtLnBob3RvOwogICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0uZHJpdmluZ0xpY2Vuc2VJbWcgPSBfdGhpczEwLmRpc3BhdGNoRm9ybS52ZWhpY2xlTGljZW5zZUltZ3M7CiAgICAgICAgICBfdGhpczEwLmRpc3BhdGNoRm9ybS5pZENhcmRObyA9IF90aGlzMTAuZGlzcGF0Y2hGb3JtLmlkQ2FyZDsKICAgICAgICAgIGlmIChfdGhpczEwLmRpc3BhdGNoRm9ybS5zZXggPT0gIjEiKSB7CiAgICAgICAgICAgIF90aGlzMTAuZGlzcGF0Y2hGb3JtLnNleCA9IDE7CiAgICAgICAgICB9IGVsc2UgaWYgKF90aGlzMTAuZGlzcGF0Y2hGb3JtLnNleCA9PSAiMiIpIHsKICAgICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0uc2V4ID0gMjsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChfdGhpczEwLmRpc3BhdGNoRm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPT0gIuWbveS6lCIpIHsKICAgICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID0gMTsKICAgICAgICAgIH0gZWxzZSBpZiAoX3RoaXMxMC5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID09ICLlm73lha0iKSB7CiAgICAgICAgICAgIF90aGlzMTAuZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9IDI7CiAgICAgICAgICB9IGVsc2UgaWYgKF90aGlzMTAuZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9PSAi5paw6IO95rqQIikgewogICAgICAgICAgICBfdGhpczEwLmRpc3BhdGNoRm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPSAzOwogICAgICAgICAgfQogICAgICAgICAgY29uc29sZS5sb2coInRoaXMuZGlzcGF0Y2hGb3JtIiwgX3RoaXMxMC5kaXNwYXRjaEZvcm0pOwogICAgICAgICAgdmFyIGRpc3BhdGNoSW5mbyA9IHt9OwogICAgICAgICAgZGlzcGF0Y2hJbmZvLmNhck51bSA9IF90aGlzMTAuZGlzcGF0Y2hGb3JtLmNhck51bTsKICAgICAgICAgICgwLCBfdGFzay5pc0FsbG93RGlzcGF0Y2gpKGRpc3BhdGNoSW5mbykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgdmFyIHJvdyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICAgIGlmIChyb3cgPiAwKSB7CiAgICAgICAgICAgICAgX3RoaXMxMC4kbWVzc2FnZS5lcnJvcigi5b2T5YmN6L2m5pyJ5q2j5Zyo5omn6KGM55qE5Lu75YqhIik7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdmFyIHBhcmFtID0ge307CiAgICAgICAgICAgICAgcGFyYW0ubGVhdmVUYXNrID0gX3RoaXMxMC5kaXNwYXRjaEZvcm07CiAgICAgICAgICAgICAgcGFyYW0ubGVhdmVUYXNrTWF0ZXJpYWxMaXN0ID0gcmVzdWx0TGlzdDsKICAgICAgICAgICAgICAoMCwgX3Rhc2suYWRkVGFza0FuZE1hdGVyaWFsQW5kQWRkTGVhdmVMb2cpKHBhcmFtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCJhZGRUYXNrQW5kTWF0ZXJpYWxBbmRBZGRMZWF2ZUxvZyIsIHJlcyk7CiAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTAuJG1lc3NhZ2Uuc3VjY2Vzcygn5rS+6L2m5oiQ5YqfJyk7CiAgICAgICAgICAgICAgICAgIF90aGlzMTAuZGlzcGF0Y2hEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIF90aGlzMTAuZ2V0TGlzdFRhc2tJbmZvKCk7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAvLyDlhbbku5blpLHotKXljp/lm6AKICAgICAgICAgICAgICAgICAgX3RoaXMxMC4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSB8fCAn5rS+6L2m5aSx6LSlJyk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycikgewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignZGlzcGF0Y2ggZXJyb3I6JywgZXJyKTsKICAgICAgICAgICAgICAgIF90aGlzMTAuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOwogICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAvLyBhZGRUYXNrQW5kTWF0ZXJpYWwodGhpcy5kaXNwYXRjaEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIC8vICAgY29uc29sZS5sb2coImFkZFRhc2tBbmRNYXRlcmlhbCIsIHJlc3BvbnNlKTsKICAgICAgICAgICAgICAvLyAgIGxldCBzbm93SWQgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICAgIC8vICAgdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgICAvLyAgICAgaXRlbS50YXNrTm8gPSBzbm93SWQ7CiAgICAgICAgICAgICAgLy8gICAgIGFkZFRhc2tNYXRlcmlhbChpdGVtKTsKICAgICAgICAgICAgICAvLyAgIH0pOwoKICAgICAgICAgICAgICAvLyAgIGNvbnNvbGUubG9nKCLnlJ/miJDmtL7ovabml6Xlv5ciKTsKCiAgICAgICAgICAgICAgLy8gICAvL+eUn+aIkOa0vui9puaXpeW/lwogICAgICAgICAgICAgIC8vICAgbGV0IGxlYXZlVGFza0xvZyA9IHt9OwoKICAgICAgICAgICAgICAvLyAgIGxlYXZlVGFza0xvZy5sb2dUeXBlID0gMjsKICAgICAgICAgICAgICAvLyAgIGxlYXZlVGFza0xvZy50YXNrTm8gPSBzbm93SWQ7CiAgICAgICAgICAgICAgLy8gICBsZWF2ZVRhc2tMb2cuYXBwbHlObyA9IHRoaXMuYXBwbHlObzsKICAgICAgICAgICAgICAvLyAgIGxlYXZlVGFza0xvZy5pbmZvID0gJ+a0vui9puS7u+WKoeWIm+W7uu+8micgKyB0aGlzLmRpc3BhdGNoRm9ybS5jYXJOdW0gKyAnICcgKyB0aGlzLmRpc3BhdGNoRm9ybS5kcml2ZXJOYW1lCiAgICAgICAgICAgICAgLy8gICBhZGRMZWF2ZUxvZyhsZWF2ZVRhc2tMb2cpOwoKICAgICAgICAgICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5rS+6L2m5oiQ5YqfJyk7CiAgICAgICAgICAgICAgLy8gICB0aGlzLmRpc3BhdGNoRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgIC8vICAgdGhpcy5nZXRMaXN0VGFza0luZm8oKTsKICAgICAgICAgICAgICAvLyB9KTsKCiAgICAgICAgICAgICAgX3RoaXMxMC5kaXNwYXRjaERpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgfQogICAgICAgICAgICBjb25zb2xlLmxvZygidGhpcy5pc0FsbG93RGlzcGF0Y2giLCByZXNwb25zZSk7CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyKSB7CiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ2Rpc3BhdGNoIGVycm9yOicsIGVycik7CiAgICAgICAgICAgIF90aGlzMTAuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOwogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOagvOW8j+WMluaXpeacn+aXtumXtAogICAgZm9ybWF0RGF0ZVRpbWU6IGZ1bmN0aW9uIGZvcm1hdERhdGVUaW1lKGRhdGUpIHsKICAgICAgdmFyIHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCk7CiAgICAgIHZhciBtb250aCA9IChkYXRlLmdldE1vbnRoKCkgKyAxKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIHZhciBkYXkgPSBkYXRlLmdldERhdGUoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIHZhciBob3VycyA9IGRhdGUuZ2V0SG91cnMoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIHZhciBtaW51dGVzID0gZGF0ZS5nZXRNaW51dGVzKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpOwogICAgICB2YXIgc2Vjb25kcyA9IGRhdGUuZ2V0U2Vjb25kcygpLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgcmV0dXJuICIiLmNvbmNhdCh5ZWFyLCAiLSIpLmNvbmNhdChtb250aCwgIi0iKS5jb25jYXQoZGF5LCAiICIpLmNvbmNhdChob3VycywgIjoiKS5jb25jYXQobWludXRlcywgIjoiKS5jb25jYXQoc2Vjb25kcyk7CiAgICB9LAogICAgLy8g5omT5Y2w5Yqf6IO9CiAgICBoYW5kbGVQcmludDogZnVuY3Rpb24gaGFuZGxlUHJpbnQoKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5omT5Y2w5Yqf6IO95bCa5pyq5a6e546wJyk7CiAgICAgIC8vIOWunumZhemhueebruS4reWPr+S7peiwg+eUqOa1j+iniOWZqOaJk+WNsOWKn+iDvQogICAgICAvLyB3aW5kb3cucHJpbnQoKTsKICAgIH0sCiAgICAvLyDov5Tlm57mjInpkq4KICAgIGNhbmNlbDogZnVuY3Rpb24gY2FuY2VsKCkgewogICAgICB0aGlzLiR0YWIuY2xvc2VPcGVuUGFnZSh0aGlzLiRyb3V0ZSk7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAiL2xlYXZlL2xlYXZlUGxhbkxpc3QiLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICB0OiBEYXRlLm5vdygpCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDot7PovazliLDku7vliqHor6bmg4XpobXpnaIKICAgIGdvVG9UYXNrRGV0YWlsOiBmdW5jdGlvbiBnb1RvVGFza0RldGFpbChyb3cpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIHBhdGg6ICIvbGVhdmUvcGxhbi90YXNrLyIuY29uY2F0KHJvdy50YXNrTm8pCiAgICAgIH0pOwogICAgfSwKICAgIGdldFRhc2tUeXBlVGV4dDogZnVuY3Rpb24gZ2V0VGFza1R5cGVUZXh0KHRhc2tUeXBlKSB7CiAgICAgIHZhciBzdGFuZGFyZE1hcCA9IHsKICAgICAgICAxOiAn5Ye65Y6CJywKICAgICAgICAyOiAn6L+U5Y6CJywKICAgICAgICAzOiAn6Leo5Yy66LCD5ouoJwogICAgICB9OwogICAgICByZXR1cm4gc3RhbmRhcmRNYXBbdGFza1R5cGVdIHx8ICfmnKrnn6UnOwogICAgfSwKICAgIGdldFN0YXR1c1RleHQ6IGZ1bmN0aW9uIGdldFN0YXR1c1RleHQoc3RhbmRhcmQpIHsKICAgICAgdmFyIHN0YW5kYXJkTWFwID0gewogICAgICAgIDE6ICflvoXov4fnmq7ph40nLAogICAgICAgIDI6ICflvoXoo4XotKcnLAogICAgICAgIDM6ICflvoXov4fmr5vph40nLAogICAgICAgIDQ6ICflvoXlh7rljoInLAogICAgICAgIDU6ICflvoXov5TljoInLAogICAgICAgIDY6ICflvoXov4fmr5vph40o5aSN56OFKScsCiAgICAgICAgNzogJ+W+heWNuOi0pycsCiAgICAgICAgODogJ+W+hei/h+earumHjSjlpI3no4UpJywKICAgICAgICA5OiAn5a6M5oiQJwogICAgICB9OwogICAgICByZXR1cm4gc3RhbmRhcmRNYXBbc3RhbmRhcmRdIHx8ICfmnKrnn6UnOwogICAgfSwKICAgIC8vIOiOt+WPluiuoeWIkueKtuaAgeexu+WeiwogICAgZ2V0UGxhblN0YXR1c1R5cGU6IGZ1bmN0aW9uIGdldFBsYW5TdGF0dXNUeXBlKHN0YXR1cykgewogICAgICB2YXIgc3RhdHVzTWFwID0gewogICAgICAgICcxJzogJ3dhcm5pbmcnLAogICAgICAgIC8vIOW+heWIhuWOguWuoeaJuQogICAgICAgICcyJzogJ3dhcm5pbmcnLAogICAgICAgIC8vIOW+heWIhuWOguWkjeWuoQogICAgICAgICczJzogJ3dhcm5pbmcnLAogICAgICAgIC8vIOW+heeUn+S6p+aMh+aMpeS4reW/g+WuoeaJuQogICAgICAgICc0JzogJ3N1Y2Nlc3MnLAogICAgICAgIC8vIOWuoeaJueWujOaIkAogICAgICAgICc1JzogJ3ByaW1hcnknLAogICAgICAgIC8vIOW3suWHuuWOggogICAgICAgICc2JzogJ2luZm8nLAogICAgICAgIC8vIOmDqOWIhuaUtui0pwogICAgICAgICc3JzogJ3N1Y2Nlc3MnLAogICAgICAgIC8vIOW3suWujOaIkAogICAgICAgICcxMSc6ICdkYW5nZXInLAogICAgICAgIC8vIOmps+WbngogICAgICAgICcxMic6ICdkYW5nZXInLAogICAgICAgIC8vIOW6n+W8gwogICAgICAgICcxMyc6ICdkYW5nZXInIC8vIOi/h+acnwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ2luZm8nOwogICAgfSwKICAgIC8qKg0KICAgICAqIOiOt+WPluiuoeWIkuS4i+aJgOacieS7u+WKoeeahOS7u+WKoeeJqei1hA0KICAgICAqIEByZXR1cm5zIHtQcm9taXNlPHZvaWQ+fQ0KICAgICAqLwogICAgZ2V0QWxsVGFza01hdGVyaWFsczogZnVuY3Rpb24gZ2V0QWxsVGFza01hdGVyaWFscygpIHsKICAgICAgdmFyIF90aGlzMTEgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIHBhcmFtcywgcmVzcG9uc2UsIF90OwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dC5wID0gMDsKICAgICAgICAgICAgICAvLyDmuIXnqbrnjrDmnInmlbDmja4KICAgICAgICAgICAgICBfdGhpczExLnRhc2tNYXRlcmlhbE1hcC5jbGVhcigpOwoKICAgICAgICAgICAgICAvLyDojrflj5bor6XorqHliJLkuIvmiYDmnInku7vliqHnmoTku7vliqHnianotYQKICAgICAgICAgICAgICBwYXJhbXMgPSB7CiAgICAgICAgICAgICAgICBhcHBseU5vOiBfdGhpczExLmFwcGx5Tm8KICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSAxOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX3BsYW4ubGlzdFRhc2tNYXRlcmlhbCkocGFyYW1zKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQudjsKICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLnJvd3MpIHsKICAgICAgICAgICAgICAgIC8vIOWwhuS7u+WKoeeJqei1hOaMieeJqei1hElE5YiG57uE5a2Y5YKoCiAgICAgICAgICAgICAgICByZXNwb25zZS5yb3dzLmZvckVhY2goZnVuY3Rpb24gKG1hdGVyaWFsKSB7CiAgICAgICAgICAgICAgICAgIHZhciBrZXkgPSBtYXRlcmlhbC5tYXRlcmlhbElkOwogICAgICAgICAgICAgICAgICBpZiAoIV90aGlzMTEudGFza01hdGVyaWFsTWFwLmhhcyhrZXkpKSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMxMS50YXNrTWF0ZXJpYWxNYXAuc2V0KGtleSwgewogICAgICAgICAgICAgICAgICAgICAgbWF0ZXJpYWxJZDogbWF0ZXJpYWwubWF0ZXJpYWxJZCwKICAgICAgICAgICAgICAgICAgICAgIG1hdGVyaWFsTmFtZTogbWF0ZXJpYWwubWF0ZXJpYWxOYW1lLAogICAgICAgICAgICAgICAgICAgICAgbWF0ZXJpYWxTcGVjOiBtYXRlcmlhbC5tYXRlcmlhbFNwZWMsCiAgICAgICAgICAgICAgICAgICAgICBwbGFuTnVtOiBtYXRlcmlhbC5wbGFuTnVtLAogICAgICAgICAgICAgICAgICAgICAgdXNlZE51bTogMCwKICAgICAgICAgICAgICAgICAgICAgIHRhc2tNYXRlcmlhbHM6IFtdIC8vIOWtmOWCqOavj+S4quS7u+WKoeeahOWFt+S9k+eJqei1hOS/oeaBrwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIHZhciBtYXRlcmlhbEluZm8gPSBfdGhpczExLnRhc2tNYXRlcmlhbE1hcC5nZXQoa2V5KTsKICAgICAgICAgICAgICAgICAgLy8g57Sv5Yqg5q+P5Liq5Lu75Yqh54mp6LWE55qE6K6h5YiS5pWw6YeP5L2c5Li65bey5L2/55So5pWw6YePCiAgICAgICAgICAgICAgICAgIG1hdGVyaWFsSW5mby51c2VkTnVtICs9IG1hdGVyaWFsLnBsYW5OdW07CiAgICAgICAgICAgICAgICAgIG1hdGVyaWFsSW5mby50YXNrTWF0ZXJpYWxzLnB1c2goewogICAgICAgICAgICAgICAgICAgIHRhc2tObzogbWF0ZXJpYWwudGFza05vLAogICAgICAgICAgICAgICAgICAgIGNhck51bTogbWF0ZXJpYWwuY2FyTnVtLAogICAgICAgICAgICAgICAgICAgIHBsYW5OdW06IG1hdGVyaWFsLnBsYW5OdW0sCiAgICAgICAgICAgICAgICAgICAgY3JlYXRlVGltZTogbWF0ZXJpYWwuY3JlYXRlVGltZQogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgLy8g5pu05paw54mp6LWE6YCJ5oup5YiX6KGo5Lit55qE5bey5L2/55So5pWw6YePCiAgICAgICAgICAgICAgX3RoaXMxMS51cGRhdGVNYXRlcmlhbFVzZWROdW0oKTsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVGFzayBNYXRlcmlhbCBNYXA6JywgX3RoaXMxMS50YXNrTWF0ZXJpYWxNYXApOwogICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSAzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgX2NvbnRleHQucCA9IDI7CiAgICAgICAgICAgICAgX3QgPSBfY29udGV4dC52OwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluS7u+WKoeeJqei1hOWksei0pTonLCBfdCk7CiAgICAgICAgICAgICAgX3RoaXMxMS4kbWVzc2FnZS5lcnJvcign6I635Y+W5Lu75Yqh54mp6LWE5aSx6LSlJyk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzAsIDJdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKg0KICAgICAqIOabtOaWsOeJqei1hOmAieaLqeWIl+ihqOS4reeahOW3suS9v+eUqOaVsOmHjw0KICAgICAqLwogICAgdXBkYXRlTWF0ZXJpYWxVc2VkTnVtOiBmdW5jdGlvbiB1cGRhdGVNYXRlcmlhbFVzZWROdW0oKSB7CiAgICAgIHZhciBfdGhpczEyID0gdGhpczsKICAgICAgdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QuZm9yRWFjaChmdW5jdGlvbiAocm93KSB7CiAgICAgICAgaWYgKHJvdy5tYXRlcmlhbElkKSB7CiAgICAgICAgICB2YXIgbWF0ZXJpYWxJbmZvID0gX3RoaXMxMi50YXNrTWF0ZXJpYWxNYXAuZ2V0KHJvdy5tYXRlcmlhbElkKTsKICAgICAgICAgIGlmIChtYXRlcmlhbEluZm8pIHsKICAgICAgICAgICAgLy8g55u05o6l5L2/55So57Sv5Yqg55qE6K6h5YiS5pWw6YeP5L2c5Li65bey5L2/55So5pWw6YePCiAgICAgICAgICAgIHJvdy51c2VkTnVtID0gbWF0ZXJpYWxJbmZvLnVzZWROdW07CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDnianotYTnoa7orqTmjInpkq7ngrnlh7vkuovku7YKICAgIGhhbmRsZU1hdGVyaWFsQ29uZmlybTogZnVuY3Rpb24gaGFuZGxlTWF0ZXJpYWxDb25maXJtKCkgewogICAgICB2YXIgX3RoaXMxMyA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLm0oZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIHVuZmluaXNoZWRUYXNrcywgX3QyOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQyLm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wID0gMDsKICAgICAgICAgICAgICBpZiAoIShfdGhpczEzLnRhc2tMaXN0SW5mbyAmJiBfdGhpczEzLnRhc2tMaXN0SW5mby5sZW5ndGggPiAwKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQyLm4gPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHVuZmluaXNoZWRUYXNrcyA9IF90aGlzMTMudGFza0xpc3RJbmZvLmZpbHRlcihmdW5jdGlvbiAodGFzaykgewogICAgICAgICAgICAgICAgcmV0dXJuIHRhc2sudGFza1N0YXR1cyAhPT0gOTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBpZiAoISh1bmZpbmlzaGVkVGFza3MubGVuZ3RoID4gMCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5uID0gMTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczEzLiRtZXNzYWdlLmVycm9yKCflrZjlnKjmnKrlrozmiJDnmoTku7vliqHvvIzml6Dms5Xov5vooYznianotYTnoa7orqQnKTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLmEoMik7CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgICBfY29udGV4dDIubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfcGxhbi5jb25maXJtTWF0ZXJpYWwpKHsKICAgICAgICAgICAgICAgIGFwcGx5Tm86IF90aGlzMTMuYXBwbHlObwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgX3RoaXMxMy4kbWVzc2FnZS5zdWNjZXNzKCfnianotYTnoa7orqTmiJDlip8nKTsKICAgICAgICAgICAgICAvLyDliLfmlrDor6bmg4UKICAgICAgICAgICAgICBfdGhpczEzLmdldExpc3RUYXNrSW5mbygpOwogICAgICAgICAgICAgIC8vIOmHjeaWsOiOt+WPlnBsYW5JbmZvCiAgICAgICAgICAgICAgKDAsIF9wbGFuLmRldGFpbFBsYW4pKF90aGlzMTMuYXBwbHlObykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICAgIF90aGlzMTMucGxhbkluZm8gPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uID0gNDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wID0gMzsKICAgICAgICAgICAgICBfdDIgPSBfY29udGV4dDIudjsKICAgICAgICAgICAgICBfdGhpczEzLiRtZXNzYWdlLmVycm9yKCfnianotYTnoa7orqTlpLHotKUnKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMiwgbnVsbCwgW1swLCAzXV0pOwogICAgICB9KSkoKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_plan", "require", "_task", "_driver", "_sortablejs", "name", "data", "validateCarNumber", "rule", "value", "callback", "pattern", "test", "Error", "validatePhone", "validateIdCard", "isTaskTypeEdit", "vehicleEmissionStandardsOptions", "taskTypeOptions", "carList", "searchCarQuery", "filteredCarOptions", "driverList", "searchDriverQuery", "filteredDriverOptions", "approveForm", "applyNo", "approve<PERSON>ontent", "approveFlag", "imageList", "fileList", "dispatchDialogVisible", "taskListInfo", "dispatchForm", "dispatchRules", "carNumber", "required", "message", "trigger", "validator", "<PERSON><PERSON><PERSON>", "min", "max", "driverPhone", "driverIdCard", "dispatchList", "id", "dispatchTime", "status", "tareWeight", "grossWeight", "recheckedGrossWeight", "recheckedTareWeight", "planInfo", "taskQueryParams", "taskMaterialList", "materialSelectionList", "materialId", "materialName", "materialSpec", "planNum", "usedNum", "remainingNum", "currentNum", "availableMaterials", "taskMaterialListMap", "Map", "taskMaterialMap", "computed", "canDispatchCar", "displayDriverListOptions", "slice", "displayCarListOptions", "canShowMaterialConfirm", "includes", "planStatus", "activated", "_this", "getDicts", "then", "response", "planType", "splice", "$route", "params", "detailPlan", "taskTypeEditUpdate", "console", "log", "parseImageAndFileData", "getListTaskInfo", "getDriverList", "getCarList", "methods", "handleApprove", "_this2", "approve", "$message", "success", "$router", "push", "path", "query", "t", "Date", "now", "refresh", "catch", "error", "handleReject", "_this3", "handleDiscard", "_this4", "discard", "window", "history", "length", "go", "_this5", "listTask", "rows", "getAllTaskMaterials", "openNewDriverWindow", "newWindowUrl", "open", "openNewCarWindow", "vehicleEmissionStandardsFormat", "row", "column", "selectDictLabel", "vehicleEmissionStandards", "taskTypeFormat", "getTaskTypeText", "taskType", "taskStatusFormat", "getStatusText", "taskStatus", "_this6", "loading", "getXctgDriverCarListByPage", "filterCarData", "filter", "item", "handleDriverChange", "_this7", "driverId", "for<PERSON>ach", "idCard", "company", "phone", "photo", "faceImgList", "driverLicenseImgs", "vehicleLicenseImgs", "sex", "gender", "handleCarChange", "_this8", "<PERSON><PERSON><PERSON><PERSON>", "licensePlateColor", "carId", "trailerNumber", "trailerId", "axisType", "driverWeight", "maxWeight", "engineNumber", "vinNumber", "_this9", "getXctgDriverUserListByPage", "filterDriverData", "driverInfo", "applyImgUrl", "JSON", "parse", "e", "applyFileUrl", "downloadFile", "url", "fileName", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "getPlanTypeText", "type", "typeMap", "getBusinessCategoryText", "category", "categoryMap", "getMaterialTypeText", "getPlanStatusText", "statusMap", "getLogColor", "logTypeColorMap", "logType", "getDispatchStatusText", "getDispatchStatusType", "getPlanTypeTagType", "getMaterialTypeTagType", "getBusinessCategoryTagType", "openDispatchDialog", "materials", "getTaskMaterialListAndInitSelection", "measureFlag", "hasType1Task", "some", "task", "warning", "roles", "$store", "getters", "businessCategory", "_this0", "clear", "type2List", "map", "mat", "taskNo", "listTaskMaterial", "taskMaterials", "material", "has", "set", "taskMaterialInfo", "existingMaterial", "get", "Array", "from", "_ref", "_ref2", "_slicedToArray2", "default", "key", "_objectSpread2", "_this0$taskMaterialLi", "Math", "resetDispatchForm", "$refs", "resetFields", "getTaskMaterialList", "_this1", "_ref3", "_ref4", "addMaterialRow", "removeMaterial", "index", "handleMaterialChange", "selectedMaterial", "find", "selectPlanMaterial", "getMaxAvailableNum", "materialInfo", "isMaterialAvailable", "submitDispatchForm", "_this10", "validate", "valid", "resultList", "selRow", "planMaterial", "newItem", "hasInvalidMaterial", "isDirectSupply", "planNo", "carNum", "companyName", "driverLicenseImg", "mobilePhone", "faceImg", "drivingLicenseImg", "idCardNo", "dispatchInfo", "isAllowDispatch", "param", "leaveTask", "leaveTaskMaterialList", "addTaskAndMaterialAndAddLeaveLog", "res", "code", "err", "formatDateTime", "date", "year", "getFullYear", "month", "getMonth", "toString", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "handlePrint", "cancel", "$tab", "closeOpenPage", "goToTaskDetail", "standardMap", "standard", "getPlanStatusType", "_this11", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "v", "createTime", "updateMaterialUsedNum", "a", "_this12", "handleMaterialConfirm", "_this13", "_callee2", "unfinishedTasks", "_t2", "_context2", "confirmMaterial"], "sources": ["src/views/leave/plan/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <h3>申请详情</h3>\r\n      </div>\r\n\r\n      <!-- 基本信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">基本信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"申请编号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 申请编号</template>\r\n            {{ planInfo.applyNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划号</template>\r\n            {{ planInfo.planNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划状态\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-flag\"></i> 计划状态</template>\r\n            <el-tag :type=\"getPlanStatusType(planInfo.planStatus)\">{{ getPlanStatusText(planInfo.planStatus) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"计划类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-order\"></i> 计划类型</template>\r\n            <el-tag :type=\"getPlanTypeTagType(planInfo.planType)\">{{ getPlanTypeText(planInfo.planType) }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"业务类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-management\"></i> 业务类型</template>\r\n            <el-tag :type=\"getBusinessCategoryTagType(planInfo.businessCategory)\">{{\r\n              getBusinessCategoryText(planInfo.businessCategory) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"是否计量\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-operation\"></i> 是否计量</template>\r\n            <el-tag :type=\"planInfo.measureFlag === 1 ? 'success' : 'danger'\">\r\n              {{ planInfo.measureFlag === 1 ? '计量' : '不计量' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.plannedAmount\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划量（吨）</template>\r\n            {{ planInfo.plannedAmount }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"是否复审\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-check\"></i> 是否复审</template>\r\n            <el-tag :type=\"planInfo.secApproveFlag === 1 ? 'warning' : 'info'\">\r\n              {{ planInfo.secApproveFlag === 1 ? '是' : '否' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请单位\" v-if=\"planInfo.sourceCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 申请单位</template>\r\n            {{ planInfo.sourceCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"收货单位\" v-if=\"planInfo.receiveCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-school\"></i> 收货单位</template>\r\n            {{ planInfo.receiveCompany }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"返回单位\" v-if=\"planInfo.targetCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-home\"></i> 返回单位</template>\r\n            {{ planInfo.targetCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划返回时间\" v-if=\"planInfo.planReturnTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-time\"></i> 计划返回时间</template>\r\n            {{ planInfo.planReturnTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"退货单位\" v-if=\"planInfo.refundDepartment\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-shop\"></i> 退货单位</template>\r\n            {{ planInfo.refundDepartment }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"开始时间\" v-if=\"planInfo.startTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 开始时间</template>\r\n            {{ planInfo.startTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"结束时间\" v-if=\"planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 结束时间</template>\r\n            {{ planInfo.endTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"有效期\" v-if=\"!planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 有效期</template>\r\n            {{ planInfo.expireTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"监装人\" v-if=\"planInfo.monitor\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 监装人</template>\r\n            {{ planInfo.monitor }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"物资专管员\" v-if=\"planInfo.specialManager\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-custom\"></i> 物资专管员</template>\r\n            {{ planInfo.specialManager }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"物资类型\" v-if=\"planInfo.itemType\">\r\n            <template slot=\"label\"><i class=\"el-icon-goods\"></i> 物资类型</template>\r\n            <el-tag :type=\"getMaterialTypeTagType(planInfo.itemType)\">\r\n              {{ getMaterialTypeText(planInfo.itemType) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"出厂原因\" v-if=\"planInfo.reason\">\r\n            <template slot=\"label\"><i class=\"el-icon-info\"></i> 出厂原因</template>\r\n            {{ planInfo.reason }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"合同号\" v-if=\"planInfo.contractNo\">\r\n            <template slot=\"label\"><i class=\"el-icon-tickets\"></i> 合同号</template>\r\n            {{ planInfo.contractNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"申请时间\" v-if=\"planInfo.applyTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-timer\"></i> 申请时间</template>\r\n            {{ planInfo.applyTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请人\" v-if=\"planInfo.applyUserName\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 申请人</template>\r\n            {{ planInfo.applyUserName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.planned_amount\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 计划量(吨)</template>\r\n            {{ planInfo.planned_amount }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 新增图片列表部分 -->\r\n      <div class=\"section-container\" v-if=\"imageList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-picture-outline\"></i> 申请图片</span>\r\n        </div>\r\n        <div class=\"image-container\">\r\n          <viewer :images=\"imageList\">\r\n            <div class=\"image-list\">\r\n              <div class=\"image-item\" v-for=\"(image, index) in imageList\" :key=\"'img-' + index\">\r\n                <img :src=\"image.url\" :alt=\"image.name\">\r\n                <div class=\"image-name\">{{ image.name }}</div>\r\n              </div>\r\n            </div>\r\n          </viewer>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 新增文件列表部分 -->\r\n      <div class=\"section-container\" v-if=\"fileList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-document\"></i> 申请附件</span>\r\n        </div>\r\n        <div class=\"file-container\">\r\n          <div class=\"file-list\">\r\n            <div class=\"file-item\" v-for=\"(file, index) in fileList\" :key=\"'file-' + index\"\r\n              @click=\"downloadFile(file.url, file.name)\">\r\n              <i class=\"el-icon-document file-icon\"></i>\r\n              <div class=\"file-name\">{{ file.name }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"planInfo.materials\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\" width=\"150\">\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 派车信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">\r\n          <span>派车信息</span>\r\n          <el-button type=\"primary\" size=\"small\" icon=\"el-icon-truck\" @click=\"openDispatchDialog\"\r\n            :disabled=\"!canDispatchCar\" class=\"dispatch-btn\">\r\n            派车\r\n          </el-button>\r\n        </div>\r\n\r\n        <el-table v-if=\"taskListInfo.length > 0\" :data=\"taskListInfo\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"carNum\" label=\"车牌号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"driverName\" label=\"司机姓名\" width=\"100\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"mobilePhone\" label=\"司机手机号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\" :formatter=\"taskTypeFormat\">\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0 \">\r\n          </el-table-column>\r\n          <el-table-column prop=\"factoryReceiveNum\" label=\"分厂确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"计量单位\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.tare || '-' }} {{ scope.row.tare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.gross || '-' }} {{ scope.row.gross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secTare || '-' }} {{ scope.row.secTare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secGross || '-' }} {{ scope.row.secGross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n\r\n\r\n          <el-table-column prop=\"createTime\" label=\"派车时间\" width=\"160\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskStatus\" label=\"任务状态\" width=\"120\" :formatter=\"taskStatusFormat\">\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"goToTaskDetail(scope.row)\">\r\n                任务详情\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <div v-else class=\"empty-data\">\r\n          <el-empty description=\"暂无派车记录\"></el-empty>\r\n        </div>\r\n\r\n        <!-- 物资确认按钮 -->\r\n        <div style=\"text-align: right; margin-top: 15px;\">\r\n          <el-button type=\"primary\" icon=\"el-icon-finished\" @click=\"handleMaterialConfirm\">\r\n            物资确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <!-- v-if=\"canShowMaterialConfirm\" -->\r\n\r\n      <!-- 审核内容部分 -->\r\n      <div class=\"section-container\" v-if=\"planInfo.approveButtonShow\">\r\n        <div class=\"section-title\">审核内容</div>\r\n        <el-form label-width=\"80px\" :model=\"approveForm\" ref=\"approveForm\">\r\n          <el-form-item label=\"审核建议\">\r\n            <el-input type=\"textarea\" v-model=\"approveForm.approveContent\" :rows=\"4\" placeholder=\"请输入审核建议\"\r\n              maxlength=\"200\" show-word-limit></el-input>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px; margin-bottom: 10px;\">审核建议可不填，默认通过为同意，驳回为拒绝\r\n            </div>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">日志列表</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in planInfo.leaveLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <!-- 固定底部操作栏 -->\r\n      <div class=\"fixed-bottom-action\">\r\n        <el-row :gutter=\"10\" type=\"flex\" justify=\"center\" align=\"middle\">\r\n          <!-- 返回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\">\r\n            <el-button size=\"medium\" @click=\"cancel\">返回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 通过按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.approveButtonShow\">\r\n            <el-button size=\"medium\" type=\"primary\" icon=\"el-icon-check\" @click=\"handleApprove\">通过</el-button>\r\n          </el-col>\r\n\r\n          <!-- 驳回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.rejectButtonShow\">\r\n            <el-button size=\"medium\" type=\"danger\" icon=\"el-icon-close\" @click=\"handleReject\">驳回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 废弃按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.discardButtonShow\">\r\n            <el-button size=\"medium\" type=\"success\" icon=\"el-icon-delete\" @click=\"handleDiscard\">废弃</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n    </el-card>\r\n\r\n    <!-- 派车弹框 -->\r\n    <el-dialog title=\"派车\" :visible.sync=\"dispatchDialogVisible\" width=\"1200px\" append-to-body destroy-on-close\r\n      @closed=\"resetDispatchForm\">\r\n      <el-form ref=\"dispatchForm\" :model=\"dispatchForm\" :rules=\"dispatchRules\" label-width=\"100px\"\r\n        class=\"dispatch-form\">\r\n\r\n        <el-form-item label=\"货车司机\" prop=\"driverId\" :rules=\"[{ required: true, message: '司机信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.driverId\" filterable :filter-method=\"filterDriverData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleDriverChange\">\r\n            <el-option v-for=\"item in filteredDriverOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.driverInfo\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewDriverWindow\">前往新增或修改司机信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"司机名称\" prop=\"name\" v-if=\"dispatchForm.name != null\">\r\n          <el-input v-model=\"dispatchForm.name\" placeholder=\"请输入司机名称\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\" v-if=\"dispatchForm.phone != null\">\r\n          <el-input v-model=\"dispatchForm.phone\" placeholder=\"请输入手机号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idCard\" v-if=\"dispatchForm.idCard != null\">\r\n          <el-input v-model=\"dispatchForm.idCard\" placeholder=\"请输入身份证号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"人脸照片\" prop=\"faceImg\" v-if=\"dispatchForm.photo != null && dispatchForm.photo != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.faceImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n                      lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.photo\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"驾驶证照片\" prop=\"driverLicenseImgs\"\r\n          v-if=\"dispatchForm.driverLicenseImgs != null && dispatchForm.driverLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.drivingLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.driverLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"行驶证照片\" prop=\"vehicleLicenseImgs\"\r\n          v-if=\"dispatchForm.vehicleLicenseImgs != null && dispatchForm.vehicleLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.driverLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.vehicleLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"货车\" prop=\"carUUId\" :rules=\"[{ required: true, message: '货车信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.carUUId\" filterable :filter-method=\"filterCarData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleCarChange\">\r\n            <el-option v-for=\"item in filteredCarOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.carNumber\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewCarWindow\">前往新增或修改货车信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车牌号\" prop=\"carNumber\" v-if=\"dispatchForm.carNumber != null\">\r\n          <el-input v-model=\"dispatchForm.carNumber\" placeholder=\"请输入车牌号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"车辆排放标准\" prop=\"vehicleEmissionStandards\"\r\n          v-if=\"dispatchForm.vehicleEmissionStandards != null\">\r\n          <el-select v-model=\"dispatchForm.vehicleEmissionStandards\" placeholder=\"请选择车辆排放标准\" disabled\r\n            style=\"width:300px\">\r\n            <el-option v-for=\"dict in vehicleEmissionStandardsOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"任务类型\" prop=\"taskType\" :rules=\"[{ required: true, message: '任务类型不能为空' }]\"\r\n          v-if=\"isTaskTypeEdit == true\">\r\n          <el-select v-model=\"dispatchForm.taskType\" placeholder=\"请选择车任务类型\" style=\"width:300px\">\r\n            <el-option v-for=\"dict in taskTypeOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 新增物资选择表格 -->\r\n        <el-form-item label=\"物资选择\" prop=\"selectedMaterials\"\r\n          v-if=\"planInfo.measureFlag == 0 && dispatchForm.taskType == 2\">\r\n          <el-table :data=\"materialSelectionList\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" width=\"50\" label=\"序号\"></el-table-column>\r\n            <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-select v-model=\"scope.row.materialId\" placeholder=\"请选择物资\"\r\n                  @change=\"handleMaterialChange(scope.row, scope.$index)\">\r\n                  <el-option v-for=\"item in availableMaterials\" :key=\"item.materialId\" :label=\"item.materialName\"\r\n                    :value=\"item.materialId\" :disabled=\"isMaterialAvailable(item)\">\r\n\r\n                  </el-option>\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"materialSpec\" label=\"物资规格\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.materialSpec }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.planNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remainingNum\" label=\"剩余数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.remainingNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"currentNum\" label=\"本次数量\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number v-model=\"scope.row.currentNum\" :min=\"0\" :max=\"getMaxAvailableNum(scope.row)\"\r\n                  @change=\"handleNumChange($event, scope.$index)\" :disabled=\"!scope.row.materialId\">\r\n                </el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"removeMaterial(scope.$index)\"\r\n                  :disabled=\"materialSelectionList.length === 1\">\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <div style=\"margin-top: 10px;\">\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addMaterialRow\">添加物资</el-button>\r\n          </div>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dispatchDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitDispatchForm\">确 认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { detailPlan, approve, discard, listTaskMaterial, confirmMaterial } from \"@/api/leave/plan\";\r\nimport { listTask, addTask, addTaskMaterial, addTaskAndMaterial, addLeaveLog, isAllowDispatch, addTaskAndMaterialAndAddLeaveLog } from \"@/api/leave/task\";\r\nimport { listAllDriver,  getXctgDriverUserListByPage, getXctgDriverCarListByPage } from \"@/api/dgcb/driver/driver\";\r\nimport { mount } from \"sortablejs\";\r\nexport default {\r\n  name: \"DetailLeavePlan\",\r\n  data() {\r\n    // 验证车牌号\r\n    const validateCarNumber = (rule, value, callback) => {\r\n      const pattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的车牌号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证手机号\r\n    const validatePhone = (rule, value, callback) => {\r\n      const pattern = /^1[3-9]\\d{9}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的手机号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证身份证号\r\n    const validateIdCard = (rule, value, callback) => {\r\n      const pattern = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的身份证号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      isTaskTypeEdit: true,\r\n      vehicleEmissionStandardsOptions: [],\r\n      taskTypeOptions: [],\r\n      carList: [],\r\n      searchCarQuery: '',\r\n      filteredCarOptions: [],\r\n      driverList: [],\r\n      searchDriverQuery: '',\r\n      filteredDriverOptions: [],\r\n      //审核表单\r\n      approveForm: {\r\n        applyNo: null,\r\n        approveContent: '',//审核意见\r\n        approveFlag: true,//审核状态\r\n      },\r\n\r\n      // 图片列表\r\n      imageList: [],\r\n\r\n      // 文件列表\r\n      fileList: [],\r\n\r\n      // 派车弹框可见性\r\n      dispatchDialogVisible: false,\r\n\r\n      taskListInfo: [],\r\n\r\n      // 派车表单数据\r\n      dispatchForm: {\r\n        // carNumber: '',\r\n        // driverName: '',\r\n        // driverPhone: '',\r\n        // driverIdCard: ''\r\n      },\r\n\r\n      // 派车表单验证规则\r\n      dispatchRules: {\r\n        carNumber: [\r\n          { required: true, message: '请输入车牌号', trigger: 'blur' },\r\n          { validator: validateCarNumber, trigger: 'blur' }\r\n        ],\r\n        driverName: [\r\n          { required: true, message: '请输入司机姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }\r\n        ],\r\n        driverPhone: [\r\n          { required: true, message: '请输入司机手机号', trigger: 'blur' },\r\n          { validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        driverIdCard: [\r\n          { required: true, message: '请输入司机身份证号', trigger: 'blur' },\r\n          { validator: validateIdCard, trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 派车列表数据\r\n      dispatchList: [\r\n        {\r\n          id: 1,\r\n          carNumber: '京A12345',\r\n          driverName: '王小明',\r\n          driverPhone: '13800138000',\r\n          driverIdCard: '110101199001010001',\r\n          dispatchTime: '2025-03-18 09:30:00',\r\n          status: 2,\r\n          tareWeight: 8500,\r\n          grossWeight: 15800,\r\n          recheckedGrossWeight: 15750,\r\n          recheckedTareWeight: 8480\r\n        },\r\n        {\r\n          id: 2,\r\n          carNumber: '京B98765',\r\n          driverName: '李大壮',\r\n          driverPhone: '13900139000',\r\n          driverIdCard: '110101199102020002',\r\n          dispatchTime: '2025-03-19 14:15:00',\r\n          status: 1,\r\n          tareWeight: 7800,\r\n          grossWeight: 12600,\r\n          recheckedGrossWeight: null,\r\n          recheckedTareWeight: null\r\n        }\r\n      ],\r\n\r\n      // 计划详情信息\r\n      planInfo: {},\r\n      applyNo: null,\r\n      taskQueryParams: {\r\n        applyNo: null,\r\n      },\r\n\r\n      taskMaterialList: null,\r\n      // 物资选择相关数据\r\n      materialSelectionList: [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        usedNum: 0,\r\n        remainingNum: 0,\r\n        currentNum: 0\r\n      }],\r\n      availableMaterials: [], // 可选的物资列表\r\n      taskMaterialListMap: new Map(), // 已派车的物资列表\r\n      taskMaterialMap: new Map(), // 存储所有任务物资的映射\r\n    };\r\n  },\r\n  computed: {\r\n    // 判断是否可以派车\r\n    canDispatchCar() {\r\n      // 判断申请单是否已通过\r\n      // const isPlanApproved = this.planInfo.planStatus === 2;\r\n\r\n      // // 如果是非计量类型，且已经派过车，则不能再派车\r\n      // if (this.planInfo.measureFlag !== 1 && this.dispatchList.length > 0) {\r\n      //   return false;\r\n      // }\r\n\r\n      return true;\r\n    },\r\n    // 默认显示前50条，若有搜索，则显示搜索后的数据\r\n    displayDriverListOptions() {\r\n      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);\r\n    },\r\n    displayCarListOptions() {\r\n      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);\r\n    },\r\n    canShowMaterialConfirm() {\r\n      // 只有planStatus为5或6时显示（已出厂/部分收货），且不是已完成/废弃/驳回/过期\r\n      return [5, 6].includes(this.planInfo.planStatus);\r\n    }\r\n  },\r\n  activated() {\r\n    this.getDicts(\"xctg_driver_car_emission_standards\").then(response => {\r\n      this.vehicleEmissionStandardsOptions = response.data;\r\n    });\r\n    this.getDicts(\"leave_task_type\").then(response => {\r\n      this.taskTypeOptions = response.data;\r\n      if (this.planInfo.planType !== 3) {\r\n        this.taskTypeOptions.splice(2, 1);\r\n      }\r\n    });\r\n    // 获取路由参数中的ID\r\n    const applyNo = this.$route.params.applyNo;\r\n    this.applyNo = applyNo\r\n    this.taskQueryParams.applyNo = applyNo;\r\n    this.approveForm.applyNo = applyNo;\r\n    if (applyNo) {\r\n      detailPlan(applyNo).then(response => {\r\n        this.planInfo = response.data;\r\n        this.taskTypeEditUpdate();\r\n        console.log(\"this.planInfo\", this.planInfo);\r\n        // 解析图片和文件数据\r\n        this.parseImageAndFileData();\r\n      });\r\n    };\r\n    this.getListTaskInfo();\r\n    this.getDriverList();\r\n    this.getCarList();\r\n\r\n\r\n\r\n  },\r\n\r\n\r\n  methods: {\r\n    handleApprove() {\r\n      this.approveForm.approveFlag = true;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('审核通过');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleReject() {\r\n      this.approveForm.approveFlag = false;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('驳回成功');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleDiscard() {\r\n      discard(this.planInfo).then(response => {\r\n        this.$message.success('废弃成功');\r\n\r\n        if (window.history.length > 1) {\r\n          this.$router.go(-1);\r\n        } else {\r\n          this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('废弃失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    taskTypeEditUpdate() {\r\n      if (this.planInfo.planType !== 2) {\r\n        this.isTaskTypeEdit = false;\r\n      }\r\n    },\r\n    getListTaskInfo() {\r\n      listTask(this.taskQueryParams).then(response => {\r\n        console.log(\"response.data\", response.rows);\r\n        this.taskListInfo = response.rows;\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        // 获取所有任务物资\r\n        this.getAllTaskMaterials();\r\n      });\r\n    },\r\n    openNewDriverWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    openNewCarWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    // 1国五，2国六，3新能源字典翻译\r\n    vehicleEmissionStandardsFormat(row, column) {\r\n      return this.selectDictLabel(this.vehicleEmissionStandardsOptions, row.vehicleEmissionStandards);\r\n    },\r\n\r\n    taskTypeFormat(row, column) {\r\n      return this.getTaskTypeText(row.taskType);\r\n    },\r\n    taskStatusFormat(row, column) {\r\n      return this.getStatusText(row.taskStatus);\r\n    },\r\n\r\n    /** 查询司机信息列表 */\r\n    getCarList() {\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverCarListByPage().then(response => {\r\n        this.carList = response.rows;\r\n        this.filteredCarOptions = this.carList;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterCarData(query) {\r\n      this.searchCarQuery = query;\r\n\r\n      if (this.searchCarQuery) {\r\n\r\n        this.filteredCarOptions = this.carList.filter(item =>\r\n          item.carNumber.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredCarOptions = this.carList.slice(0, 50);\r\n      }\r\n    },\r\n    //通过driverId获取司机信息\r\n    handleDriverChange() {\r\n      if (this.dispatchForm.driverId != null) {\r\n        this.driverList.forEach(item => {\r\n          if (item.id == this.dispatchForm.driverId) {\r\n            this.dispatchForm.name = item.name;\r\n            this.dispatchForm.idCard = item.idCard;\r\n            this.dispatchForm.company = item.company;\r\n            this.dispatchForm.phone = item.phone;\r\n            this.dispatchForm.photo = item.photo;\r\n            this.dispatchForm.faceImgList = item.faceImgList;\r\n            this.dispatchForm.driverLicenseImgs = item.driverLicenseImgs;\r\n            this.dispatchForm.vehicleLicenseImgs = item.vehicleLicenseImgs;\r\n            this.dispatchForm.sex = item.gender;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n    //通过driverId获取司机信息\r\n    handleCarChange() {\r\n      console.log(\"handleCarChange\")\r\n      if (this.dispatchForm.carUUId != null) {\r\n        this.carList.forEach(item => {\r\n          if (item.id == this.dispatchForm.carUUId) {\r\n            this.dispatchForm.carNumber = item.carNumber;\r\n\r\n            if (item.vehicleEmissionStandards == 1) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"国五\";\r\n            } else if (item.vehicleEmissionStandards == 2) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"国六\";\r\n            } else if (item.vehicleEmissionStandards == 3) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"新能源\";\r\n            } else {\r\n              this.dispatchForm.vehicleEmissionStandards = \"\";\r\n            }\r\n            this.dispatchForm.licensePlateColor = item.licensePlateColor;\r\n            this.dispatchForm.carId = item.carId;\r\n            this.dispatchForm.trailerNumber = item.trailerNumber;\r\n            this.dispatchForm.trailerId = item.trailerId;\r\n            this.dispatchForm.axisType = item.axisType;\r\n            this.dispatchForm.driverWeight = item.driverWeight;\r\n            this.dispatchForm.maxWeight = item.maxWeight;\r\n            this.dispatchForm.engineNumber = item.engineNumber;\r\n            this.dispatchForm.vinNumber = item.vinNumber;\r\n          }\r\n\r\n        });\r\n      }\r\n    },\r\n    /** 查询司机信息列表 */\r\n    getDriverList() {\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverUserListByPage().then(response => {\r\n        this.driverList = response.rows;\r\n        console.log(\"this.driverList\", this.driverList);\r\n        this.filteredDriverOptions = this.driverList;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterDriverData(query) {\r\n      this.searchDriverQuery = query;\r\n\r\n      if (this.searchDriverQuery) {\r\n\r\n        this.filteredDriverOptions = this.driverList.filter(item =>\r\n          item.driverInfo.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredDriverOptions = this.driverList.slice(0, 50);\r\n      }\r\n    },\r\n    // 解析图片和文件数据\r\n    parseImageAndFileData() {\r\n      // 解析图片数据\r\n      if (this.planInfo.applyImgUrl) {\r\n        try {\r\n          this.imageList = JSON.parse(this.planInfo.applyImgUrl);\r\n        } catch (e) {\r\n          console.error('解析图片数据失败:', e);\r\n          this.imageList = [];\r\n        }\r\n      }\r\n\r\n      // 解析文件数据\r\n      if (this.planInfo.applyFileUrl) {\r\n        try {\r\n          this.fileList = JSON.parse(this.planInfo.applyFileUrl);\r\n        } catch (e) {\r\n          console.error('解析文件数据失败:', e);\r\n          this.fileList = [];\r\n        }\r\n      }\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(url, fileName) {\r\n      if (!url) {\r\n        this.$message.error('文件链接无效');\r\n        return;\r\n      }\r\n\r\n      // 创建一个a元素用于下载\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName || '下载文件';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    // 获取计划类型文本\r\n    getPlanTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂不返回',\r\n        2: '出厂返回',\r\n        3: '跨区调拨',\r\n        4: '退货申请'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取业务类型文本\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用',\r\n        11: '通用',\r\n        12: '委外加工',\r\n        21: '有计划量计量',\r\n        22: '短期',\r\n        23: '钢板（圆钢）',\r\n        31: '通用'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n\r\n    // 获取物资类型文本\r\n    getMaterialTypeText(type) {\r\n      const typeMap = {\r\n        1: '钢材',\r\n        2: '钢板',\r\n        3: '其他'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取计划状态文本\r\n    getPlanStatusText(status) {\r\n      const statusMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#67C23A', // 审批\r\n        3: '#E6A23C', // 流转\r\n        4: '#F56C6C', // 驳回\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.logType] || '#409EFF';\r\n    },\r\n\r\n    // 获取派车状态文本\r\n    getDispatchStatusText(status) {\r\n      const statusMap = {\r\n        0: '待出发',\r\n        1: '已出发',\r\n        2: '已到达',\r\n        3: '已完成',\r\n        4: '已取消'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取派车状态类型（用于标签颜色）\r\n    getDispatchStatusType(status) {\r\n      const statusMap = {\r\n        0: 'info',\r\n        1: 'primary',\r\n        2: 'success',\r\n        3: 'success',\r\n        4: 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取计划类型标签样式\r\n    getPlanTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'success',  // 出厂不返回\r\n        2: 'warning',  // 出厂返回\r\n        3: 'info',     // 跨区调拨\r\n        4: 'danger'    // 退货申请\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取物资类型标签样式\r\n    getMaterialTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'primary',  // 钢材\r\n        2: 'success',  // 钢板\r\n        3: 'info'      // 其他\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取业务类型标签样式\r\n    getBusinessCategoryTagType(category) {\r\n      const typeMap = {\r\n        '1': 'primary',   // 通用\r\n        '11': 'primary',  // 通用\r\n        '12': 'warning',  // 委外加工\r\n        '21': 'success',  // 有计划量计量\r\n        '22': 'info',     // 短期\r\n        '23': 'danger',   // 钢板（圆钢）\r\n        '31': 'primary'   // 通用\r\n      };\r\n      return typeMap[category] || 'info';\r\n    },\r\n\r\n    // 打开派车弹框\r\n    openDispatchDialog() {\r\n      // 初始化物资数据\r\n      this.availableMaterials = this.planInfo.materials || [];\r\n      console.log(\"this.availableMaterials\", this.availableMaterials);\r\n\r\n      // 获取已派车的物资列表，并在回调中初始化 materialSelectionList\r\n      this.getTaskMaterialListAndInitSelection();\r\n      // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.supplier') && !roles.includes('leave.applicant')) {\r\n        this.$message.error('您没有派车权限');\r\n        return;\r\n      }\r\n\r\n      console.log(\"this.planInfo.planStatus\", this.planInfo.planStatus);\r\n      if (![4, 5, 6].includes(this.planInfo.planStatus)) {\r\n        this.$message.warning('当前状态无法派车');\r\n        return;\r\n      }\r\n\r\n\r\n\r\n\r\n      console.log(\"openDispatchDialog\", this.taskListInfo.length);\r\n      if (this.planInfo.businessCategory == 22 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('短期计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      if (this.planInfo.businessCategory == 23 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('钢板（圆钢）计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      this.dispatchForm = {};\r\n\r\n      if (this.planInfo.planType == 1) {\r\n        this.dispatchForm.taskType = 1\r\n      } else if (this.planInfo.planType == 3) {\r\n        this.dispatchForm.taskType = 3\r\n      } else if (this.planInfo.planType == 4) {\r\n        this.dispatchForm.taskType = 1\r\n      }\r\n      console.log(this.dispatchForm.taskType),\r\n        this.dispatchDialogVisible = true;\r\n\r\n\r\n    },\r\n\r\n    // 新增方法\r\n    getTaskMaterialListAndInitSelection() {\r\n      // 清空已用数量映射\r\n      this.taskMaterialListMap.clear();\r\n      // 统计所有已派车物资\r\n      const type2List = this.taskListInfo.filter(item => item.taskType === 2);\r\n      console.log(\"type2List\", type2List);\r\n      if (!type2List || type2List.length === 0) {\r\n        // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n        this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n          // const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n          // const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n          return {\r\n            materialId: mat.materialId,\r\n            materialName: mat.materialName,\r\n            materialSpec: mat.materialSpec,\r\n            planNum: mat.planNum,\r\n            usedNum: 0,\r\n            remainingNum: mat.planNum,\r\n            currentNum: mat.planNum\r\n          };\r\n        });\r\n      } else {\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        type2List.forEach(task => {\r\n          const params = { taskNo: task.taskNo };\r\n          listTaskMaterial(params).then(response => {\r\n            let taskMaterials = response.rows || [];\r\n            taskMaterials.forEach(material => {\r\n              if (!this.taskMaterialListMap.has(material.materialId)) {\r\n                this.taskMaterialListMap.set(material.materialId, {\r\n                  taskMaterialInfo: material,\r\n                  usedNum: material.planNum\r\n                });\r\n              } else {\r\n                const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n                existingMaterial.usedNum += material.planNum;\r\n              }\r\n            });\r\n\r\n            // 将taskMaterialListMap转换为数组集合\r\n            this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n              materialId: key,\r\n              ...value\r\n            }));\r\n\r\n            // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n            this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n              const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n              const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n\r\n              return {\r\n                materialId: mat.materialId,\r\n                materialName: mat.materialName,\r\n                materialSpec: mat.materialSpec,\r\n                planNum: mat.planNum,\r\n                usedNum: usedNum,\r\n                remainingNum: remainingNum,\r\n                currentNum: remainingNum\r\n              };\r\n            });\r\n\r\n            this.materialSelectionList = this.materialSelectionList.filter(item => item.remainingNum > 0);\r\n          });\r\n        });\r\n      }\r\n\r\n         // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n    },\r\n\r\n    // 重置派车表单\r\n    resetDispatchForm() {\r\n      this.$refs.dispatchForm && this.$refs.dispatchForm.resetFields();\r\n      this.materialSelectionList = [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      }];\r\n    },\r\n\r\n    // 获取已派车的物资列表\r\n    getTaskMaterialList() {\r\n      // 从taskListInfo中获取已派车的物资信息\r\n      this.taskMaterialListMap.clear();\r\n      this.taskListInfo.forEach(task => {\r\n        const params = {\r\n          taskNo: task.taskNo,\r\n        };\r\n        listTaskMaterial(params).then(response => {\r\n          console.log(\"listTaskMaterial\", response.rows);\r\n          let taskMaterials = [];\r\n          taskMaterials = response.rows;\r\n          taskMaterials.forEach(material => {\r\n            if (!this.taskMaterialListMap.has(material.materialId)) {\r\n              this.taskMaterialListMap.set(material.materialId, {\r\n                taskMaterialInfo: material,\r\n                usedNum: material.planNum\r\n              });\r\n            } else {\r\n              const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n              existingMaterial.usedNum += material.planNum;\r\n            }\r\n          });\r\n          // 将taskMaterialListMap转换为数组集合\r\n          this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n            materialId: key,\r\n            ...value\r\n          }));\r\n          console.log(\"taskMaterialArray\", this.taskMaterialList);\r\n          console.log(\"taskMaterialListMap\", this.taskMaterialListMap);\r\n        });\r\n      });\r\n    },\r\n\r\n    // 添加物资行\r\n    addMaterialRow() {\r\n      this.materialSelectionList.push({\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      });\r\n    },\r\n\r\n    // 移除物资行\r\n    removeMaterial(index) {\r\n      this.materialSelectionList.splice(index, 1);\r\n    },\r\n\r\n    // 处理物资选择变化\r\n    handleMaterialChange(row, index) {\r\n      console.log(\"handleMaterialChange\", this.taskMaterialList);\r\n\r\n\r\n      const selectedMaterial = this.taskMaterialList.find(item => item.materialId === row.materialId);\r\n      if (selectedMaterial) {\r\n        row.usedNum = selectedMaterial.usedNum;\r\n      }\r\n      const selectPlanMaterial = this.planInfo.materials.find(item => item.materialId === row.materialId);\r\n\r\n      if (selectPlanMaterial) {\r\n        row.planNum = selectPlanMaterial.planNum;\r\n        row.materialName = selectPlanMaterial.materialName;\r\n        row.materialSpec = selectPlanMaterial.materialSpec;\r\n      }\r\n\r\n      row.remainingNum = row.planNum - row.usedNum;\r\n      row.currentNum = row.planNum - row.usedNum;\r\n\r\n      console.log(\"handleMaterialChange\", row, index);\r\n\r\n    },\r\n\r\n    // 获取物资最大可用数量\r\n    getMaxAvailableNum(row) {\r\n      if (!row.materialId) return 0;\r\n\r\n      // 从taskMaterialListMap中获取已用数量\r\n      const materialInfo = this.taskMaterialListMap.get(row.materialId);\r\n      const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      return row.planNum - usedNum;\r\n    },\r\n\r\n    // 判断物资是否可选\r\n    isMaterialAvailable(material) {\r\n      // 从taskMaterialListMap中获取已用数量\r\n      // const materialInfo = this.taskMaterialListMap.get(material.id);\r\n      // const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      // let selected = false;\r\n\r\n      // this.availableMaterials.forEach(item => {\r\n      //   if (item.materialId === material.materialId) {\r\n      //     selected = true;\r\n      //   }\r\n      // });\r\n\r\n      return this.materialSelectionList.some(row => row.materialId === material.materialId);;\r\n    },\r\n\r\n    // 修改提交派车表单方法\r\n    submitDispatchForm() {\r\n      this.$refs.dispatchForm.validate(valid => {\r\n        if (valid) {\r\n          // 判断非计量且taskType为1的情况\r\n          if (this.planInfo.measureFlag == 0) {\r\n            if (this.dispatchForm.taskType == 1) {\r\n              // 检查是否已经有taskType为1的任务\r\n              const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n              if (hasType1Task) {\r\n                this.$message.warning('非计量只能派车出厂一次');\r\n                return;\r\n              }\r\n            }\r\n          }\r\n\r\n          // 新集合\r\n          let resultList = [];\r\n\r\n          console.log(\"this.planInfo.measureFlag\", this.planInfo.measureFlag);\r\n          console.log(\"this.dispatchForm.taskType\", this.dispatchForm.taskType);\r\n\r\n          if (this.planInfo.measureFlag == 0 && this.dispatchForm.taskType == 2) {\r\n            this.materialSelectionList.forEach(selRow => {\r\n              // 在 planInfo.materials 中查找相同 materialId 的元素\r\n              const planMaterial = (this.planInfo.materials || []).find(\r\n                mat => mat.materialId === selRow.materialId\r\n              );\r\n              if (planMaterial) {\r\n                // 深拷贝一份，避免影响原数据\r\n                const newItem = { ...planMaterial };\r\n                newItem.planNum = selRow.currentNum; // 设置为本次数量\r\n                resultList.push(newItem);\r\n              }\r\n            });\r\n\r\n            // resultList 即为你需要的新集合\r\n            console.log('this.materialSelectionList', this.materialSelectionList);\r\n            console.log('resultList', resultList);\r\n\r\n            // 物资校验：必须有物资\r\n            if (!this.materialSelectionList.length) {\r\n              this.$message.warning('请至少选择一种物资');\r\n              return;\r\n            }\r\n\r\n            // 校验每一行物资\r\n            const hasInvalidMaterial = this.materialSelectionList.some(row => {\r\n              // 必须选择物资，数量>0，且数量<=剩余数量\r\n              return (\r\n                !row.materialId ||\r\n                row.currentNum <= 0 ||\r\n                row.currentNum > row.remainingNum\r\n              );\r\n            });\r\n\r\n            if (hasInvalidMaterial) {\r\n              this.$message.warning('请选择物资且本次数量需大于0且不超过剩余数量');\r\n              return;\r\n            }\r\n          } else {\r\n            console.log(\"this.planInfo.materials\", this.planInfo.materials);\r\n            resultList = this.planInfo.materials ? this.planInfo.materials.map(item => ({ ...item })) : [];\r\n            console.log(\"123321\", resultList);\r\n          }\r\n\r\n\r\n\r\n\r\n\r\n          if (this.planInfo.measureFlag == 1 && this.dispatchForm.taskType !== 2) {\r\n            this.dispatchForm.taskStatus = 1;\r\n          } else {\r\n            this.dispatchForm.taskStatus = 4;\r\n          }\r\n\r\n          if (this.dispatchForm.taskType == 2) {\r\n            this.dispatchForm.taskStatus = 5;\r\n          }\r\n\r\n\r\n          //是否直供默认为0\r\n          this.dispatchForm.isDirectSupply = 0;\r\n          // todo 任务状态确认\r\n          this.dispatchForm.applyNo = this.applyNo;\r\n          this.dispatchForm.planNo = this.planInfo.planNo;\r\n          this.dispatchForm.carNum = this.dispatchForm.carNumber;\r\n          this.dispatchForm.companyName = this.dispatchForm.company;\r\n          this.dispatchForm.driverLicenseImg = this.dispatchForm.driverLicenseImgs;\r\n          this.dispatchForm.driverName = this.dispatchForm.name;\r\n          this.dispatchForm.mobilePhone = this.dispatchForm.phone;\r\n          this.dispatchForm.faceImg = this.dispatchForm.photo;\r\n          this.dispatchForm.drivingLicenseImg = this.dispatchForm.vehicleLicenseImgs;\r\n          this.dispatchForm.idCardNo = this.dispatchForm.idCard;\r\n          if (this.dispatchForm.sex == \"1\") {\r\n            this.dispatchForm.sex = 1;\r\n          } else if (this.dispatchForm.sex == \"2\") {\r\n            this.dispatchForm.sex = 2;\r\n          }\r\n          if (this.dispatchForm.vehicleEmissionStandards == \"国五\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 1;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"国六\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 2;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"新能源\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 3;\r\n          }\r\n          console.log(\"this.dispatchForm\", this.dispatchForm);\r\n\r\n          let dispatchInfo = {};\r\n          dispatchInfo.carNum = this.dispatchForm.carNum;\r\n\r\n          isAllowDispatch(dispatchInfo).then(response => {\r\n            let row = response.data;\r\n            if (row > 0) {\r\n              this.$message.error(\"当前车有正在执行的任务\")\r\n            } else {\r\n              let param = {};\r\n              param.leaveTask = this.dispatchForm;\r\n              param.leaveTaskMaterialList = resultList;\r\n              addTaskAndMaterialAndAddLeaveLog(param).then(res => {\r\n                console.log(\"addTaskAndMaterialAndAddLeaveLog\", res)\r\n                if (res.code == 200) {\r\n                  this.$message.success('派车成功');\r\n                  this.dispatchDialogVisible = false;\r\n                  this.getListTaskInfo();\r\n                } else {\r\n                  // 其他失败原因\r\n                  this.$message.error(res.message || '派车失败');\r\n                }\r\n              }).catch(err => {\r\n                console.error('dispatch error:', err);\r\n                this.$message.error('网络异常，稍后重试');\r\n              });\r\n\r\n              // addTaskAndMaterial(this.dispatchForm).then(response => {\r\n              //   console.log(\"addTaskAndMaterial\", response);\r\n              //   let snowId = response.data;\r\n              //   this.planInfo.materials.forEach(item => {\r\n              //     item.taskNo = snowId;\r\n              //     addTaskMaterial(item);\r\n              //   });\r\n\r\n              //   console.log(\"生成派车日志\");\r\n\r\n              //   //生成派车日志\r\n              //   let leaveTaskLog = {};\r\n\r\n\r\n              //   leaveTaskLog.logType = 2;\r\n              //   leaveTaskLog.taskNo = snowId;\r\n              //   leaveTaskLog.applyNo = this.applyNo;\r\n              //   leaveTaskLog.info = '派车任务创建：' + this.dispatchForm.carNum + ' ' + this.dispatchForm.driverName\r\n              //   addLeaveLog(leaveTaskLog);\r\n\r\n              //   this.$message.success('派车成功');\r\n              //   this.dispatchDialogVisible = false;\r\n              //   this.getListTaskInfo();\r\n              // });\r\n\r\n              this.dispatchDialogVisible = false;\r\n            }\r\n            console.log(\"this.isAllowDispatch\", response);\r\n          }).catch(err => {\r\n            console.error('dispatch error:', err);\r\n            this.$message.error('网络异常，稍后重试');\r\n          });\r\n\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(date) {\r\n      const year = date.getFullYear();\r\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const day = date.getDate().toString().padStart(2, '0');\r\n      const hours = date.getHours().toString().padStart(2, '0');\r\n      const minutes = date.getMinutes().toString().padStart(2, '0');\r\n      const seconds = date.getSeconds().toString().padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    // 打印功能\r\n    handlePrint() {\r\n      this.$message.success('打印功能尚未实现');\r\n      // 实际项目中可以调用浏览器打印功能\r\n      // window.print();\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$tab.closeOpenPage(this.$route);\r\n      this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n    },\r\n\r\n    // 跳转到任务详情页面\r\n    goToTaskDetail(row) {\r\n      this.$router.push({\r\n        path: `/leave/plan/task/${row.taskNo}`\r\n      });\r\n    },\r\n\r\n    getTaskTypeText(taskType) {\r\n      const standardMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return standardMap[taskType] || '未知';\r\n    },\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取计划状态类型\r\n    getPlanStatusType(status) {\r\n      const statusMap = {\r\n        '1': 'warning',  // 待分厂审批\r\n        '2': 'warning',  // 待分厂复审\r\n        '3': 'warning',  // 待生产指挥中心审批\r\n        '4': 'success',  // 审批完成\r\n        '5': 'primary',  // 已出厂\r\n        '6': 'info',     // 部分收货\r\n        '7': 'success',  // 已完成\r\n        '11': 'danger',  // 驳回\r\n        '12': 'danger',  // 废弃\r\n        '13': 'danger'   // 过期\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n\r\n    /**\r\n     * 获取计划下所有任务的任务物资\r\n     * @returns {Promise<void>}\r\n     */\r\n    async getAllTaskMaterials() {\r\n      try {\r\n        // 清空现有数据\r\n        this.taskMaterialMap.clear();\r\n\r\n        // 获取该计划下所有任务的任务物资\r\n        const params = {\r\n          applyNo: this.applyNo\r\n        };\r\n\r\n        const response = await listTaskMaterial(params);\r\n        if (response.code === 200 && response.rows) {\r\n          // 将任务物资按物资ID分组存储\r\n          response.rows.forEach(material => {\r\n            const key = material.materialId;\r\n            if (!this.taskMaterialMap.has(key)) {\r\n              this.taskMaterialMap.set(key, {\r\n                materialId: material.materialId,\r\n                materialName: material.materialName,\r\n                materialSpec: material.materialSpec,\r\n                planNum: material.planNum,\r\n                usedNum: 0,\r\n                taskMaterials: [] // 存储每个任务的具体物资信息\r\n              });\r\n            }\r\n\r\n            const materialInfo = this.taskMaterialMap.get(key);\r\n            // 累加每个任务物资的计划数量作为已使用数量\r\n            materialInfo.usedNum += material.planNum;\r\n            materialInfo.taskMaterials.push({\r\n              taskNo: material.taskNo,\r\n              carNum: material.carNum,\r\n              planNum: material.planNum,\r\n              createTime: material.createTime\r\n            });\r\n          });\r\n        }\r\n\r\n        // 更新物资选择列表中的已使用数量\r\n        this.updateMaterialUsedNum();\r\n\r\n        console.log('Task Material Map:', this.taskMaterialMap);\r\n      } catch (error) {\r\n        console.error('获取任务物资失败:', error);\r\n        this.$message.error('获取任务物资失败');\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 更新物资选择列表中的已使用数量\r\n     */\r\n    updateMaterialUsedNum() {\r\n      this.materialSelectionList.forEach(row => {\r\n        if (row.materialId) {\r\n          const materialInfo = this.taskMaterialMap.get(row.materialId);\r\n          if (materialInfo) {\r\n            // 直接使用累加的计划数量作为已使用数量\r\n            row.usedNum = materialInfo.usedNum;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 物资确认按钮点击事件\r\n    async handleMaterialConfirm() {\r\n      try {\r\n        // 校验所有任务的taskStatus是否为9\r\n        if (this.taskListInfo && this.taskListInfo.length > 0) {\r\n          const unfinishedTasks = this.taskListInfo.filter(task => task.taskStatus !== 9);\r\n          if (unfinishedTasks.length > 0) {\r\n            this.$message.error('存在未完成的任务，无法进行物资确认');\r\n            return;\r\n          }\r\n        }\r\n\r\n        // 调用后端接口，传递applyNo\r\n        await confirmMaterial({ applyNo: this.applyNo });\r\n        this.$message.success('物资确认成功');\r\n        // 刷新详情\r\n        this.getListTaskInfo();\r\n        // 重新获取planInfo\r\n        detailPlan(this.applyNo).then(response => {\r\n          this.planInfo = response.data;\r\n        });\r\n      } catch (e) {\r\n        this.$message.error('物资确认失败');\r\n      }\r\n    },\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #8957e5;\r\n  /* 基本信息模块 - 紫色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 图片列表模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 文件列表模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 物资列表模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 派车信息模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(6) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #8957e5;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(6) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.fixed-bottom-action {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 15px 0;\r\n  text-align: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.empty-data {\r\n  padding: 30px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.el-dialog__body {\r\n  padding: 20px 30px 0;\r\n}\r\n\r\n.image-container,\r\n.file-container {\r\n  padding: 20px;\r\n}\r\n\r\n.image-list,\r\n.file-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.image-item {\r\n  width: 150px;\r\n  height: 180px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.image-item img {\r\n  width: 100%;\r\n  height: 150px;\r\n  object-fit: cover;\r\n  display: block;\r\n}\r\n\r\n.image-name {\r\n  padding: 5px;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  min-width: 180px;\r\n  max-width: 250px;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 24px;\r\n  margin-right: 8px;\r\n  color: #909399;\r\n}\r\n\r\n.file-item:hover .file-icon {\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 新增物资选择相关样式 */\r\n.el-input-number {\r\n  width: 120px;\r\n}\r\n\r\n.material-selection {\r\n  margin-top: 20px;\r\n}\r\n\r\n.material-selection .el-table {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.dispatch-log-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 0px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.el-button--text {\r\n  color: #409EFF;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n\r\n  &:hover {\r\n    color: #66b1ff;\r\n    background-color: transparent;\r\n  }\r\n\r\n  &:focus {\r\n    color: #409EFF;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4cA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAI,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;IACA,IAAAC,iBAAA,YAAAA,kBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,OAAA;MACA,KAAAA,OAAA,CAAAC,IAAA,CAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;;IAEA;IACA,IAAAI,aAAA,YAAAA,cAAAN,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,OAAA;MACA,KAAAA,OAAA,CAAAC,IAAA,CAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;;IAEA;IACA,IAAAK,cAAA,YAAAA,eAAAP,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,OAAA;MACA,KAAAA,OAAA,CAAAC,IAAA,CAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IAEA;MACAM,cAAA;MACAC,+BAAA;MACAC,eAAA;MACAC,OAAA;MACAC,cAAA;MACAC,kBAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,qBAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,cAAA;QAAA;QACAC,WAAA;MACA;MAEA;MACAC,SAAA;MAEA;MACAC,QAAA;MAEA;MACAC,qBAAA;MAEAC,YAAA;MAEA;MACAC,YAAA;QACA;QACA;QACA;QACA;MAAA,CACA;MAEA;MACAC,aAAA;QACAC,SAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,SAAA,EAAAhC,iBAAA;UAAA+B,OAAA;QAAA,EACA;QACAE,UAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,GAAA;UAAAC,GAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,WAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,SAAA,EAAAzB,aAAA;UAAAwB,OAAA;QAAA,EACA;QACAM,YAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,SAAA,EAAAxB,cAAA;UAAAuB,OAAA;QAAA;MAEA;MAEA;MACAO,YAAA,GACA;QACAC,EAAA;QACAX,SAAA;QACAK,UAAA;QACAG,WAAA;QACAC,YAAA;QACAG,YAAA;QACAC,MAAA;QACAC,UAAA;QACAC,WAAA;QACAC,oBAAA;QACAC,mBAAA;MACA,GACA;QACAN,EAAA;QACAX,SAAA;QACAK,UAAA;QACAG,WAAA;QACAC,YAAA;QACAG,YAAA;QACAC,MAAA;QACAC,UAAA;QACAC,WAAA;QACAC,oBAAA;QACAC,mBAAA;MACA,EACA;MAEA;MACAC,QAAA;MACA3B,OAAA;MACA4B,eAAA;QACA5B,OAAA;MACA;MAEA6B,gBAAA;MACA;MACAC,qBAAA;QACAC,UAAA;QACAC,YAAA;QACAC,YAAA;QACAC,OAAA;QACAC,OAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACAC,kBAAA;MAAA;MACAC,mBAAA,MAAAC,GAAA;MAAA;MACAC,eAAA,MAAAD,GAAA;IACA;EACA;EACAE,QAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;IACA;IACA;IACAC,wBAAA,WAAAA,yBAAA;MACA,YAAA/C,iBAAA,QAAAC,qBAAA,QAAAF,UAAA,CAAAiD,KAAA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA,YAAApD,cAAA,QAAAC,kBAAA,QAAAF,OAAA,CAAAoD,KAAA;IACA;IACAE,sBAAA,WAAAA,uBAAA;MACA;MACA,cAAAC,QAAA,MAAArB,QAAA,CAAAsB,UAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,QAAA,uCAAAC,IAAA,WAAAC,QAAA;MACAH,KAAA,CAAA5D,+BAAA,GAAA+D,QAAA,CAAA1E,IAAA;IACA;IACA,KAAAwE,QAAA,oBAAAC,IAAA,WAAAC,QAAA;MACAH,KAAA,CAAA3D,eAAA,GAAA8D,QAAA,CAAA1E,IAAA;MACA,IAAAuE,KAAA,CAAAxB,QAAA,CAAA4B,QAAA;QACAJ,KAAA,CAAA3D,eAAA,CAAAgE,MAAA;MACA;IACA;IACA;IACA,IAAAxD,OAAA,QAAAyD,MAAA,CAAAC,MAAA,CAAA1D,OAAA;IACA,KAAAA,OAAA,GAAAA,OAAA;IACA,KAAA4B,eAAA,CAAA5B,OAAA,GAAAA,OAAA;IACA,KAAAD,WAAA,CAAAC,OAAA,GAAAA,OAAA;IACA,IAAAA,OAAA;MACA,IAAA2D,gBAAA,EAAA3D,OAAA,EAAAqD,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,QAAA,GAAA2B,QAAA,CAAA1E,IAAA;QACAuE,KAAA,CAAAS,kBAAA;QACAC,OAAA,CAAAC,GAAA,kBAAAX,KAAA,CAAAxB,QAAA;QACA;QACAwB,KAAA,CAAAY,qBAAA;MACA;IACA;IAAA;IACA,KAAAC,eAAA;IACA,KAAAC,aAAA;IACA,KAAAC,UAAA;EAIA;EAGAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAtE,WAAA,CAAAG,WAAA;MACA2D,OAAA,CAAAC,GAAA,0BAAA/D,WAAA;MACA,IAAAuE,aAAA,OAAAvE,WAAA,EAAAsD,IAAA,WAAAC,QAAA;QACAe,MAAA,CAAAE,QAAA,CAAAC,OAAA;QACA;QACAH,MAAA,CAAAI,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA;YACAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;YACAC,OAAA;UACA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAb,MAAA,CAAAE,QAAA,CAAAW,KAAA;QACArB,OAAA,CAAAqB,KAAA,oBAAAA,KAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAArF,WAAA,CAAAG,WAAA;MACA2D,OAAA,CAAAC,GAAA,0BAAA/D,WAAA;MACA,IAAAuE,aAAA,OAAAvE,WAAA,EAAAsD,IAAA,WAAAC,QAAA;QACA8B,MAAA,CAAAb,QAAA,CAAAC,OAAA;QACA;QACAY,MAAA,CAAAX,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA;YACAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;YACAC,OAAA;UACA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAE,MAAA,CAAAb,QAAA,CAAAW,KAAA;QACArB,OAAA,CAAAqB,KAAA,oBAAAA,KAAA;MACA;IACA;IACAG,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,aAAA,OAAA5D,QAAA,EAAA0B,IAAA,WAAAC,QAAA;QACAgC,MAAA,CAAAf,QAAA,CAAAC,OAAA;QAEA,IAAAgB,MAAA,CAAAC,OAAA,CAAAC,MAAA;UACAJ,MAAA,CAAAb,OAAA,CAAAkB,EAAA;QACA;UACAL,MAAA,CAAAb,OAAA,CAAAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;cAAAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;YAAA;UAAA;QACA;MACA,GAAAE,KAAA,WAAAC,KAAA;QACAI,MAAA,CAAAf,QAAA,CAAAW,KAAA;QACArB,OAAA,CAAAqB,KAAA,oBAAAA,KAAA;MACA;IACA;IACAtB,kBAAA,WAAAA,mBAAA;MACA,SAAAjC,QAAA,CAAA4B,QAAA;QACA,KAAAjE,cAAA;MACA;IACA;IACA0E,eAAA,WAAAA,gBAAA;MAAA,IAAA4B,MAAA;MACA,IAAAC,cAAA,OAAAjE,eAAA,EAAAyB,IAAA,WAAAC,QAAA;QACAO,OAAA,CAAAC,GAAA,kBAAAR,QAAA,CAAAwC,IAAA;QACAF,MAAA,CAAAtF,YAAA,GAAAgD,QAAA,CAAAwC,IAAA;QACAjC,OAAA,CAAAC,GAAA,sBAAA8B,MAAA,CAAAtF,YAAA;QACA;QACAsF,MAAA,CAAAG,mBAAA;MACA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,IAAAC,YAAA;MACAT,MAAA,CAAAU,IAAA,CAAAD,YAAA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MACA,IAAAF,YAAA;MACAT,MAAA,CAAAU,IAAA,CAAAD,YAAA;IACA;IACA;IACAG,8BAAA,WAAAA,+BAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAAhH,+BAAA,EAAA8G,GAAA,CAAAG,wBAAA;IACA;IAEAC,cAAA,WAAAA,eAAAJ,GAAA,EAAAC,MAAA;MACA,YAAAI,eAAA,CAAAL,GAAA,CAAAM,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAP,GAAA,EAAAC,MAAA;MACA,YAAAO,aAAA,CAAAR,GAAA,CAAAS,UAAA;IACA;IAEA,eACA5C,UAAA,WAAAA,WAAA;MAAA,IAAA6C,MAAA;MACA,KAAAC,OAAA;MACA;MACA;MACA;MACA;MACA,IAAAC,kCAAA,IAAA5D,IAAA,WAAAC,QAAA;QACAyD,MAAA,CAAAtH,OAAA,GAAA6D,QAAA,CAAAwC,IAAA;QACAiB,MAAA,CAAApH,kBAAA,GAAAoH,MAAA,CAAAtH,OAAA;QACAsH,MAAA,CAAAC,OAAA;MACA;IACA;IACA;IACAE,aAAA,WAAAA,cAAAtC,KAAA;MACA,KAAAlF,cAAA,GAAAkF,KAAA;MAEA,SAAAlF,cAAA;QAEA,KAAAC,kBAAA,QAAAF,OAAA,CAAA0H,MAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAA3G,SAAA,CAAAuC,QAAA,CAAA4B,KAAA;QAAA,CACA;MACA;QAEA,KAAAjF,kBAAA,QAAAF,OAAA,CAAAoD,KAAA;MACA;IACA;IACA;IACAwE,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,SAAA/G,YAAA,CAAAgH,QAAA;QACA,KAAA3H,UAAA,CAAA4H,OAAA,WAAAJ,IAAA;UACA,IAAAA,IAAA,CAAAhG,EAAA,IAAAkG,MAAA,CAAA/G,YAAA,CAAAgH,QAAA;YACAD,MAAA,CAAA/G,YAAA,CAAA5B,IAAA,GAAAyI,IAAA,CAAAzI,IAAA;YACA2I,MAAA,CAAA/G,YAAA,CAAAkH,MAAA,GAAAL,IAAA,CAAAK,MAAA;YACAH,MAAA,CAAA/G,YAAA,CAAAmH,OAAA,GAAAN,IAAA,CAAAM,OAAA;YACAJ,MAAA,CAAA/G,YAAA,CAAAoH,KAAA,GAAAP,IAAA,CAAAO,KAAA;YACAL,MAAA,CAAA/G,YAAA,CAAAqH,KAAA,GAAAR,IAAA,CAAAQ,KAAA;YACAN,MAAA,CAAA/G,YAAA,CAAAsH,WAAA,GAAAT,IAAA,CAAAS,WAAA;YACAP,MAAA,CAAA/G,YAAA,CAAAuH,iBAAA,GAAAV,IAAA,CAAAU,iBAAA;YACAR,MAAA,CAAA/G,YAAA,CAAAwH,kBAAA,GAAAX,IAAA,CAAAW,kBAAA;YACAT,MAAA,CAAA/G,YAAA,CAAAyH,GAAA,GAAAZ,IAAA,CAAAa,MAAA;UAEA;QACA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACAtE,OAAA,CAAAC,GAAA;MACA,SAAAvD,YAAA,CAAA6H,OAAA;QACA,KAAA3I,OAAA,CAAA+H,OAAA,WAAAJ,IAAA;UACA,IAAAA,IAAA,CAAAhG,EAAA,IAAA+G,MAAA,CAAA5H,YAAA,CAAA6H,OAAA;YACAD,MAAA,CAAA5H,YAAA,CAAAE,SAAA,GAAA2G,IAAA,CAAA3G,SAAA;YAEA,IAAA2G,IAAA,CAAAZ,wBAAA;cACA2B,MAAA,CAAA5H,YAAA,CAAAiG,wBAAA;YACA,WAAAY,IAAA,CAAAZ,wBAAA;cACA2B,MAAA,CAAA5H,YAAA,CAAAiG,wBAAA;YACA,WAAAY,IAAA,CAAAZ,wBAAA;cACA2B,MAAA,CAAA5H,YAAA,CAAAiG,wBAAA;YACA;cACA2B,MAAA,CAAA5H,YAAA,CAAAiG,wBAAA;YACA;YACA2B,MAAA,CAAA5H,YAAA,CAAA8H,iBAAA,GAAAjB,IAAA,CAAAiB,iBAAA;YACAF,MAAA,CAAA5H,YAAA,CAAA+H,KAAA,GAAAlB,IAAA,CAAAkB,KAAA;YACAH,MAAA,CAAA5H,YAAA,CAAAgI,aAAA,GAAAnB,IAAA,CAAAmB,aAAA;YACAJ,MAAA,CAAA5H,YAAA,CAAAiI,SAAA,GAAApB,IAAA,CAAAoB,SAAA;YACAL,MAAA,CAAA5H,YAAA,CAAAkI,QAAA,GAAArB,IAAA,CAAAqB,QAAA;YACAN,MAAA,CAAA5H,YAAA,CAAAmI,YAAA,GAAAtB,IAAA,CAAAsB,YAAA;YACAP,MAAA,CAAA5H,YAAA,CAAAoI,SAAA,GAAAvB,IAAA,CAAAuB,SAAA;YACAR,MAAA,CAAA5H,YAAA,CAAAqI,YAAA,GAAAxB,IAAA,CAAAwB,YAAA;YACAT,MAAA,CAAA5H,YAAA,CAAAsI,SAAA,GAAAzB,IAAA,CAAAyB,SAAA;UACA;QAEA;MACA;IACA;IACA,eACA5E,aAAA,WAAAA,cAAA;MAAA,IAAA6E,MAAA;MACA;MACA;MACA;MACA;MACA,IAAAC,mCAAA,IAAA1F,IAAA,WAAAC,QAAA;QACAwF,MAAA,CAAAlJ,UAAA,GAAA0D,QAAA,CAAAwC,IAAA;QACAjC,OAAA,CAAAC,GAAA,oBAAAgF,MAAA,CAAAlJ,UAAA;QACAkJ,MAAA,CAAAhJ,qBAAA,GAAAgJ,MAAA,CAAAlJ,UAAA;MACA;IACA;IACA;IACAoJ,gBAAA,WAAAA,iBAAApE,KAAA;MACA,KAAA/E,iBAAA,GAAA+E,KAAA;MAEA,SAAA/E,iBAAA;QAEA,KAAAC,qBAAA,QAAAF,UAAA,CAAAuH,MAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAA6B,UAAA,CAAAjG,QAAA,CAAA4B,KAAA;QAAA,CACA;MACA;QAEA,KAAA9E,qBAAA,QAAAF,UAAA,CAAAiD,KAAA;MACA;IACA;IACA;IACAkB,qBAAA,WAAAA,sBAAA;MACA;MACA,SAAApC,QAAA,CAAAuH,WAAA;QACA;UACA,KAAA/I,SAAA,GAAAgJ,IAAA,CAAAC,KAAA,MAAAzH,QAAA,CAAAuH,WAAA;QACA,SAAAG,CAAA;UACAxF,OAAA,CAAAqB,KAAA,cAAAmE,CAAA;UACA,KAAAlJ,SAAA;QACA;MACA;;MAEA;MACA,SAAAwB,QAAA,CAAA2H,YAAA;QACA;UACA,KAAAlJ,QAAA,GAAA+I,IAAA,CAAAC,KAAA,MAAAzH,QAAA,CAAA2H,YAAA;QACA,SAAAD,CAAA;UACAxF,OAAA,CAAAqB,KAAA,cAAAmE,CAAA;UACA,KAAAjJ,QAAA;QACA;MACA;IACA;IAEA;IACAmJ,YAAA,WAAAA,aAAAC,GAAA,EAAAC,QAAA;MACA,KAAAD,GAAA;QACA,KAAAjF,QAAA,CAAAW,KAAA;QACA;MACA;;MAEA;MACA,IAAAwE,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,IAAA,CAAAG,IAAA,GAAAL,GAAA;MACAE,IAAA,CAAAI,QAAA,GAAAL,QAAA;MACAE,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;MACAA,IAAA,CAAAO,KAAA;MACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,IAAA;IACA;IAEA;IACAS,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAE,uBAAA,WAAAA,wBAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAE,mBAAA,WAAAA,oBAAAL,IAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAM,iBAAA,WAAAA,kBAAApJ,MAAA;MACA,IAAAqJ,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAArJ,MAAA;IACA;IAEA;IACAsJ,WAAA,WAAAA,YAAA9G,GAAA;MACA,IAAA+G,eAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,eAAA,CAAA/G,GAAA,CAAAgH,OAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAzJ,MAAA;MACA,IAAAqJ,SAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAArJ,MAAA;IACA;IAEA;IACA0J,qBAAA,WAAAA,sBAAA1J,MAAA;MACA,IAAAqJ,SAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAArJ,MAAA;IACA;IAEA;IACA2J,kBAAA,WAAAA,mBAAAb,IAAA;MACA,IAAAC,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAc,sBAAA,WAAAA,uBAAAd,IAAA;MACA,IAAAC,OAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAe,0BAAA,WAAAA,2BAAAZ,QAAA;MACA,IAAAF,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAE,QAAA;IACA;IAEA;IACAa,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAA9I,kBAAA,QAAAX,QAAA,CAAA0J,SAAA;MACAxH,OAAA,CAAAC,GAAA,iCAAAxB,kBAAA;;MAEA;MACA,KAAAgJ,mCAAA;MACA;MACA,SAAA3J,QAAA,CAAA4J,WAAA;QACA,SAAAhL,YAAA,CAAAoG,QAAA;UACA;UACA,IAAA6E,YAAA,QAAAlL,YAAA,CAAAmL,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAA/E,QAAA;UAAA;UACA,IAAA6E,YAAA;YACA,KAAAjH,QAAA,CAAAoH,OAAA;YACA;UACA;UACA9H,OAAA,CAAAC,GAAA,iBAAA0H,YAAA;QACA;MACA;;MAGA;MACA,IAAAI,KAAA,QAAAC,MAAA,CAAAC,OAAA,CAAAF,KAAA;MACA/H,OAAA,CAAAC,GAAA,UAAA8H,KAAA;MACA,KAAAA,KAAA,CAAA5I,QAAA,uBAAA4I,KAAA,CAAA5I,QAAA;QACA,KAAAuB,QAAA,CAAAW,KAAA;QACA;MACA;MAEArB,OAAA,CAAAC,GAAA,kCAAAnC,QAAA,CAAAsB,UAAA;MACA,eAAAD,QAAA,MAAArB,QAAA,CAAAsB,UAAA;QACA,KAAAsB,QAAA,CAAAoH,OAAA;QACA;MACA;MAKA9H,OAAA,CAAAC,GAAA,4BAAAxD,YAAA,CAAAoF,MAAA;MACA,SAAA/D,QAAA,CAAAoK,gBAAA,eAAAzL,YAAA,CAAAoF,MAAA;QACA,KAAAnB,QAAA,CAAAoH,OAAA;QACA;MACA;MAEA,SAAAhK,QAAA,CAAAoK,gBAAA,eAAAzL,YAAA,CAAAoF,MAAA;QACA,KAAAnB,QAAA,CAAAoH,OAAA;QACA;MACA;MAEA,KAAApL,YAAA;MAEA,SAAAoB,QAAA,CAAA4B,QAAA;QACA,KAAAhD,YAAA,CAAAoG,QAAA;MACA,gBAAAhF,QAAA,CAAA4B,QAAA;QACA,KAAAhD,YAAA,CAAAoG,QAAA;MACA,gBAAAhF,QAAA,CAAA4B,QAAA;QACA,KAAAhD,YAAA,CAAAoG,QAAA;MACA;MACA9C,OAAA,CAAAC,GAAA,MAAAvD,YAAA,CAAAoG,QAAA,GACA,KAAAtG,qBAAA;IAGA;IAEA;IACAiL,mCAAA,WAAAA,oCAAA;MAAA,IAAAU,MAAA;MACA;MACA,KAAAzJ,mBAAA,CAAA0J,KAAA;MACA;MACA,IAAAC,SAAA,QAAA5L,YAAA,CAAA6G,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAT,QAAA;MAAA;MACA9C,OAAA,CAAAC,GAAA,cAAAoI,SAAA;MACA,KAAAA,SAAA,IAAAA,SAAA,CAAAxG,MAAA;QACA;QACA,KAAA5D,qBAAA,SAAAH,QAAA,CAAA0J,SAAA,QAAAc,GAAA,WAAAC,GAAA;UACA;UACA;UACA;YACArK,UAAA,EAAAqK,GAAA,CAAArK,UAAA;YACAC,YAAA,EAAAoK,GAAA,CAAApK,YAAA;YACAC,YAAA,EAAAmK,GAAA,CAAAnK,YAAA;YACAC,OAAA,EAAAkK,GAAA,CAAAlK,OAAA;YACAC,OAAA;YACAC,YAAA,EAAAgK,GAAA,CAAAlK,OAAA;YACAG,UAAA,EAAA+J,GAAA,CAAAlK;UACA;QACA;MACA;QACA2B,OAAA,CAAAC,GAAA,2BAAAxD,YAAA;QACA4L,SAAA,CAAA1E,OAAA,WAAAkE,IAAA;UACA,IAAAhI,MAAA;YAAA2I,MAAA,EAAAX,IAAA,CAAAW;UAAA;UACA,IAAAC,sBAAA,EAAA5I,MAAA,EAAAL,IAAA,WAAAC,QAAA;YACA,IAAAiJ,aAAA,GAAAjJ,QAAA,CAAAwC,IAAA;YACAyG,aAAA,CAAA/E,OAAA,WAAAgF,QAAA;cACA,KAAAR,MAAA,CAAAzJ,mBAAA,CAAAkK,GAAA,CAAAD,QAAA,CAAAzK,UAAA;gBACAiK,MAAA,CAAAzJ,mBAAA,CAAAmK,GAAA,CAAAF,QAAA,CAAAzK,UAAA;kBACA4K,gBAAA,EAAAH,QAAA;kBACArK,OAAA,EAAAqK,QAAA,CAAAtK;gBACA;cACA;gBACA,IAAA0K,gBAAA,GAAAZ,MAAA,CAAAzJ,mBAAA,CAAAsK,GAAA,CAAAL,QAAA,CAAAzK,UAAA;gBACA6K,gBAAA,CAAAzK,OAAA,IAAAqK,QAAA,CAAAtK,OAAA;cACA;YACA;;YAEA;YACA8J,MAAA,CAAAnK,gBAAA,GAAAiL,KAAA,CAAAC,IAAA,CAAAf,MAAA,CAAAzJ,mBAAA,YAAAyK,IAAA;cAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAH,IAAA;gBAAAI,GAAA,GAAAH,KAAA;gBAAAlO,KAAA,GAAAkO,KAAA;cAAA,WAAAI,cAAA,CAAAF,OAAA;gBACApL,UAAA,EAAAqL;cAAA,GACArO,KAAA;YAAA,CACA;;YAEA;YACAiN,MAAA,CAAAlK,qBAAA,IAAAkK,MAAA,CAAArK,QAAA,CAAA0J,SAAA,QAAAc,GAAA,WAAAC,GAAA;cAAA,IAAAkB,qBAAA;cACA,IAAAnL,OAAA,KAAAmL,qBAAA,GAAAtB,MAAA,CAAAzJ,mBAAA,CAAAsK,GAAA,CAAAT,GAAA,CAAArK,UAAA,eAAAuL,qBAAA,uBAAAA,qBAAA,CAAAnL,OAAA;cACA,IAAAC,YAAA,GAAAmL,IAAA,CAAAvM,GAAA,EAAAoL,GAAA,CAAAlK,OAAA,SAAAC,OAAA;cAEA;gBACAJ,UAAA,EAAAqK,GAAA,CAAArK,UAAA;gBACAC,YAAA,EAAAoK,GAAA,CAAApK,YAAA;gBACAC,YAAA,EAAAmK,GAAA,CAAAnK,YAAA;gBACAC,OAAA,EAAAkK,GAAA,CAAAlK,OAAA;gBACAC,OAAA,EAAAA,OAAA;gBACAC,YAAA,EAAAA,YAAA;gBACAC,UAAA,EAAAD;cACA;YACA;YAEA4J,MAAA,CAAAlK,qBAAA,GAAAkK,MAAA,CAAAlK,qBAAA,CAAAqF,MAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAAhF,YAAA;YAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAT,QAAA,CAAA4J,WAAA;QACA,SAAAhL,YAAA,CAAAoG,QAAA;UACA;UACA,IAAA6E,YAAA,QAAAlL,YAAA,CAAAmL,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAA/E,QAAA;UAAA;UACA,IAAA6E,YAAA;YACA,KAAAjH,QAAA,CAAAoH,OAAA;YACA;UACA;UACA9H,OAAA,CAAAC,GAAA,iBAAA0H,YAAA;QACA;MACA;IAGA;IAEA;IACAgC,iBAAA,WAAAA,kBAAA;MACA,KAAAC,KAAA,CAAAlN,YAAA,SAAAkN,KAAA,CAAAlN,YAAA,CAAAmN,WAAA;MACA,KAAA5L,qBAAA;QACAC,UAAA;QACAC,YAAA;QACAC,YAAA;QACAC,OAAA;QACAE,YAAA;QACAD,OAAA;QACAE,UAAA;MACA;IACA;IAEA;IACAsL,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAArL,mBAAA,CAAA0J,KAAA;MACA,KAAA3L,YAAA,CAAAkH,OAAA,WAAAkE,IAAA;QACA,IAAAhI,MAAA;UACA2I,MAAA,EAAAX,IAAA,CAAAW;QACA;QACA,IAAAC,sBAAA,EAAA5I,MAAA,EAAAL,IAAA,WAAAC,QAAA;UACAO,OAAA,CAAAC,GAAA,qBAAAR,QAAA,CAAAwC,IAAA;UACA,IAAAyG,aAAA;UACAA,aAAA,GAAAjJ,QAAA,CAAAwC,IAAA;UACAyG,aAAA,CAAA/E,OAAA,WAAAgF,QAAA;YACA,KAAAoB,MAAA,CAAArL,mBAAA,CAAAkK,GAAA,CAAAD,QAAA,CAAAzK,UAAA;cACA6L,MAAA,CAAArL,mBAAA,CAAAmK,GAAA,CAAAF,QAAA,CAAAzK,UAAA;gBACA4K,gBAAA,EAAAH,QAAA;gBACArK,OAAA,EAAAqK,QAAA,CAAAtK;cACA;YACA;cACA,IAAA0K,gBAAA,GAAAgB,MAAA,CAAArL,mBAAA,CAAAsK,GAAA,CAAAL,QAAA,CAAAzK,UAAA;cACA6K,gBAAA,CAAAzK,OAAA,IAAAqK,QAAA,CAAAtK,OAAA;YACA;UACA;UACA;UACA0L,MAAA,CAAA/L,gBAAA,GAAAiL,KAAA,CAAAC,IAAA,CAAAa,MAAA,CAAArL,mBAAA,YAAAsL,KAAA;YAAA,IAAAC,KAAA,OAAAZ,eAAA,CAAAC,OAAA,EAAAU,KAAA;cAAAT,GAAA,GAAAU,KAAA;cAAA/O,KAAA,GAAA+O,KAAA;YAAA,WAAAT,cAAA,CAAAF,OAAA;cACApL,UAAA,EAAAqL;YAAA,GACArO,KAAA;UAAA,CACA;UACA8E,OAAA,CAAAC,GAAA,sBAAA8J,MAAA,CAAA/L,gBAAA;UACAgC,OAAA,CAAAC,GAAA,wBAAA8J,MAAA,CAAArL,mBAAA;QACA;MACA;IACA;IAEA;IACAwL,cAAA,WAAAA,eAAA;MACA,KAAAjM,qBAAA,CAAA4C,IAAA;QACA3C,UAAA;QACAC,YAAA;QACAC,YAAA;QACAC,OAAA;QACAE,YAAA;QACAD,OAAA;QACAE,UAAA;MACA;IACA;IAEA;IACA2L,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAAnM,qBAAA,CAAA0B,MAAA,CAAAyK,KAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA7H,GAAA,EAAA4H,KAAA;MACApK,OAAA,CAAAC,GAAA,8BAAAjC,gBAAA;MAGA,IAAAsM,gBAAA,QAAAtM,gBAAA,CAAAuM,IAAA,WAAAhH,IAAA;QAAA,OAAAA,IAAA,CAAArF,UAAA,KAAAsE,GAAA,CAAAtE,UAAA;MAAA;MACA,IAAAoM,gBAAA;QACA9H,GAAA,CAAAlE,OAAA,GAAAgM,gBAAA,CAAAhM,OAAA;MACA;MACA,IAAAkM,kBAAA,QAAA1M,QAAA,CAAA0J,SAAA,CAAA+C,IAAA,WAAAhH,IAAA;QAAA,OAAAA,IAAA,CAAArF,UAAA,KAAAsE,GAAA,CAAAtE,UAAA;MAAA;MAEA,IAAAsM,kBAAA;QACAhI,GAAA,CAAAnE,OAAA,GAAAmM,kBAAA,CAAAnM,OAAA;QACAmE,GAAA,CAAArE,YAAA,GAAAqM,kBAAA,CAAArM,YAAA;QACAqE,GAAA,CAAApE,YAAA,GAAAoM,kBAAA,CAAApM,YAAA;MACA;MAEAoE,GAAA,CAAAjE,YAAA,GAAAiE,GAAA,CAAAnE,OAAA,GAAAmE,GAAA,CAAAlE,OAAA;MACAkE,GAAA,CAAAhE,UAAA,GAAAgE,GAAA,CAAAnE,OAAA,GAAAmE,GAAA,CAAAlE,OAAA;MAEA0B,OAAA,CAAAC,GAAA,yBAAAuC,GAAA,EAAA4H,KAAA;IAEA;IAEA;IACAK,kBAAA,WAAAA,mBAAAjI,GAAA;MACA,KAAAA,GAAA,CAAAtE,UAAA;;MAEA;MACA,IAAAwM,YAAA,QAAAhM,mBAAA,CAAAsK,GAAA,CAAAxG,GAAA,CAAAtE,UAAA;MACA,IAAAI,OAAA,GAAAoM,YAAA,GAAAA,YAAA,CAAApM,OAAA;MAEA,OAAAkE,GAAA,CAAAnE,OAAA,GAAAC,OAAA;IACA;IAEA;IACAqM,mBAAA,WAAAA,oBAAAhC,QAAA;MACA;MACA;MACA;;MAEA;;MAEA;MACA;MACA;MACA;MACA;;MAEA,YAAA1K,qBAAA,CAAA2J,IAAA,WAAApF,GAAA;QAAA,OAAAA,GAAA,CAAAtE,UAAA,KAAAyK,QAAA,CAAAzK,UAAA;MAAA;MAAA;IACA;IAEA;IACA0M,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAAjB,KAAA,CAAAlN,YAAA,CAAAoO,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAF,OAAA,CAAA/M,QAAA,CAAA4J,WAAA;YACA,IAAAmD,OAAA,CAAAnO,YAAA,CAAAoG,QAAA;cACA;cACA,IAAA6E,YAAA,GAAAkD,OAAA,CAAApO,YAAA,CAAAmL,IAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAA/E,QAAA;cAAA;cACA,IAAA6E,YAAA;gBACAkD,OAAA,CAAAnK,QAAA,CAAAoH,OAAA;gBACA;cACA;YACA;UACA;;UAEA;UACA,IAAAkD,UAAA;UAEAhL,OAAA,CAAAC,GAAA,8BAAA4K,OAAA,CAAA/M,QAAA,CAAA4J,WAAA;UACA1H,OAAA,CAAAC,GAAA,+BAAA4K,OAAA,CAAAnO,YAAA,CAAAoG,QAAA;UAEA,IAAA+H,OAAA,CAAA/M,QAAA,CAAA4J,WAAA,SAAAmD,OAAA,CAAAnO,YAAA,CAAAoG,QAAA;YACA+H,OAAA,CAAA5M,qBAAA,CAAA0F,OAAA,WAAAsH,MAAA;cACA;cACA,IAAAC,YAAA,IAAAL,OAAA,CAAA/M,QAAA,CAAA0J,SAAA,QAAA+C,IAAA,CACA,UAAAhC,GAAA;gBAAA,OAAAA,GAAA,CAAArK,UAAA,KAAA+M,MAAA,CAAA/M,UAAA;cAAA,CACA;cACA,IAAAgN,YAAA;gBACA;gBACA,IAAAC,OAAA,OAAA3B,cAAA,CAAAF,OAAA,MAAA4B,YAAA;gBACAC,OAAA,CAAA9M,OAAA,GAAA4M,MAAA,CAAAzM,UAAA;gBACAwM,UAAA,CAAAnK,IAAA,CAAAsK,OAAA;cACA;YACA;;YAEA;YACAnL,OAAA,CAAAC,GAAA,+BAAA4K,OAAA,CAAA5M,qBAAA;YACA+B,OAAA,CAAAC,GAAA,eAAA+K,UAAA;;YAEA;YACA,KAAAH,OAAA,CAAA5M,qBAAA,CAAA4D,MAAA;cACAgJ,OAAA,CAAAnK,QAAA,CAAAoH,OAAA;cACA;YACA;;YAEA;YACA,IAAAsD,kBAAA,GAAAP,OAAA,CAAA5M,qBAAA,CAAA2J,IAAA,WAAApF,GAAA;cACA;cACA,OACA,CAAAA,GAAA,CAAAtE,UAAA,IACAsE,GAAA,CAAAhE,UAAA,SACAgE,GAAA,CAAAhE,UAAA,GAAAgE,GAAA,CAAAjE,YAAA;YAEA;YAEA,IAAA6M,kBAAA;cACAP,OAAA,CAAAnK,QAAA,CAAAoH,OAAA;cACA;YACA;UACA;YACA9H,OAAA,CAAAC,GAAA,4BAAA4K,OAAA,CAAA/M,QAAA,CAAA0J,SAAA;YACAwD,UAAA,GAAAH,OAAA,CAAA/M,QAAA,CAAA0J,SAAA,GAAAqD,OAAA,CAAA/M,QAAA,CAAA0J,SAAA,CAAAc,GAAA,WAAA/E,IAAA;cAAA,WAAAiG,cAAA,CAAAF,OAAA,MAAA/F,IAAA;YAAA;YACAvD,OAAA,CAAAC,GAAA,WAAA+K,UAAA;UACA;UAMA,IAAAH,OAAA,CAAA/M,QAAA,CAAA4J,WAAA,SAAAmD,OAAA,CAAAnO,YAAA,CAAAoG,QAAA;YACA+H,OAAA,CAAAnO,YAAA,CAAAuG,UAAA;UACA;YACA4H,OAAA,CAAAnO,YAAA,CAAAuG,UAAA;UACA;UAEA,IAAA4H,OAAA,CAAAnO,YAAA,CAAAoG,QAAA;YACA+H,OAAA,CAAAnO,YAAA,CAAAuG,UAAA;UACA;;UAGA;UACA4H,OAAA,CAAAnO,YAAA,CAAA2O,cAAA;UACA;UACAR,OAAA,CAAAnO,YAAA,CAAAP,OAAA,GAAA0O,OAAA,CAAA1O,OAAA;UACA0O,OAAA,CAAAnO,YAAA,CAAA4O,MAAA,GAAAT,OAAA,CAAA/M,QAAA,CAAAwN,MAAA;UACAT,OAAA,CAAAnO,YAAA,CAAA6O,MAAA,GAAAV,OAAA,CAAAnO,YAAA,CAAAE,SAAA;UACAiO,OAAA,CAAAnO,YAAA,CAAA8O,WAAA,GAAAX,OAAA,CAAAnO,YAAA,CAAAmH,OAAA;UACAgH,OAAA,CAAAnO,YAAA,CAAA+O,gBAAA,GAAAZ,OAAA,CAAAnO,YAAA,CAAAuH,iBAAA;UACA4G,OAAA,CAAAnO,YAAA,CAAAO,UAAA,GAAA4N,OAAA,CAAAnO,YAAA,CAAA5B,IAAA;UACA+P,OAAA,CAAAnO,YAAA,CAAAgP,WAAA,GAAAb,OAAA,CAAAnO,YAAA,CAAAoH,KAAA;UACA+G,OAAA,CAAAnO,YAAA,CAAAiP,OAAA,GAAAd,OAAA,CAAAnO,YAAA,CAAAqH,KAAA;UACA8G,OAAA,CAAAnO,YAAA,CAAAkP,iBAAA,GAAAf,OAAA,CAAAnO,YAAA,CAAAwH,kBAAA;UACA2G,OAAA,CAAAnO,YAAA,CAAAmP,QAAA,GAAAhB,OAAA,CAAAnO,YAAA,CAAAkH,MAAA;UACA,IAAAiH,OAAA,CAAAnO,YAAA,CAAAyH,GAAA;YACA0G,OAAA,CAAAnO,YAAA,CAAAyH,GAAA;UACA,WAAA0G,OAAA,CAAAnO,YAAA,CAAAyH,GAAA;YACA0G,OAAA,CAAAnO,YAAA,CAAAyH,GAAA;UACA;UACA,IAAA0G,OAAA,CAAAnO,YAAA,CAAAiG,wBAAA;YACAkI,OAAA,CAAAnO,YAAA,CAAAiG,wBAAA;UACA,WAAAkI,OAAA,CAAAnO,YAAA,CAAAiG,wBAAA;YACAkI,OAAA,CAAAnO,YAAA,CAAAiG,wBAAA;UACA,WAAAkI,OAAA,CAAAnO,YAAA,CAAAiG,wBAAA;YACAkI,OAAA,CAAAnO,YAAA,CAAAiG,wBAAA;UACA;UACA3C,OAAA,CAAAC,GAAA,sBAAA4K,OAAA,CAAAnO,YAAA;UAEA,IAAAoP,YAAA;UACAA,YAAA,CAAAP,MAAA,GAAAV,OAAA,CAAAnO,YAAA,CAAA6O,MAAA;UAEA,IAAAQ,qBAAA,EAAAD,YAAA,EAAAtM,IAAA,WAAAC,QAAA;YACA,IAAA+C,GAAA,GAAA/C,QAAA,CAAA1E,IAAA;YACA,IAAAyH,GAAA;cACAqI,OAAA,CAAAnK,QAAA,CAAAW,KAAA;YACA;cACA,IAAA2K,KAAA;cACAA,KAAA,CAAAC,SAAA,GAAApB,OAAA,CAAAnO,YAAA;cACAsP,KAAA,CAAAE,qBAAA,GAAAlB,UAAA;cACA,IAAAmB,sCAAA,EAAAH,KAAA,EAAAxM,IAAA,WAAA4M,GAAA;gBACApM,OAAA,CAAAC,GAAA,qCAAAmM,GAAA;gBACA,IAAAA,GAAA,CAAAC,IAAA;kBACAxB,OAAA,CAAAnK,QAAA,CAAAC,OAAA;kBACAkK,OAAA,CAAArO,qBAAA;kBACAqO,OAAA,CAAA1K,eAAA;gBACA;kBACA;kBACA0K,OAAA,CAAAnK,QAAA,CAAAW,KAAA,CAAA+K,GAAA,CAAAtP,OAAA;gBACA;cACA,GAAAsE,KAAA,WAAAkL,GAAA;gBACAtM,OAAA,CAAAqB,KAAA,oBAAAiL,GAAA;gBACAzB,OAAA,CAAAnK,QAAA,CAAAW,KAAA;cACA;;cAEA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;;cAEA;cACA;;cAGA;cACA;cACA;cACA;cACA;;cAEA;cACA;cACA;cACA;;cAEAwJ,OAAA,CAAArO,qBAAA;YACA;YACAwD,OAAA,CAAAC,GAAA,yBAAAR,QAAA;UACA,GAAA2B,KAAA,WAAAkL,GAAA;YACAtM,OAAA,CAAAqB,KAAA,oBAAAiL,GAAA;YACAzB,OAAA,CAAAnK,QAAA,CAAAW,KAAA;UACA;QAEA;UACA;QACA;MACA;IACA;IAEA;IACAkL,cAAA,WAAAA,eAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,IAAAH,IAAA,CAAAI,QAAA,QAAAC,QAAA,GAAAC,QAAA;MACA,IAAAC,GAAA,GAAAP,IAAA,CAAAQ,OAAA,GAAAH,QAAA,GAAAC,QAAA;MACA,IAAAG,KAAA,GAAAT,IAAA,CAAAU,QAAA,GAAAL,QAAA,GAAAC,QAAA;MACA,IAAAK,OAAA,GAAAX,IAAA,CAAAY,UAAA,GAAAP,QAAA,GAAAC,QAAA;MACA,IAAAO,OAAA,GAAAb,IAAA,CAAAc,UAAA,GAAAT,QAAA,GAAAC,QAAA;MAEA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IAEA;IACAG,WAAA,WAAAA,YAAA;MACA,KAAA9M,QAAA,CAAAC,OAAA;MACA;MACA;IACA;IAEA;IACA8M,MAAA,WAAAA,OAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,MAAA/N,MAAA;MACA,KAAAgB,OAAA,CAAAC,IAAA;QAAAC,IAAA;QAAAC,KAAA;UAAAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;QAAA;MAAA;IACA;IAEA;IACA0M,cAAA,WAAAA,eAAApL,GAAA;MACA,KAAA5B,OAAA,CAAAC,IAAA;QACAC,IAAA,sBAAAyM,MAAA,CAAA/K,GAAA,CAAAgG,MAAA;MACA;IACA;IAEA3F,eAAA,WAAAA,gBAAAC,QAAA;MACA,IAAA+K,WAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAA/K,QAAA;IACA;IAEAE,aAAA,WAAAA,cAAA8K,QAAA;MACA,IAAAD,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAC,QAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAAtQ,MAAA;MACA,IAAAqJ,SAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAArJ,MAAA;IACA;IAEA;AACA;AACA;AACA;IACAyE,mBAAA,WAAAA,oBAAA;MAAA,IAAA8L,OAAA;MAAA,WAAAC,kBAAA,CAAA3E,OAAA,mBAAA4E,aAAA,CAAA5E,OAAA,IAAA6E,CAAA,UAAAC,QAAA;QAAA,IAAAvO,MAAA,EAAAJ,QAAA,EAAA4O,EAAA;QAAA,WAAAH,aAAA,CAAA5E,OAAA,IAAAgF,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAEA;cACAT,OAAA,CAAApP,eAAA,CAAAwJ,KAAA;;cAEA;cACAvI,MAAA;gBACA1D,OAAA,EAAA6R,OAAA,CAAA7R;cACA;cAAAoS,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAA/F,sBAAA,EAAA5I,MAAA;YAAA;cAAAJ,QAAA,GAAA8O,QAAA,CAAAG,CAAA;cACA,IAAAjP,QAAA,CAAA4M,IAAA,YAAA5M,QAAA,CAAAwC,IAAA;gBACA;gBACAxC,QAAA,CAAAwC,IAAA,CAAA0B,OAAA,WAAAgF,QAAA;kBACA,IAAAY,GAAA,GAAAZ,QAAA,CAAAzK,UAAA;kBACA,KAAA8P,OAAA,CAAApP,eAAA,CAAAgK,GAAA,CAAAW,GAAA;oBACAyE,OAAA,CAAApP,eAAA,CAAAiK,GAAA,CAAAU,GAAA;sBACArL,UAAA,EAAAyK,QAAA,CAAAzK,UAAA;sBACAC,YAAA,EAAAwK,QAAA,CAAAxK,YAAA;sBACAC,YAAA,EAAAuK,QAAA,CAAAvK,YAAA;sBACAC,OAAA,EAAAsK,QAAA,CAAAtK,OAAA;sBACAC,OAAA;sBACAoK,aAAA;oBACA;kBACA;kBAEA,IAAAgC,YAAA,GAAAsD,OAAA,CAAApP,eAAA,CAAAoK,GAAA,CAAAO,GAAA;kBACA;kBACAmB,YAAA,CAAApM,OAAA,IAAAqK,QAAA,CAAAtK,OAAA;kBACAqM,YAAA,CAAAhC,aAAA,CAAA7H,IAAA;oBACA2H,MAAA,EAAAG,QAAA,CAAAH,MAAA;oBACA+C,MAAA,EAAA5C,QAAA,CAAA4C,MAAA;oBACAlN,OAAA,EAAAsK,QAAA,CAAAtK,OAAA;oBACAsQ,UAAA,EAAAhG,QAAA,CAAAgG;kBACA;gBACA;cACA;;cAEA;cACAX,OAAA,CAAAY,qBAAA;cAEA5O,OAAA,CAAAC,GAAA,uBAAA+N,OAAA,CAAApP,eAAA;cAAA2P,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAG,CAAA;cAEA1O,OAAA,CAAAqB,KAAA,cAAAgN,EAAA;cACAL,OAAA,CAAAtN,QAAA,CAAAW,KAAA;YAAA;cAAA,OAAAkN,QAAA,CAAAM,CAAA;UAAA;QAAA,GAAAT,OAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAQ,qBAAA,WAAAA,sBAAA;MAAA,IAAAE,OAAA;MACA,KAAA7Q,qBAAA,CAAA0F,OAAA,WAAAnB,GAAA;QACA,IAAAA,GAAA,CAAAtE,UAAA;UACA,IAAAwM,YAAA,GAAAoE,OAAA,CAAAlQ,eAAA,CAAAoK,GAAA,CAAAxG,GAAA,CAAAtE,UAAA;UACA,IAAAwM,YAAA;YACA;YACAlI,GAAA,CAAAlE,OAAA,GAAAoM,YAAA,CAAApM,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAyQ,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAf,kBAAA,CAAA3E,OAAA,mBAAA4E,aAAA,CAAA5E,OAAA,IAAA6E,CAAA,UAAAc,SAAA;QAAA,IAAAC,eAAA,EAAAC,GAAA;QAAA,WAAAjB,aAAA,CAAA5E,OAAA,IAAAgF,CAAA,WAAAc,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,CAAA;YAAA;cAAAY,SAAA,CAAAX,CAAA;cAAA,MAGAO,OAAA,CAAAvS,YAAA,IAAAuS,OAAA,CAAAvS,YAAA,CAAAoF,MAAA;gBAAAuN,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cACAU,eAAA,GAAAF,OAAA,CAAAvS,YAAA,CAAA6G,MAAA,WAAAuE,IAAA;gBAAA,OAAAA,IAAA,CAAA5E,UAAA;cAAA;cAAA,MACAiM,eAAA,CAAArN,MAAA;gBAAAuN,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cACAQ,OAAA,CAAAtO,QAAA,CAAAW,KAAA;cAAA,OAAA+N,SAAA,CAAAP,CAAA;YAAA;cAAAO,SAAA,CAAAZ,CAAA;cAAA,OAMA,IAAAa,qBAAA;gBAAAlT,OAAA,EAAA6S,OAAA,CAAA7S;cAAA;YAAA;cACA6S,OAAA,CAAAtO,QAAA,CAAAC,OAAA;cACA;cACAqO,OAAA,CAAA7O,eAAA;cACA;cACA,IAAAL,gBAAA,EAAAkP,OAAA,CAAA7S,OAAA,EAAAqD,IAAA,WAAAC,QAAA;gBACAuP,OAAA,CAAAlR,QAAA,GAAA2B,QAAA,CAAA1E,IAAA;cACA;cAAAqU,SAAA,CAAAZ,CAAA;cAAA;YAAA;cAAAY,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAAV,CAAA;cAEAM,OAAA,CAAAtO,QAAA,CAAAW,KAAA;YAAA;cAAA,OAAA+N,SAAA,CAAAP,CAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;EAEA;AACA", "ignoreList": []}]}