{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue", "mtime": 1756200516023}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_plan", "require", "_task", "_driver", "_sortablejs", "name", "data", "validateCarNumber", "rule", "value", "callback", "pattern", "test", "Error", "validatePhone", "validateIdCard", "isTaskTypeEdit", "vehicleEmissionStandardsOptions", "taskTypeOptions", "carList", "searchCarQuery", "filteredCarOptions", "driverList", "searchDriverQuery", "filteredDriverOptions", "approveForm", "applyNo", "approve<PERSON>ontent", "approveFlag", "imageList", "fileList", "dispatchDialogVisible", "taskListInfo", "dispatchForm", "dispatchRules", "carNumber", "required", "message", "trigger", "validator", "<PERSON><PERSON><PERSON>", "min", "max", "driverPhone", "driverIdCard", "dispatchList", "id", "dispatchTime", "status", "tareWeight", "grossWeight", "recheckedGrossWeight", "recheckedTareWeight", "planInfo", "taskQueryParams", "taskMaterialList", "materialSelectionList", "materialId", "materialName", "materialSpec", "planNum", "usedNum", "remainingNum", "currentNum", "availableMaterials", "taskMaterialListMap", "Map", "taskMaterialMap", "computed", "canDispatchCar", "displayDriverListOptions", "slice", "displayCarListOptions", "canShowMaterialConfirm", "includes", "planStatus", "activated", "_this", "getDicts", "then", "response", "$route", "params", "detailPlan", "taskTypeEditUpdate", "console", "log", "parseImageAndFileData", "getListTaskInfo", "getDriverList", "getCarList", "methods", "handleApprove", "_this2", "approve", "$message", "success", "$router", "push", "path", "query", "t", "Date", "now", "refresh", "catch", "error", "handleReject", "_this3", "handleDiscard", "_this4", "discard", "window", "history", "length", "go", "planType", "updateTaskTypeOptions", "_this5", "options", "filter", "option", "dict<PERSON><PERSON>ue", "_this6", "listAllTask", "rows", "getAllTaskMaterials", "openNewDriverWindow", "newWindowUrl", "open", "openNewCarWindow", "vehicleEmissionStandardsFormat", "row", "column", "selectDictLabel", "vehicleEmissionStandards", "taskTypeFormat", "getTaskTypeText", "taskType", "taskStatusFormat", "getStatusText", "taskStatus", "_this7", "loading", "getXctgDriverCarListByPage", "filterCarData", "_this8", "searchParams", "handleDriverChange", "_this9", "driverId", "for<PERSON>ach", "item", "idCard", "company", "phone", "photo", "faceImgList", "driverLicenseImgs", "vehicleLicenseImgs", "sex", "gender", "handleCarChange", "_this0", "<PERSON><PERSON><PERSON><PERSON>", "licensePlateColor", "carId", "trailerNumber", "trailerId", "axisType", "driverWeight", "maxWeight", "engineNumber", "vinNumber", "_this1", "getXctgDriverUserListByPage", "filterDriverData", "_this10", "searchValue", "applyImgUrl", "JSON", "parse", "e", "applyFileUrl", "downloadFile", "url", "fileName", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "getPlanTypeText", "type", "typeMap", "getBusinessCategoryText", "category", "categoryMap", "getMaterialTypeText", "getPlanStatusText", "statusMap", "getLogColor", "logTypeColorMap", "logType", "getDispatchStatusText", "getDispatchStatusType", "getPlanTypeTagType", "getMaterialTypeTagType", "getBusinessCategoryTagType", "openDispatchDialog", "materials", "getTaskMaterialListAndInitSelection", "measureFlag", "hasType1Task", "some", "task", "warning", "roles", "$store", "getters", "businessCategory", "_this11", "clear", "type2List", "map", "mat", "taskNo", "listTaskMaterial", "taskMaterials", "material", "has", "set", "taskMaterialInfo", "existingMaterial", "get", "Array", "from", "_ref", "_ref2", "_slicedToArray2", "default", "key", "_objectSpread2", "_this11$taskMaterialL", "Math", "resetDispatchForm", "$refs", "resetFields", "getTaskMaterialList", "_this12", "_ref3", "_ref4", "addMaterialRow", "removeMaterial", "index", "splice", "handleMaterialChange", "selectedMaterial", "find", "selectPlanMaterial", "getMaxAvailableNum", "materialInfo", "isMaterialAvailable", "submitDispatchForm", "_this13", "validate", "valid", "resultList", "selRow", "planMaterial", "newItem", "hasInvalidMaterial", "isDirectSupply", "planNo", "carNum", "companyName", "driverLicenseImg", "mobilePhone", "faceImg", "drivingLicenseImg", "idCardNo", "dispatchInfo", "isAllowDispatch", "param", "leaveTask", "leaveTaskMaterialList", "addTaskAndMaterialAndAddLeaveLog", "res", "code", "err", "formatDateTime", "date", "year", "getFullYear", "month", "getMonth", "toString", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "handlePrint", "cancel", "$tab", "closeOpenPage", "goToTaskDetail", "standardMap", "standard", "getPlanStatusType", "_this14", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "v", "createTime", "updateMaterialUsedNum", "a", "_this15", "handleMaterialConfirm", "_this16", "_callee2", "unfinishedTasks", "_t2", "_context2", "confirmMaterial"], "sources": ["src/views/leave/plan/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <h3>申请详情</h3>\r\n      </div>\r\n\r\n      <!-- 基本信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">基本信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"申请编号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 申请编号</template>\r\n            {{ planInfo.applyNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划号</template>\r\n            {{ planInfo.planNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划状态\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-flag\"></i> 计划状态</template>\r\n            <el-tag :type=\"getPlanStatusType(planInfo.planStatus)\">{{ getPlanStatusText(planInfo.planStatus) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"计划类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-order\"></i> 计划类型</template>\r\n            <el-tag :type=\"getPlanTypeTagType(planInfo.planType)\">{{ getPlanTypeText(planInfo.planType) }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"业务类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-management\"></i> 业务类型</template>\r\n            <el-tag :type=\"getBusinessCategoryTagType(planInfo.businessCategory)\">{{\r\n              getBusinessCategoryText(planInfo.businessCategory) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"是否计量\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-operation\"></i> 是否计量</template>\r\n            <el-tag :type=\"planInfo.measureFlag === 1 ? 'success' : 'danger'\">\r\n              {{ planInfo.measureFlag === 1 ? '计量' : '不计量' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.plannedAmount\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划量（吨）</template>\r\n            {{ planInfo.plannedAmount }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"是否复审\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-check\"></i> 是否复审</template>\r\n            <el-tag :type=\"planInfo.secApproveFlag === 1 ? 'warning' : 'info'\">\r\n              {{ planInfo.secApproveFlag === 1 ? '是' : '否' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请单位\" v-if=\"planInfo.sourceCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 申请单位</template>\r\n            {{ planInfo.sourceCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"收货单位\" v-if=\"planInfo.receiveCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-school\"></i> 收货单位</template>\r\n            {{ planInfo.receiveCompany }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"返回单位\" v-if=\"planInfo.targetCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-home\"></i> 返回单位</template>\r\n            {{ planInfo.targetCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划返回时间\" v-if=\"planInfo.planReturnTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-time\"></i> 计划返回时间</template>\r\n            {{ planInfo.planReturnTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"退货单位\" v-if=\"planInfo.refundDepartment\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-shop\"></i> 退货单位</template>\r\n            {{ planInfo.refundDepartment }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"开始时间\" v-if=\"planInfo.startTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 开始时间</template>\r\n            {{ planInfo.startTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"结束时间\" v-if=\"planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 结束时间</template>\r\n            {{ planInfo.endTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"有效期\" v-if=\"!planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 有效期</template>\r\n            {{ planInfo.expireTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"监装人\" v-if=\"planInfo.monitor\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 监装人</template>\r\n            {{ planInfo.monitor }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"物资专管员\" v-if=\"planInfo.specialManager\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-custom\"></i> 物资专管员</template>\r\n            {{ planInfo.specialManager }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"物资类型\" v-if=\"planInfo.itemType\">\r\n            <template slot=\"label\"><i class=\"el-icon-goods\"></i> 物资类型</template>\r\n            <el-tag :type=\"getMaterialTypeTagType(planInfo.itemType)\">\r\n              {{ getMaterialTypeText(planInfo.itemType) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"出厂原因\" v-if=\"planInfo.reason\">\r\n            <template slot=\"label\"><i class=\"el-icon-info\"></i> 出厂原因</template>\r\n            {{ planInfo.reason }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"合同号\" v-if=\"planInfo.contractNo\">\r\n            <template slot=\"label\"><i class=\"el-icon-tickets\"></i> 合同号</template>\r\n            {{ planInfo.contractNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"申请时间\" v-if=\"planInfo.applyTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-timer\"></i> 申请时间</template>\r\n            {{ planInfo.applyTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请人\" v-if=\"planInfo.applyUserName\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 申请人</template>\r\n            {{ planInfo.applyUserName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.planned_amount\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 计划量(吨)</template>\r\n            {{ planInfo.planned_amount }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 新增图片列表部分 -->\r\n      <div class=\"section-container\" v-if=\"imageList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-picture-outline\"></i> 申请图片</span>\r\n        </div>\r\n        <div class=\"image-container\">\r\n          <viewer :images=\"imageList\">\r\n            <div class=\"image-list\">\r\n              <div class=\"image-item\" v-for=\"(image, index) in imageList\" :key=\"'img-' + index\">\r\n                <img :src=\"image.url\" :alt=\"image.name\">\r\n                <div class=\"image-name\">{{ image.name }}</div>\r\n              </div>\r\n            </div>\r\n          </viewer>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 新增文件列表部分 -->\r\n      <div class=\"section-container\" v-if=\"fileList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-document\"></i> 申请附件</span>\r\n        </div>\r\n        <div class=\"file-container\">\r\n          <div class=\"file-list\">\r\n            <div class=\"file-item\" v-for=\"(file, index) in fileList\" :key=\"'file-' + index\"\r\n              @click=\"downloadFile(file.url, file.name)\">\r\n              <i class=\"el-icon-document file-icon\"></i>\r\n              <div class=\"file-name\">{{ file.name }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"planInfo.materials\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\" width=\"150\">\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 派车信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">\r\n          <span>派车信息</span>\r\n          <el-button type=\"primary\" size=\"small\" icon=\"el-icon-truck\" @click=\"openDispatchDialog\"\r\n            :disabled=\"!canDispatchCar\" class=\"dispatch-btn\">\r\n            派车\r\n          </el-button>\r\n        </div>\r\n\r\n        <el-table v-if=\"taskListInfo.length > 0\" :data=\"taskListInfo\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"carNum\" label=\"车牌号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"driverName\" label=\"司机姓名\" width=\"100\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"mobilePhone\" label=\"司机手机号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\" :formatter=\"taskTypeFormat\">\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0 \">\r\n          </el-table-column>\r\n          <el-table-column prop=\"factoryReceiveNum\" label=\"分厂确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"计量单位\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.tare || '-' }} {{ scope.row.tare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.gross || '-' }} {{ scope.row.gross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secTare || '-' }} {{ scope.row.secTare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secGross || '-' }} {{ scope.row.secGross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n\r\n\r\n          <el-table-column prop=\"createTime\" label=\"派车时间\" width=\"160\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskStatus\" label=\"任务状态\" width=\"120\" :formatter=\"taskStatusFormat\">\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"goToTaskDetail(scope.row)\">\r\n                任务详情\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <div v-else class=\"empty-data\">\r\n          <el-empty description=\"暂无派车记录\"></el-empty>\r\n        </div>\r\n\r\n        <!-- 物资确认按钮 -->\r\n        <div style=\"text-align: right; margin-top: 15px;\">\r\n          <el-button type=\"primary\" icon=\"el-icon-finished\" @click=\"handleMaterialConfirm\">\r\n            物资确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <!-- v-if=\"canShowMaterialConfirm\" -->\r\n\r\n      <!-- 审核内容部分 -->\r\n      <div class=\"section-container\" v-if=\"planInfo.approveButtonShow\">\r\n        <div class=\"section-title\">审核内容</div>\r\n        <el-form label-width=\"80px\" :model=\"approveForm\" ref=\"approveForm\">\r\n          <el-form-item label=\"审核建议\">\r\n            <el-input type=\"textarea\" v-model=\"approveForm.approveContent\" :rows=\"4\" placeholder=\"请输入审核建议\"\r\n              maxlength=\"200\" show-word-limit></el-input>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px; margin-bottom: 10px;\">审核建议可不填，默认通过为同意，驳回为拒绝\r\n            </div>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">日志列表</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in planInfo.leaveLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <!-- 固定底部操作栏 -->\r\n      <div class=\"fixed-bottom-action\">\r\n        <el-row :gutter=\"10\" type=\"flex\" justify=\"center\" align=\"middle\">\r\n          <!-- 返回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\">\r\n            <el-button size=\"medium\" @click=\"cancel\">返回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 通过按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.approveButtonShow\">\r\n            <el-button size=\"medium\" type=\"primary\" icon=\"el-icon-check\" @click=\"handleApprove\">通过</el-button>\r\n          </el-col>\r\n\r\n          <!-- 驳回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.rejectButtonShow\">\r\n            <el-button size=\"medium\" type=\"danger\" icon=\"el-icon-close\" @click=\"handleReject\">驳回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 废弃按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.discardButtonShow\">\r\n            <el-button size=\"medium\" type=\"success\" icon=\"el-icon-delete\" @click=\"handleDiscard\">废弃</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n    </el-card>\r\n\r\n    <!-- 派车弹框 -->\r\n    <el-dialog title=\"派车\" :visible.sync=\"dispatchDialogVisible\" width=\"1200px\" append-to-body destroy-on-close\r\n      @closed=\"resetDispatchForm\">\r\n      <el-form ref=\"dispatchForm\" :model=\"dispatchForm\" :rules=\"dispatchRules\" label-width=\"100px\"\r\n        class=\"dispatch-form\">\r\n\r\n        <el-form-item label=\"货车司机\" prop=\"driverId\" :rules=\"[{ required: true, message: '司机信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.driverId\" filterable :filter-method=\"filterDriverData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleDriverChange\">\r\n            <el-option v-for=\"item in filteredDriverOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.driverInfo\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewDriverWindow\">前往新增或修改司机信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"司机名称\" prop=\"name\" v-if=\"dispatchForm.name != null\">\r\n          <el-input v-model=\"dispatchForm.name\" placeholder=\"请输入司机名称\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\" v-if=\"dispatchForm.phone != null\">\r\n          <el-input v-model=\"dispatchForm.phone\" placeholder=\"请输入手机号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idCard\" v-if=\"dispatchForm.idCard != null\">\r\n          <el-input v-model=\"dispatchForm.idCard\" placeholder=\"请输入身份证号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"人脸照片\" prop=\"faceImg\" v-if=\"dispatchForm.photo != null && dispatchForm.photo != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.faceImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n                      lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.photo\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"驾驶证照片\" prop=\"driverLicenseImgs\"\r\n          v-if=\"dispatchForm.driverLicenseImgs != null && dispatchForm.driverLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.drivingLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.driverLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"行驶证照片\" prop=\"vehicleLicenseImgs\"\r\n          v-if=\"dispatchForm.vehicleLicenseImgs != null && dispatchForm.vehicleLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.driverLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.vehicleLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"货车\" prop=\"carUUId\" :rules=\"[{ required: true, message: '货车信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.carUUId\" filterable :filter-method=\"filterCarData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleCarChange\">\r\n            <el-option v-for=\"item in filteredCarOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.carNumber\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewCarWindow\">前往新增或修改货车信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车牌号\" prop=\"carNumber\" v-if=\"dispatchForm.carNumber != null\">\r\n          <el-input v-model=\"dispatchForm.carNumber\" placeholder=\"请输入车牌号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"车辆排放标准\" prop=\"vehicleEmissionStandards\"\r\n          v-if=\"dispatchForm.vehicleEmissionStandards != null\">\r\n          <el-select v-model=\"dispatchForm.vehicleEmissionStandards\" placeholder=\"请选择车辆排放标准\" disabled\r\n            style=\"width:300px\">\r\n            <el-option v-for=\"dict in vehicleEmissionStandardsOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"任务类型\" prop=\"taskType\" :rules=\"[{ required: true, message: '任务类型不能为空' }]\"\r\n          v-if=\"isTaskTypeEdit == true\">\r\n          <el-select v-model=\"dispatchForm.taskType\" placeholder=\"请选择车任务类型\" style=\"width:300px\">\r\n            <el-option v-for=\"dict in taskTypeOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 新增物资选择表格 -->\r\n        <el-form-item label=\"物资选择\" prop=\"selectedMaterials\"\r\n          v-if=\"planInfo.measureFlag == 0 && dispatchForm.taskType == 2\">\r\n          <el-table :data=\"materialSelectionList\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" width=\"50\" label=\"序号\"></el-table-column>\r\n            <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-select v-model=\"scope.row.materialId\" placeholder=\"请选择物资\"\r\n                  @change=\"handleMaterialChange(scope.row, scope.$index)\">\r\n                  <el-option v-for=\"item in availableMaterials\" :key=\"item.materialId\" :label=\"item.materialName\"\r\n                    :value=\"item.materialId\" :disabled=\"isMaterialAvailable(item)\">\r\n\r\n                  </el-option>\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"materialSpec\" label=\"物资规格\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.materialSpec }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.planNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remainingNum\" label=\"剩余数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.remainingNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"currentNum\" label=\"本次数量\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number v-model=\"scope.row.currentNum\" :min=\"0\" :max=\"getMaxAvailableNum(scope.row)\"\r\n                  @change=\"handleNumChange($event, scope.$index)\" :disabled=\"!scope.row.materialId\">\r\n                </el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"removeMaterial(scope.$index)\"\r\n                  :disabled=\"materialSelectionList.length === 1\">\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <div style=\"margin-top: 10px;\">\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addMaterialRow\">添加物资</el-button>\r\n          </div>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dispatchDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitDispatchForm\">确 认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { detailPlan, approve, discard, listTaskMaterial, confirmMaterial } from \"@/api/leave/plan\";\r\nimport { listAllTask, addTask, addTaskMaterial, addTaskAndMaterial, addLeaveLog, isAllowDispatch, addTaskAndMaterialAndAddLeaveLog } from \"@/api/leave/task\";\r\nimport { listAllDriver,  getXctgDriverUserListByPage, getXctgDriverCarListByPage } from \"@/api/dgcb/driver/driver\";\r\nimport { mount } from \"sortablejs\";\r\nexport default {\r\n  name: \"DetailLeavePlan\",\r\n  data() {\r\n    // 验证车牌号\r\n    const validateCarNumber = (rule, value, callback) => {\r\n      const pattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的车牌号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证手机号\r\n    const validatePhone = (rule, value, callback) => {\r\n      const pattern = /^1[3-9]\\d{9}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的手机号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证身份证号\r\n    const validateIdCard = (rule, value, callback) => {\r\n      const pattern = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的身份证号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      isTaskTypeEdit: true,\r\n      vehicleEmissionStandardsOptions: [],\r\n      taskTypeOptions: [],\r\n      carList: [],\r\n      searchCarQuery: '',\r\n      filteredCarOptions: [],\r\n      driverList: [],\r\n      searchDriverQuery: '',\r\n      filteredDriverOptions: [],\r\n      //审核表单\r\n      approveForm: {\r\n        applyNo: null,\r\n        approveContent: '',//审核意见\r\n        approveFlag: true,//审核状态\r\n      },\r\n\r\n      // 图片列表\r\n      imageList: [],\r\n\r\n      // 文件列表\r\n      fileList: [],\r\n\r\n      // 派车弹框可见性\r\n      dispatchDialogVisible: false,\r\n\r\n      taskListInfo: [],\r\n\r\n      // 派车表单数据\r\n      dispatchForm: {\r\n        // carNumber: '',\r\n        // driverName: '',\r\n        // driverPhone: '',\r\n        // driverIdCard: ''\r\n      },\r\n\r\n      // 派车表单验证规则\r\n      dispatchRules: {\r\n        carNumber: [\r\n          { required: true, message: '请输入车牌号', trigger: 'blur' },\r\n          { validator: validateCarNumber, trigger: 'blur' }\r\n        ],\r\n        driverName: [\r\n          { required: true, message: '请输入司机姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }\r\n        ],\r\n        driverPhone: [\r\n          { required: true, message: '请输入司机手机号', trigger: 'blur' },\r\n          { validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        driverIdCard: [\r\n          { required: true, message: '请输入司机身份证号', trigger: 'blur' },\r\n          { validator: validateIdCard, trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 派车列表数据\r\n      dispatchList: [\r\n        {\r\n          id: 1,\r\n          carNumber: '京A12345',\r\n          driverName: '王小明',\r\n          driverPhone: '13800138000',\r\n          driverIdCard: '110101199001010001',\r\n          dispatchTime: '2025-03-18 09:30:00',\r\n          status: 2,\r\n          tareWeight: 8500,\r\n          grossWeight: 15800,\r\n          recheckedGrossWeight: 15750,\r\n          recheckedTareWeight: 8480\r\n        },\r\n        {\r\n          id: 2,\r\n          carNumber: '京B98765',\r\n          driverName: '李大壮',\r\n          driverPhone: '13900139000',\r\n          driverIdCard: '110101199102020002',\r\n          dispatchTime: '2025-03-19 14:15:00',\r\n          status: 1,\r\n          tareWeight: 7800,\r\n          grossWeight: 12600,\r\n          recheckedGrossWeight: null,\r\n          recheckedTareWeight: null\r\n        }\r\n      ],\r\n\r\n      // 计划详情信息\r\n      planInfo: {},\r\n      applyNo: null,\r\n      taskQueryParams: {\r\n        applyNo: null,\r\n      },\r\n\r\n      taskMaterialList: null,\r\n      // 物资选择相关数据\r\n      materialSelectionList: [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        usedNum: 0,\r\n        remainingNum: 0,\r\n        currentNum: 0\r\n      }],\r\n      availableMaterials: [], // 可选的物资列表\r\n      taskMaterialListMap: new Map(), // 已派车的物资列表\r\n      taskMaterialMap: new Map(), // 存储所有任务物资的映射\r\n    };\r\n  },\r\n  computed: {\r\n    // 判断是否可以派车\r\n    canDispatchCar() {\r\n      // 判断申请单是否已通过\r\n      // const isPlanApproved = this.planInfo.planStatus === 2;\r\n\r\n      // // 如果是非计量类型，且已经派过车，则不能再派车\r\n      // if (this.planInfo.measureFlag !== 1 && this.dispatchList.length > 0) {\r\n      //   return false;\r\n      // }\r\n\r\n      return true;\r\n    },\r\n    // 默认显示前50条，若有搜索，则显示搜索后的数据\r\n    displayDriverListOptions() {\r\n      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);\r\n    },\r\n    displayCarListOptions() {\r\n      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);\r\n    },\r\n    canShowMaterialConfirm() {\r\n      // 只有planStatus为5或6时显示（已出厂/部分收货），且不是已完成/废弃/驳回/过期\r\n      return [5, 6].includes(this.planInfo.planStatus);\r\n    }\r\n  },\r\n  activated() {\r\n    this.getDicts(\"xctg_driver_car_emission_standards\").then(response => {\r\n      this.vehicleEmissionStandardsOptions = response.data;\r\n    });\r\n    // 初始化任务类型选项（在获取计划信息后会重新更新）\r\n    this.getDicts(\"leave_task_type\").then(response => {\r\n      this.taskTypeOptions = response.data;\r\n    });\r\n    // 获取路由参数中的ID\r\n    const applyNo = this.$route.params.applyNo;\r\n    this.applyNo = applyNo\r\n    this.taskQueryParams.applyNo = applyNo;\r\n    this.approveForm.applyNo = applyNo;\r\n    if (applyNo) {\r\n      detailPlan(applyNo).then(response => {\r\n        this.planInfo = response.data;\r\n        this.taskTypeEditUpdate();\r\n        console.log(\"this.planInfo\", this.planInfo);\r\n        // 解析图片和文件数据\r\n        this.parseImageAndFileData();\r\n        // 获取任务信息后更新任务类型选项\r\n        this.getListTaskInfo();\r\n      });\r\n    };\r\n    this.getDriverList();\r\n    this.getCarList();\r\n\r\n\r\n\r\n  },\r\n\r\n\r\n  methods: {\r\n    handleApprove() {\r\n      this.approveForm.approveFlag = true;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('审核通过');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleReject() {\r\n      this.approveForm.approveFlag = false;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('驳回成功');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleDiscard() {\r\n      discard(this.planInfo).then(response => {\r\n        this.$message.success('废弃成功');\r\n\r\n        if (window.history.length > 1) {\r\n          this.$router.go(-1);\r\n        } else {\r\n          this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('废弃失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    taskTypeEditUpdate() {\r\n      if (this.planInfo.planType !== 2) {\r\n        this.isTaskTypeEdit = false;\r\n      }\r\n    },\r\n    // 更新任务类型选项\r\n    updateTaskTypeOptions() {\r\n      // 获取原始的任务类型选项\r\n      this.getDicts(\"leave_task_type\").then(response => {\r\n        let options = response.data;\r\n\r\n        // 对于出厂返回任务（planType=2）\r\n        if (this.planInfo.planType === 2) {\r\n          // 如果当前任务数为0，只显示出厂选项\r\n          if (this.taskListInfo.length === 0) {\r\n            options = options.filter(option => option.dictValue === '1'); // 只保留出厂选项\r\n          }\r\n          // 如果已有任务，显示所有选项（出厂和返厂）\r\n        } else {\r\n          // 对于其他计划类型，保持原有逻辑\r\n          if (this.planInfo.planType !== 3) {\r\n            options = options.filter(option => option.dictValue !== '3'); // 移除跨区调拨选项\r\n          }\r\n        }\r\n\r\n        this.taskTypeOptions = options;\r\n      });\r\n    },\r\n    getListTaskInfo() {\r\n      listAllTask(this.taskQueryParams).then(response => {\r\n        console.log(\"response.data\", response.rows);\r\n        this.taskListInfo = response.data;\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        // 获取所有任务物资\r\n        this.getAllTaskMaterials();\r\n        // 更新任务类型选项（基于当前任务数）\r\n        this.updateTaskTypeOptions();\r\n      });\r\n    },\r\n    openNewDriverWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    openNewCarWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    // 1国五，2国六，3新能源字典翻译\r\n    vehicleEmissionStandardsFormat(row, column) {\r\n      return this.selectDictLabel(this.vehicleEmissionStandardsOptions, row.vehicleEmissionStandards);\r\n    },\r\n\r\n    taskTypeFormat(row, column) {\r\n      return this.getTaskTypeText(row.taskType);\r\n    },\r\n    taskStatusFormat(row, column) {\r\n      return this.getStatusText(row.taskStatus);\r\n    },\r\n\r\n    /** 查询司机信息列表 */\r\n    getCarList() {\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverCarListByPage().then(response => {\r\n        this.carList = response.rows;\r\n        this.filteredCarOptions = this.carList;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterCarData(query) {\r\n      this.searchCarQuery = query;\r\n\r\n      if (this.searchCarQuery) {\r\n        // 调用后端接口进行搜索\r\n        const searchParams = {\r\n          carNumber: query\r\n        };\r\n        getXctgDriverCarListByPage(searchParams).then(response => {\r\n          this.filteredCarOptions = response.rows || [];\r\n        }).catch(error => {\r\n          console.error('搜索货车失败:', error);\r\n          this.filteredCarOptions = [];\r\n        });\r\n      } else {\r\n        // 如果没有搜索条件，显示前50条数据\r\n        this.filteredCarOptions = this.carList.slice(0, 50);\r\n      }\r\n    },\r\n    //通过driverId获取司机信息\r\n    handleDriverChange() {\r\n      if (this.dispatchForm.driverId != null) {\r\n        this.driverList.forEach(item => {\r\n          if (item.id == this.dispatchForm.driverId) {\r\n            this.dispatchForm.name = item.name;\r\n            this.dispatchForm.idCard = item.idCard;\r\n            this.dispatchForm.company = item.company;\r\n            this.dispatchForm.phone = item.phone;\r\n            this.dispatchForm.photo = item.photo;\r\n            this.dispatchForm.faceImgList = item.faceImgList;\r\n            this.dispatchForm.driverLicenseImgs = item.driverLicenseImgs;\r\n            this.dispatchForm.vehicleLicenseImgs = item.vehicleLicenseImgs;\r\n            this.dispatchForm.sex = item.gender;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n    //通过driverId获取司机信息\r\n    handleCarChange() {\r\n      console.log(\"handleCarChange\")\r\n      if (this.dispatchForm.carUUId != null) {\r\n        this.carList.forEach(item => {\r\n          if (item.id == this.dispatchForm.carUUId) {\r\n            this.dispatchForm.carNumber = item.carNumber;\r\n\r\n            if (item.vehicleEmissionStandards == 1) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"国五\";\r\n            } else if (item.vehicleEmissionStandards == 2) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"国六\";\r\n            } else if (item.vehicleEmissionStandards == 3) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"新能源\";\r\n            } else {\r\n              this.dispatchForm.vehicleEmissionStandards = \"\";\r\n            }\r\n            this.dispatchForm.licensePlateColor = item.licensePlateColor;\r\n            this.dispatchForm.carId = item.carId;\r\n            this.dispatchForm.trailerNumber = item.trailerNumber;\r\n            this.dispatchForm.trailerId = item.trailerId;\r\n            this.dispatchForm.axisType = item.axisType;\r\n            this.dispatchForm.driverWeight = item.driverWeight;\r\n            this.dispatchForm.maxWeight = item.maxWeight;\r\n            this.dispatchForm.engineNumber = item.engineNumber;\r\n            this.dispatchForm.vinNumber = item.vinNumber;\r\n          }\r\n\r\n        });\r\n      }\r\n    },\r\n    /** 查询司机信息列表 */\r\n    getDriverList() {\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverUserListByPage().then(response => {\r\n        this.driverList = response.rows;\r\n        console.log(\"this.driverList\", this.driverList);\r\n        this.filteredDriverOptions = this.driverList;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterDriverData(query) {\r\n      this.searchDriverQuery = query;\r\n\r\n      if (this.searchDriverQuery) {\r\n        // 调用后端接口进行搜索\r\n        const searchParams = {\r\n          searchValue: query\r\n        };\r\n        getXctgDriverUserListByPage(searchParams).then(response => {\r\n          this.filteredDriverOptions = response.rows || [];\r\n        }).catch(error => {\r\n          console.error('搜索货车司机失败:', error);\r\n          this.filteredDriverOptions = [];\r\n        });\r\n      } else {\r\n        // 如果没有搜索条件，显示前50条数据\r\n        this.filteredDriverOptions = this.driverList.slice(0, 50);\r\n      }\r\n    },\r\n    // 解析图片和文件数据\r\n    parseImageAndFileData() {\r\n      // 解析图片数据\r\n      if (this.planInfo.applyImgUrl) {\r\n        try {\r\n          this.imageList = JSON.parse(this.planInfo.applyImgUrl);\r\n        } catch (e) {\r\n          console.error('解析图片数据失败:', e);\r\n          this.imageList = [];\r\n        }\r\n      }\r\n\r\n      // 解析文件数据\r\n      if (this.planInfo.applyFileUrl) {\r\n        try {\r\n          this.fileList = JSON.parse(this.planInfo.applyFileUrl);\r\n        } catch (e) {\r\n          console.error('解析文件数据失败:', e);\r\n          this.fileList = [];\r\n        }\r\n      }\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(url, fileName) {\r\n      if (!url) {\r\n        this.$message.error('文件链接无效');\r\n        return;\r\n      }\r\n\r\n      // 创建一个a元素用于下载\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName || '下载文件';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    // 获取计划类型文本\r\n    getPlanTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂不返回',\r\n        2: '出厂返回',\r\n        3: '跨区调拨',\r\n        4: '退货申请'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取业务类型文本\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用',\r\n        11: '通用',\r\n        12: '委外加工',\r\n        21: '有计划量计量',\r\n        22: '短期',\r\n        23: '钢板（圆钢）',\r\n        31: '通用'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n\r\n    // 获取物资类型文本\r\n    getMaterialTypeText(type) {\r\n      const typeMap = {\r\n        1: '钢材',\r\n        2: '钢板',\r\n        3: '其他'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取计划状态文本\r\n    getPlanStatusText(status) {\r\n      const statusMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#67C23A', // 审批\r\n        3: '#E6A23C', // 流转\r\n        4: '#F56C6C', // 驳回\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.logType] || '#409EFF';\r\n    },\r\n\r\n    // 获取派车状态文本\r\n    getDispatchStatusText(status) {\r\n      const statusMap = {\r\n        0: '待出发',\r\n        1: '已出发',\r\n        2: '已到达',\r\n        3: '已完成',\r\n        4: '已取消'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取派车状态类型（用于标签颜色）\r\n    getDispatchStatusType(status) {\r\n      const statusMap = {\r\n        0: 'info',\r\n        1: 'primary',\r\n        2: 'success',\r\n        3: 'success',\r\n        4: 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取计划类型标签样式\r\n    getPlanTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'success',  // 出厂不返回\r\n        2: 'warning',  // 出厂返回\r\n        3: 'info',     // 跨区调拨\r\n        4: 'danger'    // 退货申请\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取物资类型标签样式\r\n    getMaterialTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'primary',  // 钢材\r\n        2: 'success',  // 钢板\r\n        3: 'info'      // 其他\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取业务类型标签样式\r\n    getBusinessCategoryTagType(category) {\r\n      const typeMap = {\r\n        '1': 'primary',   // 通用\r\n        '11': 'primary',  // 通用\r\n        '12': 'warning',  // 委外加工\r\n        '21': 'success',  // 有计划量计量\r\n        '22': 'info',     // 短期\r\n        '23': 'danger',   // 钢板（圆钢）\r\n        '31': 'primary'   // 通用\r\n      };\r\n      return typeMap[category] || 'info';\r\n    },\r\n\r\n    // 打开派车弹框\r\n    openDispatchDialog() {\r\n      // 初始化物资数据\r\n      this.availableMaterials = this.planInfo.materials || [];\r\n      console.log(\"this.availableMaterials\", this.availableMaterials);\r\n\r\n      // 获取已派车的物资列表，并在回调中初始化 materialSelectionList\r\n      this.getTaskMaterialListAndInitSelection();\r\n      // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.supplier') && !roles.includes('leave.applicant')) {\r\n        this.$message.error('您没有派车权限');\r\n        return;\r\n      }\r\n\r\n      console.log(\"this.planInfo.planStatus\", this.planInfo.planStatus);\r\n      if (![4, 5, 6].includes(this.planInfo.planStatus)) {\r\n        this.$message.warning('当前状态无法派车');\r\n        return;\r\n      }\r\n\r\n\r\n\r\n\r\n      console.log(\"openDispatchDialog\", this.taskListInfo.length);\r\n      if (this.planInfo.businessCategory == 22 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('短期计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      if (this.planInfo.businessCategory == 23 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('钢板（圆钢）计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      this.dispatchForm = {};\r\n\r\n      // 更新任务类型选项\r\n      this.updateTaskTypeOptions();\r\n\r\n      if (this.planInfo.planType == 1) {\r\n        this.dispatchForm.taskType = 1\r\n      } else if (this.planInfo.planType == 3) {\r\n        this.dispatchForm.taskType = 3\r\n      } else if (this.planInfo.planType == 4) {\r\n        this.dispatchForm.taskType = 1\r\n      } else if (this.planInfo.planType == 2) {\r\n        // 对于出厂返回任务，根据当前任务数决定默认任务类型\r\n        if (this.taskListInfo.length === 0) {\r\n          this.dispatchForm.taskType = 1; // 默认选择出厂\r\n        } else {\r\n          this.dispatchForm.taskType = 2; // 默认选择返厂\r\n        }\r\n      }\r\n      console.log(this.dispatchForm.taskType),\r\n        this.dispatchDialogVisible = true;\r\n\r\n\r\n    },\r\n\r\n    // 新增方法\r\n    getTaskMaterialListAndInitSelection() {\r\n      // 清空已用数量映射\r\n      this.taskMaterialListMap.clear();\r\n      // 统计所有已派车物资\r\n      const type2List = this.taskListInfo.filter(item => item.taskType === 2);\r\n      console.log(\"type2List\", type2List);\r\n      if (!type2List || type2List.length === 0) {\r\n        // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n        this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n          // const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n          // const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n          return {\r\n            materialId: mat.materialId,\r\n            materialName: mat.materialName,\r\n            materialSpec: mat.materialSpec,\r\n            planNum: mat.planNum,\r\n            usedNum: 0,\r\n            remainingNum: mat.planNum,\r\n            currentNum: mat.planNum\r\n          };\r\n        });\r\n      } else {\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        type2List.forEach(task => {\r\n          const params = { taskNo: task.taskNo };\r\n          listTaskMaterial(params).then(response => {\r\n            let taskMaterials = response.rows || [];\r\n            taskMaterials.forEach(material => {\r\n              if (!this.taskMaterialListMap.has(material.materialId)) {\r\n                this.taskMaterialListMap.set(material.materialId, {\r\n                  taskMaterialInfo: material,\r\n                  usedNum: material.planNum\r\n                });\r\n              } else {\r\n                const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n                existingMaterial.usedNum += material.planNum;\r\n              }\r\n            });\r\n\r\n            // 将taskMaterialListMap转换为数组集合\r\n            this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n              materialId: key,\r\n              ...value\r\n            }));\r\n\r\n            // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n            this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n              const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n              const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n\r\n              return {\r\n                materialId: mat.materialId,\r\n                materialName: mat.materialName,\r\n                materialSpec: mat.materialSpec,\r\n                planNum: mat.planNum,\r\n                usedNum: usedNum,\r\n                remainingNum: remainingNum,\r\n                currentNum: remainingNum\r\n              };\r\n            });\r\n\r\n            this.materialSelectionList = this.materialSelectionList.filter(item => item.remainingNum > 0);\r\n          });\r\n        });\r\n      }\r\n\r\n         // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n    },\r\n\r\n    // 重置派车表单\r\n    resetDispatchForm() {\r\n      this.$refs.dispatchForm && this.$refs.dispatchForm.resetFields();\r\n      this.materialSelectionList = [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      }];\r\n    },\r\n\r\n    // 获取已派车的物资列表\r\n    getTaskMaterialList() {\r\n      // 从taskListInfo中获取已派车的物资信息\r\n      this.taskMaterialListMap.clear();\r\n      this.taskListInfo.forEach(task => {\r\n        const params = {\r\n          taskNo: task.taskNo,\r\n        };\r\n        listTaskMaterial(params).then(response => {\r\n          console.log(\"listTaskMaterial\", response.rows);\r\n          let taskMaterials = [];\r\n          taskMaterials = response.rows;\r\n          taskMaterials.forEach(material => {\r\n            if (!this.taskMaterialListMap.has(material.materialId)) {\r\n              this.taskMaterialListMap.set(material.materialId, {\r\n                taskMaterialInfo: material,\r\n                usedNum: material.planNum\r\n              });\r\n            } else {\r\n              const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n              existingMaterial.usedNum += material.planNum;\r\n            }\r\n          });\r\n          // 将taskMaterialListMap转换为数组集合\r\n          this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n            materialId: key,\r\n            ...value\r\n          }));\r\n          console.log(\"taskMaterialArray\", this.taskMaterialList);\r\n          console.log(\"taskMaterialListMap\", this.taskMaterialListMap);\r\n        });\r\n      });\r\n    },\r\n\r\n    // 添加物资行\r\n    addMaterialRow() {\r\n      this.materialSelectionList.push({\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      });\r\n    },\r\n\r\n    // 移除物资行\r\n    removeMaterial(index) {\r\n      this.materialSelectionList.splice(index, 1);\r\n    },\r\n\r\n    // 处理物资选择变化\r\n    handleMaterialChange(row, index) {\r\n      console.log(\"handleMaterialChange\", this.taskMaterialList);\r\n\r\n\r\n      const selectedMaterial = this.taskMaterialList.find(item => item.materialId === row.materialId);\r\n      if (selectedMaterial) {\r\n        row.usedNum = selectedMaterial.usedNum;\r\n      }\r\n      const selectPlanMaterial = this.planInfo.materials.find(item => item.materialId === row.materialId);\r\n\r\n      if (selectPlanMaterial) {\r\n        row.planNum = selectPlanMaterial.planNum;\r\n        row.materialName = selectPlanMaterial.materialName;\r\n        row.materialSpec = selectPlanMaterial.materialSpec;\r\n      }\r\n\r\n      row.remainingNum = row.planNum - row.usedNum;\r\n      row.currentNum = row.planNum - row.usedNum;\r\n\r\n      console.log(\"handleMaterialChange\", row, index);\r\n\r\n    },\r\n\r\n    // 获取物资最大可用数量\r\n    getMaxAvailableNum(row) {\r\n      if (!row.materialId) return 0;\r\n\r\n      // 从taskMaterialListMap中获取已用数量\r\n      const materialInfo = this.taskMaterialListMap.get(row.materialId);\r\n      const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      return row.planNum - usedNum;\r\n    },\r\n\r\n    // 判断物资是否可选\r\n    isMaterialAvailable(material) {\r\n      // 从taskMaterialListMap中获取已用数量\r\n      // const materialInfo = this.taskMaterialListMap.get(material.id);\r\n      // const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      // let selected = false;\r\n\r\n      // this.availableMaterials.forEach(item => {\r\n      //   if (item.materialId === material.materialId) {\r\n      //     selected = true;\r\n      //   }\r\n      // });\r\n\r\n      return this.materialSelectionList.some(row => row.materialId === material.materialId);;\r\n    },\r\n\r\n    // 修改提交派车表单方法\r\n    submitDispatchForm() {\r\n      this.$refs.dispatchForm.validate(valid => {\r\n        if (valid) {\r\n          // 判断非计量且taskType为1的情况\r\n          if (this.planInfo.measureFlag == 0) {\r\n            if (this.dispatchForm.taskType == 1) {\r\n              // 检查是否已经有taskType为1的任务\r\n              const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n              if (hasType1Task) {\r\n                this.$message.warning('非计量只能派车出厂一次');\r\n                return;\r\n              }\r\n            }\r\n          }\r\n\r\n          // 新集合\r\n          let resultList = [];\r\n\r\n          console.log(\"this.planInfo.measureFlag\", this.planInfo.measureFlag);\r\n          console.log(\"this.dispatchForm.taskType\", this.dispatchForm.taskType);\r\n\r\n          if (this.planInfo.measureFlag == 0 && this.dispatchForm.taskType == 2) {\r\n            this.materialSelectionList.forEach(selRow => {\r\n              // 在 planInfo.materials 中查找相同 materialId 的元素\r\n              const planMaterial = (this.planInfo.materials || []).find(\r\n                mat => mat.materialId === selRow.materialId\r\n              );\r\n              if (planMaterial) {\r\n                // 深拷贝一份，避免影响原数据\r\n                const newItem = { ...planMaterial };\r\n                newItem.planNum = selRow.currentNum; // 设置为本次数量\r\n                resultList.push(newItem);\r\n              }\r\n            });\r\n\r\n            // resultList 即为你需要的新集合\r\n            console.log('this.materialSelectionList', this.materialSelectionList);\r\n            console.log('resultList', resultList);\r\n\r\n            // 物资校验：必须有物资\r\n            if (!this.materialSelectionList.length) {\r\n              this.$message.warning('请至少选择一种物资');\r\n              return;\r\n            }\r\n\r\n            // 校验每一行物资\r\n            const hasInvalidMaterial = this.materialSelectionList.some(row => {\r\n              // 必须选择物资，数量>0，且数量<=剩余数量\r\n              return (\r\n                !row.materialId ||\r\n                row.currentNum <= 0 ||\r\n                row.currentNum > row.remainingNum\r\n              );\r\n            });\r\n\r\n            if (hasInvalidMaterial) {\r\n              this.$message.warning('请选择物资且本次数量需大于0且不超过剩余数量');\r\n              return;\r\n            }\r\n          } else {\r\n            console.log(\"this.planInfo.materials\", this.planInfo.materials);\r\n            resultList = this.planInfo.materials ? this.planInfo.materials.map(item => ({ ...item })) : [];\r\n            console.log(\"123321\", resultList);\r\n          }\r\n\r\n\r\n\r\n\r\n\r\n          if (this.planInfo.measureFlag == 1 && this.dispatchForm.taskType !== 2) {\r\n            this.dispatchForm.taskStatus = 1;\r\n          } else {\r\n            this.dispatchForm.taskStatus = 4;\r\n          }\r\n\r\n          if (this.dispatchForm.taskType == 2) {\r\n            this.dispatchForm.taskStatus = 5;\r\n          }\r\n\r\n\r\n          //是否直供默认为0\r\n          this.dispatchForm.isDirectSupply = 0;\r\n          // todo 任务状态确认\r\n          this.dispatchForm.applyNo = this.applyNo;\r\n          this.dispatchForm.planNo = this.planInfo.planNo;\r\n          this.dispatchForm.carNum = this.dispatchForm.carNumber;\r\n          this.dispatchForm.companyName = this.dispatchForm.company;\r\n          this.dispatchForm.driverLicenseImg = this.dispatchForm.driverLicenseImgs;\r\n          this.dispatchForm.driverName = this.dispatchForm.name;\r\n          this.dispatchForm.mobilePhone = this.dispatchForm.phone;\r\n          this.dispatchForm.faceImg = this.dispatchForm.photo;\r\n          this.dispatchForm.drivingLicenseImg = this.dispatchForm.vehicleLicenseImgs;\r\n          this.dispatchForm.idCardNo = this.dispatchForm.idCard;\r\n          if (this.dispatchForm.sex == \"1\") {\r\n            this.dispatchForm.sex = 1;\r\n          } else if (this.dispatchForm.sex == \"2\") {\r\n            this.dispatchForm.sex = 2;\r\n          }\r\n          if (this.dispatchForm.vehicleEmissionStandards == \"国五\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 1;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"国六\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 2;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"新能源\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 3;\r\n          }\r\n          console.log(\"this.dispatchForm\", this.dispatchForm);\r\n\r\n          let dispatchInfo = {};\r\n          dispatchInfo.carNum = this.dispatchForm.carNum;\r\n\r\n          isAllowDispatch(dispatchInfo).then(response => {\r\n            let row = response.data;\r\n            if (row > 0) {\r\n              this.$message.error(\"当前车有正在执行的任务\")\r\n            } else {\r\n              let param = {};\r\n              param.leaveTask = this.dispatchForm;\r\n              param.leaveTaskMaterialList = resultList;\r\n              addTaskAndMaterialAndAddLeaveLog(param).then(res => {\r\n                console.log(\"addTaskAndMaterialAndAddLeaveLog\", res)\r\n                if (res.code == 200) {\r\n                  this.$message.success('派车成功');\r\n                  this.dispatchDialogVisible = false;\r\n                  this.getListTaskInfo();\r\n                } else {\r\n                  // 其他失败原因\r\n                  this.$message.error(res.message || '派车失败');\r\n                }\r\n              }).catch(err => {\r\n                console.error('dispatch error:', err);\r\n                this.$message.error('网络异常，稍后重试');\r\n              });\r\n\r\n              // addTaskAndMaterial(this.dispatchForm).then(response => {\r\n              //   console.log(\"addTaskAndMaterial\", response);\r\n              //   let snowId = response.data;\r\n              //   this.planInfo.materials.forEach(item => {\r\n              //     item.taskNo = snowId;\r\n              //     addTaskMaterial(item);\r\n              //   });\r\n\r\n              //   console.log(\"生成派车日志\");\r\n\r\n              //   //生成派车日志\r\n              //   let leaveTaskLog = {};\r\n\r\n\r\n              //   leaveTaskLog.logType = 2;\r\n              //   leaveTaskLog.taskNo = snowId;\r\n              //   leaveTaskLog.applyNo = this.applyNo;\r\n              //   leaveTaskLog.info = '派车任务创建：' + this.dispatchForm.carNum + ' ' + this.dispatchForm.driverName\r\n              //   addLeaveLog(leaveTaskLog);\r\n\r\n              //   this.$message.success('派车成功');\r\n              //   this.dispatchDialogVisible = false;\r\n              //   this.getListTaskInfo();\r\n              // });\r\n\r\n              this.dispatchDialogVisible = false;\r\n            }\r\n            console.log(\"this.isAllowDispatch\", response);\r\n          }).catch(err => {\r\n            console.error('dispatch error:', err);\r\n            this.$message.error('网络异常，稍后重试');\r\n          });\r\n\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(date) {\r\n      const year = date.getFullYear();\r\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const day = date.getDate().toString().padStart(2, '0');\r\n      const hours = date.getHours().toString().padStart(2, '0');\r\n      const minutes = date.getMinutes().toString().padStart(2, '0');\r\n      const seconds = date.getSeconds().toString().padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    // 打印功能\r\n    handlePrint() {\r\n      this.$message.success('打印功能尚未实现');\r\n      // 实际项目中可以调用浏览器打印功能\r\n      // window.print();\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$tab.closeOpenPage(this.$route);\r\n      this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n    },\r\n\r\n    // 跳转到任务详情页面\r\n    goToTaskDetail(row) {\r\n      this.$router.push({\r\n        path: `/leave/plan/task/${row.taskNo}`\r\n      });\r\n    },\r\n\r\n    getTaskTypeText(taskType) {\r\n      const standardMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return standardMap[taskType] || '未知';\r\n    },\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取计划状态类型\r\n    getPlanStatusType(status) {\r\n      const statusMap = {\r\n        '1': 'warning',  // 待分厂审批\r\n        '2': 'warning',  // 待分厂复审\r\n        '3': 'warning',  // 待生产指挥中心审批\r\n        '4': 'success',  // 审批完成\r\n        '5': 'primary',  // 已出厂\r\n        '6': 'info',     // 部分收货\r\n        '7': 'success',  // 已完成\r\n        '11': 'danger',  // 驳回\r\n        '12': 'danger',  // 废弃\r\n        '13': 'danger'   // 过期\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n\r\n    /**\r\n     * 获取计划下所有任务的任务物资\r\n     * @returns {Promise<void>}\r\n     */\r\n    async getAllTaskMaterials() {\r\n      try {\r\n        // 清空现有数据\r\n        this.taskMaterialMap.clear();\r\n\r\n        // 获取该计划下所有任务的任务物资\r\n        const params = {\r\n          applyNo: this.applyNo\r\n        };\r\n\r\n        const response = await listTaskMaterial(params);\r\n        if (response.code === 200 && response.rows) {\r\n          // 将任务物资按物资ID分组存储\r\n          response.rows.forEach(material => {\r\n            const key = material.materialId;\r\n            if (!this.taskMaterialMap.has(key)) {\r\n              this.taskMaterialMap.set(key, {\r\n                materialId: material.materialId,\r\n                materialName: material.materialName,\r\n                materialSpec: material.materialSpec,\r\n                planNum: material.planNum,\r\n                usedNum: 0,\r\n                taskMaterials: [] // 存储每个任务的具体物资信息\r\n              });\r\n            }\r\n\r\n            const materialInfo = this.taskMaterialMap.get(key);\r\n            // 累加每个任务物资的计划数量作为已使用数量\r\n            materialInfo.usedNum += material.planNum;\r\n            materialInfo.taskMaterials.push({\r\n              taskNo: material.taskNo,\r\n              carNum: material.carNum,\r\n              planNum: material.planNum,\r\n              createTime: material.createTime\r\n            });\r\n          });\r\n        }\r\n\r\n        // 更新物资选择列表中的已使用数量\r\n        this.updateMaterialUsedNum();\r\n\r\n        console.log('Task Material Map:', this.taskMaterialMap);\r\n      } catch (error) {\r\n        console.error('获取任务物资失败:', error);\r\n        this.$message.error('获取任务物资失败');\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 更新物资选择列表中的已使用数量\r\n     */\r\n    updateMaterialUsedNum() {\r\n      this.materialSelectionList.forEach(row => {\r\n        if (row.materialId) {\r\n          const materialInfo = this.taskMaterialMap.get(row.materialId);\r\n          if (materialInfo) {\r\n            // 直接使用累加的计划数量作为已使用数量\r\n            row.usedNum = materialInfo.usedNum;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 物资确认按钮点击事件\r\n    async handleMaterialConfirm() {\r\n      try {\r\n        // 校验所有任务的taskStatus是否为9\r\n        if (this.taskListInfo && this.taskListInfo.length > 0) {\r\n          const unfinishedTasks = this.taskListInfo.filter(task => task.taskStatus !== 9);\r\n          if (unfinishedTasks.length > 0) {\r\n            this.$message.error('存在未完成的任务，无法进行物资确认');\r\n            return;\r\n          }\r\n        }\r\n\r\n        // 调用后端接口，传递applyNo\r\n        await confirmMaterial({ applyNo: this.applyNo });\r\n        this.$message.success('物资确认成功');\r\n        // 刷新详情\r\n        this.getListTaskInfo();\r\n        // 重新获取planInfo\r\n        detailPlan(this.applyNo).then(response => {\r\n          this.planInfo = response.data;\r\n        });\r\n      } catch (e) {\r\n        this.$message.error('物资确认失败');\r\n      }\r\n    },\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #8957e5;\r\n  /* 基本信息模块 - 紫色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 图片列表模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 文件列表模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 物资列表模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 派车信息模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(6) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #8957e5;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(6) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.fixed-bottom-action {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 15px 0;\r\n  text-align: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.empty-data {\r\n  padding: 30px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.el-dialog__body {\r\n  padding: 20px 30px 0;\r\n}\r\n\r\n.image-container,\r\n.file-container {\r\n  padding: 20px;\r\n}\r\n\r\n.image-list,\r\n.file-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.image-item {\r\n  width: 150px;\r\n  height: 180px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.image-item img {\r\n  width: 100%;\r\n  height: 150px;\r\n  object-fit: cover;\r\n  display: block;\r\n}\r\n\r\n.image-name {\r\n  padding: 5px;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  min-width: 180px;\r\n  max-width: 250px;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 24px;\r\n  margin-right: 8px;\r\n  color: #909399;\r\n}\r\n\r\n.file-item:hover .file-icon {\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 新增物资选择相关样式 */\r\n.el-input-number {\r\n  width: 120px;\r\n}\r\n\r\n.material-selection {\r\n  margin-top: 20px;\r\n}\r\n\r\n.material-selection .el-table {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.dispatch-log-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 0px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.el-button--text {\r\n  color: #409EFF;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n\r\n  &:hover {\r\n    color: #66b1ff;\r\n    background-color: transparent;\r\n  }\r\n\r\n  &:focus {\r\n    color: #409EFF;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4cA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAI,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;IACA,IAAAC,iBAAA,YAAAA,kBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,OAAA;MACA,KAAAA,OAAA,CAAAC,IAAA,CAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;;IAEA;IACA,IAAAI,aAAA,YAAAA,cAAAN,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,OAAA;MACA,KAAAA,OAAA,CAAAC,IAAA,CAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;;IAEA;IACA,IAAAK,cAAA,YAAAA,eAAAP,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,OAAA;MACA,KAAAA,OAAA,CAAAC,IAAA,CAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IAEA;MACAM,cAAA;MACAC,+BAAA;MACAC,eAAA;MACAC,OAAA;MACAC,cAAA;MACAC,kBAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,qBAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,cAAA;QAAA;QACAC,WAAA;MACA;MAEA;MACAC,SAAA;MAEA;MACAC,QAAA;MAEA;MACAC,qBAAA;MAEAC,YAAA;MAEA;MACAC,YAAA;QACA;QACA;QACA;QACA;MAAA,CACA;MAEA;MACAC,aAAA;QACAC,SAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,SAAA,EAAAhC,iBAAA;UAAA+B,OAAA;QAAA,EACA;QACAE,UAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,GAAA;UAAAC,GAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,WAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,SAAA,EAAAzB,aAAA;UAAAwB,OAAA;QAAA,EACA;QACAM,YAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,SAAA,EAAAxB,cAAA;UAAAuB,OAAA;QAAA;MAEA;MAEA;MACAO,YAAA,GACA;QACAC,EAAA;QACAX,SAAA;QACAK,UAAA;QACAG,WAAA;QACAC,YAAA;QACAG,YAAA;QACAC,MAAA;QACAC,UAAA;QACAC,WAAA;QACAC,oBAAA;QACAC,mBAAA;MACA,GACA;QACAN,EAAA;QACAX,SAAA;QACAK,UAAA;QACAG,WAAA;QACAC,YAAA;QACAG,YAAA;QACAC,MAAA;QACAC,UAAA;QACAC,WAAA;QACAC,oBAAA;QACAC,mBAAA;MACA,EACA;MAEA;MACAC,QAAA;MACA3B,OAAA;MACA4B,eAAA;QACA5B,OAAA;MACA;MAEA6B,gBAAA;MACA;MACAC,qBAAA;QACAC,UAAA;QACAC,YAAA;QACAC,YAAA;QACAC,OAAA;QACAC,OAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACAC,kBAAA;MAAA;MACAC,mBAAA,MAAAC,GAAA;MAAA;MACAC,eAAA,MAAAD,GAAA;IACA;EACA;EACAE,QAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;IACA;IACA;IACAC,wBAAA,WAAAA,yBAAA;MACA,YAAA/C,iBAAA,QAAAC,qBAAA,QAAAF,UAAA,CAAAiD,KAAA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA,YAAApD,cAAA,QAAAC,kBAAA,QAAAF,OAAA,CAAAoD,KAAA;IACA;IACAE,sBAAA,WAAAA,uBAAA;MACA;MACA,cAAAC,QAAA,MAAArB,QAAA,CAAAsB,UAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,QAAA,uCAAAC,IAAA,WAAAC,QAAA;MACAH,KAAA,CAAA5D,+BAAA,GAAA+D,QAAA,CAAA1E,IAAA;IACA;IACA;IACA,KAAAwE,QAAA,oBAAAC,IAAA,WAAAC,QAAA;MACAH,KAAA,CAAA3D,eAAA,GAAA8D,QAAA,CAAA1E,IAAA;IACA;IACA;IACA,IAAAoB,OAAA,QAAAuD,MAAA,CAAAC,MAAA,CAAAxD,OAAA;IACA,KAAAA,OAAA,GAAAA,OAAA;IACA,KAAA4B,eAAA,CAAA5B,OAAA,GAAAA,OAAA;IACA,KAAAD,WAAA,CAAAC,OAAA,GAAAA,OAAA;IACA,IAAAA,OAAA;MACA,IAAAyD,gBAAA,EAAAzD,OAAA,EAAAqD,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,QAAA,GAAA2B,QAAA,CAAA1E,IAAA;QACAuE,KAAA,CAAAO,kBAAA;QACAC,OAAA,CAAAC,GAAA,kBAAAT,KAAA,CAAAxB,QAAA;QACA;QACAwB,KAAA,CAAAU,qBAAA;QACA;QACAV,KAAA,CAAAW,eAAA;MACA;IACA;IAAA;IACA,KAAAC,aAAA;IACA,KAAAC,UAAA;EAIA;EAGAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAApE,WAAA,CAAAG,WAAA;MACAyD,OAAA,CAAAC,GAAA,0BAAA7D,WAAA;MACA,IAAAqE,aAAA,OAAArE,WAAA,EAAAsD,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAAE,QAAA,CAAAC,OAAA;QACA;QACAH,MAAA,CAAAI,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA;YACAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;YACAC,OAAA;UACA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAb,MAAA,CAAAE,QAAA,CAAAW,KAAA;QACArB,OAAA,CAAAqB,KAAA,oBAAAA,KAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAnF,WAAA,CAAAG,WAAA;MACAyD,OAAA,CAAAC,GAAA,0BAAA7D,WAAA;MACA,IAAAqE,aAAA,OAAArE,WAAA,EAAAsD,IAAA,WAAAC,QAAA;QACA4B,MAAA,CAAAb,QAAA,CAAAC,OAAA;QACA;QACAY,MAAA,CAAAX,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA;YACAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;YACAC,OAAA;UACA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAE,MAAA,CAAAb,QAAA,CAAAW,KAAA;QACArB,OAAA,CAAAqB,KAAA,oBAAAA,KAAA;MACA;IACA;IACAG,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,aAAA,OAAA1D,QAAA,EAAA0B,IAAA,WAAAC,QAAA;QACA8B,MAAA,CAAAf,QAAA,CAAAC,OAAA;QAEA,IAAAgB,MAAA,CAAAC,OAAA,CAAAC,MAAA;UACAJ,MAAA,CAAAb,OAAA,CAAAkB,EAAA;QACA;UACAL,MAAA,CAAAb,OAAA,CAAAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;cAAAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;YAAA;UAAA;QACA;MACA,GAAAE,KAAA,WAAAC,KAAA;QACAI,MAAA,CAAAf,QAAA,CAAAW,KAAA;QACArB,OAAA,CAAAqB,KAAA,oBAAAA,KAAA;MACA;IACA;IACAtB,kBAAA,WAAAA,mBAAA;MACA,SAAA/B,QAAA,CAAA+D,QAAA;QACA,KAAApG,cAAA;MACA;IACA;IACA;IACAqG,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAxC,QAAA,oBAAAC,IAAA,WAAAC,QAAA;QACA,IAAAuC,OAAA,GAAAvC,QAAA,CAAA1E,IAAA;;QAEA;QACA,IAAAgH,MAAA,CAAAjE,QAAA,CAAA+D,QAAA;UACA;UACA,IAAAE,MAAA,CAAAtF,YAAA,CAAAkF,MAAA;YACAK,OAAA,GAAAA,OAAA,CAAAC,MAAA,WAAAC,MAAA;cAAA,OAAAA,MAAA,CAAAC,SAAA;YAAA;UACA;UACA;QACA;UACA;UACA,IAAAJ,MAAA,CAAAjE,QAAA,CAAA+D,QAAA;YACAG,OAAA,GAAAA,OAAA,CAAAC,MAAA,WAAAC,MAAA;cAAA,OAAAA,MAAA,CAAAC,SAAA;YAAA;UACA;QACA;QAEAJ,MAAA,CAAApG,eAAA,GAAAqG,OAAA;MACA;IACA;IACA/B,eAAA,WAAAA,gBAAA;MAAA,IAAAmC,MAAA;MACA,IAAAC,iBAAA,OAAAtE,eAAA,EAAAyB,IAAA,WAAAC,QAAA;QACAK,OAAA,CAAAC,GAAA,kBAAAN,QAAA,CAAA6C,IAAA;QACAF,MAAA,CAAA3F,YAAA,GAAAgD,QAAA,CAAA1E,IAAA;QACA+E,OAAA,CAAAC,GAAA,sBAAAqC,MAAA,CAAA3F,YAAA;QACA;QACA2F,MAAA,CAAAG,mBAAA;QACA;QACAH,MAAA,CAAAN,qBAAA;MACA;IACA;IACAU,mBAAA,WAAAA,oBAAA;MACA,IAAAC,YAAA;MACAhB,MAAA,CAAAiB,IAAA,CAAAD,YAAA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MACA,IAAAF,YAAA;MACAhB,MAAA,CAAAiB,IAAA,CAAAD,YAAA;IACA;IACA;IACAG,8BAAA,WAAAA,+BAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAArH,+BAAA,EAAAmH,GAAA,CAAAG,wBAAA;IACA;IAEAC,cAAA,WAAAA,eAAAJ,GAAA,EAAAC,MAAA;MACA,YAAAI,eAAA,CAAAL,GAAA,CAAAM,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAP,GAAA,EAAAC,MAAA;MACA,YAAAO,aAAA,CAAAR,GAAA,CAAAS,UAAA;IACA;IAEA,eACAnD,UAAA,WAAAA,WAAA;MAAA,IAAAoD,MAAA;MACA,KAAAC,OAAA;MACA;MACA;MACA;MACA;MACA,IAAAC,kCAAA,IAAAjE,IAAA,WAAAC,QAAA;QACA8D,MAAA,CAAA3H,OAAA,GAAA6D,QAAA,CAAA6C,IAAA;QACAiB,MAAA,CAAAzH,kBAAA,GAAAyH,MAAA,CAAA3H,OAAA;QACA2H,MAAA,CAAAC,OAAA;MACA;IACA;IACA;IACAE,aAAA,WAAAA,cAAA7C,KAAA;MAAA,IAAA8C,MAAA;MACA,KAAA9H,cAAA,GAAAgF,KAAA;MAEA,SAAAhF,cAAA;QACA;QACA,IAAA+H,YAAA;UACAhH,SAAA,EAAAiE;QACA;QACA,IAAA4C,kCAAA,EAAAG,YAAA,EAAApE,IAAA,WAAAC,QAAA;UACAkE,MAAA,CAAA7H,kBAAA,GAAA2D,QAAA,CAAA6C,IAAA;QACA,GAAApB,KAAA,WAAAC,KAAA;UACArB,OAAA,CAAAqB,KAAA,YAAAA,KAAA;UACAwC,MAAA,CAAA7H,kBAAA;QACA;MACA;QACA;QACA,KAAAA,kBAAA,QAAAF,OAAA,CAAAoD,KAAA;MACA;IACA;IACA;IACA6E,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,SAAApH,YAAA,CAAAqH,QAAA;QACA,KAAAhI,UAAA,CAAAiI,OAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAA1G,EAAA,IAAAuG,MAAA,CAAApH,YAAA,CAAAqH,QAAA;YACAD,MAAA,CAAApH,YAAA,CAAA5B,IAAA,GAAAmJ,IAAA,CAAAnJ,IAAA;YACAgJ,MAAA,CAAApH,YAAA,CAAAwH,MAAA,GAAAD,IAAA,CAAAC,MAAA;YACAJ,MAAA,CAAApH,YAAA,CAAAyH,OAAA,GAAAF,IAAA,CAAAE,OAAA;YACAL,MAAA,CAAApH,YAAA,CAAA0H,KAAA,GAAAH,IAAA,CAAAG,KAAA;YACAN,MAAA,CAAApH,YAAA,CAAA2H,KAAA,GAAAJ,IAAA,CAAAI,KAAA;YACAP,MAAA,CAAApH,YAAA,CAAA4H,WAAA,GAAAL,IAAA,CAAAK,WAAA;YACAR,MAAA,CAAApH,YAAA,CAAA6H,iBAAA,GAAAN,IAAA,CAAAM,iBAAA;YACAT,MAAA,CAAApH,YAAA,CAAA8H,kBAAA,GAAAP,IAAA,CAAAO,kBAAA;YACAV,MAAA,CAAApH,YAAA,CAAA+H,GAAA,GAAAR,IAAA,CAAAS,MAAA;UAEA;QACA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA9E,OAAA,CAAAC,GAAA;MACA,SAAArD,YAAA,CAAAmI,OAAA;QACA,KAAAjJ,OAAA,CAAAoI,OAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAA1G,EAAA,IAAAqH,MAAA,CAAAlI,YAAA,CAAAmI,OAAA;YACAD,MAAA,CAAAlI,YAAA,CAAAE,SAAA,GAAAqH,IAAA,CAAArH,SAAA;YAEA,IAAAqH,IAAA,CAAAjB,wBAAA;cACA4B,MAAA,CAAAlI,YAAA,CAAAsG,wBAAA;YACA,WAAAiB,IAAA,CAAAjB,wBAAA;cACA4B,MAAA,CAAAlI,YAAA,CAAAsG,wBAAA;YACA,WAAAiB,IAAA,CAAAjB,wBAAA;cACA4B,MAAA,CAAAlI,YAAA,CAAAsG,wBAAA;YACA;cACA4B,MAAA,CAAAlI,YAAA,CAAAsG,wBAAA;YACA;YACA4B,MAAA,CAAAlI,YAAA,CAAAoI,iBAAA,GAAAb,IAAA,CAAAa,iBAAA;YACAF,MAAA,CAAAlI,YAAA,CAAAqI,KAAA,GAAAd,IAAA,CAAAc,KAAA;YACAH,MAAA,CAAAlI,YAAA,CAAAsI,aAAA,GAAAf,IAAA,CAAAe,aAAA;YACAJ,MAAA,CAAAlI,YAAA,CAAAuI,SAAA,GAAAhB,IAAA,CAAAgB,SAAA;YACAL,MAAA,CAAAlI,YAAA,CAAAwI,QAAA,GAAAjB,IAAA,CAAAiB,QAAA;YACAN,MAAA,CAAAlI,YAAA,CAAAyI,YAAA,GAAAlB,IAAA,CAAAkB,YAAA;YACAP,MAAA,CAAAlI,YAAA,CAAA0I,SAAA,GAAAnB,IAAA,CAAAmB,SAAA;YACAR,MAAA,CAAAlI,YAAA,CAAA2I,YAAA,GAAApB,IAAA,CAAAoB,YAAA;YACAT,MAAA,CAAAlI,YAAA,CAAA4I,SAAA,GAAArB,IAAA,CAAAqB,SAAA;UACA;QAEA;MACA;IACA;IACA,eACApF,aAAA,WAAAA,cAAA;MAAA,IAAAqF,MAAA;MACA;MACA;MACA;MACA;MACA,IAAAC,mCAAA,IAAAhG,IAAA,WAAAC,QAAA;QACA8F,MAAA,CAAAxJ,UAAA,GAAA0D,QAAA,CAAA6C,IAAA;QACAxC,OAAA,CAAAC,GAAA,oBAAAwF,MAAA,CAAAxJ,UAAA;QACAwJ,MAAA,CAAAtJ,qBAAA,GAAAsJ,MAAA,CAAAxJ,UAAA;MACA;IACA;IACA;IACA0J,gBAAA,WAAAA,iBAAA5E,KAAA;MAAA,IAAA6E,OAAA;MACA,KAAA1J,iBAAA,GAAA6E,KAAA;MAEA,SAAA7E,iBAAA;QACA;QACA,IAAA4H,YAAA;UACA+B,WAAA,EAAA9E;QACA;QACA,IAAA2E,mCAAA,EAAA5B,YAAA,EAAApE,IAAA,WAAAC,QAAA;UACAiG,OAAA,CAAAzJ,qBAAA,GAAAwD,QAAA,CAAA6C,IAAA;QACA,GAAApB,KAAA,WAAAC,KAAA;UACArB,OAAA,CAAAqB,KAAA,cAAAA,KAAA;UACAuE,OAAA,CAAAzJ,qBAAA;QACA;MACA;QACA;QACA,KAAAA,qBAAA,QAAAF,UAAA,CAAAiD,KAAA;MACA;IACA;IACA;IACAgB,qBAAA,WAAAA,sBAAA;MACA;MACA,SAAAlC,QAAA,CAAA8H,WAAA;QACA;UACA,KAAAtJ,SAAA,GAAAuJ,IAAA,CAAAC,KAAA,MAAAhI,QAAA,CAAA8H,WAAA;QACA,SAAAG,CAAA;UACAjG,OAAA,CAAAqB,KAAA,cAAA4E,CAAA;UACA,KAAAzJ,SAAA;QACA;MACA;;MAEA;MACA,SAAAwB,QAAA,CAAAkI,YAAA;QACA;UACA,KAAAzJ,QAAA,GAAAsJ,IAAA,CAAAC,KAAA,MAAAhI,QAAA,CAAAkI,YAAA;QACA,SAAAD,CAAA;UACAjG,OAAA,CAAAqB,KAAA,cAAA4E,CAAA;UACA,KAAAxJ,QAAA;QACA;MACA;IACA;IAEA;IACA0J,YAAA,WAAAA,aAAAC,GAAA,EAAAC,QAAA;MACA,KAAAD,GAAA;QACA,KAAA1F,QAAA,CAAAW,KAAA;QACA;MACA;;MAEA;MACA,IAAAiF,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,IAAA,CAAAG,IAAA,GAAAL,GAAA;MACAE,IAAA,CAAAI,QAAA,GAAAL,QAAA;MACAE,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,IAAA;MACAA,IAAA,CAAAO,KAAA;MACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,IAAA;IACA;IAEA;IACAS,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAE,uBAAA,WAAAA,wBAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAE,mBAAA,WAAAA,oBAAAL,IAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAM,iBAAA,WAAAA,kBAAA3J,MAAA;MACA,IAAA4J,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA5J,MAAA;IACA;IAEA;IACA6J,WAAA,WAAAA,YAAAvH,GAAA;MACA,IAAAwH,eAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,eAAA,CAAAxH,GAAA,CAAAyH,OAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAhK,MAAA;MACA,IAAA4J,SAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA5J,MAAA;IACA;IAEA;IACAiK,qBAAA,WAAAA,sBAAAjK,MAAA;MACA,IAAA4J,SAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA5J,MAAA;IACA;IAEA;IACAkK,kBAAA,WAAAA,mBAAAb,IAAA;MACA,IAAAC,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAc,sBAAA,WAAAA,uBAAAd,IAAA;MACA,IAAAC,OAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAe,0BAAA,WAAAA,2BAAAZ,QAAA;MACA,IAAAF,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAE,QAAA;IACA;IAEA;IACAa,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAArJ,kBAAA,QAAAX,QAAA,CAAAiK,SAAA;MACAjI,OAAA,CAAAC,GAAA,iCAAAtB,kBAAA;;MAEA;MACA,KAAAuJ,mCAAA;MACA;MACA,SAAAlK,QAAA,CAAAmK,WAAA;QACA,SAAAvL,YAAA,CAAAyG,QAAA;UACA;UACA,IAAA+E,YAAA,QAAAzL,YAAA,CAAA0L,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAjF,QAAA;UAAA;UACA,IAAA+E,YAAA;YACA,KAAA1H,QAAA,CAAA6H,OAAA;YACA;UACA;UACAvI,OAAA,CAAAC,GAAA,iBAAAmI,YAAA;QACA;MACA;;MAGA;MACA,IAAAI,KAAA,QAAAC,MAAA,CAAAC,OAAA,CAAAF,KAAA;MACAxI,OAAA,CAAAC,GAAA,UAAAuI,KAAA;MACA,KAAAA,KAAA,CAAAnJ,QAAA,uBAAAmJ,KAAA,CAAAnJ,QAAA;QACA,KAAAqB,QAAA,CAAAW,KAAA;QACA;MACA;MAEArB,OAAA,CAAAC,GAAA,kCAAAjC,QAAA,CAAAsB,UAAA;MACA,eAAAD,QAAA,MAAArB,QAAA,CAAAsB,UAAA;QACA,KAAAoB,QAAA,CAAA6H,OAAA;QACA;MACA;MAKAvI,OAAA,CAAAC,GAAA,4BAAAtD,YAAA,CAAAkF,MAAA;MACA,SAAA7D,QAAA,CAAA2K,gBAAA,eAAAhM,YAAA,CAAAkF,MAAA;QACA,KAAAnB,QAAA,CAAA6H,OAAA;QACA;MACA;MAEA,SAAAvK,QAAA,CAAA2K,gBAAA,eAAAhM,YAAA,CAAAkF,MAAA;QACA,KAAAnB,QAAA,CAAA6H,OAAA;QACA;MACA;MAEA,KAAA3L,YAAA;;MAEA;MACA,KAAAoF,qBAAA;MAEA,SAAAhE,QAAA,CAAA+D,QAAA;QACA,KAAAnF,YAAA,CAAAyG,QAAA;MACA,gBAAArF,QAAA,CAAA+D,QAAA;QACA,KAAAnF,YAAA,CAAAyG,QAAA;MACA,gBAAArF,QAAA,CAAA+D,QAAA;QACA,KAAAnF,YAAA,CAAAyG,QAAA;MACA,gBAAArF,QAAA,CAAA+D,QAAA;QACA;QACA,SAAApF,YAAA,CAAAkF,MAAA;UACA,KAAAjF,YAAA,CAAAyG,QAAA;QACA;UACA,KAAAzG,YAAA,CAAAyG,QAAA;QACA;MACA;MACArD,OAAA,CAAAC,GAAA,MAAArD,YAAA,CAAAyG,QAAA,GACA,KAAA3G,qBAAA;IAGA;IAEA;IACAwL,mCAAA,WAAAA,oCAAA;MAAA,IAAAU,OAAA;MACA;MACA,KAAAhK,mBAAA,CAAAiK,KAAA;MACA;MACA,IAAAC,SAAA,QAAAnM,YAAA,CAAAwF,MAAA,WAAAgC,IAAA;QAAA,OAAAA,IAAA,CAAAd,QAAA;MAAA;MACArD,OAAA,CAAAC,GAAA,cAAA6I,SAAA;MACA,KAAAA,SAAA,IAAAA,SAAA,CAAAjH,MAAA;QACA;QACA,KAAA1D,qBAAA,SAAAH,QAAA,CAAAiK,SAAA,QAAAc,GAAA,WAAAC,GAAA;UACA;UACA;UACA;YACA5K,UAAA,EAAA4K,GAAA,CAAA5K,UAAA;YACAC,YAAA,EAAA2K,GAAA,CAAA3K,YAAA;YACAC,YAAA,EAAA0K,GAAA,CAAA1K,YAAA;YACAC,OAAA,EAAAyK,GAAA,CAAAzK,OAAA;YACAC,OAAA;YACAC,YAAA,EAAAuK,GAAA,CAAAzK,OAAA;YACAG,UAAA,EAAAsK,GAAA,CAAAzK;UACA;QACA;MACA;QACAyB,OAAA,CAAAC,GAAA,2BAAAtD,YAAA;QACAmM,SAAA,CAAA5E,OAAA,WAAAoE,IAAA;UACA,IAAAzI,MAAA;YAAAoJ,MAAA,EAAAX,IAAA,CAAAW;UAAA;UACA,IAAAC,sBAAA,EAAArJ,MAAA,EAAAH,IAAA,WAAAC,QAAA;YACA,IAAAwJ,aAAA,GAAAxJ,QAAA,CAAA6C,IAAA;YACA2G,aAAA,CAAAjF,OAAA,WAAAkF,QAAA;cACA,KAAAR,OAAA,CAAAhK,mBAAA,CAAAyK,GAAA,CAAAD,QAAA,CAAAhL,UAAA;gBACAwK,OAAA,CAAAhK,mBAAA,CAAA0K,GAAA,CAAAF,QAAA,CAAAhL,UAAA;kBACAmL,gBAAA,EAAAH,QAAA;kBACA5K,OAAA,EAAA4K,QAAA,CAAA7K;gBACA;cACA;gBACA,IAAAiL,gBAAA,GAAAZ,OAAA,CAAAhK,mBAAA,CAAA6K,GAAA,CAAAL,QAAA,CAAAhL,UAAA;gBACAoL,gBAAA,CAAAhL,OAAA,IAAA4K,QAAA,CAAA7K,OAAA;cACA;YACA;;YAEA;YACAqK,OAAA,CAAA1K,gBAAA,GAAAwL,KAAA,CAAAC,IAAA,CAAAf,OAAA,CAAAhK,mBAAA,YAAAgL,IAAA;cAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAH,IAAA;gBAAAI,GAAA,GAAAH,KAAA;gBAAAzO,KAAA,GAAAyO,KAAA;cAAA,WAAAI,cAAA,CAAAF,OAAA;gBACA3L,UAAA,EAAA4L;cAAA,GACA5O,KAAA;YAAA,CACA;;YAEA;YACAwN,OAAA,CAAAzK,qBAAA,IAAAyK,OAAA,CAAA5K,QAAA,CAAAiK,SAAA,QAAAc,GAAA,WAAAC,GAAA;cAAA,IAAAkB,qBAAA;cACA,IAAA1L,OAAA,KAAA0L,qBAAA,GAAAtB,OAAA,CAAAhK,mBAAA,CAAA6K,GAAA,CAAAT,GAAA,CAAA5K,UAAA,eAAA8L,qBAAA,uBAAAA,qBAAA,CAAA1L,OAAA;cACA,IAAAC,YAAA,GAAA0L,IAAA,CAAA9M,GAAA,EAAA2L,GAAA,CAAAzK,OAAA,SAAAC,OAAA;cAEA;gBACAJ,UAAA,EAAA4K,GAAA,CAAA5K,UAAA;gBACAC,YAAA,EAAA2K,GAAA,CAAA3K,YAAA;gBACAC,YAAA,EAAA0K,GAAA,CAAA1K,YAAA;gBACAC,OAAA,EAAAyK,GAAA,CAAAzK,OAAA;gBACAC,OAAA,EAAAA,OAAA;gBACAC,YAAA,EAAAA,YAAA;gBACAC,UAAA,EAAAD;cACA;YACA;YAEAmK,OAAA,CAAAzK,qBAAA,GAAAyK,OAAA,CAAAzK,qBAAA,CAAAgE,MAAA,WAAAgC,IAAA;cAAA,OAAAA,IAAA,CAAA1F,YAAA;YAAA;UACA;QACA;MACA;;MAEA;MACA,SAAAT,QAAA,CAAAmK,WAAA;QACA,SAAAvL,YAAA,CAAAyG,QAAA;UACA;UACA,IAAA+E,YAAA,QAAAzL,YAAA,CAAA0L,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAjF,QAAA;UAAA;UACA,IAAA+E,YAAA;YACA,KAAA1H,QAAA,CAAA6H,OAAA;YACA;UACA;UACAvI,OAAA,CAAAC,GAAA,iBAAAmI,YAAA;QACA;MACA;IAGA;IAEA;IACAgC,iBAAA,WAAAA,kBAAA;MACA,KAAAC,KAAA,CAAAzN,YAAA,SAAAyN,KAAA,CAAAzN,YAAA,CAAA0N,WAAA;MACA,KAAAnM,qBAAA;QACAC,UAAA;QACAC,YAAA;QACAC,YAAA;QACAC,OAAA;QACAE,YAAA;QACAD,OAAA;QACAE,UAAA;MACA;IACA;IAEA;IACA6L,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAA5L,mBAAA,CAAAiK,KAAA;MACA,KAAAlM,YAAA,CAAAuH,OAAA,WAAAoE,IAAA;QACA,IAAAzI,MAAA;UACAoJ,MAAA,EAAAX,IAAA,CAAAW;QACA;QACA,IAAAC,sBAAA,EAAArJ,MAAA,EAAAH,IAAA,WAAAC,QAAA;UACAK,OAAA,CAAAC,GAAA,qBAAAN,QAAA,CAAA6C,IAAA;UACA,IAAA2G,aAAA;UACAA,aAAA,GAAAxJ,QAAA,CAAA6C,IAAA;UACA2G,aAAA,CAAAjF,OAAA,WAAAkF,QAAA;YACA,KAAAoB,OAAA,CAAA5L,mBAAA,CAAAyK,GAAA,CAAAD,QAAA,CAAAhL,UAAA;cACAoM,OAAA,CAAA5L,mBAAA,CAAA0K,GAAA,CAAAF,QAAA,CAAAhL,UAAA;gBACAmL,gBAAA,EAAAH,QAAA;gBACA5K,OAAA,EAAA4K,QAAA,CAAA7K;cACA;YACA;cACA,IAAAiL,gBAAA,GAAAgB,OAAA,CAAA5L,mBAAA,CAAA6K,GAAA,CAAAL,QAAA,CAAAhL,UAAA;cACAoL,gBAAA,CAAAhL,OAAA,IAAA4K,QAAA,CAAA7K,OAAA;YACA;UACA;UACA;UACAiM,OAAA,CAAAtM,gBAAA,GAAAwL,KAAA,CAAAC,IAAA,CAAAa,OAAA,CAAA5L,mBAAA,YAAA6L,KAAA;YAAA,IAAAC,KAAA,OAAAZ,eAAA,CAAAC,OAAA,EAAAU,KAAA;cAAAT,GAAA,GAAAU,KAAA;cAAAtP,KAAA,GAAAsP,KAAA;YAAA,WAAAT,cAAA,CAAAF,OAAA;cACA3L,UAAA,EAAA4L;YAAA,GACA5O,KAAA;UAAA,CACA;UACA4E,OAAA,CAAAC,GAAA,sBAAAuK,OAAA,CAAAtM,gBAAA;UACA8B,OAAA,CAAAC,GAAA,wBAAAuK,OAAA,CAAA5L,mBAAA;QACA;MACA;IACA;IAEA;IACA+L,cAAA,WAAAA,eAAA;MACA,KAAAxM,qBAAA,CAAA0C,IAAA;QACAzC,UAAA;QACAC,YAAA;QACAC,YAAA;QACAC,OAAA;QACAE,YAAA;QACAD,OAAA;QACAE,UAAA;MACA;IACA;IAEA;IACAkM,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAA1M,qBAAA,CAAA2M,MAAA,CAAAD,KAAA;IACA;IAEA;IACAE,oBAAA,WAAAA,qBAAAhI,GAAA,EAAA8H,KAAA;MACA7K,OAAA,CAAAC,GAAA,8BAAA/B,gBAAA;MAGA,IAAA8M,gBAAA,QAAA9M,gBAAA,CAAA+M,IAAA,WAAA9G,IAAA;QAAA,OAAAA,IAAA,CAAA/F,UAAA,KAAA2E,GAAA,CAAA3E,UAAA;MAAA;MACA,IAAA4M,gBAAA;QACAjI,GAAA,CAAAvE,OAAA,GAAAwM,gBAAA,CAAAxM,OAAA;MACA;MACA,IAAA0M,kBAAA,QAAAlN,QAAA,CAAAiK,SAAA,CAAAgD,IAAA,WAAA9G,IAAA;QAAA,OAAAA,IAAA,CAAA/F,UAAA,KAAA2E,GAAA,CAAA3E,UAAA;MAAA;MAEA,IAAA8M,kBAAA;QACAnI,GAAA,CAAAxE,OAAA,GAAA2M,kBAAA,CAAA3M,OAAA;QACAwE,GAAA,CAAA1E,YAAA,GAAA6M,kBAAA,CAAA7M,YAAA;QACA0E,GAAA,CAAAzE,YAAA,GAAA4M,kBAAA,CAAA5M,YAAA;MACA;MAEAyE,GAAA,CAAAtE,YAAA,GAAAsE,GAAA,CAAAxE,OAAA,GAAAwE,GAAA,CAAAvE,OAAA;MACAuE,GAAA,CAAArE,UAAA,GAAAqE,GAAA,CAAAxE,OAAA,GAAAwE,GAAA,CAAAvE,OAAA;MAEAwB,OAAA,CAAAC,GAAA,yBAAA8C,GAAA,EAAA8H,KAAA;IAEA;IAEA;IACAM,kBAAA,WAAAA,mBAAApI,GAAA;MACA,KAAAA,GAAA,CAAA3E,UAAA;;MAEA;MACA,IAAAgN,YAAA,QAAAxM,mBAAA,CAAA6K,GAAA,CAAA1G,GAAA,CAAA3E,UAAA;MACA,IAAAI,OAAA,GAAA4M,YAAA,GAAAA,YAAA,CAAA5M,OAAA;MAEA,OAAAuE,GAAA,CAAAxE,OAAA,GAAAC,OAAA;IACA;IAEA;IACA6M,mBAAA,WAAAA,oBAAAjC,QAAA;MACA;MACA;MACA;;MAEA;;MAEA;MACA;MACA;MACA;MACA;;MAEA,YAAAjL,qBAAA,CAAAkK,IAAA,WAAAtF,GAAA;QAAA,OAAAA,GAAA,CAAA3E,UAAA,KAAAgL,QAAA,CAAAhL,UAAA;MAAA;MAAA;IACA;IAEA;IACAkN,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAAlB,KAAA,CAAAzN,YAAA,CAAA4O,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAF,OAAA,CAAAvN,QAAA,CAAAmK,WAAA;YACA,IAAAoD,OAAA,CAAA3O,YAAA,CAAAyG,QAAA;cACA;cACA,IAAA+E,YAAA,GAAAmD,OAAA,CAAA5O,YAAA,CAAA0L,IAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAjF,QAAA;cAAA;cACA,IAAA+E,YAAA;gBACAmD,OAAA,CAAA7K,QAAA,CAAA6H,OAAA;gBACA;cACA;YACA;UACA;;UAEA;UACA,IAAAmD,UAAA;UAEA1L,OAAA,CAAAC,GAAA,8BAAAsL,OAAA,CAAAvN,QAAA,CAAAmK,WAAA;UACAnI,OAAA,CAAAC,GAAA,+BAAAsL,OAAA,CAAA3O,YAAA,CAAAyG,QAAA;UAEA,IAAAkI,OAAA,CAAAvN,QAAA,CAAAmK,WAAA,SAAAoD,OAAA,CAAA3O,YAAA,CAAAyG,QAAA;YACAkI,OAAA,CAAApN,qBAAA,CAAA+F,OAAA,WAAAyH,MAAA;cACA;cACA,IAAAC,YAAA,IAAAL,OAAA,CAAAvN,QAAA,CAAAiK,SAAA,QAAAgD,IAAA,CACA,UAAAjC,GAAA;gBAAA,OAAAA,GAAA,CAAA5K,UAAA,KAAAuN,MAAA,CAAAvN,UAAA;cAAA,CACA;cACA,IAAAwN,YAAA;gBACA;gBACA,IAAAC,OAAA,OAAA5B,cAAA,CAAAF,OAAA,MAAA6B,YAAA;gBACAC,OAAA,CAAAtN,OAAA,GAAAoN,MAAA,CAAAjN,UAAA;gBACAgN,UAAA,CAAA7K,IAAA,CAAAgL,OAAA;cACA;YACA;;YAEA;YACA7L,OAAA,CAAAC,GAAA,+BAAAsL,OAAA,CAAApN,qBAAA;YACA6B,OAAA,CAAAC,GAAA,eAAAyL,UAAA;;YAEA;YACA,KAAAH,OAAA,CAAApN,qBAAA,CAAA0D,MAAA;cACA0J,OAAA,CAAA7K,QAAA,CAAA6H,OAAA;cACA;YACA;;YAEA;YACA,IAAAuD,kBAAA,GAAAP,OAAA,CAAApN,qBAAA,CAAAkK,IAAA,WAAAtF,GAAA;cACA;cACA,OACA,CAAAA,GAAA,CAAA3E,UAAA,IACA2E,GAAA,CAAArE,UAAA,SACAqE,GAAA,CAAArE,UAAA,GAAAqE,GAAA,CAAAtE,YAAA;YAEA;YAEA,IAAAqN,kBAAA;cACAP,OAAA,CAAA7K,QAAA,CAAA6H,OAAA;cACA;YACA;UACA;YACAvI,OAAA,CAAAC,GAAA,4BAAAsL,OAAA,CAAAvN,QAAA,CAAAiK,SAAA;YACAyD,UAAA,GAAAH,OAAA,CAAAvN,QAAA,CAAAiK,SAAA,GAAAsD,OAAA,CAAAvN,QAAA,CAAAiK,SAAA,CAAAc,GAAA,WAAA5E,IAAA;cAAA,WAAA8F,cAAA,CAAAF,OAAA,MAAA5F,IAAA;YAAA;YACAnE,OAAA,CAAAC,GAAA,WAAAyL,UAAA;UACA;UAMA,IAAAH,OAAA,CAAAvN,QAAA,CAAAmK,WAAA,SAAAoD,OAAA,CAAA3O,YAAA,CAAAyG,QAAA;YACAkI,OAAA,CAAA3O,YAAA,CAAA4G,UAAA;UACA;YACA+H,OAAA,CAAA3O,YAAA,CAAA4G,UAAA;UACA;UAEA,IAAA+H,OAAA,CAAA3O,YAAA,CAAAyG,QAAA;YACAkI,OAAA,CAAA3O,YAAA,CAAA4G,UAAA;UACA;;UAGA;UACA+H,OAAA,CAAA3O,YAAA,CAAAmP,cAAA;UACA;UACAR,OAAA,CAAA3O,YAAA,CAAAP,OAAA,GAAAkP,OAAA,CAAAlP,OAAA;UACAkP,OAAA,CAAA3O,YAAA,CAAAoP,MAAA,GAAAT,OAAA,CAAAvN,QAAA,CAAAgO,MAAA;UACAT,OAAA,CAAA3O,YAAA,CAAAqP,MAAA,GAAAV,OAAA,CAAA3O,YAAA,CAAAE,SAAA;UACAyO,OAAA,CAAA3O,YAAA,CAAAsP,WAAA,GAAAX,OAAA,CAAA3O,YAAA,CAAAyH,OAAA;UACAkH,OAAA,CAAA3O,YAAA,CAAAuP,gBAAA,GAAAZ,OAAA,CAAA3O,YAAA,CAAA6H,iBAAA;UACA8G,OAAA,CAAA3O,YAAA,CAAAO,UAAA,GAAAoO,OAAA,CAAA3O,YAAA,CAAA5B,IAAA;UACAuQ,OAAA,CAAA3O,YAAA,CAAAwP,WAAA,GAAAb,OAAA,CAAA3O,YAAA,CAAA0H,KAAA;UACAiH,OAAA,CAAA3O,YAAA,CAAAyP,OAAA,GAAAd,OAAA,CAAA3O,YAAA,CAAA2H,KAAA;UACAgH,OAAA,CAAA3O,YAAA,CAAA0P,iBAAA,GAAAf,OAAA,CAAA3O,YAAA,CAAA8H,kBAAA;UACA6G,OAAA,CAAA3O,YAAA,CAAA2P,QAAA,GAAAhB,OAAA,CAAA3O,YAAA,CAAAwH,MAAA;UACA,IAAAmH,OAAA,CAAA3O,YAAA,CAAA+H,GAAA;YACA4G,OAAA,CAAA3O,YAAA,CAAA+H,GAAA;UACA,WAAA4G,OAAA,CAAA3O,YAAA,CAAA+H,GAAA;YACA4G,OAAA,CAAA3O,YAAA,CAAA+H,GAAA;UACA;UACA,IAAA4G,OAAA,CAAA3O,YAAA,CAAAsG,wBAAA;YACAqI,OAAA,CAAA3O,YAAA,CAAAsG,wBAAA;UACA,WAAAqI,OAAA,CAAA3O,YAAA,CAAAsG,wBAAA;YACAqI,OAAA,CAAA3O,YAAA,CAAAsG,wBAAA;UACA,WAAAqI,OAAA,CAAA3O,YAAA,CAAAsG,wBAAA;YACAqI,OAAA,CAAA3O,YAAA,CAAAsG,wBAAA;UACA;UACAlD,OAAA,CAAAC,GAAA,sBAAAsL,OAAA,CAAA3O,YAAA;UAEA,IAAA4P,YAAA;UACAA,YAAA,CAAAP,MAAA,GAAAV,OAAA,CAAA3O,YAAA,CAAAqP,MAAA;UAEA,IAAAQ,qBAAA,EAAAD,YAAA,EAAA9M,IAAA,WAAAC,QAAA;YACA,IAAAoD,GAAA,GAAApD,QAAA,CAAA1E,IAAA;YACA,IAAA8H,GAAA;cACAwI,OAAA,CAAA7K,QAAA,CAAAW,KAAA;YACA;cACA,IAAAqL,KAAA;cACAA,KAAA,CAAAC,SAAA,GAAApB,OAAA,CAAA3O,YAAA;cACA8P,KAAA,CAAAE,qBAAA,GAAAlB,UAAA;cACA,IAAAmB,sCAAA,EAAAH,KAAA,EAAAhN,IAAA,WAAAoN,GAAA;gBACA9M,OAAA,CAAAC,GAAA,qCAAA6M,GAAA;gBACA,IAAAA,GAAA,CAAAC,IAAA;kBACAxB,OAAA,CAAA7K,QAAA,CAAAC,OAAA;kBACA4K,OAAA,CAAA7O,qBAAA;kBACA6O,OAAA,CAAApL,eAAA;gBACA;kBACA;kBACAoL,OAAA,CAAA7K,QAAA,CAAAW,KAAA,CAAAyL,GAAA,CAAA9P,OAAA;gBACA;cACA,GAAAoE,KAAA,WAAA4L,GAAA;gBACAhN,OAAA,CAAAqB,KAAA,oBAAA2L,GAAA;gBACAzB,OAAA,CAAA7K,QAAA,CAAAW,KAAA;cACA;;cAEA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;;cAEA;cACA;;cAGA;cACA;cACA;cACA;cACA;;cAEA;cACA;cACA;cACA;;cAEAkK,OAAA,CAAA7O,qBAAA;YACA;YACAsD,OAAA,CAAAC,GAAA,yBAAAN,QAAA;UACA,GAAAyB,KAAA,WAAA4L,GAAA;YACAhN,OAAA,CAAAqB,KAAA,oBAAA2L,GAAA;YACAzB,OAAA,CAAA7K,QAAA,CAAAW,KAAA;UACA;QAEA;UACA;QACA;MACA;IACA;IAEA;IACA4L,cAAA,WAAAA,eAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,IAAAH,IAAA,CAAAI,QAAA,QAAAC,QAAA,GAAAC,QAAA;MACA,IAAAC,GAAA,GAAAP,IAAA,CAAAQ,OAAA,GAAAH,QAAA,GAAAC,QAAA;MACA,IAAAG,KAAA,GAAAT,IAAA,CAAAU,QAAA,GAAAL,QAAA,GAAAC,QAAA;MACA,IAAAK,OAAA,GAAAX,IAAA,CAAAY,UAAA,GAAAP,QAAA,GAAAC,QAAA;MACA,IAAAO,OAAA,GAAAb,IAAA,CAAAc,UAAA,GAAAT,QAAA,GAAAC,QAAA;MAEA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IAEA;IACAG,WAAA,WAAAA,YAAA;MACA,KAAAxN,QAAA,CAAAC,OAAA;MACA;MACA;IACA;IAEA;IACAwN,MAAA,WAAAA,OAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,MAAAzO,MAAA;MACA,KAAAgB,OAAA,CAAAC,IAAA;QAAAC,IAAA;QAAAC,KAAA;UAAAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;QAAA;MAAA;IACA;IAEA;IACAoN,cAAA,WAAAA,eAAAvL,GAAA;MACA,KAAAnC,OAAA,CAAAC,IAAA;QACAC,IAAA,sBAAAmN,MAAA,CAAAlL,GAAA,CAAAkG,MAAA;MACA;IACA;IAEA7F,eAAA,WAAAA,gBAAAC,QAAA;MACA,IAAAkL,WAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAlL,QAAA;IACA;IAEAE,aAAA,WAAAA,cAAAiL,QAAA;MACA,IAAAD,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAC,QAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA9Q,MAAA;MACA,IAAA4J,SAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAA5J,MAAA;IACA;IAEA;AACA;AACA;AACA;IACA8E,mBAAA,WAAAA,oBAAA;MAAA,IAAAiM,OAAA;MAAA,WAAAC,kBAAA,CAAA5E,OAAA,mBAAA6E,aAAA,CAAA7E,OAAA,IAAA8E,CAAA,UAAAC,QAAA;QAAA,IAAAjP,MAAA,EAAAF,QAAA,EAAAoP,EAAA;QAAA,WAAAH,aAAA,CAAA7E,OAAA,IAAAiF,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAEA;cACAT,OAAA,CAAA5P,eAAA,CAAA+J,KAAA;;cAEA;cACAhJ,MAAA;gBACAxD,OAAA,EAAAqS,OAAA,CAAArS;cACA;cAAA4S,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAhG,sBAAA,EAAArJ,MAAA;YAAA;cAAAF,QAAA,GAAAsP,QAAA,CAAAG,CAAA;cACA,IAAAzP,QAAA,CAAAoN,IAAA,YAAApN,QAAA,CAAA6C,IAAA;gBACA;gBACA7C,QAAA,CAAA6C,IAAA,CAAA0B,OAAA,WAAAkF,QAAA;kBACA,IAAAY,GAAA,GAAAZ,QAAA,CAAAhL,UAAA;kBACA,KAAAsQ,OAAA,CAAA5P,eAAA,CAAAuK,GAAA,CAAAW,GAAA;oBACA0E,OAAA,CAAA5P,eAAA,CAAAwK,GAAA,CAAAU,GAAA;sBACA5L,UAAA,EAAAgL,QAAA,CAAAhL,UAAA;sBACAC,YAAA,EAAA+K,QAAA,CAAA/K,YAAA;sBACAC,YAAA,EAAA8K,QAAA,CAAA9K,YAAA;sBACAC,OAAA,EAAA6K,QAAA,CAAA7K,OAAA;sBACAC,OAAA;sBACA2K,aAAA;oBACA;kBACA;kBAEA,IAAAiC,YAAA,GAAAsD,OAAA,CAAA5P,eAAA,CAAA2K,GAAA,CAAAO,GAAA;kBACA;kBACAoB,YAAA,CAAA5M,OAAA,IAAA4K,QAAA,CAAA7K,OAAA;kBACA6M,YAAA,CAAAjC,aAAA,CAAAtI,IAAA;oBACAoI,MAAA,EAAAG,QAAA,CAAAH,MAAA;oBACAgD,MAAA,EAAA7C,QAAA,CAAA6C,MAAA;oBACA1N,OAAA,EAAA6K,QAAA,CAAA7K,OAAA;oBACA8Q,UAAA,EAAAjG,QAAA,CAAAiG;kBACA;gBACA;cACA;;cAEA;cACAX,OAAA,CAAAY,qBAAA;cAEAtP,OAAA,CAAAC,GAAA,uBAAAyO,OAAA,CAAA5P,eAAA;cAAAmQ,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAG,CAAA;cAEApP,OAAA,CAAAqB,KAAA,cAAA0N,EAAA;cACAL,OAAA,CAAAhO,QAAA,CAAAW,KAAA;YAAA;cAAA,OAAA4N,QAAA,CAAAM,CAAA;UAAA;QAAA,GAAAT,OAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAQ,qBAAA,WAAAA,sBAAA;MAAA,IAAAE,OAAA;MACA,KAAArR,qBAAA,CAAA+F,OAAA,WAAAnB,GAAA;QACA,IAAAA,GAAA,CAAA3E,UAAA;UACA,IAAAgN,YAAA,GAAAoE,OAAA,CAAA1Q,eAAA,CAAA2K,GAAA,CAAA1G,GAAA,CAAA3E,UAAA;UACA,IAAAgN,YAAA;YACA;YACArI,GAAA,CAAAvE,OAAA,GAAA4M,YAAA,CAAA5M,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAiR,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAf,kBAAA,CAAA5E,OAAA,mBAAA6E,aAAA,CAAA7E,OAAA,IAAA8E,CAAA,UAAAc,SAAA;QAAA,IAAAC,eAAA,EAAAC,GAAA;QAAA,WAAAjB,aAAA,CAAA7E,OAAA,IAAAiF,CAAA,WAAAc,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,CAAA;YAAA;cAAAY,SAAA,CAAAX,CAAA;cAAA,MAGAO,OAAA,CAAA/S,YAAA,IAAA+S,OAAA,CAAA/S,YAAA,CAAAkF,MAAA;gBAAAiO,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cACAU,eAAA,GAAAF,OAAA,CAAA/S,YAAA,CAAAwF,MAAA,WAAAmG,IAAA;gBAAA,OAAAA,IAAA,CAAA9E,UAAA;cAAA;cAAA,MACAoM,eAAA,CAAA/N,MAAA;gBAAAiO,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cACAQ,OAAA,CAAAhP,QAAA,CAAAW,KAAA;cAAA,OAAAyO,SAAA,CAAAP,CAAA;YAAA;cAAAO,SAAA,CAAAZ,CAAA;cAAA,OAMA,IAAAa,qBAAA;gBAAA1T,OAAA,EAAAqT,OAAA,CAAArT;cAAA;YAAA;cACAqT,OAAA,CAAAhP,QAAA,CAAAC,OAAA;cACA;cACA+O,OAAA,CAAAvP,eAAA;cACA;cACA,IAAAL,gBAAA,EAAA4P,OAAA,CAAArT,OAAA,EAAAqD,IAAA,WAAAC,QAAA;gBACA+P,OAAA,CAAA1R,QAAA,GAAA2B,QAAA,CAAA1E,IAAA;cACA;cAAA6U,SAAA,CAAAZ,CAAA;cAAA;YAAA;cAAAY,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAAV,CAAA;cAEAM,OAAA,CAAAhP,QAAA,CAAAW,KAAA;YAAA;cAAA,OAAAyO,SAAA,CAAAP,CAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;EAEA;AACA", "ignoreList": []}]}