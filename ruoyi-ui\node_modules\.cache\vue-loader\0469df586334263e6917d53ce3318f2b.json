{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\info\\index.vue?vue&type=style&index=0&id=5e25e3b2&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\info\\index.vue", "mtime": 1756170476879}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi51cGxvYWQtc2VjdGlvbiB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIHBhZGRpbmc6IDI1cHg7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgbWFyZ2luLWJvdHRvbTogMjVweDsNCiAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi51cGxvYWQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgY29sb3I6ICNmZmY7DQp9DQoNCi51cGxvYWQtaGVhZGVyIGkgew0KICBmb250LXNpemU6IDIwcHg7DQogIG1hcmdpbi1yaWdodDogMTBweDsNCn0NCg0KLnVwbG9hZC10aXRsZSB7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLnVwbG9hZC1jb250ZW50IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1pbi1oZWlnaHQ6IDE4MHB4Ow0KICBib3JkZXI6IDJweCBkYXNoZWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7DQp9DQoNCi51cGxvYWQtY29udGVudDpob3ZlciB7DQogIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjYpOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMTUpOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQogIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTsNCn0NCg0KLnVwbG9hZC1kcmFnZ2VyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCn0NCg0KLnVwbG9hZC1hcmVhIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIHBhZGRpbmc6IDIwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnVwbG9hZC1pY29uIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQoudXBsb2FkLXRleHQgew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQoudXBsb2FkLW1haW4tdGV4dCB7DQogIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLnVwbG9hZC1jbGljay10ZXh0IHsNCiAgY29sb3I6ICNmZmY7DQogIGZvbnQtc3R5bGU6IG5vcm1hbDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7DQogIGN1cnNvcjogcG9pbnRlcjsNCn0NCg0KLnVwbG9hZC10aXAgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpOw0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi51cGxvYWQtdGlwIGkgew0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQoudXBsb2FkLWxpbWl0cyB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOw0KICB3aWR0aDogMTAwJTsNCiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTsNCiAgZm9udC1zaXplOiAxM3B4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGdhcDogMTVweDsNCn0NCg0KLmxpbWl0LWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBib3JkZXItcmFkaXVzOiAyMHB4Ow0KICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoNXB4KTsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQoubGltaXQtaXRlbTpob3ZlciB7DQogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOw0KfQ0KDQoubGltaXQtaXRlbSBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA2cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsNCn0NCg0KLmZpbGUtbGlzdC1zZWN0aW9uIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KfQ0KDQouZmlsZS1saXN0LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQouZmlsZS1saXN0LXRpdGxlIHsNCiAgbWFyZ2luLWxlZnQ6IDhweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQouZmlsZS1jb3VudCB7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouZmlsZS1saXN0LWNvbnRlbnQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIHBhZGRpbmc6IDEwcHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi5maWxlLWluZm8gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiA1cHg7DQp9DQoNCi5maWxlLW5hbWUgew0KICBtYXJnaW4tbGVmdDogOHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgbWF4LXdpZHRoOiAxNTBweDsNCn0NCg0KLmVsLXRhYmxlIC5zdWNjZXNzLXJvdyB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmMGY5ZWI7DQp9DQoNCi5lbC10YWJsZSAuZGFuZ2VyLXJvdyB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZWYwZjA7DQp9DQoNCi5vcGVyYXRpb24tYnV0dG9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLm9wZXJhdGlvbi1idXR0b25zIC5lbC1idXR0b24gew0KICBtYXJnaW46IDA7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAglBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/supply/info", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 查询表单 -->\r\n    <el-form :inline=\"true\" :model=\"queryParams\" class=\"demo-form-inline\" @submit.native.prevent>\r\n      <el-form-item label=\"供应商代码\">\r\n        <el-input v-model=\"queryParams.supplyCode\" placeholder=\"请输入供应商代码\" clearable />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">查询</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 工具栏 -->\r\n    <div style=\"margin-bottom: 10px;\">\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n      <el-button type=\"success\" icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\r\n      <el-button type=\"warning\" icon=\"el-icon-download\" @click=\"handleExport\">导出</el-button>\r\n    </div>\r\n\r\n    <!-- 用户列表 -->\r\n    <el-table :data=\"userList\" border stripe style=\"width: 100%\">\r\n      <el-table-column prop=\"id\" label=\"ID\" width=\"60\" />\r\n      <el-table-column prop=\"supplyCode\" label=\"供应商代码\" />\r\n      <el-table-column prop=\"userName\" label=\"用户姓名\" />\r\n      <el-table-column prop=\"idcard\" label=\"身份证\" />\r\n      <el-table-column label=\"岗位识别卡\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFacDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"健康信息\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openHealthDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"附件\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFileDialog(scope.row)\">管理</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"operation-buttons\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadFile(scope.row)\"\r\n            >\r\n              下载\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"deleteFile(scope.row)\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <el-pagination\r\n      style=\"margin-top: 10px;\"\r\n      background\r\n      layout=\"total, prev, pager, next, jumper\"\r\n      :total=\"total\"\r\n      :page-size=\"queryParams.pageSize\"\r\n      :current-page.sync=\"queryParams.pageNum\"\r\n      @current-change=\"handleQuery\"\r\n    />\r\n\r\n    <!-- 新增/编辑主表弹窗 -->\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <el-form :model=\"form\" label-width=\"100px\">\r\n        <el-form-item label=\"供应商代码\">\r\n          <el-input v-model=\"form.supplyCode\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"供应商名称\">\r\n          <el-input v-model=\"form.supplyName\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户编号\">\r\n          <el-input v-model=\"form.userCode\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户姓名\">\r\n          <el-input v-model=\"form.userName\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证\">\r\n          <el-input v-model=\"form.idcard\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-select v-model=\"form.state\" placeholder=\"请选择\">\r\n            <el-option label=\"正常\" :value=\"1\" />\r\n            <el-option label=\"删除\" :value=\"101\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导入弹窗 -->\r\n    <el-dialog title=\"导入相关方人员\" :visible.sync=\"importDialogVisible\">\r\n      <el-upload\r\n        :action=\"importUrl\"\r\n        :show-file-list=\"false\"\r\n        :on-success=\"handleImportSuccess\"\r\n        :before-upload=\"beforeImportUpload\"\r\n        :headers=\"uploadHeaders\"\r\n      >\r\n        <el-button type=\"primary\">选择文件上传</el-button>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 岗位识别卡弹窗 -->\r\n    <vxe-modal v-model=\"facDialogVisible\" title=\"岗位识别卡\" width=\"700\" show-footer>\r\n      <vxe-form\r\n        :data=\"facForm\"\r\n        :items=\"facFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"facDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitFac\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 健康信息弹窗 -->\r\n    <vxe-modal v-model=\"healthDialogVisible\" title=\"健康信息\" width=\"800\" show-footer>\r\n      <vxe-form\r\n        :data=\"healthForm\"\r\n        :items=\"healthFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"healthDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitHealth\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 附件管理弹窗 -->\r\n    <el-dialog \r\n      :visible.sync=\"fileDialogVisible\" \r\n      title=\"附件管理\" \r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n    >\r\n      <!-- 上传区域 -->\r\n      <div class=\"upload-section\">\r\n        <div class=\"upload-header\">\r\n          <i class=\"el-icon-upload\"></i>\r\n          <span class=\"upload-title\">文件上传</span>\r\n        </div>\r\n        <div class=\"upload-content\">\r\n          <el-upload\r\n            ref=\"fileUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"upload.headers\"\r\n            :data=\"uploadData\"\r\n            :on-success=\"handleFileUploadSuccess\"\r\n            :on-error=\"handleFileUploadError\"\r\n            :before-upload=\"beforeFileUpload\"\r\n            :show-file-list=\"false\"\r\n            accept=\".pdf\"\r\n            drag\r\n            class=\"upload-dragger\"\r\n          >\r\n            <div class=\"upload-area\">\r\n              <i class=\"el-icon-upload upload-icon\"></i>\r\n              <div class=\"upload-text\">\r\n                <span class=\"upload-main-text\">将PDF文件拖到此处，或</span>\r\n                <em class=\"upload-click-text\">点击上传</em>\r\n              </div>\r\n              <div class=\"upload-tip\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>仅支持PDF格式文件，单个文件不超过50MB</span>\r\n              </div>\r\n              <div class=\"upload-limits\">\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span>文件格式：PDF</span>\r\n                </div>\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-files\"></i>\r\n                  <span>文件大小：≤ 50MB</span>\r\n                </div>\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>支持拖拽上传</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 文件列表 -->\r\n      <div class=\"file-list-section\">\r\n        <div class=\"file-list-header\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span class=\"file-list-title\">已上传文件</span>\r\n          <span class=\"file-count\">(共 {{fileList.length}} 个文件)</span>\r\n        </div>\r\n        <div class=\"file-list-content\">\r\n          <el-table \r\n            :data=\"fileList\" \r\n            style=\"width: 100%\"\r\n            :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n            :row-class-name=\"tableRowClassName\"\r\n          >\r\n            <el-table-column prop=\"filename\" label=\"文件名\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"file-info\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span class=\"file-name\">{{scope.row.filename}}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"format\" label=\"格式\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{scope.row.format}}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"state\" label=\"状态\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag \r\n                  size=\"mini\" \r\n                  :type=\"scope.row.state === 1 ? 'success' : 'danger'\"\r\n                >\r\n                  {{scope.row.state === 1 ? '正常' : '异常'}}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"operation-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-download\"\r\n                    @click=\"downloadFile(scope.row)\"\r\n                  >\r\n                    下载\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"deleteFile(scope.row)\"\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 弹窗底部 -->\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"fileDialogVisible = false\" icon=\"el-icon-close\">关闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listInfo, addInfo, updateInfo, delInfo, exportInfo } from '@/api/supply/info'\r\nimport { getFac, addFac, updateFac } from '@/api/supply/fac'\r\nimport { getHealth, addHealth, updateHealth } from '@/api/supply/health'\r\nimport { listFile, delFile } from '@/api/supply/file'\r\nimport request from '@/utils/request'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'SupplyUserInfo',\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        supplyCode: ''\r\n      },\r\n      userList: [],\r\n      total: 0,\r\n      // 岗位识别卡\r\n      facDialogVisible: false,\r\n      facForm: {},\r\n      facFormItems: [\r\n        { field: 'userPost', title: '岗位名称', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入岗位名称', rows: 2 } } },\r\n        { field: 'userFacClass', title: '岗位班组', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位班组' } } },\r\n        { field: 'userDeptName', title: '所属部门', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入所属部门' } } },\r\n        { field: 'userFacWork', title: '岗位描述', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位描述' } } },\r\n        { field: 'userTimeBegin', title: '入厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'userTimeEnd', title: '离厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 24,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '起草', value: 0 },\r\n              { label: '分厂审核人', value: 1 },\r\n              { label: '人力资源部', value: 2 },\r\n              { label: '退回', value: -1 },\r\n              { label: '禁用', value: 101 },\r\n              { label: '审核通过', value: 99 },\r\n              { label: '删除', value: 102 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 健康信息\r\n      healthDialogVisible: false,\r\n      healthForm: {},\r\n      healthFormItems: [\r\n        { field: 'healdate', title: '体检日期', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'hos', title: '医院', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入医院' } } },\r\n        { field: 'healtz', title: '体重', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重' } } },\r\n        { field: 'healtzzs', title: '体重指数', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重指数' } } },\r\n        { field: 'healptt', title: '血糖', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入血糖' } } },\r\n        { field: 'healssy', title: '收缩压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入收缩压' } } },\r\n        { field: 'healszy', title: '舒张压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入舒张压' } } },\r\n        { field: 'healzdgc', title: '总胆固醇', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入总胆固醇' } } },\r\n        { field: 'healgysz', title: '甘油三酯', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入甘油三酯' } } },\r\n        { field: 'healga', title: '谷氨酰转肽酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷氨酰转肽酶' } } },\r\n        { field: 'healgb', title: '谷丙转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷丙转氨酶' } } },\r\n        { field: 'healgc', title: '谷草转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷草转氨酶' } } },\r\n        { field: 'healnsd', title: '尿素氮', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入尿素氮' } } },\r\n        { field: 'healjg', title: '肌酐', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入肌酐' } } },\r\n        { field: 'healxd', title: '心电图', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入心电图' } } },\r\n        { field: 'healxj', title: '小结', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入小结', rows: 2 } } },\r\n        { field: 'healjy', title: '建议', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入建议', rows: 2 } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 12,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '正常', value: 1 },\r\n              { label: '删除', value: 101 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 附件\r\n      fileDialogVisible: false,\r\n      fileList: [],\r\n      uploadUrl: process.env.VUE_APP_BASE_API + '/web/supply/userfile/upload', // 新的 SFTP 上传接口\r\n      currentUserId: null,\r\n      currentUserInfo: {}, // 新增：保存当前用户信息\r\n      // 上传配置\r\n      upload: {\r\n        headers: { Authorization: 'Bearer ' + getToken() }\r\n      },\r\n      // 新增/编辑主表\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      form: {},\r\n      importDialogVisible: false,\r\n      importUrl: '/web/supply/userinfo/import'\r\n    }\r\n  },\r\n  computed: {\r\n    uploadData() {\r\n      return {\r\n        userid: this.currentUserId,\r\n        usercode: this.currentUserInfo.userCode,\r\n        username: this.currentUserInfo.userName,\r\n        supplycode: this.currentUserInfo.supplyCode,\r\n        supplyname: this.currentUserInfo.supplyName,\r\n        idcard: this.currentUserInfo.idcard,\r\n        userdeptname: this.currentUserInfo.userDeptName\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 查询用户列表\r\n    handleQuery() {\r\n      listInfo(this.queryParams).then(res => {\r\n        this.userList = res.rows\r\n        this.total = res.total\r\n      })\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.supplyCode = ''\r\n      this.handleQuery()\r\n    },\r\n    // 新增\r\n    handleAdd() {\r\n      this.dialogTitle = '新增相关方人员'\r\n      this.form = {}\r\n      this.dialogVisible = true\r\n    },\r\n    // 编辑\r\n    handleEdit(row) {\r\n      this.dialogTitle = '编辑相关方人员'\r\n      this.form = Object.assign({}, row)\r\n      this.dialogVisible = true\r\n    },\r\n    // 删除\r\n    handleDelete(row) {\r\n      this.$confirm('确定删除该条数据吗？', '提示', { type: 'warning' }).then(() => {\r\n        delInfo(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.handleQuery()\r\n        })\r\n      })\r\n    },\r\n    // 提交主表\r\n    submitForm() {\r\n      if (this.form.id) {\r\n        updateInfo(this.form).then(() => {\r\n          this.$message.success('修改成功')\r\n          this.dialogVisible = false\r\n          this.handleQuery()\r\n        })\r\n      } else {\r\n        addInfo(this.form).then(() => {\r\n          this.$message.success('新增成功')\r\n          this.dialogVisible = false\r\n          this.handleQuery()\r\n        })\r\n      }\r\n    },\r\n    // 导出\r\n    handleExport() {\r\n      exportInfo(this.queryParams).then(res => {\r\n        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })\r\n        const url = window.URL.createObjectURL(blob)\r\n        const link = document.createElement('a')\r\n        link.style.display = 'none'\r\n        link.href = url\r\n        link.setAttribute('download', '相关方人员数据.xlsx')\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        window.URL.revokeObjectURL(url)\r\n      })\r\n    },\r\n    // 导入\r\n    handleImport() {\r\n      this.importDialogVisible = true\r\n    },\r\n    handleImportSuccess(response) {\r\n      if (response.code === 200) {\r\n        this.$message.success('导入成功')\r\n        this.importDialogVisible = false\r\n        this.handleQuery()\r\n      } else {\r\n        this.$message.error(response.msg || '导入失败')\r\n      }\r\n    },\r\n    // 导入前检查文件类型\r\n    beforeImportUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件！')\r\n      }\r\n      return isExcel\r\n    },\r\n    // 附件上传前检查文件类型\r\n    beforeFileUpload(file) {\r\n      // 检查文件类型是否为PDF\r\n      const isPDF = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')\r\n      if (!isPDF) {\r\n        this.$message.error('只能上传PDF格式文件！')\r\n        return false\r\n      }\r\n      \r\n      // 检查文件大小限制\r\n      const maxSize = 50 * 1024 * 1024 // 50MB\r\n      if (file.size > maxSize) {\r\n        this.$message.error('文件大小不能超过50MB！')\r\n        return false\r\n      }\r\n      \r\n      return true\r\n    },\r\n    // 岗位识别卡\r\n    openFacDialog(row) {\r\n      getFac(row.id).then(res => {\r\n        this.facForm = res.data || { userId: row.id }\r\n        this.facDialogVisible = true\r\n      })\r\n    },\r\n    submitFac() {\r\n      const api = this.facForm.id ? updateFac : addFac\r\n      api(this.facForm).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.facDialogVisible = false\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    // 健康信息\r\n    openHealthDialog(row) {\r\n      getHealth(row.id).then(res => {\r\n        this.healthForm = res.data || { userid: row.id }\r\n        this.healthDialogVisible = true\r\n      })\r\n    },\r\n    submitHealth() {\r\n      const api = this.healthForm.id ? updateHealth : addHealth\r\n      api(this.healthForm).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.healthDialogVisible = false\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    // 附件管理\r\n    openFileDialog(row) {\r\n      this.currentUserId = row.id\r\n      this.currentUserInfo = row // 保存当前用户信息\r\n      this.getFileList(row.id)\r\n      this.fileDialogVisible = true\r\n    },\r\n    getFileList(userid) {\r\n      listFile({ userid }).then(res => {\r\n        this.fileList = res.rows\r\n      })\r\n    },\r\n    handleFileUploadSuccess(response) {\r\n      if (response.code === 200) {\r\n        this.$message.success('文件上传成功')\r\n        this.getFileList(this.currentUserId)\r\n      } else {\r\n        this.$message.error(response.msg || '文件上传失败')\r\n      }\r\n    },\r\n    handleFileUploadError(err) {\r\n      this.$message.error('文件上传失败: ' + (err.message || '未知错误'))\r\n    },\r\n    deleteFile(row) {\r\n      this.$confirm('确定删除该附件吗？', '提示', { type: 'warning' }).then(() => {\r\n        delFile(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getFileList(this.currentUserId)\r\n        })\r\n      })\r\n    },\r\n    downloadFile(row) {\r\n      // 调用下载接口获取文件URL\r\n      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          // 获取到文件URL后，在新窗口中打开下载\r\n          const fileUrl = response.data\r\n          window.open(fileUrl, '_blank')\r\n        } else {\r\n          this.$message.error(response.msg || '下载失败')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('下载失败: ' + error.message)\r\n      })\r\n    },\r\n    tableRowClassName({ row, rowIndex }) {\r\n      if (row.state === 1) {\r\n        return 'success-row'\r\n      } else {\r\n        return 'danger-row'\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleQuery()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.upload-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 25px;\r\n  border-radius: 8px;\r\n  margin-bottom: 25px;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.upload-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  color: #fff;\r\n}\r\n\r\n.upload-header i {\r\n  font-size: 20px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.upload-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.upload-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 180px;\r\n  border: 2px dashed rgba(255, 255, 255, 0.3);\r\n  border-radius: 8px;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.upload-content:hover {\r\n  border-color: rgba(255, 255, 255, 0.6);\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.upload-dragger {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 48px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.upload-text {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.upload-main-text {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 16px;\r\n}\r\n\r\n.upload-click-text {\r\n  color: #fff;\r\n  font-style: normal;\r\n  font-weight: 600;\r\n  text-decoration: underline;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 14px;\r\n}\r\n\r\n.upload-tip i {\r\n  margin-right: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.upload-limits {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  width: 100%;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 13px;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.limit-item {\r\n  display: flex;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 8px 12px;\r\n  border-radius: 20px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.limit-item:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.limit-item i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.file-list-section {\r\n  background-color: #f5f7fa;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.file-list-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  color: #606266;\r\n}\r\n\r\n.file-list-title {\r\n  margin-left: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-count {\r\n  margin-left: 10px;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.file-list-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  padding: 10px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-name {\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 150px;\r\n}\r\n\r\n.el-table .success-row {\r\n  background-color: #f0f9eb;\r\n}\r\n\r\n.el-table .danger-row {\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.operation-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.operation-buttons .el-button {\r\n  margin: 0;\r\n}\r\n</style>\r\n"]}]}