package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.dto.ApproveRequestDTO;
import com.ruoyi.app.leave.dto.LeaveStatusDTO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.transaction.annotation.Transactional;

/**
 * 出门证计划申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ILeavePlanService 
{
    /**
     * 查询出门证计划申请
     * 
     * @param id 出门证计划申请ID
     * @return 出门证计划申请
     */
    public LeavePlan selectLeavePlanById(Long id);

    /**
     * 查询出门证计划申请列表
     * 
     * @param leavePlan 出门证计划申请
     * @return 出门证计划申请集合
     */
    public List<LeavePlan> selectLeavePlanList(LeavePlan leavePlan);

    /**
     * 新增出门证计划申请
     * 
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    public void insertLeavePlan(LeavePlan leavePlan);

    /**
     * 修改出门证计划申请
     * 
     * @param leavePlan 出门证计划申请
     * @return 结果
     */
    public int updateLeavePlan(LeavePlan leavePlan);

    /**
     * 批量删除出门证计划申请
     * 
     * @param ids 需要删除的出门证计划申请ID
     * @return 结果
     */
    public int deleteLeavePlanByIds(Long[] ids);

    /**
     * 删除出门证计划申请信息
     * 
     * @param id 出门证计划申请ID
     * @return 结果
     */
    public int deleteLeavePlanById(Long id);

    public LeavePlan detail(String applyNo);

    void updateIsSendCar(String applyNo);
    public LeavePlan detail(String applyNo,String workNo);

    //审核
    public AjaxResult approve(ApproveRequestDTO approveRequestD,SysUser currentUser);

    //驳回
//    public AjaxResult reject(LeavePlan leavePlan, String workNo);

    //废弃
    public void discard(String workNo,SysUser sysUser);

    //废弃前校验
    public void checkBeforeDiscard(String applyNo,SysUser sysUser);

    //待审批列表
    public TableDataInfo waitApproveList(LeavePlan leavePlan,String workNo);

    List<LeaveStatusDTO> queryStatusList();

    void showBtn(LeavePlan leavePlan);

    public TableDataInfo  selectListForMiniApp(LeavePlan leavePlan, String workNo);

    /**
     * 处理出门证计划不同类型对应的相关数据
     * 根据planType对应处理不同表的数据
     * @param leavePlan 出门证计划
     */
    public void handlePlan(LeavePlan leavePlan);

    @Transactional(rollbackFor = Exception.class)
    void leavePlanExpired();

    /**
     * 物资确认，将计划状态更新为已完成（7）
     * @param applyNo 申请编号
     * @return 结果
     */
    int confirmMaterial(String applyNo);
}
