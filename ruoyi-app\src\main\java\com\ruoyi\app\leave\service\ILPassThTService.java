package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LPassThT;

/**
 * 退货申请主Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface ILPassThTService 
{
    /**
     * 查询退货申请主
     * 
     * @param id 退货申请主ID
     * @return 退货申请主
     */
    public LPassThT selectLPassThTById(Long id);

    /**
     * 查询退货申请主列表
     * 
     * @param lPassThT 退货申请主
     * @return 退货申请主集合
     */
    public List<LPassThT> selectLPassThTList(LPassThT lPassThT);

    /**
     * 新增退货申请主
     * 
     * @param lPassThT 退货申请主
     * @return 结果
     */
    public int insertLPassThT(LPassThT lPassThT);

    /**
     * 修改退货申请主
     * 
     * @param lPassThT 退货申请主
     * @return 结果
     */
    public int updateLPassThT(LPassThT lPassThT);

    /**
     * 批量删除退货申请主
     * 
     * @param ids 需要删除的退货申请主ID
     * @return 结果
     */
    public int deleteLPassThTByIds(Long[] ids);

    /**
     * 删除退货申请主信息
     * 
     * @param id 退货申请主ID
     * @return 结果
     */
    public int deleteLPassThTById(Long id);
}
