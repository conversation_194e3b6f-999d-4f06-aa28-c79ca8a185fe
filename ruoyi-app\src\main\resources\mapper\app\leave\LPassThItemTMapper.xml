<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LPassThItemTMapper">
    
    <resultMap type="LPassThItemT" id="LPassThItemTResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="materialcode"    column="materialcode"    />
        <result property="materialname"    column="materialname"    />
        <result property="materialspec"    column="materialspec"    />
        <result property="materialtype"    column="materialtype"    />
        <result property="unit"    column="unit"    />
        <result property="count"    column="count"    />
        <result property="weight"    column="weight"    />
        <result property="memo"    column="memo"    />
        <result property="counttmp"    column="counttmp"    />
        <result property="planid"    column="planid"    />
    </resultMap>

    <sql id="selectLPassThItemTVo">
        select id, pid, materialcode, materialname, materialspec, materialtype, unit, count, weight, memo, counttmp, planid from L_PASS_TH_ITEM_T
    </sql>

    <select id="selectLPassThItemTList" parameterType="LPassThItemT" resultMap="LPassThItemTResult">
        <include refid="selectLPassThItemTVo"/>
        <where>  
            <if test="pid != null "> and pid = #{pid}</if>
            <if test="materialcode != null  and materialcode != ''"> and materialcode = #{materialcode}</if>
            <if test="materialname != null  and materialname != ''"> and materialname like concat('%', #{materialname}, '%')</if>
            <if test="materialspec != null  and materialspec != ''"> and materialspec = #{materialspec}</if>
            <if test="materialtype != null  and materialtype != ''"> and materialtype = #{materialtype}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="count != null  and count != ''"> and count = #{count}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="counttmp != null "> and counttmp = #{counttmp}</if>
            <if test="planid != null  and planid != ''"> and planid = #{planid}</if>
        </where>
    </select>
    
    <select id="selectLPassThItemTById" parameterType="Long" resultMap="LPassThItemTResult">
        <include refid="selectLPassThItemTVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLPassThItemT" parameterType="LPassThItemT">
        insert into L_PASS_TH_ITEM_T
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pid != null">pid,</if>
            <if test="materialcode != null">materialcode,</if>
            <if test="materialname != null">materialname,</if>
            <if test="materialspec != null">materialspec,</if>
            <if test="materialtype != null">materialtype,</if>
            <if test="unit != null">unit,</if>
            <if test="count != null">count,</if>
            <if test="weight != null">weight,</if>
            <if test="memo != null">memo,</if>
            <if test="counttmp != null">counttmp,</if>
            <if test="planid != null">planid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pid != null">#{pid},</if>
            <if test="materialcode != null">#{materialcode},</if>
            <if test="materialname != null">#{materialname},</if>
            <if test="materialspec != null">#{materialspec},</if>
            <if test="materialtype != null">#{materialtype},</if>
            <if test="unit != null">#{unit},</if>
            <if test="count != null">#{count},</if>
            <if test="weight != null">#{weight},</if>
            <if test="memo != null">#{memo},</if>
            <if test="counttmp != null">#{counttmp},</if>
            <if test="planid != null">#{planid},</if>
         </trim>
    </insert>

    <update id="updateLPassThItemT" parameterType="LPassThItemT">
        update L_PASS_TH_ITEM_T
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="materialcode != null">materialcode = #{materialcode},</if>
            <if test="materialname != null">materialname = #{materialname},</if>
            <if test="materialspec != null">materialspec = #{materialspec},</if>
            <if test="materialtype != null">materialtype = #{materialtype},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="count != null">count = #{count},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="counttmp != null">counttmp = #{counttmp},</if>
            <if test="planid != null">planid = #{planid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLPassThItemTById" parameterType="Long">
        delete from L_PASS_TH_ITEM_T where id = #{id}
    </delete>

    <delete id="deleteLPassThItemTByIds" parameterType="String">
        delete from L_PASS_TH_ITEM_T where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteByPid" parameterType="Long">
        delete from L_PASS_TH_ITEM_T where pid = #{pid}
    </delete>
</mapper>