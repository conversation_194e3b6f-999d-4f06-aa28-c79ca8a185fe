package com.ruoyi.app.leave.task;

import com.ruoyi.app.leave.domain.LeaveLog;
import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.mapper.LeaveLogMapper;
import com.ruoyi.app.leave.mapper.LeavePlanMapper;
import com.ruoyi.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Description: 出门证定时任务
 * @CreateTime: 2025-03-25 14:14
 * @Author: liekkas
 */
@Component("exitPermitScheduledTask")
public class ExitPermitScheduledTask {

    private static final Logger log = LoggerFactory.getLogger(ExitPermitScheduledTask.class);

    @Autowired
    private LeaveLogMapper logMapper;

    @Autowired
    private LeavePlanMapper planMapper;

    /**
     * 出门证过期
     * 出厂前任务允许过期
     *
     * @param
     * @return
     */
    public void passPermitExpired() {
        log.info("ExitPermitScheduledTask.class 定时任务->出门证过期");
        List<LeavePlan> list = planMapper.selectExpiredList();
        //批量更新
        LeavePlan updatePlan = new LeavePlan();
        updatePlan.setPlanStatus(13);
        planMapper.updateExpiredLeavePlan(updatePlan);
        Date nowTime = DateUtils.getNowDate();
        //插入日志
        for (LeavePlan plan : list) {
            LeaveLog logItem = new LeaveLog();
            logItem.setLogType(1);
            logItem.setApplyNo(plan.getApplyNo());
            logItem.setInfo("计划已过期");
            logItem.setCreateTime(nowTime);
            logItem.setCreateBy("cronJob");
            logMapper.insertLeaveLog(logItem);
        }
    }
}
