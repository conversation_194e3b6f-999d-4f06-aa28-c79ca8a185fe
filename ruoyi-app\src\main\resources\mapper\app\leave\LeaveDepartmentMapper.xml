<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeaveDepartmentMapper">

    <resultMap type="LeaveDepartment" id="LeaveDepartmentResult">
        <result property="id"    column="id"    />
        <result property="validFlag"    column="valid_flag"    />
        <result property="storeName"    column="store_name"    />
        <result property="queryWord"    column="query_word"    />
        <result property="type"    column="type"    />
        <result property="unitName"    column="unit_name"    />
        <result property="position"    column="position"    />
        <result property="lowerLimit"    column="lower_limit"    />
        <result property="upLimit"    column="up_limit"    />
        <result property="memo"    column="memo"    />
        <result property="fStoreCode"    column="f_store_code"    />
        <result property="fStoreName"    column="f_store_name"    />
        <result property="levelName"    column="level_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="area"    column="area"    />
    </resultMap>

    <sql id="selectLeaveDepartmentVo">
        select id, valid_flag,  store_name, query_word, type, unit_name, position, lower_limit, up_limit, memo, f_store_code, f_store_name, level_name, create_time, create_by, update_time, update_by,area from leave_department
    </sql>

    <select id="selectLeaveDepartmentList" parameterType="LeaveDepartment" resultMap="LeaveDepartmentResult">
        <include refid="selectLeaveDepartmentVo"/>
        <where>
            valid_flag = 1
            <if test="storeName != null  and storeName != ''"> and store_name like concat('%', #{storeName}, '%')</if>
            <if test="queryWord != null  and queryWord != ''"> and query_word like concat('%', #{queryWord}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectLeaveDepartmentByDeptName" parameterType="LeaveDepartment" resultMap="LeaveDepartmentResult">
        <include refid="selectLeaveDepartmentVo"/>
        <where>
            valid_flag = 1
            <if test="storeName != null  and storeName != ''"> and store_name = #{storeName}</if>
        </where>
    </select>

    <select id="selectLeaveDepartmentListBy" parameterType="LeaveDepartment" resultMap="LeaveDepartmentResult">
        <include refid="selectLeaveDepartmentVo"/>
        <where>
            valid_flag = 1
            <if test="storeName != null  and storeName != ''"> and store_name like concat('%', #{storeName}, '%')</if>
            <if test="queryWord != null  and queryWord != ''"> and query_word like concat('%', #{queryWord}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectLeaveDepartmentById" parameterType="Long" resultMap="LeaveDepartmentResult">
        <include refid="selectLeaveDepartmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertLeaveDepartment" parameterType="LeaveDepartment" useGeneratedKeys="true" keyProperty="id">
        insert into leave_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
            valid_flag,
            <if test="storeName != null">store_name,</if>
            <if test="queryWord != null">query_word,</if>
            <if test="type != null">type,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="position != null">position,</if>
            <if test="lowerLimit != null">lower_limit,</if>
            <if test="upLimit != null">up_limit,</if>
            <if test="memo != null">memo,</if>
            <if test="fStoreCode != null">f_store_code,</if>
            <if test="fStoreName != null">f_store_name,</if>
            <if test="levelName != null">level_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="area != null">area,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            1,
            <if test="storeName != null">#{storeName},</if>
            <if test="queryWord != null">#{queryWord},</if>
            <if test="type != null">#{type},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="position != null">#{position},</if>
            <if test="lowerLimit != null">#{lowerLimit},</if>
            <if test="upLimit != null">#{upLimit},</if>
            <if test="memo != null">#{memo},</if>
            <if test="fStoreCode != null">#{fStoreCode},</if>
            <if test="fStoreName != null">#{fStoreName},</if>
            <if test="levelName != null">#{levelName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="area != null">#{area},</if>
         </trim>
    </insert>

    <update id="updateLeaveDepartment" parameterType="LeaveDepartment">
        update leave_department
        <trim prefix="SET" suffixOverrides=",">
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="storeName != null">store_name = #{storeName},</if>
            <if test="queryWord != null">query_word = #{queryWord},</if>
            <if test="type != null">type = #{type},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="position != null">position = #{position},</if>
            <if test="lowerLimit != null">lower_limit = #{lowerLimit},</if>
            <if test="upLimit != null">up_limit = #{upLimit},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="fStoreCode != null">f_store_code = #{fStoreCode},</if>
            <if test="fStoreName != null">f_store_name = #{fStoreName},</if>
            <if test="levelName != null">level_name = #{levelName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="area != null">area = #{area},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="cancelLeaveDepartment" parameterType="LeaveDepartment">
        update leave_department
        <trim prefix="SET" suffixOverrides=",">
            valid_flag = 0,
            <if test="storeName != null">store_name = #{storeName},</if>
            <if test="queryWord != null">query_word = #{queryWord},</if>
            <if test="type != null">type = #{type},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="position != null">position = #{position},</if>
            <if test="lowerLimit != null">lower_limit = #{lowerLimit},</if>
            <if test="upLimit != null">up_limit = #{upLimit},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="fStoreCode != null">f_store_code = #{fStoreCode},</if>
            <if test="fStoreName != null">f_store_name = #{fStoreName},</if>
            <if test="levelName != null">level_name = #{levelName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="area != null">area = #{area},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveDepartmentById" parameterType="Long">
        delete from leave_department where id = #{id}
    </delete>

    <delete id="deleteLeaveDepartmentByIds" parameterType="String">
        delete from leave_department where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
