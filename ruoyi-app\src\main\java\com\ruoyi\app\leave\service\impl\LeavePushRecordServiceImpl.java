package com.ruoyi.app.leave.service.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeavePushRecordMapper;
import com.ruoyi.app.leave.domain.LeavePushRecord;
import com.ruoyi.app.leave.service.ILeavePushRecordService;
import com.ruoyi.app.leave.enums.LeavePushStatusEnum;

/**
 * 出门证推送记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Service
public class LeavePushRecordServiceImpl implements ILeavePushRecordService 
{
    @Autowired
    private LeavePushRecordMapper leavePushRecordMapper;

    /**
     * 查询出门证推送记录
     * 
     * @param id 出门证推送记录ID
     * @return 出门证推送记录
     */
    @Override
    public LeavePushRecord selectLeavePushRecordById(Long id)
    {
        return leavePushRecordMapper.selectLeavePushRecordById(id);
    }

    /**
     * 查询出门证推送记录列表
     * 
     * @param leavePushRecord 出门证推送记录
     * @return 出门证推送记录
     */
    @Override
    public List<LeavePushRecord> selectLeavePushRecordList(LeavePushRecord leavePushRecord)
    {
        return leavePushRecordMapper.selectLeavePushRecordList(leavePushRecord);
    }

    /**
     * 新增出门证推送记录
     * 
     * @param leavePushRecord 出门证推送记录
     * @return 结果
     */
    @Override
    public int insertLeavePushRecord(LeavePushRecord leavePushRecord)
    {
        return leavePushRecordMapper.insertLeavePushRecord(leavePushRecord);
    }

    /**
     * 修改出门证推送记录
     * 
     * @param leavePushRecord 出门证推送记录
     * @return 结果
     */
    @Override
    public int updateLeavePushRecord(LeavePushRecord leavePushRecord)
    {
        return leavePushRecordMapper.updateLeavePushRecord(leavePushRecord);
    }

    /**
     * 批量删除出门证推送记录
     * 
     * @param ids 需要删除的出门证推送记录ID
     * @return 结果
     */
    @Override
    public int deleteLeavePushRecordByIds(Long[] ids)
    {
        return leavePushRecordMapper.deleteLeavePushRecordByIds(ids);
    }

    /**
     * 删除出门证推送记录信息
     * 
     * @param id 出门证推送记录ID
     * @return 结果
     */
    @Override
    public int deleteLeavePushRecordById(Long id)
    {
        return leavePushRecordMapper.deleteLeavePushRecordById(id);
    }

    /**
     * 查询待重试的推送记录
     * 
     * @return 待重试的推送记录列表
     */
    @Override
    public List<LeavePushRecord> selectRetryPushRecords()
    {
        return leavePushRecordMapper.selectRetryPushRecords();
    }

    /**
     * 记录推送失败
     * 
     * @param businessNo 业务单号
     * @param type 业务类型
     * @param failReason 失败原因
     * @return 结果
     */
    @Override
    public int recordPushFailure(String businessNo, Integer type, String failReason)
    {
        LeavePushRecord record = new LeavePushRecord();
        record.setBusinessNo(businessNo);
        record.setType(type);
        record.setPushStatus(LeavePushStatusEnum.FAIL.getCode()); // 推送失败
        record.setRetryCount(0);
        record.setFailReason(failReason);
        // 设置下次重试时间为5分钟后
        Date nextRetryTime = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
        record.setNextRetryTime(nextRetryTime.toString());
        return leavePushRecordMapper.insertLeavePushRecord(record);
    }

    /**
     * 更新推送状态
     * 
     * @param id 记录ID
     * @param pushStatus 推送状态
     * @return 结果
     */
    @Override
    public int updatePushStatus(Long id, Integer pushStatus)
    {
        LeavePushRecord record = new LeavePushRecord();
        record.setId(id);
        record.setPushStatus(pushStatus);
        if (pushStatus.equals(LeavePushStatusEnum.FAIL.getCode())) { // 推送失败
            record.setRetryCount(record.getRetryCount() + 1);
            // 设置下次重试时间为5分钟后
            Date nextRetryTime = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
            record.setNextRetryTime(nextRetryTime.toString());
        }
        return leavePushRecordMapper.updateLeavePushRecord(record);
    }
} 