<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeavePushRecordMapper">
    
    <resultMap type="LeavePushRecord" id="LeavePushRecordResult">
        <result property="id"    column="id"    />
        <result property="businessNo"    column="business_no"    />
        <result property="type"    column="type"    />
        <result property="pushStatus"    column="push_status"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="failReason"    column="fail_reason"    />
        <result property="nextRetryTime"    column="next_retry_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectLeavePushRecordVo">
        select id, business_no, type, push_status, retry_count, fail_reason, next_retry_time, create_time, create_by, update_time, update_by from leave_push_record
    </sql>

    <select id="selectLeavePushRecordList" parameterType="LeavePushRecord" resultMap="LeavePushRecordResult">
        <include refid="selectLeavePushRecordVo"/>
        <where>  
            <if test="businessNo != null  and businessNo != ''"> and business_no = #{businessNo}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="pushStatus != null "> and push_status = #{pushStatus}</if>
            <if test="retryCount != null "> and retry_count = #{retryCount}</if>
            <if test="failReason != null  and failReason != ''"> and fail_reason = #{failReason}</if>
            <if test="nextRetryTime != null  and nextRetryTime != ''"> and next_retry_time = #{nextRetryTime}</if>
        </where>
    </select>
    
    <select id="selectLeavePushRecordById" parameterType="Long" resultMap="LeavePushRecordResult">
        <include refid="selectLeavePushRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLeavePushRecord" parameterType="LeavePushRecord" useGeneratedKeys="true" keyProperty="id">
        insert into leave_push_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessNo != null">business_no,</if>
            <if test="type != null">type,</if>
            <if test="pushStatus != null">push_status,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="failReason != null">fail_reason,</if>
            <if test="nextRetryTime != null">next_retry_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessNo != null">#{businessNo},</if>
            <if test="type != null">#{type},</if>
            <if test="pushStatus != null">#{pushStatus},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="failReason != null">#{failReason},</if>
            <if test="nextRetryTime != null">#{nextRetryTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateLeavePushRecord" parameterType="LeavePushRecord">
        update leave_push_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessNo != null">business_no = #{businessNo},</if>
            <if test="type != null">type = #{type},</if>
            <if test="pushStatus != null">push_status = #{pushStatus},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="failReason != null">fail_reason = #{failReason},</if>
            <if test="nextRetryTime != null">next_retry_time = #{nextRetryTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeavePushRecordById" parameterType="Long">
        delete from leave_push_record where id = #{id}
    </delete>

    <delete id="deleteLeavePushRecordByIds" parameterType="String">
        delete from leave_push_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectRetryPushRecords" resultMap="LeavePushRecordResult">
    <include refid="selectLeavePushRecordVo"/>
    where push_status = 2
    and retry_count &lt; 3
    and next_retry_time &lt;= now()
</select>

</mapper> 