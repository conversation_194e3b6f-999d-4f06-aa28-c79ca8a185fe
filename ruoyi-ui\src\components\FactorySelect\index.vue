<template>
  <el-dialog
    title="选择分厂"
    :visible.sync="visible"
    width="600px"
    append-to-body
    @close="handleClose"
  >
    <el-table
      v-loading="loading"
      :data="factoryList"
      @row-click="handleRowClick"
      highlight-current-row
    >
      <el-table-column
        type="radio"
        width="55"
      />
      <el-table-column
        label="分厂编码"
        prop="code"
        width="120"
      />
      <el-table-column
        label="分厂名称"
        prop="info"
      />
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getFactoryList } from "@/api/dgcb/common/common";

export default {
  name: "FactorySelect",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      factoryList: [],
      selectedFactory: null
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.getList();
      }
    }
  },
  methods: {
    getList() {
      this.loading = true;
      getFactoryList().then(response => {
        this.factoryList = response.data;
        this.loading = false;
      });
    },
    handleRowClick(row) {
      this.selectedFactory = row;
    },
    handleClose() {
      this.$emit('update:visible', false);
      this.selectedFactory = null;
    },
    handleConfirm() {
      if (!this.selectedFactory) {
        this.$message.warning('请选择分厂');
        return;
      }
      this.$emit('select', this.selectedFactory);
      this.handleClose();
    }
  }
};
</script>
