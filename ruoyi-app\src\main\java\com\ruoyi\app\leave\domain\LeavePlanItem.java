package com.ruoyi.app.leave.domain;

import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;

@Getter
@Setter
//原计量物资类
public class LeavePlanItem {

    /** 物资表ID */
    private Integer id;

    /** 物资名称 */
    private String materialName;

    /** 物资规格 */
    private String materialSpec;

    /** 单位 */
    private String unit;

    /** 数量 */
    private BigDecimal count;

    /** 物资状态   1 正常 2 确认出厂 3 确认进厂 4收货完成，5门卫接收部分，6分厂接收部分 */
    private int fpFlag;

    /** 出门时间 */
    private String outDate;

    /** 进门时间 */
    private String inDate;

    /** 收货时间 */
    private String shDate;

    /** 出门地点*/
    private String outDoor;

    /** 进门地点 */
    private String inDoor;

    /** 接收者 */
    private String receiver;

    /** 门卫本次接收数量 */
    private Integer mwshCount;

    /** 门卫总共接收数量 */
    private Integer mwshCcountEd;



}
