package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.LPassThItemT;

/**
 * 退货申请物资明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface LPassThItemTMapper 
{
    /**
     * 查询退货申请物资明细
     * 
     * @param id 退货申请物资明细ID
     * @return 退货申请物资明细
     */
    public LPassThItemT selectLPassThItemTById(Long id);

    /**
     * 查询退货申请物资明细列表
     * 
     * @param lPassThItemT 退货申请物资明细
     * @return 退货申请物资明细集合
     */
    public List<LPassThItemT> selectLPassThItemTList(LPassThItemT lPassThItemT);

    /**
     * 新增退货申请物资明细
     * 
     * @param lPassThItemT 退货申请物资明细
     * @return 结果
     */
    public int insertLPassThItemT(LPassThItemT lPassThItemT);

    /**
     * 修改退货申请物资明细
     * 
     * @param lPassThItemT 退货申请物资明细
     * @return 结果
     */
    public int updateLPassThItemT(LPassThItemT lPassThItemT);

    /**
     * 删除退货申请物资明细
     * 
     * @param id 退货申请物资明细ID
     * @return 结果
     */
    public int deleteLPassThItemTById(Long id);

    /**
     * 批量删除退货申请物资明细
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteLPassThItemTByIds(Long[] ids);

    /**
     * 根据主表ID删除明细
     * 
     * @param pid 主表ID
     * @return 结果
     */
    public int deleteByPid(Long pid);
}
