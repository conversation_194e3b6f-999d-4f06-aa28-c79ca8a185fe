package com.ruoyi.web.controller.leave;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.leave.domain.LPassFhT;
import com.ruoyi.app.leave.service.ILPassFhTService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出厂返回主Controller
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@RestController
@RequestMapping("/web/leave/passFh")
public class WebLPassFhTController extends BaseController
{
    @Autowired
    private ILPassFhTService lPassFhTService;

    /**
     * 查询出厂返回主列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LPassFhT lPassFhT)
    {
        startPage();
        List<LPassFhT> list = lPassFhTService.selectLPassFhTList(lPassFhT);
        return getDataTable(list);
    }

    /**
     * 导出出厂返回主列表
     */
    @Log(title = "出厂返回主", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LPassFhT lPassFhT)
    {
        List<LPassFhT> list = lPassFhTService.selectLPassFhTList(lPassFhT);
        ExcelUtil<LPassFhT> util = new ExcelUtil<LPassFhT>(LPassFhT.class);
        return util.exportExcel(list, "passFh");
    }

    /**
     * 获取出厂返回主详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lPassFhTService.selectLPassFhTById(id));
    }

    /**
     * 新增出厂返回主
     */
    @Log(title = "出厂返回主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LPassFhT lPassFhT)
    {
        return toAjax(lPassFhTService.insertLPassFhT(lPassFhT));
    }

    /**
     * 修改出厂返回主
     */
    @Log(title = "出厂返回主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LPassFhT lPassFhT)
    {
        return toAjax(lPassFhTService.updateLPassFhT(lPassFhT));
    }

    /**
     * 删除出厂返回主
     */
    @Log(title = "出厂返回主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lPassFhTService.deleteLPassFhTByIds(ids));
    }
}
