{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\dimensionalityOverview.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\dimensionalityOverview.vue", "mtime": 1756170476815}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkID0gcmVxdWlyZSgiRTovamF2YV93b3Jrc3BhY2UvbmV3X3dvcmtzcGFjZS94Y3RnL3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlV2lsZGNhcmQuanMiKS5kZWZhdWx0Owp2YXIgX2ludGVyb3BSZXF1aXJlRGVmYXVsdCA9IHJlcXVpcmUoIkU6L2phdmFfd29ya3NwYWNlL25ld193b3Jrc3BhY2UveGN0Zy9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsbC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLWluZGV4LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5mcm9tLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zbGljZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc29ydC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXktYnVmZmVyLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS1idWZmZXIuc2xpY2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLnRvLWZpeGVkLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnBhZC1zdGFydC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5zdGFydHMtd2l0aC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkudWludDgtYXJyYXkuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LmNvcHktd2l0aGluLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5ldmVyeS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuZmlsbC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5maW5kLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5maW5kLWluZGV4LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LmluZGV4LW9mLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5pdGVyYXRvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuam9pbi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkubGFzdC1pbmRleC1vZi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5yZWR1Y2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LnJlZHVjZS1yaWdodC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkucmV2ZXJzZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuc2V0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy50eXBlZC1hcnJheS5zbGljZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuc29tZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuc29ydC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMudHlwZWQtYXJyYXkuc3ViYXJyYXkuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LnRvLWxvY2FsZS1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnR5cGVkLWFycmF5LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZmluZC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZvci1lYWNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQudHlwZWQtYXJyYXkuYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiKTsKdmFyIF90b0NvbnN1bWFibGVBcnJheTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkU6L2phdmFfd29ya3NwYWNlL25ld193b3Jrc3BhY2UveGN0Zy9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy90b0NvbnN1bWFibGVBcnJheS5qcyIpKTsKdmFyIF9kZWZpbmVQcm9wZXJ0eTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkU6L2phdmFfd29ya3NwYWNlL25ld193b3Jrc3BhY2UveGN0Zy9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eS5qcyIpKTsKdmFyIGVjaGFydHMgPSBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKCJlY2hhcnRzIikpOwp2YXIgX2F4aW9zID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJheGlvcyIpKTsKdmFyIFhMU1ggPSBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKCJ4bHN4IikpOwp2YXIgX2RpbWVuc2lvbmFsaXR5ID0gcmVxdWlyZSgiQC9hcGkvdFlqeS9kaW1lbnNpb25hbGl0eSIpOwp2YXIgX2Fuc3dlciA9IHJlcXVpcmUoIkAvYXBpL3RZankvYW5zd2VyIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnRGltZW5zaW9uYWxpdHlPdmVydmlldycsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOaKgOe7j+aMh+agh+aVsOaNrgogICAgICB0ZWNoSW5kaWNhdG9yczogW10sCiAgICAgIGRldGFpbERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBjdXJyZW50SW5kaWNhdG9yOiBudWxsLAogICAgICAvLyBFeGNlbOaWh+S7tuWKoOi9veeKtuaAgQogICAgICBleGNlbExvYWRpbmc6IGZhbHNlLAogICAgICBhZG1pblNob3c6ICIwIiwKICAgICAgLy8g5b2T5YmN5pi+56S655qE6YOo6Zeo77yI55So5LqO5pyI5bqm6LaL5Yq/5Zu+5YiH5o2i77yJCiAgICAgIC8vIGN1cnJlbnRCdXNpbmVzc1VuaXQ6ICfngrzpk4HkuovkuJrpg6gnLCAvLyDlvZPliY3pgInmi6nnmoTkuovkuJrpg6gKICAgICAgLy8gY3VycmVudERlcGFydG1lbnQ6ICcnLAogICAgICAvLyBjdXJyZW50SW5kaWNhdG9yOiAnJywKICAgICAgLy8gYXV0b1N3aXRjaERlcGFydG1lbnQ6IHRydWUsCiAgICAgIC8vIOWumuaXtuWZqAogICAgICB0cmVuZENoYXJ0VGltZXI6IG51bGwsCiAgICAgIC8vIOeUqOS6juWIhuWOguWIh+aNogogICAgICBidXNpbmVzc1VuaXRUaW1lcjogbnVsbCwKICAgICAgLy8g55So5LqO5LqL5Lia6YOo5YiH5o2iCiAgICAgIHNjcm9sbFRpbWVyOiBudWxsLAogICAgICB0YWJsZVNjcm9sbFBhdXNlZDogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuaaguWBnOihqOagvOiHquWKqOa7muWKqAogICAgICBjb21wbGV0aW9uQ2hhcnRUaW1lcjogbnVsbCwKICAgICAgLy8g55So5LqO5a6M5oiQ546H5Zu+6KGo5rua5YqoCiAgICAgIGluZGljYXRvckNhcmRzU2Nyb2xsVGltZXI6IG51bGwsCiAgICAgIC8vIOeUqOS6juaMh+agh+WNoeeJh+a7muWKqAogICAgICBjb21wbGV0aW9uQ2hhcnRTY3JvbGxEaXJlY3Rpb246ICdkb3duJywKICAgICAgLy8g5rua5Yqo5pa55ZCR77yaJ3VwJyDmiJYgJ2Rvd24nCiAgICAgIGNvbXBsZXRpb25DaGFydFNjcm9sbFBhdXNlZDogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuaaguWBnOiHquWKqOa7muWKqAogICAgICBpbmRpY2F0b3JDYXJkc1Njcm9sbFBhdXNlZDogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuaaguWBnOaMh+agh+WNoeeJh+iHquWKqOa7muWKqAoKICAgICAgZW5lcmd5RGVwdFRpbWVyOiBudWxsLAogICAgICAvLyDog73mupDpg6jpl6jliIfmjaLlrprml7blmagKICAgICAgc2Nyb2xsU3BlZWQ6IDUwLAogICAgICAvLyDmu5rliqjpgJ/luqbvvIzmlbDlgLzotorlpKfpgJ/luqbotormhaIKCiAgICAgIC8vIOS6i+S7tuWkhOeQhuWZqOW8leeUqAogICAgICBjaGFydE1vdXNlT3ZlckhhbmRsZXI6IG51bGwsCiAgICAgIGNoYXJ0TW91c2VPdXRIYW5kbGVyOiBudWxsLAogICAgICB0YWJsZU1vdXNlRW50ZXJIYW5kbGVyOiBudWxsLAogICAgICAvLyDooajmoLzpvKDmoIfov5vlhaXlpITnkIblmagKICAgICAgdGFibGVNb3VzZUxlYXZlSGFuZGxlcjogbnVsbCwKICAgICAgLy8g6KGo5qC86byg5qCH56a75byA5aSE55CG5ZmoCgogICAgICAvLyDlvZPliY3pgInmi6nnmoTog73mupDnsbvlnovlkozpg6jpl6gKICAgICAgY3VycmVudEVuZXJneVR5cGU6ICdlbGVjdHJpY2l0eScsCiAgICAgIGN1cnJlbnRFbmVyZ3lEZXB0OiAn54K86ZOB5YiG5Y6CJywKICAgICAgLy8g5L+u5pS55Li65YiG5Y6C5ZCN56ewCgogICAgICAvLyDpg6jpl6jliJfooagKICAgICAgZGVwYXJ0bWVudHM6IFsn54K86ZOB5LqL5Lia6YOoJywgJ+eCvOmSouS6i+S4mumDqCcsICfovafpkqLkuovkuJrpg6gnLCAn6ams56eR5omY6ZKi55CDJywgJ+eJueadv+S6i+S4mumDqCcsICfliqjlipvkuovkuJrpg6gnLCAn54mp5rWB5LqL5Lia6YOoJywgJ+ajgOS/ruS6i+S4mumDqCddLAogICAgICAvLyDmjIfmoIflrozmiJDmg4XlhrXmlbDmja4gLSDku47lm77niYfkuK3mj5Dlj5bnmoTmlbDmja4KICAgICAgY29tcGxldGlvbkRhdGE6IFt7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eCvOmTgeS6i+S4mumDqC3ngrzpk4HliIbljoInLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn57u85ZCI54eD5paZ5q+UJywKICAgICAgICAgIHRhcmdldDogNTEyLjMzLAogICAgICAgICAgYWN0dWFsOiA1MjMuNDUsCiAgICAgICAgICB1bml0OiAna2cvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFs1MjAuMjYsIDUyMy42NywgNTIzLjQ1LCA1MTkuMDZdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJy0xLjU1JScsICctMi4wMCUnLCAnLTIuMTclJywgJy0xLjMxJSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+W3peW6j+iDveiAlycsCiAgICAgICAgICB0YXJnZXQ6IDM2OS45MSwKICAgICAgICAgIGFjdHVhbDogMzY5LjY5LAogICAgICAgICAgdW5pdDogJ2tnQ2UvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFszNjkuNzQsIDM2Ny45OCwgMzY5LjY5LCAzNjMuNzZdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDUlJywgJzAuNTIlJywgJzAuMDYlJywgJzEuNjYlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAnNDAw6auY54KJ5bel5bqP6IO96ICXJywKICAgICAgICAgIHRhcmdldDogNDMuNzAsCiAgICAgICAgICBhY3R1YWw6IDQzLjU2LAogICAgICAgICAgdW5pdDogJ2tnQ2UvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFs0My42NywgNDMuNjcsIDQzLjU2LCA0My42N10sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wNyUnLCAnMC4wNyUnLCAnMC4zMiUnLCAnMC4wNyUnXQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICczNjDpq5jngonlt6Xluo/og73ogJcnLAogICAgICAgICAgdGFyZ2V0OiA0NS4wMSwKICAgICAgICAgIGFjdHVhbDogNDQuODQsCiAgICAgICAgICB1bml0OiAna2dDZS90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzQ1LjAwLCA0NC45MywgNDQuODQsIDQ0Ljk5XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjAyJScsICcwLjA3JScsICcwLjM4JScsICcwLjA0JSddCiAgICAgICAgfV0KICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfngrzpk4HkuovkuJrpg6gt54On57uT5YiG5Y6CJywKICAgICAgICBpbmRpY2F0b3JzOiBbewogICAgICAgICAgbmFtZTogJ+W3peW6j+iDveiAlycsCiAgICAgICAgICB0YXJnZXQ6IDE1NC42NSwKICAgICAgICAgIGFjdHVhbDogMTU0LjU2LAogICAgICAgICAgdW5pdDogJ2tnQ2UvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFsxNTQuNjUsIDE1NC45MSwgMTU0LjU2LCAxNTQuNTddLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDElJywgJzAuMDMlJywgJzAuMDYlJywgJzAuMDUlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn55+/5rij5aKe5bmFJywKICAgICAgICAgIHRhcmdldDogMTYuMDAsCiAgICAgICAgICBhY3R1YWw6IDE1LjgwLAogICAgICAgICAgdW5pdDogJ2tnQ2UvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFsxNS44MiwgMTUuOTQsIDE1LjgwLCAxNS44NV0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC41MCUnLCAnMC4zOCUnLCAnMS4yNSUnLCAnMC45NCUnXQogICAgICAgIH1dCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn54K86ZKi5LqL5Lia6YOoLeS4gOeCvOmSoicsCiAgICAgICAgaW5kaWNhdG9yczogW3sKICAgICAgICAgIG5hbWU6ICfnu7zlkIjnn7PngbDmtojogJcnLAogICAgICAgICAgdGFyZ2V0OiA0Mi45NSwKICAgICAgICAgIGFjdHVhbDogNDIuODksCiAgICAgICAgICB1bml0OiAna2cvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFs0Mi45NCwgNDIuODIsIDQyLjg5LCA0MC41NV0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMiUnLCAnMC4zMCUnLCAnMC4xNCUnLCAnNS41OSUnXQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICfmsKfmsJTmtojogJcnLAogICAgICAgICAgdGFyZ2V0OiA0NC41NiwKICAgICAgICAgIGFjdHVhbDogNDQuNDUsCiAgICAgICAgICB1bml0OiAnbTMvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFs0NC41MCwgNDQuMzQsIDQ0LjQ1LCA0NC4zN10sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4xMyUnLCAnMC40OSUnLCAnMC4yNSUnLCAnMC40MyUnXQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICfnlLXngonlt6Xluo/og73ogJcnLAogICAgICAgICAgdGFyZ2V0OiA1Ny41MCwKICAgICAgICAgIGFjdHVhbDogNTcuMzIsCiAgICAgICAgICB1bml0OiAna2dDZS90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzU3LjM0LCA1Ny4zMywgNTcuMzIsIDU3LjE5XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjI4JScsICcwLjMwJScsICcwLjMxJScsICcwLjU0JSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+mSoumTgeaWmeaUtuW+l+eOhycsCiAgICAgICAgICB0YXJnZXQ6IDkxLjUwLAogICAgICAgICAgYWN0dWFsOiA5MS4zMiwKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbOTEuNTEsIDkxLjMyLCA5MS4zMiwgOTEuMzJdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDElJywgJy0wLjIwJScsICctMC4yMCUnLCAnLTAuMjAlJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eCvOmSouS6i+S4mumDqC3kuozngrzpkqInLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn57u85ZCI55+z54Gw5raI6ICXJywKICAgICAgICAgIHRhcmdldDogNTAuMDAsCiAgICAgICAgICBhY3R1YWw6IDQ4Ljk1LAogICAgICAgICAgdW5pdDogJ2tnL3QnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLAogICAgICAgICAgdmFsdWVzOiBbNDkuOTAsIDQ5LjQwLCA0OC45NSwgNDkuMDBdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMjAlJywgJzEuMDglJywgJzIuMTAlJywgJzIuMDAlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn5rCn5rCU5raI6ICXJywKICAgICAgICAgIHRhcmdldDogNDUuNjUsCiAgICAgICAgICBhY3R1YWw6IDQ1LjI2LAogICAgICAgICAgdW5pdDogJ20zL3QnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLAogICAgICAgICAgdmFsdWVzOiBbNDUuNDksIDQ1LjM3LCA0NS4yNiwgNDUuMzVdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMzUlJywgJzAuMzklJywgJzAuODUlJywgJzAuNjYlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn6L2s54KJ5bel5bqP6IO96ICXJywKICAgICAgICAgIHRhcmdldDogLTI4LjUwLAogICAgICAgICAgYWN0dWFsOiAtMjkuNTIsCiAgICAgICAgICB1bml0OiAna2dDZS90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbMjkuNzgsIDI5LjU3LCAyOS41MiwgMjkuNjFdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJy0wLjk4JScsICctMy43NSUnLCAnLTMuNTglJywgJy0zLjg5JSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+mSoumTgeaWmeaUtuW+l+eOhycsCiAgICAgICAgICB0YXJnZXQ6IDkxLjUwLAogICAgICAgICAgYWN0dWFsOiA5MS4xNiwKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbOTEuNTAsIDkxLjMyLCA5MS4xNiwgOTEuMTVdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJy0wLjIwJScsICctMC4zNyUnLCAnLTAuMzglJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+i9p+mSouS6i+S4mumDqC3nu7zlkIjliKnnlKgnLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn5bqf6YeR6YeR5bGe6YePJywKICAgICAgICAgIHRhcmdldDogMC42NSwKICAgICAgICAgIGFjdHVhbDogMC42NCwKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzAuNjUsIDAuNTMsIDAuNjQsIDAuNDldLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzE4LjQwJScsICcxLjU0JScsICctMjYuMTUlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn55S16ICXJywKICAgICAgICAgIHRhcmdldDogMjIuMzMsCiAgICAgICAgICBhY3R1YWw6IDIzLjgxLAogICAgICAgICAgdW5pdDogJ2tXaC90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzIyLjMzLCAyMy4wNywgMjMuODEsIDIxLjE5XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjQ1JScsICctMy4zMSUnLCAnLTYuNjIlJywgJzUuMTElJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+i9p+mSouS6i+S4mumDqC3kuIDovafpkqInLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn54Ot6L2n57u85ZCI5oiQ5p2Q546HJywKICAgICAgICAgIHRhcmdldDogOTYuMzUsCiAgICAgICAgICBhY3R1YWw6IDk2LjIyLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5Ni4yMywgOTYuMjUsIDk2LjIyLCA5Ni4yNF0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnLTAuMTIlJywgJy0wLjEwJScsICctMC4xMyUnLCAnLTAuMTElJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXJywKICAgICAgICAgIHRhcmdldDogNTguNTUsCiAgICAgICAgICBhY3R1YWw6IDU4LjUxLAogICAgICAgICAgdW5pdDogJ2tnQ2UvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFs1OC40NywgNTguNTIsIDU4LjUxLCA1OC40NV0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4xNCUnLCAnMC4wNSUnLCAnMC4wNyUnLCAnMC4xNyUnXQogICAgICAgIH1dCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn6L2n6ZKi5LqL5Lia6YOoLeS6jOi9p+mSoicsCiAgICAgICAgaW5kaWNhdG9yczogW3sKICAgICAgICAgIG5hbWU6ICfng63ovafnu7zlkIjmiJDmnZDnjoco5aSn5qOSKScsCiAgICAgICAgICB0YXJnZXQ6IDk1LjM3LAogICAgICAgICAgYWN0dWFsOiA5NS4zNywKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbOTUuMzcsIDk1LjIyLCA5NS4zNywgOTUuMzddLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJy0wLjE1JScsICcwLjAwJScsICcwLjAwJSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+eDrei9p+e7vOWQiOaIkOadkOeOhyjlsI/mo5IpJywKICAgICAgICAgIHRhcmdldDogOTYuNTYsCiAgICAgICAgICBhY3R1YWw6IDk2LjU2LAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5Ni4zOSwgOTYuNTYsIDk2LjU2LCA5Ni41Nl0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnLTAuMTclJywgJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXKOWkp+ajkiknLAogICAgICAgICAgdGFyZ2V0OiA2Ny4yOCwKICAgICAgICAgIGFjdHVhbDogNzIuNDksCiAgICAgICAgICB1bml0OiAna2dDZS90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzcxLjM1LCA3My44MCwgNzIuNDksIDY2LjI0XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWyctMC45MyUnLCAnMC43NSUnLCAnMC4wMCUnLCAnLTAuMjUlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXKOWwj+ajkiknLAogICAgICAgICAgdGFyZ2V0OiA0Mi4wNSwKICAgICAgICAgIGFjdHVhbDogNDIuMDIsCiAgICAgICAgICB1bml0OiAna2dDZS90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzQyLjAzLCA0Mi4wNSwgNDIuMDIsIDQ1LjY4XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjA1JScsICcwLjA1JScsICcwLjA1JScsICcwLjM0JSddCiAgICAgICAgfV0KICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfovafpkqLkuovkuJrpg6gt5LiJ6L2n6ZKiJywKICAgICAgICBpbmRpY2F0b3JzOiBbewogICAgICAgICAgbmFtZTogJ+eDrei9p+e7vOWQiOaIkOadkOeOhycsCiAgICAgICAgICB0YXJnZXQ6IDk2LjA0LAogICAgICAgICAgYWN0dWFsOiA5NS41MCwKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbOTUuNzYsIDk2LjMwLCA5NS41MCwgOTUuNTFdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJy0wLjI4JScsICcwLjQ0JScsICctMC41MCUnLCAnLTAuNTAlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXJywKICAgICAgICAgIHRhcmdldDogNTYuMzEsCiAgICAgICAgICBhY3R1YWw6IDU0LjY3LAogICAgICAgICAgdW5pdDogJ2tnQ2UvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFs1NS4yNiwgNTYuMzQsIDU0LjY3LCA1NS4xOV0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnLTAuNzklJywgJzAuNzElJywgJy0xLjMzJScsICctMS4xOCUnXQogICAgICAgIH1dCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn6L2n6ZKi5LqL5Lia6YOoLeeJueauiumSoui9p+adkCcsCiAgICAgICAgaW5kaWNhdG9yczogW3sKICAgICAgICAgIG5hbWU6ICfng63ovafpkqLmnZDlt6Xluo/og73ogJcnLAogICAgICAgICAgdGFyZ2V0OiA2Ny4wNCwKICAgICAgICAgIGFjdHVhbDogNjguNjQsCiAgICAgICAgICB1bml0OiAna2dDZS90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzY3LjM1LCA2NC4wOSwgNjguNjQsIDY0Ljc3XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjQ2JScsICctMy4yNiUnLCAnMC4wMCUnLCAnLTAuMjMlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn57u85ZCI5oiQ5p2Q546HJywKICAgICAgICAgIHRhcmdldDogOTYuNzMsCiAgICAgICAgICBhY3R1YWw6IDk2LjczLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5Ni43MywgOTYuNzksIDk2LjczLCA5Ni40NV0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wNiUnLCAnMC4wMCUnLCAnLTAuMjglJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+i9p+mSouS6i+S4mumDqC3mo5LmnZDovafliLbljoInLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn54Ot6L2n6ZKi5p2Q5bel5bqP6IO96ICXKOajkuaJgSknLAogICAgICAgICAgdGFyZ2V0OiA1Ni45MywKICAgICAgICAgIGFjdHVhbDogNjEuODEsCiAgICAgICAgICB1bml0OiAna2dDZS90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzY2LjE0LCA2MC4wMCwgNjEuODEsIDU5Ljk2XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWyctMC45MSUnLCAnLTEuMzUlJywgJzAuMDAlJywgJy0wLjI0JSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+eDrei9p+mSouadkOW3peW6j+iDveiAlyjlpKfnm5gpJywKICAgICAgICAgIHRhcmdldDogNTcuMDgsCiAgICAgICAgICBhY3R1YWw6IDYxLjI4LAogICAgICAgICAgdW5pdDogJ2tnQ2UvdCcsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogZmFsc2UsCiAgICAgICAgICB2YWx1ZXM6IFs2NC4zMCwgNjAuMjksIDYxLjI4LCA2MC4wMl0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnLTAuMTklJywgJy0wLjE1JScsICcwLjAwJScsICctMC4yNiUnXQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICfnu7zlkIjmiJDmnZDnjoco5qOS5omB6L2n5p2QKScsCiAgICAgICAgICB0YXJnZXQ6IDk2LjQ1LAogICAgICAgICAgYWN0dWFsOiA5Ni4xMiwKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbOTYuMTQsIDk2LjExLCA5Ni4xMiwgOTYuMDNdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJy0wLjMxJScsICctMC4zMyUnLCAnMC4wMCUnLCAnLTAuMjklJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn57u85ZCI5oiQ5p2Q546HKOWkp+ebmOWNtyknLAogICAgICAgICAgdGFyZ2V0OiA5NS44NSwKICAgICAgICAgIGFjdHVhbDogOTUuODQsCiAgICAgICAgICB1bml0OiAnJScsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogdHJ1ZSwKICAgICAgICAgIHZhbHVlczogWzk1Ljg2LCA5NS45MCwgOTUuODQsIDk1Ljg3XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjAxJScsICcwLjA0JScsICcwLjAwJScsICcwLjAyJSddCiAgICAgICAgfV0KICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfovafpkqLkuovkuJrpg6gt5qOS57q/5p2Q5rex5Yqg5belKOajkiknLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn57u85ZCI5oiQ5p2Q546HJywKICAgICAgICAgIHRhcmdldDogOTIuNjAsCiAgICAgICAgICBhY3R1YWw6IDkyLjYwLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5Mi42MCwgOTIuNjAsIDkyLjYwLCA5Mi42MF0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnXQogICAgICAgIH1dCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn6L2n6ZKi5LqL5Lia6YOoLeajkue6v+adkOa3seWKoOW3pSjnur8pJywKICAgICAgICBpbmRpY2F0b3JzOiBbewogICAgICAgICAgbmFtZTogJ+aOp+e6v+adkOe7vOWQiOaIkOadkOeOhycsCiAgICAgICAgICB0YXJnZXQ6IDk4LjU1LAogICAgICAgICAgYWN0dWFsOiA5OC41NiwKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbOTguNTUsIDk4LjU1LCA5OC41NiwgOTguNTVdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+i9p+mSouS6i+S4mumDqC3nur/mnZDmt7HliqDlt6UnLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn5Ya36ZWm5p2Q57u85ZCI5oiQ5p2Q546HJywKICAgICAgICAgIHRhcmdldDogOTYuMzYsCiAgICAgICAgICBhY3R1YWw6IDk2LjAyLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5Ni4zNiwgOTYuMzYsIDk2LjAyLCA5NC40NF0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnLCAnLTEuOTQlJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+mprOenkeaJmOmSoueQgy3pqaznp5HmiZjpkqLnkIMnLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn57u85ZCI5oiQ5p2Q546HJywKICAgICAgICAgIHRhcmdldDogOTMuMTksCiAgICAgICAgICBhY3R1YWw6IDkzLjYxLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5My4xMywgOTMuNTQsIDkzLjYxLCA5My44MF0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnLTAuMDYlJywgJzAuNDIlJywgJzAuMDAlJywgJzAuMjAlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn5rCn5rCU5raI6ICXJywKICAgICAgICAgIHRhcmdldDogNDQuNjQsCiAgICAgICAgICBhY3R1YWw6IDQ0LjYzLAogICAgICAgICAgdW5pdDogJ20zL3QnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLAogICAgICAgICAgdmFsdWVzOiBbNDQuNjQsIDQ0LjY0LCA0NC42MywgNDQuNjRdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eJueadv+S6i+S4mumDqC3nibnpkqLngrzpkqLliIbljoInLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn5rCn5rCU5raI6ICXJywKICAgICAgICAgIHRhcmdldDogNDQuNjQsCiAgICAgICAgICBhY3R1YWw6IDQ0LjYzLAogICAgICAgICAgdW5pdDogJ20zL3QnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLAogICAgICAgICAgdmFsdWVzOiBbNDQuNjQsIDQ0LjY0LCA0NC42MywgNDQuNjRdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn6L2s54KJ5bel5bqP6IO96ICXJywKICAgICAgICAgIHRhcmdldDogLTI4LjUwLAogICAgICAgICAgYWN0dWFsOiAtMjkuOTEsCiAgICAgICAgICB1bml0OiAna2dDZS90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbMjguOTMsIDI5LjY3LCAyOS45MSwgMjkuNTVdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJy0wLjk4JScsICctMy43NSUnLCAnLTMuNTglJywgJy0zLjg5JSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+mSoumTgeaWmeaUtuW+l+eOhycsCiAgICAgICAgICB0YXJnZXQ6IDkxLjYwLAogICAgICAgICAgYWN0dWFsOiA5MS4yNywKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbOTEuODAsIDkxLjMxLCA5MS4yNywgOTEuMjddLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMjIlJywgJy0wLjMzJScsICctMC4zNyUnLCAnLTAuMzclJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn57u85ZCI55+z54Gw5raI6ICXJywKICAgICAgICAgIHRhcmdldDogNDMuNjcsCiAgICAgICAgICBhY3R1YWw6IDQ2LjA3LAogICAgICAgICAgdW5pdDogJ2tnL3QnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLAogICAgICAgICAgdmFsdWVzOiBbNDYuMDEsIDQ3LjE5LCA0Ni4wNywgNDMuOTddLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzIuODQlJywgJzMuMjclJywgJzIuMTQlJywgJzIuMDclJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eJueadv+S6i+S4mumDqC3kuK3mnb/liIbljoInLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn57u85ZCI5ZG95Lit546HJywKICAgICAgICAgIHRhcmdldDogOTguNjIsCiAgICAgICAgICBhY3R1YWw6IDk4LjYzLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5OC42MywgOTguNjUsIDk4LjYzLCA5OC42M10sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wMiUnLCAnMC4wMCUnLCAnMC4wMCUnXQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICfnu7zlkIjmiJDmnZDnjocnLAogICAgICAgICAgdGFyZ2V0OiA5Mi41NSwKICAgICAgICAgIGFjdHVhbDogOTIuMDQsCiAgICAgICAgICB1bml0OiAnJScsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogdHJ1ZSwKICAgICAgICAgIHZhbHVlczogWzkyLjYzLCA5Mi4wMywgOTIuMDQsIDkyLjY1XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjA5JScsICctMC41MSUnLCAnLTAuNTElJywgJzAuMDIlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn5pW05a6i5oi35Lqk5LuY546HJywKICAgICAgICAgIHRhcmdldDogOTguNzUsCiAgICAgICAgICBhY3R1YWw6IDk4Ljc4LAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5OC43NSwgOTguNzcsIDk4Ljc4LCA5OC43NV0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wMiUnLCAnMC4wMCUnLCAnMC4wMCUnXQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICfng63ovaflt6Xluo/og73ogJcnLAogICAgICAgICAgdGFyZ2V0OiA0NS4wMiwKICAgICAgICAgIGFjdHVhbDogNDMuMzIsCiAgICAgICAgICB1bml0OiAna2dDZS90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzQ0LjYwLCA0NC4xNSwgNDMuMzIsIDQzLjgwXSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWyctMC45MyUnLCAnLTEuMjUlJywgJy0xLjY4JScsICctMC43MCUnXQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICfng63oo4Xmr5QnLAogICAgICAgICAgdGFyZ2V0OiA3NS4wMCwKICAgICAgICAgIGFjdHVhbDogNzUuODUsCiAgICAgICAgICB1bml0OiAnJScsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogdHJ1ZSwKICAgICAgICAgIHZhbHVlczogWzc1LjQwLCA3Ny42NCwgNzUuODUsIDc0LjA1XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjUzJScsICcyLjE4JScsICcwLjAwJScsICctMS45NSUnXQogICAgICAgIH1dCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn54m55p2/5LqL5Lia6YOoLeWOmuadv+WIhuWOgicsCiAgICAgICAgaW5kaWNhdG9yczogW3sKICAgICAgICAgIG5hbWU6ICfnu7zlkIjlkb3kuK3njocnLAogICAgICAgICAgdGFyZ2V0OiA5Ny40OSwKICAgICAgICAgIGFjdHVhbDogOTcuMjcsCiAgICAgICAgICB1bml0OiAnJScsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogdHJ1ZSwKICAgICAgICAgIHZhbHVlczogWzk3LjQ5LCA5Ny41MywgOTcuMjcsIDk3LjUyXSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjAwJScsICcwLjA0JScsICctMC4yNiUnLCAnMC4wNSUnXQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICfnu7zlkIjmiJDmnZDnjocnLAogICAgICAgICAgdGFyZ2V0OiA5MC45MSwKICAgICAgICAgIGFjdHVhbDogOTAuNzYsCiAgICAgICAgICB1bml0OiAnJScsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogdHJ1ZSwKICAgICAgICAgIHZhbHVlczogWzkwLjQxLCA5MC43OSwgOTAuNzYsIDkwLjc4XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWyctMC41NSUnLCAnMC4zMiUnLCAnLTAuMjYlJywgJzAuMDIlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn5pW05a6i5oi35Lqk5LuY546HJywKICAgICAgICAgIHRhcmdldDogOTYuMzQsCiAgICAgICAgICBhY3R1YWw6IDk2LjM0LAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5Ni4zNywgOTYuMzUsIDk2LjM0LCA5Ni4zMV0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMyUnLCAnLTAuMDIlJywgJzAuMDAlJywgJy0wLjAzJSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+eDrei9p+W3peW6j+iDveiAlycsCiAgICAgICAgICB0YXJnZXQ6IDQ4LjYyLAogICAgICAgICAgYWN0dWFsOiA0NS44NSwKICAgICAgICAgIHVuaXQ6ICdrZ0NlL3QnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLAogICAgICAgICAgdmFsdWVzOiBbNDYuMjcsIDQ2LjAxLCA0NS44NSwgNDcuMTFdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJy0yLjc5JScsICctMi41NiUnLCAnLTIuMzQlJywgJy0xLjU0JSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+eDreijheavlCgyMDDihIMpJywKICAgICAgICAgIHRhcmdldDogNTAuMDAsCiAgICAgICAgICBhY3R1YWw6IDMxLjIzLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs1MC42MCwgNTEuMjgsIDMxLjIzLCA1MC4yOF0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMS4yMCUnLCAnMi41NiUnLCAnMC4wMCUnLCAnLTEuNTYlJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eJueadv+S6i+S4mumDqC3pkqLmnZDmt7HliqDlt6UnLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn5pW05a6i5oi35Lqk5LuY546HJywKICAgICAgICAgIHRhcmdldDogOTkuMTEsCiAgICAgICAgICBhY3R1YWw6IDk5LjEyLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5OS4xMSwgOTkuMTAsIDk5LjEyLCA5OS4xMl0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnLTAuMDElJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn57u85ZCI5ZG95Lit546HJywKICAgICAgICAgIHRhcmdldDogOTkuNzMsCiAgICAgICAgICBhY3R1YWw6IDk5Ljc1LAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IHRydWUsCiAgICAgICAgICB2YWx1ZXM6IFs5OS43MywgOTkuNzQsIDk5Ljc1LCA5OS43NF0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnLTAuMDElJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+WKqOWKm+S6i+S4mumDqC3ng63nlLUnLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn5qCH54Wk5Lqn5rG9546HJywKICAgICAgICAgIHRhcmdldDogMTAuODUsCiAgICAgICAgICBhY3R1YWw6IDEwLjkwLAogICAgICAgICAgdW5pdDogJ3QvdENlJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbMTAuODcsIDEwLjg5LCAxMC45MCwgMTAuOTJdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMTklJywgJzAuMjAlJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+WKqOWKm+S6i+S4mumDqC3kvpvnlLXlt6XljLonLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn5L6b55S15Yqf546H5Zug5pWwJywKICAgICAgICAgIHRhcmdldDogOTUuMDAsCiAgICAgICAgICBhY3R1YWw6IDk4LjAwLAogICAgICAgICAgdW5pdDogJyUoOTUuMy0xMDApJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbOTguNjYsIDk4LjY2LCA5OC4wMCwgOTguMDBdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzMuMjAlJywgJzMuMjAlJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+WKqOWKm+S6i+S4mumDqC3msLTlpITnkIbliIbljoInLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn5ZCo6ZKi6L2v5rC05rC0JywKICAgICAgICAgIHRhcmdldDogMS42MiwKICAgICAgICAgIGFjdHVhbDogMS42MywKICAgICAgICAgIHVuaXQ6ICdtMy90JywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzEuNjIsIDEuNjEsIDEuNjMsIDEuNjldLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuNjIlJywgJy0wLjYyJScsICcwLjAwJScsICcwLjYyJSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+WQqOmSoueDreawtOWkhOeQhumHjycsCiAgICAgICAgICB0YXJnZXQ6IDIxLjIwLAogICAgICAgICAgYWN0dWFsOiAyMC4yNSwKICAgICAgICAgIHVuaXQ6ICd0L3TpkqInLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLAogICAgICAgICAgdmFsdWVzOiBbMTkuODksIDIwLjE0LCAyMC4yNSwgMjAuMjhdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJy02LjE5JScsICctMy41NyUnLCAnMC4wMCUnLCAnMS40MCUnXQogICAgICAgIH1dCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5Yqo5Yqb5LqL5Lia6YOoLeWItuawp+WIhuWOgicsCiAgICAgICAgaW5kaWNhdG9yczogW3sKICAgICAgICAgIG5hbWU6ICfmsKfmsJTlsLHogJfnjocnLAogICAgICAgICAgdGFyZ2V0OiAwLjAyLAogICAgICAgICAgYWN0dWFsOiAwLjAwLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLAogICAgICAgICAgdmFsdWVzOiBbMC4wMCwgMC4wMCwgMC4wMCwgMC4wMF0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnXQogICAgICAgIH1dCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5Yqo5Yqb5LqL5Lia6YOoLeeFpOawlOWIhuWOgicsCiAgICAgICAgaW5kaWNhdG9yczogW3sKICAgICAgICAgIG5hbWU6ICfpq5jngonnhaTmsJTlsLHogJfnjocnLAogICAgICAgICAgdGFyZ2V0OiAwLjAyLAogICAgICAgICAgYWN0dWFsOiAwLjAwLAogICAgICAgICAgdW5pdDogJyUnLAogICAgICAgICAgaXNIaWdoZXJCZXR0ZXI6IGZhbHNlLAogICAgICAgICAgdmFsdWVzOiBbMC4wMCwgMC4wMCwgMC4wMCwgMC4wMF0sCiAgICAgICAgICBjb21wbGV0aW9uUmF0ZXM6IFsnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnLCAnMC4wMCUnXQogICAgICAgIH1dCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn54mp5rWB5LqL5Lia6YOoLeWCqOi/kOWFrOWPuCcsCiAgICAgICAgaW5kaWNhdG9yczogW3sKICAgICAgICAgIG5hbWU6ICczNjDmt7fljIDnn7/msLTliIblkIjmoLznjocnLAogICAgICAgICAgdGFyZ2V0OiA5MC41NiwKICAgICAgICAgIGFjdHVhbDogOTEuMzIsCiAgICAgICAgICB1bml0OiAnJScsCiAgICAgICAgICBpc0hpZ2hlckJldHRlcjogdHJ1ZSwKICAgICAgICAgIHZhbHVlczogWzkwLjYzLCA5MC42OCwgOTEuMzIsIDkwLjY4XSwKICAgICAgICAgIGNvbXBsZXRpb25SYXRlczogWycwLjA3JScsICcwLjA1JScsICcwLjAwJScsICcwLjAwJSddCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+a3t+WMgOefv+eos+WumueOhycsCiAgICAgICAgICB0YXJnZXQ6IDk3LjgyLAogICAgICAgICAgYWN0dWFsOiA5Ny44OSwKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiB0cnVlLAogICAgICAgICAgdmFsdWVzOiBbOTcuODMsIDk4LjAxLCA5Ny44OSwgOTguMDFdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMTglJywgJzAuMTclJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+ajgOS/ruS6i+S4mumDqC3mo4Dkv67liIbljoInLAogICAgICAgIGluZGljYXRvcnM6IFt7CiAgICAgICAgICBuYW1lOiAn54Ot5L+u5py6546HJywKICAgICAgICAgIHRhcmdldDogMC4xMCwKICAgICAgICAgIGFjdHVhbDogMC4wMCwKICAgICAgICAgIHVuaXQ6ICclJywKICAgICAgICAgIGlzSGlnaGVyQmV0dGVyOiBmYWxzZSwKICAgICAgICAgIHZhbHVlczogWzAuMDAsIDAuMDAsIDAuMDAsIDAuMDBdLAogICAgICAgICAgY29tcGxldGlvblJhdGVzOiBbJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJywgJzAuMDAlJ10KICAgICAgICB9XQogICAgICB9XSwKICAgICAgY29tcGxldGlvbkRhdGExOiBbewogICAgICAgIGRlcGFydG1lbnQ6ICfpkpnkuJrliIbljoInCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn55+/5rij5b6u57KJJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eDp+e7k+WIhuWOgi0xI+eDp+e7kycKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfng6fnu5PliIbljoItMiPng6fnu5MnCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn54K86ZOB5YiG5Y6CLTEj6auY54KJJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eCvOmTgeWIhuWOgi0yI+mrmOeCiScKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfngrzpk4HliIbljoItMyPpq5jngoknCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn54K86ZOB5YiG5Y6CLeWwj+WWt+eFpCcKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfngrzpk4HliIbljoIt5aSn5Za354WkJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+S4gOeCvOmSoicKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfkuozngrzpkqInCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5LiA6L2n6ZKiJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+S6jOi9py3lpKfmo5InCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5LqM6L2nLeWwj+ajkicKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfnibnmnb/ngrzpkqInCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAnMzUwMOS4readvycKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICc0MzAw5Y6a5p2/JwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJzQzMDDmsLTlpITnkIYnCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn54Ot5aSE55CGJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+mrmOe6v+WIhuWOgicKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfnur/mnZDmt7HliqDlt6UnCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5qOS5p2Q5rex5Yqg5belJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eDreeUteWIhuWOgi3ng63nlLUnCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn54Ot55S15YiG5Y6CLeS6muS4tOeVjCcKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfng63nlLXliIbljoIt5L2Z54OtJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eDreeUteWIhuWOgi3pvJPpo44nCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5Yi25rCn5YiG5Y6CJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+WItuawp+WIhuWOgi3kuIDmnJ8nCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5Yi25rCn5YiG5Y6CLeS4ieacnycKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfliLbmsKfliIbljoIt56m65Y6L56uZJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+awtOWkhOeQhi3kuIDmnJ8nCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5rC05aSE55CGLeS6jOacnycKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfmsLTlpITnkIYt5LiJ5pyfJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+eFpOawlOWIhuWOgicKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfkvpvnlLXkuIDljLonCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5YW05r6E6ZKi55CDJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+WFtOa+hOa4r+WKoScKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICflgqjov5Dlhazlj7gnCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn57u85ZCI5Yip55SoJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+WQiOmHkeeCieWIhuWOgicKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfniannrqHpg6gnCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5ZCO5Yuk6YOoJwogICAgICB9LCB7CiAgICAgICAgZGVwYXJ0bWVudDogJ+WFtuS7licKICAgICAgfSwgewogICAgICAgIGRlcGFydG1lbnQ6ICfmjZ/ogJcnCiAgICAgIH0sIHsKICAgICAgICBkZXBhcnRtZW50OiAn5ZCI6K6hJwogICAgICB9XSwKICAgICAgLy8g5pyq5a6M5oiQ5oyH5qCH5pWw5o2uCiAgICAgIGluY29tcGxldGVEYXRhOiBbXSwKICAgICAgLy8g6IO95rqQ5oyH5qCH57uf6K6h5pWw5o2uCiAgICAgIGVuZXJneVN0YXRzOiBbewogICAgICAgIHRpdGxlOiAn57u85ZCI6IO96ICXJywKICAgICAgICB2YWx1ZTogJzUuMjHlkKjmoIfnhaQv5ZCo6ZKiJywKICAgICAgICBjaGFuZ2U6IC0yLjMsCiAgICAgICAgc3RhdHVzOiAnZ29vZCcKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5rC06LWE5rqQ5raI6ICXJywKICAgICAgICB2YWx1ZTogJzMuOOeri+aWueexsy/lkKjpkqInLAogICAgICAgIGNoYW5nZTogLTEuNSwKICAgICAgICBzdGF0dXM6ICdnb29kJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnlLXlipvmtojogJcnLAogICAgICAgIHZhbHVlOiAnNDg15Y2D55Om5pe2L+WQqOmSoicsCiAgICAgICAgY2hhbmdlOiAwLjgsCiAgICAgICAgc3RhdHVzOiAnd2FybmluZycKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn54Wk5rCU5Zue5pS2546HJywKICAgICAgICB2YWx1ZTogJzk4LjUlJywKICAgICAgICBjaGFuZ2U6IDEuMiwKICAgICAgICBzdGF0dXM6ICdnb29kJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfkvZnng63lm57mlLbnjocnLAogICAgICAgIHZhbHVlOiAnNzUuMiUnLAogICAgICAgIGNoYW5nZTogMi41LAogICAgICAgIHN0YXR1czogJ2dvb2QnCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+S6jOawp+WMlueis+aOkuaUvicsCiAgICAgICAgdmFsdWU6ICcxLjg15ZCoL+WQqOmSoicsCiAgICAgICAgY2hhbmdlOiAtMy4yLAogICAgICAgIHN0YXR1czogJ2dvb2QnCiAgICAgIH1dLAogICAgICAvLyDog73mupDmtojogJfmlbDmja4KICAgICAgZW5lcmd5Q29uc3VtcHRpb25EYXRhOiBbewogICAgICAgIG5hbWU6ICfnlLXlipsnLAogICAgICAgIHZhbHVlOiAzNQogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+eFpOeCrScsCiAgICAgICAgdmFsdWU6IDI1CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5aSp54S25rCUJywKICAgICAgICB2YWx1ZTogMTUKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfokrjmsb0nLAogICAgICAgIHZhbHVlOiAxMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+WFtuS7licsCiAgICAgICAgdmFsdWU6IDE1CiAgICAgIH1dLAogICAgICAvLyDpg6jpl6jog73mupDmtojogJfmlbDmja7vvIjmlrDlop7vvIktIOaMieaciOe7n+iuoQogICAgICBkZXBhcnRtZW50RW5lcmd5RGF0YTogewogICAgICAgIG1vbnRoczogWycx5pyIJywgJzLmnIgnLCAnM+aciCcsICc05pyIJ10sCiAgICAgICAgZGVwYXJ0bWVudHM6IFsn54K86ZOB5LqL5Lia6YOoJywgJ+eCvOmSouS6i+S4mumDqCcsICfovafpkqLkuovkuJrpg6gnLCAn6ams56eR5omY6ZKi55CDJywgJ+eJueadv+S6i+S4mumDqCcsICfliqjlipvkuovkuJrpg6gnLCAn54mp5rWB5LqL5Lia6YOoJywgJ+ajgOS/ruS6i+S4mumDqCddLAogICAgICAgIGVsZWN0cmljaXR5OiB7CiAgICAgICAgICAvLyDnlLXlipvmtojogJcgKOS4h+WNg+eTpuaXtikKICAgICAgICAgICfngrzpk4HkuovkuJrpg6gnOiBbMTI1MCwgMTE4MCwgMTIyMCwgMTI2MF0sCiAgICAgICAgICAn54K86ZKi5LqL5Lia6YOoJzogWzE4NTAsIDE3OTAsIDE4MTAsIDE4ODBdLAogICAgICAgICAgJ+i9p+mSouS6i+S4mumDqCc6IFsxNDUwLCAxNDIwLCAxNDgwLCAxNDQwXSwKICAgICAgICAgICfpqaznp5HmiZjpkqLnkIMnOiBbNDIwLCA0MTAsIDQzMCwgNDI1XSwKICAgICAgICAgICfnibnmnb/kuovkuJrpg6gnOiBbOTgwLCA5NTAsIDk3MCwgOTkwXSwKICAgICAgICAgICfliqjlipvkuovkuJrpg6gnOiBbMzIwLCAzMTAsIDMzMCwgMzI1XSwKICAgICAgICAgICfnianmtYHkuovkuJrpg6gnOiBbMTgwLCAxNzUsIDE4NSwgMTgyXSwKICAgICAgICAgICfmo4Dkv67kuovkuJrpg6gnOiBbMTUwLCAxNDUsIDE1NSwgMTUyXQogICAgICAgIH0sCiAgICAgICAgd2F0ZXI6IHsKICAgICAgICAgIC8vIOawtOi1hOa6kOa2iOiAlyAo5LiH5ZCoKQogICAgICAgICAgJ+eCvOmTgeS6i+S4mumDqCc6IFs4NSwgODIsIDg2LCA4OF0sCiAgICAgICAgICAn54K86ZKi5LqL5Lia6YOoJzogWzEyMCwgMTE1LCAxMTgsIDEyMl0sCiAgICAgICAgICAn6L2n6ZKi5LqL5Lia6YOoJzogWzk1LCA5MiwgOTYsIDk0XSwKICAgICAgICAgICfpqaznp5HmiZjpkqLnkIMnOiBbMjgsIDI3LCAyOSwgMjguNV0sCiAgICAgICAgICAn54m55p2/5LqL5Lia6YOoJzogWzY1LCA2MywgNjYsIDY3XSwKICAgICAgICAgICfliqjlipvkuovkuJrpg6gnOiBbMTgwLCAxNzUsIDE4NSwgMTgyXSwKICAgICAgICAgICfnianmtYHkuovkuJrpg6gnOiBbMTUsIDE0LCAxNiwgMTUuNV0sCiAgICAgICAgICAn5qOA5L+u5LqL5Lia6YOoJzogWzEyLCAxMS41LCAxMi41LCAxMi4yXQogICAgICAgIH0sCiAgICAgICAgZ2FzOiB7CiAgICAgICAgICAvLyDlpKnnhLbmsJTmtojogJcgKOS4h+eri+aWueexsykKICAgICAgICAgICfngrzpk4HkuovkuJrpg6gnOiBbMzIwLCAzMTAsIDMyNSwgMzMwXSwKICAgICAgICAgICfngrzpkqLkuovkuJrpg6gnOiBbNDgwLCA0NzAsIDQ4NSwgNDkwXSwKICAgICAgICAgICfovafpkqLkuovkuJrpg6gnOiBbMzgwLCAzNzAsIDM4NSwgMzc1XSwKICAgICAgICAgICfpqaznp5HmiZjpkqLnkIMnOiBbMTEwLCAxMDUsIDExMiwgMTA4XSwKICAgICAgICAgICfnibnmnb/kuovkuJrpg6gnOiBbMjUwLCAyNDUsIDI1NSwgMjYwXSwKICAgICAgICAgICfliqjlipvkuovkuJrpg6gnOiBbODUsIDgyLCA4NywgODZdLAogICAgICAgICAgJ+eJqea1geS6i+S4mumDqCc6IFs0NSwgNDMsIDQ2LCA0NF0sCiAgICAgICAgICAn5qOA5L+u5LqL5Lia6YOoJzogWzM1LCAzNCwgMzYsIDM1LjVdCiAgICAgICAgfSwKICAgICAgICBzdGVhbTogewogICAgICAgICAgLy8g6JK45rG95raI6ICXICjkuIflkKgpCiAgICAgICAgICAn54K86ZOB5LqL5Lia6YOoJzogWzQ1LCA0MywgNDYsIDQ3XSwKICAgICAgICAgICfngrzpkqLkuovkuJrpg6gnOiBbNjUsIDYzLCA2NiwgNjddLAogICAgICAgICAgJ+i9p+mSouS6i+S4mumDqCc6IFs1MiwgNTAsIDUzLCA1MV0sCiAgICAgICAgICAn6ams56eR5omY6ZKi55CDJzogWzE1LCAxNC41LCAxNS41LCAxNS4yXSwKICAgICAgICAgICfnibnmnb/kuovkuJrpg6gnOiBbMzUsIDM0LCAzNiwgMzddLAogICAgICAgICAgJ+WKqOWKm+S6i+S4mumDqCc6IFsxMiwgMTEuNSwgMTIuNSwgMTIuMl0sCiAgICAgICAgICAn54mp5rWB5LqL5Lia6YOoJzogWzgsIDcuOCwgOC4yLCA4LjFdLAogICAgICAgICAgJ+ajgOS/ruS6i+S4mumDqCc6IFs2LCA1LjgsIDYuMiwgNi4xXQogICAgICAgIH0KICAgICAgfSwKICAgICAgLy8g6IO95rqQ57G75Z6L6YCJ6aG5CiAgICAgIGVuZXJneVR5cGVzOiBbewogICAgICAgIGxhYmVsOiAn55S15Yqb5raI6ICXJywKICAgICAgICB2YWx1ZTogJ2VsZWN0cmljaXR5JywKICAgICAgICB1bml0OiAn5Y2D55Om5pe2JywKICAgICAgICBjb2xvcjogJyMyZjgwZWQnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+awtOi1hOa6kOa2iOiAlycsCiAgICAgICAgdmFsdWU6ICd3YXRlcicsCiAgICAgICAgdW5pdDogJ+WQqCcsCiAgICAgICAgY29sb3I6ICcjMmQ5Y2RiJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICflpKnnhLbmsJTmtojogJcnLAogICAgICAgIHZhbHVlOiAnZ2FzJywKICAgICAgICB1bml0OiAn56uL5pa557GzJywKICAgICAgICBjb2xvcjogJyMxYTczZTgnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+iSuOaxvea2iOiAlycsCiAgICAgICAgdmFsdWU6ICdzdGVhbScsCiAgICAgICAgdW5pdDogJ+WQqCcsCiAgICAgICAgY29sb3I6ICcjMjdhZTYwJwogICAgICB9XSwKICAgICAgLy8g6IO95rqQ5oiQ5pys5pWw5o2uCiAgICAgIGVuZXJneUNvc3REYXRhOiB7CiAgICAgICAgbW9udGhzOiBbJzHmnIgnLCAnMuaciCcsICcz5pyIJywgJzTmnIgnLCAnNeaciCcsICc25pyIJywgJzfmnIgnLCAnOOaciCcsICc55pyIJywgJzEw5pyIJywgJzEx5pyIJywgJzEy5pyIJ10sCiAgICAgICAgZWxlY3RyaWNpdHk6IFszMjAsIDMzMiwgMzAxLCAzMzQsIDM5MCwgMzMwLCAzMjAsIDMxNSwgMzEwLCAzMjUsIDMxNSwgMzE4XSwKICAgICAgICBjb2FsOiBbMjIwLCAxODIsIDE5MSwgMjM0LCAyOTAsIDMzMCwgMzEwLCAyOTUsIDMwMCwgMjg1LCAyNzAsIDI3NV0sCiAgICAgICAgZ2FzOiBbMTUwLCAyMzIsIDIwMSwgMTU0LCAxOTAsIDE4MCwgMTY1LCAxNzUsIDE5MCwgMTk1LCAyMDUsIDIxMF0sCiAgICAgICAgc3RlYW06IFs5OCwgNzcsIDEwMSwgOTksIDEyMCwgMTI1LCAxMTAsIDEwMCwgMTA1LCAxMTUsIDExMCwgMTIwXQogICAgICB9LAogICAgICAvLyDog73mupDor6bmg4XmlbDmja4gLSDlvZPml6XmtojogJcKICAgICAgZW5lcmd5RGV0YWlsRGF0YTogW3sKICAgICAgICBuYW1lOiAn6L2s54KJ54Wk5rCUJywKICAgICAgICBjYXRlZ29yeTogJ2dhcycsCiAgICAgICAgdmFsdWU6IDEyNS42LAogICAgICAgIHVuaXQ6ICfkuIdtwrMnLAogICAgICAgIHRhcmdldDogMTMwLAogICAgICAgIHdhcm5pbmc6IDE0MCwKICAgICAgICBkYW5nZXI6IDE1MCwKICAgICAgICBzdGF0dXM6ICdub3JtYWwnLAogICAgICAgIHRyZW5kOiAtMi4xIC8vIOebuOavlOaYqOaXpeWPmOWMlueZvuWIhuavlAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+mrmOeCieeFpOawlCcsCiAgICAgICAgY2F0ZWdvcnk6ICdnYXMnLAogICAgICAgIHZhbHVlOiAyODcuMywKICAgICAgICB1bml0OiAn5LiHbcKzJywKICAgICAgICB0YXJnZXQ6IDI4MCwKICAgICAgICB3YXJuaW5nOiAzMDAsCiAgICAgICAgZGFuZ2VyOiAzMjAsCiAgICAgICAgc3RhdHVzOiAnd2FybmluZycsCiAgICAgICAgdHJlbmQ6IDUuMwogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+eEpueCieeFpOawlCcsCiAgICAgICAgY2F0ZWdvcnk6ICdnYXMnLAogICAgICAgIHZhbHVlOiA5OC40LAogICAgICAgIHVuaXQ6ICfkuIdtwrMnLAogICAgICAgIHRhcmdldDogMTAwLAogICAgICAgIHdhcm5pbmc6IDExMCwKICAgICAgICBkYW5nZXI6IDEyMCwKICAgICAgICBzdGF0dXM6ICdub3JtYWwnLAogICAgICAgIHRyZW5kOiAtMS4yCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5aSp54S25rCUJywKICAgICAgICBjYXRlZ29yeTogJ2dhcycsCiAgICAgICAgdmFsdWU6IDQ1LjcsCiAgICAgICAgdW5pdDogJ+S4h23CsycsCiAgICAgICAgdGFyZ2V0OiA0MCwKICAgICAgICB3YXJuaW5nOiA0NSwKICAgICAgICBkYW5nZXI6IDUwLAogICAgICAgIHN0YXR1czogJ2RhbmdlcicsCiAgICAgICAgdHJlbmQ6IDEyLjUKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfppbHlkozokrjmsb0nLAogICAgICAgIGNhdGVnb3J5OiAnc3RlYW0nLAogICAgICAgIHZhbHVlOiA1Ni4yLAogICAgICAgIHVuaXQ6ICfkuIflkKgnLAogICAgICAgIHRhcmdldDogNTUsCiAgICAgICAgd2FybmluZzogNjAsCiAgICAgICAgZGFuZ2VyOiA2NSwKICAgICAgICBzdGF0dXM6ICdub3JtYWwnLAogICAgICAgIHRyZW5kOiAxLjgKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfov4fng63okrjmsb0nLAogICAgICAgIGNhdGVnb3J5OiAnc3RlYW0nLAogICAgICAgIHZhbHVlOiAzMi44LAogICAgICAgIHVuaXQ6ICfkuIflkKgnLAogICAgICAgIHRhcmdldDogMzAsCiAgICAgICAgd2FybmluZzogMzUsCiAgICAgICAgZGFuZ2VyOiA0MCwKICAgICAgICBzdGF0dXM6ICd3YXJuaW5nJywKICAgICAgICB0cmVuZDogNy4yCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5bel5Lia55So5rC0JywKICAgICAgICBjYXRlZ29yeTogJ3dhdGVyJywKICAgICAgICB2YWx1ZTogMTQyLjUsCiAgICAgICAgdW5pdDogJ+S4h+WQqCcsCiAgICAgICAgdGFyZ2V0OiAxNDAsCiAgICAgICAgd2FybmluZzogMTUwLAogICAgICAgIGRhbmdlcjogMTYwLAogICAgICAgIHN0YXR1czogJ25vcm1hbCcsCiAgICAgICAgdHJlbmQ6IDEuNQogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+W+queOr+WGt+WNtOawtCcsCiAgICAgICAgY2F0ZWdvcnk6ICd3YXRlcicsCiAgICAgICAgdmFsdWU6IDI4NS4zLAogICAgICAgIHVuaXQ6ICfkuIflkKgnLAogICAgICAgIHRhcmdldDogMjgwLAogICAgICAgIHdhcm5pbmc6IDMwMCwKICAgICAgICBkYW5nZXI6IDMyMCwKICAgICAgICBzdGF0dXM6ICd3YXJuaW5nJywKICAgICAgICB0cmVuZDogMy44CiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn6auY5Y6L55S15YqbJywKICAgICAgICBjYXRlZ29yeTogJ2VsZWN0cmljaXR5JywKICAgICAgICB2YWx1ZTogMTg1Ni40LAogICAgICAgIHVuaXQ6ICfkuIdrV2gnLAogICAgICAgIHRhcmdldDogMTgwMCwKICAgICAgICB3YXJuaW5nOiAxOTAwLAogICAgICAgIGRhbmdlcjogMjAwMCwKICAgICAgICBzdGF0dXM6ICd3YXJuaW5nJywKICAgICAgICB0cmVuZDogNC4yCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5Lit5Y6L55S15YqbJywKICAgICAgICBjYXRlZ29yeTogJ2VsZWN0cmljaXR5JywKICAgICAgICB2YWx1ZTogOTQ1LjIsCiAgICAgICAgdW5pdDogJ+S4h2tXaCcsCiAgICAgICAgdGFyZ2V0OiA5NTAsCiAgICAgICAgd2FybmluZzogMTAwMCwKICAgICAgICBkYW5nZXI6IDEwNTAsCiAgICAgICAgc3RhdHVzOiAnbm9ybWFsJywKICAgICAgICB0cmVuZDogLTAuOAogICAgICB9XSwKICAgICAgLy8g5YiG5Y6C6IO95rqQ5raI6ICX5pWw5o2uIC0g5oyJ5pyI57uf6K6hCiAgICAgIGZhY3RvcnlFbmVyZ3lEYXRhOiB7CiAgICAgICAgbW9udGhzOiBbJzHmnIgnLCAnMuaciCcsICcz5pyIJywgJzTmnIgnLCAnNeaciCcsICc25pyIJ10sCiAgICAgICAgZWxlY3RyaWNpdHk6ICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKHsKICAgICAgICAgIC8vIOeUteWKm+a2iOiAlyAo5LiH5Y2D55Om5pe2KQogICAgICAgICAgJ+mSmeS4muWIhuWOgic6IFsxNDAwODg0LCAxNDM0MDA1LCAxNDM1NzY2LCAxMzc2MzE5LCAxMzAxMDk1LCAxMjY5NjMwXSwKICAgICAgICAgICfnn7/muKPlvq7nsoknOiBbNTgwNTc5NCwgMjEzMTg0NywgNjA4OTA0NiwgNjEwMDk5OSwgNjQxNzMzNSwgNjQ3ODI2Ml0sCiAgICAgICAgICAn54On57uT5YiG5Y6CLTEj54On57uTJzogWzEzMTE3OTAyLCAxMjk0MzU2OCwgMTEwNjE0NDQsIDEwODEyMzkzLCAxMTYyMzcwMiwgMTEwMzI1MzldLAogICAgICAgICAgJ+eDp+e7k+WIhuWOgi0yI+eDp+e7kyc6IFsxMzAzMzkyNiwgMTA0MzY2MzQsIDkyODc4ODQsIDk3NjkzMTYsIDk4NzkzOTcsIDEwNTY1NzYwXSwKICAgICAgICAgICfngrzpk4HliIbljoItMSPpq5jngoknOiBbMCwgMCwgMCwgMCwgMCwgMF0sCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLTIj6auY54KJJzogWzY2OTQwLCAwLCAwLCAwLCAwLCAwXSwKICAgICAgICAgICfngrzpk4HliIbljoItMyPpq5jngoknOiBbMCwgMCwgMCwgMCwgMCwgMF0sCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLeWwj+WWt+eFpCc6IFswLCAwLCAwLCAwLCAwLCAwXSwKICAgICAgICAgICfkuIDngrzpkqInOiBbMzA3NjgxNjcsIDMwMTI1NzgxLCAzMDI5NzQ2MywgMjg5ODA0OTcsIDI5Nzc0MTU5LCAzMTM0MzM5N10sCiAgICAgICAgICAn5LqM54K86ZKiJzogWzIyNDg4NDk1LCAyMTk2ODk0MywgMjE3ODc5MTYsIDIyMTcxMDY3LCAyMTI0MjExNSwgMjE3ODgxMTldLAogICAgICAgICAgJ+S4gOi9p+mSoic6IFs1Nzk1Nzc3LCA1NDUyMjA0LCA1NzExMDUxLCA1NjQ4NTc1LCA1NDk2NDQ3LCA1NzMzNDAzXSwKICAgICAgICAgICfkuozovact5aSn5qOSJzogWzMyNDYyNTAsIDMxOTUwOTEsIDMyNjg4MzYsIDMzNjMwODIsIDMyNjI1NTMsIDM0NjY5MzVdLAogICAgICAgICAgJ+S6jOi9py3lsI/mo5InOiBbNTA1OTAxOCwgNDk1NDUxMSwgNDgxMTk4NywgNTA1MzQ1NiwgNDY4NzkyMiwgNDg1MjM3MF0sCiAgICAgICAgICAn54m55p2/54K86ZKiJzogWzMwMzg3ODYyLCAyOTg0MjAxOSwgMjY0MzE3MTYsIDI5NDY5MzcyLCAyOTI3MTAzNSwgMjkwMzU1MjBdLAogICAgICAgICAgJzM1MDDkuK3mnb8nOiBbNzcwNjMwMCwgNjY0NDQyMCwgNzM5NzcxNiwgNzMyODEwMiwgNzIwNjIxNSwgNzQyMTE3OV0sCiAgICAgICAgICAnNDMwMOWOmuadvyc6IFsxMzUxOTExMiwgMTI0NjQ2NjIsIDEwMDI4NTM2LCAxMjg4MTI4NiwgMTI2NzQ5NDAsIDEzMTY2Njc5XSwKICAgICAgICAgICfng63lpITnkIYnOiBbMjgxMzkzNywgMjcyNjUwMSwgMjI3NTQyNSwgMjM4NDQxMiwgMjIwNjU0OCwgMzEzNTEwNV0sCiAgICAgICAgICAn6auY57q/5YiG5Y6CJzogWzc2MjE0NTIsIDc4MjI1MzgsIDc5MjA0NzAsIDcxNTI0NDYsIDcwNzE0MTIsIDczNDIwNjZdLAogICAgICAgICAgJ+e6v+adkOa3seWKoOW3pSc6IFs5Njg2NTQsIDg3ODA3NSwgOTI1Mjg0LCA5MDI1NzMsIDkxNTQzNCwgOTE1MDUzXSwKICAgICAgICAgICfmo5LmnZDmt7HliqDlt6UnOiBbMzMzMDI5NSwgMzI4MDg3NSwgMzM1MDUxOCwgMzQ4MTg5OCwgMzMwNDM1NywgMzMyNjQzM10sCiAgICAgICAgICAn54Ot55S15YiG5Y6CLeeDreeUtSc6IFs4NzA5NTE5LCA5NzU3ODQwLCAxMDEwMjcyMCwgOTY2MDQ4MCwgOTMwNzY4MCwgNzQ3MzUyMF0sCiAgICAgICAgICAn54Ot55S15YiG5Y6CLeS6muS4tOeVjCc6IFsyODQyMjQ1LCA0MDQyMDgwLCAzNjM0NDAwLCA0MzA5ODAwLCA0MzU4NDM1LCA0MDMzNTg4XSwKICAgICAgICAgICfng63nlLXliIbljoIt5L2Z54OtJzogWzEzMzUwMCwgMjQ3MjgwLCAxMTI2NDAsIDExMjY0MCwgMjE4NTYwLCAxMzY0ODBdLAogICAgICAgICAgJ+eDreeUteWIhuWOgi3pvJPpo44nOiBbMTIxNDYxNDAsIDIxOTk1NjE2LCAyMTk2OTgwMywgMjMzNzc3NjIsIDI4NDI4OTc2LCAzNzAzNjkzM10sCiAgICAgICAgICAn5Yi25rCn5YiG5Y6CLeS4gOacnyc6IFsyNjIwMzU0OSwgMjY0NTY1MjMsIDIwMzQ3NDY0LCAyNjY0NjczNSwgMjU1MjM4ODgsIDI3MjkwNzQ2XSwKICAgICAgICAgICfliLbmsKfliIbljoIt5LiJ5pyfJzogWzI5MjM5NjMyLCAyNzg4NjQzNCwgMjkyNzQ2NTcsIDI2NjAxOTE5LCAyNjE2MjAxNSwgMjY4NjU2NzhdLAogICAgICAgICAgJ+WItuawp+WIhuWOgi3nqbrljovnq5knOiBbNjA5Mjc1OSwgNjQ4MzYwOSwgNjQ1NTkzMCwgNjY2MTAzOSwgNjM2OTI5NywgNjQ2NDc5Ml0sCiAgICAgICAgICAn5rC05aSE55CGLeS4gOacnyc6IFsyNDY3MjQwLCAyNDcwNDQyLCAyNTE1ODI5LCAyNTQ5NDU3LCAzMjIyMDMxLCAyODg0MDY5XSwKICAgICAgICAgICfmsLTlpITnkIYt5LqM5pyfJzogWzQ5NTE4OTcsIDQ5MDI5ODYsIDQ4NDM3MjMsIDUwNDA5ODQsIDUwMjE3MDgsIDUyNjMyMjRdLAogICAgICAgICAgJ+awtOWkhOeQhi3kuInmnJ8nOiBbNTIyNDY0OSwgNTMyMDAxMiwgNTA2MDgxMywgNTQwNzU4OCwgNTQ4ODcxNSwgNTgxNjU2MF0sCiAgICAgICAgICAn54Wk5rCU5YiG5Y6CJzogWzY0MzEzMiwgNjkzNDY2LCA2NTcwNTIsIDY1OTU0MywgNjI0ODMwLCA2MjA5NzNdLAogICAgICAgICAgJ+S+m+eUteS4gOWMuic6IFsxMDA0MTUsIDEwMzUzNywgMTAzOTA2LCAxMzM2MTEsIDE2NjAyNywgMTgwMjI3XSwKICAgICAgICAgICflhbTmvoTpkqLnkIMnOiBbMTA4Nzk4MSwgODQwODE4LCA5ODE3NTEsIDEwNTcyNzUsIDkwOTI3NSwgMTE4ODU1N10sCiAgICAgICAgICAn5YW05r6E5riv5YqhJzogWzY4MDIzLCA1OTQ4MSwgNjk5MTgsIDYzMzM2LCA0NDU0MSwgNjUzODldLAogICAgICAgICAgJ+WCqOi/kOWFrOWPuCc6IFs1NzU5MzI0LCA1ODU5OTc1LCA1MzUyMjA2LCA1MzE2NjQwLCA1Mzc4MzM3LCA1NjQ0OTM4XSwKICAgICAgICAgICfnu7zlkIjliKnnlKgnOiBbMTA0Njc2MywgMTA0ODczNywgMTEwMzE3OCwgMTAwNjk0MywgMTA4Mjk3NSwgMTA2ODg2OF0sCiAgICAgICAgICAn5ZCI6YeR54KJ5YiG5Y6CJzogWzI3NjkwOTIsIDQzMjEwMDUsIDMyMjE1NTksIDQ5MzI3NjEsIDQ3ODk4MDAsIDU4Nzg5ODBdLAogICAgICAgICAgJ+eJqeeuoemDqCc6IFszOTkwMiwgNDM0OTgsIDM0OTUzLCAyOTY2MiwgMjQzNzMsIDI0MjI3XSwKICAgICAgICAgICflkI7li6Tpg6gnOiBbNDY0MzYsIDUxMTQ0LCAzOTczOSwgMzY0NTksIDM2ODE3LCAzNjU5Nl0sCiAgICAgICAgICAn5YW25LuWJzogWzgyNDM3NSwgNzc1MTA3LCA3NDk5NDMsIDY4ODM2NSwgNzY0MzM3LCA5Nzg1MjBdCiAgICAgICAgfSwgIlx1NzI2OVx1N0JBMVx1OTBFOCIsIFsxMjk1MTQwLCA2NjQwMDAsIDYyMTkyMCwgNTQxNDgwLCA1NzY2MjAsIDgzNjk2MF0pLCAn5ZCI6K6hJywgWzI3ODgyMjQ3NiwgMjgwMzI1MjY0LCAyNjkzMzUxNjEsIDI4MTcxMDI3MiwgMjg0ODMzOTAzLCAzMDA2NjIyNzRdKSwKICAgICAgICB3YXRlcjogewogICAgICAgICAgLy8g5rC06LWE5rqQ5raI6ICXICjkuIflkKgpCiAgICAgICAgICAn6ZKZ5Lia5YiG5Y6CJzogWzI1MTcsIDIzNzYsIDIyNTMsIDIxNzMsIDIyNTksIDIzMDFdLAogICAgICAgICAgJ+efv+a4o+W+rueyiSc6IFsyODkwLCAyNTkxLCAyNDc4LCAyMzU4LCAyMzgxLCAyMjU2XSwKICAgICAgICAgICfng6fnu5PliIbljoItMSPng6fnu5MnOiBbNDEyOTAsIDQwNDQzLCAzOTU5MSwgNDAxMzYsIDQwODUyLCA0MDE0Nl0sCiAgICAgICAgICAn54On57uT5YiG5Y6CLTIj54On57uTJzogWzcxODA0LCA2NzUzOSwgNjkwMDksIDY5OTQ3LCA3MjA1MCwgNzAzNDRdLAogICAgICAgICAgJ+eCvOmTgeWIhuWOgi0xI+mrmOeCiSc6IFsyMDg3MCwgMjEwODIsIDIxMjMxLCAyNjcyOSwgMjQ3MDIsIDI4MTg4XSwKICAgICAgICAgICfngrzpk4HliIbljoItMiPpq5jngoknOiBbNjEwMzIsIDY1NjE1LCA1NjIxOCwgNjU2OTAsIDYzNTY3LCA2NzAzM10sCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLTMj6auY54KJJzogWzcwNjA0LCA2OTk2NCwgNzg2MTMsIDg1OTE0LCA4NTM1OCwgOTkwMTZdLAogICAgICAgICAgJ+eCvOmTgeWIhuWOgi3llrfnhaTvvIgwIzIj6auY54KJ77yJJzogWzIzMDgsIDI0NTcsIDI4OTcsIDMwMTcsIDI4NTEsIDM1OTddLAogICAgICAgICAgJ+S4gOeCvOmSoic6IFs0NjMxMTUyLCA0NTI5MTYwLCA0NjA5NTEwLCA0NjQ1NDQ5LCA0NTM2OTMyLCA0NjE4NTYzXSwKICAgICAgICAgICfkuozngrzpkqInOiBbOTEwNDYyOSwgODk3NDU4NCwgODcwNzI0MSwgODg2NDQ0OSwgODU3NDExMCwgODc4MDQ5M10sCiAgICAgICAgICAn5LiA6L2n6ZKiJzogWzE2MTM0MTYsIDE1NjYzMDMsIDE2MDM2NjAsIDE2MDQwMjEsIDE1NzQyMjIsIDE2NDE5NTZdLAogICAgICAgICAgJ+S6jOi9py3lpKfmo5InOiBbMTU0OTk3NiwgMTQ3MzY1MiwgMTQ0ODk5OCwgMTQ4Mjg0MCwgMTQyMDg0OCwgMTQ2MTA5N10sCiAgICAgICAgICAn5LqM6L2nLeWwj+ajkic6IFsxNzYxOTIyLCAxNjM2NTE4LCAxNTk5NjE4LCAxNzA3Nzc3LCAxNjExMjkwLCAxNTY2NDUzXSwKICAgICAgICAgICfnibnmnb/ngrzpkqInOiBbMTEwNjUyMDIsIDEwODYxODk3LCAxMDA3ODM3MCwgMTA3OTgyNzEsIDEwNTM5MjQ2LCAxMDQxNDA3Ml0sCiAgICAgICAgICAnMzUwMOS4readvyc6IFsyNTI4NzY4LCAyNTMxMTAyLCAyNTc3MTA2LCAyNjE0MzMzLCAyNTA5ODUyLCAyNzQ2MTY0XSwKICAgICAgICAgICc0MzAw5Y6a5p2/JzogWzUzNjAyLCA1Mjk3MywgNDcxMDAsIDUxNzI3LCA0ODk5MywgNTI0MzldLAogICAgICAgICAgJzQzMDDmsLTlpITnkIYnOiBbNTE5NTcsIDU5OTk3LCA1ODY5MSwgNTU1MTUsIDYxODkxLCA2NjQ3NF0sCiAgICAgICAgICAn54Ot5aSE55CGJzogWzE1ODczLCAxMzI1NywgMTE1MzYsIDEwMjEzLCAxMDc5NiwgMTAwMThdLAogICAgICAgICAgJ+mrmOe6v+WIhuWOgic6IFsyNDc4MywgMjEzMjgsIDE5MDgyLCAyMzExNywgMjQ1NDgsIDIzNTE4XSwKICAgICAgICAgICfng63nlLXliIbljoIt54Ot55S1JzogWzMyNjU0LCAzNTk5MSwgNDY5NzYsIDM0OTA3LCAzNTgzMSwgMzAyMTFdLAogICAgICAgICAgJ+eDreeUteWIhuWOgi3pvJPpo44nOiBbNjY3ODEsIDYwNTc4LCA3NDUwNiwgODQ3MTUsIDU2ODQ3LCA2MDIyMl0sCiAgICAgICAgICAn54Ot55S15YiG5Y6CLeS6muS4tOeVjCc6IFs1MDcwMiwgNDI4OTgsIDUzMzYwLCA4MDkzNywgNzUwOTAsIDgzMjIwXSwKICAgICAgICAgICfng63nlLXliIbljoIt5YeA5rC05Y6C55u05L6b5Y6f5rC0JzogWzgyNzEwLCA2NjY0MSwgNTg2NzAsIDU5MDIyLCA1MTAzNCwgNTE3MzldLAogICAgICAgICAgJ+WItuawp+WIhuWOgi3kuIDmnJ8nOiBbNDgxMDcsIDg5OTUzLCA0MDY2NSwgNTEzNjcsIDU2NjA1LCA1NTg2Nl0sCiAgICAgICAgICAn5Yi25rCn5YiG5Y6CLeS4ieacnyc6IFsyNzEzNiwgMTMzOTcsIDEwNzI2LCAyMTg5NiwgMjU2ODIsIDExNzE2XSwKICAgICAgICAgICfliLbmsKfliIbljoIt56m65Y6L56uZJzogWzM5MjgsIDM1NjMsIDQzODUsIDQ5ODMsIDMzNDMsIDM1NDJdLAogICAgICAgICAgJ+awtOWkhOeQhi3kuIDmnJ8nOiBbNjY2NDU3LCA2ODQ4NjEsIDY5OTAxNywgNzA2Mzc0LCA3MDM0OTcsIDczNzI5MV0sCiAgICAgICAgICAn5rC05aSE55CGLeS6jOacnyc6IFsyODM3MzMsIDI5MDY2MCwgMjQ1MjE3LCAyNDM4ODMsIDI0NTMwMCwgMjg1NDg1XSwKICAgICAgICAgICfmsLTlpITnkIYt5LiJ5pyfJzogWzE2MjAxMiwgMTQwNzM3LCAxNDMyOTgsIDIxMTMyOSwgMjA2MzAzLCAyNDUxMzJdLAogICAgICAgICAgJ+S+m+eUteS4gOWMuic6IFsyMjczLCAyMzU3LCAyNTAyLCAyNTk3LCAyMzg1LCAyMTUzXSwKICAgICAgICAgICflgqjov5Dlhazlj7gnOiBbNDE5MDYsIDQwMzYyLCA0MTk4MCwgNDIzNjEsIDQxOTYzLCA0MDc3OV0sCiAgICAgICAgICAn57u85ZCI5Yip55SoJzogWzI1MTE3LCAyNDU4NiwgMTk5MzEsIDI1OTI2LCAyMjU1NSwgMTU5NjRdLAogICAgICAgICAgJ+WQiOmHkeeCieWIhuWOgic6IFs5Mzk5NDQsIDE0MDYwNjEsIDExMjk2OTAsIDE1OTExOTYsIDEzOTI5OTAsIDE4MzQzOThdLAogICAgICAgICAgJ+WQjuWLpOe7vOWQiOalvCc6IFs2MDYyNjQsIDQ3MzAyNywgNTIwODU1LCAyMzMzODQsIDE2ODE0NiwgMzY1OTk3XSwKICAgICAgICAgICflhaznlKgnOiBbNzkwNjUsIDc5ODcxLCA3OTYzNCwgNzk4NzEsIDgwOTcxLCA4MTM0MV0sCiAgICAgICAgICAn5ZCI6K6hJzogWzM1NzkzMzg0LCAzNTQ0ODM4MSwgMzQyMDQ2MTQsIDM1NjI4NDI0LCAzNDM3NTI5MCwgMzU1OTkxODRdCiAgICAgICAgICAvLyAn54K86ZOB5YiG5Y6CJzogWzU1LCA1MywgNTYsIDU3XSwKICAgICAgICAgIC8vICfng6fnu5PliIbljoInOiBbMzAsIDI5LCAzMSwgMzJdLAogICAgICAgICAgLy8gJ+S4gOeCvOmSoic6IFs2NSwgNjMsIDY2LCA2N10sCiAgICAgICAgICAvLyAn5LqM54K86ZKiJzogWzYwLCA1OCwgNjEsIDYyXSwKICAgICAgICAgIC8vICfnu7zlkIjliKnnlKgnOiBbMjAsIDE5LCAyMSwgMjAuNV0sCiAgICAgICAgICAvLyAn5LiA6L2n6ZKiJzogWzI1LCAyNCwgMjYsIDI1LjVdLAogICAgICAgICAgLy8gJ+S6jOi9p+mSoic6IFsyMywgMjIsIDI0LCAyMy41XSwKICAgICAgICAgIC8vICfkuInovafpkqInOiBbMjIsIDIxLCAyMywgMjIuNV0sCiAgICAgICAgICAvLyAn54m55q6K6ZKi6L2n5p2QJzogWzIxLCAyMCwgMjIsIDIxLjVdLAogICAgICAgICAgLy8gJ+ajkuadkOi9p+WItuWOgic6IFsyMCwgMTksIDIxLCAyMC41XSwKICAgICAgICAgIC8vICfmo5Lnur/mnZDmt7HliqDlt6Uo5qOSKSc6IFsxMiwgMTEsIDEzLCAxMi41XSwKICAgICAgICAgIC8vICfmo5Lnur/mnZDmt7HliqDlt6Uo57q/KSc6IFsxMSwgMTAsIDEyLCAxMS41XSwKICAgICAgICAgIC8vICfnur/mnZDmt7HliqDlt6UnOiBbOSwgOCwgMTAsIDkuNV0sCiAgICAgICAgICAvLyAn6ams56eR5omY6ZKi55CDJzogWzI4LCAyNywgMjksIDI4LjVdLAogICAgICAgICAgLy8gJ+eJuemSoueCvOmSouWIhuWOgic6IFsyNSwgMjQsIDI2LCAyNS41XSwKICAgICAgICAgIC8vICfkuK3mnb/liIbljoInOiBbMjMsIDIyLCAyNCwgMjMuNV0sCiAgICAgICAgICAvLyAn5Y6a5p2/5YiG5Y6CJzogWzIyLCAyMSwgMjMsIDIyLjVdLAogICAgICAgICAgLy8gJ+mSouadkOa3seWKoOW3pSc6IFsyMSwgMjAsIDIyLCAyMS41XSwKICAgICAgICAgIC8vICfng63nlLUnOiBbNjUsIDYzLCA2NywgNjZdLAogICAgICAgICAgLy8gJ+S+m+eUteW3peWMuic6IFs0NSwgNDMsIDQ3LCA0Nl0sCiAgICAgICAgICAvLyAn5rC05aSE55CG5YiG5Y6CJzogWzcwLCA2OCwgNzIsIDcxXSwKICAgICAgICAgIC8vICfliLbmsKfliIbljoInOiBbNTUsIDUzLCA1NywgNTZdLAogICAgICAgICAgLy8gJ+eFpOawlOWIhuWOgic6IFs0NSwgNDMsIDQ3LCA0Nl0sCiAgICAgICAgICAvLyAn5YKo6L+Q5YWs5Y+4JzogWzE1LCAxNCwgMTYsIDE1LjVdLAogICAgICAgICAgLy8gJ+ajgOS/ruWIhuWOgic6IFsxMiwgMTEuNSwgMTIuNSwgMTIuMl0KICAgICAgICB9LAogICAgICAgIGdhczogewogICAgICAgICAgJ+mSmeS4muWIhuWOgic6IFsyMDgwLCAxNDMxNywgMTI4NzUsIDYyNDAsIDAsIDBdLAogICAgICAgICAgJ+efv+a4o+W+rueyiSc6IFswLCAwLCAwLCAwLCAwLCAwXSwKICAgICAgICAgICfng6fnu5PliIbljoItMSPng6fnu5MnOiBbOTI4LCAzNTgsIDEwNTQsIDI0NzYsIDEwNDAsIDEwNDBdLAogICAgICAgICAgJ+eDp+e7k+WIhuWOgi0yI+eDp+e7kyc6IFsxMzQ3MSwgMzQ0MCwgMTE1OTIsIDE0NDQ4LCAxOTA5NCwgMzgyNzJdLAogICAgICAgICAgJ+eCvOmTgeWIhuWOgi0xI+mrmOeCiSc6IFs0MjY0LCAyMzkyLCAxODMyLCAzOTUyLCAxNDU2LCAyMTg0XSwKICAgICAgICAgICfngrzpk4HliIbljoItMiPpq5jngoknOiBbMzc0NCwgNjM0NCwgMTQ4MDgsIDQxMDQsIDM0MzIsIDU2MTZdLAogICAgICAgICAgJ+eCvOmTgeWIhuWOgi0zI+mrmOeCiSc6IFs0ODg4MCwgNzQ0ODMsIDYyNTQxLCA5MjI3MiwgMTc5OTIsIDQ3ODg5XSwKICAgICAgICAgICfngrzpk4HliIbljoIt5bCP5Za354WkJzogWzkzNiwgMTE0NCwgMTA0MCwgMTIxMiwgOTM2LCAxMDQwXSwKICAgICAgICAgICfngrzpk4HliIbljoIt5aSn5Za354WkJzogWzQyNDMyLCAzNTQ2NCwgMzIyMDAsIDIwNjk2LCAxNTI4OCwgMTc3ODRdLAogICAgICAgICAgJ+S4gOeCvOmSoic6IFszMjEwNDQsIDQ0MTE3OSwgNDM4NDIzLCA0NTA5OTMsIDM4MTU3NSwgMzUxMjc1XSwKICAgICAgICAgICfkuozngrzpkqInOiBbMTUwODQ0LCAyMDg4ODksIDIxMjYwNSwgMjEyMjc0LCAyMDQzNzIsIDE5NTQyNV0sCiAgICAgICAgICAn5LiA6L2n6ZKiJzogWzI4NTcsIDEyMDg0LCAxMjEyMCwgNjgxOCwgNjIzMCwgMTYyMTddLAogICAgICAgICAgJ+S6jOi9py3lpKfmo5InOiBbMjM4MjQsIDMwMDAwLCAxNDUyMCwgMTA2MzAsIDI0MDM2LCAyNDQ4OV0sCiAgICAgICAgICAn5LqM6L2nLeWwj+ajkic6IFsxNjAwMCwgMTI0ODcsIDg5MjYsIDQ1NTUsIDE2MDI0LCAxMDQ5Nl0sCiAgICAgICAgICAn54m55p2/54K86ZKiJzogWzE1NDc5MCwgMjE4ODMyLCAxOTA1NjgsIDEzNTc1NywgMTQzNDA1LCAxNDA4ODZdLAogICAgICAgICAgJzM1MDDkuK3mnb8nOiBbNjE5MjMzLCA2NDQyMTcsIDc2NzU2MywgOTA4OTA2LCA4OTAwMDIsIDkyNjYzNl0sCiAgICAgICAgICAnNDMwMOWOmuadvyc6IFs0OTI2NTYsIDYyMTc3NSwgNzg1MjQ3LCA1MDA5ODgsIDU2Mzg4NiwgNzkyOTE5XSwKICAgICAgICAgICfng63lpITnkIYnOiBbMTA5Mzc0MCwgMTM3MDM4OSwgMTI5NjM2NSwgMTQwODk4OCwgMTE3MDM2MCwgMTI5OTA3Nl0sCiAgICAgICAgICAn6auY57q/5YiG5Y6CJzogWzIxNCwgOTYsIDI5OCwgMjA3LCAxMDAsIDIwNF0sCiAgICAgICAgICAn5qOS5p2Q5rex5Yqg5belJzogWzExMzM2MDAsIDExNzc0NzMsIDExMzk0NDAsIDEyMTQxMDgsIDExNDE3MzcsIDExNTM5MzBdLAogICAgICAgICAgJ+e6v+adkOa3seWKoOW3pSc6IFs2MTQwOTIsIDUwMTgxMCwgNTA5NzUwLCA1NTIyODAsIDU1OTQ0MCwgNTE3NzIwXSwKICAgICAgICAgICfng63nlLXliIbljoIt54Ot55S1JzogWzE1NzEzLCAzMzQ3MiwgMTgzNTQ5LCAyMTI3OSwgODI1MjUsIDMzOTY1XSwKICAgICAgICAgICfng63nlLXliIbljoIt6byT6aOOJzogWzU4NjM4LCAzNTY3OCwgMjA0MDE0LCAyODMwMywgNjA3NzAsIDU4MjQwXSwKICAgICAgICAgICfng63nlLXliIbljoIt5Lqa5Li055WMJzogWzk0MDYzLCA2MDMyNiwgMjczMTUsIDYzOTQxLCA5MDM4OSwgOTM4MzddLAogICAgICAgICAgJ+eFpOawlOWIhuWOgic6IFs2Mjg1LCA3Mjg5LCA3NjE2LCA3NTM3LCA3NTYxLCAzMDc1XSwKICAgICAgICAgICflhbTmvoTpkqLnkIMnOiBbMTk0OTcwLCAyMDYzMDcsIDIwODg5MCwgMjI1NDIyLCAyMDk0NjMsIDIxNTczMF0sCiAgICAgICAgICAn57u85ZCI5Yip55SoJzogWzAsIDAsIDAsIDAsIDAsIDBdLAogICAgICAgICAgJ+WQiOiuoSc6IFs1MTA5Mjk4LCA1NzIwMjQ1LCA2MTQ1MTUxLCA1ODk4Mzg2LCA1NjExMTEzLCA1OTQ3OTQ1XQoKICAgICAgICAgIC8vIOWkqeeEtuawlOa2iOiAlyAo5LiH56uL5pa557GzKQogICAgICAgICAgLy8gJ+eCvOmTgeWIhuWOgic6IFsyMjAsIDIxMCwgMjI1LCAyMzBdLAogICAgICAgICAgLy8gJ+eDp+e7k+WIhuWOgic6IFsxMDAsIDk1LCAxMDUsIDExMF0sCiAgICAgICAgICAvLyAn5LiA54K86ZKiJzogWzI1MCwgMjQwLCAyNTUsIDI2MF0sCiAgICAgICAgICAvLyAn5LqM54K86ZKiJzogWzIzMCwgMjIwLCAyMzUsIDI0MF0sCiAgICAgICAgICAvLyAn57u85ZCI5Yip55SoJzogWzc1LCA3MCwgNzgsIDc2XSwKICAgICAgICAgIC8vICfkuIDovafpkqInOiBbOTUsIDkwLCA5OCwgOTZdLAogICAgICAgICAgLy8gJ+S6jOi9p+mSoic6IFs5MCwgODUsIDkzLCA5MV0sCiAgICAgICAgICAvLyAn5LiJ6L2n6ZKiJzogWzg1LCA4MCwgODgsIDg2XSwKICAgICAgICAgIC8vICfnibnmrorpkqLovafmnZAnOiBbODAsIDc1LCA4MywgODFdLAogICAgICAgICAgLy8gJ+ajkuadkOi9p+WItuWOgic6IFs3NSwgNzAsIDc4LCA3Nl0sCiAgICAgICAgICAvLyAn5qOS57q/5p2Q5rex5Yqg5belKOajkiknOiBbNDUsIDQwLCA0OCwgNDZdLAogICAgICAgICAgLy8gJ+ajkue6v+adkOa3seWKoOW3pSjnur8pJzogWzQwLCAzNSwgNDMsIDQxXSwKICAgICAgICAgIC8vICfnur/mnZDmt7HliqDlt6UnOiBbMzUsIDMwLCAzOCwgMzZdLAogICAgICAgICAgLy8gJ+mprOenkeaJmOmSoueQgyc6IFsxMTAsIDEwNSwgMTEyLCAxMDhdLAogICAgICAgICAgLy8gJ+eJuemSoueCvOmSouWIhuWOgic6IFs5NSwgOTAsIDk4LCA5Nl0sCiAgICAgICAgICAvLyAn5Lit5p2/5YiG5Y6CJzogWzkwLCA4NSwgOTMsIDkxXSwKICAgICAgICAgIC8vICfljprmnb/liIbljoInOiBbODUsIDgwLCA4OCwgODZdLAogICAgICAgICAgLy8gJ+mSouadkOa3seWKoOW3pSc6IFs4MCwgNzUsIDgzLCA4MV0sCiAgICAgICAgICAvLyAn54Ot55S1JzogWzM1LCAzMCwgMzgsIDM2XSwKICAgICAgICAgIC8vICfkvpvnlLXlt6XljLonOiBbMjUsIDIwLCAyOCwgMjZdLAogICAgICAgICAgLy8gJ+awtOWkhOeQhuWIhuWOgic6IFsyMCwgMTUsIDIzLCAyMV0sCiAgICAgICAgICAvLyAn5Yi25rCn5YiG5Y6CJzogWzE1LCAxMCwgMTgsIDE2XSwKICAgICAgICAgIC8vICfnhaTmsJTliIbljoInOiBbMTAsIDUsIDEzLCAxMV0sCiAgICAgICAgICAvLyAn5YKo6L+Q5YWs5Y+4JzogWzQ1LCA0MywgNDYsIDQ0XSwKICAgICAgICAgIC8vICfmo4Dkv67liIbljoInOiBbMzUsIDM0LCAzNiwgMzUuNV0KICAgICAgICB9LAogICAgICAgIHN0ZWFtOiB7CiAgICAgICAgICAvLyDokrjmsb3mtojogJcgKOS4h+WQqCkKICAgICAgICAgICfpkpnkuJrliIbljoInOiBbMCwgMCwgMCwgMCwgMCwgMF0sCiAgICAgICAgICAn55+/5rij5b6u57KJJzogWzAsIDAsIDAsIDAsIDAsIDBdLAogICAgICAgICAgJ+eDp+e7k+WIhuWOgi0xI+eDp+e7kyc6IFswLCAwLCAwLCAwLCAwLCAwXSwKICAgICAgICAgICfng6fnu5PliIbljoItMiPng6fnu5MnOiBbMjM2OCwgMjM3OSwgMTc2NSwgMTYxNSwgMTQyMiwgMTY2M10sCiAgICAgICAgICAn54K86ZOB5YiG5Y6CLTEj6auY54KJJzogWzU3OCwgNjM3LCA0ODUsIDU1NCwgMzg4LCA2NzFdLAogICAgICAgICAgJ+eCvOmTgeWIhuWOgi0yI+mrmOeCiSc6IFsyOTUsIDE0MSwgMTA5LCAyNjUsIDQxOSwgMzEyXSwKICAgICAgICAgICfngrzpk4HliIbljoItMyPpq5jngoknOiBbMTQ0NSwgMjE0MywgMTYzMywgMTI0LCAwLCAxMjddLAogICAgICAgICAgJ+S4gOeCvOmSoic6IFsxMTYsIDM4OCwgNTAsIDUwLCA5NiwgMTY0XSwKICAgICAgICAgICfkuozngrzpkqInOiBbMTI5MjcsIDE2NDQzLCAxNzYzOCwgMTcwNzEsIDE2ODQzLCAxNjM1MV0sCiAgICAgICAgICAn5LiA6L2n6ZKiJzogWzAsIDAsIDAsIDAsIDAsIDBdLAogICAgICAgICAgJ+S6jOi9py3lpKfmo5InOiBbMjA5LCAyMjIsIDI1MywgMTMzLCAxOTgsIDExNl0sCiAgICAgICAgICAn5LqM6L2nLeWwj+ajkic6IFsxOTMsIDIwNCwgMjMzLCAxMjMsIDE4MiwgMTA4XSwKICAgICAgICAgICfnibnmnb/ngrzpkqInOiBbMjI5NjgsIDIyMzM2LCAxODgxOSwgMjE2NDcsIDIxNjA0LCAyMTcwOF0sCiAgICAgICAgICAnMzUwMOS4readvyc6IFswLCAwLCAwLCAwLCAwLCAwXSwKICAgICAgICAgICc0MzAw5Y6a5p2/JzogWzAsIDAsIDAsIDAsIDAsIDBdLAogICAgICAgICAgJ+mrmOe6v+WIhuWOgic6IFswLCAwLCAwLCAwLCAwLCAwXSwKICAgICAgICAgICfmo5LmnZDmt7HliqDlt6UnOiBbMCwgMCwgMCwgMCwgMCwgMF0sCiAgICAgICAgICAn57q/5p2Q5rex5Yqg5belJzogWzIyNDAsIDE2MTYsIDEzNjQsIDExMjEsIDEwODIsIDkyMF0sCiAgICAgICAgICAn54Ot55S15YiG5Y6CLeeDreeUtSc6IFs4NTE5OSwgOTA1ODMsIDgzMDc1LCA5MzEwOCwgODk2MTUsIDk1MTU2XSwKICAgICAgICAgICfng63nlLXliIbljoIt6byT6aOOJzogWzAsIDAsIDAsIDAsIDAsIDBdLAogICAgICAgICAgJ+eDreeUteWIhuWOgi3ng63msLQnOiBbMCwgMCwgMCwgMCwgMCwgMF0sCiAgICAgICAgICAn54Wk5rCU5YiG5Y6CJzogWzAsIDAsIDAsIDAsIDAsIDBdLAogICAgICAgICAgJ+WItuawp+WIhuWOgic6IFs0MDAsIDQwMCwgNDAwLCA0MDAsIDQwMCwgNDAwXSwKICAgICAgICAgICflkI7li6Tpg6gnOiBbMCwgMCwgMCwgMCwgMCwgMF0sCiAgICAgICAgICAn5aSW6ZSA6JK45rG9JzogWzMyNTY4LCAxNzUzNCwgMjczMzQsIDI0MzExLCAyMTEyNiwgMTk1MDRdLAogICAgICAgICAgJ+e7vOWQiOWIqeeUqCc6IFsyMTYsIDMxLCAxNiwgMTksIDMyLCA0Nl0sCiAgICAgICAgICAn5o2f6ICXJzogWzEyNzAsIDExNjYsIDEwODgsIDkwNywgODM4LCA4MjVdLAogICAgICAgICAgJ+WQiOiuoSc6IFsxNjI5OTIsIDE1NjIyMywgMTU0MjYyLCAxNjE0NDgsIDE1NDI0NSwgMTU4MDcxXQogICAgICAgICAgLy8gJ+eCvOmTgeWIhuWOgic6IFszMCwgMjgsIDMxLCAzMl0sCiAgICAgICAgICAvLyAn54On57uT5YiG5Y6CJzogWzE1LCAxMywgMTYsIDE3XSwKICAgICAgICAgIC8vICfkuIDngrzpkqInOiBbMzUsIDMzLCAzNiwgMzddLAogICAgICAgICAgLy8gJ+S6jOeCvOmSoic6IFszMCwgMjgsIDMxLCAzMl0sCiAgICAgICAgICAvLyAn57u85ZCI5Yip55SoJzogWzEwLCA5LCAxMSwgMTAuNV0sCiAgICAgICAgICAvLyAn5LiA6L2n6ZKiJzogWzEzLCAxMiwgMTQsIDEzLjVdLAogICAgICAgICAgLy8gJ+S6jOi9p+mSoic6IFsxMiwgMTEsIDEzLCAxMi41XSwKICAgICAgICAgIC8vICfkuInovafpkqInOiBbMTEsIDEwLCAxMiwgMTEuNV0sCiAgICAgICAgICAvLyAn54m55q6K6ZKi6L2n5p2QJzogWzEwLCA5LCAxMSwgMTAuNV0sCiAgICAgICAgICAvLyAn5qOS5p2Q6L2n5Yi25Y6CJzogWzksIDgsIDEwLCA5LjVdLAogICAgICAgICAgLy8gJ+ajkue6v+adkOa3seWKoOW3pSjmo5IpJzogWzUsIDQsIDYsIDUuNV0sCiAgICAgICAgICAvLyAn5qOS57q/5p2Q5rex5Yqg5belKOe6vyknOiBbNCwgMywgNSwgNC41XSwKICAgICAgICAgIC8vICfnur/mnZDmt7HliqDlt6UnOiBbMywgMiwgNCwgMy41XSwKICAgICAgICAgIC8vICfpqaznp5HmiZjpkqLnkIMnOiBbMTUsIDE0LjUsIDE1LjUsIDE1LjJdLAogICAgICAgICAgLy8gJ+eJuemSoueCvOmSouWIhuWOgic6IFsxMywgMTIsIDE0LCAxMy41XSwKICAgICAgICAgIC8vICfkuK3mnb/liIbljoInOiBbMTIsIDExLCAxMywgMTIuNV0sCiAgICAgICAgICAvLyAn5Y6a5p2/5YiG5Y6CJzogWzExLCAxMCwgMTIsIDExLjVdLAogICAgICAgICAgLy8gJ+mSouadkOa3seWKoOW3pSc6IFsxMCwgOSwgMTEsIDEwLjVdLAogICAgICAgICAgLy8gJ+eDreeUtSc6IFs1LCA0LCA2LCA1LjVdLAogICAgICAgICAgLy8gJ+S+m+eUteW3peWMuic6IFszLCAyLCA0LCAzLjVdLAogICAgICAgICAgLy8gJ+awtOWkhOeQhuWIhuWOgic6IFsyLCAxLCAzLCAyLjVdLAogICAgICAgICAgLy8gJ+WItuawp+WIhuWOgic6IFsxLjUsIDAuNSwgMi41LCAyXSwKICAgICAgICAgIC8vICfnhaTmsJTliIbljoInOiBbMSwgMC44LCAxLjIsIDEuMV0sCiAgICAgICAgICAvLyAn5YKo6L+Q5YWs5Y+4JzogWzgsIDcuOCwgOC4yLCA4LjFdLAogICAgICAgICAgLy8gJ+ajgOS/ruWIhuWOgic6IFs2LCA1LjgsIDYuMiwgNi4xXQogICAgICAgIH0KICAgICAgfSwKICAgICAgLy8g5YWz6ZSu6IO95rqQ5oyH5qCH5b2T5pel5YC85LiO5pio5pel5a+55q+UCiAgICAgIGtleUVuZXJneUluZGljYXRvcnM6IFtdLAogICAgICAvLyBbCiAgICAgIC8vICAgewogICAgICAvLyAgICAgbmFtZTogJ+awp+awlCcsCiAgICAgIC8vICAgICB0b2RheTogNDYuODAsIC8vIOi2heWHuuebruagh+iMg+WbtOS4iumZkAogICAgICAvLyAgICAgeWVzdGVyZGF5OiA0NC42NCwKICAgICAgLy8gICAgIHVuaXQ6ICdtwrMvdCcsCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDQyLjAwLAogICAgICAvLyAgICAgdGFyZ2V0TWF4OiA0NS4wMCwKICAgICAgLy8gICAgIGNoYW5nZTogNC44NCwKICAgICAgLy8gICAgIHN0YXR1czogJ2RhbmdlcicKICAgICAgLy8gICB9LAogICAgICAvLyAgIHsKICAgICAgLy8gICAgIG5hbWU6ICfmsK7msJQnLAogICAgICAvLyAgICAgdG9kYXk6IDE0LjgyLAogICAgICAvLyAgICAgeWVzdGVyZGF5OiAxNS4yMSwKICAgICAgLy8gICAgIHVuaXQ6ICdtwrMvdCcsCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDE0LjAwLAogICAgICAvLyAgICAgdGFyZ2V0TWF4OiAxNi4wMCwKICAgICAgLy8gICAgIGNoYW5nZTogLTIuNTYsCiAgICAgIC8vICAgICBzdGF0dXM6ICdnb29kJwogICAgICAvLyAgIH0sCiAgICAgIC8vICAgewogICAgICAvLyAgICAgbmFtZTogJ+awqeawlCcsCiAgICAgIC8vICAgICB0b2RheTogMC44NSwKICAgICAgLy8gICAgIHllc3RlcmRheTogMC44OCwKICAgICAgLy8gICAgIHVuaXQ6ICdtwrMvdCcsCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDAuODAsCiAgICAgIC8vICAgICB0YXJnZXRNYXg6IDAuOTAsCiAgICAgIC8vICAgICBjaGFuZ2U6IC0zLjQxLAogICAgICAvLyAgICAgc3RhdHVzOiAnZ29vZCcKICAgICAgLy8gICB9LAogICAgICAvLyAgIHsKICAgICAgLy8gICAgIG5hbWU6ICfnqbrmsJQnLAogICAgICAvLyAgICAgdG9kYXk6IDQ1MCwgLy8g5L2O5LqO55uu5qCH6IyD5Zu05LiL6ZmQCiAgICAgIC8vICAgICB5ZXN0ZXJkYXk6IDQ4MSwKICAgICAgLy8gICAgIHVuaXQ6ICdtwrMvaCcsCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDQ2MCwKICAgICAgLy8gICAgIHRhcmdldE1heDogNTAwLAogICAgICAvLyAgICAgY2hhbmdlOiAtNi40NCwKICAgICAgLy8gICAgIHN0YXR1czogJ2RhbmdlcicKICAgICAgLy8gICB9LAogICAgICAvLyAgIHsKICAgICAgLy8gICAgIG5hbWU6ICfpq5jngonnhaTmsJQnLAogICAgICAvLyAgICAgdG9kYXk6IDk4NS4zLAogICAgICAvLyAgICAgeWVzdGVyZGF5OiA5NjIuNywKICAgICAgLy8gICAgIHVuaXQ6ICdtwrMvdCcsCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDk1MC4wLAogICAgICAvLyAgICAgdGFyZ2V0TWF4OiAxMDAwLjAsCiAgICAgIC8vICAgICBjaGFuZ2U6IDIuMzUsCiAgICAgIC8vICAgICBzdGF0dXM6ICdnb29kJwogICAgICAvLyAgIH0sCiAgICAgIC8vICAgewogICAgICAvLyAgICAgbmFtZTogJ+i9rOeCieeFpOawlCcsCiAgICAgIC8vICAgICB0b2RheTogODUuMiwKICAgICAgLy8gICAgIHllc3RlcmRheTogODguNCwKICAgICAgLy8gICAgIHVuaXQ6ICdtwrMvdCcsCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDgwLjAsCiAgICAgIC8vICAgICB0YXJnZXRNYXg6IDkwLjAsCiAgICAgIC8vICAgICBjaGFuZ2U6IC0zLjYyLAogICAgICAvLyAgICAgc3RhdHVzOiAnZ29vZCcKICAgICAgLy8gICB9LAogICAgICAvLyAgIHsKICAgICAgLy8gICAgIG5hbWU6ICfnhKbngonnhaTmsJQnLAogICAgICAvLyAgICAgdG9kYXk6IDQxLjMsCiAgICAgIC8vICAgICB5ZXN0ZXJkYXk6IDQyLjgsCiAgICAgIC8vICAgICB1bml0OiAnbcKzL3QnLAogICAgICAvLyAgICAgdGFyZ2V0TWluOiA0MC4wLAogICAgICAvLyAgICAgdGFyZ2V0TWF4OiA0NS4wLAogICAgICAvLyAgICAgY2hhbmdlOiAtMy41MCwKICAgICAgLy8gICAgIHN0YXR1czogJ2dvb2QnCiAgICAgIC8vICAgfSwKICAgICAgLy8gICB7CiAgICAgIC8vICAgICBuYW1lOiAn6aWx5ZKM6JK45rG9JywKICAgICAgLy8gICAgIHRvZGF5OiAwLjUyLAogICAgICAvLyAgICAgeWVzdGVyZGF5OiAwLjU0LAogICAgICAvLyAgICAgdW5pdDogJ3QvdCcsCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDAuNTAsCiAgICAgIC8vICAgICB0YXJnZXRNYXg6IDAuNTgsCiAgICAgIC8vICAgICBjaGFuZ2U6IC0zLjcwLAogICAgICAvLyAgICAgc3RhdHVzOiAnZ29vZCcKICAgICAgLy8gICB9LAogICAgICAvLyAgIHsKICAgICAgLy8gICAgIG5hbWU6ICfov4fng63okrjmsb0nLAogICAgICAvLyAgICAgdG9kYXk6IDAuMzMsCiAgICAgIC8vICAgICB5ZXN0ZXJkYXk6IDAuMzEsCiAgICAgIC8vICAgICB1bml0OiAndC90JywKICAgICAgLy8gICAgIHRhcmdldE1pbjogMC4zMCwKICAgICAgLy8gICAgIHRhcmdldE1heDogMC4zNSwKICAgICAgLy8gICAgIGNoYW5nZTogNi40NSwKICAgICAgLy8gICAgIHN0YXR1czogJ3dhcm5pbmcnCiAgICAgIC8vICAgfSwKICAgICAgLy8gICB7CiAgICAgIC8vICAgICBuYW1lOiAn5L2O5Y6L6JK45rG9JywKICAgICAgLy8gICAgIHRvZGF5OiAwLjIxLAogICAgICAvLyAgICAgeWVzdGVyZGF5OiAwLjIzLAogICAgICAvLyAgICAgdW5pdDogJ3QvdCcsCiAgICAgIC8vICAgICB0YXJnZXRNaW46IDAuMjAsCiAgICAgIC8vICAgICB0YXJnZXRNYXg6IDAuMjUsCiAgICAgIC8vICAgICBjaGFuZ2U6IC04LjcwLAogICAgICAvLyAgICAgc3RhdHVzOiAnZ29vZCcKICAgICAgLy8gICB9LAogICAgICAvLyAgIHsKICAgICAgLy8gICAgIG5hbWU6ICflpKnnhLbmsJQnLAogICAgICAvLyAgICAgdG9kYXk6IDI0LjMsCiAgICAgIC8vICAgICB5ZXN0ZXJkYXk6IDI1LjEsCiAgICAgIC8vICAgICB1bml0OiAnbcKzL3QnLAogICAgICAvLyAgICAgdGFyZ2V0TWluOiAyMi4wLAogICAgICAvLyAgICAgdGFyZ2V0TWF4OiAyNi4wLAogICAgICAvLyAgICAgY2hhbmdlOiAtMy4xOSwKICAgICAgLy8gICAgIHN0YXR1czogJ2dvb2QnCiAgICAgIC8vICAgfSwKICAgICAgLy8gICB7CiAgICAgIC8vICAgICBuYW1lOiAn5Y6L57yp5aSp54S25rCUJywKICAgICAgLy8gICAgIHRvZGF5OiAyLjg1LAogICAgICAvLyAgICAgeWVzdGVyZGF5OiAyLjkxLAogICAgICAvLyAgICAgdW5pdDogJ23Csy90JywKICAgICAgLy8gICAgIHRhcmdldE1pbjogMi43MCwKICAgICAgLy8gICAgIHRhcmdldE1heDogMy4wMCwKICAgICAgLy8gICAgIGNoYW5nZTogLTIuMDYsCiAgICAgIC8vICAgICBzdGF0dXM6ICdnb29kJwogICAgICAvLyAgIH0KICAgICAgLy8gXSwKCiAgICAgIC8vIOiDvea6kOaMh+agh+aYr+WQpui2heWHuuebruagh+iMg+WbtOagh+iusAogICAgICBpc0VuZXJneUNoYXJ0V2FybmluZzogewogICAgICAgIGVsZWN0cmljaXR5OiBmYWxzZSwKICAgICAgICB3YXRlcjogZmFsc2UsCiAgICAgICAgZ2FzOiBmYWxzZSwKICAgICAgICBzdGVhbTogZmFsc2UKICAgICAgfSwKICAgICAgLy8g6IO95rqQ55uu5qCH6IyD5Zu0CiAgICAgIGVuZXJneVRhcmdldFJhbmdlczogewogICAgICAgIGVsZWN0cmljaXR5OiB7CiAgICAgICAgICBtaW46IDgwMCwKICAgICAgICAgIG1heDogOTAwLAogICAgICAgICAgdW5pdDogJ+WNg+eTpuaXticKICAgICAgICB9LAogICAgICAgIHdhdGVyOiB7CiAgICAgICAgICBtaW46IDUwLAogICAgICAgICAgbWF4OiA2MCwKICAgICAgICAgIHVuaXQ6ICflkKgnCiAgICAgICAgfSwKICAgICAgICBnYXM6IHsKICAgICAgICAgIG1pbjogMjAwLAogICAgICAgICAgbWF4OiAyNDAsCiAgICAgICAgICB1bml0OiAn56uL5pa557GzJwogICAgICAgIH0sCiAgICAgICAgc3RlYW06IHsKICAgICAgICAgIG1pbjogMjUsCiAgICAgICAgICBtYXg6IDM1LAogICAgICAgICAgdW5pdDogJ+WQqCcKICAgICAgICB9CiAgICAgIH0KICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgLy8g6K6h566X5omA5pyJ6YOo6Zeo55qE5a6M5oiQ546H5pWw5o2u77yI5Y+q5pi+56S65YiG5Y6C5bGC57qn77yJCiAgICBkZXBhcnRtZW50Q29tcGxldGlvblJhdGVzOiBmdW5jdGlvbiBkZXBhcnRtZW50Q29tcGxldGlvblJhdGVzKCkgewogICAgICAvLyDmjInpg6jpl6jliIbnu4TorqHnrpflrozmiJDnjofvvIzlj6rlpITnkIbmnIkiLSLnmoTliIbljoLlsYLnuqcKICAgICAgdmFyIGRlcGFydG1lbnRSYXRlcyA9IHRoaXMuY29tcGxldGlvbkRhdGEuZmlsdGVyKGZ1bmN0aW9uIChkZXB0KSB7CiAgICAgICAgcmV0dXJuIGRlcHQuZGVwYXJ0bWVudC5pbmNsdWRlcygnLScpOwogICAgICB9KSAvLyDlj6rpgInmi6nliIbljoLlsYLnuqcKICAgICAgLm1hcChmdW5jdGlvbiAoZGVwdCkgewogICAgICAgIHZhciBpbmRpY2F0b3JzID0gZGVwdC5pbmRpY2F0b3JzOwogICAgICAgIHZhciBjb21wbGV0ZWRDb3VudCA9IDA7CiAgICAgICAgdmFyIHRvdGFsSW5kaWNhdG9ycyA9IGluZGljYXRvcnMubGVuZ3RoOwoKICAgICAgICAvLyDorqHnrpflt7LlrozmiJDmjIfmoIfmlbDph4/vvIzkvb/nlKg05pyI5Lu95pWw5o2uCiAgICAgICAgaW5kaWNhdG9ycy5mb3JFYWNoKGZ1bmN0aW9uIChpbmRpY2F0b3IpIHsKICAgICAgICAgIC8vIOS9v+eUqDTmnIjku73mlbDmja7vvIjnrKwz5Liq57Si5byV77yJCiAgICAgICAgICB2YXIgYWN0dWFsID0gaW5kaWNhdG9yLnZhbHVlc1szXTsgLy8g56ys5Zub5ZGoLzTmnIjku73nmoTlrp7pmYXlgLwKICAgICAgICAgIHZhciB0YXJnZXQgPSBpbmRpY2F0b3IudGFyZ2V0OwogICAgICAgICAgdmFyIGlzQ29tcGxldGVkID0gaW5kaWNhdG9yLmlzSGlnaGVyQmV0dGVyID8gYWN0dWFsID49IHRhcmdldCA6IGFjdHVhbCA8PSB0YXJnZXQ7CiAgICAgICAgICBpZiAoaXNDb21wbGV0ZWQpIGNvbXBsZXRlZENvdW50Kys7CiAgICAgICAgfSk7CgogICAgICAgIC8vIOiuoeeul+WujOaIkOeOhwogICAgICAgIHZhciBjb21wbGV0aW9uUmF0ZSA9IGNvbXBsZXRlZENvdW50IC8gdG90YWxJbmRpY2F0b3JzICogMTAwOwogICAgICAgIHJldHVybiB7CiAgICAgICAgICBkZXBhcnRtZW50OiBkZXB0LmRlcGFydG1lbnQuc3BsaXQoJy0nKVsxXSwKICAgICAgICAgIC8vIOWPquaYvuekuuWIhuWOguWQjeensAogICAgICAgICAgZnVsbERlcGFydG1lbnQ6IGRlcHQuZGVwYXJ0bWVudCwKICAgICAgICAgIC8vIOS/neWtmOWujOaVtOmDqOmXqOWQjeensOeUqOS6juaVsOaNruafpeivogogICAgICAgICAgY29tcGxldGlvblJhdGU6IHBhcnNlRmxvYXQoY29tcGxldGlvblJhdGUudG9GaXhlZCgxKSksCiAgICAgICAgICB0b3RhbEluZGljYXRvcnM6IHRvdGFsSW5kaWNhdG9ycywKICAgICAgICAgIGNvbXBsZXRlZEluZGljYXRvcnM6IGNvbXBsZXRlZENvdW50CiAgICAgICAgfTsKICAgICAgfSk7CgogICAgICAvLyDmjInlrozmiJDnjofku47pq5jliLDkvY7mjpLluo8KICAgICAgcmV0dXJuIGRlcGFydG1lbnRSYXRlcy5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7CiAgICAgICAgcmV0dXJuIGIuY29tcGxldGlvblJhdGUgLSBhLmNvbXBsZXRpb25SYXRlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDorqHnrpfmiYDmnInmjIfmoIfnmoTlrozmiJDnjofvvIg05pyI5Lu95pWw5o2u77yJCiAgICBhbGxJbmRpY2F0b3JDb21wbGV0aW9uUmF0ZXM6IGZ1bmN0aW9uIGFsbEluZGljYXRvckNvbXBsZXRpb25SYXRlcygpIHsKICAgICAgdmFyIGFsbEluZGljYXRvcnMgPSBbXTsKICAgICAgdGhpcy5jb21wbGV0aW9uRGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChkZXB0KSB7CiAgICAgICAgaWYgKGRlcHQuZGVwYXJ0bWVudC5pbmNsdWRlcygnLScpKSB7CiAgICAgICAgICAvLyDlj6rpgInmi6nliIbljoLlsYLnuqcKICAgICAgICAgIHZhciBkZXBhcnRtZW50TmFtZSA9IGRlcHQuZGVwYXJ0bWVudC5zcGxpdCgnLScpWzFdOwogICAgICAgICAgZGVwdC5pbmRpY2F0b3JzLmZvckVhY2goZnVuY3Rpb24gKGluZGljYXRvcikgewogICAgICAgICAgICAvLyDnm7TmjqXkvb/nlKgwNOaciOWIl+eahOWujOaIkOeOh+aVsOaNrgogICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmnIDlkI7kuIDliJfvvIgwNOaciO+8ieeahOWujOaIkOeOh+aVsOaNrgogICAgICAgICAgICB2YXIgY29tcGxldGlvblJhdGVTdHIgPSBpbmRpY2F0b3IuY29tcGxldGlvblJhdGVzICYmIGluZGljYXRvci5jb21wbGV0aW9uUmF0ZXNbM107IC8vIDA05pyI5a6M5oiQ546HCiAgICAgICAgICAgIHZhciBjb21wbGV0aW9uUmF0ZSA9IDA7CgogICAgICAgICAgICAvLyDlpoLmnpzmnInnm7TmjqXnmoTlrozmiJDnjofmlbDmja7vvIzliJnkvb/nlKjlroMKICAgICAgICAgICAgaWYgKGNvbXBsZXRpb25SYXRlU3RyKSB7CiAgICAgICAgICAgICAgLy8g5bCG55m+5YiG5q+U5a2X56ym5Liy6L2s5o2i5Li65pWw5YC877yM5L6L5aaCICItMS4zMSUiIC0+IC0xLjMxCiAgICAgICAgICAgICAgY29tcGxldGlvblJhdGUgPSBwYXJzZUZsb2F0KGNvbXBsZXRpb25SYXRlU3RyLnJlcGxhY2UoJyUnLCAnJykpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIC8vIOWmguaenOayoeacieebtOaOpeaVsOaNru+8jOWImeeUqOWOn+adpeeahOaWueazleiuoeeulwogICAgICAgICAgICAgIHZhciBhY3R1YWwgPSBpbmRpY2F0b3IudmFsdWVzWzNdOyAvLyA05pyI5Lu95a6e6ZmF5YC8CiAgICAgICAgICAgICAgdmFyIHRhcmdldCA9IGluZGljYXRvci50YXJnZXQ7CiAgICAgICAgICAgICAgaWYgKGluZGljYXRvci5pc0hpZ2hlckJldHRlcikgewogICAgICAgICAgICAgICAgLy8g5aaC5p6c5oyH5qCH6LaK6auY6LaK5aW977yM5a6M5oiQ546HID0g5a6e6ZmF5YC8L+ebruagh+WAvCAqIDEwMCUgLSAxMDAlCiAgICAgICAgICAgICAgICBjb21wbGV0aW9uUmF0ZSA9IGFjdHVhbCAvIHRhcmdldCAqIDEwMCAtIDEwMDsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgLy8g5aaC5p6c5oyH5qCH6LaK5L2O6LaK5aW977yM5a6M5oiQ546HID0g55uu5qCH5YC8L+WunumZheWAvCAqIDEwMCUgLSAxMDAlCiAgICAgICAgICAgICAgICBjb21wbGV0aW9uUmF0ZSA9IHRhcmdldCAvIGFjdHVhbCAqIDEwMCAtIDEwMDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOWvueWujOaIkOeOh+i/m+ihjOWkhOeQhgogICAgICAgICAgICAvLyDpnZ7mlbDlrZflrozmiJDnjofosIPmlbTkuLowCiAgICAgICAgICAgIGlmIChpc05hTihjb21wbGV0aW9uUmF0ZSkgfHwgIWlzRmluaXRlKGNvbXBsZXRpb25SYXRlKSkgewogICAgICAgICAgICAgIGNvbXBsZXRpb25SYXRlID0gMDsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgLy8g6ZmQ5Yi25pyA5aSn5a6M5oiQ546H57ud5a+55YC85Li6MjAwJQogICAgICAgICAgICBpZiAoTWF0aC5hYnMoY29tcGxldGlvblJhdGUpID4gMjAwKSB7CiAgICAgICAgICAgICAgY29tcGxldGlvblJhdGUgPSBjb21wbGV0aW9uUmF0ZSA+IDAgPyAyMDAgOiAtMjAwOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGFsbEluZGljYXRvcnMucHVzaCh7CiAgICAgICAgICAgICAgZGVwYXJ0bWVudDogZGVwYXJ0bWVudE5hbWUsCiAgICAgICAgICAgICAgaW5kaWNhdG9yOiBpbmRpY2F0b3IubmFtZSwKICAgICAgICAgICAgICBjb21wbGV0aW9uUmF0ZTogcGFyc2VGbG9hdChjb21wbGV0aW9uUmF0ZS50b0ZpeGVkKDEpKSwKICAgICAgICAgICAgICB0YXJnZXQ6IGluZGljYXRvci50YXJnZXQsCiAgICAgICAgICAgICAgYWN0dWFsOiBpbmRpY2F0b3IudmFsdWVzWzNdLAogICAgICAgICAgICAgIC8vIDTmnIjku73lrp7pmYXlgLwKICAgICAgICAgICAgICB1bml0OiBpbmRpY2F0b3IudW5pdCwKICAgICAgICAgICAgICBpc0hpZ2hlckJldHRlcjogaW5kaWNhdG9yLmlzSGlnaGVyQmV0dGVyCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKCiAgICAgIC8vIOaMieWujOaIkOeOh+S7jumrmOWIsOS9juaOkuW6jwogICAgICByZXR1cm4gYWxsSW5kaWNhdG9ycy5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7CiAgICAgICAgcmV0dXJuIGIuY29tcGxldGlvblJhdGUgLSBhLmNvbXBsZXRpb25SYXRlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDnm7TmjqXkvb/nlKjmiYDmnInmjIfmoIflrozmiJDnjofmlbDmja4KICAgIHBhZ2luYXRlZEluZGljYXRvclJhdGVzOiBmdW5jdGlvbiBwYWdpbmF0ZWRJbmRpY2F0b3JSYXRlcygpIHsKICAgICAgcmV0dXJuIHRoaXMuYWxsSW5kaWNhdG9yQ29tcGxldGlvblJhdGVzOwogICAgfSwKICAgIC8vIOiOt+WPluS6i+S4mumDqOWIl+ihqO+8iOeUqOS6juetm+mAie+8iQogICAgYnVzaW5lc3NVbml0czogZnVuY3Rpb24gYnVzaW5lc3NVbml0cygpIHsKICAgICAgdmFyIHVuaXRzID0gbmV3IFNldCgpOwogICAgICB0aGlzLmNvbXBsZXRpb25EYXRhLmZvckVhY2goZnVuY3Rpb24gKGRlcHQpIHsKICAgICAgICBpZiAoZGVwdC5kZXBhcnRtZW50LmluY2x1ZGVzKCctJykpIHsKICAgICAgICAgIHVuaXRzLmFkZChkZXB0LmRlcGFydG1lbnQuc3BsaXQoJy0nKVswXSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHVuaXRzLmFkZChkZXB0LmRlcGFydG1lbnQpOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiBBcnJheS5mcm9tKHVuaXRzKTsKICAgIH0sCiAgICAvLyDojrflj5blvZPliY3pgInmi6nnmoTkuovkuJrpg6jkuIvnmoTliIbljoLliJfooagKICAgIGN1cnJlbnRCdXNpbmVzc1VuaXREZXBhcnRtZW50czogZnVuY3Rpb24gY3VycmVudEJ1c2luZXNzVW5pdERlcGFydG1lbnRzKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICBpZiAoIXRoaXMuY3VycmVudEJ1c2luZXNzVW5pdCkgcmV0dXJuIFtdOwogICAgICByZXR1cm4gdGhpcy5jb21wbGV0aW9uRGF0YS5maWx0ZXIoZnVuY3Rpb24gKGRlcHQpIHsKICAgICAgICByZXR1cm4gZGVwdC5kZXBhcnRtZW50LnN0YXJ0c1dpdGgoX3RoaXMuY3VycmVudEJ1c2luZXNzVW5pdCArICctJyk7CiAgICAgIH0pLm1hcChmdW5jdGlvbiAoZGVwdCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICB2YWx1ZTogZGVwdC5kZXBhcnRtZW50LAogICAgICAgICAgbGFiZWw6IGRlcHQuZGVwYXJ0bWVudC5zcGxpdCgnLScpWzFdCiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6I635Y+W5b2T5YmN6YOo6Zeo55qE5oyH5qCH5YiX6KGoCiAgICBjdXJyZW50RGVwYXJ0bWVudEluZGljYXRvcnM6IGZ1bmN0aW9uIGN1cnJlbnREZXBhcnRtZW50SW5kaWNhdG9ycygpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHZhciBkZXB0RGF0YSA9IHRoaXMuY29tcGxldGlvbkRhdGEuZmluZChmdW5jdGlvbiAoZGVwdCkgewogICAgICAgIHJldHVybiBkZXB0LmRlcGFydG1lbnQgPT09IF90aGlzMi5jdXJyZW50RGVwYXJ0bWVudDsKICAgICAgfSk7CiAgICAgIHJldHVybiBkZXB0RGF0YSA/IGRlcHREYXRhLmluZGljYXRvcnMgOiBbXTsKICAgIH0sCiAgICAvLyDojrflj5bmiYDmnInliIbljoLliJfooajvvIjnlKjkuo7og73mupDmtojogJfor6bmg4XvvIkKICAgIGFsbEZhY3RvcmllczogZnVuY3Rpb24gYWxsRmFjdG9yaWVzKCkgewogICAgICByZXR1cm4gdGhpcy5jb21wbGV0aW9uRGF0YTEubWFwKGZ1bmN0aW9uIChkZXB0KSB7CiAgICAgICAgcmV0dXJuIGRlcHQuZGVwYXJ0bWVudDsKICAgICAgfSk7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgLy8gLy8g55uR5ZCs5LqL5Lia6YOo5Y+Y5YyWCiAgICAvLyBjdXJyZW50QnVzaW5lc3NVbml0KG5ld1ZhbCkgewogICAgLy8gICBpZiAodGhpcy5jdXJyZW50QnVzaW5lc3NVbml0RGVwYXJ0bWVudHMubGVuZ3RoID4gMCkgewogICAgLy8gICAgIHRoaXMuY3VycmVudERlcGFydG1lbnQgPSB0aGlzLmN1cnJlbnRCdXNpbmVzc1VuaXREZXBhcnRtZW50c1swXS52YWx1ZQogICAgLy8gICB9CgogICAgLy8gICAvLyDlkK/liqjoh6rliqjliIfmjaIKICAgIC8vICAgdGhpcy5zdGFydEF1dG9Td2l0Y2hXaXRoaW5CdXNpbmVzc1VuaXQoKQogICAgLy8gfSwKCiAgICAvLyAvLyDnm5HlkKzpg6jpl6jlj5jljJbvvIzoh6rliqjpgInmi6nnrKzkuIDkuKrmjIfmoIcKICAgIC8vIGN1cnJlbnREZXBhcnRtZW50KG5ld1ZhbCkgewogICAgLy8gICBpZiAodGhpcy5jdXJyZW50RGVwYXJ0bWVudEluZGljYXRvcnMubGVuZ3RoID4gMCkgewogICAgLy8gICAgIHRoaXMuY3VycmVudEluZGljYXRvciA9IHRoaXMuY3VycmVudERlcGFydG1lbnRJbmRpY2F0b3JzWzBdLm5hbWUKICAgIC8vICAgfQogICAgLy8gICB0aGlzLmluaXRNb250aGx5VHJlbmRDaGFydCgpCiAgICAvLyB9LAoKICAgIC8vIC8vIOebkeWQrOaMh+agh+WPmOWMlu+8jOabtOaWsOWbvuihqAogICAgLy8gY3VycmVudEluZGljYXRvcigpIHsKICAgIC8vICAgdGhpcy5pbml0TW9udGhseVRyZW5kQ2hhcnQoKQogICAgLy8gfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgLy8g5Yid5aeL5YyW5Zu+6KGoCiAgICB0aGlzLmluaXRDaGFydHMoKTsKCiAgICAvLyDorqHnrpfmnKrlrozmiJDmjIfmoIfmlbDmja4KICAgIHRoaXMuY2FsY3VsYXRlSW5jb21wbGV0ZURhdGEoKTsKCiAgICAvLyDnoa7kv51jdXJyZW50RW5lcmd5RGVwdOacieS4gOS4quacieaViOeahOWIneWni+WAvAogICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICBfdGhpczMuaW5pdEVuZXJneURldGFpbENoYXJ0cygpOwogICAgICBpZiAoX3RoaXMzLmFsbEZhY3RvcmllcyAmJiBfdGhpczMuYWxsRmFjdG9yaWVzLmxlbmd0aCA+IDApIHsKICAgICAgICBfdGhpczMuY3VycmVudEVuZXJneURlcHQgPSBfdGhpczMuYWxsRmFjdG9yaWVzWzBdOwogICAgICAgIC8vIOmHjeaWsOWIneWni+WMluiDvea6kOivpuaDheWbvuihqAogICAgICAgIF90aGlzMy5pbml0RW5lcmd5RGV0YWlsQ2hhcnRzKCk7CiAgICAgIH0KICAgIH0pOwogICAgdGhpcy5sb2FkRXhjZWxGcm9tUmVtb3RlKCk7CiAgICAvLyDlkK/liqjmu5rliqjooajmoLwKICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgLy8g5aSN5Yi26KGo5qC85YaF5a655Lul5a6e546w5peg57yd5rua5YqoCiAgICAgIHZhciB0YWJsZSA9IF90aGlzMy4kcmVmcy5pbmNvbXBsZXRlVGFibGU7CiAgICAgIGlmICh0YWJsZSAmJiBfdGhpczMuaW5jb21wbGV0ZURhdGEubGVuZ3RoID4gMCkgewogICAgICAgIC8vIOiOt+WPluihqOagvOWGheWuuemDqOWIhu+8iOS4jeWMheWQq+ihqOWktO+8iQogICAgICAgIHZhciB0YWJsZUJvZHkgPSB0YWJsZS5xdWVyeVNlbGVjdG9yKCcuZWwtdGFibGVfX2JvZHktd3JhcHBlcicpOwogICAgICAgIGlmICh0YWJsZUJvZHkpIHsKICAgICAgICAgIHZhciBvcmlnaW5hbENvbnRlbnQgPSB0YWJsZUJvZHkuaW5uZXJIVE1MOwogICAgICAgICAgLy8g5Zyo6KGo5qC85YaF5a655ZCO6Z2i5re75Yqg5LiA5Lu955u45ZCM55qE5YaF5a6577yM6ICM5LiN5piv5pW05Liq6KGo5qC8CiAgICAgICAgICB0YWJsZUJvZHkuaW5uZXJIVE1MICs9IG9yaWdpbmFsQ29udGVudDsKICAgICAgICB9CiAgICAgIH0KICAgICAgX3RoaXMzLnN0YXJ0VGFibGVTY3JvbGwoKTsKICAgIH0pOwoKICAgIC8vIOWQr+WKqOS6i+S4mumDqOWGhemDqOiHquWKqOWIh+aNogogICAgdGhpcy5zdGFydEF1dG9Td2l0Y2hXaXRoaW5CdXNpbmVzc1VuaXQoKTsKCiAgICAvLyDlkK/liqjkuovkuJrpg6jliIfmjaLvvIjmr48zMOenkuWIh+aNouS4gOasoe+8iQogICAgdGhpcy5zdGFydEJ1c2luZXNzVW5pdFN3aXRjaCgpOwoKICAgIC8vIOWQr+WKqOaMh+agh+WNoeeJh+iHquWKqOa7muWKqAogICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICBfdGhpczMuaW5pdEluZGljYXRvckNhcmRzU2Nyb2xsKCk7CgogICAgICAvLyDkuLrmjIfmoIfljaHniYflrrnlmajmt7vliqDpvKDmoIfmgqzlgZzkuovku7blpITnkIYKICAgICAgdmFyIGNhcmRzQ29udGFpbmVyID0gX3RoaXMzLiRyZWZzLmluZGljYXRvckNhcmRzQ29udGFpbmVyOwogICAgICBpZiAoY2FyZHNDb250YWluZXIpIHsKICAgICAgICBjYXJkc0NvbnRhaW5lci5hZGRFdmVudExpc3RlbmVyKCdtb3VzZW92ZXInLCBmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpczMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxQYXVzZWQgPSB0cnVlOwogICAgICAgIH0pOwogICAgICAgIGNhcmRzQ29udGFpbmVyLmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlb3V0JywgZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXMzLmluZGljYXRvckNhcmRzU2Nyb2xsUGF1c2VkID0gZmFsc2U7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0pOwoKICAgIC8vIOebkeWQrOeql+WPo+Wkp+Wwj+WPmOWMlu+8jOmHjeaWsOa4suafk+WbvuihqAogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQ2hhcnRzKTsKICB9LAogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgIC8vIOa4hemZpOWumuaXtuWZqAogICAgdGhpcy5zdG9wQXV0b1N3aXRjaCgpOwogICAgaWYgKHRoaXMuc2Nyb2xsVGltZXIpIHsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnNjcm9sbFRpbWVyKTsKCiAgICAgIC8vIOenu+mZpOihqOagvOa7muWKqOeahOS6i+S7tuebkeWQrOWZqAogICAgICB2YXIgdGFibGUgPSB0aGlzLiRyZWZzLmluY29tcGxldGVUYWJsZTsKICAgICAgaWYgKHRhYmxlKSB7CiAgICAgICAgdmFyIHRhYmxlQm9keSA9IHRhYmxlLnF1ZXJ5U2VsZWN0b3IoJy5lbC10YWJsZV9fYm9keS13cmFwcGVyJyk7CiAgICAgICAgaWYgKHRhYmxlQm9keSAmJiB0aGlzLnRhYmxlTW91c2VFbnRlckhhbmRsZXIgJiYgdGhpcy50YWJsZU1vdXNlTGVhdmVIYW5kbGVyKSB7CiAgICAgICAgICB0YWJsZUJvZHkucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2VlbnRlcicsIHRoaXMudGFibGVNb3VzZUVudGVySGFuZGxlcik7CiAgICAgICAgICB0YWJsZUJvZHkucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2VsZWF2ZScsIHRoaXMudGFibGVNb3VzZUxlYXZlSGFuZGxlcik7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgICBpZiAodGhpcy5idXNpbmVzc1VuaXRUaW1lcikgewogICAgICBjbGVhckludGVydmFsKHRoaXMuYnVzaW5lc3NVbml0VGltZXIpOwogICAgfQogICAgaWYgKHRoaXMuZW5lcmd5RGVwdFRpbWVyKSB7CiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5lbmVyZ3lEZXB0VGltZXIpOwogICAgfQogICAgaWYgKHRoaXMuY29tcGxldGlvbkNoYXJ0VGltZXIpIHsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmNvbXBsZXRpb25DaGFydFRpbWVyKTsKICAgIH0KICAgIGlmICh0aGlzLmluZGljYXRvckNhcmRzU2Nyb2xsVGltZXIpIHsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmluZGljYXRvckNhcmRzU2Nyb2xsVGltZXIpOwogICAgfQoKICAgIC8vIOenu+mZpOeql+WPo+Wkp+Wwj+WPmOWMluebkeWQrAogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQ2hhcnRzKTsKCiAgICAvLyDnp7vpmaTmjIfmoIfljaHniYflrrnlmajnmoTkuovku7bnm5HlkKzlmagKICAgIHZhciBjYXJkc0NvbnRhaW5lciA9IHRoaXMuJHJlZnMuaW5kaWNhdG9yQ2FyZHNDb250YWluZXI7CiAgICBpZiAoY2FyZHNDb250YWluZXIpIHsKICAgICAgY2FyZHNDb250YWluZXIucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2VvdmVyJywgZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNC5pbmRpY2F0b3JDYXJkc1Njcm9sbFBhdXNlZCA9IHRydWU7CiAgICAgIH0pOwogICAgICBjYXJkc0NvbnRhaW5lci5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZW91dCcsIGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQuaW5kaWNhdG9yQ2FyZHNTY3JvbGxQYXVzZWQgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9CgogICAgLy8g6ZSA5q+B5Zu+6KGo5a6e5L6LCiAgICB0aGlzLmRpc3Bvc2VDaGFydHMoKTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldERhdGVVcGRhdGVMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliJ3lp4vljJbmiYDmnInlm77ooagKICAgIGxvYWRFeGNlbEZyb21SZW1vdGU6IGZ1bmN0aW9uIGxvYWRFeGNlbEZyb21SZW1vdGUoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLmV4Y2VsTG9hZGluZyA9IHRydWU7CiAgICAgIHZhciB1cmwgPSAiaHR0cHM6Ly95ZHh0LmNpdGljc3RlZWwuY29tOjgwOTkvbWluaW8veGN0Zy90ZW1wL2pt5YWz6ZSu5oqA5b6E5oyH5qCHLnhsc3giOwogICAgICAoMCwgX2F4aW9zLmRlZmF1bHQpKHsKICAgICAgICBtZXRob2Q6ICJnZXQiLAogICAgICAgIHVybDogdXJsLAogICAgICAgIHJlc3BvbnNlVHlwZTogImFycmF5YnVmZmVyIgogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIHZhciBkYXRhID0gbmV3IFVpbnQ4QXJyYXkocmVzcG9uc2UuZGF0YSk7CiAgICAgICAgdmFyIHdvcmtib29rID0gWExTWC5yZWFkKGRhdGEsIHsKICAgICAgICAgIHR5cGU6ICJhcnJheSIKICAgICAgICB9KTsKICAgICAgICB2YXIgZmlyc3RTaGVldCA9IHdvcmtib29rLlNoZWV0c1t3b3JrYm9vay5TaGVldE5hbWVzWzBdXTsKICAgICAgICB2YXIganNvbkRhdGEgPSBYTFNYLnV0aWxzLnNoZWV0X3RvX2pzb24oZmlyc3RTaGVldCwgewogICAgICAgICAgaGVhZGVyOiAxLAogICAgICAgICAgcmFuZ2U6IDEKICAgICAgICB9KTsKICAgICAgICBjb25zb2xlLmxvZyhqc29uRGF0YSk7CiAgICAgICAgX3RoaXM1LnByb2Nlc3NFeGNlbERhdGEoanNvbkRhdGEpOwogICAgICAgIF90aGlzNS5leGNlbExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcigi5Yqg6L29RXhjZWzmlofku7blpLHotKU6IiwgZXJyb3IpOwogICAgICAgIF90aGlzNS4kbWVzc2FnZS5lcnJvcigi5Yqg6L29RXhjZWzmlofku7blpLHotKXvvIzor7fnqI3lkI7ph43or5UiKTsKICAgICAgICBfdGhpczUuZXhjZWxMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGdldERldGFpbDogZnVuY3Rpb24gZ2V0RGV0YWlsKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgKDAsIF9kaW1lbnNpb25hbGl0eS5kaW1lbnNpb25hbGl0eWxpc3RQZXJtaXNzaW9uTGlzdCkoKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczYuYWRtaW5TaG93ID0gcmVzLm1zZzsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0RGF0ZVVwZGF0ZUxpc3Q6IGZ1bmN0aW9uIGdldERhdGVVcGRhdGVMaXN0KCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgKDAsIF9hbnN3ZXIuZGF0ZVVwZGF0ZUxpc3QpKCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM3LmtleUVuZXJneUluZGljYXRvcnMgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgcHJvY2Vzc0V4Y2VsRGF0YTogZnVuY3Rpb24gcHJvY2Vzc0V4Y2VsRGF0YShqc29uRGF0YSkgewogICAgICBpZiAoIWpzb25EYXRhIHx8IGpzb25EYXRhLmxlbmd0aCA8IDIpIHJldHVybjsKICAgICAgdmFyIGhlYWRlcnMgPSBqc29uRGF0YVswXTsKICAgICAgdmFyIGNvbEluZGV4ZXMgPSB7CiAgICAgICAgZmFjdG9yeTogaGVhZGVycy5pbmRleE9mKCLliIbljoIiKSwKICAgICAgICBuYW1lOiBoZWFkZXJzLmluZGV4T2YoIuaMh+agh+WQjeensCIpLAogICAgICAgIHRhcmdldDogaGVhZGVycy5pbmRleE9mKCLnm67moIciKSwKICAgICAgICB1bml0OiBoZWFkZXJzLmluZGV4T2YoIuWNleS9jSIpLAogICAgICAgIGphbjogaGVhZGVycy5pbmRleE9mKCIwMeaciOWunue7qSIpLAogICAgICAgIGphblN0YXR1czogaGVhZGVycy5pbmRleE9mKCIwMeaciOWunue7qSIpICsgMSwKICAgICAgICBmZWI6IGhlYWRlcnMuaW5kZXhPZigiMDLmnIjlrp7nu6kiKSwKICAgICAgICBmZWJTdGF0dXM6IGhlYWRlcnMuaW5kZXhPZigiMDLmnIjlrp7nu6kiKSArIDEsCiAgICAgICAgbWFyOiBoZWFkZXJzLmluZGV4T2YoIjAz5pyI5a6e57upIiksCiAgICAgICAgbWFyU3RhdHVzOiBoZWFkZXJzLmluZGV4T2YoIjAz5pyI5a6e57upIikgKyAxLAogICAgICAgIGFwcjogaGVhZGVycy5pbmRleE9mKCIwNOaciOWunue7qSIpLAogICAgICAgIGFwclN0YXR1czogaGVhZGVycy5pbmRleE9mKCIwNOaciOWunue7qSIpICsgMSwKICAgICAgICBtYXk6IGhlYWRlcnMuaW5kZXhPZigiMDXmnIjlrp7nu6kiKSwKICAgICAgICBtYXlTdGF0dXM6IGhlYWRlcnMuaW5kZXhPZigiMDXmnIjlrp7nu6kiKSArIDEKICAgICAgfTsKICAgICAgdmFyIGRhdGFSb3dzID0ganNvbkRhdGEuc2xpY2UoMSk7CiAgICAgIHZhciB0ZWNoSW5kaWNhdG9ycyA9IGRhdGFSb3dzLm1hcChmdW5jdGlvbiAocm93KSB7CiAgICAgICAgaWYgKCFyb3cgfHwgcm93Lmxlbmd0aCA9PT0gMCkgcmV0dXJuIG51bGw7CiAgICAgICAgaWYgKHJvd1tjb2xJbmRleGVzLmZhY3RvcnldID09PSB1bmRlZmluZWQgfHwgcm93W2NvbEluZGV4ZXMubmFtZV0gPT09IHVuZGVmaW5lZCkgcmV0dXJuIG51bGw7CiAgICAgICAgdmFyIGluZGljYXRvciA9IHsKICAgICAgICAgIGZhY3Rvcnk6IHJvd1tjb2xJbmRleGVzLmZhY3RvcnldIHx8ICIiLAogICAgICAgICAgbmFtZTogcm93W2NvbEluZGV4ZXMubmFtZV0gfHwgIiIsCiAgICAgICAgICB0YXJnZXQ6IHJvd1tjb2xJbmRleGVzLnRhcmdldF0gIT09IHVuZGVmaW5lZCA/IFN0cmluZyhyb3dbY29sSW5kZXhlcy50YXJnZXRdKSA6ICIiLAogICAgICAgICAgdW5pdDogcm93W2NvbEluZGV4ZXMudW5pdF0gfHwgIiIsCiAgICAgICAgICBqYW46IHJvd1tjb2xJbmRleGVzLmphbl0gIT09IHVuZGVmaW5lZCA/IFN0cmluZyhyb3dbY29sSW5kZXhlcy5qYW5dKSA6ICIiLAogICAgICAgICAgamFuU3RhdHVzOiByb3dbY29sSW5kZXhlcy5qYW5TdGF0dXNdID09PSAxLjAgPyAxIDogMCwKICAgICAgICAgIGZlYjogcm93W2NvbEluZGV4ZXMuZmViXSAhPT0gdW5kZWZpbmVkID8gU3RyaW5nKHJvd1tjb2xJbmRleGVzLmZlYl0pIDogIiIsCiAgICAgICAgICBmZWJTdGF0dXM6IHJvd1tjb2xJbmRleGVzLmZlYlN0YXR1c10gPT09IDEuMCA/IDEgOiAwLAogICAgICAgICAgbWFyOiByb3dbY29sSW5kZXhlcy5tYXJdICE9PSB1bmRlZmluZWQgPyBTdHJpbmcocm93W2NvbEluZGV4ZXMubWFyXSkgOiAiIiwKICAgICAgICAgIG1hclN0YXR1czogcm93W2NvbEluZGV4ZXMubWFyU3RhdHVzXSA9PT0gMS4wID8gMSA6IDAsCiAgICAgICAgICBhcHI6IHJvd1tjb2xJbmRleGVzLmFwcl0gIT09IHVuZGVmaW5lZCA/IFN0cmluZyhyb3dbY29sSW5kZXhlcy5hcHJdKSA6ICIiLAogICAgICAgICAgYXByU3RhdHVzOiByb3dbY29sSW5kZXhlcy5hcHJTdGF0dXNdID09PSAxLjAgPyAxIDogMCwKICAgICAgICAgIG1heTogcm93W2NvbEluZGV4ZXMubWF5XSAhPT0gdW5kZWZpbmVkID8gU3RyaW5nKHJvd1tjb2xJbmRleGVzLm1heV0pIDogIiIsCiAgICAgICAgICBtYXlTdGF0dXM6IHJvd1tjb2xJbmRleGVzLm1heVN0YXR1c10gPT09IDEuMCA/IDEgOiAwCiAgICAgICAgfTsKICAgICAgICBpbmRpY2F0b3IubW9udGhseURhdGEgPSBbcGFyc2VGbG9hdChpbmRpY2F0b3IuamFuKSB8fCAwLCBwYXJzZUZsb2F0KGluZGljYXRvci5mZWIpIHx8IDAsIHBhcnNlRmxvYXQoaW5kaWNhdG9yLm1hcikgfHwgMCwgcGFyc2VGbG9hdChpbmRpY2F0b3IuYXByKSB8fCAwLCBwYXJzZUZsb2F0KGluZGljYXRvci5tYXkpIHx8IDBdOwogICAgICAgIHJldHVybiBpbmRpY2F0b3I7CiAgICAgIH0pLmZpbHRlcihCb29sZWFuKTsKCiAgICAgIC8vIOWkhOeQhuebuOWQjOWIhuWOguWSjOaMh+agh+WQjeensOeahOaVsOaNru+8jOehruS/neaVsOaNruS4gOiHtOaApwogICAgICB2YXIgdW5pcXVlS2V5cyA9IG5ldyBNYXAoKTsKICAgICAgdmFyIHVuaXF1ZUluZGljYXRvcnMgPSBbXTsKCiAgICAgIC8vIOWFiOWkhOeQhuebuOWQjOaMh+agh+eahOWQiOW5tgogICAgICB0ZWNoSW5kaWNhdG9ycy5mb3JFYWNoKGZ1bmN0aW9uIChpbmRpY2F0b3IpIHsKICAgICAgICB2YXIga2V5ID0gIiIuY29uY2F0KGluZGljYXRvci5mYWN0b3J5LCAiXyIpLmNvbmNhdChpbmRpY2F0b3IubmFtZSwgIl8iKS5jb25jYXQoaW5kaWNhdG9yLnVuaXQpOwogICAgICAgIGlmICghdW5pcXVlS2V5cy5oYXMoa2V5KSkgewogICAgICAgICAgdW5pcXVlS2V5cy5zZXQoa2V5LCB1bmlxdWVJbmRpY2F0b3JzLmxlbmd0aCk7CiAgICAgICAgICB1bmlxdWVJbmRpY2F0b3JzLnB1c2goaW5kaWNhdG9yKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICB0aGlzLnRlY2hJbmRpY2F0b3JzID0gdW5pcXVlSW5kaWNhdG9yczsKICAgIH0sCiAgICBpbml0Q2hhcnRzOiBmdW5jdGlvbiBpbml0Q2hhcnRzKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIC8vIOWIneWni+WMlumDqOmXqOWujOaIkOeOh+afseeKtuWbvgogICAgICAgIF90aGlzOC5pbml0RGVwYXJ0bWVudENvbXBsZXRpb25DaGFydCgpOwoKICAgICAgICAvLyDorr7nva7pu5jorqTpg6jpl6jlkozmjIfmoIcKICAgICAgICBpZiAoX3RoaXM4LmN1cnJlbnRCdXNpbmVzc1VuaXREZXBhcnRtZW50cy5sZW5ndGggPiAwICYmICFfdGhpczguY3VycmVudERlcGFydG1lbnQpIHsKICAgICAgICAgIF90aGlzOC5jdXJyZW50RGVwYXJ0bWVudCA9IF90aGlzOC5jdXJyZW50QnVzaW5lc3NVbml0RGVwYXJ0bWVudHNbMF0udmFsdWU7CiAgICAgICAgfQogICAgICAgIGlmIChfdGhpczguY3VycmVudERlcGFydG1lbnRJbmRpY2F0b3JzLmxlbmd0aCA+IDAgJiYgIV90aGlzOC5jdXJyZW50SW5kaWNhdG9yKSB7CiAgICAgICAgICBfdGhpczguY3VycmVudEluZGljYXRvciA9IF90aGlzOC5jdXJyZW50RGVwYXJ0bWVudEluZGljYXRvcnNbMF0ubmFtZTsKICAgICAgICB9CgogICAgICAgIC8vIOWIneWni+WMluaciOW6pui2i+WKv+aKmOe6v+WbvgogICAgICAgIF90aGlzOC5pbml0TW9udGhseVRyZW5kQ2hhcnQoKTsKCiAgICAgICAgLy8g6K6+572u6IO95rqQ6YOo6Zeo6buY6K6k5YC85bm25Yid5aeL5YyW6IO95rqQ5a2Q5Zu+6KGoCiAgICAgICAgaWYgKF90aGlzOC5hbGxGYWN0b3JpZXMgJiYgX3RoaXM4LmFsbEZhY3Rvcmllcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICBpZiAoIV90aGlzOC5jdXJyZW50RW5lcmd5RGVwdCkgewogICAgICAgICAgICBfdGhpczguY3VycmVudEVuZXJneURlcHQgPSBfdGhpczguYWxsRmFjdG9yaWVzWzBdOwogICAgICAgICAgfQogICAgICAgICAgLy8g5Yid5aeL5YyW6IO95rqQ5a2Q5Zu+6KGoCiAgICAgICAgICBfdGhpczguaW5pdEVuZXJneURldGFpbENoYXJ0cygpOwogICAgICAgIH0KCiAgICAgICAgLy8g5ZCv5Yqo5LqL5Lia6YOo5YaF6YOo6Ieq5Yqo5YiH5o2iCiAgICAgICAgX3RoaXM4LnN0YXJ0QXV0b1N3aXRjaFdpdGhpbkJ1c2luZXNzVW5pdCgpOwoKICAgICAgICAvLyDlkK/liqjkuovkuJrpg6jliIfmjaLvvIjmr48zMOenkuWIh+aNouS4gOasoe+8iQogICAgICAgIF90aGlzOC5zdGFydEJ1c2luZXNzVW5pdFN3aXRjaCgpOwoKICAgICAgICAvLyDlkK/liqjog73mupDpg6jpl6joh6rliqjliIfmjaIKICAgICAgICBfdGhpczguc3RhcnRFbmVyZ3lEZXB0U3dpdGNoKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWQr+WKqOS6i+S4mumDqOWGhemDqOiHquWKqOWIh+aNogogICAgc3RhcnRBdXRvU3dpdGNoV2l0aGluQnVzaW5lc3NVbml0OiBmdW5jdGlvbiBzdGFydEF1dG9Td2l0Y2hXaXRoaW5CdXNpbmVzc1VuaXQoKSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICAvLyDlgZzmraLkuYvliY3nmoToh6rliqjliIfmjaIKICAgICAgdGhpcy5zdG9wQXV0b1N3aXRjaCgpOwoKICAgICAgLy8g5ZCv5Yqo5paw55qE6Ieq5Yqo5YiH5o2iCiAgICAgIHRoaXMudHJlbmRDaGFydFRpbWVyID0gc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzOS5zd2l0Y2hEZXBhcnRtZW50V2l0aGluQnVzaW5lc3NVbml0KCk7CiAgICAgIH0sIDMwMDApOyAvLyDliIbljoLliIfmjaLpopHnjofkuLoz56eSCiAgICB9LAogICAgLy8g5Zyo5b2T5YmN5LqL5Lia6YOo5YaF5YiH5o2i6YOo6ZeoCiAgICBzd2l0Y2hEZXBhcnRtZW50V2l0aGluQnVzaW5lc3NVbml0OiBmdW5jdGlvbiBzd2l0Y2hEZXBhcnRtZW50V2l0aGluQnVzaW5lc3NVbml0KCkgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgdmFyIGRlcGFydG1lbnRzID0gdGhpcy5jdXJyZW50QnVzaW5lc3NVbml0RGVwYXJ0bWVudHM7CiAgICAgIGlmIChkZXBhcnRtZW50cy5sZW5ndGggPD0gMSkgcmV0dXJuOwogICAgICB2YXIgY3VycmVudEluZGV4ID0gZGVwYXJ0bWVudHMuZmluZEluZGV4KGZ1bmN0aW9uIChkZXB0KSB7CiAgICAgICAgcmV0dXJuIGRlcHQudmFsdWUgPT09IF90aGlzMC5jdXJyZW50RGVwYXJ0bWVudDsKICAgICAgfSk7CiAgICAgIHZhciBuZXh0SW5kZXggPSAoY3VycmVudEluZGV4ICsgMSkgJSBkZXBhcnRtZW50cy5sZW5ndGg7CiAgICAgIHRoaXMuY3VycmVudERlcGFydG1lbnQgPSBkZXBhcnRtZW50c1tuZXh0SW5kZXhdLnZhbHVlOwogICAgfSwKICAgIC8vIOWkhOeQhuiHquWKqOWIh+aNouW8gOWFs+WPmOWMlgogICAgaGFuZGxlQXV0b1N3aXRjaENoYW5nZTogZnVuY3Rpb24gaGFuZGxlQXV0b1N3aXRjaENoYW5nZSh2YWx1ZSkgewogICAgICBpZiAodmFsdWUpIHsKICAgICAgICAvLyDlkK/liqjoh6rliqjliIfmjaIKICAgICAgICB0aGlzLnN0YXJ0QXV0b1N3aXRjaFdpdGhpbkJ1c2luZXNzVW5pdCgpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWBnOatouiHquWKqOWIh+aNogogICAgICAgIHRoaXMuc3RvcEF1dG9Td2l0Y2goKTsKICAgICAgfQogICAgfSwKICAgIC8vIOWQr+WKqOiHquWKqOWIh+aNogogICAgc3RhcnRBdXRvU3dpdGNoOiBmdW5jdGlvbiBzdGFydEF1dG9Td2l0Y2goKSB7CiAgICAgIHZhciBfdGhpczEgPSB0aGlzOwogICAgICBpZiAodGhpcy50cmVuZENoYXJ0VGltZXIpIHsKICAgICAgICBjbGVhckludGVydmFsKHRoaXMudHJlbmRDaGFydFRpbWVyKTsKICAgICAgfQogICAgICB0aGlzLnRyZW5kQ2hhcnRUaW1lciA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczEuc3dpdGNoRGVwYXJ0bWVudCgpOwogICAgICB9LCAzMDAwKTsKICAgIH0sCiAgICAvLyDlgZzmraLoh6rliqjliIfmjaIKICAgIHN0b3BBdXRvU3dpdGNoOiBmdW5jdGlvbiBzdG9wQXV0b1N3aXRjaCgpIHsKICAgICAgaWYgKHRoaXMudHJlbmRDaGFydFRpbWVyKSB7CiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnRyZW5kQ2hhcnRUaW1lcik7CiAgICAgICAgdGhpcy50cmVuZENoYXJ0VGltZXIgPSBudWxsOwogICAgICB9CiAgICB9LAogICAgLy8g5YiH5o2i5a6M5oiQ546H5Zu+6KGo6aG156CBCiAgICBjaGFuZ2VQYWdlOiBmdW5jdGlvbiBjaGFuZ2VQYWdlKHBhZ2UpIHsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IHBhZ2U7CiAgICAgIHRoaXMuaW5pdERlcGFydG1lbnRDb21wbGV0aW9uQ2hhcnQoKTsKICAgIH0sCiAgICAvLyDorqHnrpfmnKrlrozmiJDmjIfmoIfmlbDmja7vvIznrZvpgInotJ/mlbDlrozmiJDnjocKICAgIGNhbGN1bGF0ZUluY29tcGxldGVEYXRhOiBmdW5jdGlvbiBjYWxjdWxhdGVJbmNvbXBsZXRlRGF0YSgpIHsKICAgICAgdmFyIGluY29tcGxldGUgPSBbXTsKICAgICAgdGhpcy5jb21wbGV0aW9uRGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChkZXB0KSB7CiAgICAgICAgZGVwdC5pbmRpY2F0b3JzLmZvckVhY2goZnVuY3Rpb24gKGluZGljYXRvcikgewogICAgICAgICAgLy8g55u05o6l5L2/55SoMDTmnIjliJfnmoTlrozmiJDnjofmlbDmja4KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuacieacgOWQjuS4gOWIl++8iDA05pyI77yJ55qE5a6M5oiQ546H5pWw5o2uCiAgICAgICAgICB2YXIgY29tcGxldGlvblJhdGVTdHIgPSBpbmRpY2F0b3IuY29tcGxldGlvblJhdGVzICYmIGluZGljYXRvci5jb21wbGV0aW9uUmF0ZXNbM107IC8vIDA05pyI5a6M5oiQ546HCiAgICAgICAgICB2YXIgY29tcGxldGlvblJhdGUgPSAwOwoKICAgICAgICAgIC8vIOWmguaenOacieebtOaOpeeahOWujOaIkOeOh+aVsOaNru+8jOWImeS9v+eUqOWugwogICAgICAgICAgaWYgKGNvbXBsZXRpb25SYXRlU3RyKSB7CiAgICAgICAgICAgIC8vIOWwhueZvuWIhuavlOWtl+espuS4sui9rOaNouS4uuaVsOWAvO+8jOS+i+WmgiAiLTEuMzElIiAtPiAtMS4zMQogICAgICAgICAgICBjb21wbGV0aW9uUmF0ZSA9IHBhcnNlRmxvYXQoY29tcGxldGlvblJhdGVTdHIucmVwbGFjZSgnJScsICcnKSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDlpoLmnpzmsqHmnInnm7TmjqXmlbDmja7vvIzliJnnlKjljp/mnaXnmoTmlrnms5XorqHnrpcKICAgICAgICAgICAgdmFyIGFjdHVhbCA9IGluZGljYXRvci52YWx1ZXNbM107IC8vIDTmnIjku73lrp7pmYXlgLwKICAgICAgICAgICAgdmFyIHRhcmdldCA9IGluZGljYXRvci50YXJnZXQ7CiAgICAgICAgICAgIGlmIChpbmRpY2F0b3IuaXNIaWdoZXJCZXR0ZXIpIHsKICAgICAgICAgICAgICAvLyDlpoLmnpzmjIfmoIfotorpq5jotorlpb3vvIzlrozmiJDnjocgPSDlrp7pmYXlgLwv55uu5qCH5YC8ICogMTAwJSAtIDEwMCUKICAgICAgICAgICAgICBjb21wbGV0aW9uUmF0ZSA9IGFjdHVhbCAvIHRhcmdldCAqIDEwMCAtIDEwMDsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAvLyDlpoLmnpzmjIfmoIfotorkvY7otorlpb3vvIzlrozmiJDnjocgPSDnm67moIflgLwv5a6e6ZmF5YC8ICogMTAwJSAtIDEwMCUKICAgICAgICAgICAgICBjb21wbGV0aW9uUmF0ZSA9IHRhcmdldCAvIGFjdHVhbCAqIDEwMCAtIDEwMDsKICAgICAgICAgICAgfQogICAgICAgICAgfQoKICAgICAgICAgIC8vIOWPqua3u+WKoOWujOaIkOeOh+S4uui0n+aVsOeahOiusOW9lQogICAgICAgICAgaWYgKGNvbXBsZXRpb25SYXRlIDwgMCkgewogICAgICAgICAgICBpbmNvbXBsZXRlLnB1c2goewogICAgICAgICAgICAgIGRlcGFydG1lbnQ6IGRlcHQuZGVwYXJ0bWVudC5pbmNsdWRlcygnLScpID8gZGVwdC5kZXBhcnRtZW50LnNwbGl0KCctJylbMV0gOiBkZXB0LmRlcGFydG1lbnQsCiAgICAgICAgICAgICAgaW5kaWNhdG9yOiBpbmRpY2F0b3IubmFtZSwKICAgICAgICAgICAgICB0YXJnZXQ6IGluZGljYXRvci50YXJnZXQsCiAgICAgICAgICAgICAgYWN0dWFsOiBpbmRpY2F0b3IudmFsdWVzWzNdLAogICAgICAgICAgICAgIC8vIDTmnIjku73lrp7pmYXlgLwKICAgICAgICAgICAgICB1bml0OiBpbmRpY2F0b3IudW5pdCwKICAgICAgICAgICAgICBjb21wbGV0aW9uUmF0ZTogcGFyc2VGbG9hdChjb21wbGV0aW9uUmF0ZS50b0ZpeGVkKDEpKQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CgogICAgICAvLyDmjInlrozmiJDnjofku47kvY7liLDpq5jmjpLluo8KICAgICAgdGhpcy5pbmNvbXBsZXRlRGF0YSA9IGluY29tcGxldGUuc29ydChmdW5jdGlvbiAoYSwgYikgewogICAgICAgIHJldHVybiBhLmNvbXBsZXRpb25SYXRlIC0gYi5jb21wbGV0aW9uUmF0ZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5ZCv5Yqo6KGo5qC85rua5YqoCiAgICBzdGFydFRhYmxlU2Nyb2xsOiBmdW5jdGlvbiBzdGFydFRhYmxlU2Nyb2xsKCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIGlmICh0aGlzLmluY29tcGxldGVEYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICB2YXIgdGFibGUgPSB0aGlzLiRyZWZzLmluY29tcGxldGVUYWJsZTsKCiAgICAgICAgLy8g5aSN5Yi26KGo5qC85YaF5a655Lul5a6e546w5peg57yd5rua5YqoCiAgICAgICAgaWYgKHRhYmxlKSB7CiAgICAgICAgICAvLyDmuIXpmaTkuYvliY3nmoTmu5rliqjlrprml7blmagKICAgICAgICAgIGlmICh0aGlzLnNjcm9sbFRpbWVyKSB7CiAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5zY3JvbGxUaW1lcik7CiAgICAgICAgICB9CgogICAgICAgICAgLy8g6I635Y+W6KGo5qC85YaF5a656YOo5YiG77yI5LiN5YyF5ZCr6KGo5aS077yJCiAgICAgICAgICB2YXIgdGFibGVCb2R5ID0gdGFibGUucXVlcnlTZWxlY3RvcignLmVsLXRhYmxlX19ib2R5LXdyYXBwZXInKTsKICAgICAgICAgIGlmICh0YWJsZUJvZHkpIHsKICAgICAgICAgICAgLy8g5Yib5bu65paw55qE5rua5Yqo5a6a5pe25ZmoCiAgICAgICAgICAgIHZhciBzY3JvbGxUb3AgPSAwOwogICAgICAgICAgICB0aGlzLnNjcm9sbFRpbWVyID0gc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgIC8vIOWmguaenOaaguWBnOa7muWKqO+8jOWImeS4jeaJp+ihjOa7muWKqOaTjeS9nAogICAgICAgICAgICAgIGlmIChfdGhpczEwLnRhYmxlU2Nyb2xsUGF1c2VkKSByZXR1cm47CiAgICAgICAgICAgICAgc2Nyb2xsVG9wKys7CiAgICAgICAgICAgICAgaWYgKHNjcm9sbFRvcCA+PSB0YWJsZUJvZHkuc2Nyb2xsSGVpZ2h0IC8gMikgewogICAgICAgICAgICAgICAgc2Nyb2xsVG9wID0gMDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgdGFibGVCb2R5LnNjcm9sbFRvcCA9IHNjcm9sbFRvcDsKICAgICAgICAgICAgfSwgdGhpcy5zY3JvbGxTcGVlZCk7CgogICAgICAgICAgICAvLyDliJvlu7rpvKDmoIfkuovku7blpITnkIblh73mlbAKICAgICAgICAgICAgdGhpcy50YWJsZU1vdXNlRW50ZXJIYW5kbGVyID0gZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgIF90aGlzMTAudGFibGVTY3JvbGxQYXVzZWQgPSB0cnVlOwogICAgICAgICAgICB9OwogICAgICAgICAgICB0aGlzLnRhYmxlTW91c2VMZWF2ZUhhbmRsZXIgPSBmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgX3RoaXMxMC50YWJsZVNjcm9sbFBhdXNlZCA9IGZhbHNlOwogICAgICAgICAgICB9OwoKICAgICAgICAgICAgLy8g5re75Yqg6byg5qCH5oKs5YGc5LqL5Lu25aSE55CGCiAgICAgICAgICAgIHRhYmxlQm9keS5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWVudGVyJywgdGhpcy50YWJsZU1vdXNlRW50ZXJIYW5kbGVyKTsKICAgICAgICAgICAgdGFibGVCb2R5LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbGVhdmUnLCB0aGlzLnRhYmxlTW91c2VMZWF2ZUhhbmRsZXIpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8vIOWIh+aNoumDqOmXqOi2i+WKv+WbvgogICAgc3dpdGNoRGVwYXJ0bWVudDogZnVuY3Rpb24gc3dpdGNoRGVwYXJ0bWVudCgpIHsKICAgICAgdmFyIGN1cnJlbnRJbmRleCA9IHRoaXMuZGVwYXJ0bWVudHMuaW5kZXhPZih0aGlzLmN1cnJlbnREZXBhcnRtZW50KTsKICAgICAgdmFyIG5leHRJbmRleCA9IChjdXJyZW50SW5kZXggKyAxKSAlIHRoaXMuZGVwYXJ0bWVudHMubGVuZ3RoOwogICAgICB0aGlzLmN1cnJlbnREZXBhcnRtZW50ID0gdGhpcy5kZXBhcnRtZW50c1tuZXh0SW5kZXhdOwogICAgfSwKICAgIC8vIOWIneWni+WMlumDqOmXqOWujOaIkOeOh+afseeKtuWbviAtIOS/ruaUueS4uuaoquWQkeafseeKtuWbvgogICAgaW5pdERlcGFydG1lbnRDb21wbGV0aW9uQ2hhcnQ6IGZ1bmN0aW9uIGluaXREZXBhcnRtZW50Q29tcGxldGlvbkNoYXJ0KCkgewogICAgICB2YXIgX3RoaXMxMSA9IHRoaXM7CiAgICAgIHZhciBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdkZXBhcnRtZW50Q29tcGxldGlvbkNoYXJ0Jyk7CiAgICAgIHZhciBjaGFydCA9IGVjaGFydHMuaW5pdChjaGFydERvbSk7CgogICAgICAvLyDorqHnrpfpu5jorqTmmL7npLrnmoTnmb7liIbmr5QKICAgICAgdmFyIGRlZmF1bHREaXNwbGF5UGVyY2VudCA9IE1hdGgubWluKDYgLyB0aGlzLmFsbEluZGljYXRvckNvbXBsZXRpb25SYXRlcy5sZW5ndGggKiAxMDAsIDEwMCk7CgogICAgICAvLyDkvb/nlKjmjIfmoIfnuqfliKvnmoTlrozmiJDnjofmlbDmja4KICAgICAgdmFyIGxhYmVscyA9IHRoaXMucGFnaW5hdGVkSW5kaWNhdG9yUmF0ZXMubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGRlcHROYW1lOiBpdGVtLmRlcGFydG1lbnQsCiAgICAgICAgICBpbmRpY2F0b3JOYW1lOiBpdGVtLmluZGljYXRvciwKICAgICAgICAgIGZ1bGxUZXh0OiAiIi5jb25jYXQoaXRlbS5kZXBhcnRtZW50LCAiXG4iKS5jb25jYXQoaXRlbS5pbmRpY2F0b3IpCiAgICAgICAgfTsKICAgICAgfSk7CiAgICAgIHZhciByYXRlcyA9IHRoaXMucGFnaW5hdGVkSW5kaWNhdG9yUmF0ZXMubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uY29tcGxldGlvblJhdGU7CiAgICAgIH0pOwogICAgICB2YXIgb3B0aW9uID0gewogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywKICAgICAgICAgIGF4aXNQb2ludGVyOiB7CiAgICAgICAgICAgIHR5cGU6ICdzaGFkb3cnCiAgICAgICAgICB9LAogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiAocGFyYW1zKSB7CiAgICAgICAgICAgIHZhciBkYXRhID0gcGFyYW1zWzBdOwogICAgICAgICAgICB2YXIgaXRlbSA9IHRoaXMucGFnaW5hdGVkSW5kaWNhdG9yUmF0ZXNbZGF0YS5kYXRhSW5kZXhdOwogICAgICAgICAgICB2YXIgYWN0dWFsVGV4dCA9IGl0ZW0uaXNIaWdoZXJCZXR0ZXIgPyAiXHU1QjlFXHU5NjQ1XHU1MDNDOiAiLmNvbmNhdChpdGVtLmFjdHVhbCkuY29uY2F0KGl0ZW0udW5pdCkgOiAiXHU1QjlFXHU5NjQ1XHU1MDNDOiAiLmNvbmNhdChpdGVtLmFjdHVhbCkuY29uY2F0KGl0ZW0udW5pdCwgIiAiKTsKICAgICAgICAgICAgcmV0dXJuICIiLmNvbmNhdChpdGVtLmRlcGFydG1lbnQsICItIikuY29uY2F0KGl0ZW0uaW5kaWNhdG9yLCAiPGJyLz5cbiAgICAgICAgICAgICAgICAgICAgXHU3NkVFXHU2ODA3XHU1MDNDOiAiKS5jb25jYXQoaXRlbS50YXJnZXQpLmNvbmNhdChpdGVtLnVuaXQsICI8YnIvPlxuICAgICAgICAgICAgICAgICAgICAiKS5jb25jYXQoYWN0dWFsVGV4dCwgIjxici8+XG4gICAgICAgICAgICAgICAgICAgIFx1NUI4Q1x1NjIxMFx1NzM4NzogIikuY29uY2F0KGl0ZW0uY29tcGxldGlvblJhdGUsICIlIik7CiAgICAgICAgICB9LmJpbmQodGhpcykKICAgICAgICB9LAogICAgICAgIGdyaWQ6IHsKICAgICAgICAgIGxlZnQ6ICcwJywKICAgICAgICAgIC8vIOW3puenuzMwcHgKICAgICAgICAgIHJpZ2h0OiAnMCcsCiAgICAgICAgICBib3R0b206ICczJScsCiAgICAgICAgICB0b3A6ICczJScsCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUKICAgICAgICB9LAogICAgICAgIGRhdGFab29tOiBbewogICAgICAgICAgdHlwZTogJ3NsaWRlcicsCiAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgeUF4aXNJbmRleDogWzBdLAogICAgICAgICAgc3RhcnQ6IDAsCiAgICAgICAgICBlbmQ6IGRlZmF1bHREaXNwbGF5UGVyY2VudCwKICAgICAgICAgIHdpZHRoOiAxMCwKICAgICAgICAgIGhhbmRsZVNpemU6IDIwLAogICAgICAgICAgc2hvd0RldGFpbDogZmFsc2UsCiAgICAgICAgICB6b29tTG9jazogZmFsc2UsCiAgICAgICAgICBtb3ZlT25Nb3VzZVdoZWVsOiB0cnVlLAogICAgICAgICAgcHJldmVudERlZmF1bHRNb3VzZU1vdmU6IHRydWUKICAgICAgICB9LCB7CiAgICAgICAgICB0eXBlOiAnaW5zaWRlJywKICAgICAgICAgIHlBeGlzSW5kZXg6IFswXSwKICAgICAgICAgIHN0YXJ0OiAwLAogICAgICAgICAgZW5kOiBkZWZhdWx0RGlzcGxheVBlcmNlbnQsCiAgICAgICAgICB6b29tTG9jazogZmFsc2UKICAgICAgICB9XSwKICAgICAgICB0b29sYm94OiB7CiAgICAgICAgICBmZWF0dXJlOiB7CiAgICAgICAgICAgIGRhdGFab29tOiB7CiAgICAgICAgICAgICAgeUF4aXNJbmRleDogJ25vbmUnCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHJlc3RvcmU6IHt9LAogICAgICAgICAgICBzYXZlQXNJbWFnZToge30KICAgICAgICAgIH0sCiAgICAgICAgICByaWdodDogMTAsCiAgICAgICAgICB0b3A6IDAKICAgICAgICB9LAogICAgICAgIHhBeGlzOiB7CiAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgbmFtZTogJ+WujOaIkOeOhyglKScsCiAgICAgICAgICBtaW46IC0zMCwKICAgICAgICAgIC8vIOiwg+aVtOacgOWwj+WAvOS4ui0zMCXvvIzotrPlpJ/mmL7npLrotJ/mlbDlrozmiJDnjocKICAgICAgICAgIG1heDogMzAsCiAgICAgICAgICAvLyDosIPmlbTmnIDlpKflgLzkuLozMCXvvIzkvb/lm77ooajmm7TogZrnhKYKICAgICAgICAgIGF4aXNMaW5lOiB7CiAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnIzU0NzBjNicKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIHNwbGl0TGluZTogewogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICB0eXBlOiAnZGFzaGVkJywKICAgICAgICAgICAgICBjb2xvcjogJyNFMEU2RjEnCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHlBeGlzOiB7CiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLAogICAgICAgICAgZGF0YTogbGFiZWxzLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICByZXR1cm4gaXRlbS5mdWxsVGV4dDsKICAgICAgICAgIH0pLAogICAgICAgICAgYXhpc0xhYmVsOiB7CiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gZm9ybWF0dGVyKHZhbHVlKSB7CiAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlOwogICAgICAgICAgICB9LAogICAgICAgICAgICBjb2xvcjogJyMzMzMnLAogICAgICAgICAgICBsaW5lSGVpZ2h0OiAxNiwKICAgICAgICAgICAgbWFyZ2luOiAxMiwKICAgICAgICAgICAgcmljaDogewogICAgICAgICAgICAgIGRlcHQ6IHsKICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJywKICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDIwCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBpbmRpY2F0b3I6IHsKICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDIwCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgYXhpc0xpbmU6IHsKICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjNTQ3MGM2JwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBzZXJpZXM6IFt7CiAgICAgICAgICBuYW1lOiAn5a6M5oiQ546HJywKICAgICAgICAgIHR5cGU6ICdiYXInLAogICAgICAgICAgZGF0YTogcmF0ZXMsCiAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6IGZ1bmN0aW9uIGNvbG9yKHBhcmFtcykgewogICAgICAgICAgICAgIC8vIOagueaNruWujOaIkOeOh+iuvue9ruS4jeWQjOminOiJsgogICAgICAgICAgICAgIGlmIChwYXJhbXMuZGF0YSA+PSAxMDApIHsKICAgICAgICAgICAgICAgIHJldHVybiAnIzU0NzBjNic7IC8vIOiTneiJsu+8jOi2hei/hzEwMCUKICAgICAgICAgICAgICB9IGVsc2UgaWYgKHBhcmFtcy5kYXRhID49IDkwKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gJyM5MWNjNzUnOyAvLyDnu7/oibLvvIzlrozmiJDnjofpq5gKICAgICAgICAgICAgICB9IGVsc2UgaWYgKHBhcmFtcy5kYXRhID49IDcwKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gJyNmYWM4NTgnOyAvLyDpu4ToibLvvIzlrozmiJDnjofkuK3nrYkKICAgICAgICAgICAgICB9IGVsc2UgaWYgKHBhcmFtcy5kYXRhID49IDApIHsKICAgICAgICAgICAgICAgIHJldHVybiAnI2VlNjY2Nic7IC8vIOe6ouiJsu+8jOWujOaIkOeOh+S9jgogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICByZXR1cm4gJyNmZjUyNTInOyAvLyDmm7Tmt7HnmoTnuqLoibLvvIzotJ/mlbDlrozmiJDnjocKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICBwb3NpdGlvbjogJ3JpZ2h0JywKICAgICAgICAgICAgZm9ybWF0dGVyOiAne2N9JScsCiAgICAgICAgICAgIGNvbG9yOiBmdW5jdGlvbiBjb2xvcihwYXJhbXMpIHsKICAgICAgICAgICAgICAvLyDotJ/mlbDlrozmiJDnjofnlKjnmb3oibLmloflrZfvvIzmm7TliqDmmI7mmL4KICAgICAgICAgICAgICByZXR1cm4gcGFyYW1zLmRhdGEgPCAwID8gJyNmZmZmZmYnIDogJyMzMzMzMzMnOwogICAgICAgICAgICB9LAogICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsCiAgICAgICAgICAgIGRpc3RhbmNlOiAxNQogICAgICAgICAgfSwKICAgICAgICAgIGJhcldpZHRoOiAnNTAlJywKICAgICAgICAgIC8vIOafseWtkOabtOe7hgogICAgICAgICAgYmFyQ2F0ZWdvcnlHYXA6ICc0MCUnLAogICAgICAgICAgLy8g5aKe5Yqg5p+x5a2Q6Ze06LedCiAgICAgICAgICBhbmltYXRpb25EZWxheTogZnVuY3Rpb24gYW5pbWF0aW9uRGVsYXkoaWR4KSB7CiAgICAgICAgICAgIHJldHVybiBpZHggKiAxMDAgKyAxMDA7CiAgICAgICAgICB9CiAgICAgICAgfV0sCiAgICAgICAgYW5pbWF0aW9uRWFzaW5nOiAnZWxhc3RpY091dCcsCiAgICAgICAgYW5pbWF0aW9uRGVsYXlVcGRhdGU6IGZ1bmN0aW9uIGFuaW1hdGlvbkRlbGF5VXBkYXRlKGlkeCkgewogICAgICAgICAgcmV0dXJuIGlkeCAqIDU7CiAgICAgICAgfQogICAgICB9OwogICAgICBjaGFydC5zZXRPcHRpb24ob3B0aW9uKTsKCiAgICAgIC8vIOS/neWtmGRhdGFab29t54q25oCB77yM6Ziy5q2i5rua5Yqo5p2h5Zue5by5CiAgICAgIGNoYXJ0Lm9uKCdkYXRhem9vbScsIGZ1bmN0aW9uIChwYXJhbXMpIHsKICAgICAgICB2YXIgc3RhcnQgPSBwYXJhbXMuc3RhcnQsCiAgICAgICAgICBlbmQgPSBwYXJhbXMuZW5kOwogICAgICAgIG9wdGlvbi5kYXRhWm9vbVswXS5zdGFydCA9IHN0YXJ0OwogICAgICAgIG9wdGlvbi5kYXRhWm9vbVswXS5lbmQgPSBlbmQ7CiAgICAgICAgb3B0aW9uLmRhdGFab29tWzFdLnN0YXJ0ID0gc3RhcnQ7CiAgICAgICAgb3B0aW9uLmRhdGFab29tWzFdLmVuZCA9IGVuZDsKICAgICAgICBjaGFydC5zZXRPcHRpb24ob3B0aW9uKTsKICAgICAgfSk7CgogICAgICAvLyDliJvlu7rkuovku7blpITnkIblmagKICAgICAgdGhpcy5jaGFydE1vdXNlT3ZlckhhbmRsZXIgPSBmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMxMS5jb21wbGV0aW9uQ2hhcnRTY3JvbGxQYXVzZWQgPSB0cnVlOwogICAgICB9OwogICAgICB0aGlzLmNoYXJ0TW91c2VPdXRIYW5kbGVyID0gZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMTEuY29tcGxldGlvbkNoYXJ0U2Nyb2xsUGF1c2VkID0gZmFsc2U7CiAgICAgIH07CgogICAgICAvLyDmt7vliqDpvKDmoIfmgqzlgZzkuovku7bvvIzmmoLlgZzoh6rliqjmu5rliqgKICAgICAgY2hhcnREb20uYWRkRXZlbnRMaXN0ZW5lcignbW91c2VvdmVyJywgdGhpcy5jaGFydE1vdXNlT3ZlckhhbmRsZXIpOwoKICAgICAgLy8g5re75Yqg6byg5qCH56a75byA5LqL5Lu277yM5oGi5aSN6Ieq5Yqo5rua5YqoCiAgICAgIGNoYXJ0RG9tLmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlb3V0JywgdGhpcy5jaGFydE1vdXNlT3V0SGFuZGxlcik7CgogICAgICAvLyDnqpflj6PlpKflsI/lj5jljJbml7boh6rliqjosIPmlbTlm77ooajlpKflsI8KICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGZ1bmN0aW9uICgpIHsKICAgICAgICBjaGFydC5yZXNpemUoKTsKICAgICAgfSk7CgogICAgICAvLyDkv53lrZjlm77ooajlrp7kvovku6Xkvr/lkI7nu63kvb/nlKgKICAgICAgdGhpcy5jb21wbGV0aW9uQ2hhcnQgPSBjaGFydDsKICAgIH0sCiAgICAvLyDlkK/liqjlrozmiJDnjoflm77ooajoh6rliqjmu5rliqgKICAgIHN0YXJ0Q29tcGxldGlvbkNoYXJ0U2Nyb2xsOiBmdW5jdGlvbiBzdGFydENvbXBsZXRpb25DaGFydFNjcm9sbCgpIHsKICAgICAgdmFyIF90aGlzMTIgPSB0aGlzOwogICAgICBpZiAodGhpcy5jb21wbGV0aW9uQ2hhcnRUaW1lcikgewogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5jb21wbGV0aW9uQ2hhcnRUaW1lcik7CiAgICAgIH0KICAgICAgdGhpcy5jb21wbGV0aW9uQ2hhcnRUaW1lciA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICBpZiAoX3RoaXMxMi5jb21wbGV0aW9uQ2hhcnRTY3JvbGxQYXVzZWQpIHJldHVybjsKICAgICAgICBpZiAoIV90aGlzMTIuY29tcGxldGlvbkNoYXJ0KSByZXR1cm47CiAgICAgICAgdmFyIG9wdGlvbiA9IF90aGlzMTIuY29tcGxldGlvbkNoYXJ0LmdldE9wdGlvbigpOwogICAgICAgIHZhciBzdGFydCA9IG9wdGlvbi5kYXRhWm9vbVswXS5zdGFydDsKICAgICAgICB2YXIgZW5kID0gb3B0aW9uLmRhdGFab29tWzBdLmVuZDsKICAgICAgICB2YXIgc3RlcCA9IDAuMTsgLy8g5q+P5qyh5rua5Yqo55qE55m+5YiG5q+U77yM5pS55Li6MC4x77yM5L2/5rua5Yqo6Z2e5bi457yT5oWiCiAgICAgICAgdmFyIHJhbmdlID0gZW5kIC0gc3RhcnQ7IC8vIOW9k+WJjeaYvuekuueahOiMg+WbtAoKICAgICAgICAvLyDmoLnmja7mu5rliqjmlrnlkJHosIPmlbTmu5rliqjkvY3nva4KICAgICAgICBpZiAoX3RoaXMxMi5jb21wbGV0aW9uQ2hhcnRTY3JvbGxEaXJlY3Rpb24gPT09ICdkb3duJykgewogICAgICAgICAgLy8g5ZCR5LiL5rua5YqoCiAgICAgICAgICBpZiAoZW5kIDwgMTAwKSB7CiAgICAgICAgICAgIHN0YXJ0ICs9IHN0ZXA7CiAgICAgICAgICAgIGVuZCArPSBzdGVwOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgLy8g5bey5Yiw5bqV6YOo77yM5pS55Y+Y5pa55ZCRCiAgICAgICAgICAgIF90aGlzMTIuY29tcGxldGlvbkNoYXJ0U2Nyb2xsRGlyZWN0aW9uID0gJ3VwJzsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5ZCR5LiK5rua5YqoCiAgICAgICAgICBpZiAoc3RhcnQgPiAwKSB7CiAgICAgICAgICAgIHN0YXJ0IC09IHN0ZXA7CiAgICAgICAgICAgIGVuZCAtPSBzdGVwOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgLy8g5bey5Yiw6aG26YOo77yM5pS55Y+Y5pa55ZCRCiAgICAgICAgICAgIF90aGlzMTIuY29tcGxldGlvbkNoYXJ0U2Nyb2xsRGlyZWN0aW9uID0gJ2Rvd24nOwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLy8g5pu05paw5rua5Yqo5L2N572uCiAgICAgICAgb3B0aW9uLmRhdGFab29tWzBdLnN0YXJ0ID0gc3RhcnQ7CiAgICAgICAgb3B0aW9uLmRhdGFab29tWzBdLmVuZCA9IGVuZDsKICAgICAgICBvcHRpb24uZGF0YVpvb21bMV0uc3RhcnQgPSBzdGFydDsKICAgICAgICBvcHRpb24uZGF0YVpvb21bMV0uZW5kID0gZW5kOwogICAgICAgIF90aGlzMTIuY29tcGxldGlvbkNoYXJ0LnNldE9wdGlvbihvcHRpb24pOwogICAgICB9LCAzMDApOyAvLyDmu5rliqjpl7TpmpTmlLnkuLozMDDmr6vnp5LvvIzov5vkuIDmraXlh4/mhaLmu5rliqjpgJ/luqYKICAgIH0sCiAgICAvLyDliJ3lp4vljJbmnIjluqbotovlir/mipjnur/lm74gLSDmmL7npLrljZXmnaHmipjnur8KICAgIGluaXRNb250aGx5VHJlbmRDaGFydDogZnVuY3Rpb24gaW5pdE1vbnRobHlUcmVuZENoYXJ0KCkgewogICAgICB2YXIgX3RoaXMxMyA9IHRoaXM7CiAgICAgIHZhciBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdtb250aGx5VHJlbmRDaGFydCcpOwogICAgICB2YXIgY2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pOwoKICAgICAgLy8g5p+l5om+5b2T5YmN6YOo6Zeo55qE5pWw5o2uCiAgICAgIHZhciBkZXB0RGF0YSA9IHRoaXMuY29tcGxldGlvbkRhdGEuZmluZChmdW5jdGlvbiAoZGVwdCkgewogICAgICAgIHJldHVybiBkZXB0LmRlcGFydG1lbnQgPT09IF90aGlzMTMuY3VycmVudERlcGFydG1lbnQgfHwgZGVwdC5kZXBhcnRtZW50LnN0YXJ0c1dpdGgoX3RoaXMxMy5jdXJyZW50RGVwYXJ0bWVudCk7CiAgICAgIH0pOwogICAgICBpZiAoIWRlcHREYXRhIHx8ICF0aGlzLmN1cnJlbnRJbmRpY2F0b3IpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOafpeaJvuW9k+WJjeaMh+aghwogICAgICB2YXIgaW5kaWNhdG9yID0gZGVwdERhdGEuaW5kaWNhdG9ycy5maW5kKGZ1bmN0aW9uIChpbmQpIHsKICAgICAgICByZXR1cm4gaW5kLm5hbWUgPT09IF90aGlzMTMuY3VycmVudEluZGljYXRvcjsKICAgICAgfSk7CiAgICAgIGlmICghaW5kaWNhdG9yKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDlh4blpIfmnIjku73mlbDmja4KICAgICAgdmFyIG1vbnRocyA9IFsnMeaciCcsICcy5pyIJywgJzPmnIgnLCAnNOaciCddOwoKICAgICAgLy8g6K6h566X5pWw5o2u6IyD5Zu077yM5Lul5L6/6K6+572ueei9tOiMg+WbtOS9v+aKmOe6v+WxheS4rQogICAgICB2YXIgdmFsdWVzID0gaW5kaWNhdG9yLnZhbHVlczsKICAgICAgdmFyIG1pbiA9IE1hdGgubWluLmFwcGx5KE1hdGgsICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKHZhbHVlcykpOwogICAgICB2YXIgbWF4ID0gTWF0aC5tYXguYXBwbHkoTWF0aCwgKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkodmFsdWVzKSk7CiAgICAgIHZhciByYW5nZSA9IG1heCAtIG1pbjsKCiAgICAgIC8vIOiuvue9rnnovbTojIPlm7TvvIzmianlpKcyMCXnmoTojIPlm7Tkvb/ms6LliqjnnIvotbfmnaXmm7TmmI7mmL4KICAgICAgdmFyIHlNaW4gPSBtaW4gLSByYW5nZSAqIDAuNDsKICAgICAgdmFyIHlNYXggPSBtYXggKyByYW5nZSAqIDAuNDsKICAgICAgdmFyIG9wdGlvbiA9IHsKICAgICAgICB0aXRsZTogewogICAgICAgICAgdGV4dDogIiIuY29uY2F0KHRoaXMuY3VycmVudERlcGFydG1lbnQsICIgLSAiKS5jb25jYXQodGhpcy5jdXJyZW50SW5kaWNhdG9yKSwKICAgICAgICAgIGxlZnQ6ICdjZW50ZXInLAogICAgICAgICAgdGV4dFN0eWxlOiB7CiAgICAgICAgICAgIGZvbnRTaXplOiAxNgogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgdG9vbHRpcDogewogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLAogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiBmb3JtYXR0ZXIocGFyYW1zKSB7CiAgICAgICAgICAgIHJldHVybiAiIi5jb25jYXQocGFyYW1zWzBdLm5hbWUsICI8YnIvPiIpLmNvbmNhdChpbmRpY2F0b3IubmFtZSwgIjogIikuY29uY2F0KHBhcmFtc1swXS52YWx1ZSwgIiAiKS5jb25jYXQoaW5kaWNhdG9yLnVuaXQsICI8YnIvPlx1NzZFRVx1NjgwN1x1NTAzQzogIikuY29uY2F0KGluZGljYXRvci50YXJnZXQsICIgIikuY29uY2F0KGluZGljYXRvci51bml0KTsKICAgICAgICAgIH0sCiAgICAgICAgICBheGlzUG9pbnRlcjogewogICAgICAgICAgICB0eXBlOiAnY3Jvc3MnLAogICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyM2YTc5ODUnCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGdyaWQ6IHsKICAgICAgICAgIGxlZnQ6ICc1JScsCiAgICAgICAgICByaWdodDogJzUlJywKICAgICAgICAgIGJvdHRvbTogJzEwJScsCiAgICAgICAgICB0b3A6ICc2MHB4JywKICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgeEF4aXM6IHsKICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsCiAgICAgICAgICBib3VuZGFyeUdhcDogZmFsc2UsCiAgICAgICAgICBkYXRhOiBtb250aHMsCiAgICAgICAgICBheGlzTGluZTogewogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICB3aWR0aDogMgogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICB5QXhpczogewogICAgICAgICAgdHlwZTogJ3ZhbHVlJywKICAgICAgICAgIG5hbWU6IGluZGljYXRvci51bml0LAogICAgICAgICAgbWluOiB5TWluLAogICAgICAgICAgbWF4OiB5TWF4LAogICAgICAgICAgYXhpc0xhYmVsOiB7CiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gZm9ybWF0dGVyKHZhbHVlKSB7CiAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlLnRvRml4ZWQoMik7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBzcGxpdExpbmU6IHsKICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgdHlwZTogJ2Rhc2hlZCcKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIGF4aXNMaW5lOiB7CiAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgIHdpZHRoOiAyCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHNlcmllczogW3sKICAgICAgICAgIG5hbWU6IGluZGljYXRvci5uYW1lLAogICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgZGF0YTogaW5kaWNhdG9yLnZhbHVlcywKICAgICAgICAgIHNtb290aDogdHJ1ZSwKICAgICAgICAgIC8vIOW5s+a7keabsue6vwogICAgICAgICAgc3ltYm9sOiAnZW1wdHlDaXJjbGUnLAogICAgICAgICAgc3ltYm9sU2l6ZTogMTAsCiAgICAgICAgICBlbXBoYXNpczogewogICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAxMCwKICAgICAgICAgICAgICBzaGFkb3dDb2xvcjogJ3JnYmEoNjQsIDE1OCwgMjU1LCAwLjUpJwogICAgICAgICAgICB9LAogICAgICAgICAgICBzY2FsZTogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICBjb2xvcjogJyM0MDlFRkYnLAogICAgICAgICAgICBib3JkZXJXaWR0aDogMgogICAgICAgICAgfSwKICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICB3aWR0aDogNCwKICAgICAgICAgICAgc2hhZG93Q29sb3I6ICdyZ2JhKDAsIDAsIDAsIDAuMyknLAogICAgICAgICAgICBzaGFkb3dCbHVyOiAxMCwKICAgICAgICAgICAgc2hhZG93T2Zmc2V0WTogNQogICAgICAgICAgfSwKICAgICAgICAgIGFyZWFTdHlsZTogewogICAgICAgICAgICBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbewogICAgICAgICAgICAgIG9mZnNldDogMCwKICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoNjQsIDE1OCwgMjU1LCAwLjcpJwogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgb2Zmc2V0OiAwLjUsCiAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDY0LCAxNTgsIDI1NSwgMC4zKScKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIG9mZnNldDogMSwKICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoNjQsIDE1OCwgMjU1LCAwLjEpJwogICAgICAgICAgICB9XSkKICAgICAgICAgIH0sCiAgICAgICAgICBtYXJrTGluZTogewogICAgICAgICAgICBzaWxlbnQ6IHRydWUsCiAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnI0Y1NkM2QycsCiAgICAgICAgICAgICAgdHlwZTogJ2Rhc2hlZCcsCiAgICAgICAgICAgICAgd2lkdGg6IDIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YTogW3sKICAgICAgICAgICAgICB5QXhpczogaW5kaWNhdG9yLnRhcmdldCwKICAgICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgICAgZm9ybWF0dGVyOiAiXHU3NkVFXHU2ODA3XHU1MDNDOiAiLmNvbmNhdChpbmRpY2F0b3IudGFyZ2V0KSwKICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAnaW5zaWRlRW5kVG9wJywKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMiwKICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMjQ1LCAxMDgsIDEwOCwgMC4yKScsCiAgICAgICAgICAgICAgICBwYWRkaW5nOiBbMiwgNF0sCiAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDIKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH1dCiAgICAgICAgICB9LAogICAgICAgICAgYW5pbWF0aW9uRHVyYXRpb246IDIwMDAsCiAgICAgICAgICBhbmltYXRpb25FYXNpbmc6ICdlbGFzdGljT3V0JywKICAgICAgICAgIGFuaW1hdGlvbkRlbGF5OiBmdW5jdGlvbiBhbmltYXRpb25EZWxheShpZHgpIHsKICAgICAgICAgICAgcmV0dXJuIGlkeCAqIDIwMDsKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAn55uu5qCH5YC8JywKICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgIGRhdGE6IEFycmF5KG1vbnRocy5sZW5ndGgpLmZpbGwoaW5kaWNhdG9yLnRhcmdldCksCiAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6ICcjRjU2QzZDJywKICAgICAgICAgICAgdHlwZTogJ2Rhc2hlZCcsCiAgICAgICAgICAgIHdpZHRoOiAyCiAgICAgICAgICB9LAogICAgICAgICAgc3ltYm9sOiAnbm9uZScKICAgICAgICB9XSwKICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgIGRhdGE6IFtpbmRpY2F0b3IubmFtZSwgJ+ebruagh+WAvCddLAogICAgICAgICAgYm90dG9tOiAnMCUnCiAgICAgICAgfQogICAgICB9OwogICAgICBjaGFydC5zZXRPcHRpb24ob3B0aW9uKTsKICAgIH0sCiAgICAvLyDliJ3lp4vljJbog73mupDmtojogJflrZDlm77ooagKICAgIGluaXRFbmVyZ3lEZXRhaWxDaGFydHM6IGZ1bmN0aW9uIGluaXRFbmVyZ3lEZXRhaWxDaGFydHMoKSB7CiAgICAgIHZhciBfdGhpczE0ID0gdGhpczsKICAgICAgLy8g5qOA5p+l5b2T5YmN6YCJ5oup55qE6YOo6Zeo5pWw5o2u5piv5ZCm5pyJ5pWICiAgICAgIGlmICghdGhpcy5jdXJyZW50RW5lcmd5RGVwdCB8fCAhdGhpcy5mYWN0b3J5RW5lcmd5RGF0YSkgcmV0dXJuOwoKICAgICAgLy8g6YeN572u6K2m5ZGK54q25oCBCiAgICAgIGZvciAodmFyIGtleSBpbiB0aGlzLmlzRW5lcmd5Q2hhcnRXYXJuaW5nKSB7CiAgICAgICAgdGhpcy5pc0VuZXJneUNoYXJ0V2FybmluZ1trZXldID0gZmFsc2U7CiAgICAgIH0KCiAgICAgIC8vIOS4uuavj+enjeiDvea6kOexu+Wei+WIm+W7uuWNleeLrOeahOWbvuihqAogICAgICB0aGlzLmVuZXJneVR5cGVzLmZvckVhY2goZnVuY3Rpb24gKHR5cGUpIHsKICAgICAgICB2YXIgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnZW5lcmd5U3ViY2hhcnRfJyArIHR5cGUudmFsdWUpOwogICAgICAgIGlmICghY2hhcnREb20pIHJldHVybjsKCiAgICAgICAgLy8g5riF6Zmk5LmL5YmN55qE5a6e5L6LCiAgICAgICAgdmFyIGV4aXN0aW5nQ2hhcnQgPSBlY2hhcnRzLmdldEluc3RhbmNlQnlEb20oY2hhcnREb20pOwogICAgICAgIGlmIChleGlzdGluZ0NoYXJ0KSB7CiAgICAgICAgICBleGlzdGluZ0NoYXJ0LmRpc3Bvc2UoKTsKICAgICAgICB9CiAgICAgICAgdmFyIGNoYXJ0ID0gZWNoYXJ0cy5pbml0KGNoYXJ0RG9tKTsKICAgICAgICB2YXIgZGF0YSA9IF90aGlzMTQuZmFjdG9yeUVuZXJneURhdGFbdHlwZS52YWx1ZV1bX3RoaXMxNC5jdXJyZW50RW5lcmd5RGVwdF0gfHwgW107CiAgICAgICAgdmFyIG1vbnRocyA9IF90aGlzMTQuZmFjdG9yeUVuZXJneURhdGEubW9udGhzOwogICAgICAgIHZhciB0YXJnZXRSYW5nZSA9IF90aGlzMTQuZW5lcmd5VGFyZ2V0UmFuZ2VzW3R5cGUudmFsdWVdOwoKICAgICAgICAvLyDorqHnrpfmlbDmja7nmoTmnIDlsI/lgLzlkozmnIDlpKflgLzvvIzku6Xkvr/orr7nva556L2055qE6IyD5Zu0CiAgICAgICAgdmFyIG1pblZhbHVlID0gTWF0aC5taW4uYXBwbHkoTWF0aCwgKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkoZGF0YSkpOwogICAgICAgIHZhciBtYXhWYWx1ZSA9IE1hdGgubWF4LmFwcGx5KE1hdGgsICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKGRhdGEpKTsKICAgICAgICB2YXIgdmFsdWVSYW5nZSA9IG1heFZhbHVlIC0gbWluVmFsdWU7CgogICAgICAgIC8vIOiuvue9rnnovbTnmoTmnIDlsI/lgLzlkozmnIDlpKflgLzvvIzku6Xkvb/mipjnur/lsYXkuK3lubblop7liqDms6LliqjmhJ8KICAgICAgICAvLyDpgJrov4fnvKnlsI956L206IyD5Zu05L2/5oqY57q/5pi+5b6X5pu05Yqg5puy5oqYCiAgICAgICAgdmFyIHlNaW4gPSBtaW5WYWx1ZSAtIHZhbHVlUmFuZ2UgKiAwLjM7IC8vIOS4i+aWuemihOeVmTMwJeeahOepuumXtAogICAgICAgIHZhciB5TWF4ID0gbWF4VmFsdWUgKyB2YWx1ZVJhbmdlICogMC4zOyAvLyDkuIrmlrnpooTnlZkzMCXnmoTnqbrpl7QKCiAgICAgICAgdmFyIG9wdGlvbiA9IHsKICAgICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLAogICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIGZvcm1hdHRlcihwYXJhbXMpIHsKICAgICAgICAgICAgICB2YXIgdmFsdWUgPSBwYXJhbXNbMF0udmFsdWU7CiAgICAgICAgICAgICAgcmV0dXJuICIiLmNvbmNhdChwYXJhbXNbMF0ubmFtZSwgIjxici8+IikuY29uY2F0KHR5cGUubGFiZWwsICI6ICIpLmNvbmNhdCh2YWx1ZSwgIiAiKS5jb25jYXQodGFyZ2V0UmFuZ2UudW5pdCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBncmlkOiB7CiAgICAgICAgICAgIGxlZnQ6ICcxMCUnLAogICAgICAgICAgICByaWdodDogJzUlJywKICAgICAgICAgICAgYm90dG9tOiAnMTUlJywKICAgICAgICAgICAgdG9wOiAnMTUlJywKICAgICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgeEF4aXM6IHsKICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywKICAgICAgICAgICAgZGF0YTogbW9udGhzLAogICAgICAgICAgICBheGlzTGluZTogewogICAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgICAgY29sb3I6ICcjMzMzMzMzJyAvLyDpu5HoibLlnZDmoIfovbQKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICAgIGNvbG9yOiAnIzMzMzMzMycgLy8g6buR6Imy5Z2Q5qCH6L205paH5a2XCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGJvdW5kYXJ5R2FwOiBmYWxzZSAvLyDorqnmm7Lnur/ku47lnZDmoIfovbTlvIDlp4sKICAgICAgICAgIH0sCiAgICAgICAgICB5QXhpczogewogICAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgICBuYW1lOiB0YXJnZXRSYW5nZS51bml0LAogICAgICAgICAgICBtaW46IHlNaW4sCiAgICAgICAgICAgIC8vIOiuvue9ruacgOWwj+WAvOS9v+aKmOe6v+WxheS4rQogICAgICAgICAgICBtYXg6IHlNYXgsCiAgICAgICAgICAgIC8vIOiuvue9ruacgOWkp+WAvOS9v+aKmOe6v+WxheS4rQogICAgICAgICAgICBheGlzTGluZTogewogICAgICAgICAgICAgIGxpbmVTdHlsZTogewogICAgICAgICAgICAgICAgY29sb3I6ICcjMzMzMzMzJyAvLyDpu5HoibLlnZDmoIfovbQKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICAgIGNvbG9yOiAnIzMzMzMzMycsCiAgICAgICAgICAgICAgLy8g6buR6Imy5Z2Q5qCH6L205paH5a2XCiAgICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiBmb3JtYXR0ZXIodmFsdWUpIHsKICAgICAgICAgICAgICAgIC8vIOS/neeVmemAguW9k+eahOWwj+aVsOS9jeaVsOS7pemBv+WFjei/h+W6puaLpeaMpAogICAgICAgICAgICAgICAgaWYgKHZhbHVlID49IDEwMCkgewogICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUudG9GaXhlZCgwKTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZS50b0ZpeGVkKDEpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgc3BsaXRMaW5lOiB7CiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgICB0eXBlOiAnZGFzaGVkJywKICAgICAgICAgICAgICAgIGNvbG9yOiAnI0UwRTZGMScKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBzZXJpZXM6IFt7CiAgICAgICAgICAgIG5hbWU6IHR5cGUubGFiZWwsCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgICAgZGF0YTogZGF0YSwKICAgICAgICAgICAgc21vb3RoOiB0cnVlLAogICAgICAgICAgICAvLyDkvb/nlKjlubPmu5Hmm7Lnur8KICAgICAgICAgICAgc21vb3RoTW9ub3RvbmU6ICdub25lJywKICAgICAgICAgICAgLy8g5LiN5L+d5oyB5Y2V6LCD5oCn77yM5YWB6K645pu05aSa5rOi5YqoCiAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsCiAgICAgICAgICAgIHN5bWJvbFNpemU6IDgsCiAgICAgICAgICAgIHNhbXBsaW5nOiAnYXZlcmFnZScsCiAgICAgICAgICAgIC8vIOS9v+eUqOW5s+Wdh+mHh+agtwogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICB3aWR0aDogNCwKICAgICAgICAgICAgICAvLyDliqDnspfnur/mnaEKICAgICAgICAgICAgICBjb2xvcjogdHlwZS5jb2xvciAvLyDkvb/nlKjmm7Tmt7HnmoTpopzoibIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6IHR5cGUuY29sb3IsCiAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDIsCiAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjZmZmJywKICAgICAgICAgICAgICBzaGFkb3dDb2xvcjogJ3JnYmEoMCwgMCwgMCwgMC4zKScsCiAgICAgICAgICAgICAgc2hhZG93Qmx1cjogNQogICAgICAgICAgICB9LAogICAgICAgICAgICBlbXBoYXNpczogewogICAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDMsCiAgICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAxMAogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgICB3aWR0aDogNiAvLyDpvKDmoIfmgqzlgZzml7bnur/mnaHmm7TnspcKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGFyZWFTdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFt7CiAgICAgICAgICAgICAgICBvZmZzZXQ6IDAsCiAgICAgICAgICAgICAgICBjb2xvcjogX3RoaXMxNC5oZXhUb1JnYmEodHlwZS5jb2xvciwgMC42KSAvLyDmm7Tpq5jnmoTpgI/mmI7luqbkvb/popzoibLmm7Tmt7EKICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICBvZmZzZXQ6IDEsCiAgICAgICAgICAgICAgICBjb2xvcjogX3RoaXMxNC5oZXhUb1JnYmEodHlwZS5jb2xvciwgMC4xKQogICAgICAgICAgICAgIH1dKQogICAgICAgICAgICB9CiAgICAgICAgICB9XQogICAgICAgIH07CiAgICAgICAgY2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOminOiJsui9rOaNoui+heWKqeWHveaVsAogICAgaGV4VG9SZ2JhOiBmdW5jdGlvbiBoZXhUb1JnYmEoaGV4LCBhbHBoYSkgewogICAgICB2YXIgciA9IHBhcnNlSW50KGhleC5zbGljZSgxLCAzKSwgMTYpOwogICAgICB2YXIgZyA9IHBhcnNlSW50KGhleC5zbGljZSgzLCA1KSwgMTYpOwogICAgICB2YXIgYiA9IHBhcnNlSW50KGhleC5zbGljZSg1LCA3KSwgMTYpOwogICAgICByZXR1cm4gInJnYmEoIi5jb25jYXQociwgIiwgIikuY29uY2F0KGcsICIsICIpLmNvbmNhdChiLCAiLCAiKS5jb25jYXQoYWxwaGEsICIpIik7CiAgICB9LAogICAgLy8g5ZCv5Yqo6IO95rqQ6YOo6Zeo6Ieq5Yqo5YiH5o2iCiAgICBzdGFydEVuZXJneURlcHRTd2l0Y2g6IGZ1bmN0aW9uIHN0YXJ0RW5lcmd5RGVwdFN3aXRjaCgpIHsKICAgICAgdmFyIF90aGlzMTUgPSB0aGlzOwogICAgICAvLyDlgZzmraLkuYvliY3nmoTog73mupDpg6jpl6jliIfmjaIKICAgICAgaWYgKHRoaXMuZW5lcmd5RGVwdFRpbWVyKSB7CiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmVuZXJneURlcHRUaW1lcik7CiAgICAgIH0KCiAgICAgIC8vIOWQr+WKqOaWsOeahOiDvea6kOmDqOmXqOWIh+aNogogICAgICB0aGlzLmVuZXJneURlcHRUaW1lciA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczE1LnN3aXRjaEVuZXJneURlcHQoKTsKICAgICAgfSwgMTAwMDApOyAvLyDmr48xMOenkuWIh+aNouS4gOasoemDqOmXqO+8jOWOn+adpeaYrzUwMDAKICAgIH0sCiAgICAvLyDliIfmjaLog73mupDpg6jpl6gKICAgIHN3aXRjaEVuZXJneURlcHQ6IGZ1bmN0aW9uIHN3aXRjaEVuZXJneURlcHQoKSB7CiAgICAgIHZhciBjdXJyZW50SW5kZXggPSB0aGlzLmFsbEZhY3Rvcmllcy5pbmRleE9mKHRoaXMuY3VycmVudEVuZXJneURlcHQpOwogICAgICB2YXIgbmV4dEluZGV4ID0gKGN1cnJlbnRJbmRleCArIDEpICUgdGhpcy5hbGxGYWN0b3JpZXMubGVuZ3RoOwogICAgICB0aGlzLmN1cnJlbnRFbmVyZ3lEZXB0ID0gdGhpcy5hbGxGYWN0b3JpZXNbbmV4dEluZGV4XTsKICAgICAgdGhpcy5pbml0RW5lcmd5RGV0YWlsQ2hhcnRzKCk7CiAgICB9LAogICAgLy8g56qX5Y+j5aSn5bCP5Y+Y5YyW5pe26YeN5paw5riy5p+T5Zu+6KGoCiAgICByZXNpemVDaGFydHM6IGZ1bmN0aW9uIHJlc2l6ZUNoYXJ0cygpIHsKICAgICAgdmFyIGNoYXJ0SWRzID0gWydkZXBhcnRtZW50Q29tcGxldGlvbkNoYXJ0JywgJ21vbnRobHlUcmVuZENoYXJ0J107CiAgICAgIGNoYXJ0SWRzLmZvckVhY2goZnVuY3Rpb24gKGlkKSB7CiAgICAgICAgdmFyIGNoYXJ0RG9tID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoaWQpOwogICAgICAgIGlmIChjaGFydERvbSkgewogICAgICAgICAgdmFyIGNoYXJ0ID0gZWNoYXJ0cy5nZXRJbnN0YW5jZUJ5RG9tKGNoYXJ0RG9tKTsKICAgICAgICAgIGlmIChjaGFydCkgewogICAgICAgICAgICBjaGFydC5yZXNpemUoKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgLy8g6LCD5pW06IO95rqQ5a2Q5Zu+6KGo5aSn5bCPCiAgICAgIHRoaXMuZW5lcmd5VHlwZXMuZm9yRWFjaChmdW5jdGlvbiAodHlwZSkgewogICAgICAgIHZhciBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdlbmVyZ3lTdWJjaGFydF8nICsgdHlwZS52YWx1ZSk7CiAgICAgICAgaWYgKGNoYXJ0RG9tKSB7CiAgICAgICAgICB2YXIgY2hhcnQgPSBlY2hhcnRzLmdldEluc3RhbmNlQnlEb20oY2hhcnREb20pOwogICAgICAgICAgaWYgKGNoYXJ0KSB7CiAgICAgICAgICAgIGNoYXJ0LnJlc2l6ZSgpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g6ZSA5q+B5Zu+6KGo5a6e5L6LCiAgICBkaXNwb3NlQ2hhcnRzOiBmdW5jdGlvbiBkaXNwb3NlQ2hhcnRzKCkgewogICAgICB2YXIgX3RoaXMxNiA9IHRoaXM7CiAgICAgIHZhciBjaGFydElkcyA9IFsnZGVwYXJ0bWVudENvbXBsZXRpb25DaGFydCcsICdtb250aGx5VHJlbmRDaGFydCddOwogICAgICBjaGFydElkcy5mb3JFYWNoKGZ1bmN0aW9uIChpZCkgewogICAgICAgIHZhciBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGlkKTsKICAgICAgICBpZiAoY2hhcnREb20pIHsKICAgICAgICAgIC8vIOenu+mZpOS6i+S7tuebkeWQrOWZqAogICAgICAgICAgaWYgKGlkID09PSAnZGVwYXJ0bWVudENvbXBsZXRpb25DaGFydCcgJiYgX3RoaXMxNi5jaGFydE1vdXNlT3ZlckhhbmRsZXIgJiYgX3RoaXMxNi5jaGFydE1vdXNlT3V0SGFuZGxlcikgewogICAgICAgICAgICBjaGFydERvbS5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZW92ZXInLCBfdGhpczE2LmNoYXJ0TW91c2VPdmVySGFuZGxlcik7CiAgICAgICAgICAgIGNoYXJ0RG9tLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlb3V0JywgX3RoaXMxNi5jaGFydE1vdXNlT3V0SGFuZGxlcik7CiAgICAgICAgICB9CiAgICAgICAgICB2YXIgY2hhcnQgPSBlY2hhcnRzLmdldEluc3RhbmNlQnlEb20oY2hhcnREb20pOwogICAgICAgICAgaWYgKGNoYXJ0KSB7CiAgICAgICAgICAgIGNoYXJ0LmRpc3Bvc2UoKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgLy8g5riF6Zmk5a6M5oiQ546H5Zu+6KGo5byV55SoCiAgICAgIHRoaXMuY29tcGxldGlvbkNoYXJ0ID0gbnVsbDsKCiAgICAgIC8vIOmUgOavgeiDvea6kOWtkOWbvuihqAogICAgICB0aGlzLmVuZXJneVR5cGVzLmZvckVhY2goZnVuY3Rpb24gKHR5cGUpIHsKICAgICAgICB2YXIgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnZW5lcmd5U3ViY2hhcnRfJyArIHR5cGUudmFsdWUpOwogICAgICAgIGlmIChjaGFydERvbSkgewogICAgICAgICAgdmFyIGNoYXJ0ID0gZWNoYXJ0cy5nZXRJbnN0YW5jZUJ5RG9tKGNoYXJ0RG9tKTsKICAgICAgICAgIGlmIChjaGFydCkgewogICAgICAgICAgICBjaGFydC5kaXNwb3NlKCk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDlkK/liqjkuovkuJrpg6jliIfmjaIKICAgIHN0YXJ0QnVzaW5lc3NVbml0U3dpdGNoOiBmdW5jdGlvbiBzdGFydEJ1c2luZXNzVW5pdFN3aXRjaCgpIHsKICAgICAgdmFyIF90aGlzMTcgPSB0aGlzOwogICAgICAvLyDlgZzmraLkuYvliY3nmoTkuovkuJrpg6jliIfmjaIKICAgICAgaWYgKHRoaXMuYnVzaW5lc3NVbml0VGltZXIpIHsKICAgICAgICBjbGVhckludGVydmFsKHRoaXMuYnVzaW5lc3NVbml0VGltZXIpOwogICAgICB9CgogICAgICAvLyDlkK/liqjmlrDnmoTkuovkuJrpg6jliIfmjaIKICAgICAgdGhpcy5idXNpbmVzc1VuaXRUaW1lciA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczE3LnN3aXRjaEJ1c2luZXNzVW5pdCgpOwogICAgICB9LCAzMDAwMCk7IC8vIOS6i+S4mumDqOWIh+aNoumikeeOh+S4ujMw56eSCiAgICB9LAogICAgLy8g5YiH5o2i5LqL5Lia6YOoCiAgICBzd2l0Y2hCdXNpbmVzc1VuaXQ6IGZ1bmN0aW9uIHN3aXRjaEJ1c2luZXNzVW5pdCgpIHsKICAgICAgdmFyIGN1cnJlbnRJbmRleCA9IHRoaXMuZGVwYXJ0bWVudHMuaW5kZXhPZih0aGlzLmN1cnJlbnRCdXNpbmVzc1VuaXQpOwogICAgICB2YXIgbmV4dEluZGV4ID0gKGN1cnJlbnRJbmRleCArIDEpICUgdGhpcy5kZXBhcnRtZW50cy5sZW5ndGg7CiAgICAgIHRoaXMuY3VycmVudEJ1c2luZXNzVW5pdCA9IHRoaXMuZGVwYXJ0bWVudHNbbmV4dEluZGV4XTsKICAgIH0sCiAgICAvLyDliJ3lp4vljJblhbPplK7og73mupDmjIfmoIflr7nmr5Tlm74KICAgIGluaXRLZXlFbmVyZ3lJbmRpY2F0b3JzQ2hhcnQ6IGZ1bmN0aW9uIGluaXRLZXlFbmVyZ3lJbmRpY2F0b3JzQ2hhcnQoKSB7CiAgICAgIHZhciBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdrZXlFbmVyZ3lJbmRpY2F0b3JzQ2hhcnQnKTsKICAgICAgdmFyIGNoYXJ0ID0gZWNoYXJ0cy5pbml0KGNoYXJ0RG9tKTsKICAgICAgdmFyIG9wdGlvbiA9IHsKICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsCiAgICAgICAgICBheGlzUG9pbnRlcjogewogICAgICAgICAgICB0eXBlOiAnc2hhZG93JwogICAgICAgICAgfSwKICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gKHBhcmFtcykgewogICAgICAgICAgICB2YXIgZGF0YUluZGV4ID0gcGFyYW1zWzBdLmRhdGFJbmRleDsKICAgICAgICAgICAgdmFyIGluZGljYXRvciA9IHRoaXMua2V5RW5lcmd5SW5kaWNhdG9yc1tkYXRhSW5kZXhdOwogICAgICAgICAgICB2YXIgY2hhbmdlVGV4dCA9IGluZGljYXRvci5jaGFuZ2UgPiAwID8gIisiLmNvbmNhdChpbmRpY2F0b3IuY2hhbmdlLCAiJSIpIDogIiIuY29uY2F0KGluZGljYXRvci5jaGFuZ2UsICIlIik7CiAgICAgICAgICAgIHZhciBjaGFuZ2VDb2xvciA9IGluZGljYXRvci5zdGF0dXMgPT09ICdnb29kJyA/ICcjNjdDMjNBJyA6IGluZGljYXRvci5zdGF0dXMgPT09ICd3YXJuaW5nJyA/ICcjRTZBMjNDJyA6ICcjRjU2QzZDJzsKICAgICAgICAgICAgcmV0dXJuICJcbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT1cImZvbnQtd2VpZ2h0OmJvbGRcIj4iLmNvbmNhdChpbmRpY2F0b3IubmFtZSwgIjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2Plx1NEVDQVx1NjVFNTogIikuY29uY2F0KGluZGljYXRvci50b2RheSwgIiAiKS5jb25jYXQoaW5kaWNhdG9yLnVuaXQsICI8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cdTY2MjhcdTY1RTU6ICIpLmNvbmNhdChpbmRpY2F0b3IueWVzdGVyZGF5LCAiICIpLmNvbmNhdChpbmRpY2F0b3IudW5pdCwgIjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2Plx1NTNEOFx1NTMxNjogPHNwYW4gc3R5bGU9XCJjb2xvcjoiKS5jb25jYXQoY2hhbmdlQ29sb3IsICJcIj4iKS5jb25jYXQoY2hhbmdlVGV4dCwgIjwvc3Bhbj48L2Rpdj5cbiAgICAgICAgICAgICIpOwogICAgICAgICAgfS5iaW5kKHRoaXMpCiAgICAgICAgfSwKICAgICAgICBncmlkOiB7CiAgICAgICAgICBsZWZ0OiAnMyUnLAogICAgICAgICAgcmlnaHQ6ICc0JScsCiAgICAgICAgICBib3R0b206ICczJScsCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUKICAgICAgICB9LAogICAgICAgIHhBeGlzOiB7CiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLAogICAgICAgICAgZGF0YTogdGhpcy5rZXlFbmVyZ3lJbmRpY2F0b3JzLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICByZXR1cm4gaXRlbS5uYW1lOwogICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIHlBeGlzOiB7CiAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgYXhpc0xhYmVsOiB7CiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gZm9ybWF0dGVyKHZhbHVlKSB7CiAgICAgICAgICAgICAgaWYgKHZhbHVlID49IDEwMCkgewogICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlLnRvRml4ZWQoMCk7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZS50b0ZpeGVkKDEpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbewogICAgICAgICAgbmFtZTogJ+S7iuaXpeWAvCcsCiAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgIGRhdGE6IHRoaXMua2V5RW5lcmd5SW5kaWNhdG9ycy5tYXAoZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0udG9kYXksCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgICBjb2xvcjogaXRlbS5zdGF0dXMgPT09ICdnb29kJyA/ICcjNjdDMjNBJyA6IGl0ZW0uc3RhdHVzID09PSAnd2FybmluZycgPyAnI0U2QTIzQycgOiAnI0Y1NkM2QycKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH07CiAgICAgICAgICB9KSwKICAgICAgICAgIGxhYmVsOiB7CiAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgIHBvc2l0aW9uOiAndG9wJywKICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiAocGFyYW1zKSB7CiAgICAgICAgICAgICAgdmFyIGluZGV4ID0gcGFyYW1zLmRhdGFJbmRleDsKICAgICAgICAgICAgICByZXR1cm4gdGhpcy5rZXlFbmVyZ3lJbmRpY2F0b3JzW2luZGV4XS50b2RheTsKICAgICAgICAgICAgfS5iaW5kKHRoaXMpCiAgICAgICAgICB9LAogICAgICAgICAgYmFyV2lkdGg6ICczMCUnCiAgICAgICAgfSwgewogICAgICAgICAgbmFtZTogJ+aYqOaXpeWAvCcsCiAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgIGRhdGE6IHRoaXMua2V5RW5lcmd5SW5kaWNhdG9ycy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgcmV0dXJuIGl0ZW0ueWVzdGVyZGF5OwogICAgICAgICAgfSksCiAgICAgICAgICBiYXJXaWR0aDogJzMwJScsCiAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6ICcjOTA5Mzk5JywKICAgICAgICAgICAgb3BhY2l0eTogMC41CiAgICAgICAgICB9CiAgICAgICAgfV0sCiAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICBkYXRhOiBbJ+S7iuaXpeWAvCcsICfmmKjml6XlgLwnXSwKICAgICAgICAgIGJvdHRvbTogMAogICAgICAgIH0KICAgICAgfTsKICAgICAgY2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7CiAgICB9LAogICAgLy8g5qC55o2u57Si5byV5L2N572u6K6h566X5riQ5Y+Y6aKc6Imy5qC35byPCiAgICBnZXRHcmFkaWVudFN0eWxlOiBmdW5jdGlvbiBnZXRHcmFkaWVudFN0eWxlKGluZGV4LCB0b3RhbCkgewogICAgICAvLyDorqHnrpfor6XmjIfmoIflnKjmlbTkvZPkuK3nmoTnm7jlr7nkvY3nva7vvIgwLTHkuYvpl7TvvIkKICAgICAgdmFyIHBvc2l0aW9uID0gaW5kZXggLyAodG90YWwgLSAxKTsKCiAgICAgIC8vIOWumuS5iee7v+iJsuWIsOe6ouiJsueahOa4kOWPmOminOiJsuaVsOe7hAogICAgICB2YXIgY29sb3JTdG9wcyA9IFsnIzUyYzQxYScsCiAgICAgIC8vIOS6rue7v+iJsgogICAgICAnIzg1Y2U2MScsCiAgICAgIC8vIOe7v+iJsgogICAgICAnI2IzZTE5ZCcsCiAgICAgIC8vIOa1hee7v+iJsgogICAgICAnI2Q0ZjhiZScsCiAgICAgIC8vIOmdnuW4uOa1heeahOe7v+iJsgogICAgICAnI2ZhZmY3MicsCiAgICAgIC8vIOm7hOiJsgogICAgICAnI2ZhZGIxNCcsCiAgICAgIC8vIOmHkem7hOiJsgogICAgICAnI2ZmYTk0MCcsCiAgICAgIC8vIOapmeiJsgogICAgICAnI2ZhOGMxNicsCiAgICAgIC8vIOa3seapmeiJsgogICAgICAnI2ZmNzg3NScsCiAgICAgIC8vIOa1hee6ouiJsgogICAgICAnI2ZmNGQ0ZicsCiAgICAgIC8vIOe6ouiJsgogICAgICAnI2Y1MjIyZCcsCiAgICAgIC8vIOS6rue6ouiJsgogICAgICAnI2NmMTMyMicgLy8g5rex57qi6ImyCiAgICAgIF07CgogICAgICAvLyDmoLnmja7kvY3nva7lnKjmuJDlj5joibLkuYvpl7Tmj5LlgLzojrflj5bpopzoibIKICAgICAgLy8g5om+5Yiw5a+55bqU55qE6aKc6Imy5Yy66Ze0CiAgICAgIHZhciBzZWdtZW50Q291bnQgPSBjb2xvclN0b3BzLmxlbmd0aCAtIDE7CiAgICAgIHZhciBzZWdtZW50ID0gTWF0aC5taW4oTWF0aC5mbG9vcihwb3NpdGlvbiAqIHNlZ21lbnRDb3VudCksIHNlZ21lbnRDb3VudCAtIDEpOwogICAgICB2YXIgbG9jYWxQb3NpdGlvbiA9IHBvc2l0aW9uICogc2VnbWVudENvdW50IC0gc2VnbWVudDsgLy8g5Zyo5b2T5YmN5Yy66Ze05YaF55qE55u45a+55L2N572uICgwLTEpCgogICAgICAvLyDojrflj5bljLrpl7TnmoTotbfmraLpopzoibIKICAgICAgdmFyIHN0YXJ0Q29sb3IgPSBjb2xvclN0b3BzW3NlZ21lbnRdOwogICAgICB2YXIgZW5kQ29sb3IgPSBjb2xvclN0b3BzW3NlZ21lbnQgKyAxXTsKCiAgICAgIC8vIOWcqOS4pOS4quminOiJsuS5i+mXtOaPkuWAvAogICAgICB2YXIgYmdDb2xvciA9IHRoaXMuaW50ZXJwb2xhdGVDb2xvcnMoc3RhcnRDb2xvciwgZW5kQ29sb3IsIGxvY2FsUG9zaXRpb24pOwoKICAgICAgLy8g6K6h566X5paH5a2X6aKc6ImyCiAgICAgIC8vIOaPkOWPllJHQuW5tuiuoeeul+S6ruW6pgogICAgICB2YXIgciA9IHBhcnNlSW50KGJnQ29sb3Iuc2xpY2UoMSwgMyksIDE2KTsKICAgICAgdmFyIGcgPSBwYXJzZUludChiZ0NvbG9yLnNsaWNlKDMsIDUpLCAxNik7CiAgICAgIHZhciBiID0gcGFyc2VJbnQoYmdDb2xvci5zbGljZSg1LCA3KSwgMTYpOwogICAgICB2YXIgYnJpZ2h0bmVzcyA9IChyICogMjk5ICsgZyAqIDU4NyArIGIgKiAxMTQpIC8gMTAwMDsKCiAgICAgIC8vIOS6ruW6puWkp+S6jjE0MOS9v+eUqOa3seiJsuaWh+Wtl++8jOWQpuWImeS9v+eUqOa1heiJsuaWh+WtlwogICAgICB2YXIgdGV4dENvbG9yID0gYnJpZ2h0bmVzcyA+IDE0MCA/ICcjMzMzMzMzJyA6ICcjZmZmZmZmJzsKCiAgICAgIC8vIOiuvue9rui+ueahhuminOiJsu+8iOavlOiDjOaZr+iJsueojea3se+8iQogICAgICB2YXIgYm9yZGVyQ29sb3IgPSB0aGlzLmFkanVzdENvbG9yKGJnQ29sb3IsIC0yMCk7CiAgICAgIHJldHVybiB7CiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBiZ0NvbG9yLAogICAgICAgIGNvbG9yOiB0ZXh0Q29sb3IsCiAgICAgICAgYm9yZGVyVG9wQ29sb3I6IGJvcmRlckNvbG9yCiAgICAgIH07CiAgICB9LAogICAgLy8g6aKc6Imy5o+S5YC85Ye95pWwCiAgICBpbnRlcnBvbGF0ZUNvbG9yczogZnVuY3Rpb24gaW50ZXJwb2xhdGVDb2xvcnMoY29sb3IxLCBjb2xvcjIsIGZhY3RvcikgewogICAgICAvLyDop6PmnpDpopzoibIKICAgICAgdmFyIHIxID0gcGFyc2VJbnQoY29sb3IxLnNsaWNlKDEsIDMpLCAxNik7CiAgICAgIHZhciBnMSA9IHBhcnNlSW50KGNvbG9yMS5zbGljZSgzLCA1KSwgMTYpOwogICAgICB2YXIgYjEgPSBwYXJzZUludChjb2xvcjEuc2xpY2UoNSwgNyksIDE2KTsKICAgICAgdmFyIHIyID0gcGFyc2VJbnQoY29sb3IyLnNsaWNlKDEsIDMpLCAxNik7CiAgICAgIHZhciBnMiA9IHBhcnNlSW50KGNvbG9yMi5zbGljZSgzLCA1KSwgMTYpOwogICAgICB2YXIgYjIgPSBwYXJzZUludChjb2xvcjIuc2xpY2UoNSwgNyksIDE2KTsKCiAgICAgIC8vIOe6v+aAp+aPkuWAvAogICAgICB2YXIgciA9IE1hdGgucm91bmQocjEgKyBmYWN0b3IgKiAocjIgLSByMSkpOwogICAgICB2YXIgZyA9IE1hdGgucm91bmQoZzEgKyBmYWN0b3IgKiAoZzIgLSBnMSkpOwogICAgICB2YXIgYiA9IE1hdGgucm91bmQoYjEgKyBmYWN0b3IgKiAoYjIgLSBiMSkpOwoKICAgICAgLy8g6L2s5Zue5Y2B5YWt6L+b5Yi2CiAgICAgIHJldHVybiAiIyIuY29uY2F0KHIudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJykpLmNvbmNhdChnLnRvU3RyaW5nKDE2KS5wYWRTdGFydCgyLCAnMCcpKS5jb25jYXQoYi50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKSk7CiAgICB9LAogICAgLy8g6I635Y+W5b6956ug5qC35byPCiAgICBnZXRCYWRnZVN0eWxlOiBmdW5jdGlvbiBnZXRCYWRnZVN0eWxlKGluZGV4LCB0b3RhbCkgewogICAgICAvLyDojrflj5blvZPliY3ljaHniYfnmoTog4zmma/oibIKICAgICAgdmFyIHBvc2l0aW9uID0gaW5kZXggLyAodG90YWwgLSAxKTsKICAgICAgdmFyIGNvbG9yU3RvcHMgPSBbJyM1MmM0MWEnLAogICAgICAvLyDkuq7nu7/oibIKICAgICAgJyM4NWNlNjEnLAogICAgICAvLyDnu7/oibIKICAgICAgJyNiM2UxOWQnLAogICAgICAvLyDmtYXnu7/oibIKICAgICAgJyNkNGY4YmUnLAogICAgICAvLyDpnZ7luLjmtYXnmoTnu7/oibIKICAgICAgJyNmYWZmNzInLAogICAgICAvLyDpu4ToibIKICAgICAgJyNmYWRiMTQnLAogICAgICAvLyDph5Hpu4ToibIKICAgICAgJyNmZmE5NDAnLAogICAgICAvLyDmqZnoibIKICAgICAgJyNmYThjMTYnLAogICAgICAvLyDmt7HmqZnoibIKICAgICAgJyNmZjc4NzUnLAogICAgICAvLyDmtYXnuqLoibIKICAgICAgJyNmZjRkNGYnLAogICAgICAvLyDnuqLoibIKICAgICAgJyNmNTIyMmQnLAogICAgICAvLyDkuq7nuqLoibIKICAgICAgJyNjZjEzMjInIC8vIOa3see6ouiJsgogICAgICBdOwogICAgICB2YXIgc2VnbWVudENvdW50ID0gY29sb3JTdG9wcy5sZW5ndGggLSAxOwogICAgICB2YXIgc2VnbWVudCA9IE1hdGgubWluKE1hdGguZmxvb3IocG9zaXRpb24gKiBzZWdtZW50Q291bnQpLCBzZWdtZW50Q291bnQgLSAxKTsKICAgICAgdmFyIGxvY2FsUG9zaXRpb24gPSBwb3NpdGlvbiAqIHNlZ21lbnRDb3VudCAtIHNlZ21lbnQ7CiAgICAgIHZhciBzdGFydENvbG9yID0gY29sb3JTdG9wc1tzZWdtZW50XTsKICAgICAgdmFyIGVuZENvbG9yID0gY29sb3JTdG9wc1tzZWdtZW50ICsgMV07CiAgICAgIHZhciBiZ0NvbG9yID0gdGhpcy5pbnRlcnBvbGF0ZUNvbG9ycyhzdGFydENvbG9yLCBlbmRDb2xvciwgbG9jYWxQb3NpdGlvbik7CgogICAgICAvLyDorqHnrpfmloflrZfpopzoibIKICAgICAgdmFyIHIgPSBwYXJzZUludChiZ0NvbG9yLnNsaWNlKDEsIDMpLCAxNik7CiAgICAgIHZhciBnID0gcGFyc2VJbnQoYmdDb2xvci5zbGljZSgzLCA1KSwgMTYpOwogICAgICB2YXIgYiA9IHBhcnNlSW50KGJnQ29sb3Iuc2xpY2UoNSwgNyksIDE2KTsKICAgICAgdmFyIGJyaWdodG5lc3MgPSAociAqIDI5OSArIGcgKiA1ODcgKyBiICogMTE0KSAvIDEwMDA7CgogICAgICAvLyDmoLnmja7og4zmma/kuq7luqborr7nva7lvr3nq6DmoLflvI8KICAgICAgaWYgKGJyaWdodG5lc3MgPiAxNDApIHsKICAgICAgICAvLyDmtYXoibLog4zmma/kvb/nlKjmt7HoibLovrnmoYbnmoTlvr3nq6AKICAgICAgICB2YXIgYmFkZ2VCZ0NvbG9yID0gdGhpcy5hZGp1c3RDb2xvcihiZ0NvbG9yLCAtNDApOwogICAgICAgIHJldHVybiB7CiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGJhZGdlQmdDb2xvciwKICAgICAgICAgIGNvbG9yOiAnI2ZmZmZmZicsCiAgICAgICAgICBib3hTaGFkb3c6ICcwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpJwogICAgICAgIH07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5rex6Imy6IOM5pmv5L2/55So5rWF6Imy5b6956ugCiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC4yNSknLAogICAgICAgICAgY29sb3I6ICcjZmZmZmZmJywKICAgICAgICAgIGJveFNoYWRvdzogJzAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMiknCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKICAgIC8vIOminOiJsuiwg+aVtOi+heWKqeWHveaVsAogICAgYWRqdXN0Q29sb3I6IGZ1bmN0aW9uIGFkanVzdENvbG9yKGNvbG9yLCBhbW91bnQpIHsKICAgICAgLy8g5aaC5p6c5piv55m96Imy54m55q6K5aSE55CGCiAgICAgIGlmIChjb2xvciA9PT0gJyNmZmZmZmYnKSB7CiAgICAgICAgcmV0dXJuICcjZTBlMGUwJzsKICAgICAgfQoKICAgICAgLy8g5bCG6aKc6Imy6L2s5o2i5Li6UkdCCiAgICAgIHZhciByID0gcGFyc2VJbnQoY29sb3Iuc2xpY2UoMSwgMyksIDE2KTsKICAgICAgdmFyIGcgPSBwYXJzZUludChjb2xvci5zbGljZSgzLCA1KSwgMTYpOwogICAgICB2YXIgYiA9IHBhcnNlSW50KGNvbG9yLnNsaWNlKDUsIDcpLCAxNik7CgogICAgICAvLyDosIPmlbTkuq7luqYKICAgICAgciA9IE1hdGgubWF4KDAsIE1hdGgubWluKDI1NSwgciArIGFtb3VudCkpOwogICAgICBnID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMjU1LCBnICsgYW1vdW50KSk7CiAgICAgIGIgPSBNYXRoLm1heCgwLCBNYXRoLm1pbigyNTUsIGIgKyBhbW91bnQpKTsKCiAgICAgIC8vIOi9rOWbnuWNgeWFrei/m+WItgogICAgICByZXR1cm4gIiMiLmNvbmNhdChyLnRvU3RyaW5nKDE2KS5wYWRTdGFydCgyLCAnMCcpKS5jb25jYXQoZy50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKSkuY29uY2F0KGIudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJykpOwogICAgfSwKICAgIC8vIOWIneWni+WMluaMh+agh+WNoeeJh+a7muWKqAogICAgaW5pdEluZGljYXRvckNhcmRzU2Nyb2xsOiBmdW5jdGlvbiBpbml0SW5kaWNhdG9yQ2FyZHNTY3JvbGwoKSB7CiAgICAgIHZhciBfdGhpczE4ID0gdGhpczsKICAgICAgLy8g5riF6Zmk5LmL5YmN55qE5rua5Yqo5a6a5pe25ZmoCiAgICAgIGlmICh0aGlzLmluZGljYXRvckNhcmRzU2Nyb2xsVGltZXIpIHsKICAgICAgICBjbGVhckludGVydmFsKHRoaXMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxUaW1lcik7CiAgICAgIH0KICAgICAgdmFyIGNvbnRhaW5lciA9IHRoaXMuJHJlZnMuaW5kaWNhdG9yQ2FyZHNDb250YWluZXI7CiAgICAgIGlmICghY29udGFpbmVyKSByZXR1cm47CgogICAgICAvLyDorqHnrpfmu5rliqjmiYDpnIDlj4LmlbAKICAgICAgdmFyIHNjcm9sbFRvcCA9IDA7CiAgICAgIHZhciBzY3JvbGxIZWlnaHQgPSBjb250YWluZXIuc2Nyb2xsSGVpZ2h0OwogICAgICB2YXIgY2xpZW50SGVpZ2h0ID0gY29udGFpbmVyLmNsaWVudEhlaWdodDsKICAgICAgdmFyIG1heFNjcm9sbCA9IHNjcm9sbEhlaWdodCAtIGNsaWVudEhlaWdodDsKCiAgICAgIC8vIOWmguaenOWGheWuueS4jei2s+S7pea7muWKqO+8jOebtOaOpei/lOWbngogICAgICBpZiAobWF4U2Nyb2xsIDw9IDApIHJldHVybjsKCiAgICAgIC8vIOa7muWKqOatpemVv+WSjOmAn+W6pgogICAgICB2YXIgc3RlcCA9IDAuNTsgLy8g5rua5Yqo5q2l6ZW/5pu05bCP77yM5L2/5rua5Yqo5pu05bmz5ruRCiAgICAgIHZhciBzY3JvbGxJbnRlcnZhbCA9IDIwOyAvLyDmu5rliqjmm7TmlrDpopHnjofmm7Tpq5jvvIzmm7TmtYHnlYUKCiAgICAgIHRoaXMuaW5kaWNhdG9yQ2FyZHNTY3JvbGxUaW1lciA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICAvLyDpvKDmoIfmgqzlgZzml7bmmoLlgZzmu5rliqgKICAgICAgICBpZiAoX3RoaXMxOC5pbmRpY2F0b3JDYXJkc1Njcm9sbFBhdXNlZCkgcmV0dXJuOwogICAgICAgIHNjcm9sbFRvcCArPSBzdGVwOwoKICAgICAgICAvLyDlvZPmu5rliqjliLDlupXpg6jml7bvvIzlv6vpgJ/lm57liLDpobbpg6jlubbnu6fnu63mu5rliqgKICAgICAgICBpZiAoc2Nyb2xsVG9wID49IG1heFNjcm9sbCkgewogICAgICAgICAgLy8g6YeN572u5rua5Yqo5L2N572u5Yiw6aG26YOoCiAgICAgICAgICBzY3JvbGxUb3AgPSAwOwogICAgICAgICAgY29udGFpbmVyLnNjcm9sbFRvcCA9IDA7CgogICAgICAgICAgLy8g55+t5pqC5pqC5YGc5LiA5LiL77yM6K6p55So5oi36IO955yL5Yiw5Zue5Yiw6aG26YOo55qE6L+H56iLCiAgICAgICAgICBfdGhpczE4LmluZGljYXRvckNhcmRzU2Nyb2xsUGF1c2VkID0gdHJ1ZTsKICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpczE4LmluZGljYXRvckNhcmRzU2Nyb2xsUGF1c2VkID0gZmFsc2U7CiAgICAgICAgICB9LCAxMDAwKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29udGFpbmVyLnNjcm9sbFRvcCA9IHNjcm9sbFRvcDsKICAgICAgICB9CiAgICAgIH0sIHNjcm9sbEludGVydmFsKTsKICAgIH0sCiAgICAvLyDooajlpLTmoLflvI8KICAgIGhlYWRlckNlbGxTdHlsZTogZnVuY3Rpb24gaGVhZGVyQ2VsbFN0eWxlKCkgewogICAgICByZXR1cm4gewogICAgICAgIGJhY2tncm91bmRDb2xvcjogIiNmNWY3ZmEiLAogICAgICAgIGNvbG9yOiAiIzYwNjI2NiIsCiAgICAgICAgZm9udFdlaWdodDogImJvbGQiCiAgICAgIH07CiAgICB9LAogICAgLy8g5pi+56S65oyH5qCH6K+m5oOFCiAgICBzaG93RGV0YWlsczogZnVuY3Rpb24gc2hvd0RldGFpbHMocm93KSB7CiAgICAgIHZhciBfdGhpczE5ID0gdGhpczsKICAgICAgdGhpcy5jdXJyZW50SW5kaWNhdG9yID0gcm93OwogICAgICB0aGlzLmRldGFpbERpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMxOS5pbml0RGV0YWlsQ2hhcnQoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Yid5aeL5YyW6K+m5oOF5Zu+6KGoCiAgICBpbml0RGV0YWlsQ2hhcnQ6IGZ1bmN0aW9uIGluaXREZXRhaWxDaGFydCgpIHsKICAgICAgaWYgKCF0aGlzLmN1cnJlbnRJbmRpY2F0b3IpIHJldHVybjsKICAgICAgdmFyIGNoYXJ0RG9tID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoImluZGljYXRvckNoYXJ0Iik7CiAgICAgIHZhciBteUNoYXJ0ID0gZWNoYXJ0cy5pbml0KGNoYXJ0RG9tKTsKICAgICAgdmFyIG1vbnRocyA9IFsiMeaciCIsICIy5pyIIiwgIjPmnIgiLCAiNOaciCIsICI15pyIIl07CgogICAgICAvLyDnlJ/miJDms6LliqjovoPlpKfnmoTmlbDmja7vvIznoa7kv53lm7Tnu5Xnm67moIflgLzmnInmmI7mmL7otbfkvI8KICAgICAgdmFyIHRhcmdldFZhbHVlID0gcGFyc2VGbG9hdCh0aGlzLmN1cnJlbnRJbmRpY2F0b3IudGFyZ2V0KSB8fCAxMDA7CgogICAgICAvLyDkvb/nlKjlrp7pmYXmlbDmja7vvIzlpoLmnpzmsqHmnInliJnnlJ/miJDms6LliqjmlbDmja4KICAgICAgdmFyIGFjdHVhbERhdGEgPSBbXTsKICAgICAgaWYgKHRoaXMuY3VycmVudEluZGljYXRvci5tb250aGx5RGF0YSAmJiB0aGlzLmN1cnJlbnRJbmRpY2F0b3IubW9udGhseURhdGEubGVuZ3RoID09PSA1KSB7CiAgICAgICAgYWN0dWFsRGF0YSA9IHRoaXMuY3VycmVudEluZGljYXRvci5tb250aGx5RGF0YTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDnlJ/miJDms6LliqjmlbDmja7vvIznoa7kv53mnInotbfkvI8KICAgICAgICB2YXIgZmx1Y3R1YXRpb25SYW5nZSA9IHRhcmdldFZhbHVlICogMC4zOyAvLyDms6LliqjojIPlm7TkuLrnm67moIflgLznmoQzMCUKICAgICAgICBhY3R1YWxEYXRhID0gbW9udGhzLm1hcChmdW5jdGlvbiAoKSB7CiAgICAgICAgICB2YXIgZmx1Y3R1YXRpb24gPSAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiAyICogZmx1Y3R1YXRpb25SYW5nZTsKICAgICAgICAgIHJldHVybiBNYXRoLm1heCgwLCB0YXJnZXRWYWx1ZSArIGZsdWN0dWF0aW9uKTsKICAgICAgICB9KTsKICAgICAgfQoKICAgICAgLy8g55uu5qCH5YC857q/CiAgICAgIHZhciB0YXJnZXREYXRhID0gQXJyYXkoNSkuZmlsbCh0YXJnZXRWYWx1ZSk7CiAgICAgIHZhciBvcHRpb24gPSB7CiAgICAgICAgdGl0bGU6IHsKICAgICAgICAgIHRleHQ6ICIiLmNvbmNhdCh0aGlzLmN1cnJlbnRJbmRpY2F0b3IubmFtZSwgIlx1NjcwOFx1NUVBNlx1OEQ4Qlx1NTJCRiIpLAogICAgICAgICAgbGVmdDogImNlbnRlciIKICAgICAgICB9LAogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICJheGlzIiwKICAgICAgICAgIGF4aXNQb2ludGVyOiB7CiAgICAgICAgICAgIHR5cGU6ICJjcm9zcyIKICAgICAgICAgIH0sCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIGZvcm1hdHRlcihwYXJhbXMpIHsKICAgICAgICAgICAgdmFyIGFjdHVhbFZhbHVlID0gcGFyYW1zWzBdLnZhbHVlLnRvRml4ZWQoMik7CiAgICAgICAgICAgIHZhciB0YXJnZXRWYWx1ZSA9IHBhcmFtc1sxXS52YWx1ZS50b0ZpeGVkKDIpOwogICAgICAgICAgICB2YXIgZGlmZiA9IChhY3R1YWxWYWx1ZSAtIHRhcmdldFZhbHVlKS50b0ZpeGVkKDIpOwogICAgICAgICAgICAvLyDkvb/nlKjnu5/kuIDnmoTnmb3oibLmmL7npLrlt67lgLwKICAgICAgICAgICAgdmFyIGRpZmZDb2xvciA9ICdjb2xvcjojZmZmZmZmJzsKICAgICAgICAgICAgcmV0dXJuICIiLmNvbmNhdChwYXJhbXNbMF0ubmFtZSwgIjxici8+XG4gICAgICAgICAgICAgICAgICAgIikuY29uY2F0KHBhcmFtc1swXS5tYXJrZXIsICIgIikuY29uY2F0KHBhcmFtc1swXS5zZXJpZXNOYW1lLCAiOiAiKS5jb25jYXQoYWN0dWFsVmFsdWUsICI8YnIvPlxuICAgICAgICAgICAgICAgICAgICIpLmNvbmNhdChwYXJhbXNbMV0ubWFya2VyLCAiICIpLmNvbmNhdChwYXJhbXNbMV0uc2VyaWVzTmFtZSwgIjogIikuY29uY2F0KHRhcmdldFZhbHVlLCAiPGJyLz5cbiAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT1cIiIpLmNvbmNhdChkaWZmQ29sb3IsICJcIj5cdTVERUVcdTUwM0M6ICIpLmNvbmNhdChkaWZmLCAiPC9zcGFuPiIpOwogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICBkYXRhOiBbIuWunumZheWAvCIsICLnm67moIflgLwiXSwKICAgICAgICAgIGJvdHRvbTogMTAKICAgICAgICB9LAogICAgICAgIGdyaWQ6IHsKICAgICAgICAgIGxlZnQ6ICIzJSIsCiAgICAgICAgICByaWdodDogIjQlIiwKICAgICAgICAgIGJvdHRvbTogIjE1JSIsCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUKICAgICAgICB9LAogICAgICAgIHhBeGlzOiB7CiAgICAgICAgICB0eXBlOiAiY2F0ZWdvcnkiLAogICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLAogICAgICAgICAgZGF0YTogbW9udGhzCiAgICAgICAgfSwKICAgICAgICB5QXhpczogewogICAgICAgICAgdHlwZTogInZhbHVlIiwKICAgICAgICAgIG5hbWU6IHRoaXMuY3VycmVudEluZGljYXRvci51bml0LAogICAgICAgICAgYXhpc0xhYmVsOiB7CiAgICAgICAgICAgIGZvcm1hdHRlcjogInt2YWx1ZX0iCiAgICAgICAgICB9LAogICAgICAgICAgc2NhbGU6IHRydWUgLy8g57yp5pS+Wei9tOS7peeqgeWHuuaYvuekuuaVsOaNruazouWKqAogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbewogICAgICAgICAgbmFtZTogIuWunumZheWAvCIsCiAgICAgICAgICB0eXBlOiAibGluZSIsCiAgICAgICAgICBkYXRhOiBhY3R1YWxEYXRhLAogICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgIGNvbG9yOiAiIzQwOUVGRiIKICAgICAgICAgIH0sCiAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgd2lkdGg6IDMKICAgICAgICAgIH0sCiAgICAgICAgICBzeW1ib2w6ICJjaXJjbGUiLAogICAgICAgICAgc3ltYm9sU2l6ZTogOCwKICAgICAgICAgIG1hcmtQb2ludDogewogICAgICAgICAgICBkYXRhOiBbewogICAgICAgICAgICAgIHR5cGU6ICJtYXgiLAogICAgICAgICAgICAgIG5hbWU6ICLmnIDlpKflgLwiCiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICB0eXBlOiAibWluIiwKICAgICAgICAgICAgICBuYW1lOiAi5pyA5bCP5YC8IgogICAgICAgICAgICB9XQogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICLnm67moIflgLwiLAogICAgICAgICAgdHlwZTogImxpbmUiLAogICAgICAgICAgZGF0YTogdGFyZ2V0RGF0YSwKICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICBjb2xvcjogIiNGNTZDNkMiCiAgICAgICAgICB9LAogICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgIHdpZHRoOiAyLAogICAgICAgICAgICB0eXBlOiAiZGFzaGVkIgogICAgICAgICAgfSwKICAgICAgICAgIG1hcmtMaW5lOiB7CiAgICAgICAgICAgIGRhdGE6IFt7CiAgICAgICAgICAgICAgdHlwZTogImF2ZXJhZ2UiLAogICAgICAgICAgICAgIG5hbWU6ICLnm67moIflgLwiCiAgICAgICAgICAgIH1dLAogICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgIGZvcm1hdHRlcjogIuebruagh+WAvDoge2N9IgogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfV0KICAgICAgfTsKICAgICAgbXlDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsKCiAgICAgIC8vIOWTjeW6lOW8j+WkhOeQhgogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigicmVzaXplIiwgZnVuY3Rpb24gKCkgewogICAgICAgIG15Q2hhcnQucmVzaXplKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWFs+mXreWvueivneahhgogICAgaGFuZGxlRGlhbG9nQ2xvc2U6IGZ1bmN0aW9uIGhhbmRsZURpYWxvZ0Nsb3NlKCkgewogICAgICB0aGlzLmRldGFpbERpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy5jdXJyZW50SW5kaWNhdG9yID0gbnVsbDsKICAgICAgLy8g5riF6Zmk5Zu+6KGo5a6e5L6LCiAgICAgIHZhciBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCJpbmRpY2F0b3JDaGFydCIpOwogICAgICBpZiAoY2hhcnREb20pIHsKICAgICAgICB2YXIgY2hhcnQgPSBlY2hhcnRzLmdldEluc3RhbmNlQnlEb20oY2hhcnREb20pOwogICAgICAgIGlmIChjaGFydCkgewogICAgICAgICAgY2hhcnQuZGlzcG9zZSgpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8vIOWkhOeQhuihqOagvOWNleWFg+agvOWQiOW5tgogICAgb2JqZWN0U3Bhbk1ldGhvZDogZnVuY3Rpb24gb2JqZWN0U3Bhbk1ldGhvZChfcmVmKSB7CiAgICAgIHZhciByb3cgPSBfcmVmLnJvdywKICAgICAgICBjb2x1bW4gPSBfcmVmLmNvbHVtbiwKICAgICAgICByb3dJbmRleCA9IF9yZWYucm93SW5kZXgsCiAgICAgICAgY29sdW1uSW5kZXggPSBfcmVmLmNvbHVtbkluZGV4OwogICAgICAvLyDku4Xlr7nliIbljoLlkozmjIfmoIflkI3np7DliJfov5vooYzlkIjlubYKICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAwIHx8IGNvbHVtbkluZGV4ID09PSAxKSB7CiAgICAgICAgLy8g6I635Y+W5b2T5YmN6KGM5pWw5o2uCiAgICAgICAgdmFyIGN1cnJlbnRSb3cgPSB0aGlzLnRlY2hJbmRpY2F0b3JzW3Jvd0luZGV4XTsKICAgICAgICBpZiAoIWN1cnJlbnRSb3cpIHJldHVybiB7CiAgICAgICAgICByb3dzcGFuOiAxLAogICAgICAgICAgY29sc3BhbjogMQogICAgICAgIH07CgogICAgICAgIC8vIOWIhuWOguWIl+WQiOW5tuWkhOeQhgogICAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gMCkgewogICAgICAgICAgLy8g5aaC5p6c5piv56ys5LiA6KGM5oiW6ICF5LiO5YmN5LiA6KGM5YiG5Y6C5LiN5ZCM77yM5YiZ6K6h566X5ZCI5bm26KGM5pWwCiAgICAgICAgICBpZiAocm93SW5kZXggPT09IDAgfHwgY3VycmVudFJvdy5mYWN0b3J5ICE9PSB0aGlzLnRlY2hJbmRpY2F0b3JzW3Jvd0luZGV4IC0gMV0uZmFjdG9yeSkgewogICAgICAgICAgICAvLyDorqHnrpfov57nu63nm7jlkIzliIbljoLnmoTooYzmlbAKICAgICAgICAgICAgdmFyIHJvd3NwYW4gPSAxOwogICAgICAgICAgICBmb3IgKHZhciBpID0gcm93SW5kZXggKyAxOyBpIDwgdGhpcy50ZWNoSW5kaWNhdG9ycy5sZW5ndGg7IGkrKykgewogICAgICAgICAgICAgIGlmICh0aGlzLnRlY2hJbmRpY2F0b3JzW2ldLmZhY3RvcnkgPT09IGN1cnJlbnRSb3cuZmFjdG9yeSkgewogICAgICAgICAgICAgICAgcm93c3BhbisrOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICByb3dzcGFuOiByb3dzcGFuLAogICAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgICAgfTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOWmguaenOW9k+WJjeihjOS4juWJjeS4gOihjOWIhuWOguebuOWQjO+8jOWImemakOiXj+W9k+WJjeWNleWFg+agvAogICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgIHJvd3NwYW46IDAsCiAgICAgICAgICAgICAgY29sc3BhbjogMAogICAgICAgICAgICB9OwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLy8g5oyH5qCH5ZCN56ew5YiX5ZCI5bm25aSE55CGCiAgICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAxKSB7CiAgICAgICAgICAvLyDlpoLmnpzmmK/nrKzkuIDooYzvvIzmiJbogIXkuI7liY3kuIDooYzliIbljoLkuI3lkIzvvIzmiJbogIXliIbljoLnm7jlkIzkvYbmjIfmoIflkI3np7DkuI3lkIwKICAgICAgICAgIGlmIChyb3dJbmRleCA9PT0gMCB8fCBjdXJyZW50Um93LmZhY3RvcnkgIT09IHRoaXMudGVjaEluZGljYXRvcnNbcm93SW5kZXggLSAxXS5mYWN0b3J5IHx8IGN1cnJlbnRSb3cubmFtZSAhPT0gdGhpcy50ZWNoSW5kaWNhdG9yc1tyb3dJbmRleCAtIDFdLm5hbWUpIHsKICAgICAgICAgICAgLy8g6K6h566X6L+e57ut55u45ZCM5YiG5Y6C5ZKM5oyH5qCH5ZCN56ew55qE6KGM5pWwCiAgICAgICAgICAgIHZhciBfcm93c3BhbiA9IDE7CiAgICAgICAgICAgIGZvciAodmFyIF9pID0gcm93SW5kZXggKyAxOyBfaSA8IHRoaXMudGVjaEluZGljYXRvcnMubGVuZ3RoOyBfaSsrKSB7CiAgICAgICAgICAgICAgaWYgKHRoaXMudGVjaEluZGljYXRvcnNbX2ldLmZhY3RvcnkgPT09IGN1cnJlbnRSb3cuZmFjdG9yeSAmJiB0aGlzLnRlY2hJbmRpY2F0b3JzW19pXS5uYW1lID09PSBjdXJyZW50Um93Lm5hbWUpIHsKICAgICAgICAgICAgICAgIF9yb3dzcGFuKys7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgIHJvd3NwYW46IF9yb3dzcGFuLAogICAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgICAgfTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOWmguaenOW9k+WJjeihjOS4juWJjeS4gOihjOWIhuWOguWSjOaMh+agh+WQjeensOmDveebuOWQjO+8jOWImemakOiXj+W9k+WJjeWNleWFg+agvAogICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgIHJvd3NwYW46IDAsCiAgICAgICAgICAgICAgY29sc3BhbjogMAogICAgICAgICAgICB9OwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgICByZXR1cm4gewogICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgY29sc3BhbjogMQogICAgICB9OwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_axios", "_interopRequireDefault", "XLSX", "_dimensionality", "_answer", "name", "data", "techIndicators", "detailDialogVisible", "currentIndicator", "excelLoading", "adminShow", "trendChartTimer", "businessUnitTimer", "scrollTimer", "tableScrollPaused", "completionChartTimer", "indicatorCardsScrollTimer", "completionChartScrollDirection", "completionChartScrollPaused", "indicatorCardsScrollPaused", "energyDeptTimer", "scrollSpeed", "chartMouseOverHandler", "chartMouseOutHandler", "tableMouseEnterHandler", "tableMouseLeaveHandler", "currentEnergyType", "currentEnergyDept", "departments", "completionData", "department", "indicators", "target", "actual", "unit", "is<PERSON>igherBetter", "values", "completionRates", "completionData1", "incompleteData", "energyStats", "title", "value", "change", "status", "energyConsumptionData", "departmentEnergyData", "months", "electricity", "water", "gas", "steam", "energyTypes", "label", "color", "energyCostData", "coal", "energyDetailData", "category", "warning", "danger", "trend", "factoryEnergyData", "_defineProperty2", "default", "keyEnergyIndicators", "isEnergyChartWarning", "energyTargetRanges", "min", "max", "computed", "departmentCompletionRates", "departmentRates", "filter", "dept", "includes", "map", "completedCount", "totalIndicators", "length", "for<PERSON>ach", "indicator", "isCompleted", "completionRate", "split", "fullDepartment", "parseFloat", "toFixed", "completedIndicators", "sort", "a", "b", "allIndicatorCompletionRates", "allIndicators", "departmentName", "completionRateStr", "replace", "isNaN", "isFinite", "Math", "abs", "push", "paginatedIndicatorRates", "businessUnits", "units", "Set", "add", "Array", "from", "currentBusinessUnitDepartments", "_this", "currentBusinessUnit", "startsWith", "currentDepartmentIndicators", "_this2", "deptData", "find", "currentDepartment", "allFactories", "watch", "mounted", "_this3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calculateIncompleteData", "$nextTick", "initEnergyDetailCharts", "loadExcelFromRemote", "table", "$refs", "incompleteTable", "tableBody", "querySelector", "originalContent", "innerHTML", "startTableScroll", "startAutoSwitchWithinBusinessUnit", "startBusinessUnitSwitch", "initIndicatorCardsScroll", "cardsContainer", "indicatorCardsContainer", "addEventListener", "window", "resi<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_this4", "stopAutoSwitch", "clearInterval", "removeEventListener", "dispose<PERSON><PERSON><PERSON>", "created", "getDateUpdateList", "methods", "_this5", "url", "axios", "method", "responseType", "then", "response", "Uint8Array", "workbook", "read", "type", "firstSheet", "Sheets", "SheetNames", "jsonData", "utils", "sheet_to_json", "header", "range", "console", "log", "processExcelData", "catch", "error", "$message", "getDetail", "_this6", "dimensionalitylistPermissionList", "res", "msg", "_this7", "dateUpdateList", "headers", "colIndexes", "factory", "indexOf", "jan", "jan<PERSON><PERSON><PERSON>", "feb", "febStatus", "mar", "mar<PERSON><PERSON>us", "apr", "aprStatus", "may", "<PERSON><PERSON><PERSON><PERSON>", "dataRows", "slice", "row", "undefined", "String", "monthlyData", "Boolean", "uniqueKeys", "Map", "uniqueIndicators", "key", "concat", "has", "set", "_this8", "initDepartmentCompletionChart", "initMonthlyTrendChart", "startEnergyDeptSwitch", "_this9", "setInterval", "switchDepartmentWithinBusinessUnit", "_this0", "currentIndex", "findIndex", "nextIndex", "handleAutoSwitchChange", "startAutoSwitch", "_this1", "switchDepartment", "changePage", "page", "currentPage", "incomplete", "_this10", "scrollTop", "scrollHeight", "_this11", "chartDom", "document", "getElementById", "chart", "init", "defaultDisplayPercent", "labels", "item", "deptName", "indicatorName", "fullText", "rates", "option", "tooltip", "trigger", "axisPointer", "formatter", "params", "dataIndex", "actualText", "bind", "grid", "left", "right", "bottom", "top", "containLabel", "dataZoom", "show", "yAxisIndex", "start", "end", "width", "handleSize", "showDetail", "zoomLock", "moveOnMouseWheel", "preventDefaultMouseMove", "toolbox", "feature", "restore", "saveAsImage", "xAxis", "axisLine", "lineStyle", "splitLine", "yAxis", "axisLabel", "lineHeight", "margin", "rich", "fontWeight", "series", "itemStyle", "position", "distance", "<PERSON><PERSON><PERSON><PERSON>", "barCategoryGap", "animationDelay", "idx", "animationEasing", "animationDelayUpdate", "setOption", "on", "resize", "completionChart", "startCompletionChartScroll", "_this12", "getOption", "step", "_this13", "ind", "apply", "_toConsumableArray2", "yMin", "yMax", "text", "textStyle", "fontSize", "backgroundColor", "boundaryGap", "smooth", "symbol", "symbolSize", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "scale", "borderWidth", "shadowOffsetY", "areaStyle", "graphic", "LinearGradient", "offset", "markLine", "silent", "padding", "borderRadius", "animationDuration", "fill", "legend", "_this14", "existingChart", "getInstanceByDom", "dispose", "targetRange", "minValue", "maxValue", "valueRange", "smoothMonotone", "sampling", "borderColor", "hexToRgba", "hex", "alpha", "r", "parseInt", "g", "_this15", "switchEnergyDept", "chartIds", "id", "_this16", "_this17", "switchBusinessUnit", "initKeyEnergyIndicatorsChart", "changeText", "changeColor", "today", "yesterday", "index", "opacity", "getGradientStyle", "total", "colorStops", "segmentCount", "segment", "floor", "localPosition", "startColor", "endColor", "bgColor", "interpolateColors", "brightness", "textColor", "adjustColor", "borderTopColor", "color1", "color2", "factor", "r1", "g1", "b1", "r2", "g2", "b2", "round", "toString", "padStart", "getBadgeStyle", "badgeBgColor", "boxShadow", "amount", "_this18", "container", "clientHeight", "maxScroll", "scrollInterval", "setTimeout", "headerCellStyle", "showDetails", "_this19", "initDetailChart", "myChart", "targetValue", "actualData", "fluctuationRange", "fluctuation", "random", "targetData", "actualValue", "diff", "diffColor", "marker", "seriesName", "markPoint", "handleDialogClose", "objectSpanMethod", "_ref", "column", "rowIndex", "columnIndex", "currentRow", "rowspan", "colspan", "i"], "sources": ["src/views/dataReport/form/dimensionalityOverview.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"page-title\">\r\n      <h2>技经能源指标报表</h2>\r\n    </div>\r\n\r\n\r\n        <!-- 能源指标部分 -->\r\n    <el-card class=\"box-card energy-section\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"clearfix section-header\">\r\n        <span>能源指标概览</span>\r\n      </div>\r\n      <div class=\"chart-container\">\r\n        <!-- 能源报表一行显示 -->\r\n        <div class=\"energy-charts-row\">\r\n          <!-- 部门能源消耗详情（原图四，现在放在第一位置） -->\r\n          <div class=\"chart-item energy-chart-half\">\r\n            <div class=\"chart-title\">\r\n              <span>部门能源消耗详情</span>\r\n              <div class=\"energy-dept-selector\">\r\n                <el-select v-model=\"currentEnergyDept\" size=\"small\" placeholder=\"选择部门\" @change=\"initEnergyDetailCharts\">\r\n                  <el-option\r\n                    v-for=\"factory in allFactories\"\r\n                    :key=\"factory\"\r\n                    :label=\"factory\"\r\n                    :value=\"factory\">\r\n                  </el-option>\r\n                </el-select>\r\n              </div>\r\n            </div>\r\n            <div class=\"energy-subchart-container\">\r\n              <div v-for=\"type in energyTypes\" :key=\"type.value\" \r\n                  class=\"energy-subchart\">\r\n                <div class=\"subchart-title\">{{ type.label }}</div>\r\n                <div :id=\"'energySubchart_' + type.value\" class=\"subchart\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 部门能源消耗趋势（原图一，现在放在第三位置） -->\r\n          <div class=\"chart-item energy-chart-half\">\r\n            <div class=\"chart-title\">\r\n              <span>能源数据监控</span>\r\n            </div>\r\n            <div class=\"key-indicators-container\">\r\n              <div \r\n                v-for=\"(indicator, index) in keyEnergyIndicators\" \r\n                :key=\"index\" \r\n                class=\"indicator-card\"\r\n                :class=\"[indicator.status, {'danger': indicator.change < -50 || indicator.change > 50}]\"\r\n              >\r\n                <div class=\"indicator-title\">{{ indicator.name }}</div>\r\n                <div class=\"indicator-value\">{{ indicator.today }} <span class=\"indicator-unit\">{{ indicator.unit }}</span></div>\r\n                <!-- <div class=\"indicator-target\">\r\n                  目标范围: {{ indicator.targetMin }} ~ {{ indicator.targetMax }} {{ indicator.unit }}\r\n                </div> -->\r\n                <div class=\"indicator-compare\">\r\n                  <span>昨日: {{ indicator.yesterday }}</span>\r\n                  <!-- <span \r\n                    class=\"indicator-change\" \r\n                    :class=\"{ 'up': indicator.change > 0, 'down': indicator.change < 0 }\"\r\n                  >\r\n                    {{ indicator.change > 0 ? '+' : '' }}{{ indicator.change }}%\r\n                  </span> -->\r\n                </div>\r\n                <div class=\"indicator-target\">\r\n                  <span \r\n                    class=\"indicator-change\" \r\n                    :class=\"{ 'up': indicator.change > 0, 'down': indicator.change < 0 }\"\r\n                  >\r\n                   环比变化:{{ indicator.change > 0 ? '+' : '' }}{{ indicator.change }}%\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <el-card class=\"box-card tech-economic-section\" shadow=\"hover\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>技经指标数据表格</span>\r\n        <el-button\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          style=\"float: right; margin-left: 10px\"\r\n          @click=\"loadExcelFromRemote\"\r\n          :loading=\"excelLoading\"\r\n        >\r\n          {{ excelLoading ? \"正在加载...\" : \"重新加载数据\" }}\r\n        </el-button>\r\n      </div>\r\n      <el-table\r\n        :data=\"techIndicators\"\r\n        border\r\n        style=\"width: 100%\"\r\n        :header-cell-style=\"headerCellStyle\"\r\n        :span-method=\"objectSpanMethod\"\r\n      >\r\n        <el-table-column\r\n          prop=\"factory\"\r\n          label=\"分厂\"\r\n          width=\"250\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"name\"\r\n          label=\"指标名称\"\r\n          width=\"280\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: '#409EFF',\r\n                fontWeight: 'bold',\r\n                backgroundColor: scope.row.highlight\r\n                  ? '#a9d3ff'\r\n                  : 'transparent',\r\n              }\"\r\n              >{{ scope.row.name }}</span\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          prop=\"target\"\r\n          label=\"目标\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"unit\"\r\n          label=\"单位\"\r\n          width=\"150\"\r\n          align=\"center\"\r\n        ></el-table-column>\r\n        <!-- <el-table-column prop=\"jan\" label=\"01月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.janStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.jan }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"feb\" label=\"02月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.febStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.feb }}\r\n            </span>\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column prop=\"mar\" label=\"03月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.marStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.mar }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"apr\" label=\"04月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.aprStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.apr }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"may\" label=\"05月实绩\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              :style=\"{\r\n                color: scope.row.mayStatus === 1 ? '#67C23A' : '#F56C6C',\r\n                fontWeight: 'bold',\r\n              }\"\r\n            >\r\n              {{ scope.row.may }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-view\"\r\n              circle\r\n              @click=\"showDetails(scope.row)\"\r\n            ></el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-card>\r\n\r\n        <!-- 指标详情对话框 -->\r\n    <el-dialog\r\n      title=\"指标详情\"\r\n      :visible.sync=\"detailDialogVisible\"\r\n      width=\"70%\"\r\n      :before-close=\"handleDialogClose\"\r\n    >\r\n      <div v-if=\"currentIndicator\">\r\n        <h3>{{ currentIndicator.name }} ({{ currentIndicator.unit }})</h3>\r\n        <div class=\"indicator-info\">\r\n          <p>分厂: {{ currentIndicator.factory }}</p>\r\n          <p>目标值: {{ currentIndicator.target }}</p>\r\n          <p>当前值: {{ currentIndicator.may }}</p>\r\n        </div>\r\n        <div id=\"indicatorChart\" style=\"width: 100%; height: 400px\"></div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport axios from \"axios\";\r\nimport * as XLSX from \"xlsx\";\r\nimport {\r\n  dimensionalitylistPermissionList\r\n} from \"@/api/tYjy/dimensionality\";\r\nimport { dateUpdateList } from \"@/api/tYjy/answer\";\r\nexport default {\r\n  name: 'DimensionalityOverview',\r\n  data() {\r\n    return {\r\n\r\n      // 技经指标数据\r\n      techIndicators: [],\r\n\r\n      detailDialogVisible: false,\r\n      currentIndicator: null,\r\n      // Excel文件加载状态\r\n      excelLoading: false,\r\n      adminShow:\"0\",\r\n\r\n      // 当前显示的部门（用于月度趋势图切换）\r\n      // currentBusinessUnit: '炼铁事业部', // 当前选择的事业部\r\n      // currentDepartment: '',\r\n      // currentIndicator: '',\r\n      // autoSwitchDepartment: true,\r\n      // 定时器\r\n      trendChartTimer: null, // 用于分厂切换\r\n      businessUnitTimer: null, // 用于事业部切换\r\n      scrollTimer: null,\r\n      tableScrollPaused: false, // 是否暂停表格自动滚动\r\n      completionChartTimer: null, // 用于完成率图表滚动\r\n      indicatorCardsScrollTimer: null, // 用于指标卡片滚动\r\n      completionChartScrollDirection: 'down', // 滚动方向：'up' 或 'down'\r\n      completionChartScrollPaused: false, // 是否暂停自动滚动\r\n      indicatorCardsScrollPaused: false, // 是否暂停指标卡片自动滚动\r\n  \r\n      energyDeptTimer: null, // 能源部门切换定时器\r\n      scrollSpeed: 50, // 滚动速度，数值越大速度越慢\r\n      \r\n      // 事件处理器引用\r\n      chartMouseOverHandler: null,\r\n      chartMouseOutHandler: null,\r\n      tableMouseEnterHandler: null, // 表格鼠标进入处理器\r\n      tableMouseLeaveHandler: null, // 表格鼠标离开处理器\r\n      \r\n      // 当前选择的能源类型和部门\r\n      currentEnergyType: 'electricity',\r\n      currentEnergyDept: '炼铁分厂', // 修改为分厂名称\r\n      \r\n      // 部门列表\r\n      departments: ['炼铁事业部', '炼钢事业部', '轧钢事业部', '马科托钢球', '特板事业部', '动力事业部', '物流事业部', '检修事业部'],\r\n      \r\n      // 指标完成情况数据 - 从图片中提取的数据\r\n      completionData: [\r\n        { department: '炼铁事业部-炼铁分厂', indicators: [\r\n          { name: '综合燃料比', target: 512.33, actual: 523.45, unit: 'kg/t', isHigherBetter: false, values: [520.26, 523.67, 523.45, 519.06], completionRates: ['-1.55%', '-2.00%', '-2.17%', '-1.31%'] },\r\n          { name: '工序能耗', target: 369.91, actual: 369.69, unit: 'kgCe/t', isHigherBetter: false, values: [369.74, 367.98, 369.69, 363.76], completionRates: ['0.05%', '0.52%', '0.06%', '1.66%'] },\r\n          { name: '400高炉工序能耗', target: 43.70, actual: 43.56, unit: 'kgCe/t', isHigherBetter: false, values: [43.67, 43.67, 43.56, 43.67], completionRates: ['0.07%', '0.07%', '0.32%', '0.07%'] },\r\n          { name: '360高炉工序能耗', target: 45.01, actual: 44.84, unit: 'kgCe/t', isHigherBetter: false, values: [45.00, 44.93, 44.84, 44.99], completionRates: ['0.02%', '0.07%', '0.38%', '0.04%'] }\r\n        ]},\r\n        { department: '炼铁事业部-烧结分厂', indicators: [\r\n          { name: '工序能耗', target: 154.65, actual: 154.56, unit: 'kgCe/t', isHigherBetter: false, values: [154.65, 154.91, 154.56, 154.57], completionRates: ['0.01%', '0.03%', '0.06%', '0.05%'] },\r\n          { name: '矿渣增幅', target: 16.00, actual: 15.80, unit: 'kgCe/t', isHigherBetter: false, values: [15.82, 15.94, 15.80, 15.85], completionRates: ['0.50%', '0.38%', '1.25%', '0.94%'] },\r\n        ]},\r\n        { department: '炼钢事业部-一炼钢', indicators: [\r\n          { name: '综合石灰消耗', target: 42.95, actual: 42.89, unit: 'kg/t', isHigherBetter: false, values: [42.94, 42.82, 42.89, 40.55], completionRates: ['0.02%', '0.30%', '0.14%', '5.59%'] },\r\n          { name: '氧气消耗', target: 44.56, actual: 44.45, unit: 'm3/t', isHigherBetter: false, values: [44.50, 44.34, 44.45, 44.37], completionRates: ['0.13%', '0.49%', '0.25%', '0.43%'] },\r\n          { name: '电炉工序能耗', target: 57.50, actual: 57.32, unit: 'kgCe/t', isHigherBetter: false, values: [57.34, 57.33, 57.32, 57.19], completionRates: ['0.28%', '0.30%', '0.31%', '0.54%'] },\r\n          { name: '钢铁料收得率', target: 91.50, actual: 91.32, unit: '%', isHigherBetter: true, values: [91.51, 91.32, 91.32, 91.32], completionRates: ['0.01%', '-0.20%', '-0.20%', '-0.20%'] }\r\n        ]},\r\n        { department: '炼钢事业部-二炼钢', indicators: [\r\n          { name: '综合石灰消耗', target: 50.00, actual: 48.95, unit: 'kg/t', isHigherBetter: false, values: [49.90, 49.40, 48.95, 49.00], completionRates: ['0.20%', '1.08%', '2.10%', '2.00%'] },\r\n          { name: '氧气消耗', target: 45.65, actual: 45.26, unit: 'm3/t', isHigherBetter: false, values: [45.49, 45.37, 45.26, 45.35], completionRates: ['0.35%', '0.39%', '0.85%', '0.66%'] },\r\n          { name: '转炉工序能耗', target: -28.50, actual: -29.52, unit: 'kgCe/t', isHigherBetter: true, values: [29.78, 29.57, 29.52, 29.61], completionRates: ['-0.98%', '-3.75%', '-3.58%', '-3.89%'] },\r\n          { name: '钢铁料收得率', target: 91.50, actual: 91.16, unit: '%', isHigherBetter: true, values: [91.50, 91.32, 91.16, 91.15], completionRates: ['0.00%', '-0.20%', '-0.37%', '-0.38%'] }\r\n        ]},\r\n        { department: '轧钢事业部-综合利用', indicators: [\r\n          { name: '废金金属量', target: 0.65, actual: 0.64, unit: '%', isHigherBetter: false, values: [0.65, 0.53, 0.64, 0.49], completionRates: ['0.00%', '18.40%', '1.54%', '-26.15%'] },\r\n          { name: '电耗', target: 22.33, actual: 23.81, unit: 'kWh/t', isHigherBetter: false, values: [22.33, 23.07, 23.81, 21.19], completionRates: ['0.45%', '-3.31%', '-6.62%', '5.11%'] }\r\n        ]},\r\n        { department: '轧钢事业部-一轧钢', indicators: [\r\n          { name: '热轧综合成材率', target: 96.35, actual: 96.22, unit: '%', isHigherBetter: true, values: [96.23, 96.25, 96.22, 96.24], completionRates: ['-0.12%', '-0.10%', '-0.13%', '-0.11%'] },\r\n          { name: '热轧钢材工序能耗', target: 58.55, actual: 58.51, unit: 'kgCe/t', isHigherBetter: false, values: [58.47, 58.52, 58.51, 58.45], completionRates: ['0.14%', '0.05%', '0.07%', '0.17%'] }\r\n        ]},\r\n        { department: '轧钢事业部-二轧钢', indicators: [\r\n          { name: '热轧综合成材率(大棒)', target: 95.37, actual: 95.37, unit: '%', isHigherBetter: true, values: [95.37, 95.22, 95.37, 95.37], completionRates: ['0.00%', '-0.15%', '0.00%', '0.00%'] },\r\n          { name: '热轧综合成材率(小棒)', target: 96.56, actual: 96.56, unit: '%', isHigherBetter: true, values: [96.39, 96.56, 96.56, 96.56], completionRates: ['-0.17%', '0.00%', '0.00%', '0.00%'] },\r\n          { name: '热轧钢材工序能耗(大棒)', target: 67.28, actual: 72.49, unit: 'kgCe/t', isHigherBetter: false, values: [71.35, 73.80, 72.49, 66.24], completionRates: ['-0.93%', '0.75%', '0.00%', '-0.25%'] },\r\n          { name: '热轧钢材工序能耗(小棒)', target: 42.05, actual: 42.02, unit: 'kgCe/t', isHigherBetter: false, values: [42.03, 42.05, 42.02, 45.68], completionRates: ['0.05%', '0.05%', '0.05%', '0.34%'] }\r\n        ]},\r\n        { department: '轧钢事业部-三轧钢', indicators: [\r\n          { name: '热轧综合成材率', target: 96.04, actual: 95.50, unit: '%', isHigherBetter: true, values: [95.76, 96.30, 95.50, 95.51], completionRates: ['-0.28%', '0.44%', '-0.50%', '-0.50%'] },\r\n          { name: '热轧钢材工序能耗', target: 56.31, actual: 54.67, unit: 'kgCe/t', isHigherBetter: false, values: [55.26, 56.34, 54.67, 55.19], completionRates: ['-0.79%', '0.71%', '-1.33%', '-1.18%'] }\r\n        ]},\r\n        { department: '轧钢事业部-特殊钢轧材', indicators: [\r\n          { name: '热轧钢材工序能耗', target: 67.04, actual: 68.64, unit: 'kgCe/t', isHigherBetter: false, values: [67.35, 64.09, 68.64, 64.77], completionRates: ['0.46%', '-3.26%', '0.00%', '-0.23%'] },\r\n          { name: '综合成材率', target: 96.73, actual: 96.73, unit: '%', isHigherBetter: true, values: [96.73, 96.79, 96.73, 96.45], completionRates: ['0.00%', '0.06%', '0.00%', '-0.28%'] }\r\n        ]},\r\n        { department: '轧钢事业部-棒材轧制厂', indicators: [\r\n          { name: '热轧钢材工序能耗(棒扁)', target: 56.93, actual: 61.81, unit: 'kgCe/t', isHigherBetter: false, values: [66.14, 60.00, 61.81, 59.96], completionRates: ['-0.91%', '-1.35%', '0.00%', '-0.24%'] },\r\n          { name: '热轧钢材工序能耗(大盘)', target: 57.08, actual: 61.28, unit: 'kgCe/t', isHigherBetter: false, values: [64.30, 60.29, 61.28, 60.02], completionRates: ['-0.19%', '-0.15%', '0.00%', '-0.26%'] },\r\n          { name: '综合成材率(棒扁轧材)', target: 96.45, actual: 96.12, unit: '%', isHigherBetter: true, values: [96.14, 96.11, 96.12, 96.03], completionRates: ['-0.31%', '-0.33%', '0.00%', '-0.29%'] },\r\n          { name: '综合成材率(大盘卷)', target: 95.85, actual: 95.84, unit: '%', isHigherBetter: true, values: [95.86, 95.90, 95.84, 95.87], completionRates: ['0.01%', '0.04%', '0.00%', '0.02%'] }\r\n        ]},\r\n        { department: '轧钢事业部-棒线材深加工(棒)', indicators: [\r\n          { name: '综合成材率', target: 92.60, actual: 92.60, unit: '%', isHigherBetter: true, values: [92.60, 92.60, 92.60, 92.60], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '轧钢事业部-棒线材深加工(线)', indicators: [\r\n          { name: '控线材综合成材率', target: 98.55, actual: 98.56, unit: '%', isHigherBetter: true, values: [98.55, 98.55, 98.56, 98.55], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '轧钢事业部-线材深加工', indicators: [\r\n          { name: '冷镦材综合成材率', target: 96.36, actual: 96.02, unit: '%', isHigherBetter: true, values: [96.36, 96.36, 96.02, 94.44], completionRates: ['0.00%', '0.00%', '0.00%', '-1.94%'] }\r\n        ]},\r\n        { department: '马科托钢球-马科托钢球', indicators: [\r\n          { name: '综合成材率', target: 93.19, actual: 93.61, unit: '%', isHigherBetter: true, values: [93.13, 93.54, 93.61, 93.80], completionRates: ['-0.06%', '0.42%', '0.00%', '0.20%'] },\r\n          { name: '氧气消耗', target: 44.64, actual: 44.63, unit: 'm3/t', isHigherBetter: false, values: [44.64, 44.64, 44.63, 44.64], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '特板事业部-特钢炼钢分厂', indicators: [\r\n          { name: '氧气消耗', target: 44.64, actual: 44.63, unit: 'm3/t', isHigherBetter: false, values: [44.64, 44.64, 44.63, 44.64], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] },\r\n          { name: '转炉工序能耗', target: -28.50, actual: -29.91, unit: 'kgCe/t', isHigherBetter: true, values: [28.93, 29.67, 29.91, 29.55], completionRates: ['-0.98%', '-3.75%', '-3.58%', '-3.89%'] },\r\n          { name: '钢铁料收得率', target: 91.60, actual: 91.27, unit: '%', isHigherBetter: true, values: [91.80, 91.31, 91.27, 91.27], completionRates: ['0.22%', '-0.33%', '-0.37%', '-0.37%'] },\r\n          { name: '综合石灰消耗', target: 43.67, actual: 46.07, unit: 'kg/t', isHigherBetter: false, values: [46.01, 47.19, 46.07, 43.97], completionRates: ['2.84%', '3.27%', '2.14%', '2.07%'] }\r\n        ]},\r\n        { department: '特板事业部-中板分厂', indicators: [\r\n          { name: '综合命中率', target: 98.62, actual: 98.63, unit: '%', isHigherBetter: true, values: [98.63, 98.65, 98.63, 98.63], completionRates: ['0.00%', '0.02%', '0.00%', '0.00%'] },\r\n          { name: '综合成材率', target: 92.55, actual: 92.04, unit: '%', isHigherBetter: true, values: [92.63, 92.03, 92.04, 92.65], completionRates: ['0.09%', '-0.51%', '-0.51%', '0.02%'] },\r\n          { name: '整客户交付率', target: 98.75, actual: 98.78, unit: '%', isHigherBetter: true, values: [98.75, 98.77, 98.78, 98.75], completionRates: ['0.00%', '0.02%', '0.00%', '0.00%'] },\r\n          { name: '热轧工序能耗', target: 45.02, actual: 43.32, unit: 'kgCe/t', isHigherBetter: false, values: [44.60, 44.15, 43.32, 43.80], completionRates: ['-0.93%', '-1.25%', '-1.68%', '-0.70%'] },\r\n          { name: '热装比', target: 75.00, actual: 75.85, unit: '%', isHigherBetter: true, values: [75.40, 77.64, 75.85, 74.05], completionRates: ['0.53%', '2.18%', '0.00%', '-1.95%'] }\r\n        ]},\r\n        { department: '特板事业部-厚板分厂', indicators: [\r\n          { name: '综合命中率', target: 97.49, actual: 97.27, unit: '%', isHigherBetter: true, values: [97.49, 97.53, 97.27, 97.52], completionRates: ['0.00%', '0.04%', '-0.26%', '0.05%'] },\r\n          { name: '综合成材率', target: 90.91, actual: 90.76, unit: '%', isHigherBetter: true, values: [90.41, 90.79, 90.76, 90.78], completionRates: ['-0.55%', '0.32%', '-0.26%', '0.02%'] },\r\n          { name: '整客户交付率', target: 96.34, actual: 96.34, unit: '%', isHigherBetter: true, values: [96.37, 96.35, 96.34, 96.31], completionRates: ['0.03%', '-0.02%', '0.00%', '-0.03%'] },\r\n          { name: '热轧工序能耗', target: 48.62, actual: 45.85, unit: 'kgCe/t', isHigherBetter: false, values: [46.27, 46.01, 45.85, 47.11], completionRates: ['-2.79%', '-2.56%', '-2.34%', '-1.54%'] },\r\n          { name: '热装比(200℃)', target: 50.00, actual: 31.23, unit: '%', isHigherBetter: true, values: [50.60, 51.28, 31.23, 50.28], completionRates: ['1.20%', '2.56%', '0.00%', '-1.56%'] }\r\n        ]},\r\n        { department: '特板事业部-钢材深加工', indicators: [\r\n          { name: '整客户交付率', target: 99.11, actual: 99.12, unit: '%', isHigherBetter: true, values: [99.11, 99.10, 99.12, 99.12], completionRates: ['0.00%', '-0.01%', '0.00%', '0.00%'] },\r\n          { name: '综合命中率', target: 99.73, actual: 99.75, unit: '%', isHigherBetter: true, values: [99.73, 99.74, 99.75, 99.74], completionRates: ['0.00%', '-0.01%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-热电', indicators: [\r\n          { name: '标煤产汽率', target: 10.85, actual: 10.90, unit: 't/tCe', isHigherBetter: true, values: [10.87, 10.89, 10.90, 10.92], completionRates: ['0.19%', '0.20%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-供电工区', indicators: [\r\n          { name: '供电功率因数', target: 95.00, actual: 98.00, unit: '%(95.3-100)', isHigherBetter: true, values: [98.66, 98.66, 98.00, 98.00], completionRates: ['3.20%', '3.20%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-水处理分厂', indicators: [\r\n          { name: '吨钢软水水', target: 1.62, actual: 1.63, unit: 'm3/t', isHigherBetter: false, values: [1.62, 1.61, 1.63, 1.69], completionRates: ['0.62%', '-0.62%', '0.00%', '0.62%'] },\r\n          { name: '吨钢热水处理量', target: 21.20, actual: 20.25, unit: 't/t钢', isHigherBetter: false, values: [19.89, 20.14, 20.25, 20.28], completionRates: ['-6.19%', '-3.57%', '0.00%', '1.40%'] }\r\n        ]},\r\n        { department: '动力事业部-制氧分厂', indicators: [\r\n          { name: '氧气就耗率', target: 0.02, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '动力事业部-煤气分厂', indicators: [\r\n          { name: '高炉煤气就耗率', target: 0.02, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '物流事业部-储运公司', indicators: [\r\n          { name: '360混匀矿水分合格率', target: 90.56, actual: 91.32, unit: '%', isHigherBetter: true, values: [90.63, 90.68, 91.32, 90.68], completionRates: ['0.07%', '0.05%', '0.00%', '0.00%'] },\r\n          { name: '混匀矿稳定率', target: 97.82, actual: 97.89, unit: '%', isHigherBetter: true, values: [97.83, 98.01, 97.89, 98.01], completionRates: ['0.18%', '0.17%', '0.00%', '0.00%'] }\r\n        ]},\r\n        { department: '检修事业部-检修分厂', indicators: [\r\n          { name: '热修机率', target: 0.10, actual: 0.00, unit: '%', isHigherBetter: false, values: [0.00, 0.00, 0.00, 0.00], completionRates: ['0.00%', '0.00%', '0.00%', '0.00%'] }\r\n        ]}\r\n      ],\r\n      completionData1: \r\n      [\r\n      {department:'钙业分厂'},\r\n      {department:'矿渣微粉'},\r\n      {department:'烧结分厂-1#烧结'},\r\n      {department:'烧结分厂-2#烧结'},\r\n      {department:'炼铁分厂-1#高炉'},\r\n      {department:'炼铁分厂-2#高炉'},\r\n      {department:'炼铁分厂-3#高炉'},\r\n      {department:'炼铁分厂-小喷煤'},\r\n      {department:'炼铁分厂-大喷煤'},\r\n      {department:'一炼钢'},\r\n      {department:'二炼钢'},\r\n      {department:'一轧钢'},\r\n      {department:'二轧-大棒'},\r\n      {department:'二轧-小棒'},\r\n      {department:'特板炼钢'},\r\n      {department:'3500中板'},\r\n      {department:'4300厚板'},\r\n      {department:'4300水处理'},\r\n      {department:'热处理'},\r\n      {department:'高线分厂'},\r\n      {department:'线材深加工'},\r\n      {department:'棒材深加工'},\r\n      {department:'热电分厂-热电'},\r\n      {department:'热电分厂-亚临界'},\r\n      {department:'热电分厂-余热'},\r\n      {department:'热电分厂-鼓风'},\r\n      {department:'制氧分厂'},\r\n      {department:'制氧分厂-一期'},\r\n      {department:'制氧分厂-三期'},\r\n      {department:'制氧分厂-空压站'},\r\n      {department:'水处理-一期'},\r\n      {department:'水处理-二期'},\r\n      {department:'水处理-三期'},\r\n      {department:'煤气分厂'},\r\n      {department:'供电一区'},\r\n      {department:'兴澄钢球'},\r\n      {department:'兴澄港务'},\r\n      {department:'储运公司'},\r\n      {department:'综合利用'},\r\n      {department:'合金炉分厂'},\r\n      {department:'物管部'},\r\n      {department:'后勤部'},\r\n      {department:'其他'},\r\n      {department:'损耗'},\r\n      {department:'合计'},\r\n\r\n      ],\r\n      // 未完成指标数据\r\n      incompleteData: [],\r\n      \r\n      // 能源指标统计数据\r\n      energyStats: [\r\n        { title: '综合能耗', value: '5.21吨标煤/吨钢', change: -2.3, status: 'good' },\r\n        { title: '水资源消耗', value: '3.8立方米/吨钢', change: -1.5, status: 'good' },\r\n        { title: '电力消耗', value: '485千瓦时/吨钢', change: 0.8, status: 'warning' },\r\n        { title: '煤气回收率', value: '98.5%', change: 1.2, status: 'good' },\r\n        { title: '余热回收率', value: '75.2%', change: 2.5, status: 'good' },\r\n        { title: '二氧化碳排放', value: '1.85吨/吨钢', change: -3.2, status: 'good' }\r\n      ],\r\n      \r\n      // 能源消耗数据\r\n      energyConsumptionData: [\r\n        { name: '电力', value: 35 },\r\n        { name: '煤炭', value: 25 },\r\n        { name: '天然气', value: 15 },\r\n        { name: '蒸汽', value: 10 },\r\n        { name: '其他', value: 15 }\r\n      ],\r\n      \r\n      // 部门能源消耗数据（新增）- 按月统计\r\n      departmentEnergyData: {\r\n        months: ['1月', '2月', '3月', '4月'],\r\n        departments: ['炼铁事业部', '炼钢事业部', '轧钢事业部', '马科托钢球', '特板事业部', '动力事业部', '物流事业部', '检修事业部'],\r\n        electricity: { // 电力消耗 (万千瓦时)\r\n          '炼铁事业部': [1250, 1180, 1220, 1260],\r\n          '炼钢事业部': [1850, 1790, 1810, 1880],\r\n          '轧钢事业部': [1450, 1420, 1480, 1440],\r\n          '马科托钢球': [420, 410, 430, 425],\r\n          '特板事业部': [980, 950, 970, 990],\r\n          '动力事业部': [320, 310, 330, 325],\r\n          '物流事业部': [180, 175, 185, 182],\r\n          '检修事业部': [150, 145, 155, 152]\r\n        },\r\n        water: { // 水资源消耗 (万吨)\r\n          '炼铁事业部': [85, 82, 86, 88],\r\n          '炼钢事业部': [120, 115, 118, 122],\r\n          '轧钢事业部': [95, 92, 96, 94],\r\n          '马科托钢球': [28, 27, 29, 28.5],\r\n          '特板事业部': [65, 63, 66, 67],\r\n          '动力事业部': [180, 175, 185, 182],\r\n          '物流事业部': [15, 14, 16, 15.5],\r\n          '检修事业部': [12, 11.5, 12.5, 12.2]\r\n        },\r\n        gas: { // 天然气消耗 (万立方米)\r\n          '炼铁事业部': [320, 310, 325, 330],\r\n          '炼钢事业部': [480, 470, 485, 490],\r\n          '轧钢事业部': [380, 370, 385, 375],\r\n          '马科托钢球': [110, 105, 112, 108],\r\n          '特板事业部': [250, 245, 255, 260],\r\n          '动力事业部': [85, 82, 87, 86],\r\n          '物流事业部': [45, 43, 46, 44],\r\n          '检修事业部': [35, 34, 36, 35.5]\r\n        },\r\n        steam: { // 蒸汽消耗 (万吨)\r\n          '炼铁事业部': [45, 43, 46, 47],\r\n          '炼钢事业部': [65, 63, 66, 67],\r\n          '轧钢事业部': [52, 50, 53, 51],\r\n          '马科托钢球': [15, 14.5, 15.5, 15.2],\r\n          '特板事业部': [35, 34, 36, 37],\r\n          '动力事业部': [12, 11.5, 12.5, 12.2],\r\n          '物流事业部': [8, 7.8, 8.2, 8.1],\r\n          '检修事业部': [6, 5.8, 6.2, 6.1]\r\n        }\r\n      },\r\n      \r\n      // 能源类型选项\r\n      energyTypes: [\r\n        { label: '电力消耗', value: 'electricity', unit: '千瓦时', color: '#2f80ed' },\r\n        { label: '水资源消耗', value: 'water', unit: '吨', color: '#2d9cdb' },\r\n        { label: '天然气消耗', value: 'gas', unit: '立方米', color: '#1a73e8' },\r\n        { label: '蒸汽消耗', value: 'steam', unit: '吨', color: '#27ae60' }\r\n      ],\r\n      \r\n      // 能源成本数据\r\n      energyCostData: {\r\n        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\r\n        electricity: [320, 332, 301, 334, 390, 330, 320, 315, 310, 325, 315, 318],\r\n        coal: [220, 182, 191, 234, 290, 330, 310, 295, 300, 285, 270, 275],\r\n        gas: [150, 232, 201, 154, 190, 180, 165, 175, 190, 195, 205, 210],\r\n        steam: [98, 77, 101, 99, 120, 125, 110, 100, 105, 115, 110, 120]\r\n      },\r\n      \r\n      // 能源详情数据 - 当日消耗\r\n      energyDetailData: [\r\n        { \r\n          name: '转炉煤气', \r\n          category: 'gas',\r\n          value: 125.6, \r\n          unit: '万m³', \r\n          target: 130, \r\n          warning: 140, \r\n          danger: 150, \r\n          status: 'normal',\r\n          trend: -2.1 // 相比昨日变化百分比\r\n        },\r\n        { \r\n          name: '高炉煤气', \r\n          category: 'gas',\r\n          value: 287.3, \r\n          unit: '万m³', \r\n          target: 280, \r\n          warning: 300, \r\n          danger: 320, \r\n          status: 'warning',\r\n          trend: 5.3\r\n        },\r\n        { \r\n          name: '焦炉煤气', \r\n          category: 'gas',\r\n          value: 98.4, \r\n          unit: '万m³', \r\n          target: 100, \r\n          warning: 110, \r\n          danger: 120, \r\n          status: 'normal',\r\n          trend: -1.2\r\n        },\r\n        { \r\n          name: '天然气', \r\n          category: 'gas',\r\n          value: 45.7, \r\n          unit: '万m³', \r\n          target: 40, \r\n          warning: 45, \r\n          danger: 50, \r\n          status: 'danger',\r\n          trend: 12.5\r\n        },\r\n        { \r\n          name: '饱和蒸汽', \r\n          category: 'steam',\r\n          value: 56.2, \r\n          unit: '万吨', \r\n          target: 55, \r\n          warning: 60, \r\n          danger: 65, \r\n          status: 'normal',\r\n          trend: 1.8\r\n        },\r\n        { \r\n          name: '过热蒸汽', \r\n          category: 'steam',\r\n          value: 32.8, \r\n          unit: '万吨', \r\n          target: 30, \r\n          warning: 35, \r\n          danger: 40, \r\n          status: 'warning',\r\n          trend: 7.2\r\n        },\r\n        { \r\n          name: '工业用水', \r\n          category: 'water',\r\n          value: 142.5, \r\n          unit: '万吨', \r\n          target: 140, \r\n          warning: 150, \r\n          danger: 160, \r\n          status: 'normal',\r\n          trend: 1.5\r\n        },\r\n        { \r\n          name: '循环冷却水', \r\n          category: 'water',\r\n          value: 285.3, \r\n          unit: '万吨', \r\n          target: 280, \r\n          warning: 300, \r\n          danger: 320, \r\n          status: 'warning',\r\n          trend: 3.8\r\n        },\r\n        { \r\n          name: '高压电力', \r\n          category: 'electricity',\r\n          value: 1856.4, \r\n          unit: '万kWh', \r\n          target: 1800, \r\n          warning: 1900, \r\n          danger: 2000, \r\n          status: 'warning',\r\n          trend: 4.2\r\n        },\r\n        { \r\n          name: '中压电力', \r\n          category: 'electricity',\r\n          value: 945.2, \r\n          unit: '万kWh', \r\n          target: 950, \r\n          warning: 1000, \r\n          danger: 1050, \r\n          status: 'normal',\r\n          trend: -0.8\r\n        }\r\n      ],\r\n      \r\n      // 分厂能源消耗数据 - 按月统计\r\n      factoryEnergyData: {\r\n        months: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n        electricity: { // 电力消耗 (万千瓦时)\r\n          '钙业分厂':[1400884,1434005,1435766,1376319,1301095,1269630],\r\n          '矿渣微粉':[5805794,2131847,6089046,6100999,6417335,6478262],\r\n          '烧结分厂-1#烧结':[13117902,12943568,11061444,10812393,11623702,11032539],\r\n          '烧结分厂-2#烧结':[13033926,10436634,9287884,9769316,9879397,10565760],\r\n          '炼铁分厂-1#高炉':[0,0,0,0,0,0],\r\n          '炼铁分厂-2#高炉':[66940,0,0,0,0,0],\r\n          '炼铁分厂-3#高炉':[0,0,0,0,0,0],\r\n          '炼铁分厂-小喷煤':[0,0,0,0,0,0],\r\n          '一炼钢':[30768167,30125781,30297463,28980497,29774159,31343397],\r\n          '二炼钢':[22488495,21968943,21787916,22171067,21242115,21788119],\r\n          '一轧钢':[5795777,5452204,5711051,5648575,5496447,5733403],\r\n          '二轧-大棒':[3246250,3195091,3268836,3363082,3262553,3466935],\r\n          '二轧-小棒':[5059018,4954511,4811987,5053456,4687922,4852370],\r\n          '特板炼钢':[30387862,29842019,26431716,29469372,29271035,29035520],\r\n          '3500中板':[7706300,6644420,7397716,7328102,7206215,7421179],\r\n          '4300厚板':[13519112,12464662,10028536,12881286,12674940,13166679],\r\n          '热处理':[2813937,2726501,2275425,2384412,2206548,3135105],\r\n          '高线分厂':[7621452,7822538,7920470,7152446,7071412,7342066],\r\n          '线材深加工':[968654,878075,925284,902573,915434,915053],\r\n          '棒材深加工':[3330295,3280875,3350518,3481898,3304357,3326433],\r\n          '热电分厂-热电':[8709519,9757840,10102720,9660480,9307680,7473520],\r\n          '热电分厂-亚临界':[2842245,4042080,3634400,4309800,4358435,4033588],\r\n          '热电分厂-余热':[133500,247280,112640,112640,218560,136480],\r\n          '热电分厂-鼓风':[12146140,21995616,21969803,23377762,28428976,37036933],\r\n          '制氧分厂-一期':[26203549,26456523,20347464,26646735,25523888,27290746],\r\n          '制氧分厂-三期':[29239632,27886434,29274657,26601919,26162015,26865678],\r\n          '制氧分厂-空压站':[6092759,6483609,6455930,6661039,6369297,6464792],\r\n          '水处理-一期':[2467240,2470442,2515829,2549457,3222031,2884069],\r\n          '水处理-二期':[4951897,4902986,4843723,5040984,5021708,5263224],\r\n          '水处理-三期':[5224649,5320012,5060813,5407588,5488715,5816560],\r\n          '煤气分厂':[643132,693466,657052,659543,624830,620973],\r\n          '供电一区':[100415,103537,103906,133611,166027,180227],\r\n          '兴澄钢球':[1087981,840818,981751,1057275,909275,1188557],\r\n          '兴澄港务':[68023,59481,69918,63336,44541,65389],\r\n          '储运公司':[5759324,5859975,5352206,5316640,5378337,5644938],\r\n          '综合利用':[1046763,1048737,1103178,1006943,1082975,1068868],\r\n          '合金炉分厂':[2769092,4321005,3221559,4932761,4789800,5878980],\r\n          '物管部':[39902,43498,34953,29662,24373,24227],\r\n          '后勤部':[46436,51144,39739,36459,36817,36596],\r\n          '其他':[824375,775107,749943,688365,764337,978520],\r\n          '物管部':[1295140,664000,621920,541480,576620,836960],\r\n          '合计':[278822476,280325264,269335161,281710272,284833903,300662274]\r\n          // '炼铁分厂': [850, 820, 840, 860],\r\n          // '烧结分厂': [400, 380, 390, 410],\r\n          // '一炼钢': [950, 920, 940, 960],\r\n          // '二炼钢': [900, 880, 890, 920],\r\n          // '综合利用': [280, 270, 290, 275],\r\n          // '一轧钢': [380, 370, 390, 375],\r\n          // '二轧钢': [360, 350, 370, 355],\r\n          // '三轧钢': [340, 330, 350, 335],\r\n          // '特殊钢轧材': [320, 310, 330, 315],\r\n          // '棒材轧制厂': [300, 290, 310, 295],\r\n          // '棒线材深加工(棒)': [180, 170, 190, 175],\r\n          // '棒线材深加工(线)': [160, 150, 170, 155],\r\n          // '线材深加工': [140, 130, 150, 135],\r\n          // '马科托钢球': [420, 410, 430, 425],\r\n          // '特钢炼钢分厂': [380, 370, 390, 375],\r\n          // '中板分厂': [360, 350, 370, 355],\r\n          // '厚板分厂': [340, 330, 350, 335],\r\n          // '钢材深加工': [320, 310, 330, 315],\r\n          // '热电': [180, 170, 190, 175],\r\n          // '供电工区': [160, 150, 170, 155],\r\n          // '水处理分厂': [140, 130, 150, 135],\r\n          // '制氧分厂': [120, 110, 130, 115],\r\n          // '煤气分厂': [100, 90, 110, 95],\r\n          // '储运公司': [180, 175, 185, 182],\r\n          // '检修分厂': [150, 145, 155, 152]\r\n        },\r\n        water: { // 水资源消耗 (万吨)\r\n          '钙业分厂':[2517,2376,2253,2173,2259,2301],\r\n          '矿渣微粉':[2890,2591,2478,2358,2381,2256],\r\n          '烧结分厂-1#烧结':[41290,40443,39591,40136,40852,40146],\r\n          '烧结分厂-2#烧结':[71804,67539,69009,69947,72050,70344],\r\n          '炼铁分厂-1#高炉':[20870,21082,21231,26729,24702,28188],\r\n          '炼铁分厂-2#高炉':[61032,65615,56218,65690,63567,67033],\r\n          '炼铁分厂-3#高炉':[70604,69964,78613,85914,85358,99016],\r\n          '炼铁分厂-喷煤（0#2#高炉）':[2308,2457,2897,3017,2851,3597],\r\n          '一炼钢':[4631152,4529160,4609510,4645449,4536932,4618563],\r\n          '二炼钢':[9104629,8974584,8707241,8864449,8574110,8780493],\r\n          '一轧钢':[1613416,1566303,1603660,1604021,1574222,1641956],\r\n          '二轧-大棒':[1549976,1473652,1448998,1482840,1420848,1461097],\r\n          '二轧-小棒':[1761922,1636518,1599618,1707777,1611290,1566453],\r\n          '特板炼钢':[11065202,10861897,10078370,10798271,10539246,10414072],\r\n          '3500中板':[2528768,2531102,2577106,2614333,2509852,2746164],\r\n          '4300厚板':[53602,52973,47100,51727,48993,52439],\r\n          '4300水处理':[51957,59997,58691,55515,61891,66474],\r\n          '热处理':[15873,13257,11536,10213,10796,10018],\r\n          '高线分厂':[24783,21328,19082,23117,24548,23518],\r\n          '热电分厂-热电':[32654,35991,46976,34907,35831,30211],\r\n          '热电分厂-鼓风':[66781,60578,74506,84715,56847,60222],\r\n          '热电分厂-亚临界':[50702,42898,53360,80937,75090,83220],\r\n          '热电分厂-净水厂直供原水':[82710,66641,58670,59022,51034,51739],\r\n          '制氧分厂-一期':[48107,89953,40665,51367,56605,55866],\r\n          '制氧分厂-三期':[27136,13397,10726,21896,25682,11716],\r\n          '制氧分厂-空压站':[3928,3563,4385,4983,3343,3542],\r\n          '水处理-一期':[666457,684861,699017,706374,703497,737291],\r\n          '水处理-二期':[283733,290660,245217,243883,245300,285485],\r\n          '水处理-三期':[162012,140737,143298,211329,206303,245132],\r\n          '供电一区':[2273,2357,2502,2597,2385,2153],\r\n          '储运公司':[41906,40362,41980,42361,41963,40779],\r\n          '综合利用':[25117,24586,19931,25926,22555,15964],\r\n          '合金炉分厂':[939944,1406061,1129690,1591196,1392990,1834398],\r\n          '后勤综合楼':[606264,473027,520855,233384,168146,365997],\r\n          '公用':[79065,79871,79634,79871,80971,81341],\r\n          '合计':[35793384,35448381,34204614,35628424,34375290,35599184]\r\n          // '炼铁分厂': [55, 53, 56, 57],\r\n          // '烧结分厂': [30, 29, 31, 32],\r\n          // '一炼钢': [65, 63, 66, 67],\r\n          // '二炼钢': [60, 58, 61, 62],\r\n          // '综合利用': [20, 19, 21, 20.5],\r\n          // '一轧钢': [25, 24, 26, 25.5],\r\n          // '二轧钢': [23, 22, 24, 23.5],\r\n          // '三轧钢': [22, 21, 23, 22.5],\r\n          // '特殊钢轧材': [21, 20, 22, 21.5],\r\n          // '棒材轧制厂': [20, 19, 21, 20.5],\r\n          // '棒线材深加工(棒)': [12, 11, 13, 12.5],\r\n          // '棒线材深加工(线)': [11, 10, 12, 11.5],\r\n          // '线材深加工': [9, 8, 10, 9.5],\r\n          // '马科托钢球': [28, 27, 29, 28.5],\r\n          // '特钢炼钢分厂': [25, 24, 26, 25.5],\r\n          // '中板分厂': [23, 22, 24, 23.5],\r\n          // '厚板分厂': [22, 21, 23, 22.5],\r\n          // '钢材深加工': [21, 20, 22, 21.5],\r\n          // '热电': [65, 63, 67, 66],\r\n          // '供电工区': [45, 43, 47, 46],\r\n          // '水处理分厂': [70, 68, 72, 71],\r\n          // '制氧分厂': [55, 53, 57, 56],\r\n          // '煤气分厂': [45, 43, 47, 46],\r\n          // '储运公司': [15, 14, 16, 15.5],\r\n          // '检修分厂': [12, 11.5, 12.5, 12.2]\r\n        },\r\n        gas: {\r\n          '钙业分厂':[2080,14317,12875,6240,0,0],\r\n          '矿渣微粉':[0,0,0,0,0,0],\r\n          '烧结分厂-1#烧结':[928,358,1054,2476,1040,1040],\r\n          '烧结分厂-2#烧结':[13471,3440,11592,14448,19094,38272],\r\n          '炼铁分厂-1#高炉':[4264,2392,1832,3952,1456,2184],\r\n          '炼铁分厂-2#高炉':[3744,6344,14808,4104,3432,5616],\r\n          '炼铁分厂-3#高炉':[48880,74483,62541,92272,17992,47889],\r\n          '炼铁分厂-小喷煤':[936,1144,1040,1212,936,1040],\r\n          '炼铁分厂-大喷煤':[42432,35464,32200,20696,15288,17784],\r\n          '一炼钢':[321044,441179,438423,450993,381575,351275],\r\n          '二炼钢':[150844,208889,212605,212274,204372,195425],\r\n          '一轧钢':[2857,12084,12120,6818,6230,16217],\r\n          '二轧-大棒':[23824,30000,14520,10630,24036,24489],\r\n          '二轧-小棒':[16000,12487,8926,4555,16024,10496],\r\n          '特板炼钢':[154790,218832,190568,135757,143405,140886],\r\n          '3500中板':[619233,644217,767563,908906,890002,926636],\r\n          '4300厚板':[492656,621775,785247,500988,563886,792919],\r\n          '热处理':[1093740,1370389,1296365,1408988,1170360,1299076],\r\n          '高线分厂':[214,96,298,207,100,204],\r\n          '棒材深加工':[1133600,1177473,1139440,1214108,1141737,1153930],\r\n          '线材深加工':[614092,501810,509750,552280,559440,517720],\r\n          '热电分厂-热电':[15713,33472,183549,21279,82525,33965],\r\n          '热电分厂-鼓风':[58638,35678,204014,28303,60770,58240],\r\n          '热电分厂-亚临界':[94063,60326,27315,63941,90389,93837],\r\n          '煤气分厂':[6285,7289,7616,7537,7561,3075],\r\n          '兴澄钢球':[194970,206307,208890,225422,209463,215730],\r\n          '综合利用':[0,0,0,0,0,0],\r\n          '合计':[5109298,5720245,6145151,5898386,5611113,5947945]\r\n          \r\n           // 天然气消耗 (万立方米)\r\n          // '炼铁分厂': [220, 210, 225, 230],\r\n          // '烧结分厂': [100, 95, 105, 110],\r\n          // '一炼钢': [250, 240, 255, 260],\r\n          // '二炼钢': [230, 220, 235, 240],\r\n          // '综合利用': [75, 70, 78, 76],\r\n          // '一轧钢': [95, 90, 98, 96],\r\n          // '二轧钢': [90, 85, 93, 91],\r\n          // '三轧钢': [85, 80, 88, 86],\r\n          // '特殊钢轧材': [80, 75, 83, 81],\r\n          // '棒材轧制厂': [75, 70, 78, 76],\r\n          // '棒线材深加工(棒)': [45, 40, 48, 46],\r\n          // '棒线材深加工(线)': [40, 35, 43, 41],\r\n          // '线材深加工': [35, 30, 38, 36],\r\n          // '马科托钢球': [110, 105, 112, 108],\r\n          // '特钢炼钢分厂': [95, 90, 98, 96],\r\n          // '中板分厂': [90, 85, 93, 91],\r\n          // '厚板分厂': [85, 80, 88, 86],\r\n          // '钢材深加工': [80, 75, 83, 81],\r\n          // '热电': [35, 30, 38, 36],\r\n          // '供电工区': [25, 20, 28, 26],\r\n          // '水处理分厂': [20, 15, 23, 21],\r\n          // '制氧分厂': [15, 10, 18, 16],\r\n          // '煤气分厂': [10, 5, 13, 11],\r\n          // '储运公司': [45, 43, 46, 44],\r\n          // '检修分厂': [35, 34, 36, 35.5]\r\n        },\r\n        steam: { // 蒸汽消耗 (万吨)\r\n          '钙业分厂':[0,0,0,0,0,0],\r\n          '矿渣微粉':[0,0,0,0,0,0],\r\n          '烧结分厂-1#烧结':[0,0,0,0,0,0],\r\n          '烧结分厂-2#烧结':[2368,2379,1765,1615,1422,1663],\r\n          '炼铁分厂-1#高炉':[578,637,485,554,388,671],\r\n          '炼铁分厂-2#高炉':[295,141,109,265,419,312],\r\n          '炼铁分厂-3#高炉':[1445,2143,1633,124,0,127],\r\n          '一炼钢':[116,388,50,50,96,164],\r\n          '二炼钢':[12927,16443,17638,17071,16843,16351],\r\n          '一轧钢':[0,0,0,0,0,0],\r\n          '二轧-大棒':[209,222,253,133,198,116],\r\n          '二轧-小棒':[193,204,233,123,182,108],\r\n          '特板炼钢':[22968,22336,18819,21647,21604,21708],\r\n          '3500中板':[0,0,0,0,0,0],\r\n          '4300厚板':[0,0,0,0,0,0],\r\n          '高线分厂':[0,0,0,0,0,0],\r\n          '棒材深加工':[0,0,0,0,0,0],\r\n          '线材深加工':[2240,1616,1364,1121,1082,920],\r\n          '热电分厂-热电':[85199,90583,83075,93108,89615,95156],\r\n          '热电分厂-鼓风':[0,0,0,0,0,0],\r\n          '热电分厂-热水':[0,0,0,0,0,0],\r\n          '煤气分厂':[0,0,0,0,0,0],\r\n          '制氧分厂':[400,400,400,400,400,400],\r\n          '后勤部':[0,0,0,0,0,0],\r\n          '外销蒸汽':[32568,17534,27334,24311,21126,19504],\r\n          '综合利用':[216,31,16,19,32,46],\r\n          '损耗':[1270,1166,1088,907,838,825],\r\n          '合计':[162992,156223,154262,161448,154245,158071]\r\n          // '炼铁分厂': [30, 28, 31, 32],\r\n          // '烧结分厂': [15, 13, 16, 17],\r\n          // '一炼钢': [35, 33, 36, 37],\r\n          // '二炼钢': [30, 28, 31, 32],\r\n          // '综合利用': [10, 9, 11, 10.5],\r\n          // '一轧钢': [13, 12, 14, 13.5],\r\n          // '二轧钢': [12, 11, 13, 12.5],\r\n          // '三轧钢': [11, 10, 12, 11.5],\r\n          // '特殊钢轧材': [10, 9, 11, 10.5],\r\n          // '棒材轧制厂': [9, 8, 10, 9.5],\r\n          // '棒线材深加工(棒)': [5, 4, 6, 5.5],\r\n          // '棒线材深加工(线)': [4, 3, 5, 4.5],\r\n          // '线材深加工': [3, 2, 4, 3.5],\r\n          // '马科托钢球': [15, 14.5, 15.5, 15.2],\r\n          // '特钢炼钢分厂': [13, 12, 14, 13.5],\r\n          // '中板分厂': [12, 11, 13, 12.5],\r\n          // '厚板分厂': [11, 10, 12, 11.5],\r\n          // '钢材深加工': [10, 9, 11, 10.5],\r\n          // '热电': [5, 4, 6, 5.5],\r\n          // '供电工区': [3, 2, 4, 3.5],\r\n          // '水处理分厂': [2, 1, 3, 2.5],\r\n          // '制氧分厂': [1.5, 0.5, 2.5, 2],\r\n          // '煤气分厂': [1, 0.8, 1.2, 1.1],\r\n          // '储运公司': [8, 7.8, 8.2, 8.1],\r\n          // '检修分厂': [6, 5.8, 6.2, 6.1]\r\n        }\r\n      },\r\n      \r\n      // 关键能源指标当日值与昨日对比\r\n      keyEnergyIndicators: [],\r\n      // [\r\n      //   {\r\n      //     name: '氧气',\r\n      //     today: 46.80, // 超出目标范围上限\r\n      //     yesterday: 44.64,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 42.00,\r\n      //     targetMax: 45.00,\r\n      //     change: 4.84,\r\n      //     status: 'danger'\r\n      //   },\r\n      //   {\r\n      //     name: '氮气',\r\n      //     today: 14.82,\r\n      //     yesterday: 15.21,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 14.00,\r\n      //     targetMax: 16.00,\r\n      //     change: -2.56,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '氩气',\r\n      //     today: 0.85,\r\n      //     yesterday: 0.88,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 0.80,\r\n      //     targetMax: 0.90,\r\n      //     change: -3.41,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '空气',\r\n      //     today: 450, // 低于目标范围下限\r\n      //     yesterday: 481,\r\n      //     unit: 'm³/h',\r\n      //     targetMin: 460,\r\n      //     targetMax: 500,\r\n      //     change: -6.44,\r\n      //     status: 'danger'\r\n      //   },\r\n      //   {\r\n      //     name: '高炉煤气',\r\n      //     today: 985.3,\r\n      //     yesterday: 962.7,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 950.0,\r\n      //     targetMax: 1000.0,\r\n      //     change: 2.35,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '转炉煤气',\r\n      //     today: 85.2,\r\n      //     yesterday: 88.4,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 80.0,\r\n      //     targetMax: 90.0,\r\n      //     change: -3.62,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '焦炉煤气',\r\n      //     today: 41.3,\r\n      //     yesterday: 42.8,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 40.0,\r\n      //     targetMax: 45.0,\r\n      //     change: -3.50,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '饱和蒸汽',\r\n      //     today: 0.52,\r\n      //     yesterday: 0.54,\r\n      //     unit: 't/t',\r\n      //     targetMin: 0.50,\r\n      //     targetMax: 0.58,\r\n      //     change: -3.70,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '过热蒸汽',\r\n      //     today: 0.33,\r\n      //     yesterday: 0.31,\r\n      //     unit: 't/t',\r\n      //     targetMin: 0.30,\r\n      //     targetMax: 0.35,\r\n      //     change: 6.45,\r\n      //     status: 'warning'\r\n      //   },\r\n      //   {\r\n      //     name: '低压蒸汽',\r\n      //     today: 0.21,\r\n      //     yesterday: 0.23,\r\n      //     unit: 't/t',\r\n      //     targetMin: 0.20,\r\n      //     targetMax: 0.25,\r\n      //     change: -8.70,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '天然气',\r\n      //     today: 24.3,\r\n      //     yesterday: 25.1,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 22.0,\r\n      //     targetMax: 26.0,\r\n      //     change: -3.19,\r\n      //     status: 'good'\r\n      //   },\r\n      //   {\r\n      //     name: '压缩天然气',\r\n      //     today: 2.85,\r\n      //     yesterday: 2.91,\r\n      //     unit: 'm³/t',\r\n      //     targetMin: 2.70,\r\n      //     targetMax: 3.00,\r\n      //     change: -2.06,\r\n      //     status: 'good'\r\n      //   }\r\n      // ],\r\n      \r\n      // 能源指标是否超出目标范围标记\r\n      isEnergyChartWarning: {\r\n        electricity: false,\r\n        water: false,\r\n        gas: false,\r\n        steam: false\r\n      },\r\n      \r\n      // 能源目标范围\r\n      energyTargetRanges: {\r\n        electricity: { min: 800, max: 900, unit: '千瓦时' },\r\n        water: { min: 50, max: 60, unit: '吨' },\r\n        gas: { min: 200, max: 240, unit: '立方米' },\r\n        steam: { min: 25, max: 35, unit: '吨' }\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算所有部门的完成率数据（只显示分厂层级）\r\n    departmentCompletionRates() {\r\n      // 按部门分组计算完成率，只处理有\"-\"的分厂层级\r\n      const departmentRates = this.completionData\r\n        .filter(dept => dept.department.includes('-')) // 只选择分厂层级\r\n        .map(dept => {\r\n          const indicators = dept.indicators\r\n          let completedCount = 0\r\n          let totalIndicators = indicators.length\r\n          \r\n          // 计算已完成指标数量，使用4月份数据\r\n          indicators.forEach(indicator => {\r\n            // 使用4月份数据（第3个索引）\r\n            const actual = indicator.values[3] // 第四周/4月份的实际值\r\n            const target = indicator.target\r\n            \r\n            const isCompleted = indicator.isHigherBetter \r\n              ? actual >= target \r\n              : actual <= target\r\n            \r\n            if (isCompleted) completedCount++\r\n          })\r\n          \r\n          // 计算完成率\r\n          const completionRate = (completedCount / totalIndicators) * 100\r\n          \r\n          return {\r\n            department: dept.department.split('-')[1], // 只显示分厂名称\r\n            fullDepartment: dept.department, // 保存完整部门名称用于数据查询\r\n            completionRate: parseFloat(completionRate.toFixed(1)),\r\n            totalIndicators: totalIndicators,\r\n            completedIndicators: completedCount\r\n          }\r\n        })\r\n      \r\n      // 按完成率从高到低排序\r\n      return departmentRates.sort((a, b) => b.completionRate - a.completionRate)\r\n    },\r\n    \r\n    // 计算所有指标的完成率（4月份数据）\r\n    allIndicatorCompletionRates() {\r\n      const allIndicators = []\r\n      \r\n      this.completionData.forEach(dept => {\r\n        if (dept.department.includes('-')) { // 只选择分厂层级\r\n          const departmentName = dept.department.split('-')[1]\r\n          \r\n          dept.indicators.forEach(indicator => {\r\n            // 直接使用04月列的完成率数据\r\n            // 检查是否有最后一列（04月）的完成率数据\r\n            const completionRateStr = indicator.completionRates && indicator.completionRates[3]; // 04月完成率\r\n            let completionRate = 0;\r\n            \r\n            // 如果有直接的完成率数据，则使用它\r\n            if (completionRateStr) {\r\n              // 将百分比字符串转换为数值，例如 \"-1.31%\" -> -1.31\r\n              completionRate = parseFloat(completionRateStr.replace('%', ''));\r\n            } else {\r\n              // 如果没有直接数据，则用原来的方法计算\r\n              const actual = indicator.values[3]; // 4月份实际值\r\n              const target = indicator.target;\r\n              \r\n              if (indicator.isHigherBetter) {\r\n                // 如果指标越高越好，完成率 = 实际值/目标值 * 100% - 100%\r\n                completionRate = ((actual / target) * 100) - 100;\r\n              } else {\r\n                // 如果指标越低越好，完成率 = 目标值/实际值 * 100% - 100%\r\n                completionRate = ((target / actual) * 100) - 100;\r\n              }\r\n            }\r\n            \r\n            // 对完成率进行处理\r\n            // 非数字完成率调整为0\r\n            if (isNaN(completionRate) || !isFinite(completionRate)) {\r\n              completionRate = 0;\r\n            }\r\n            \r\n            // 限制最大完成率绝对值为200%\r\n            if (Math.abs(completionRate) > 200) {\r\n              completionRate = completionRate > 0 ? 200 : -200;\r\n            }\r\n            \r\n            allIndicators.push({\r\n              department: departmentName,\r\n              indicator: indicator.name,\r\n              completionRate: parseFloat(completionRate.toFixed(1)),\r\n              target: indicator.target,\r\n              actual: indicator.values[3], // 4月份实际值\r\n              unit: indicator.unit,\r\n              isHigherBetter: indicator.isHigherBetter\r\n            });\r\n          });\r\n        }\r\n      });\r\n      \r\n      // 按完成率从高到低排序\r\n      return allIndicators.sort((a, b) => b.completionRate - a.completionRate);\r\n    },\r\n    \r\n    // 直接使用所有指标完成率数据\r\n    paginatedIndicatorRates() {\r\n      return this.allIndicatorCompletionRates\r\n    },\r\n    \r\n    // 获取事业部列表（用于筛选）\r\n    businessUnits() {\r\n      const units = new Set()\r\n      this.completionData.forEach(dept => {\r\n        if (dept.department.includes('-')) {\r\n          units.add(dept.department.split('-')[0])\r\n        } else {\r\n          units.add(dept.department)\r\n        }\r\n      })\r\n      return Array.from(units)\r\n    },\r\n    \r\n    // 获取当前选择的事业部下的分厂列表\r\n    currentBusinessUnitDepartments() {\r\n      if (!this.currentBusinessUnit) return []\r\n      \r\n      return this.completionData\r\n        .filter(dept => dept.department.startsWith(this.currentBusinessUnit + '-'))\r\n        .map(dept => ({\r\n          value: dept.department,\r\n          label: dept.department.split('-')[1]\r\n        }))\r\n    },\r\n    \r\n    // 获取当前部门的指标列表\r\n    currentDepartmentIndicators() {\r\n      const deptData = this.completionData.find(dept => dept.department === this.currentDepartment)\r\n      return deptData ? deptData.indicators : []\r\n    },\r\n    \r\n    // 获取所有分厂列表（用于能源消耗详情）\r\n    allFactories() {\r\n      return this.completionData1.map(dept => dept.department)\r\n    }\r\n  },\r\n  watch: {\r\n    // // 监听事业部变化\r\n    // currentBusinessUnit(newVal) {\r\n    //   if (this.currentBusinessUnitDepartments.length > 0) {\r\n    //     this.currentDepartment = this.currentBusinessUnitDepartments[0].value\r\n    //   }\r\n      \r\n    //   // 启动自动切换\r\n    //   this.startAutoSwitchWithinBusinessUnit()\r\n    // },\r\n    \r\n    // // 监听部门变化，自动选择第一个指标\r\n    // currentDepartment(newVal) {\r\n    //   if (this.currentDepartmentIndicators.length > 0) {\r\n    //     this.currentIndicator = this.currentDepartmentIndicators[0].name\r\n    //   }\r\n    //   this.initMonthlyTrendChart()\r\n    // },\r\n    \r\n    // // 监听指标变化，更新图表\r\n    // currentIndicator() {\r\n    //   this.initMonthlyTrendChart()\r\n    // }\r\n  },\r\n  mounted() {\r\n    // 初始化图表\r\n    this.initCharts()\r\n    \r\n    // 计算未完成指标数据\r\n    this.calculateIncompleteData()\r\n    \r\n    // 确保currentEnergyDept有一个有效的初始值\r\n    this.$nextTick(() => {\r\n      this.initEnergyDetailCharts()\r\n      if (this.allFactories && this.allFactories.length > 0) {\r\n        this.currentEnergyDept = this.allFactories[0]\r\n        // 重新初始化能源详情图表\r\n        this.initEnergyDetailCharts()\r\n      }\r\n    })\r\n    this.loadExcelFromRemote();\r\n    // 启动滚动表格\r\n    this.$nextTick(() => {\r\n      // 复制表格内容以实现无缝滚动\r\n      const table = this.$refs.incompleteTable\r\n      if (table && this.incompleteData.length > 0) {\r\n        // 获取表格内容部分（不包含表头）\r\n        const tableBody = table.querySelector('.el-table__body-wrapper')\r\n        if (tableBody) {\r\n          const originalContent = tableBody.innerHTML\r\n          // 在表格内容后面添加一份相同的内容，而不是整个表格\r\n          tableBody.innerHTML += originalContent\r\n        }\r\n      }\r\n      this.startTableScroll()\r\n    })\r\n    \r\n    // 启动事业部内部自动切换\r\n    this.startAutoSwitchWithinBusinessUnit()\r\n    \r\n    // 启动事业部切换（每30秒切换一次）\r\n    this.startBusinessUnitSwitch()\r\n    \r\n    // 启动指标卡片自动滚动\r\n    this.$nextTick(() => {\r\n      this.initIndicatorCardsScroll()\r\n      \r\n      // 为指标卡片容器添加鼠标悬停事件处理\r\n      const cardsContainer = this.$refs.indicatorCardsContainer\r\n      if (cardsContainer) {\r\n        cardsContainer.addEventListener('mouseover', () => {\r\n          this.indicatorCardsScrollPaused = true\r\n        })\r\n        \r\n        cardsContainer.addEventListener('mouseout', () => {\r\n          this.indicatorCardsScrollPaused = false\r\n        })\r\n      }\r\n    })\r\n    \r\n    // 监听窗口大小变化，重新渲染图表\r\n    window.addEventListener('resize', this.resizeCharts)\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    this.stopAutoSwitch()\r\n    \r\n    if (this.scrollTimer) {\r\n      clearInterval(this.scrollTimer)\r\n      \r\n      // 移除表格滚动的事件监听器\r\n      const table = this.$refs.incompleteTable\r\n      if (table) {\r\n        const tableBody = table.querySelector('.el-table__body-wrapper')\r\n        if (tableBody && this.tableMouseEnterHandler && this.tableMouseLeaveHandler) {\r\n          tableBody.removeEventListener('mouseenter', this.tableMouseEnterHandler);\r\n          tableBody.removeEventListener('mouseleave', this.tableMouseLeaveHandler);\r\n        }\r\n      }\r\n    }\r\n    \r\n    if (this.businessUnitTimer) {\r\n      clearInterval(this.businessUnitTimer)\r\n    }\r\n    \r\n    if (this.energyDeptTimer) {\r\n      clearInterval(this.energyDeptTimer)\r\n    }\r\n    \r\n    if (this.completionChartTimer) {\r\n      clearInterval(this.completionChartTimer)\r\n    }\r\n    \r\n    if (this.indicatorCardsScrollTimer) {\r\n      clearInterval(this.indicatorCardsScrollTimer)\r\n    }\r\n    \r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n    \r\n    // 移除指标卡片容器的事件监听器\r\n    const cardsContainer = this.$refs.indicatorCardsContainer\r\n    if (cardsContainer) {\r\n      cardsContainer.removeEventListener('mouseover', () => {\r\n        this.indicatorCardsScrollPaused = true\r\n      })\r\n      \r\n      cardsContainer.removeEventListener('mouseout', () => {\r\n        this.indicatorCardsScrollPaused = false\r\n      })\r\n    }\r\n    \r\n    // 销毁图表实例\r\n    this.disposeCharts()\r\n  },\r\n  created() {\r\n      this.getDateUpdateList()\r\n  },\r\n  methods: {\r\n    // 初始化所有图表\r\n\r\n    loadExcelFromRemote() {\r\n      this.excelLoading = true;\r\n      const url =\r\n        \"https://ydxt.citicsteel.com:8099/minio/xctg/temp/jm关键技径指标.xlsx\";\r\n      axios({\r\n        method: \"get\",\r\n        url,\r\n        responseType: \"arraybuffer\",\r\n      })\r\n        .then((response) => {\r\n          const data = new Uint8Array(response.data);\r\n          const workbook = XLSX.read(data, { type: \"array\" });\r\n          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];\r\n          const jsonData = XLSX.utils.sheet_to_json(firstSheet, {\r\n            header: 1,\r\n            range: 1,\r\n          });\r\n          console.log(jsonData);\r\n          this.processExcelData(jsonData);\r\n          this.excelLoading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"加载Excel文件失败:\", error);\r\n          this.$message.error(\"加载Excel文件失败，请稍后重试\");\r\n          this.excelLoading = false;\r\n        });\r\n    },\r\n    getDetail(){\r\n      dimensionalitylistPermissionList().then(res => {\r\n        this.adminShow = res.msg;\r\n      });\r\n    },\r\n    getDateUpdateList(){\r\n      dateUpdateList().then(res => {\r\n        this.keyEnergyIndicators = res.data;\r\n      });\r\n    },\r\n\r\n    \r\n    processExcelData(jsonData) {\r\n      if (!jsonData || jsonData.length < 2) return;\r\n      const headers = jsonData[0];\r\n      const colIndexes = {\r\n        factory: headers.indexOf(\"分厂\"),\r\n        name: headers.indexOf(\"指标名称\"),\r\n        target: headers.indexOf(\"目标\"),\r\n        unit: headers.indexOf(\"单位\"),\r\n        jan: headers.indexOf(\"01月实绩\"),\r\n        janStatus: headers.indexOf(\"01月实绩\") + 1,\r\n        feb: headers.indexOf(\"02月实绩\"),\r\n        febStatus: headers.indexOf(\"02月实绩\") + 1,\r\n        mar: headers.indexOf(\"03月实绩\"),\r\n        marStatus: headers.indexOf(\"03月实绩\") + 1,\r\n        apr: headers.indexOf(\"04月实绩\"),\r\n        aprStatus: headers.indexOf(\"04月实绩\") + 1,\r\n        may: headers.indexOf(\"05月实绩\"),\r\n        mayStatus: headers.indexOf(\"05月实绩\") + 1,\r\n      };\r\n\r\n      const dataRows = jsonData.slice(1);\r\n      const techIndicators = dataRows\r\n        .map((row) => {\r\n          if (!row || row.length === 0) return null;\r\n          if (\r\n            row[colIndexes.factory] === undefined ||\r\n            row[colIndexes.name] === undefined\r\n          )\r\n            return null;\r\n\r\n          const indicator = {\r\n            factory: row[colIndexes.factory] || \"\",\r\n            name: row[colIndexes.name] || \"\",\r\n            target:\r\n              row[colIndexes.target] !== undefined\r\n                ? String(row[colIndexes.target])\r\n                : \"\",\r\n            unit: row[colIndexes.unit] || \"\",\r\n            jan:\r\n              row[colIndexes.jan] !== undefined\r\n                ? String(row[colIndexes.jan])\r\n                : \"\",\r\n            janStatus: row[colIndexes.janStatus] === 1.0 ? 1 : 0,\r\n            feb:\r\n              row[colIndexes.feb] !== undefined\r\n                ? String(row[colIndexes.feb])\r\n                : \"\",\r\n            febStatus: row[colIndexes.febStatus] === 1.0 ? 1 : 0,\r\n            mar:\r\n              row[colIndexes.mar] !== undefined\r\n                ? String(row[colIndexes.mar])\r\n                : \"\",\r\n            marStatus: row[colIndexes.marStatus] === 1.0 ? 1 : 0,\r\n            apr:\r\n              row[colIndexes.apr] !== undefined\r\n                ? String(row[colIndexes.apr])\r\n                : \"\",\r\n            aprStatus: row[colIndexes.aprStatus] === 1.0 ? 1 : 0,\r\n            may:\r\n              row[colIndexes.may] !== undefined\r\n                ? String(row[colIndexes.may])\r\n                : \"\",\r\n            mayStatus: row[colIndexes.mayStatus] === 1.0 ? 1 : 0,\r\n          };\r\n\r\n          indicator.monthlyData = [\r\n            parseFloat(indicator.jan) || 0,\r\n            parseFloat(indicator.feb) || 0,\r\n            parseFloat(indicator.mar) || 0,\r\n            parseFloat(indicator.apr) || 0,\r\n            parseFloat(indicator.may) || 0,\r\n          ];\r\n\r\n          return indicator;\r\n        })\r\n        .filter(Boolean);\r\n\r\n      // 处理相同分厂和指标名称的数据，确保数据一致性\r\n      const uniqueKeys = new Map();\r\n      const uniqueIndicators = [];\r\n\r\n      // 先处理相同指标的合并\r\n      techIndicators.forEach((indicator) => {\r\n        const key = `${indicator.factory}_${indicator.name}_${indicator.unit}`;\r\n        if (!uniqueKeys.has(key)) {\r\n          uniqueKeys.set(key, uniqueIndicators.length);\r\n          uniqueIndicators.push(indicator);\r\n        }\r\n      });\r\n\r\n      this.techIndicators = uniqueIndicators;\r\n    },\r\n    \r\n    initCharts() {\r\n      this.$nextTick(() => {\r\n        // 初始化部门完成率柱状图\r\n        this.initDepartmentCompletionChart()\r\n        \r\n        // 设置默认部门和指标\r\n        if (this.currentBusinessUnitDepartments.length > 0 && !this.currentDepartment) {\r\n          this.currentDepartment = this.currentBusinessUnitDepartments[0].value\r\n        }\r\n        \r\n        if (this.currentDepartmentIndicators.length > 0 && !this.currentIndicator) {\r\n          this.currentIndicator = this.currentDepartmentIndicators[0].name\r\n        }\r\n        \r\n        // 初始化月度趋势折线图\r\n        this.initMonthlyTrendChart()\r\n        \r\n        // 设置能源部门默认值并初始化能源子图表\r\n        if (this.allFactories && this.allFactories.length > 0) {\r\n          if (!this.currentEnergyDept) {\r\n            this.currentEnergyDept = this.allFactories[0]\r\n          }\r\n          // 初始化能源子图表\r\n          this.initEnergyDetailCharts()\r\n        }\r\n        \r\n        // 启动事业部内部自动切换\r\n        this.startAutoSwitchWithinBusinessUnit()\r\n        \r\n        // 启动事业部切换（每30秒切换一次）\r\n        this.startBusinessUnitSwitch()\r\n        \r\n        // 启动能源部门自动切换\r\n        this.startEnergyDeptSwitch()\r\n      })\r\n    },\r\n    \r\n    // 启动事业部内部自动切换\r\n    startAutoSwitchWithinBusinessUnit() {\r\n      // 停止之前的自动切换\r\n      this.stopAutoSwitch()\r\n      \r\n      // 启动新的自动切换\r\n      this.trendChartTimer = setInterval(() => {\r\n        this.switchDepartmentWithinBusinessUnit()\r\n      }, 3000) // 分厂切换频率为3秒\r\n    },\r\n    \r\n    // 在当前事业部内切换部门\r\n    switchDepartmentWithinBusinessUnit() {\r\n      const departments = this.currentBusinessUnitDepartments\r\n      if (departments.length <= 1) return\r\n      \r\n      const currentIndex = departments.findIndex(dept => dept.value === this.currentDepartment)\r\n      const nextIndex = (currentIndex + 1) % departments.length\r\n      this.currentDepartment = departments[nextIndex].value\r\n    },\r\n    \r\n    // 处理自动切换开关变化\r\n    handleAutoSwitchChange(value) {\r\n      if (value) {\r\n        // 启动自动切换\r\n        this.startAutoSwitchWithinBusinessUnit()\r\n      } else {\r\n        // 停止自动切换\r\n        this.stopAutoSwitch()\r\n      }\r\n    },\r\n    \r\n    // 启动自动切换\r\n    startAutoSwitch() {\r\n      if (this.trendChartTimer) {\r\n        clearInterval(this.trendChartTimer)\r\n      }\r\n      \r\n      this.trendChartTimer = setInterval(() => {\r\n        this.switchDepartment()\r\n      }, 3000)\r\n    },\r\n    \r\n    // 停止自动切换\r\n    stopAutoSwitch() {\r\n      if (this.trendChartTimer) {\r\n        clearInterval(this.trendChartTimer)\r\n        this.trendChartTimer = null\r\n      }\r\n    },\r\n    \r\n    // 切换完成率图表页码\r\n    changePage(page) {\r\n      this.currentPage = page\r\n      this.initDepartmentCompletionChart()\r\n    },\r\n    \r\n    // 计算未完成指标数据，筛选负数完成率\r\n    calculateIncompleteData() {\r\n      const incomplete = [];\r\n      \r\n      this.completionData.forEach(dept => {\r\n        dept.indicators.forEach(indicator => {\r\n          // 直接使用04月列的完成率数据\r\n          // 检查是否有最后一列（04月）的完成率数据\r\n          const completionRateStr = indicator.completionRates && indicator.completionRates[3]; // 04月完成率\r\n          let completionRate = 0;\r\n          \r\n          // 如果有直接的完成率数据，则使用它\r\n          if (completionRateStr) {\r\n            // 将百分比字符串转换为数值，例如 \"-1.31%\" -> -1.31\r\n            completionRate = parseFloat(completionRateStr.replace('%', ''));\r\n          } else {\r\n            // 如果没有直接数据，则用原来的方法计算\r\n            const actual = indicator.values[3]; // 4月份实际值\r\n            const target = indicator.target;\r\n            \r\n            if (indicator.isHigherBetter) {\r\n              // 如果指标越高越好，完成率 = 实际值/目标值 * 100% - 100%\r\n              completionRate = ((actual / target) * 100) - 100;\r\n            } else {\r\n              // 如果指标越低越好，完成率 = 目标值/实际值 * 100% - 100%\r\n              completionRate = ((target / actual) * 100) - 100;\r\n            }\r\n          }\r\n          \r\n          // 只添加完成率为负数的记录\r\n          if (completionRate < 0) {\r\n            incomplete.push({\r\n              department: dept.department.includes('-') ? dept.department.split('-')[1] : dept.department,\r\n              indicator: indicator.name,\r\n              target: indicator.target,\r\n              actual: indicator.values[3], // 4月份实际值\r\n              unit: indicator.unit,\r\n              completionRate: parseFloat(completionRate.toFixed(1))\r\n            });\r\n          }\r\n        });\r\n      });\r\n      \r\n      // 按完成率从低到高排序\r\n      this.incompleteData = incomplete.sort((a, b) => a.completionRate - b.completionRate);\r\n    },\r\n    \r\n    // 启动表格滚动\r\n    startTableScroll() {\r\n      if (this.incompleteData.length > 0) {\r\n        const table = this.$refs.incompleteTable\r\n        \r\n        // 复制表格内容以实现无缝滚动\r\n        if (table) {\r\n          // 清除之前的滚动定时器\r\n          if (this.scrollTimer) {\r\n            clearInterval(this.scrollTimer)\r\n          }\r\n          \r\n          // 获取表格内容部分（不包含表头）\r\n          const tableBody = table.querySelector('.el-table__body-wrapper')\r\n          if (tableBody) {\r\n            // 创建新的滚动定时器\r\n            let scrollTop = 0\r\n            this.scrollTimer = setInterval(() => {\r\n              // 如果暂停滚动，则不执行滚动操作\r\n              if (this.tableScrollPaused) return;\r\n              \r\n              scrollTop++\r\n              if (scrollTop >= tableBody.scrollHeight / 2) {\r\n                scrollTop = 0\r\n              }\r\n              tableBody.scrollTop = scrollTop\r\n            }, this.scrollSpeed)\r\n            \r\n            // 创建鼠标事件处理函数\r\n            this.tableMouseEnterHandler = () => {\r\n              this.tableScrollPaused = true;\r\n            };\r\n            \r\n            this.tableMouseLeaveHandler = () => {\r\n              this.tableScrollPaused = false;\r\n            };\r\n            \r\n            // 添加鼠标悬停事件处理\r\n            tableBody.addEventListener('mouseenter', this.tableMouseEnterHandler);\r\n            tableBody.addEventListener('mouseleave', this.tableMouseLeaveHandler);\r\n          }\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 切换部门趋势图\r\n    switchDepartment() {\r\n      const currentIndex = this.departments.indexOf(this.currentDepartment)\r\n      const nextIndex = (currentIndex + 1) % this.departments.length\r\n      this.currentDepartment = this.departments[nextIndex]\r\n    },\r\n    \r\n    // 初始化部门完成率柱状图 - 修改为横向柱状图\r\n    initDepartmentCompletionChart() {\r\n      const chartDom = document.getElementById('departmentCompletionChart')\r\n      const chart = echarts.init(chartDom)\r\n      \r\n      // 计算默认显示的百分比\r\n      const defaultDisplayPercent = Math.min(6 / this.allIndicatorCompletionRates.length * 100, 100)\r\n      \r\n      // 使用指标级别的完成率数据\r\n      const labels = this.paginatedIndicatorRates.map(item => {\r\n        return {\r\n          deptName: item.department,\r\n          indicatorName: item.indicator,\r\n          fullText: `${item.department}\\n${item.indicator}`\r\n        }\r\n      })\r\n      const rates = this.paginatedIndicatorRates.map(item => item.completionRate)\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const data = params[0]\r\n            const item = this.paginatedIndicatorRates[data.dataIndex]\r\n            const actualText = item.isHigherBetter \r\n              ? `实际值: ${item.actual}${item.unit}`\r\n              : `实际值: ${item.actual}${item.unit} `\r\n            return `${item.department}-${item.indicator}<br/>\r\n                    目标值: ${item.target}${item.unit}<br/>\r\n                    ${actualText}<br/>\r\n                    完成率: ${item.completionRate}%`\r\n          }.bind(this)\r\n        },\r\n        grid: {\r\n          left: '0', // 左移30px\r\n          right: '0',\r\n          bottom: '3%',\r\n          top: '3%',\r\n          containLabel: true\r\n        },\r\n        dataZoom: [\r\n          {\r\n            type: 'slider',\r\n            show: true,\r\n            yAxisIndex: [0],\r\n            start: 0,\r\n            end: defaultDisplayPercent,\r\n            width: 10,\r\n            handleSize: 20,\r\n            showDetail: false,\r\n            zoomLock: false,\r\n            moveOnMouseWheel: true,\r\n            preventDefaultMouseMove: true\r\n          },\r\n          {\r\n            type: 'inside',\r\n            yAxisIndex: [0],\r\n            start: 0,\r\n            end: defaultDisplayPercent,\r\n            zoomLock: false\r\n          }\r\n        ],\r\n        toolbox: {\r\n          feature: {\r\n            dataZoom: {\r\n              yAxisIndex: 'none'\r\n            },\r\n            restore: {},\r\n            saveAsImage: {}\r\n          },\r\n          right: 10,\r\n          top: 0\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '完成率(%)',\r\n          min: -30, // 调整最小值为-30%，足够显示负数完成率\r\n          max: 30, // 调整最大值为30%，使图表更聚焦\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              type: 'dashed',\r\n              color: '#E0E6F1'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: labels.map(item => item.fullText),\r\n          axisLabel: {\r\n            formatter: function(value) {\r\n              return value;\r\n            },\r\n            color: '#333',\r\n            lineHeight: 16,\r\n            margin: 12,\r\n            rich: {\r\n              dept: {\r\n                fontWeight: 'bold',\r\n                lineHeight: 20\r\n              },\r\n              indicator: {\r\n                lineHeight: 20\r\n              }\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#5470c6'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '完成率',\r\n            type: 'bar',\r\n            data: rates,\r\n            itemStyle: {\r\n              color: function(params) {\r\n                // 根据完成率设置不同颜色\r\n                if (params.data >= 100) {\r\n                  return '#5470c6' // 蓝色，超过100%\r\n                } else if (params.data >= 90) {\r\n                  return '#91cc75'  // 绿色，完成率高\r\n                } else if (params.data >= 70) {\r\n                  return '#fac858'  // 黄色，完成率中等\r\n                } else if (params.data >= 0) {\r\n                  return '#ee6666'  // 红色，完成率低\r\n                } else {\r\n                  return '#ff5252'  // 更深的红色，负数完成率\r\n                }\r\n              }\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'right',\r\n              formatter: '{c}%',\r\n              color: function(params) {\r\n                // 负数完成率用白色文字，更加明显\r\n                return params.data < 0 ? '#ffffff' : '#333333';\r\n              },\r\n              fontWeight: 'bold',\r\n              distance: 15\r\n            },\r\n            barWidth: '50%', // 柱子更细\r\n            barCategoryGap: '40%', // 增加柱子间距\r\n            animationDelay: function(idx) {\r\n              return idx * 100 + 100\r\n            }\r\n          }\r\n        ],\r\n        animationEasing: 'elasticOut',\r\n        animationDelayUpdate: function(idx) {\r\n          return idx * 5\r\n        }\r\n      }\r\n      \r\n      chart.setOption(option)\r\n      \r\n      // 保存dataZoom状态，防止滚动条回弹\r\n      chart.on('datazoom', function(params) {\r\n        const { start, end } = params\r\n        option.dataZoom[0].start = start\r\n        option.dataZoom[0].end = end\r\n        option.dataZoom[1].start = start\r\n        option.dataZoom[1].end = end\r\n        chart.setOption(option)\r\n      })\r\n      \r\n      // 创建事件处理器\r\n      this.chartMouseOverHandler = () => {\r\n        this.completionChartScrollPaused = true\r\n      }\r\n      \r\n      this.chartMouseOutHandler = () => {\r\n        this.completionChartScrollPaused = false\r\n      }\r\n      \r\n      // 添加鼠标悬停事件，暂停自动滚动\r\n      chartDom.addEventListener('mouseover', this.chartMouseOverHandler)\r\n      \r\n      // 添加鼠标离开事件，恢复自动滚动\r\n      chartDom.addEventListener('mouseout', this.chartMouseOutHandler)\r\n      \r\n      // 窗口大小变化时自动调整图表大小\r\n      window.addEventListener('resize', function() {\r\n        chart.resize()\r\n      })\r\n      \r\n      // 保存图表实例以便后续使用\r\n      this.completionChart = chart\r\n    },\r\n    \r\n    // 启动完成率图表自动滚动\r\n    startCompletionChartScroll() {\r\n      if (this.completionChartTimer) {\r\n        clearInterval(this.completionChartTimer)\r\n      }\r\n      \r\n      this.completionChartTimer = setInterval(() => {\r\n        if (this.completionChartScrollPaused) return\r\n        \r\n        if (!this.completionChart) return\r\n        \r\n        const option = this.completionChart.getOption()\r\n        let start = option.dataZoom[0].start\r\n        let end = option.dataZoom[0].end\r\n        const step = 0.1 // 每次滚动的百分比，改为0.1，使滚动非常缓慢\r\n        const range = end - start // 当前显示的范围\r\n        \r\n        // 根据滚动方向调整滚动位置\r\n        if (this.completionChartScrollDirection === 'down') {\r\n          // 向下滚动\r\n          if (end < 100) {\r\n            start += step\r\n            end += step\r\n          } else {\r\n            // 已到底部，改变方向\r\n            this.completionChartScrollDirection = 'up'\r\n          }\r\n        } else {\r\n          // 向上滚动\r\n          if (start > 0) {\r\n            start -= step\r\n            end -= step\r\n          } else {\r\n            // 已到顶部，改变方向\r\n            this.completionChartScrollDirection = 'down'\r\n          }\r\n        }\r\n        \r\n        // 更新滚动位置\r\n        option.dataZoom[0].start = start\r\n        option.dataZoom[0].end = end\r\n        option.dataZoom[1].start = start\r\n        option.dataZoom[1].end = end\r\n        this.completionChart.setOption(option)\r\n      }, 300) // 滚动间隔改为300毫秒，进一步减慢滚动速度\r\n    },\r\n    \r\n    // 初始化月度趋势折线图 - 显示单条折线\r\n    initMonthlyTrendChart() {\r\n      const chartDom = document.getElementById('monthlyTrendChart')\r\n      const chart = echarts.init(chartDom)\r\n      \r\n      // 查找当前部门的数据\r\n      const deptData = this.completionData.find(dept => dept.department === this.currentDepartment || dept.department.startsWith(this.currentDepartment))\r\n      \r\n      if (!deptData || !this.currentIndicator) {\r\n        return\r\n      }\r\n      \r\n      // 查找当前指标\r\n      const indicator = deptData.indicators.find(ind => ind.name === this.currentIndicator)\r\n      \r\n      if (!indicator) {\r\n        return\r\n      }\r\n      \r\n      // 准备月份数据\r\n      const months = ['1月', '2月', '3月', '4月']\r\n      \r\n      // 计算数据范围，以便设置y轴范围使折线居中\r\n      const values = indicator.values\r\n      const min = Math.min(...values)\r\n      const max = Math.max(...values)\r\n      const range = max - min\r\n      \r\n      // 设置y轴范围，扩大20%的范围使波动看起来更明显\r\n      const yMin = min - range * 0.4\r\n      const yMax = max + range * 0.4\r\n      \r\n      const option = {\r\n        title: {\r\n          text: `${this.currentDepartment} - ${this.currentIndicator}`,\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 16\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          formatter: function(params) {\r\n            return `${params[0].name}<br/>${indicator.name}: ${params[0].value} ${indicator.unit}<br/>目标值: ${indicator.target} ${indicator.unit}`\r\n          },\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        grid: {\r\n          left: '5%',\r\n          right: '5%',\r\n          bottom: '10%',\r\n          top: '60px',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: months,\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: indicator.unit,\r\n          min: yMin,\r\n          max: yMax,\r\n          axisLabel: {\r\n            formatter: function(value) {\r\n              return value.toFixed(2);\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              type: 'dashed'\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: indicator.name,\r\n            type: 'line',\r\n            data: indicator.values,\r\n            smooth: true, // 平滑曲线\r\n            symbol: 'emptyCircle',\r\n            symbolSize: 10,\r\n            emphasis: {\r\n              itemStyle: {\r\n                shadowBlur: 10,\r\n                shadowColor: 'rgba(64, 158, 255, 0.5)'\r\n              },\r\n              scale: true\r\n            },\r\n            itemStyle: {\r\n              color: '#409EFF',\r\n              borderWidth: 2\r\n            },\r\n            lineStyle: {\r\n              width: 4,\r\n              shadowColor: 'rgba(0, 0, 0, 0.3)',\r\n              shadowBlur: 10,\r\n              shadowOffsetY: 5\r\n            },\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                {\r\n                  offset: 0,\r\n                  color: 'rgba(64, 158, 255, 0.7)'\r\n                },\r\n                {\r\n                  offset: 0.5,\r\n                  color: 'rgba(64, 158, 255, 0.3)'\r\n                },\r\n                {\r\n                  offset: 1,\r\n                  color: 'rgba(64, 158, 255, 0.1)'\r\n                }\r\n              ])\r\n            },\r\n            markLine: {\r\n              silent: true,\r\n              lineStyle: {\r\n                color: '#F56C6C',\r\n                type: 'dashed',\r\n                width: 2\r\n              },\r\n              data: [\r\n                {\r\n                  yAxis: indicator.target,\r\n                  label: {\r\n                    formatter: `目标值: ${indicator.target}`,\r\n                    position: 'insideEndTop',\r\n                    fontSize: 12,\r\n                    backgroundColor: 'rgba(245, 108, 108, 0.2)',\r\n                    padding: [2, 4],\r\n                    borderRadius: 2\r\n                  }\r\n                }\r\n              ]\r\n            },\r\n            animationDuration: 2000,\r\n            animationEasing: 'elasticOut',\r\n            animationDelay: function (idx) {\r\n              return idx * 200;\r\n            }\r\n          },\r\n          {\r\n            name: '目标值',\r\n            type: 'line',\r\n            data: Array(months.length).fill(indicator.target),\r\n            lineStyle: {\r\n              color: '#F56C6C',\r\n              type: 'dashed',\r\n              width: 2\r\n            },\r\n            symbol: 'none'\r\n          }\r\n        ],\r\n        legend: {\r\n          data: [indicator.name, '目标值'],\r\n          bottom: '0%'\r\n        }\r\n      }\r\n      \r\n      chart.setOption(option)\r\n    },\r\n    \r\n    // 初始化能源消耗子图表\r\n    initEnergyDetailCharts() {\r\n      // 检查当前选择的部门数据是否有效\r\n      if (!this.currentEnergyDept || !this.factoryEnergyData) return;\r\n      \r\n      // 重置警告状态\r\n      for (let key in this.isEnergyChartWarning) {\r\n        this.isEnergyChartWarning[key] = false;\r\n      }\r\n      \r\n      // 为每种能源类型创建单独的图表\r\n      this.energyTypes.forEach(type => {\r\n        const chartDom = document.getElementById('energySubchart_' + type.value);\r\n        if (!chartDom) return;\r\n        \r\n        // 清除之前的实例\r\n        const existingChart = echarts.getInstanceByDom(chartDom);\r\n        if (existingChart) {\r\n          existingChart.dispose();\r\n        }\r\n        \r\n        const chart = echarts.init(chartDom);\r\n        \r\n        const data = this.factoryEnergyData[type.value][this.currentEnergyDept] || [];\r\n        const months = this.factoryEnergyData.months;\r\n        const targetRange = this.energyTargetRanges[type.value];\r\n        \r\n        // 计算数据的最小值和最大值，以便设置y轴的范围\r\n        const minValue = Math.min(...data);\r\n        const maxValue = Math.max(...data);\r\n        const valueRange = maxValue - minValue;\r\n        \r\n        // 设置y轴的最小值和最大值，以使折线居中并增加波动感\r\n        // 通过缩小y轴范围使折线显得更加曲折\r\n        const yMin = minValue - valueRange * 0.3; // 下方预留30%的空间\r\n        const yMax = maxValue + valueRange * 0.3; // 上方预留30%的空间\r\n        \r\n        const option = {\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            formatter: function(params) {\r\n              const value = params[0].value;\r\n              return `${params[0].name}<br/>${type.label}: ${value} ${targetRange.unit}`;\r\n            }\r\n          },\r\n          grid: {\r\n            left: '10%',\r\n            right: '5%',\r\n            bottom: '15%',\r\n            top: '15%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: months,\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#333333' // 黑色坐标轴\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#333333' // 黑色坐标轴文字\r\n            },\r\n            boundaryGap: false // 让曲线从坐标轴开始\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: targetRange.unit,\r\n            min: yMin, // 设置最小值使折线居中\r\n            max: yMax, // 设置最大值使折线居中\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#333333' // 黑色坐标轴\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#333333', // 黑色坐标轴文字\r\n              formatter: function(value) {\r\n                // 保留适当的小数位数以避免过度拥挤\r\n                if (value >= 100) {\r\n                  return value.toFixed(0);\r\n                } else {\r\n                  return value.toFixed(1);\r\n                }\r\n              }\r\n            },\r\n            splitLine: {\r\n              lineStyle: {\r\n                type: 'dashed',\r\n                color: '#E0E6F1'\r\n              }\r\n            }\r\n          },\r\n          series: [\r\n            {\r\n              name: type.label,\r\n              type: 'line',\r\n              data: data,\r\n              smooth: true, // 使用平滑曲线\r\n              smoothMonotone: 'none', // 不保持单调性，允许更多波动\r\n              symbol: 'circle',\r\n              symbolSize: 8,\r\n              sampling: 'average', // 使用平均采样\r\n              lineStyle: {\r\n                width: 4, // 加粗线条\r\n                color: type.color // 使用更深的颜色\r\n              },\r\n              itemStyle: {\r\n                color: type.color,\r\n                borderWidth: 2,\r\n                borderColor: '#fff',\r\n                shadowColor: 'rgba(0, 0, 0, 0.3)',\r\n                shadowBlur: 5\r\n              },\r\n              emphasis: {\r\n                itemStyle: {\r\n                  borderWidth: 3,\r\n                  shadowBlur: 10\r\n                },\r\n                lineStyle: {\r\n                  width: 6 // 鼠标悬停时线条更粗\r\n                }\r\n              },\r\n              areaStyle: {\r\n                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                  {\r\n                    offset: 0,\r\n                    color: this.hexToRgba(type.color, 0.6) // 更高的透明度使颜色更深\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: this.hexToRgba(type.color, 0.1)\r\n                  }\r\n                ])\r\n              }\r\n            }\r\n          ]\r\n        };\r\n        \r\n        chart.setOption(option);\r\n      });\r\n    },\r\n    \r\n    // 颜色转换辅助函数\r\n    hexToRgba(hex, alpha) {\r\n      const r = parseInt(hex.slice(1, 3), 16);\r\n      const g = parseInt(hex.slice(3, 5), 16);\r\n      const b = parseInt(hex.slice(5, 7), 16);\r\n      return `rgba(${r}, ${g}, ${b}, ${alpha})`;\r\n    },\r\n    \r\n    // 启动能源部门自动切换\r\n    startEnergyDeptSwitch() {\r\n      // 停止之前的能源部门切换\r\n      if (this.energyDeptTimer) {\r\n        clearInterval(this.energyDeptTimer)\r\n      }\r\n      \r\n      // 启动新的能源部门切换\r\n      this.energyDeptTimer = setInterval(() => {\r\n        this.switchEnergyDept()\r\n      }, 10000) // 每10秒切换一次部门，原来是5000\r\n    },\r\n    \r\n    // 切换能源部门\r\n    switchEnergyDept() {\r\n      const currentIndex = this.allFactories.indexOf(this.currentEnergyDept)\r\n      const nextIndex = (currentIndex + 1) % this.allFactories.length\r\n      this.currentEnergyDept = this.allFactories[nextIndex]\r\n      this.initEnergyDetailCharts()\r\n    },\r\n    \r\n    // 窗口大小变化时重新渲染图表\r\n    resizeCharts() {\r\n      const chartIds = [\r\n        'departmentCompletionChart',\r\n        'monthlyTrendChart'\r\n      ]\r\n      \r\n      chartIds.forEach(id => {\r\n        const chartDom = document.getElementById(id)\r\n        if (chartDom) {\r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.resize()\r\n          }\r\n        }\r\n      })\r\n      \r\n      // 调整能源子图表大小\r\n      this.energyTypes.forEach(type => {\r\n        const chartDom = document.getElementById('energySubchart_' + type.value)\r\n        if (chartDom) {\r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.resize()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 销毁图表实例\r\n    disposeCharts() {\r\n      const chartIds = [\r\n        'departmentCompletionChart',\r\n        'monthlyTrendChart'\r\n      ]\r\n      \r\n      chartIds.forEach(id => {\r\n        const chartDom = document.getElementById(id)\r\n        if (chartDom) {\r\n          // 移除事件监听器\r\n          if (id === 'departmentCompletionChart' && this.chartMouseOverHandler && this.chartMouseOutHandler) {\r\n            chartDom.removeEventListener('mouseover', this.chartMouseOverHandler)\r\n            chartDom.removeEventListener('mouseout', this.chartMouseOutHandler)\r\n          }\r\n          \r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.dispose()\r\n          }\r\n        }\r\n      })\r\n      \r\n      // 清除完成率图表引用\r\n      this.completionChart = null\r\n      \r\n      // 销毁能源子图表\r\n      this.energyTypes.forEach(type => {\r\n        const chartDom = document.getElementById('energySubchart_' + type.value)\r\n        if (chartDom) {\r\n          const chart = echarts.getInstanceByDom(chartDom)\r\n          if (chart) {\r\n            chart.dispose()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n\r\n    // 启动事业部切换\r\n    startBusinessUnitSwitch() {\r\n      // 停止之前的事业部切换\r\n      if (this.businessUnitTimer) {\r\n        clearInterval(this.businessUnitTimer)\r\n      }\r\n      \r\n      // 启动新的事业部切换\r\n      this.businessUnitTimer = setInterval(() => {\r\n        this.switchBusinessUnit()\r\n      }, 30000) // 事业部切换频率为30秒\r\n    },\r\n    \r\n    // 切换事业部\r\n    switchBusinessUnit() {\r\n      const currentIndex = this.departments.indexOf(this.currentBusinessUnit)\r\n      const nextIndex = (currentIndex + 1) % this.departments.length\r\n      this.currentBusinessUnit = this.departments[nextIndex]\r\n    },\r\n    \r\n    // 初始化关键能源指标对比图\r\n    initKeyEnergyIndicatorsChart() {\r\n      const chartDom = document.getElementById('keyEnergyIndicatorsChart')\r\n      const chart = echarts.init(chartDom)\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const dataIndex = params[0].dataIndex\r\n            const indicator = this.keyEnergyIndicators[dataIndex]\r\n            const changeText = indicator.change > 0 ? `+${indicator.change}%` : `${indicator.change}%`\r\n            const changeColor = indicator.status === 'good' ? '#67C23A' : indicator.status === 'warning' ? '#E6A23C' : '#F56C6C'\r\n            \r\n            return `\r\n              <div style=\"font-weight:bold\">${indicator.name}</div>\r\n              <div>今日: ${indicator.today} ${indicator.unit}</div>\r\n              <div>昨日: ${indicator.yesterday} ${indicator.unit}</div>\r\n              <div>变化: <span style=\"color:${changeColor}\">${changeText}</span></div>\r\n            `\r\n          }.bind(this)\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.keyEnergyIndicators.map(item => item.name)\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            formatter: function(value) {\r\n              if (value >= 100) {\r\n                return value.toFixed(0)\r\n              } else {\r\n                return value.toFixed(1)\r\n              }\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '今日值',\r\n            type: 'bar',\r\n            data: this.keyEnergyIndicators.map((item, index) => {\r\n              return {\r\n                value: item.today,\r\n                itemStyle: {\r\n                  color: item.status === 'good' ? '#67C23A' : item.status === 'warning' ? '#E6A23C' : '#F56C6C'\r\n                }\r\n              }\r\n            }),\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: function(params) {\r\n                const index = params.dataIndex\r\n                return this.keyEnergyIndicators[index].today\r\n              }.bind(this)\r\n            },\r\n            barWidth: '30%'\r\n          },\r\n          {\r\n            name: '昨日值',\r\n            type: 'bar',\r\n            data: this.keyEnergyIndicators.map(item => item.yesterday),\r\n            barWidth: '30%',\r\n            itemStyle: {\r\n              color: '#909399',\r\n              opacity: 0.5\r\n            }\r\n          }\r\n        ],\r\n        legend: {\r\n          data: ['今日值', '昨日值'],\r\n          bottom: 0\r\n        }\r\n      }\r\n      \r\n      chart.setOption(option)\r\n    },\r\n    \r\n    // 根据索引位置计算渐变颜色样式\r\n    getGradientStyle(index, total) {\r\n      // 计算该指标在整体中的相对位置（0-1之间）\r\n      const position = index / (total - 1);\r\n      \r\n      // 定义绿色到红色的渐变颜色数组\r\n      const colorStops = [\r\n        '#52c41a', // 亮绿色\r\n        '#85ce61', // 绿色\r\n        '#b3e19d', // 浅绿色\r\n        '#d4f8be', // 非常浅的绿色\r\n        '#faff72', // 黄色\r\n        '#fadb14', // 金黄色\r\n        '#ffa940', // 橙色\r\n        '#fa8c16', // 深橙色\r\n        '#ff7875', // 浅红色\r\n        '#ff4d4f', // 红色\r\n        '#f5222d', // 亮红色\r\n        '#cf1322'  // 深红色\r\n      ];\r\n      \r\n      // 根据位置在渐变色之间插值获取颜色\r\n      // 找到对应的颜色区间\r\n      const segmentCount = colorStops.length - 1;\r\n      const segment = Math.min(Math.floor(position * segmentCount), segmentCount - 1);\r\n      const localPosition = (position * segmentCount) - segment; // 在当前区间内的相对位置 (0-1)\r\n      \r\n      // 获取区间的起止颜色\r\n      const startColor = colorStops[segment];\r\n      const endColor = colorStops[segment + 1];\r\n      \r\n      // 在两个颜色之间插值\r\n      const bgColor = this.interpolateColors(startColor, endColor, localPosition);\r\n      \r\n      // 计算文字颜色\r\n      // 提取RGB并计算亮度\r\n      let r = parseInt(bgColor.slice(1, 3), 16);\r\n      let g = parseInt(bgColor.slice(3, 5), 16);\r\n      let b = parseInt(bgColor.slice(5, 7), 16);\r\n      const brightness = (r * 299 + g * 587 + b * 114) / 1000;\r\n      \r\n      // 亮度大于140使用深色文字，否则使用浅色文字\r\n      let textColor = brightness > 140 ? '#333333' : '#ffffff';\r\n      \r\n      // 设置边框颜色（比背景色稍深）\r\n      const borderColor = this.adjustColor(bgColor, -20);\r\n      \r\n      return {\r\n        backgroundColor: bgColor,\r\n        color: textColor,\r\n        borderTopColor: borderColor\r\n      };\r\n    },\r\n    \r\n    // 颜色插值函数\r\n    interpolateColors(color1, color2, factor) {\r\n      // 解析颜色\r\n      let r1 = parseInt(color1.slice(1, 3), 16);\r\n      let g1 = parseInt(color1.slice(3, 5), 16);\r\n      let b1 = parseInt(color1.slice(5, 7), 16);\r\n      \r\n      let r2 = parseInt(color2.slice(1, 3), 16);\r\n      let g2 = parseInt(color2.slice(3, 5), 16);\r\n      let b2 = parseInt(color2.slice(5, 7), 16);\r\n      \r\n      // 线性插值\r\n      let r = Math.round(r1 + factor * (r2 - r1));\r\n      let g = Math.round(g1 + factor * (g2 - g1));\r\n      let b = Math.round(b1 + factor * (b2 - b1));\r\n      \r\n      // 转回十六进制\r\n      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;\r\n    },\r\n    \r\n    // 获取徽章样式\r\n    getBadgeStyle(index, total) {\r\n      // 获取当前卡片的背景色\r\n      const position = index / (total - 1);\r\n      const colorStops = [\r\n        '#52c41a', // 亮绿色\r\n        '#85ce61', // 绿色\r\n        '#b3e19d', // 浅绿色\r\n        '#d4f8be', // 非常浅的绿色\r\n        '#faff72', // 黄色\r\n        '#fadb14', // 金黄色\r\n        '#ffa940', // 橙色\r\n        '#fa8c16', // 深橙色\r\n        '#ff7875', // 浅红色\r\n        '#ff4d4f', // 红色\r\n        '#f5222d', // 亮红色\r\n        '#cf1322'  // 深红色\r\n      ];\r\n      \r\n      const segmentCount = colorStops.length - 1;\r\n      const segment = Math.min(Math.floor(position * segmentCount), segmentCount - 1);\r\n      const localPosition = (position * segmentCount) - segment;\r\n      \r\n      const startColor = colorStops[segment];\r\n      const endColor = colorStops[segment + 1];\r\n      \r\n      const bgColor = this.interpolateColors(startColor, endColor, localPosition);\r\n      \r\n      // 计算文字颜色\r\n      let r = parseInt(bgColor.slice(1, 3), 16);\r\n      let g = parseInt(bgColor.slice(3, 5), 16);\r\n      let b = parseInt(bgColor.slice(5, 7), 16);\r\n      const brightness = (r * 299 + g * 587 + b * 114) / 1000;\r\n      \r\n      // 根据背景亮度设置徽章样式\r\n      if (brightness > 140) {\r\n        // 浅色背景使用深色边框的徽章\r\n        const badgeBgColor = this.adjustColor(bgColor, -40);\r\n        return {\r\n          backgroundColor: badgeBgColor,\r\n          color: '#ffffff',\r\n          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\r\n        };\r\n      } else {\r\n        // 深色背景使用浅色徽章\r\n        return {\r\n          backgroundColor: 'rgba(255, 255, 255, 0.25)',\r\n          color: '#ffffff',\r\n          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\r\n        };\r\n      }\r\n    },\r\n    \r\n    // 颜色调整辅助函数\r\n    adjustColor(color, amount) {\r\n      // 如果是白色特殊处理\r\n      if (color === '#ffffff') {\r\n        return '#e0e0e0';\r\n      }\r\n      \r\n      // 将颜色转换为RGB\r\n      let r = parseInt(color.slice(1, 3), 16);\r\n      let g = parseInt(color.slice(3, 5), 16);\r\n      let b = parseInt(color.slice(5, 7), 16);\r\n      \r\n      // 调整亮度\r\n      r = Math.max(0, Math.min(255, r + amount));\r\n      g = Math.max(0, Math.min(255, g + amount));\r\n      b = Math.max(0, Math.min(255, b + amount));\r\n      \r\n      // 转回十六进制\r\n      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;\r\n    },\r\n    \r\n    // 初始化指标卡片滚动\r\n    initIndicatorCardsScroll() {\r\n      // 清除之前的滚动定时器\r\n      if (this.indicatorCardsScrollTimer) {\r\n        clearInterval(this.indicatorCardsScrollTimer);\r\n      }\r\n      \r\n      const container = this.$refs.indicatorCardsContainer;\r\n      if (!container) return;\r\n      \r\n      // 计算滚动所需参数\r\n      let scrollTop = 0;\r\n      const scrollHeight = container.scrollHeight;\r\n      const clientHeight = container.clientHeight;\r\n      const maxScroll = scrollHeight - clientHeight;\r\n      \r\n      // 如果内容不足以滚动，直接返回\r\n      if (maxScroll <= 0) return;\r\n      \r\n      // 滚动步长和速度\r\n      const step = 0.5; // 滚动步长更小，使滚动更平滑\r\n      const scrollInterval = 20; // 滚动更新频率更高，更流畅\r\n      \r\n      this.indicatorCardsScrollTimer = setInterval(() => {\r\n        // 鼠标悬停时暂停滚动\r\n        if (this.indicatorCardsScrollPaused) return;\r\n        \r\n        scrollTop += step;\r\n        \r\n        // 当滚动到底部时，快速回到顶部并继续滚动\r\n        if (scrollTop >= maxScroll) {\r\n          // 重置滚动位置到顶部\r\n          scrollTop = 0;\r\n          container.scrollTop = 0;\r\n          \r\n          // 短暂暂停一下，让用户能看到回到顶部的过程\r\n          this.indicatorCardsScrollPaused = true;\r\n          setTimeout(() => {\r\n            this.indicatorCardsScrollPaused = false;\r\n          }, 1000);\r\n        } else {\r\n          container.scrollTop = scrollTop;\r\n        }\r\n      }, scrollInterval);\r\n    },\r\n\r\n    // 表头样式\r\n    headerCellStyle() {\r\n      return {\r\n        backgroundColor: \"#f5f7fa\",\r\n        color: \"#606266\",\r\n        fontWeight: \"bold\",\r\n      };\r\n    },\r\n\r\n        // 显示指标详情\r\n        showDetails(row) {\r\n      this.currentIndicator = row;\r\n      this.detailDialogVisible = true;\r\n      this.$nextTick(() => {\r\n        this.initDetailChart();\r\n      });\r\n    },\r\n\r\n    // 初始化详情图表\r\n    initDetailChart() {\r\n      if (!this.currentIndicator) return;\r\n\r\n      const chartDom = document.getElementById(\"indicatorChart\");\r\n      const myChart = echarts.init(chartDom);\r\n\r\n      const months = [\"1月\", \"2月\", \"3月\", \"4月\", \"5月\"];\r\n      \r\n      // 生成波动较大的数据，确保围绕目标值有明显起伏\r\n      const targetValue = parseFloat(this.currentIndicator.target) || 100;\r\n      \r\n      // 使用实际数据，如果没有则生成波动数据\r\n      let actualData = [];\r\n      if (this.currentIndicator.monthlyData && this.currentIndicator.monthlyData.length === 5) {\r\n        actualData = this.currentIndicator.monthlyData;\r\n      } else {\r\n        // 生成波动数据，确保有起伏\r\n        const fluctuationRange = targetValue * 0.3; // 波动范围为目标值的30%\r\n        actualData = months.map(() => {\r\n          const fluctuation = (Math.random() - 0.5) * 2 * fluctuationRange;\r\n          return Math.max(0, targetValue + fluctuation);\r\n        });\r\n      }\r\n      \r\n      // 目标值线\r\n      const targetData = Array(5).fill(targetValue);\r\n\r\n      const option = {\r\n        title: {\r\n          text: `${this.currentIndicator.name}月度趋势`,\r\n          left: \"center\",\r\n        },\r\n        tooltip: {\r\n          trigger: \"axis\",\r\n          axisPointer: {\r\n            type: \"cross\",\r\n          },\r\n          formatter: function(params) {\r\n            const actualValue = params[0].value.toFixed(2);\r\n            const targetValue = params[1].value.toFixed(2);\r\n            const diff = (actualValue - targetValue).toFixed(2);\r\n            // 使用统一的白色显示差值\r\n            const diffColor = 'color:#ffffff';\r\n            \r\n            return `${params[0].name}<br/>\r\n                   ${params[0].marker} ${params[0].seriesName}: ${actualValue}<br/>\r\n                   ${params[1].marker} ${params[1].seriesName}: ${targetValue}<br/>\r\n                   <span style=\"${diffColor}\">差值: ${diff}</span>`;\r\n          }\r\n        },\r\n        legend: {\r\n          data: [\"实际值\", \"目标值\"],\r\n          bottom: 10,\r\n        },\r\n        grid: {\r\n          left: \"3%\",\r\n          right: \"4%\",\r\n          bottom: \"15%\",\r\n          containLabel: true,\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          boundaryGap: false,\r\n          data: months,\r\n        },\r\n        yAxis: {\r\n          type: \"value\",\r\n          name: this.currentIndicator.unit,\r\n          axisLabel: {\r\n            formatter: \"{value}\",\r\n          },\r\n          scale: true, // 缩放Y轴以突出显示数据波动\r\n        },\r\n        series: [\r\n          {\r\n            name: \"实际值\",\r\n            type: \"line\",\r\n            data: actualData,\r\n            itemStyle: {\r\n              color: \"#409EFF\",\r\n            },\r\n            lineStyle: {\r\n              width: 3,\r\n            },\r\n            symbol: \"circle\",\r\n            symbolSize: 8,\r\n            markPoint: {\r\n              data: [\r\n                { type: \"max\", name: \"最大值\" },\r\n                { type: \"min\", name: \"最小值\" }\r\n              ]\r\n            }\r\n          },\r\n          {\r\n            name: \"目标值\",\r\n            type: \"line\",\r\n            data: targetData,\r\n            itemStyle: {\r\n              color: \"#F56C6C\",\r\n            },\r\n            lineStyle: {\r\n              width: 2,\r\n              type: \"dashed\",\r\n            },\r\n            markLine: {\r\n              data: [{ type: \"average\", name: \"目标值\" }],\r\n              label: {\r\n                formatter: \"目标值: {c}\"\r\n              }\r\n            }\r\n          },\r\n        ],\r\n      };\r\n\r\n      myChart.setOption(option);\r\n\r\n      // 响应式处理\r\n      window.addEventListener(\"resize\", () => {\r\n        myChart.resize();\r\n      });\r\n    },\r\n\r\n    // 关闭对话框\r\n    handleDialogClose() {\r\n      this.detailDialogVisible = false;\r\n      this.currentIndicator = null;\r\n      // 清除图表实例\r\n      const chartDom = document.getElementById(\"indicatorChart\");\r\n      if (chartDom) {\r\n        const chart = echarts.getInstanceByDom(chartDom);\r\n        if (chart) {\r\n          chart.dispose();\r\n        }\r\n      }\r\n    },\r\n        // 处理表格单元格合并\r\n        objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 仅对分厂和指标名称列进行合并\r\n      if (columnIndex === 0 || columnIndex === 1) {\r\n        // 获取当前行数据\r\n        const currentRow = this.techIndicators[rowIndex];\r\n        if (!currentRow) return { rowspan: 1, colspan: 1 };\r\n\r\n        // 分厂列合并处理\r\n        if (columnIndex === 0) {\r\n          // 如果是第一行或者与前一行分厂不同，则计算合并行数\r\n          if (\r\n            rowIndex === 0 ||\r\n            currentRow.factory !== this.techIndicators[rowIndex - 1].factory\r\n          ) {\r\n            // 计算连续相同分厂的行数\r\n            let rowspan = 1;\r\n            for (let i = rowIndex + 1; i < this.techIndicators.length; i++) {\r\n              if (this.techIndicators[i].factory === currentRow.factory) {\r\n                rowspan++;\r\n              } else {\r\n                break;\r\n              }\r\n            }\r\n            return { rowspan, colspan: 1 };\r\n          } else {\r\n            // 如果当前行与前一行分厂相同，则隐藏当前单元格\r\n            return { rowspan: 0, colspan: 0 };\r\n          }\r\n        }\r\n\r\n        // 指标名称列合并处理\r\n        if (columnIndex === 1) {\r\n          // 如果是第一行，或者与前一行分厂不同，或者分厂相同但指标名称不同\r\n          if (\r\n            rowIndex === 0 ||\r\n            currentRow.factory !== this.techIndicators[rowIndex - 1].factory ||\r\n            currentRow.name !== this.techIndicators[rowIndex - 1].name\r\n          ) {\r\n            // 计算连续相同分厂和指标名称的行数\r\n            let rowspan = 1;\r\n            for (let i = rowIndex + 1; i < this.techIndicators.length; i++) {\r\n              if (\r\n                this.techIndicators[i].factory === currentRow.factory &&\r\n                this.techIndicators[i].name === currentRow.name\r\n              ) {\r\n                rowspan++;\r\n              } else {\r\n                break;\r\n              }\r\n            }\r\n            return { rowspan, colspan: 1 };\r\n          } else {\r\n            // 如果当前行与前一行分厂和指标名称都相同，则隐藏当前单元格\r\n            return { rowspan: 0, colspan: 0 };\r\n          }\r\n        }\r\n      }\r\n      return { rowspan: 1, colspan: 1 };\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.page-title {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.page-title h2 {\r\n  font-size: 24px;\r\n  color: #303133;\r\n  margin: 0;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.box-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.section-header {\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n  color: #333;\r\n  padding: 5px 0;\r\n  border-bottom: 2px solid rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.tech-economic-section .section-header {\r\n  color: #4a6ee0;\r\n  border-bottom-color: #4a6ee0;\r\n}\r\n\r\n.energy-section .section-header {\r\n  color: #47b475;\r\n  border-bottom-color: #47b475;\r\n}\r\n\r\n.chart-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin: 0 -10px;\r\n}\r\n\r\n.top-charts-row,\r\n.energy-charts-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: nowrap;\r\n  width: 100%;\r\n  height: 600px!important;\r\n}\r\n\r\n.chart-item {\r\n  flex: 1;\r\n  min-width: 32%;\r\n  margin: 10px;\r\n  background-color: #fff;\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.chart-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.trend-controls {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.chart {\r\n  height: 340px;\r\n  width: 100%;\r\n}\r\n\r\n.scroll-table-container {\r\n  height: 340px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.scroll-table {\r\n  height: 100%;\r\n  overflow-y: hidden; /* 修改为hidden，使用JS控制滚动 */\r\n  scrollbar-width: thin;\r\n}\r\n\r\n.completion-rate {\r\n  font-weight: bold;\r\n}\r\n\r\n.completion-rate.red {\r\n  color: #F56C6C;\r\n}\r\n\r\n.completion-rate.deep-red {\r\n  color: #ff0000;\r\n  font-weight: bolder;\r\n}\r\n\r\n.completion-rate.green {\r\n  color: #67C23A;\r\n}\r\n\r\n.energy-stats {\r\n  padding: 10px;\r\n}\r\n\r\n.stat-card {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  border-radius: 6px;\r\n  text-align: center;\r\n  margin-bottom: 15px;\r\n  border-left: 4px solid #409EFF;\r\n  transition: all 0.3s;\r\n  height: 100px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.stat-card.good {\r\n  border-left-color: #67C23A;\r\n}\r\n\r\n.stat-card.warning {\r\n  border-left-color: #E6A23C;\r\n}\r\n\r\n.stat-card.danger {\r\n  border-left-color: #F56C6C;\r\n}\r\n\r\n.stat-title {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  margin: 5px 0;\r\n}\r\n\r\n.stat-change {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 动画效果 */\r\n.box-card {\r\n  animation: fadeIn 0.6s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.tech-economic-section {\r\n  animation-delay: 0.1s;\r\n  background-color: #e6f0ff; /* 修改技经指标部分的背景色，更深的蓝色背景 */\r\n}\r\n\r\n.energy-section {\r\n  animation-delay: 0.3s;\r\n  background-color: #e6fff0; /* 修改能源指标部分的背景色，更深的绿色背景 */\r\n}\r\n\r\n/* 修改卡片背景色 */\r\n.chart-item {\r\n  background-color: #ffffff; /* 白色背景 */\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 增强阴影 */\r\n  border: 1px solid rgba(0, 0, 0, 0.05); /* 添加细边框 */\r\n}\r\n\r\n/* 技经指标部分的卡片特殊样式 */\r\n.tech-economic-section .chart-item {\r\n  background-color: #f8faff; /* 浅蓝色调背景 */\r\n  border-top: 3px solid #4a6ee0; /* 蓝色上边框 */\r\n}\r\n\r\n/* 能源指标部分的卡片特殊样式 */\r\n.energy-section .chart-item {\r\n  background-color: #f8fff9; /* 浅绿色调背景 */\r\n  border-top: 3px solid #47b475; /* 绿色上边框 */\r\n}\r\n\r\n/* 媒体查询，适应不同屏幕尺寸 */\r\n@media screen and (max-width: 1600px) {\r\n  .top-charts-row,\r\n  .energy-charts-row {\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .chart-item {\r\n    min-width: 45%;\r\n    flex: 0 0 45%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .chart-item {\r\n    min-width: 100%;\r\n    flex: 0 0 100%;\r\n  }\r\n}\r\n\r\n/* 未完成指标表格样式 */\r\n.el-table {\r\n  border: none;\r\n}\r\n\r\n.el-table::before, .el-table::after {\r\n  content: none;\r\n}\r\n\r\n.el-table td.el-table__cell,\r\n.el-table th.el-table__cell {\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.el-table th.el-table__cell {\r\n  background-color: #f8f8f8;\r\n  color: #606266;\r\n  font-weight: bold;\r\n  font-size: 15px;\r\n}\r\n\r\n.el-table td.el-table__cell {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  padding: 8px 0;\r\n}\r\n\r\n.el-table__row:hover > td {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.energy-type-selector {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.energy-detail-container {\r\n  height: 340px;\r\n  overflow: auto;\r\n}\r\n\r\n.status-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-indicator.normal {\r\n  background-color: #f0f9eb;\r\n  color: #67C23A;\r\n}\r\n\r\n.status-indicator.warning {\r\n  background-color: #fdf6ec;\r\n  color: #E6A23C;\r\n}\r\n\r\n.status-indicator.danger {\r\n  background-color: #fef0f0;\r\n  color: #F56C6C;\r\n}\r\n\r\n.status-indicator i {\r\n  margin-left: 5px;\r\n  font-size: 16px;\r\n}\r\n\r\n.trend-value {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n}\r\n\r\n.trend-value.up {\r\n  color: #F56C6C;\r\n}\r\n\r\n.trend-value.down {\r\n  color: #67C23A;\r\n}\r\n\r\n.trend-value i {\r\n  margin-right: 3px;\r\n}\r\n\r\n.key-indicators-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  margin-top: 20px;\r\n  height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.indicator-card {\r\n  flex: 0 0 23%;\r\n  margin-bottom: 12px;\r\n  padding: 10px;\r\n  background-color: #f0f7ff;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  text-align: center;\r\n  position: relative;\r\n  border-left: 4px solid #409EFF;\r\n  transition: all 0.3s;\r\n  height: 125px;\r\n}\r\n\r\n.indicator-card.danger {\r\n  background-color: #dc143c; /* 猩红色 */\r\n  color: white;\r\n}\r\n\r\n.indicator-card.danger .indicator-title,\r\n.indicator-card.danger .indicator-value,\r\n.indicator-card.danger .indicator-target,\r\n.indicator-card.danger .indicator-unit,\r\n.indicator-card.danger .indicator-compare {\r\n  color: white;\r\n}\r\n\r\n.indicator-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.indicator-card.good {\r\n  border-left-color: #67C23A;\r\n  background-color: #f0fff5;\r\n}\r\n\r\n.indicator-card.warning {\r\n  border-left-color: #E6A23C;\r\n  background-color: #fffbf0;\r\n}\r\n\r\n.indicator-card.danger {\r\n  border-left-color: #F56C6C;\r\n}\r\n\r\n.indicator-title {\r\n  font-size: 14px;\r\n  color: #606266;\r\n  margin-bottom: 3px;\r\n  font-weight: bold;\r\n}\r\n\r\n.indicator-value {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  margin: 3px 0;\r\n}\r\n\r\n.indicator-target {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.indicator-unit {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.indicator-compare {\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n\r\n.indicator-change.up {\r\n  color: #67C23A;\r\n}\r\n\r\n.indicator-change.down {\r\n  color: #F56C6C;\r\n}\r\n\r\n.energy-chart-half {\r\n  flex: 0 0 48%;\r\n  min-width: 48%;\r\n}\r\n\r\n.energy-subchart-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  height: 500px;\r\n}\r\n\r\n.energy-subchart {\r\n  flex: 0 0 48%;\r\n  height: 48%;\r\n  margin-bottom: 10px;\r\n  border-radius: 4px;\r\n  padding: 5px;\r\n  background-color: #f0f8ff;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s;\r\n  border: 1px solid #d1e6ff;\r\n}\r\n\r\n.energy-chart-warning {\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n  border: 1px solid rgba(245, 108, 108, 0.3);\r\n}\r\n\r\n.subchart-title {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  text-align: center;\r\n  margin-bottom: 5px;\r\n  background-color: #e6f0ff;\r\n  padding: 3px 0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.subchart {\r\n  height: calc(100% - 0px);\r\n  width: 100%;\r\n}\r\n\r\n.chart-wrapper {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 400px;\r\n  overflow: hidden;\r\n}\r\n\r\n.chart {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n/* 指标卡片样式 */\r\n.indicator-cards-container {\r\n  height: 320px;\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  position: relative;\r\n  scrollbar-width: thin;\r\n  scrollbar-color: #e0e0e0 #f8f8f8;\r\n  padding: 6px 3px;\r\n}\r\n\r\n.indicator-cards-container::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.indicator-cards-container::-webkit-scrollbar-track {\r\n  background: #f8f8f8;\r\n}\r\n\r\n.indicator-cards-container::-webkit-scrollbar-thumb {\r\n  background-color: #e0e0e0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.indicator-cards-wrapper {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr); /* 一行显示3个 */\r\n  gap: 10px; /* 更小的间距 */\r\n  max-width: 100%;\r\n}\r\n\r\n.indicator-card-item {\r\n  background-color: #ffffff;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);\r\n  padding: 8px;\r\n  transition: all 0.3s ease;\r\n  border-top: 3px solid #909399;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 110px; /* 更小的高度 */\r\n  width: 100%;\r\n  max-width: 100%;\r\n  margin: 0 auto; /* 居中显示 */\r\n}\r\n\r\n.indicator-card-item:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.indicator-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.indicator-name {\r\n  font-size: 13px;\r\n  font-weight: bold;\r\n  flex: 1;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.completion-badge {\r\n  background-color: #f0f0f0;\r\n  padding: 1px 5px;\r\n  border-radius: 6px;\r\n  font-size: 10px;\r\n  font-weight: bold;\r\n  min-width: 40px;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n  margin-left: 4px;\r\n}\r\n\r\n.indicator-card-body {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.indicator-department {\r\n  font-size: 11px;\r\n  margin-bottom: 4px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  font-weight: 500;\r\n}\r\n\r\n.indicator-values {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 3px;\r\n}\r\n\r\n.value-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2px;\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n  padding: 1px 4px;\r\n  border-radius: 2px;\r\n}\r\n\r\n.value-label {\r\n  font-size: 10px;\r\n  font-weight: 500;\r\n}\r\n\r\n.value-number {\r\n  font-size: 11px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 不同完成率的卡片样式 */\r\n.indicator-card-item.excellent {\r\n  background-color: #67c23a;\r\n  border-top-color: #4d9e29;\r\n}\r\n\r\n.indicator-card-item.excellent .indicator-name,\r\n.indicator-card-item.excellent .indicator-department,\r\n.indicator-card-item.excellent .value-number,\r\n.indicator-card-item.excellent .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.excellent .completion-badge {\r\n  background-color: #4d9e29;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-good {\r\n  background-color: #85ce61;\r\n  border-top-color: #67c23a;\r\n}\r\n\r\n.indicator-card-item.very-good .indicator-name,\r\n.indicator-card-item.very-good .indicator-department,\r\n.indicator-card-item.very-good .value-number,\r\n.indicator-card-item.very-good .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-good .completion-badge {\r\n  background-color: #67c23a;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.good {\r\n  background-color: #b3e19d;\r\n  border-top-color: #85ce61;\r\n}\r\n\r\n.indicator-card-item.good .indicator-name,\r\n.indicator-card-item.good .indicator-department,\r\n.indicator-card-item.good .value-number {\r\n  color: #2e2e2e;\r\n}\r\n\r\n.indicator-card-item.good .completion-badge {\r\n  background-color: #85ce61;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.normal {\r\n  background-color: #f0f9eb;\r\n  border-top-color: #b3e19d;\r\n}\r\n\r\n.indicator-card-item.warning {\r\n  background-color: #fdf6ec;\r\n  border-top-color: #e6a23c;\r\n}\r\n\r\n.indicator-card-item.warning .completion-badge {\r\n  background-color: #e6a23c;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.bad {\r\n  background-color: #fef0f0;\r\n  border-top-color: #f56c6c;\r\n}\r\n\r\n.indicator-card-item.bad .completion-badge {\r\n  background-color: #f56c6c;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-bad {\r\n  background-color: #f56c6c;\r\n  border-top-color: #d63b3b;\r\n}\r\n\r\n.indicator-card-item.very-bad .indicator-name,\r\n.indicator-card-item.very-bad .indicator-department,\r\n.indicator-card-item.very-bad .value-number,\r\n.indicator-card-item.very-bad .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.very-bad .completion-badge {\r\n  background-color: #d63b3b;\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.terrible {\r\n  background-color: #dc143c; /* 猩红色 */\r\n  border-top-color: #a00f2d;\r\n}\r\n\r\n.indicator-card-item.terrible .indicator-name,\r\n.indicator-card-item.terrible .indicator-department,\r\n.indicator-card-item.terrible .value-number,\r\n.indicator-card-item.terrible .value-label {\r\n  color: #ffffff;\r\n}\r\n\r\n.indicator-card-item.terrible .completion-badge {\r\n  background-color: #a00f2d;\r\n  color: #ffffff;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0OA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,eAAA,GAAAJ,OAAA;AAGA,IAAAK,OAAA,GAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAM,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MAEA;MACAC,cAAA;MAEAC,mBAAA;MACAC,gBAAA;MACA;MACAC,YAAA;MACAC,SAAA;MAEA;MACA;MACA;MACA;MACA;MACA;MACAC,eAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,WAAA;MACAC,iBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,yBAAA;MAAA;MACAC,8BAAA;MAAA;MACAC,2BAAA;MAAA;MACAC,0BAAA;MAAA;;MAEAC,eAAA;MAAA;MACAC,WAAA;MAAA;;MAEA;MACAC,qBAAA;MACAC,oBAAA;MACAC,sBAAA;MAAA;MACAC,sBAAA;MAAA;;MAEA;MACAC,iBAAA;MACAC,iBAAA;MAAA;;MAEA;MACAC,WAAA;MAEA;MACAC,cAAA,GACA;QAAAC,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA,GACA;UAAAjC,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,GACA;QAAAP,UAAA;QAAAC,UAAA,GACA;UAAA3B,IAAA;UAAA4B,MAAA;UAAAC,MAAA;UAAAC,IAAA;UAAAC,cAAA;UAAAC,MAAA;UAAAC,eAAA;QAAA;MACA,EACA;MACAC,eAAA,EACA,CACA;QAAAR,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,GACA;QAAAA,UAAA;MAAA,EAEA;MACA;MACAS,cAAA;MAEA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,MAAA;QAAAC,MAAA;MAAA,EACA;MAEA;MACAC,qBAAA,GACA;QAAAzC,IAAA;QAAAsC,KAAA;MAAA,GACA;QAAAtC,IAAA;QAAAsC,KAAA;MAAA,GACA;QAAAtC,IAAA;QAAAsC,KAAA;MAAA,GACA;QAAAtC,IAAA;QAAAsC,KAAA;MAAA,GACA;QAAAtC,IAAA;QAAAsC,KAAA;MAAA,EACA;MAEA;MACAI,oBAAA;QACAC,MAAA;QACAnB,WAAA;QACAoB,WAAA;UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACAC,KAAA;UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACAC,GAAA;UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACAC,KAAA;UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;MAEA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAX,KAAA;QAAAR,IAAA;QAAAoB,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAX,KAAA;QAAAR,IAAA;QAAAoB,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAX,KAAA;QAAAR,IAAA;QAAAoB,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAX,KAAA;QAAAR,IAAA;QAAAoB,KAAA;MAAA,EACA;MAEA;MACAC,cAAA;QACAR,MAAA;QACAC,WAAA;QACAQ,IAAA;QACAN,GAAA;QACAC,KAAA;MACA;MAEA;MACAM,gBAAA,GACA;QACArD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,GACA;QACAzD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,GACA;QACAzD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,GACA;QACAzD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,GACA;QACAzD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,GACA;QACAzD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,GACA;QACAzD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,GACA;QACAzD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,GACA;QACAzD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,GACA;QACAzD,IAAA;QACAsD,QAAA;QACAhB,KAAA;QACAR,IAAA;QACAF,MAAA;QACA2B,OAAA;QACAC,MAAA;QACAhB,MAAA;QACAiB,KAAA;MACA,EACA;MAEA;MACAC,iBAAA;QACAf,MAAA;QACAC,WAAA,MAAAe,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QAAA,yBACA,oDACA,yEA0BA;QACAf,KAAA;UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACAC,GAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACAC,KAAA;UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;MAEA;MACAc,mBAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACAC,oBAAA;QACAlB,WAAA;QACAC,KAAA;QACAC,GAAA;QACAC,KAAA;MACA;MAEA;MACAgB,kBAAA;QACAnB,WAAA;UAAAoB,GAAA;UAAAC,GAAA;UAAAnC,IAAA;QAAA;QACAe,KAAA;UAAAmB,GAAA;UAAAC,GAAA;UAAAnC,IAAA;QAAA;QACAgB,GAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAnC,IAAA;QAAA;QACAiB,KAAA;UAAAiB,GAAA;UAAAC,GAAA;UAAAnC,IAAA;QAAA;MACA;IACA;EACA;EACAoC,QAAA;IACA;IACAC,yBAAA,WAAAA,0BAAA;MACA;MACA,IAAAC,eAAA,QAAA3C,cAAA,CACA4C,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA5C,UAAA,CAAA6C,QAAA;MAAA;MAAA,CACAC,GAAA,WAAAF,IAAA;QACA,IAAA3C,UAAA,GAAA2C,IAAA,CAAA3C,UAAA;QACA,IAAA8C,cAAA;QACA,IAAAC,eAAA,GAAA/C,UAAA,CAAAgD,MAAA;;QAEA;QACAhD,UAAA,CAAAiD,OAAA,WAAAC,SAAA;UACA;UACA,IAAAhD,MAAA,GAAAgD,SAAA,CAAA7C,MAAA;UACA,IAAAJ,MAAA,GAAAiD,SAAA,CAAAjD,MAAA;UAEA,IAAAkD,WAAA,GAAAD,SAAA,CAAA9C,cAAA,GACAF,MAAA,IAAAD,MAAA,GACAC,MAAA,IAAAD,MAAA;UAEA,IAAAkD,WAAA,EAAAL,cAAA;QACA;;QAEA;QACA,IAAAM,cAAA,GAAAN,cAAA,GAAAC,eAAA;QAEA;UACAhD,UAAA,EAAA4C,IAAA,CAAA5C,UAAA,CAAAsD,KAAA;UAAA;UACAC,cAAA,EAAAX,IAAA,CAAA5C,UAAA;UAAA;UACAqD,cAAA,EAAAG,UAAA,CAAAH,cAAA,CAAAI,OAAA;UACAT,eAAA,EAAAA,eAAA;UACAU,mBAAA,EAAAX;QACA;MACA;;MAEA;MACA,OAAAL,eAAA,CAAAiB,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAR,cAAA,GAAAO,CAAA,CAAAP,cAAA;MAAA;IACA;IAEA;IACAS,2BAAA,WAAAA,4BAAA;MACA,IAAAC,aAAA;MAEA,KAAAhE,cAAA,CAAAmD,OAAA,WAAAN,IAAA;QACA,IAAAA,IAAA,CAAA5C,UAAA,CAAA6C,QAAA;UAAA;UACA,IAAAmB,cAAA,GAAApB,IAAA,CAAA5C,UAAA,CAAAsD,KAAA;UAEAV,IAAA,CAAA3C,UAAA,CAAAiD,OAAA,WAAAC,SAAA;YACA;YACA;YACA,IAAAc,iBAAA,GAAAd,SAAA,CAAA5C,eAAA,IAAA4C,SAAA,CAAA5C,eAAA;YACA,IAAA8C,cAAA;;YAEA;YACA,IAAAY,iBAAA;cACA;cACAZ,cAAA,GAAAG,UAAA,CAAAS,iBAAA,CAAAC,OAAA;YACA;cACA;cACA,IAAA/D,MAAA,GAAAgD,SAAA,CAAA7C,MAAA;cACA,IAAAJ,MAAA,GAAAiD,SAAA,CAAAjD,MAAA;cAEA,IAAAiD,SAAA,CAAA9C,cAAA;gBACA;gBACAgD,cAAA,GAAAlD,MAAA,GAAAD,MAAA;cACA;gBACA;gBACAmD,cAAA,GAAAnD,MAAA,GAAAC,MAAA;cACA;YACA;;YAEA;YACA;YACA,IAAAgE,KAAA,CAAAd,cAAA,MAAAe,QAAA,CAAAf,cAAA;cACAA,cAAA;YACA;;YAEA;YACA,IAAAgB,IAAA,CAAAC,GAAA,CAAAjB,cAAA;cACAA,cAAA,GAAAA,cAAA;YACA;YAEAU,aAAA,CAAAQ,IAAA;cACAvE,UAAA,EAAAgE,cAAA;cACAb,SAAA,EAAAA,SAAA,CAAA7E,IAAA;cACA+E,cAAA,EAAAG,UAAA,CAAAH,cAAA,CAAAI,OAAA;cACAvD,MAAA,EAAAiD,SAAA,CAAAjD,MAAA;cACAC,MAAA,EAAAgD,SAAA,CAAA7C,MAAA;cAAA;cACAF,IAAA,EAAA+C,SAAA,CAAA/C,IAAA;cACAC,cAAA,EAAA8C,SAAA,CAAA9C;YACA;UACA;QACA;MACA;;MAEA;MACA,OAAA0D,aAAA,CAAAJ,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAR,cAAA,GAAAO,CAAA,CAAAP,cAAA;MAAA;IACA;IAEA;IACAmB,uBAAA,WAAAA,wBAAA;MACA,YAAAV,2BAAA;IACA;IAEA;IACAW,aAAA,WAAAA,cAAA;MACA,IAAAC,KAAA,OAAAC,GAAA;MACA,KAAA5E,cAAA,CAAAmD,OAAA,WAAAN,IAAA;QACA,IAAAA,IAAA,CAAA5C,UAAA,CAAA6C,QAAA;UACA6B,KAAA,CAAAE,GAAA,CAAAhC,IAAA,CAAA5C,UAAA,CAAAsD,KAAA;QACA;UACAoB,KAAA,CAAAE,GAAA,CAAAhC,IAAA,CAAA5C,UAAA;QACA;MACA;MACA,OAAA6E,KAAA,CAAAC,IAAA,CAAAJ,KAAA;IACA;IAEA;IACAK,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,KAAA;MACA,UAAAC,mBAAA;MAEA,YAAAlF,cAAA,CACA4C,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA5C,UAAA,CAAAkF,UAAA,CAAAF,KAAA,CAAAC,mBAAA;MAAA,GACAnC,GAAA,WAAAF,IAAA;QAAA;UACAhC,KAAA,EAAAgC,IAAA,CAAA5C,UAAA;UACAuB,KAAA,EAAAqB,IAAA,CAAA5C,UAAA,CAAAsD,KAAA;QACA;MAAA;IACA;IAEA;IACA6B,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,QAAAtF,cAAA,CAAAuF,IAAA,WAAA1C,IAAA;QAAA,OAAAA,IAAA,CAAA5C,UAAA,KAAAoF,MAAA,CAAAG,iBAAA;MAAA;MACA,OAAAF,QAAA,GAAAA,QAAA,CAAApF,UAAA;IACA;IAEA;IACAuF,YAAA,WAAAA,aAAA;MACA,YAAAhF,eAAA,CAAAsC,GAAA,WAAAF,IAAA;QAAA,OAAAA,IAAA,CAAA5C,UAAA;MAAA;IACA;EACA;EACAyF,KAAA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;EAAA,CACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,UAAA;;IAEA;IACA,KAAAC,uBAAA;;IAEA;IACA,KAAAC,SAAA;MACAH,MAAA,CAAAI,sBAAA;MACA,IAAAJ,MAAA,CAAAH,YAAA,IAAAG,MAAA,CAAAH,YAAA,CAAAvC,MAAA;QACA0C,MAAA,CAAA9F,iBAAA,GAAA8F,MAAA,CAAAH,YAAA;QACA;QACAG,MAAA,CAAAI,sBAAA;MACA;IACA;IACA,KAAAC,mBAAA;IACA;IACA,KAAAF,SAAA;MACA;MACA,IAAAG,KAAA,GAAAN,MAAA,CAAAO,KAAA,CAAAC,eAAA;MACA,IAAAF,KAAA,IAAAN,MAAA,CAAAlF,cAAA,CAAAwC,MAAA;QACA;QACA,IAAAmD,SAAA,GAAAH,KAAA,CAAAI,aAAA;QACA,IAAAD,SAAA;UACA,IAAAE,eAAA,GAAAF,SAAA,CAAAG,SAAA;UACA;UACAH,SAAA,CAAAG,SAAA,IAAAD,eAAA;QACA;MACA;MACAX,MAAA,CAAAa,gBAAA;IACA;;IAEA;IACA,KAAAC,iCAAA;;IAEA;IACA,KAAAC,uBAAA;;IAEA;IACA,KAAAZ,SAAA;MACAH,MAAA,CAAAgB,wBAAA;;MAEA;MACA,IAAAC,cAAA,GAAAjB,MAAA,CAAAO,KAAA,CAAAW,uBAAA;MACA,IAAAD,cAAA;QACAA,cAAA,CAAAE,gBAAA;UACAnB,MAAA,CAAAtG,0BAAA;QACA;QAEAuH,cAAA,CAAAE,gBAAA;UACAnB,MAAA,CAAAtG,0BAAA;QACA;MACA;IACA;;IAEA;IACA0H,MAAA,CAAAD,gBAAA,gBAAAE,YAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,cAAA;IAEA,SAAApI,WAAA;MACAqI,aAAA,MAAArI,WAAA;;MAEA;MACA,IAAAkH,KAAA,QAAAC,KAAA,CAAAC,eAAA;MACA,IAAAF,KAAA;QACA,IAAAG,SAAA,GAAAH,KAAA,CAAAI,aAAA;QACA,IAAAD,SAAA,SAAA1G,sBAAA,SAAAC,sBAAA;UACAyG,SAAA,CAAAiB,mBAAA,oBAAA3H,sBAAA;UACA0G,SAAA,CAAAiB,mBAAA,oBAAA1H,sBAAA;QACA;MACA;IACA;IAEA,SAAAb,iBAAA;MACAsI,aAAA,MAAAtI,iBAAA;IACA;IAEA,SAAAQ,eAAA;MACA8H,aAAA,MAAA9H,eAAA;IACA;IAEA,SAAAL,oBAAA;MACAmI,aAAA,MAAAnI,oBAAA;IACA;IAEA,SAAAC,yBAAA;MACAkI,aAAA,MAAAlI,yBAAA;IACA;;IAEA;IACA6H,MAAA,CAAAM,mBAAA,gBAAAL,YAAA;;IAEA;IACA,IAAAJ,cAAA,QAAAV,KAAA,CAAAW,uBAAA;IACA,IAAAD,cAAA;MACAA,cAAA,CAAAS,mBAAA;QACAH,MAAA,CAAA7H,0BAAA;MACA;MAEAuH,cAAA,CAAAS,mBAAA;QACAH,MAAA,CAAA7H,0BAAA;MACA;IACA;;IAEA;IACA,KAAAiI,aAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IACA;IAEAzB,mBAAA,WAAAA,oBAAA;MAAA,IAAA0B,MAAA;MACA,KAAA/I,YAAA;MACA,IAAAgJ,GAAA,GACA;MACA,IAAAC,cAAA;QACAC,MAAA;QACAF,GAAA,EAAAA,GAAA;QACAG,YAAA;MACA,GACAC,IAAA,WAAAC,QAAA;QACA,IAAAzJ,IAAA,OAAA0J,UAAA,CAAAD,QAAA,CAAAzJ,IAAA;QACA,IAAA2J,QAAA,GAAA/J,IAAA,CAAAgK,IAAA,CAAA5J,IAAA;UAAA6J,IAAA;QAAA;QACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,MAAA,CAAAJ,QAAA,CAAAK,UAAA;QACA,IAAAC,QAAA,GAAArK,IAAA,CAAAsK,KAAA,CAAAC,aAAA,CAAAL,UAAA;UACAM,MAAA;UACAC,KAAA;QACA;QACAC,OAAA,CAAAC,GAAA,CAAAN,QAAA;QACAd,MAAA,CAAAqB,gBAAA,CAAAP,QAAA;QACAd,MAAA,CAAA/I,YAAA;MACA,GACAqK,KAAA,WAAAC,KAAA;QACAJ,OAAA,CAAAI,KAAA,iBAAAA,KAAA;QACAvB,MAAA,CAAAwB,QAAA,CAAAD,KAAA;QACAvB,MAAA,CAAA/I,YAAA;MACA;IACA;IACAwK,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gDAAA,IAAAtB,IAAA,WAAAuB,GAAA;QACAF,MAAA,CAAAxK,SAAA,GAAA0K,GAAA,CAAAC,GAAA;MACA;IACA;IACA/B,iBAAA,WAAAA,kBAAA;MAAA,IAAAgC,MAAA;MACA,IAAAC,sBAAA,IAAA1B,IAAA,WAAAuB,GAAA;QACAE,MAAA,CAAArH,mBAAA,GAAAmH,GAAA,CAAA/K,IAAA;MACA;IACA;IAGAwK,gBAAA,WAAAA,iBAAAP,QAAA;MACA,KAAAA,QAAA,IAAAA,QAAA,CAAAvF,MAAA;MACA,IAAAyG,OAAA,GAAAlB,QAAA;MACA,IAAAmB,UAAA;QACAC,OAAA,EAAAF,OAAA,CAAAG,OAAA;QACAvL,IAAA,EAAAoL,OAAA,CAAAG,OAAA;QACA3J,MAAA,EAAAwJ,OAAA,CAAAG,OAAA;QACAzJ,IAAA,EAAAsJ,OAAA,CAAAG,OAAA;QACAC,GAAA,EAAAJ,OAAA,CAAAG,OAAA;QACAE,SAAA,EAAAL,OAAA,CAAAG,OAAA;QACAG,GAAA,EAAAN,OAAA,CAAAG,OAAA;QACAI,SAAA,EAAAP,OAAA,CAAAG,OAAA;QACAK,GAAA,EAAAR,OAAA,CAAAG,OAAA;QACAM,SAAA,EAAAT,OAAA,CAAAG,OAAA;QACAO,GAAA,EAAAV,OAAA,CAAAG,OAAA;QACAQ,SAAA,EAAAX,OAAA,CAAAG,OAAA;QACAS,GAAA,EAAAZ,OAAA,CAAAG,OAAA;QACAU,SAAA,EAAAb,OAAA,CAAAG,OAAA;MACA;MAEA,IAAAW,QAAA,GAAAhC,QAAA,CAAAiC,KAAA;MACA,IAAAjM,cAAA,GAAAgM,QAAA,CACA1H,GAAA,WAAA4H,GAAA;QACA,KAAAA,GAAA,IAAAA,GAAA,CAAAzH,MAAA;QACA,IACAyH,GAAA,CAAAf,UAAA,CAAAC,OAAA,MAAAe,SAAA,IACAD,GAAA,CAAAf,UAAA,CAAArL,IAAA,MAAAqM,SAAA,EAEA;QAEA,IAAAxH,SAAA;UACAyG,OAAA,EAAAc,GAAA,CAAAf,UAAA,CAAAC,OAAA;UACAtL,IAAA,EAAAoM,GAAA,CAAAf,UAAA,CAAArL,IAAA;UACA4B,MAAA,EACAwK,GAAA,CAAAf,UAAA,CAAAzJ,MAAA,MAAAyK,SAAA,GACAC,MAAA,CAAAF,GAAA,CAAAf,UAAA,CAAAzJ,MAAA,KACA;UACAE,IAAA,EAAAsK,GAAA,CAAAf,UAAA,CAAAvJ,IAAA;UACA0J,GAAA,EACAY,GAAA,CAAAf,UAAA,CAAAG,GAAA,MAAAa,SAAA,GACAC,MAAA,CAAAF,GAAA,CAAAf,UAAA,CAAAG,GAAA,KACA;UACAC,SAAA,EAAAW,GAAA,CAAAf,UAAA,CAAAI,SAAA;UACAC,GAAA,EACAU,GAAA,CAAAf,UAAA,CAAAK,GAAA,MAAAW,SAAA,GACAC,MAAA,CAAAF,GAAA,CAAAf,UAAA,CAAAK,GAAA,KACA;UACAC,SAAA,EAAAS,GAAA,CAAAf,UAAA,CAAAM,SAAA;UACAC,GAAA,EACAQ,GAAA,CAAAf,UAAA,CAAAO,GAAA,MAAAS,SAAA,GACAC,MAAA,CAAAF,GAAA,CAAAf,UAAA,CAAAO,GAAA,KACA;UACAC,SAAA,EAAAO,GAAA,CAAAf,UAAA,CAAAQ,SAAA;UACAC,GAAA,EACAM,GAAA,CAAAf,UAAA,CAAAS,GAAA,MAAAO,SAAA,GACAC,MAAA,CAAAF,GAAA,CAAAf,UAAA,CAAAS,GAAA,KACA;UACAC,SAAA,EAAAK,GAAA,CAAAf,UAAA,CAAAU,SAAA;UACAC,GAAA,EACAI,GAAA,CAAAf,UAAA,CAAAW,GAAA,MAAAK,SAAA,GACAC,MAAA,CAAAF,GAAA,CAAAf,UAAA,CAAAW,GAAA,KACA;UACAC,SAAA,EAAAG,GAAA,CAAAf,UAAA,CAAAY,SAAA;QACA;QAEApH,SAAA,CAAA0H,WAAA,IACArH,UAAA,CAAAL,SAAA,CAAA2G,GAAA,QACAtG,UAAA,CAAAL,SAAA,CAAA6G,GAAA,QACAxG,UAAA,CAAAL,SAAA,CAAA+G,GAAA,QACA1G,UAAA,CAAAL,SAAA,CAAAiH,GAAA,QACA5G,UAAA,CAAAL,SAAA,CAAAmH,GAAA,OACA;QAEA,OAAAnH,SAAA;MACA,GACAR,MAAA,CAAAmI,OAAA;;MAEA;MACA,IAAAC,UAAA,OAAAC,GAAA;MACA,IAAAC,gBAAA;;MAEA;MACAzM,cAAA,CAAA0E,OAAA,WAAAC,SAAA;QACA,IAAA+H,GAAA,MAAAC,MAAA,CAAAhI,SAAA,CAAAyG,OAAA,OAAAuB,MAAA,CAAAhI,SAAA,CAAA7E,IAAA,OAAA6M,MAAA,CAAAhI,SAAA,CAAA/C,IAAA;QACA,KAAA2K,UAAA,CAAAK,GAAA,CAAAF,GAAA;UACAH,UAAA,CAAAM,GAAA,CAAAH,GAAA,EAAAD,gBAAA,CAAAhI,MAAA;UACAgI,gBAAA,CAAA1G,IAAA,CAAApB,SAAA;QACA;MACA;MAEA,KAAA3E,cAAA,GAAAyM,gBAAA;IACA;IAEArF,UAAA,WAAAA,WAAA;MAAA,IAAA0F,MAAA;MACA,KAAAxF,SAAA;QACA;QACAwF,MAAA,CAAAC,6BAAA;;QAEA;QACA,IAAAD,MAAA,CAAAvG,8BAAA,CAAA9B,MAAA,SAAAqI,MAAA,CAAA/F,iBAAA;UACA+F,MAAA,CAAA/F,iBAAA,GAAA+F,MAAA,CAAAvG,8BAAA,IAAAnE,KAAA;QACA;QAEA,IAAA0K,MAAA,CAAAnG,2BAAA,CAAAlC,MAAA,SAAAqI,MAAA,CAAA5M,gBAAA;UACA4M,MAAA,CAAA5M,gBAAA,GAAA4M,MAAA,CAAAnG,2BAAA,IAAA7G,IAAA;QACA;;QAEA;QACAgN,MAAA,CAAAE,qBAAA;;QAEA;QACA,IAAAF,MAAA,CAAA9F,YAAA,IAAA8F,MAAA,CAAA9F,YAAA,CAAAvC,MAAA;UACA,KAAAqI,MAAA,CAAAzL,iBAAA;YACAyL,MAAA,CAAAzL,iBAAA,GAAAyL,MAAA,CAAA9F,YAAA;UACA;UACA;UACA8F,MAAA,CAAAvF,sBAAA;QACA;;QAEA;QACAuF,MAAA,CAAA7E,iCAAA;;QAEA;QACA6E,MAAA,CAAA5E,uBAAA;;QAEA;QACA4E,MAAA,CAAAG,qBAAA;MACA;IACA;IAEA;IACAhF,iCAAA,WAAAA,kCAAA;MAAA,IAAAiF,MAAA;MACA;MACA,KAAAvE,cAAA;;MAEA;MACA,KAAAtI,eAAA,GAAA8M,WAAA;QACAD,MAAA,CAAAE,kCAAA;MACA;IACA;IAEA;IACAA,kCAAA,WAAAA,mCAAA;MAAA,IAAAC,MAAA;MACA,IAAA/L,WAAA,QAAAiF,8BAAA;MACA,IAAAjF,WAAA,CAAAmD,MAAA;MAEA,IAAA6I,YAAA,GAAAhM,WAAA,CAAAiM,SAAA,WAAAnJ,IAAA;QAAA,OAAAA,IAAA,CAAAhC,KAAA,KAAAiL,MAAA,CAAAtG,iBAAA;MAAA;MACA,IAAAyG,SAAA,IAAAF,YAAA,QAAAhM,WAAA,CAAAmD,MAAA;MACA,KAAAsC,iBAAA,GAAAzF,WAAA,CAAAkM,SAAA,EAAApL,KAAA;IACA;IAEA;IACAqL,sBAAA,WAAAA,uBAAArL,KAAA;MACA,IAAAA,KAAA;QACA;QACA,KAAA6F,iCAAA;MACA;QACA;QACA,KAAAU,cAAA;MACA;IACA;IAEA;IACA+E,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,SAAAtN,eAAA;QACAuI,aAAA,MAAAvI,eAAA;MACA;MAEA,KAAAA,eAAA,GAAA8M,WAAA;QACAQ,MAAA,CAAAC,gBAAA;MACA;IACA;IAEA;IACAjF,cAAA,WAAAA,eAAA;MACA,SAAAtI,eAAA;QACAuI,aAAA,MAAAvI,eAAA;QACA,KAAAA,eAAA;MACA;IACA;IAEA;IACAwN,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAC,WAAA,GAAAD,IAAA;MACA,KAAAf,6BAAA;IACA;IAEA;IACA1F,uBAAA,WAAAA,wBAAA;MACA,IAAA2G,UAAA;MAEA,KAAAzM,cAAA,CAAAmD,OAAA,WAAAN,IAAA;QACAA,IAAA,CAAA3C,UAAA,CAAAiD,OAAA,WAAAC,SAAA;UACA;UACA;UACA,IAAAc,iBAAA,GAAAd,SAAA,CAAA5C,eAAA,IAAA4C,SAAA,CAAA5C,eAAA;UACA,IAAA8C,cAAA;;UAEA;UACA,IAAAY,iBAAA;YACA;YACAZ,cAAA,GAAAG,UAAA,CAAAS,iBAAA,CAAAC,OAAA;UACA;YACA;YACA,IAAA/D,MAAA,GAAAgD,SAAA,CAAA7C,MAAA;YACA,IAAAJ,MAAA,GAAAiD,SAAA,CAAAjD,MAAA;YAEA,IAAAiD,SAAA,CAAA9C,cAAA;cACA;cACAgD,cAAA,GAAAlD,MAAA,GAAAD,MAAA;YACA;cACA;cACAmD,cAAA,GAAAnD,MAAA,GAAAC,MAAA;YACA;UACA;;UAEA;UACA,IAAAkD,cAAA;YACAmJ,UAAA,CAAAjI,IAAA;cACAvE,UAAA,EAAA4C,IAAA,CAAA5C,UAAA,CAAA6C,QAAA,QAAAD,IAAA,CAAA5C,UAAA,CAAAsD,KAAA,WAAAV,IAAA,CAAA5C,UAAA;cACAmD,SAAA,EAAAA,SAAA,CAAA7E,IAAA;cACA4B,MAAA,EAAAiD,SAAA,CAAAjD,MAAA;cACAC,MAAA,EAAAgD,SAAA,CAAA7C,MAAA;cAAA;cACAF,IAAA,EAAA+C,SAAA,CAAA/C,IAAA;cACAiD,cAAA,EAAAG,UAAA,CAAAH,cAAA,CAAAI,OAAA;YACA;UACA;QACA;MACA;;MAEA;MACA,KAAAhD,cAAA,GAAA+L,UAAA,CAAA7I,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OAAAD,CAAA,CAAAP,cAAA,GAAAQ,CAAA,CAAAR,cAAA;MAAA;IACA;IAEA;IACAmD,gBAAA,WAAAA,iBAAA;MAAA,IAAAiG,OAAA;MACA,SAAAhM,cAAA,CAAAwC,MAAA;QACA,IAAAgD,KAAA,QAAAC,KAAA,CAAAC,eAAA;;QAEA;QACA,IAAAF,KAAA;UACA;UACA,SAAAlH,WAAA;YACAqI,aAAA,MAAArI,WAAA;UACA;;UAEA;UACA,IAAAqH,SAAA,GAAAH,KAAA,CAAAI,aAAA;UACA,IAAAD,SAAA;YACA;YACA,IAAAsG,SAAA;YACA,KAAA3N,WAAA,GAAA4M,WAAA;cACA;cACA,IAAAc,OAAA,CAAAzN,iBAAA;cAEA0N,SAAA;cACA,IAAAA,SAAA,IAAAtG,SAAA,CAAAuG,YAAA;gBACAD,SAAA;cACA;cACAtG,SAAA,CAAAsG,SAAA,GAAAA,SAAA;YACA,QAAAnN,WAAA;;YAEA;YACA,KAAAG,sBAAA;cACA+M,OAAA,CAAAzN,iBAAA;YACA;YAEA,KAAAW,sBAAA;cACA8M,OAAA,CAAAzN,iBAAA;YACA;;YAEA;YACAoH,SAAA,CAAAU,gBAAA,oBAAApH,sBAAA;YACA0G,SAAA,CAAAU,gBAAA,oBAAAnH,sBAAA;UACA;QACA;MACA;IACA;IAEA;IACAyM,gBAAA,WAAAA,iBAAA;MACA,IAAAN,YAAA,QAAAhM,WAAA,CAAA+J,OAAA,MAAAtE,iBAAA;MACA,IAAAyG,SAAA,IAAAF,YAAA,aAAAhM,WAAA,CAAAmD,MAAA;MACA,KAAAsC,iBAAA,QAAAzF,WAAA,CAAAkM,SAAA;IACA;IAEA;IACAT,6BAAA,WAAAA,8BAAA;MAAA,IAAAqB,OAAA;MACA,IAAAC,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,IAAAC,KAAA,GAAAlP,OAAA,CAAAmP,IAAA,CAAAJ,QAAA;;MAEA;MACA,IAAAK,qBAAA,GAAA7I,IAAA,CAAA/B,GAAA,UAAAwB,2BAAA,CAAAb,MAAA;;MAEA;MACA,IAAAkK,MAAA,QAAA3I,uBAAA,CAAA1B,GAAA,WAAAsK,IAAA;QACA;UACAC,QAAA,EAAAD,IAAA,CAAApN,UAAA;UACAsN,aAAA,EAAAF,IAAA,CAAAjK,SAAA;UACAoK,QAAA,KAAApC,MAAA,CAAAiC,IAAA,CAAApN,UAAA,QAAAmL,MAAA,CAAAiC,IAAA,CAAAjK,SAAA;QACA;MACA;MACA,IAAAqK,KAAA,QAAAhJ,uBAAA,CAAA1B,GAAA,WAAAsK,IAAA;QAAA,OAAAA,IAAA,CAAA/J,cAAA;MAAA;MAEA,IAAAoK,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAxF,IAAA;UACA;UACAyF,SAAA,YAAAC,MAAA;YACA,IAAAvP,IAAA,GAAAuP,MAAA;YACA,IAAAV,IAAA,QAAA5I,uBAAA,CAAAjG,IAAA,CAAAwP,SAAA;YACA,IAAAC,UAAA,GAAAZ,IAAA,CAAA/M,cAAA,0BAAA8K,MAAA,CACAiC,IAAA,CAAAjN,MAAA,EAAAgL,MAAA,CAAAiC,IAAA,CAAAhN,IAAA,2BAAA+K,MAAA,CACAiC,IAAA,CAAAjN,MAAA,EAAAgL,MAAA,CAAAiC,IAAA,CAAAhN,IAAA;YACA,UAAA+K,MAAA,CAAAiC,IAAA,CAAApN,UAAA,OAAAmL,MAAA,CAAAiC,IAAA,CAAAjK,SAAA,qDAAAgI,MAAA,CACAiC,IAAA,CAAAlN,MAAA,EAAAiL,MAAA,CAAAiC,IAAA,CAAAhN,IAAA,iCAAA+K,MAAA,CACA6C,UAAA,qDAAA7C,MAAA,CACAiC,IAAA,CAAA/J,cAAA;UACA,EAAA4K,IAAA;QACA;QACAC,IAAA;UACAC,IAAA;UAAA;UACAC,KAAA;UACAC,MAAA;UACAC,GAAA;UACAC,YAAA;QACA;QACAC,QAAA,GACA;UACApG,IAAA;UACAqG,IAAA;UACAC,UAAA;UACAC,KAAA;UACAC,GAAA,EAAA1B,qBAAA;UACA2B,KAAA;UACAC,UAAA;UACAC,UAAA;UACAC,QAAA;UACAC,gBAAA;UACAC,uBAAA;QACA,GACA;UACA9G,IAAA;UACAsG,UAAA;UACAC,KAAA;UACAC,GAAA,EAAA1B,qBAAA;UACA8B,QAAA;QACA,EACA;QACAG,OAAA;UACAC,OAAA;YACAZ,QAAA;cACAE,UAAA;YACA;YACAW,OAAA;YACAC,WAAA;UACA;UACAlB,KAAA;UACAE,GAAA;QACA;QACAiB,KAAA;UACAnH,IAAA;UACA9J,IAAA;UACAgE,GAAA;UAAA;UACAC,GAAA;UAAA;UACAiN,QAAA;YACAC,SAAA;cACAjO,KAAA;YACA;UACA;UACAkO,SAAA;YACAD,SAAA;cACArH,IAAA;cACA5G,KAAA;YACA;UACA;QACA;QACAmO,KAAA;UACAvH,IAAA;UACA7J,IAAA,EAAA4O,MAAA,CAAArK,GAAA,WAAAsK,IAAA;YAAA,OAAAA,IAAA,CAAAG,QAAA;UAAA;UACAqC,SAAA;YACA/B,SAAA,WAAAA,UAAAjN,KAAA;cACA,OAAAA,KAAA;YACA;YACAY,KAAA;YACAqO,UAAA;YACAC,MAAA;YACAC,IAAA;cACAnN,IAAA;gBACAoN,UAAA;gBACAH,UAAA;cACA;cACA1M,SAAA;gBACA0M,UAAA;cACA;YACA;UACA;UACAL,QAAA;YACAC,SAAA;cACAjO,KAAA;YACA;UACA;QACA;QACAyO,MAAA,GACA;UACA3R,IAAA;UACA8J,IAAA;UACA7J,IAAA,EAAAiP,KAAA;UACA0C,SAAA;YACA1O,KAAA,WAAAA,MAAAsM,MAAA;cACA;cACA,IAAAA,MAAA,CAAAvP,IAAA;gBACA;cACA,WAAAuP,MAAA,CAAAvP,IAAA;gBACA;cACA,WAAAuP,MAAA,CAAAvP,IAAA;gBACA;cACA,WAAAuP,MAAA,CAAAvP,IAAA;gBACA;cACA;gBACA;cACA;YACA;UACA;UACAgD,KAAA;YACAkN,IAAA;YACA0B,QAAA;YACAtC,SAAA;YACArM,KAAA,WAAAA,MAAAsM,MAAA;cACA;cACA,OAAAA,MAAA,CAAAvP,IAAA;YACA;YACAyR,UAAA;YACAI,QAAA;UACA;UACAC,QAAA;UAAA;UACAC,cAAA;UAAA;UACAC,cAAA,WAAAA,eAAAC,GAAA;YACA,OAAAA,GAAA;UACA;QACA,EACA;QACAC,eAAA;QACAC,oBAAA,WAAAA,qBAAAF,GAAA;UACA,OAAAA,GAAA;QACA;MACA;MAEAxD,KAAA,CAAA2D,SAAA,CAAAlD,MAAA;;MAEA;MACAT,KAAA,CAAA4D,EAAA,uBAAA9C,MAAA;QACA,IAAAa,KAAA,GAAAb,MAAA,CAAAa,KAAA;UAAAC,GAAA,GAAAd,MAAA,CAAAc,GAAA;QACAnB,MAAA,CAAAe,QAAA,IAAAG,KAAA,GAAAA,KAAA;QACAlB,MAAA,CAAAe,QAAA,IAAAI,GAAA,GAAAA,GAAA;QACAnB,MAAA,CAAAe,QAAA,IAAAG,KAAA,GAAAA,KAAA;QACAlB,MAAA,CAAAe,QAAA,IAAAI,GAAA,GAAAA,GAAA;QACA5B,KAAA,CAAA2D,SAAA,CAAAlD,MAAA;MACA;;MAEA;MACA,KAAAjO,qBAAA;QACAoN,OAAA,CAAAxN,2BAAA;MACA;MAEA,KAAAK,oBAAA;QACAmN,OAAA,CAAAxN,2BAAA;MACA;;MAEA;MACAyN,QAAA,CAAA/F,gBAAA,mBAAAtH,qBAAA;;MAEA;MACAqN,QAAA,CAAA/F,gBAAA,kBAAArH,oBAAA;;MAEA;MACAsH,MAAA,CAAAD,gBAAA;QACAkG,KAAA,CAAA6D,MAAA;MACA;;MAEA;MACA,KAAAC,eAAA,GAAA9D,KAAA;IACA;IAEA;IACA+D,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,OAAA;MACA,SAAA/R,oBAAA;QACAmI,aAAA,MAAAnI,oBAAA;MACA;MAEA,KAAAA,oBAAA,GAAA0M,WAAA;QACA,IAAAqF,OAAA,CAAA5R,2BAAA;QAEA,KAAA4R,OAAA,CAAAF,eAAA;QAEA,IAAArD,MAAA,GAAAuD,OAAA,CAAAF,eAAA,CAAAG,SAAA;QACA,IAAAtC,KAAA,GAAAlB,MAAA,CAAAe,QAAA,IAAAG,KAAA;QACA,IAAAC,GAAA,GAAAnB,MAAA,CAAAe,QAAA,IAAAI,GAAA;QACA,IAAAsC,IAAA;QACA,IAAAtI,KAAA,GAAAgG,GAAA,GAAAD,KAAA;;QAEA;QACA,IAAAqC,OAAA,CAAA7R,8BAAA;UACA;UACA,IAAAyP,GAAA;YACAD,KAAA,IAAAuC,IAAA;YACAtC,GAAA,IAAAsC,IAAA;UACA;YACA;YACAF,OAAA,CAAA7R,8BAAA;UACA;QACA;UACA;UACA,IAAAwP,KAAA;YACAA,KAAA,IAAAuC,IAAA;YACAtC,GAAA,IAAAsC,IAAA;UACA;YACA;YACAF,OAAA,CAAA7R,8BAAA;UACA;QACA;;QAEA;QACAsO,MAAA,CAAAe,QAAA,IAAAG,KAAA,GAAAA,KAAA;QACAlB,MAAA,CAAAe,QAAA,IAAAI,GAAA,GAAAA,GAAA;QACAnB,MAAA,CAAAe,QAAA,IAAAG,KAAA,GAAAA,KAAA;QACAlB,MAAA,CAAAe,QAAA,IAAAI,GAAA,GAAAA,GAAA;QACAoC,OAAA,CAAAF,eAAA,CAAAH,SAAA,CAAAlD,MAAA;MACA;IACA;IAEA;IACAjC,qBAAA,WAAAA,sBAAA;MAAA,IAAA2F,OAAA;MACA,IAAAtE,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,IAAAC,KAAA,GAAAlP,OAAA,CAAAmP,IAAA,CAAAJ,QAAA;;MAEA;MACA,IAAAxH,QAAA,QAAAtF,cAAA,CAAAuF,IAAA,WAAA1C,IAAA;QAAA,OAAAA,IAAA,CAAA5C,UAAA,KAAAmR,OAAA,CAAA5L,iBAAA,IAAA3C,IAAA,CAAA5C,UAAA,CAAAkF,UAAA,CAAAiM,OAAA,CAAA5L,iBAAA;MAAA;MAEA,KAAAF,QAAA,UAAA3G,gBAAA;QACA;MACA;;MAEA;MACA,IAAAyE,SAAA,GAAAkC,QAAA,CAAApF,UAAA,CAAAqF,IAAA,WAAA8L,GAAA;QAAA,OAAAA,GAAA,CAAA9S,IAAA,KAAA6S,OAAA,CAAAzS,gBAAA;MAAA;MAEA,KAAAyE,SAAA;QACA;MACA;;MAEA;MACA,IAAAlC,MAAA;;MAEA;MACA,IAAAX,MAAA,GAAA6C,SAAA,CAAA7C,MAAA;MACA,IAAAgC,GAAA,GAAA+B,IAAA,CAAA/B,GAAA,CAAA+O,KAAA,CAAAhN,IAAA,MAAAiN,mBAAA,CAAApP,OAAA,EAAA5B,MAAA;MACA,IAAAiC,GAAA,GAAA8B,IAAA,CAAA9B,GAAA,CAAA8O,KAAA,CAAAhN,IAAA,MAAAiN,mBAAA,CAAApP,OAAA,EAAA5B,MAAA;MACA,IAAAsI,KAAA,GAAArG,GAAA,GAAAD,GAAA;;MAEA;MACA,IAAAiP,IAAA,GAAAjP,GAAA,GAAAsG,KAAA;MACA,IAAA4I,IAAA,GAAAjP,GAAA,GAAAqG,KAAA;MAEA,IAAA6E,MAAA;QACA9M,KAAA;UACA8Q,IAAA,KAAAtG,MAAA,MAAA5F,iBAAA,SAAA4F,MAAA,MAAAzM,gBAAA;UACAyP,IAAA;UACAuD,SAAA;YACAC,QAAA;UACA;QACA;QACAjE,OAAA;UACAC,OAAA;UACAE,SAAA,WAAAA,UAAAC,MAAA;YACA,UAAA3C,MAAA,CAAA2C,MAAA,IAAAxP,IAAA,WAAA6M,MAAA,CAAAhI,SAAA,CAAA7E,IAAA,QAAA6M,MAAA,CAAA2C,MAAA,IAAAlN,KAAA,OAAAuK,MAAA,CAAAhI,SAAA,CAAA/C,IAAA,+BAAA+K,MAAA,CAAAhI,SAAA,CAAAjD,MAAA,OAAAiL,MAAA,CAAAhI,SAAA,CAAA/C,IAAA;UACA;UACAwN,WAAA;YACAxF,IAAA;YACA7G,KAAA;cACAqQ,eAAA;YACA;UACA;QACA;QACA1D,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,GAAA;UACAC,YAAA;QACA;QACAgB,KAAA;UACAnH,IAAA;UACAyJ,WAAA;UACAtT,IAAA,EAAA0C,MAAA;UACAuO,QAAA;YACAC,SAAA;cACAZ,KAAA;YACA;UACA;QACA;QACAc,KAAA;UACAvH,IAAA;UACA9J,IAAA,EAAA6E,SAAA,CAAA/C,IAAA;UACAkC,GAAA,EAAAiP,IAAA;UACAhP,GAAA,EAAAiP,IAAA;UACA5B,SAAA;YACA/B,SAAA,WAAAA,UAAAjN,KAAA;cACA,OAAAA,KAAA,CAAA6C,OAAA;YACA;UACA;UACAiM,SAAA;YACAD,SAAA;cACArH,IAAA;YACA;UACA;UACAoH,QAAA;YACAC,SAAA;cACAZ,KAAA;YACA;UACA;QACA;QACAoB,MAAA,GACA;UACA3R,IAAA,EAAA6E,SAAA,CAAA7E,IAAA;UACA8J,IAAA;UACA7J,IAAA,EAAA4E,SAAA,CAAA7C,MAAA;UACAwR,MAAA;UAAA;UACAC,MAAA;UACAC,UAAA;UACAC,QAAA;YACA/B,SAAA;cACAgC,UAAA;cACAC,WAAA;YACA;YACAC,KAAA;UACA;UACAlC,SAAA;YACA1O,KAAA;YACA6Q,WAAA;UACA;UACA5C,SAAA;YACAZ,KAAA;YACAsD,WAAA;YACAD,UAAA;YACAI,aAAA;UACA;UACAC,SAAA;YACA/Q,KAAA,MAAA1D,OAAA,CAAA0U,OAAA,CAAAC,cAAA,cACA;cACAC,MAAA;cACAlR,KAAA;YACA,GACA;cACAkR,MAAA;cACAlR,KAAA;YACA,GACA;cACAkR,MAAA;cACAlR,KAAA;YACA,EACA;UACA;UACAmR,QAAA;YACAC,MAAA;YACAnD,SAAA;cACAjO,KAAA;cACA4G,IAAA;cACAyG,KAAA;YACA;YACAtQ,IAAA,GACA;cACAoR,KAAA,EAAAxM,SAAA,CAAAjD,MAAA;cACAqB,KAAA;gBACAsM,SAAA,yBAAA1C,MAAA,CAAAhI,SAAA,CAAAjD,MAAA;gBACAiQ,QAAA;gBACAwB,QAAA;gBACAC,eAAA;gBACAiB,OAAA;gBACAC,YAAA;cACA;YACA;UAEA;UACAC,iBAAA;UACAtC,eAAA;UACAF,cAAA,WAAAA,eAAAC,GAAA;YACA,OAAAA,GAAA;UACA;QACA,GACA;UACAlS,IAAA;UACA8J,IAAA;UACA7J,IAAA,EAAAsG,KAAA,CAAA5D,MAAA,CAAAgC,MAAA,EAAA+P,IAAA,CAAA7P,SAAA,CAAAjD,MAAA;UACAuP,SAAA;YACAjO,KAAA;YACA4G,IAAA;YACAyG,KAAA;UACA;UACAkD,MAAA;QACA,EACA;QACAkB,MAAA;UACA1U,IAAA,GAAA4E,SAAA,CAAA7E,IAAA;UACA+P,MAAA;QACA;MACA;MAEArB,KAAA,CAAA2D,SAAA,CAAAlD,MAAA;IACA;IAEA;IACA1H,sBAAA,WAAAA,uBAAA;MAAA,IAAAmN,OAAA;MACA;MACA,UAAArT,iBAAA,UAAAmC,iBAAA;;MAEA;MACA,SAAAkJ,GAAA,SAAA9I,oBAAA;QACA,KAAAA,oBAAA,CAAA8I,GAAA;MACA;;MAEA;MACA,KAAA5J,WAAA,CAAA4B,OAAA,WAAAkF,IAAA;QACA,IAAAyE,QAAA,GAAAC,QAAA,CAAAC,cAAA,qBAAA3E,IAAA,CAAAxH,KAAA;QACA,KAAAiM,QAAA;;QAEA;QACA,IAAAsG,aAAA,GAAArV,OAAA,CAAAsV,gBAAA,CAAAvG,QAAA;QACA,IAAAsG,aAAA;UACAA,aAAA,CAAAE,OAAA;QACA;QAEA,IAAArG,KAAA,GAAAlP,OAAA,CAAAmP,IAAA,CAAAJ,QAAA;QAEA,IAAAtO,IAAA,GAAA2U,OAAA,CAAAlR,iBAAA,CAAAoG,IAAA,CAAAxH,KAAA,EAAAsS,OAAA,CAAArT,iBAAA;QACA,IAAAoB,MAAA,GAAAiS,OAAA,CAAAlR,iBAAA,CAAAf,MAAA;QACA,IAAAqS,WAAA,GAAAJ,OAAA,CAAA7Q,kBAAA,CAAA+F,IAAA,CAAAxH,KAAA;;QAEA;QACA,IAAA2S,QAAA,GAAAlP,IAAA,CAAA/B,GAAA,CAAA+O,KAAA,CAAAhN,IAAA,MAAAiN,mBAAA,CAAApP,OAAA,EAAA3D,IAAA;QACA,IAAAiV,QAAA,GAAAnP,IAAA,CAAA9B,GAAA,CAAA8O,KAAA,CAAAhN,IAAA,MAAAiN,mBAAA,CAAApP,OAAA,EAAA3D,IAAA;QACA,IAAAkV,UAAA,GAAAD,QAAA,GAAAD,QAAA;;QAEA;QACA;QACA,IAAAhC,IAAA,GAAAgC,QAAA,GAAAE,UAAA;QACA,IAAAjC,IAAA,GAAAgC,QAAA,GAAAC,UAAA;;QAEA,IAAAhG,MAAA;UACAC,OAAA;YACAC,OAAA;YACAE,SAAA,WAAAA,UAAAC,MAAA;cACA,IAAAlN,KAAA,GAAAkN,MAAA,IAAAlN,KAAA;cACA,UAAAuK,MAAA,CAAA2C,MAAA,IAAAxP,IAAA,WAAA6M,MAAA,CAAA/C,IAAA,CAAA7G,KAAA,QAAA4J,MAAA,CAAAvK,KAAA,OAAAuK,MAAA,CAAAmI,WAAA,CAAAlT,IAAA;YACA;UACA;UACA8N,IAAA;YACAC,IAAA;YACAC,KAAA;YACAC,MAAA;YACAC,GAAA;YACAC,YAAA;UACA;UACAgB,KAAA;YACAnH,IAAA;YACA7J,IAAA,EAAA0C,MAAA;YACAuO,QAAA;cACAC,SAAA;gBACAjO,KAAA;cACA;YACA;YACAoO,SAAA;cACApO,KAAA;YACA;YACAqQ,WAAA;UACA;UACAlC,KAAA;YACAvH,IAAA;YACA9J,IAAA,EAAAgV,WAAA,CAAAlT,IAAA;YACAkC,GAAA,EAAAiP,IAAA;YAAA;YACAhP,GAAA,EAAAiP,IAAA;YAAA;YACAhC,QAAA;cACAC,SAAA;gBACAjO,KAAA;cACA;YACA;YACAoO,SAAA;cACApO,KAAA;cAAA;cACAqM,SAAA,WAAAA,UAAAjN,KAAA;gBACA;gBACA,IAAAA,KAAA;kBACA,OAAAA,KAAA,CAAA6C,OAAA;gBACA;kBACA,OAAA7C,KAAA,CAAA6C,OAAA;gBACA;cACA;YACA;YACAiM,SAAA;cACAD,SAAA;gBACArH,IAAA;gBACA5G,KAAA;cACA;YACA;UACA;UACAyO,MAAA,GACA;YACA3R,IAAA,EAAA8J,IAAA,CAAA7G,KAAA;YACA6G,IAAA;YACA7J,IAAA,EAAAA,IAAA;YACAuT,MAAA;YAAA;YACA4B,cAAA;YAAA;YACA3B,MAAA;YACAC,UAAA;YACA2B,QAAA;YAAA;YACAlE,SAAA;cACAZ,KAAA;cAAA;cACArN,KAAA,EAAA4G,IAAA,CAAA5G,KAAA;YACA;YACA0O,SAAA;cACA1O,KAAA,EAAA4G,IAAA,CAAA5G,KAAA;cACA6Q,WAAA;cACAuB,WAAA;cACAzB,WAAA;cACAD,UAAA;YACA;YACAD,QAAA;cACA/B,SAAA;gBACAmC,WAAA;gBACAH,UAAA;cACA;cACAzC,SAAA;gBACAZ,KAAA;cACA;YACA;YACA0D,SAAA;cACA/Q,KAAA,MAAA1D,OAAA,CAAA0U,OAAA,CAAAC,cAAA,cACA;gBACAC,MAAA;gBACAlR,KAAA,EAAA0R,OAAA,CAAAW,SAAA,CAAAzL,IAAA,CAAA5G,KAAA;cACA,GACA;gBACAkR,MAAA;gBACAlR,KAAA,EAAA0R,OAAA,CAAAW,SAAA,CAAAzL,IAAA,CAAA5G,KAAA;cACA,EACA;YACA;UACA;QAEA;QAEAwL,KAAA,CAAA2D,SAAA,CAAAlD,MAAA;MACA;IACA;IAEA;IACAoG,SAAA,WAAAA,UAAAC,GAAA,EAAAC,KAAA;MACA,IAAAC,CAAA,GAAAC,QAAA,CAAAH,GAAA,CAAArJ,KAAA;MACA,IAAAyJ,CAAA,GAAAD,QAAA,CAAAH,GAAA,CAAArJ,KAAA;MACA,IAAA5G,CAAA,GAAAoQ,QAAA,CAAAH,GAAA,CAAArJ,KAAA;MACA,eAAAU,MAAA,CAAA6I,CAAA,QAAA7I,MAAA,CAAA+I,CAAA,QAAA/I,MAAA,CAAAtH,CAAA,QAAAsH,MAAA,CAAA4I,KAAA;IACA;IAEA;IACAtI,qBAAA,WAAAA,sBAAA;MAAA,IAAA0I,OAAA;MACA;MACA,SAAA7U,eAAA;QACA8H,aAAA,MAAA9H,eAAA;MACA;;MAEA;MACA,KAAAA,eAAA,GAAAqM,WAAA;QACAwI,OAAA,CAAAC,gBAAA;MACA;IACA;IAEA;IACAA,gBAAA,WAAAA,iBAAA;MACA,IAAAtI,YAAA,QAAAtG,YAAA,CAAAqE,OAAA,MAAAhK,iBAAA;MACA,IAAAmM,SAAA,IAAAF,YAAA,aAAAtG,YAAA,CAAAvC,MAAA;MACA,KAAApD,iBAAA,QAAA2F,YAAA,CAAAwG,SAAA;MACA,KAAAjG,sBAAA;IACA;IAEA;IACAiB,YAAA,WAAAA,aAAA;MACA,IAAAqN,QAAA,IACA,6BACA,oBACA;MAEAA,QAAA,CAAAnR,OAAA,WAAAoR,EAAA;QACA,IAAAzH,QAAA,GAAAC,QAAA,CAAAC,cAAA,CAAAuH,EAAA;QACA,IAAAzH,QAAA;UACA,IAAAG,KAAA,GAAAlP,OAAA,CAAAsV,gBAAA,CAAAvG,QAAA;UACA,IAAAG,KAAA;YACAA,KAAA,CAAA6D,MAAA;UACA;QACA;MACA;;MAEA;MACA,KAAAvP,WAAA,CAAA4B,OAAA,WAAAkF,IAAA;QACA,IAAAyE,QAAA,GAAAC,QAAA,CAAAC,cAAA,qBAAA3E,IAAA,CAAAxH,KAAA;QACA,IAAAiM,QAAA;UACA,IAAAG,KAAA,GAAAlP,OAAA,CAAAsV,gBAAA,CAAAvG,QAAA;UACA,IAAAG,KAAA;YACAA,KAAA,CAAA6D,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAvJ,aAAA,WAAAA,cAAA;MAAA,IAAAiN,OAAA;MACA,IAAAF,QAAA,IACA,6BACA,oBACA;MAEAA,QAAA,CAAAnR,OAAA,WAAAoR,EAAA;QACA,IAAAzH,QAAA,GAAAC,QAAA,CAAAC,cAAA,CAAAuH,EAAA;QACA,IAAAzH,QAAA;UACA;UACA,IAAAyH,EAAA,oCAAAC,OAAA,CAAA/U,qBAAA,IAAA+U,OAAA,CAAA9U,oBAAA;YACAoN,QAAA,CAAAxF,mBAAA,cAAAkN,OAAA,CAAA/U,qBAAA;YACAqN,QAAA,CAAAxF,mBAAA,aAAAkN,OAAA,CAAA9U,oBAAA;UACA;UAEA,IAAAuN,KAAA,GAAAlP,OAAA,CAAAsV,gBAAA,CAAAvG,QAAA;UACA,IAAAG,KAAA;YACAA,KAAA,CAAAqG,OAAA;UACA;QACA;MACA;;MAEA;MACA,KAAAvC,eAAA;;MAEA;MACA,KAAAxP,WAAA,CAAA4B,OAAA,WAAAkF,IAAA;QACA,IAAAyE,QAAA,GAAAC,QAAA,CAAAC,cAAA,qBAAA3E,IAAA,CAAAxH,KAAA;QACA,IAAAiM,QAAA;UACA,IAAAG,KAAA,GAAAlP,OAAA,CAAAsV,gBAAA,CAAAvG,QAAA;UACA,IAAAG,KAAA;YACAA,KAAA,CAAAqG,OAAA;UACA;QACA;MACA;IACA;IAGA;IACA3M,uBAAA,WAAAA,wBAAA;MAAA,IAAA8N,OAAA;MACA;MACA,SAAA1V,iBAAA;QACAsI,aAAA,MAAAtI,iBAAA;MACA;;MAEA;MACA,KAAAA,iBAAA,GAAA6M,WAAA;QACA6I,OAAA,CAAAC,kBAAA;MACA;IACA;IAEA;IACAA,kBAAA,WAAAA,mBAAA;MACA,IAAA3I,YAAA,QAAAhM,WAAA,CAAA+J,OAAA,MAAA5E,mBAAA;MACA,IAAA+G,SAAA,IAAAF,YAAA,aAAAhM,WAAA,CAAAmD,MAAA;MACA,KAAAgC,mBAAA,QAAAnF,WAAA,CAAAkM,SAAA;IACA;IAEA;IACA0I,4BAAA,WAAAA,6BAAA;MACA,IAAA7H,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,IAAAC,KAAA,GAAAlP,OAAA,CAAAmP,IAAA,CAAAJ,QAAA;MAEA,IAAAY,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAxF,IAAA;UACA;UACAyF,SAAA,YAAAC,MAAA;YACA,IAAAC,SAAA,GAAAD,MAAA,IAAAC,SAAA;YACA,IAAA5K,SAAA,QAAAhB,mBAAA,CAAA4L,SAAA;YACA,IAAA4G,UAAA,GAAAxR,SAAA,CAAAtC,MAAA,WAAAsK,MAAA,CAAAhI,SAAA,CAAAtC,MAAA,YAAAsK,MAAA,CAAAhI,SAAA,CAAAtC,MAAA;YACA,IAAA+T,WAAA,GAAAzR,SAAA,CAAArC,MAAA,0BAAAqC,SAAA,CAAArC,MAAA;YAEA,0DAAAqK,MAAA,CACAhI,SAAA,CAAA7E,IAAA,+CAAA6M,MAAA,CACAhI,SAAA,CAAA0R,KAAA,OAAA1J,MAAA,CAAAhI,SAAA,CAAA/C,IAAA,+CAAA+K,MAAA,CACAhI,SAAA,CAAA2R,SAAA,OAAA3J,MAAA,CAAAhI,SAAA,CAAA/C,IAAA,mEAAA+K,MAAA,CACAyJ,WAAA,SAAAzJ,MAAA,CAAAwJ,UAAA;UAEA,EAAA1G,IAAA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAE,YAAA;QACA;QACAgB,KAAA;UACAnH,IAAA;UACA7J,IAAA,OAAA4D,mBAAA,CAAAW,GAAA,WAAAsK,IAAA;YAAA,OAAAA,IAAA,CAAA9O,IAAA;UAAA;QACA;QACAqR,KAAA;UACAvH,IAAA;UACAwH,SAAA;YACA/B,SAAA,WAAAA,UAAAjN,KAAA;cACA,IAAAA,KAAA;gBACA,OAAAA,KAAA,CAAA6C,OAAA;cACA;gBACA,OAAA7C,KAAA,CAAA6C,OAAA;cACA;YACA;UACA;QACA;QACAwM,MAAA,GACA;UACA3R,IAAA;UACA8J,IAAA;UACA7J,IAAA,OAAA4D,mBAAA,CAAAW,GAAA,WAAAsK,IAAA,EAAA2H,KAAA;YACA;cACAnU,KAAA,EAAAwM,IAAA,CAAAyH,KAAA;cACA3E,SAAA;gBACA1O,KAAA,EAAA4L,IAAA,CAAAtM,MAAA,0BAAAsM,IAAA,CAAAtM,MAAA;cACA;YACA;UACA;UACAS,KAAA;YACAkN,IAAA;YACA0B,QAAA;YACAtC,SAAA,YAAAC,MAAA;cACA,IAAAiH,KAAA,GAAAjH,MAAA,CAAAC,SAAA;cACA,YAAA5L,mBAAA,CAAA4S,KAAA,EAAAF,KAAA;YACA,EAAA5G,IAAA;UACA;UACAoC,QAAA;QACA,GACA;UACA/R,IAAA;UACA8J,IAAA;UACA7J,IAAA,OAAA4D,mBAAA,CAAAW,GAAA,WAAAsK,IAAA;YAAA,OAAAA,IAAA,CAAA0H,SAAA;UAAA;UACAzE,QAAA;UACAH,SAAA;YACA1O,KAAA;YACAwT,OAAA;UACA;QACA,EACA;QACA/B,MAAA;UACA1U,IAAA;UACA8P,MAAA;QACA;MACA;MAEArB,KAAA,CAAA2D,SAAA,CAAAlD,MAAA;IACA;IAEA;IACAwH,gBAAA,WAAAA,iBAAAF,KAAA,EAAAG,KAAA;MACA;MACA,IAAA/E,QAAA,GAAA4E,KAAA,IAAAG,KAAA;;MAEA;MACA,IAAAC,UAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MAEA;MACA;MACA,IAAAC,YAAA,GAAAD,UAAA,CAAAlS,MAAA;MACA,IAAAoS,OAAA,GAAAhR,IAAA,CAAA/B,GAAA,CAAA+B,IAAA,CAAAiR,KAAA,CAAAnF,QAAA,GAAAiF,YAAA,GAAAA,YAAA;MACA,IAAAG,aAAA,GAAApF,QAAA,GAAAiF,YAAA,GAAAC,OAAA;;MAEA;MACA,IAAAG,UAAA,GAAAL,UAAA,CAAAE,OAAA;MACA,IAAAI,QAAA,GAAAN,UAAA,CAAAE,OAAA;;MAEA;MACA,IAAAK,OAAA,QAAAC,iBAAA,CAAAH,UAAA,EAAAC,QAAA,EAAAF,aAAA;;MAEA;MACA;MACA,IAAAvB,CAAA,GAAAC,QAAA,CAAAyB,OAAA,CAAAjL,KAAA;MACA,IAAAyJ,CAAA,GAAAD,QAAA,CAAAyB,OAAA,CAAAjL,KAAA;MACA,IAAA5G,CAAA,GAAAoQ,QAAA,CAAAyB,OAAA,CAAAjL,KAAA;MACA,IAAAmL,UAAA,IAAA5B,CAAA,SAAAE,CAAA,SAAArQ,CAAA;;MAEA;MACA,IAAAgS,SAAA,GAAAD,UAAA;;MAEA;MACA,IAAAhC,WAAA,QAAAkC,WAAA,CAAAJ,OAAA;MAEA;QACA9D,eAAA,EAAA8D,OAAA;QACAlU,KAAA,EAAAqU,SAAA;QACAE,cAAA,EAAAnC;MACA;IACA;IAEA;IACA+B,iBAAA,WAAAA,kBAAAK,MAAA,EAAAC,MAAA,EAAAC,MAAA;MACA;MACA,IAAAC,EAAA,GAAAlC,QAAA,CAAA+B,MAAA,CAAAvL,KAAA;MACA,IAAA2L,EAAA,GAAAnC,QAAA,CAAA+B,MAAA,CAAAvL,KAAA;MACA,IAAA4L,EAAA,GAAApC,QAAA,CAAA+B,MAAA,CAAAvL,KAAA;MAEA,IAAA6L,EAAA,GAAArC,QAAA,CAAAgC,MAAA,CAAAxL,KAAA;MACA,IAAA8L,EAAA,GAAAtC,QAAA,CAAAgC,MAAA,CAAAxL,KAAA;MACA,IAAA+L,EAAA,GAAAvC,QAAA,CAAAgC,MAAA,CAAAxL,KAAA;;MAEA;MACA,IAAAuJ,CAAA,GAAA3P,IAAA,CAAAoS,KAAA,CAAAN,EAAA,GAAAD,MAAA,IAAAI,EAAA,GAAAH,EAAA;MACA,IAAAjC,CAAA,GAAA7P,IAAA,CAAAoS,KAAA,CAAAL,EAAA,GAAAF,MAAA,IAAAK,EAAA,GAAAH,EAAA;MACA,IAAAvS,CAAA,GAAAQ,IAAA,CAAAoS,KAAA,CAAAJ,EAAA,GAAAH,MAAA,IAAAM,EAAA,GAAAH,EAAA;;MAEA;MACA,WAAAlL,MAAA,CAAA6I,CAAA,CAAA0C,QAAA,KAAAC,QAAA,UAAAxL,MAAA,CAAA+I,CAAA,CAAAwC,QAAA,KAAAC,QAAA,UAAAxL,MAAA,CAAAtH,CAAA,CAAA6S,QAAA,KAAAC,QAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA7B,KAAA,EAAAG,KAAA;MACA;MACA,IAAA/E,QAAA,GAAA4E,KAAA,IAAAG,KAAA;MACA,IAAAC,UAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;MAEA,IAAAC,YAAA,GAAAD,UAAA,CAAAlS,MAAA;MACA,IAAAoS,OAAA,GAAAhR,IAAA,CAAA/B,GAAA,CAAA+B,IAAA,CAAAiR,KAAA,CAAAnF,QAAA,GAAAiF,YAAA,GAAAA,YAAA;MACA,IAAAG,aAAA,GAAApF,QAAA,GAAAiF,YAAA,GAAAC,OAAA;MAEA,IAAAG,UAAA,GAAAL,UAAA,CAAAE,OAAA;MACA,IAAAI,QAAA,GAAAN,UAAA,CAAAE,OAAA;MAEA,IAAAK,OAAA,QAAAC,iBAAA,CAAAH,UAAA,EAAAC,QAAA,EAAAF,aAAA;;MAEA;MACA,IAAAvB,CAAA,GAAAC,QAAA,CAAAyB,OAAA,CAAAjL,KAAA;MACA,IAAAyJ,CAAA,GAAAD,QAAA,CAAAyB,OAAA,CAAAjL,KAAA;MACA,IAAA5G,CAAA,GAAAoQ,QAAA,CAAAyB,OAAA,CAAAjL,KAAA;MACA,IAAAmL,UAAA,IAAA5B,CAAA,SAAAE,CAAA,SAAArQ,CAAA;;MAEA;MACA,IAAA+R,UAAA;QACA;QACA,IAAAiB,YAAA,QAAAf,WAAA,CAAAJ,OAAA;QACA;UACA9D,eAAA,EAAAiF,YAAA;UACArV,KAAA;UACAsV,SAAA;QACA;MACA;QACA;QACA;UACAlF,eAAA;UACApQ,KAAA;UACAsV,SAAA;QACA;MACA;IACA;IAEA;IACAhB,WAAA,WAAAA,YAAAtU,KAAA,EAAAuV,MAAA;MACA;MACA,IAAAvV,KAAA;QACA;MACA;;MAEA;MACA,IAAAwS,CAAA,GAAAC,QAAA,CAAAzS,KAAA,CAAAiJ,KAAA;MACA,IAAAyJ,CAAA,GAAAD,QAAA,CAAAzS,KAAA,CAAAiJ,KAAA;MACA,IAAA5G,CAAA,GAAAoQ,QAAA,CAAAzS,KAAA,CAAAiJ,KAAA;;MAEA;MACAuJ,CAAA,GAAA3P,IAAA,CAAA9B,GAAA,IAAA8B,IAAA,CAAA/B,GAAA,MAAA0R,CAAA,GAAA+C,MAAA;MACA7C,CAAA,GAAA7P,IAAA,CAAA9B,GAAA,IAAA8B,IAAA,CAAA/B,GAAA,MAAA4R,CAAA,GAAA6C,MAAA;MACAlT,CAAA,GAAAQ,IAAA,CAAA9B,GAAA,IAAA8B,IAAA,CAAA/B,GAAA,MAAAuB,CAAA,GAAAkT,MAAA;;MAEA;MACA,WAAA5L,MAAA,CAAA6I,CAAA,CAAA0C,QAAA,KAAAC,QAAA,UAAAxL,MAAA,CAAA+I,CAAA,CAAAwC,QAAA,KAAAC,QAAA,UAAAxL,MAAA,CAAAtH,CAAA,CAAA6S,QAAA,KAAAC,QAAA;IACA;IAEA;IACAhQ,wBAAA,WAAAA,yBAAA;MAAA,IAAAqQ,OAAA;MACA;MACA,SAAA9X,yBAAA;QACAkI,aAAA,MAAAlI,yBAAA;MACA;MAEA,IAAA+X,SAAA,QAAA/Q,KAAA,CAAAW,uBAAA;MACA,KAAAoQ,SAAA;;MAEA;MACA,IAAAvK,SAAA;MACA,IAAAC,YAAA,GAAAsK,SAAA,CAAAtK,YAAA;MACA,IAAAuK,YAAA,GAAAD,SAAA,CAAAC,YAAA;MACA,IAAAC,SAAA,GAAAxK,YAAA,GAAAuK,YAAA;;MAEA;MACA,IAAAC,SAAA;;MAEA;MACA,IAAAjG,IAAA;MACA,IAAAkG,cAAA;;MAEA,KAAAlY,yBAAA,GAAAyM,WAAA;QACA;QACA,IAAAqL,OAAA,CAAA3X,0BAAA;QAEAqN,SAAA,IAAAwE,IAAA;;QAEA;QACA,IAAAxE,SAAA,IAAAyK,SAAA;UACA;UACAzK,SAAA;UACAuK,SAAA,CAAAvK,SAAA;;UAEA;UACAsK,OAAA,CAAA3X,0BAAA;UACAgY,UAAA;YACAL,OAAA,CAAA3X,0BAAA;UACA;QACA;UACA4X,SAAA,CAAAvK,SAAA,GAAAA,SAAA;QACA;MACA,GAAA0K,cAAA;IACA;IAEA;IACAE,eAAA,WAAAA,gBAAA;MACA;QACA1F,eAAA;QACApQ,KAAA;QACAwO,UAAA;MACA;IACA;IAEA;IACAuH,WAAA,WAAAA,YAAA7M,GAAA;MAAA,IAAA8M,OAAA;MACA,KAAA9Y,gBAAA,GAAAgM,GAAA;MACA,KAAAjM,mBAAA;MACA,KAAAqH,SAAA;QACA0R,OAAA,CAAAC,eAAA;MACA;IACA;IAEA;IACAA,eAAA,WAAAA,gBAAA;MACA,UAAA/Y,gBAAA;MAEA,IAAAmO,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,IAAA2K,OAAA,GAAA5Z,OAAA,CAAAmP,IAAA,CAAAJ,QAAA;MAEA,IAAA5L,MAAA;;MAEA;MACA,IAAA0W,WAAA,GAAAnU,UAAA,MAAA9E,gBAAA,CAAAwB,MAAA;;MAEA;MACA,IAAA0X,UAAA;MACA,SAAAlZ,gBAAA,CAAAmM,WAAA,SAAAnM,gBAAA,CAAAmM,WAAA,CAAA5H,MAAA;QACA2U,UAAA,QAAAlZ,gBAAA,CAAAmM,WAAA;MACA;QACA;QACA,IAAAgN,gBAAA,GAAAF,WAAA;QACAC,UAAA,GAAA3W,MAAA,CAAA6B,GAAA;UACA,IAAAgV,WAAA,IAAAzT,IAAA,CAAA0T,MAAA,gBAAAF,gBAAA;UACA,OAAAxT,IAAA,CAAA9B,GAAA,IAAAoV,WAAA,GAAAG,WAAA;QACA;MACA;;MAEA;MACA,IAAAE,UAAA,GAAAnT,KAAA,IAAAmO,IAAA,CAAA2E,WAAA;MAEA,IAAAlK,MAAA;QACA9M,KAAA;UACA8Q,IAAA,KAAAtG,MAAA,MAAAzM,gBAAA,CAAAJ,IAAA;UACA6P,IAAA;QACA;QACAT,OAAA;UACAC,OAAA;UACAC,WAAA;YACAxF,IAAA;UACA;UACAyF,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAmK,WAAA,GAAAnK,MAAA,IAAAlN,KAAA,CAAA6C,OAAA;YACA,IAAAkU,WAAA,GAAA7J,MAAA,IAAAlN,KAAA,CAAA6C,OAAA;YACA,IAAAyU,IAAA,IAAAD,WAAA,GAAAN,WAAA,EAAAlU,OAAA;YACA;YACA,IAAA0U,SAAA;YAEA,UAAAhN,MAAA,CAAA2C,MAAA,IAAAxP,IAAA,gCAAA6M,MAAA,CACA2C,MAAA,IAAAsK,MAAA,OAAAjN,MAAA,CAAA2C,MAAA,IAAAuK,UAAA,QAAAlN,MAAA,CAAA8M,WAAA,gCAAA9M,MAAA,CACA2C,MAAA,IAAAsK,MAAA,OAAAjN,MAAA,CAAA2C,MAAA,IAAAuK,UAAA,QAAAlN,MAAA,CAAAwM,WAAA,8CAAAxM,MAAA,CACAgN,SAAA,uBAAAhN,MAAA,CAAA+M,IAAA;UACA;QACA;QACAjF,MAAA;UACA1U,IAAA;UACA8P,MAAA;QACA;QACAH,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAE,YAAA;QACA;QACAgB,KAAA;UACAnH,IAAA;UACAyJ,WAAA;UACAtT,IAAA,EAAA0C;QACA;QACA0O,KAAA;UACAvH,IAAA;UACA9J,IAAA,OAAAI,gBAAA,CAAA0B,IAAA;UACAwP,SAAA;YACA/B,SAAA;UACA;UACAuE,KAAA;QACA;QACAnC,MAAA,GACA;UACA3R,IAAA;UACA8J,IAAA;UACA7J,IAAA,EAAAqZ,UAAA;UACA1H,SAAA;YACA1O,KAAA;UACA;UACAiO,SAAA;YACAZ,KAAA;UACA;UACAkD,MAAA;UACAC,UAAA;UACAsG,SAAA;YACA/Z,IAAA,GACA;cAAA6J,IAAA;cAAA9J,IAAA;YAAA,GACA;cAAA8J,IAAA;cAAA9J,IAAA;YAAA;UAEA;QACA,GACA;UACAA,IAAA;UACA8J,IAAA;UACA7J,IAAA,EAAAyZ,UAAA;UACA9H,SAAA;YACA1O,KAAA;UACA;UACAiO,SAAA;YACAZ,KAAA;YACAzG,IAAA;UACA;UACAuK,QAAA;YACApU,IAAA;cAAA6J,IAAA;cAAA9J,IAAA;YAAA;YACAiD,KAAA;cACAsM,SAAA;YACA;UACA;QACA;MAEA;MAEA6J,OAAA,CAAA/G,SAAA,CAAAlD,MAAA;;MAEA;MACA1G,MAAA,CAAAD,gBAAA;QACA4Q,OAAA,CAAA7G,MAAA;MACA;IACA;IAEA;IACA0H,iBAAA,WAAAA,kBAAA;MACA,KAAA9Z,mBAAA;MACA,KAAAC,gBAAA;MACA;MACA,IAAAmO,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,IAAAF,QAAA;QACA,IAAAG,KAAA,GAAAlP,OAAA,CAAAsV,gBAAA,CAAAvG,QAAA;QACA,IAAAG,KAAA;UACAA,KAAA,CAAAqG,OAAA;QACA;MACA;IACA;IACA;IACAmF,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAA/N,GAAA,GAAA+N,IAAA,CAAA/N,GAAA;QAAAgO,MAAA,GAAAD,IAAA,CAAAC,MAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;QAAAC,WAAA,GAAAH,IAAA,CAAAG,WAAA;MACA;MACA,IAAAA,WAAA,UAAAA,WAAA;QACA;QACA,IAAAC,UAAA,QAAAra,cAAA,CAAAma,QAAA;QACA,KAAAE,UAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;;QAEA;QACA,IAAAH,WAAA;UACA;UACA,IACAD,QAAA,UACAE,UAAA,CAAAjP,OAAA,UAAApL,cAAA,CAAAma,QAAA,MAAA/O,OAAA,EACA;YACA;YACA,IAAAkP,OAAA;YACA,SAAAE,CAAA,GAAAL,QAAA,MAAAK,CAAA,QAAAxa,cAAA,CAAAyE,MAAA,EAAA+V,CAAA;cACA,SAAAxa,cAAA,CAAAwa,CAAA,EAAApP,OAAA,KAAAiP,UAAA,CAAAjP,OAAA;gBACAkP,OAAA;cACA;gBACA;cACA;YACA;YACA;cAAAA,OAAA,EAAAA,OAAA;cAAAC,OAAA;YAAA;UACA;YACA;YACA;cAAAD,OAAA;cAAAC,OAAA;YAAA;UACA;QACA;;QAEA;QACA,IAAAH,WAAA;UACA;UACA,IACAD,QAAA,UACAE,UAAA,CAAAjP,OAAA,UAAApL,cAAA,CAAAma,QAAA,MAAA/O,OAAA,IACAiP,UAAA,CAAAva,IAAA,UAAAE,cAAA,CAAAma,QAAA,MAAAra,IAAA,EACA;YACA;YACA,IAAAwa,QAAA;YACA,SAAAE,EAAA,GAAAL,QAAA,MAAAK,EAAA,QAAAxa,cAAA,CAAAyE,MAAA,EAAA+V,EAAA;cACA,IACA,KAAAxa,cAAA,CAAAwa,EAAA,EAAApP,OAAA,KAAAiP,UAAA,CAAAjP,OAAA,IACA,KAAApL,cAAA,CAAAwa,EAAA,EAAA1a,IAAA,KAAAua,UAAA,CAAAva,IAAA,EACA;gBACAwa,QAAA;cACA;gBACA;cACA;YACA;YACA;cAAAA,OAAA,EAAAA,QAAA;cAAAC,OAAA;YAAA;UACA;YACA;YACA;cAAAD,OAAA;cAAAC,OAAA;YAAA;UACA;QACA;MACA;MACA;QAAAD,OAAA;QAAAC,OAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}