package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出门证任务物资对象 leave_task_material
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public class  LeaveTaskMaterial extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 任务号 */
    @Excel(name = "任务号")
    private String taskNo;

    /** 物资id */
    @Excel(name = "物资id")
    private Long materialId;

    /** 物资编号 吨钢承包类物资 */
    @Excel(name = "物资编号 吨钢承包类物资")
    private String materialNo;

    /** 物资类型 1-普通 2-吨钢 */
    @Excel(name = "物资类型 1-普通 2-吨钢")
    private Integer materialType;

    /** 物资名称 */
    @Excel(name = "物资名称")
    private String materialName;

    /** 物资型号规格 */
    @Excel(name = "物资型号规格")
    private String materialSpec;

    /** 计量单位 */
    @Excel(name = "计量单位")
    private String measureUnit;

    /** 是否计量 计量-1 不计量-0 */
    @Excel(name = "是否计量 计量-1 不计量-0")
    private Integer measureFlag;

    /** 计划数量 */
    @Excel(name = "计划数量")
    private BigDecimal planNum;

    /** 门卫接收数量 */
    @Excel(name = "门卫接收数量")
    private Long doormanReceiveNum;

    /** 分厂接收数量 */
    @Excel(name = "分厂接收数量")
    private Long factoryReceiveNum;



    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTaskNo(String taskNo) 
    {
        this.taskNo = taskNo;
    }

    public String getTaskNo() 
    {
        return taskNo;
    }
    public void setMaterialId(Long materialId) 
    {
        this.materialId = materialId;
    }

    public Long getMaterialId() 
    {
        return materialId;
    }
    public void setMaterialNo(String materialNo) 
    {
        this.materialNo = materialNo;
    }

    public String getMaterialNo() 
    {
        return materialNo;
    }
    public void setMaterialType(Integer materialType) 
    {
        this.materialType = materialType;
    }

    public Integer getMaterialType() 
    {
        return materialType;
    }
    public void setMaterialName(String materialName) 
    {
        this.materialName = materialName;
    }

    public String getMaterialName() 
    {
        return materialName;
    }
    public void setMaterialSpec(String materialSpec) 
    {
        this.materialSpec = materialSpec;
    }

    public String getMaterialSpec() 
    {
        return materialSpec;
    }
    public void setMeasureUnit(String measureUnit) 
    {
        this.measureUnit = measureUnit;
    }

    public String getMeasureUnit() 
    {
        return measureUnit;
    }
    public void setMeasureFlag(Integer measureFlag) 
    {
        this.measureFlag = measureFlag;
    }

    public Integer getMeasureFlag() 
    {
        return measureFlag;
    }
    public void setPlanNum(BigDecimal planNum) 
    {
        this.planNum = planNum;
    }

    public BigDecimal getPlanNum() 
    {
        return planNum;
    }
    public void setDoormanReceiveNum(Long doormanReceiveNum) 
    {
        this.doormanReceiveNum = doormanReceiveNum;
    }

    public Long getDoormanReceiveNum() 
    {
        return doormanReceiveNum;
    }
    public void setFactoryReceiveNum(Long factoryReceiveNum) 
    {
        this.factoryReceiveNum = factoryReceiveNum;
    }

    public Long getFactoryReceiveNum() 
    {
        return factoryReceiveNum;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskNo", getTaskNo())
            .append("materialId", getMaterialId())
            .append("materialNo", getMaterialNo())
            .append("materialType", getMaterialType())
            .append("materialName", getMaterialName())
            .append("materialSpec", getMaterialSpec())
            .append("measureUnit", getMeasureUnit())
            .append("measureFlag", getMeasureFlag())
            .append("planNum", getPlanNum())
            .append("doormanReceiveNum", getDoormanReceiveNum())
            .append("factoryReceiveNum", getFactoryReceiveNum())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
