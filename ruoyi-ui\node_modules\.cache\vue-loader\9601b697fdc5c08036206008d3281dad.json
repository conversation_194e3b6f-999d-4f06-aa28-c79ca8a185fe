{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\index.vue", "mtime": 1756170476875}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UHVuaXNobWVudCwgZ2V0UHVuaXNobWVudCwgZGVsUHVuaXNobWVudCwgYWRkUHVuaXNobWVudCwgdXBkYXRlUHVuaXNobWVudCwgZXhwb3J0UHVuaXNobWVudCwgY29uZmlybVB1bmlzaG1lbnQsIGdldFVzZXJDb21wYW55LCBnZXRVc2VyR3JvdXAgfSBmcm9tICJAL2FwaS9zdXBwUHVuaXNobWVudC9wdW5pc2htZW50IjsNCmltcG9ydCB7IGdldERlcE5hbWVMaXN0IH0gZnJvbSAiQC9hcGkvcHVyY2hhc2UvcHVyZGNoYXNlRmFjdG9yeVN0b2NrIjsNCmltcG9ydCB7IGFkZERhdGVSYW5nZSB9IGZyb20gIkAvdXRpbHMvcnVveWkiOw0KaW1wb3J0IFN1cHBJbmZvRGlhbG9nIGZyb20gIi4vc3VwcEluZm8tbW9kdWxlLnZ1ZSI7DQppbXBvcnQgTWF0ZXJpYWxJbmZvRGlhbG9nIGZyb20gIi4vbWF0ZXJpYWxJbmZvLW1vZHVsZS52dWUiOw0KaW1wb3J0IFNlcnZpY2VQcm9qZWN0RGlhbG9nIGZyb20gIi4vc2VydmljZS1tb2R1bGUudnVlIjsNCmltcG9ydCBQcm9qZWN0RGlhbG9nIGZyb20gIi4vcHJvamVjdC1tb2R1bGUudnVlIjsNCmltcG9ydCBQdW5pc2htZW50TWVhc3VyZURpYWxvZyBmcm9tICIuL3B1bmlzaG1lbnRNZWFzdXJlLW1vZHVsZS52dWUiOw0KaW1wb3J0IFB1bmlzaG1lbnRCYXNpc0RpYWxvZyBmcm9tICIuL3B1bmlzaG1lbnRCYXNpcy1tb2R1bGUudnVlIjsNCg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJQdW5pc2htZW50IiwNCiAgY29tcG9uZW50czogew0KICAgIFN1cHBJbmZvRGlhbG9nLA0KICAgIE1hdGVyaWFsSW5mb0RpYWxvZywNCiAgICBTZXJ2aWNlUHJvamVjdERpYWxvZywNCiAgICBQcm9qZWN0RGlhbG9nLA0KICAgIFB1bmlzaG1lbnRNZWFzdXJlRGlhbG9nLA0KICAgIFB1bmlzaG1lbnRCYXNpc0RpYWxvZw0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5L6b5bqU5ZWG5aSE572a6K6w5b2V6KGo5qC85pWw5o2uDQogICAgICBwdW5pc2htZW50TGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbkuLrmn6XnnIvmqKHlvI8NCiAgICAgIGlzVmlld01vZGU6IGZhbHNlLA0KICAgICAgLy8g5b2T5YmN6YCJ5oup57G75Z6L77yaZm9ybS3ooajljZXpgInmi6nvvIxxdWVyeS3mn6Xor6LmnaHku7bpgInmi6kNCiAgICAgIGN1cnJlbnRTZWxlY3RUeXBlOiAnZm9ybScsDQogICAgICAvLyDnlKjmiLfliIbnu4TmnYPpmZANCiAgICAgIHVzZXJHcm91cDogJycsDQogICAgICAvLyDmnYPpmZDmjqfliLYNCiAgICAgIHBlcm1pc3Npb25zOiB7DQogICAgICAgIGNhbkFkZDogdHJ1ZSwgICAgICAvLyDmlrDlop7mnYPpmZANCiAgICAgICAgY2FuRWRpdDogdHJ1ZSwgICAgIC8vIOS/ruaUueadg+mZkA0KICAgICAgICBjYW5EZWxldGU6IHRydWUsICAgLy8g5Yig6Zmk5p2D6ZmQDQogICAgICAgIGNhbkNvbmZpcm06IHRydWUsICAvLyDnoa7orqTmnYPpmZANCiAgICAgICAgY2FuRXhwb3J0OiB0cnVlICAgIC8vIOWvvOWHuuadg+mZkA0KICAgICAgfSwNCiAgICAgIC8vIOWkhOe9muexu+Wei+aVsOaNruWtl+WFuA0KICAgICAgcHVuaXNobWVudFR5cGVPcHRpb25zOiBbXSwNCiAgICAgIC8vIOeKtuaAgeaVsOaNruWtl+WFuA0KICAgICAgc3RhdHVzT3B0aW9uczogW10sDQogICAgICAvLyDnianotYTmiJbmnI3liqHpgInpobkNCiAgICAgIHN1cHBUeXBlT3B0aW9uczogWw0KICAgICAgICB7IHZhbHVlOiAnTScsIGxhYmVsOiAn54mp6LWEJyB9LA0KICAgICAgICB7IHZhbHVlOiAnUycsIGxhYmVsOiAn5pyN5YqhJyB9LA0KICAgICAgICB7IHZhbHVlOiAnUCcsIGxhYmVsOiAn5bel56iLJyB9DQogICAgICBdLA0KICAgICAgLy8g5pyN5Yqh6YOo6Zeo5YiX6KGoDQogICAgICBnZXREZXBOYW1lTGlzdDogW10sDQogICAgICAvLyDkuovku7blj5HnlJ/ml7bpl7TojIPlm7QNCiAgICAgIGhhcHBlbmVkVGltZVJhbmdlOiBbXSwNCiAgICAgIC8vIOWkhOe9muaJp+ihjOaXtumXtOiMg+WbtA0KICAgICAgcHVuaXNobWVudFRpbWVSYW5nZTogW10sDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcmVjQ3JlYXRvcjogbnVsbCwNCiAgICAgICAgcmVjQ3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgcmVjUmV2aXNvcjogbnVsbCwNCiAgICAgICAgcmVjUmV2aXNlVGltZTogbnVsbCwNCiAgICAgICAgdXNlck5hbWU6IG51bGwsDQogICAgICAgIHNlcmlhbE5vOiBudWxsLA0KICAgICAgICBjb21wYW55Q29kZTogbnVsbCwNCiAgICAgICAgZGVwdE5vOiBudWxsLA0KICAgICAgICBzdXBwSWQ6IG51bGwsDQogICAgICAgIHN1cHBOYW1lOiBudWxsLA0KICAgICAgICBpdGVtTm86IG51bGwsDQogICAgICAgIGl0ZW1OYW1lOiBudWxsLA0KICAgICAgICBzdXBwVHlwZTogbnVsbCwNCiAgICAgICAgcHVuaXNobWVudFR5cGU6IG51bGwsDQogICAgICAgIHN0YXRlSWQ6IG51bGwsDQogICAgICAgIHB1bmlzaG1lbnRSZWFzb246IG51bGwsDQogICAgICAgIHB1bmlzaG1lbnRCYXNpczogbnVsbCwNCiAgICAgICAgcHVuaXNobWVudE1lYXN1cmU6IG51bGwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g5aSE572a5o6q5pa95qCH562+5pWw57uEDQogICAgICBwdW5pc2htZW50TWVhc3VyZVRhZ3M6IFtdLA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBzdXBwSWQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fovpPlhaXkvpvlupTllYbku6PnoIEiIH0NCiAgICAgICAgXSwNCiAgICAgICAgc3VwcE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fovpPlhaXkvpvlupTllYblkI3np7AiIH0NCiAgICAgICAgXSwNCiAgICAgICAgc3VwcFR5cGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiY2hhbmdlIiwgbWVzc2FnZTogIuivt+mAieaLqeeJqei1hOaIluacjeWKoSIgfQ0KICAgICAgICBdLA0KICAgICAgICBwdW5pc2htZW50VHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJjaGFuZ2UiLCBtZXNzYWdlOiAi6K+36YCJ5oup5aSE572a57G75Z6LIiB9DQogICAgICAgIF0sDQogICAgICAgIGRlcHRObzogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJjaGFuZ2UiLCBtZXNzYWdlOiAi6K+36YCJ5oup55Sz6K+36YOo6ZeoIiB9DQogICAgICAgIF0sDQogICAgICAgIGl0ZW1ObzogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiBmYWxzZSwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICAgIHZhbGlkYXRvcjogKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgICAgICAgICAvLyDlj6rmnInnianotYTnsbvlnovmiY3lv4XloasNCiAgICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5zdXBwVHlwZSA9PT0gJ00nKSB7DQogICAgICAgICAgICAgICAgaWYgKCF2YWx1ZSB8fCB2YWx1ZS50cmltKCkgPT09ICcnKSB7DQogICAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpeeJqeaWmeWwj+exu+e8lueggScpKTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgLy8g5pyN5Yqh5ZKM5bel56iL57G75Z6L5LiN5qCh6aqM5b+F5aGrDQogICAgICAgICAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgaXRlbU5hbWU6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogZmFsc2UsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgICB2YWxpZGF0b3I6IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgICAgICAgICAgLy8g5Y+q5pyJ54mp6LWE57G75Z6L5omN5b+F5aGrDQogICAgICAgICAgICAgIGlmICh0aGlzLmZvcm0uc3VwcFR5cGUgPT09ICdNJykgew0KICAgICAgICAgICAgICAgIGlmICghdmFsdWUgfHwgdmFsdWUudHJpbSgpID09PSAnJykgew0KICAgICAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfor7fovpPlhaXnianmlpnlsI/nsbvlkI3np7AnKSk7DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIC8vIOacjeWKoeWSjOW3peeoi+exu+Wei+S4jeagoemqjOW/heWhqw0KICAgICAgICAgICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIGhhcHBlbmVkVGltZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJjaGFuZ2UiLCBtZXNzYWdlOiAi6K+36YCJ5oup5LqL5Lu25Y+R55Sf5pe26Ze0IiB9DQogICAgICAgIF0sDQogICAgICAgIHB1bmlzaG1lbnRUaW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHJpZ2dlcjogImNoYW5nZSIsIG1lc3NhZ2U6ICLor7fpgInmi6nlpITnvZrmiafooYzml7bpl7QiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcHVuaXNobWVudFJlYXNvbjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHRyaWdnZXI6ICJibHVyIiwgbWVzc2FnZTogIuivt+i+k+WFpeWkhOe9muS6i+eUsSIgfQ0KICAgICAgICBdLA0KICAgICAgICBwdW5pc2htZW50QmFzaXM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fpgInmi6nlpITnvZrkvp3mja4iIH0NCiAgICAgICAgXSwNCiAgICAgICAgcHVuaXNobWVudE1lYXN1cmU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciIsIG1lc3NhZ2U6ICLor7fpgInmi6nlpITnvZrmjqrmlr0iIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXREaWN0RGF0YSgpOw0KICAgIHRoaXMuZ2V0RGVwdExpc3QoKTsNCiAgICB0aGlzLmdldFVzZXJHcm91cFBlcm1pc3Npb25zKCk7DQogICAgLy8g5LiN5Zyo6L+Z6YeM6LCD55SoZ2V0TGlzdCgp77yM6ICM5piv5Zyo6K6+572u6buY6K6k5YC85ZCO6LCD55SoDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDmt7vliqDml6XmnJ/ojIPlm7QNCiAgICBhZGREYXRlUmFuZ2UsDQogICAgLy8g6I635Y+W5a2X5YW45pWw5o2uDQogICAgZ2V0RGljdERhdGEoKSB7DQogICAgICAvLyDojrflj5blpITnvZrnsbvlnosNCiAgICAgIHRoaXMuZ2V0RGljdHMoInN1cHBfcHVuaXNobWVudF90eXBlIikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5wdW5pc2htZW50VHlwZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgICAgfSk7DQogICAgICAvLyDojrflj5bnirbmgIEgLSDlpoLmnpzlrZflhbjkuI3lrZjlnKjvvIzkvb/nlKjmiYvliqjlrprkuYnnmoTnirbmgIENCiAgICAgIHRoaXMuZ2V0RGljdHMoInN1cHBfcHVuaXNobWVudF9zdGF0dXMiKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5p+l6K+i5L6b5bqU5ZWG5aSE572a6K6w5b2V5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQoNCiAgICAgIGNvbnNvbGUubG9nKCfkuovku7blj5HnlJ/ml7bpl7TojIPlm7Q6JywgdGhpcy5oYXBwZW5lZFRpbWVSYW5nZSk7DQogICAgICBjb25zb2xlLmxvZygn5aSE572a5omn6KGM5pe26Ze06IyD5Zu0OicsIHRoaXMucHVuaXNobWVudFRpbWVSYW5nZSk7DQoNCiAgICAgIC8vIOaJi+WKqOaehOW7uuaXtumXtOiMg+WbtOWPguaVsA0KICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9Ow0KICAgICAgaWYgKCFwYXJhbXMucGFyYW1zKSB7DQogICAgICAgIHBhcmFtcy5wYXJhbXMgPSB7fTsNCiAgICAgIH0NCg0KICAgICAgLy8g5aSE55CG5LqL5Lu25Y+R55Sf5pe26Ze06IyD5Zu0DQogICAgICBpZiAodGhpcy5oYXBwZW5lZFRpbWVSYW5nZSAmJiB0aGlzLmhhcHBlbmVkVGltZVJhbmdlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICBwYXJhbXMucGFyYW1zWyJoYXBwZW5lZFRpbWVCZWdpblRpbWUiXSA9IHRoaXMuaGFwcGVuZWRUaW1lUmFuZ2VbMF07DQogICAgICAgIHBhcmFtcy5wYXJhbXNbImhhcHBlbmVkVGltZUVuZFRpbWUiXSA9IHRoaXMuaGFwcGVuZWRUaW1lUmFuZ2VbMV07DQogICAgICB9DQoNCiAgICAgIC8vIOWkhOeQhuWkhOe9muaJp+ihjOaXtumXtOiMg+WbtA0KICAgICAgaWYgKHRoaXMucHVuaXNobWVudFRpbWVSYW5nZSAmJiB0aGlzLnB1bmlzaG1lbnRUaW1lUmFuZ2UubGVuZ3RoID09PSAyKSB7DQogICAgICAgIHBhcmFtcy5wYXJhbXNbInB1bmlzaG1lbnRUaW1lQmVnaW5UaW1lIl0gPSB0aGlzLnB1bmlzaG1lbnRUaW1lUmFuZ2VbMF07DQogICAgICAgIHBhcmFtcy5wYXJhbXNbInB1bmlzaG1lbnRUaW1lRW5kVGltZSJdID0gdGhpcy5wdW5pc2htZW50VGltZVJhbmdlWzFdOw0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZygn5pyA57uI5Y+C5pWwOicsIHBhcmFtcyk7DQoNCiAgICAgIGxpc3RQdW5pc2htZW50KHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucHVuaXNobWVudExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmn6Xor6LmnI3liqHpg6jpl6jliJfooaggKi8NCiAgICBnZXREZXB0TGlzdCgpIHsNCiAgICAgIGdldERlcE5hbWVMaXN0KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZ2V0RGVwTmFtZUxpc3QgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAvLyDnm7TmjqXmiafooYzmn6Xor6LvvIzkuI3orr7nva7pu5jorqTnlLPor7fpg6jpl6gNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlueUqOaIt+WIhue7hOadg+mZkCAqLw0KICAgIGdldFVzZXJHcm91cFBlcm1pc3Npb25zKCkgew0KICAgICAgZ2V0VXNlckdyb3VwKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMudXNlckdyb3VwID0gcmVzcG9uc2UuZGF0YS51c2VyR3JvdXA7DQogICAgICAgICAgdGhpcy5zZXRQZXJtaXNzaW9ucygpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOm7mOiupOiuvue9ruS4uuafpemYhee7hOadg+mZkO+8iOacgOS4peagvO+8iQ0KICAgICAgICAgIHRoaXMudXNlckdyb3VwID0gJ3F1ZXJ5JzsNCiAgICAgICAgICB0aGlzLnNldFBlcm1pc3Npb25zKCk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55So5oi35YiG57uE5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgLy8g6buY6K6k6K6+572u5Li65p+l6ZiF57uE5p2D6ZmQ77yI5pyA5Lil5qC877yJDQogICAgICAgIHRoaXMudXNlckdyb3VwID0gJ3F1ZXJ5JzsNCiAgICAgICAgdGhpcy5zZXRQZXJtaXNzaW9ucygpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmoLnmja7nlKjmiLfliIbnu4Torr7nva7mnYPpmZAgKi8NCiAgICBzZXRQZXJtaXNzaW9ucygpIHsNCiAgICAgIHN3aXRjaCAodGhpcy51c2VyR3JvdXApIHsNCiAgICAgICAgY2FzZSAnaW5wdXQnOg0KICAgICAgICAgIC8vIOWhq+aKpee7hO+8mumakOiXj+ehruiupOOAgeWvvOWHuuaMiemSrg0KICAgICAgICAgIHRoaXMucGVybWlzc2lvbnMgPSB7DQogICAgICAgICAgICBjYW5BZGQ6IHRydWUsDQogICAgICAgICAgICBjYW5FZGl0OiB0cnVlLA0KICAgICAgICAgICAgY2FuRGVsZXRlOiB0cnVlLA0KICAgICAgICAgICAgY2FuQ29uZmlybTogZmFsc2UsDQogICAgICAgICAgICBjYW5FeHBvcnQ6IGZhbHNlDQogICAgICAgICAgfTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAnY29uZmlybSc6DQogICAgICAgICAgLy8g56Gu6K6k57uE77ya6ZqQ6JeP5a+85Ye65oyJ6ZKuDQogICAgICAgICAgdGhpcy5wZXJtaXNzaW9ucyA9IHsNCiAgICAgICAgICAgIGNhbkFkZDogdHJ1ZSwNCiAgICAgICAgICAgIGNhbkVkaXQ6IHRydWUsDQogICAgICAgICAgICBjYW5EZWxldGU6IHRydWUsDQogICAgICAgICAgICBjYW5Db25maXJtOiB0cnVlLA0KICAgICAgICAgICAgY2FuRXhwb3J0OiBmYWxzZQ0KICAgICAgICAgIH07DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ3F1ZXJ5JzoNCiAgICAgICAgICAvLyDmn6XpmIXnu4TvvJrpmpDol4/mlrDlop7jgIHkv67mlLnjgIHnoa7orqTjgIHliKDpmaTmjInpkq4NCiAgICAgICAgICB0aGlzLnBlcm1pc3Npb25zID0gew0KICAgICAgICAgICAgY2FuQWRkOiBmYWxzZSwNCiAgICAgICAgICAgIGNhbkVkaXQ6IGZhbHNlLA0KICAgICAgICAgICAgY2FuRGVsZXRlOiBmYWxzZSwNCiAgICAgICAgICAgIGNhbkNvbmZpcm06IGZhbHNlLA0KICAgICAgICAgICAgY2FuRXhwb3J0OiB0cnVlDQogICAgICAgICAgfTsNCiAgICAgICAgICAvLyDorr7nva7mn6XpmIXnu4Tpu5jorqTnirbmgIHkuLoi56Gu6K6kIg0KICAgICAgICAgIHRoaXMuc2V0UXVlcnlHcm91cERlZmF1bHRzKCk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ21hbmFnZSc6DQogICAgICAgICAgLy8g566h55CG57uE77ya5YW35aSH5omA5pyJ5Yqf6IO9DQogICAgICAgICAgdGhpcy5wZXJtaXNzaW9ucyA9IHsNCiAgICAgICAgICAgIGNhbkFkZDogdHJ1ZSwNCiAgICAgICAgICAgIGNhbkVkaXQ6IHRydWUsDQogICAgICAgICAgICBjYW5EZWxldGU6IHRydWUsDQogICAgICAgICAgICBjYW5Db25maXJtOiB0cnVlLA0KICAgICAgICAgICAgY2FuRXhwb3J0OiB0cnVlDQogICAgICAgICAgfTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICAvLyDpu5jorqTkuLrmn6XpmIXnu4TmnYPpmZANCiAgICAgICAgICB0aGlzLnBlcm1pc3Npb25zID0gew0KICAgICAgICAgICAgY2FuQWRkOiBmYWxzZSwNCiAgICAgICAgICAgIGNhbkVkaXQ6IGZhbHNlLA0KICAgICAgICAgICAgY2FuRGVsZXRlOiBmYWxzZSwNCiAgICAgICAgICAgIGNhbkNvbmZpcm06IGZhbHNlLA0KICAgICAgICAgICAgY2FuRXhwb3J0OiB0cnVlDQogICAgICAgICAgfTsNCiAgICAgICAgICAvLyDorr7nva7mn6XpmIXnu4Tpu5jorqTnirbmgIHkuLoi56Gu6K6kIg0KICAgICAgICAgIHRoaXMuc2V0UXVlcnlHcm91cERlZmF1bHRzKCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDorr7nva7mn6XpmIXnu4Tpu5jorqTlgLwgKi8NCiAgICBzZXRRdWVyeUdyb3VwRGVmYXVsdHMoKSB7DQogICAgICAvLyDorr7nva7nirbmgIHpu5jorqTkuLoi56Gu6K6kIu+8iOWAvOS4ujLvvIkNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhdGVJZCA9ICcyJzsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICByZWNDcmVhdG9yOiBudWxsLA0KICAgICAgICByZWNDcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICByZWNSZXZpc29yOiBudWxsLA0KICAgICAgICByZWNSZXZpc2VUaW1lOiBudWxsLA0KICAgICAgICB1c2VyTmFtZTogbnVsbCwNCiAgICAgICAgc2VyaWFsTm86IG51bGwsDQogICAgICAgIGNvbXBhbnlDb2RlOiBudWxsLA0KICAgICAgICBkZXB0Tm86IG51bGwsDQogICAgICAgIHN1cHBJZDogbnVsbCwNCiAgICAgICAgc3VwcE5hbWU6IG51bGwsDQogICAgICAgIGl0ZW1ObzogbnVsbCwNCiAgICAgICAgaXRlbU5hbWU6IG51bGwsDQogICAgICAgIHB1bmlzaG1lbnRUeXBlOiBudWxsLA0KICAgICAgICBzdXBwVHlwZTogbnVsbCwNCiAgICAgICAgaGFwcGVuZWRUaW1lOiBudWxsLA0KICAgICAgICBwdW5pc2htZW50VGltZTogbnVsbCwNCiAgICAgICAgcHVuaXNobWVudFJlYXNvbjogbnVsbCwNCiAgICAgICAgcHVuaXNobWVudEJhc2lzOiBudWxsLA0KICAgICAgICBwdW5pc2htZW50TWVhc3VyZTogbnVsbA0KICAgICAgfTsNCiAgICAgIC8vIOmHjee9ruWkhOe9muaOquaWveagh+etvg0KICAgICAgdGhpcy5wdW5pc2htZW50TWVhc3VyZVRhZ3MgPSBbXTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvLyDlpITnvZrnsbvlnovlrZflhbjnv7vor5ENCiAgICBwdW5pc2htZW50VHlwZUZvcm1hdChyb3csIGNvbHVtbikgew0KICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMucHVuaXNobWVudFR5cGVPcHRpb25zLCByb3cucHVuaXNobWVudFR5cGUpOw0KICAgIH0sDQogICAgLy8g54q25oCB5a2X5YW457+76K+RDQogICAgc3RhdGVGb3JtYXQocm93LCBjb2x1bW4pIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnN0YXR1c09wdGlvbnMsIHJvdy5zdGF0ZUlkKTsNCiAgICB9LA0KICAgIC8vIOaXpeacn+agvOW8j+WMlu+8muWwhjIwMjUwODAz6L2s5o2i5Li6MjAyNS0wOC0wMw0KICAgIGRhdGVGb3JtYXQocm93LCBjb2x1bW4sIGNlbGxWYWx1ZSkgew0KICAgICAgaWYgKCFjZWxsVmFsdWUpIHJldHVybiAnJzsNCiAgICAgIC8vIOWmguaenOW3sue7j+aYr+ato+ehruagvOW8j++8jOebtOaOpei/lOWbng0KICAgICAgaWYgKGNlbGxWYWx1ZS5pbmNsdWRlcygnLScpKSByZXR1cm4gY2VsbFZhbHVlOw0KICAgICAgLy8g5bCGMjAyNTA4MDPmoLzlvI/ovazmjaLkuLoyMDI1LTA4LTAzDQogICAgICBpZiAoY2VsbFZhbHVlLmxlbmd0aCA9PT0gOCkgew0KICAgICAgICBjb25zdCB5ZWFyID0gY2VsbFZhbHVlLnN1YnN0cmluZygwLCA0KTsNCiAgICAgICAgY29uc3QgbW9udGggPSBjZWxsVmFsdWUuc3Vic3RyaW5nKDQsIDYpOw0KICAgICAgICBjb25zdCBkYXkgPSBjZWxsVmFsdWUuc3Vic3RyaW5nKDYsIDgpOw0KICAgICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YDsNCiAgICAgIH0NCiAgICAgIHJldHVybiBjZWxsVmFsdWU7DQogICAgfSwNCiAgICAvLyDml6XmnJ/moLzlvI/ovazmjaLvvJrlsIYyMDI1MDgwM+i9rOaNouS4ujIwMjUvMDgvMDPvvIjnlKjkuo7ooajljZXlm57mmL7vvIkNCiAgICBjb252ZXJ0RGF0ZUZvcm1hdChkYXRlVmFsdWUpIHsNCiAgICAgIGlmICghZGF0ZVZhbHVlKSByZXR1cm4gJyc7DQogICAgICAvLyDlpoLmnpzlt7Lnu4/mmK/mraPnoa7moLzlvI/vvIznm7TmjqXov5Tlm54NCiAgICAgIGlmIChkYXRlVmFsdWUuaW5jbHVkZXMoJy8nKSB8fCBkYXRlVmFsdWUuaW5jbHVkZXMoJy0nKSkgcmV0dXJuIGRhdGVWYWx1ZTsNCiAgICAgIC8vIOWwhjIwMjUwODAz5qC85byP6L2s5o2i5Li6MjAyNS8wOC8wMw0KICAgICAgaWYgKGRhdGVWYWx1ZS5sZW5ndGggPT09IDgpIHsNCiAgICAgICAgY29uc3QgeWVhciA9IGRhdGVWYWx1ZS5zdWJzdHJpbmcoMCwgNCk7DQogICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZVZhbHVlLnN1YnN0cmluZyg0LCA2KTsNCiAgICAgICAgY29uc3QgZGF5ID0gZGF0ZVZhbHVlLnN1YnN0cmluZyg2LCA4KTsNCiAgICAgICAgcmV0dXJuIGAke3llYXJ9LyR7bW9udGh9LyR7ZGF5fWA7DQogICAgICB9DQogICAgICByZXR1cm4gZGF0ZVZhbHVlOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLmhhcHBlbmVkVGltZVJhbmdlID0gW107DQogICAgICB0aGlzLnB1bmlzaG1lbnRUaW1lUmFuZ2UgPSBbXTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCg0KICAgICAgLy8g5aaC5p6c5piv5p+l6ZiF57uE77yM6YeN572u5ZCO6ZyA6KaB6YeN5paw6K6+572u6buY6K6k54q25oCBDQogICAgICBpZiAodGhpcy51c2VyR3JvdXAgPT09ICdxdWVyeScpIHsNCiAgICAgICAgdGhpcy5zZXRRdWVyeUdyb3VwRGVmYXVsdHMoKTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMuaXNWaWV3TW9kZSA9IGZhbHNlOw0KICAgICAgLy8g6Ieq5Yqo5aGr5YWl5b2T5YmN55m75b2V55So5oi3DQogICAgICBpZiAodGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lKSB7DQogICAgICAgIC8vIOeri+WNs+iOt+WPlueZu+W9leS6uuS/oeaBrw0KICAgICAgICB0aGlzLmdldFVzZXJDb21wYW55SW5mbyh0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUpOw0KICAgICAgfQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5L6b5bqU5ZWG5aSE572a6K6w5b2VIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBsZXQgaWQ7DQoNCiAgICAgIGlmIChyb3cgJiYgcm93LmlkKSB7DQogICAgICAgIC8vIOWNleihjOS/ruaUue+8muS7juihqOagvOihjOaTjeS9nOaMiemSruiwg+eUqA0KICAgICAgICBpZiAocm93LnN0YXRlSWQgIT0gMSkgew0KICAgICAgICAgIHRoaXMubXNnRXJyb3IoIuWPquacieiNieeov+eKtuaAgeeahOiusOW9leaJjeiDveS/ruaUuSIpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZCA9IHJvdy5pZDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOaJuemHj+S/ruaUue+8muS7jumhtumDqOaMiemSruiwg+eUqA0KICAgICAgICBpZiAoIXRoaXMuaWRzIHx8IHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHRoaXMubXNnRXJyb3IoIuivt+mAieaLqeimgeS/ruaUueeahOiusOW9lSIpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID4gMSkgew0KICAgICAgICAgIHRoaXMubXNnRXJyb3IoIuS/ruaUueaTjeS9nOWPquiDvemAieaLqeS4gOadoeiusOW9lSIpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOagueaNrumAieS4reeahElE5p+l5om+5a+55bqU55qE6K6w5b2VDQogICAgICAgIGNvbnN0IHNlbGVjdGVkUm93ID0gdGhpcy5wdW5pc2htZW50TGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5pZCA9PT0gdGhpcy5pZHNbMF0pOw0KICAgICAgICBpZiAoIXNlbGVjdGVkUm93KSB7DQogICAgICAgICAgdGhpcy5tc2dFcnJvcigi5peg5rOV5om+5Yiw6YCJ5Lit55qE6K6w5b2VIik7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIGlmIChzZWxlY3RlZFJvdy5zdGF0ZUlkICE9IDEpIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCLlj6rmnInojYnnqL/nirbmgIHnmoTorrDlvZXmiY3og73kv67mlLkiKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgaWQgPSBzZWxlY3RlZFJvdy5pZDsNCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKCfkv67mlLnmk43kvZwgLSBpZDonLCBpZCk7DQogICAgICBjb25zb2xlLmxvZygn5L+u5pS55pON5L2cIC0gcm93OicsIHJvdyk7DQoNCiAgICAgIGlmICghaWQpIHsNCiAgICAgICAgdGhpcy5tc2dFcnJvcigi5peg5rOV6I635Y+W6K6w5b2V5qCH6K+G77yM6K+36YeN5paw6YCJ5oupIik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgZ2V0UHVuaXNobWVudChpZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIC8vIOi9rOaNouaXpeacn+agvOW8j++8muWwhjIwMjUwODAz6L2s5o2i5Li6MjAyNS8wOC8wMw0KICAgICAgICB0aGlzLmZvcm0uaGFwcGVuZWRUaW1lID0gdGhpcy5jb252ZXJ0RGF0ZUZvcm1hdCh0aGlzLmZvcm0uaGFwcGVuZWRUaW1lKTsNCiAgICAgICAgdGhpcy5mb3JtLnB1bmlzaG1lbnRUaW1lID0gdGhpcy5jb252ZXJ0RGF0ZUZvcm1hdCh0aGlzLmZvcm0ucHVuaXNobWVudFRpbWUpOw0KICAgICAgICAvLyDop6PmnpDlpITnvZrmjqrmlr3kuLrmoIfnrb4NCiAgICAgICAgdGhpcy5wYXJzZU1lYXN1cmVUZXh0VG9UYWdzKHRoaXMuZm9ybS5wdW5pc2htZW50TWVhc3VyZSk7DQogICAgICAgIHRoaXMuaXNWaWV3TW9kZSA9IGZhbHNlOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueS+m+W6lOWVhuWkhOe9muiusOW9lSI7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluivpuaDheWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMubXNnRXJyb3IoIuiOt+WPluiusOW9leivpuaDheWksei0pSIpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmn6XnnIvmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVWaWV3KHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgaWQgPSByb3cuaWQ7DQogICAgICBjb25zb2xlLmxvZygn5p+l55yL5pON5L2cIC0gaWQ6JywgaWQpOw0KDQogICAgICBpZiAoIWlkKSB7DQogICAgICAgIHRoaXMubXNnRXJyb3IoIuaXoOazleiOt+WPluiusOW9leagh+ivhiIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGdldFB1bmlzaG1lbnQoaWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAvLyDovazmjaLml6XmnJ/moLzlvI/vvJrlsIYyMDI1MDgwM+i9rOaNouS4ujIwMjUvMDgvMDMNCiAgICAgICAgdGhpcy5mb3JtLmhhcHBlbmVkVGltZSA9IHRoaXMuY29udmVydERhdGVGb3JtYXQodGhpcy5mb3JtLmhhcHBlbmVkVGltZSk7DQogICAgICAgIHRoaXMuZm9ybS5wdW5pc2htZW50VGltZSA9IHRoaXMuY29udmVydERhdGVGb3JtYXQodGhpcy5mb3JtLnB1bmlzaG1lbnRUaW1lKTsNCiAgICAgICAgLy8g6Kej5p6Q5aSE572a5o6q5pa95Li65qCH562+DQogICAgICAgIHRoaXMucGFyc2VNZWFzdXJlVGV4dFRvVGFncyh0aGlzLmZvcm0ucHVuaXNobWVudE1lYXN1cmUpOw0KICAgICAgICB0aGlzLmlzVmlld01vZGUgPSB0cnVlOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuafpeeci+S+m+W6lOWVhuWkhOe9muiusOW9lSI7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluivpuaDheWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMubXNnRXJyb3IoIuiOt+WPluiusOW9leivpuaDheWksei0pSIpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlUHVuaXNobWVudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkUHVuaXNobWVudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOehruiupOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUNvbmZpcm0ocm93KSB7DQogICAgICBsZXQgaWRzOw0KDQogICAgICBpZiAocm93ICYmIHJvdy5pZCkgew0KICAgICAgICAvLyDljZXooYznoa7orqTvvJrku47ooajmoLzooYzmk43kvZzmjInpkq7osIPnlKgNCiAgICAgICAgaWYgKHJvdy5zdGF0ZUlkICE9IDEpIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCLlj6rmnInojYnnqL/nirbmgIHnmoTorrDlvZXmiY3og73noa7orqQiKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgaWRzID0gcm93LmlkOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5om56YeP56Gu6K6k77ya5LuO6aG26YOo5oyJ6ZKu6LCD55SoDQogICAgICAgIGlmICghdGhpcy5pZHMgfHwgdGhpcy5pZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgdGhpcy5tc2dFcnJvcigi6K+36YCJ5oup6KaB56Gu6K6k55qE6K6w5b2VIik7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qOA5p+l5omA5pyJ6YCJ5Lit6K6w5b2V55qE54q25oCB77yM5Y+q5pyJ6I2J56i/54q25oCB5omN6IO956Gu6K6kDQogICAgICAgIGNvbnN0IHNlbGVjdGVkUm93cyA9IHRoaXMucHVuaXNobWVudExpc3QuZmlsdGVyKGl0ZW0gPT4gdGhpcy5pZHMuaW5jbHVkZXMoaXRlbS5pZCkpOw0KICAgICAgICBjb25zdCBoYXNOb25EcmFmdFJlY29yZCA9IHNlbGVjdGVkUm93cy5zb21lKGl0ZW0gPT4gaXRlbS5zdGF0ZUlkICE9IDEpOw0KICAgICAgICBpZiAoaGFzTm9uRHJhZnRSZWNvcmQpIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCLlj6rmnInojYnnqL/nirbmgIHnmoTorrDlvZXmiY3og73noa7orqQiKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCg0KICAgICAgICBpZHMgPSB0aGlzLmlkczsNCiAgICAgIH0NCiAgICAgIGlmICghaWRzIHx8IChBcnJheS5pc0FycmF5KGlkcykgJiYgaWRzLmxlbmd0aCA9PT0gMCkpIHsNCiAgICAgICAgdGhpcy5tc2dFcnJvcigi6K+36YCJ5oup6KaB56Gu6K6k55qE6K6w5b2VIik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgY29uZmlybUlkcyA9IEFycmF5LmlzQXJyYXkoaWRzKSA/IGlkcy5qb2luKCcsJykgOiBpZHM7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTpgInkuK3nmoTlpITnvZrorrDlvZU/JywgIuaPkOekuiIsIHsNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHJldHVybiBjb25maXJtUHVuaXNobWVudChjb25maXJtSWRzKTsNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLnoa7orqTmiJDlip8iKTsNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBsZXQgaWRzOw0KDQogICAgICBpZiAocm93ICYmIHJvdy5pZCkgew0KICAgICAgICAvLyDljZXooYzliKDpmaTvvJrku47ooajmoLzooYzmk43kvZzmjInpkq7osIPnlKgNCiAgICAgICAgaWYgKHJvdy5zdGF0ZUlkICE9IDEpIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCLlj6rmnInojYnnqL/nirbmgIHnmoTorrDlvZXmiY3og73liKDpmaQiKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgaWRzID0gcm93LmlkOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5om56YeP5Yig6Zmk77ya5LuO6aG26YOo5oyJ6ZKu6LCD55SoDQogICAgICAgIGlmICghdGhpcy5pZHMgfHwgdGhpcy5pZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgdGhpcy5tc2dFcnJvcigi6K+36YCJ5oup6KaB5Yig6Zmk55qE6K6w5b2VIik7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qOA5p+l5omA5pyJ6YCJ5Lit6K6w5b2V55qE54q25oCB77yM5Y+q5pyJ6I2J56i/54q25oCB5omN6IO95Yig6ZmkDQogICAgICAgIGNvbnN0IHNlbGVjdGVkUm93cyA9IHRoaXMucHVuaXNobWVudExpc3QuZmlsdGVyKGl0ZW0gPT4gdGhpcy5pZHMuaW5jbHVkZXMoaXRlbS5pZCkpOw0KICAgICAgICBjb25zdCBoYXNOb25EcmFmdFJlY29yZCA9IHNlbGVjdGVkUm93cy5zb21lKGl0ZW0gPT4gaXRlbS5zdGF0ZUlkICE9IDEpOw0KICAgICAgICBpZiAoaGFzTm9uRHJhZnRSZWNvcmQpIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCLlj6rmnInojYnnqL/nirbmgIHnmoTorrDlvZXmiY3og73liKDpmaQiKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCg0KICAgICAgICBpZHMgPSB0aGlzLmlkczsNCiAgICAgIH0NCiAgICAgIGlmICghaWRzKSB7DQogICAgICAgIHRoaXMubXNnRXJyb3IoIuaXoOazleiOt+WPluiusOW9leagh+ivhu+8jOivt+mHjeaWsOmAieaLqSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpD8nLCAi6K2m5ZGKIiwgew0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgICAgfSkudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgICByZXR1cm4gZGVsUHVuaXNobWVudChpZHMpOw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaYvuekuuS+m+W6lOWVhuS/oeaBr+afpeivouW8ueeql++8iOihqOWNleeUqO+8iSAqLw0KICAgIHNob3dTdXBwSW5mb0RpYWxvZygpIHsNCiAgICAgIHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPSAnZm9ybSc7DQogICAgICB0aGlzLiRyZWZzLnN1cHBJbmZvRGlhbG9nLnNob3coKTsNCiAgICB9LA0KICAgIC8qKiDmmL7npLrkvpvlupTllYbkv6Hmga/mn6Xor6LlvLnnqpfvvIjmn6Xor6LmnaHku7bnlKjvvIkgKi8NCiAgICBzaG93U3VwcEluZm9EaWFsb2dGb3JRdWVyeSgpIHsNCiAgICAgIHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPSAncXVlcnknOw0KICAgICAgdGhpcy4kcmVmcy5zdXBwSW5mb0RpYWxvZy5zaG93KCk7DQogICAgfSwNCiAgICAvKiog5aSE55CG5L6b5bqU5ZWG6YCJ5oupICovDQogICAgaGFuZGxlU3VwcFNlbGVjdChzdXBwSW5mbykgew0KICAgICAgaWYgKHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPT09ICdmb3JtJykgew0KICAgICAgICAvLyDooajljZXkuK3nmoTkvpvlupTllYbpgInmi6kNCiAgICAgICAgdGhpcy5mb3JtLnN1cHBJZCA9IHN1cHBJbmZvLnN1cHBJZDsNCiAgICAgICAgdGhpcy5mb3JtLnN1cHBOYW1lID0gc3VwcEluZm8uc3VwcE5hbWU7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPT09ICdxdWVyeScpIHsNCiAgICAgICAgLy8g5p+l6K+i5p2h5Lu25Lit55qE5L6b5bqU5ZWG6YCJ5oupDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3VwcElkID0gc3VwcEluZm8uc3VwcElkOw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN1cHBOYW1lID0gc3VwcEluZm8uc3VwcE5hbWU7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5pi+56S654mp5paZ5L+h5oGv5p+l6K+i5by556qX77yI6KGo5Y2V55So77yJICovDQogICAgc2hvd01hdGVyaWFsSW5mb0RpYWxvZ0ZvckZvcm0oKSB7DQogICAgICB0aGlzLmN1cnJlbnRTZWxlY3RUeXBlID0gJ2Zvcm0nOw0KICAgICAgdGhpcy4kcmVmcy5tYXRlcmlhbEluZm9EaWFsb2cuc2hvdygpOw0KICAgIH0sDQogICAgLyoqIOaYvuekuueJqeaWmeS/oeaBr+afpeivouW8ueeql++8iOafpeivouadoeS7tueUqO+8iSAqLw0KICAgIHNob3dNYXRlcmlhbEluZm9EaWFsb2dGb3JRdWVyeSgpIHsNCiAgICAgIHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPSAncXVlcnknOw0KICAgICAgdGhpcy4kcmVmcy5tYXRlcmlhbEluZm9EaWFsb2cuc2hvdygpOw0KICAgIH0sDQogICAgLyoqIOWkhOeQhueJqeaWmemAieaLqSAqLw0KICAgIGhhbmRsZU1hdGVyaWFsU2VsZWN0KG1hdGVyaWFsSW5mbykgew0KICAgICAgaWYgKHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPT09ICdmb3JtJykgew0KICAgICAgICAvLyDooajljZXkuK3nmoTnianmlpnpgInmi6kNCiAgICAgICAgdGhpcy5mb3JtLml0ZW1ObyA9IG1hdGVyaWFsSW5mby5pdGVtSWQ7DQogICAgICAgIHRoaXMuZm9ybS5pdGVtTmFtZSA9IG1hdGVyaWFsSW5mby5pdGVtTmFtZTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5jdXJyZW50U2VsZWN0VHlwZSA9PT0gJ3F1ZXJ5Jykgew0KICAgICAgICAvLyDmn6Xor6LmnaHku7bkuK3nmoTnianmlpnpgInmi6kNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pdGVtTm8gPSBtYXRlcmlhbEluZm8uaXRlbUlkOw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLml0ZW1OYW1lID0gbWF0ZXJpYWxJbmZvLml0ZW1OYW1lOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOaYvuekuuacjeWKoeafpeivouW8ueeql++8iOihqOWNleeUqO+8iSAqLw0KICAgIHNob3dTZXJ2aWNlRGlhbG9nRm9yRm9ybSgpIHsNCiAgICAgIHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPSAnZm9ybSc7DQogICAgICB0aGlzLiRyZWZzLnNlcnZpY2VEaWFsb2cuc2hvdygpOw0KICAgIH0sDQogICAgLyoqIOaYvuekuuacjeWKoemhueebruafpeivouW8ueeql++8iOafpeivouadoeS7tueUqO+8iSAqLw0KICAgIHNob3dTZXJ2aWNlRGlhbG9nRm9yUXVlcnkoKSB7DQogICAgICB0aGlzLmN1cnJlbnRTZWxlY3RUeXBlID0gJ3F1ZXJ5JzsNCiAgICAgIHRoaXMuJHJlZnMuc2VydmljZURpYWxvZy5zaG93KCk7DQogICAgfSwNCiAgICAvKiog5aSE55CG5pyN5Yqh6YCJ5oupICovDQogICAgaGFuZGxlU2VydmljZVNlbGVjdChzZXJ2aWNlSW5mbykgew0KICAgICAgaWYgKHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPT09ICdmb3JtJykgew0KICAgICAgICAvLyDooajljZXkuK3nmoTmnI3liqHpgInmi6kNCiAgICAgICAgdGhpcy5mb3JtLml0ZW1ObyA9IHNlcnZpY2VJbmZvLnNlcnZpY2VObzsNCiAgICAgICAgdGhpcy5mb3JtLml0ZW1OYW1lID0gc2VydmljZUluZm8uc2VydmljZU5hbWU7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPT09ICdxdWVyeScpIHsNCiAgICAgICAgLy8g5p+l6K+i5p2h5Lu25Lit55qE5pyN5Yqh6YCJ5oupDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXRlbU5vID0gc2VydmljZUluZm8uc2VydmljZU5vOw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLml0ZW1OYW1lID0gc2VydmljZUluZm8uc2VydmljZU5hbWU7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5pi+56S66aG555uu5p+l6K+i5by556qX77yI6KGo5Y2V55So77yJICovDQogICAgc2hvd1Byb2plY3REaWFsb2dGb3JGb3JtKCkgew0KICAgICAgdGhpcy5jdXJyZW50U2VsZWN0VHlwZSA9ICdmb3JtJzsNCiAgICAgIHRoaXMuJHJlZnMucHJvamVjdERpYWxvZy5zaG93KCk7DQogICAgfSwNCiAgICAvKiog5pi+56S66aG555uu5p+l6K+i5by556qX77yI5p+l6K+i5p2h5Lu255So77yJICovDQogICAgc2hvd1Byb2plY3REaWFsb2dGb3JRdWVyeSgpIHsNCiAgICAgIHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPSAncXVlcnknOw0KICAgICAgdGhpcy4kcmVmcy5wcm9qZWN0RGlhbG9nLnNob3coKTsNCiAgICB9LA0KICAgIC8qKiDlpITnkIbpobnnm67pgInmi6kgKi8NCiAgICBoYW5kbGVQcm9qZWN0U2VsZWN0KHByb2plY3RJbmZvKSB7DQogICAgICBpZiAodGhpcy5jdXJyZW50U2VsZWN0VHlwZSA9PT0gJ2Zvcm0nKSB7DQogICAgICAgIC8vIOihqOWNleS4reeahOmhueebrumAieaLqQ0KICAgICAgICB0aGlzLmZvcm0uaXRlbU5vID0gcHJvamVjdEluZm8ucHJvamVjdE5vOw0KICAgICAgICB0aGlzLmZvcm0uaXRlbU5hbWUgPSBwcm9qZWN0SW5mby5wcm9qZWN0TmFtZTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5jdXJyZW50U2VsZWN0VHlwZSA9PT0gJ3F1ZXJ5Jykgew0KICAgICAgICAvLyDmn6Xor6LmnaHku7bkuK3nmoTpobnnm67pgInmi6kNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pdGVtTm8gPSBwcm9qZWN0SW5mby5wcm9qZWN0Tm87DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXRlbU5hbWUgPSBwcm9qZWN0SW5mby5wcm9qZWN0TmFtZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDlpITnkIbmnI3liqHpobnnm67pgInmi6kgKi8NCiAgICBoYW5kbGVTZXJ2aWNlUHJvamVjdFNlbGVjdChzZXJ2aWNlSW5mbykgew0KICAgICAgaWYgKHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPT09ICdmb3JtJykgew0KICAgICAgICAvLyDooajljZXkuK3nmoTmnI3liqHpobnnm67pgInmi6kNCiAgICAgICAgdGhpcy5mb3JtLml0ZW1ObyA9IHNlcnZpY2VJbmZvLnNlcnZpY2VObzsNCiAgICAgICAgdGhpcy5mb3JtLml0ZW1OYW1lID0gc2VydmljZUluZm8uc2VydmljZU5hbWU7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuY3VycmVudFNlbGVjdFR5cGUgPT09ICdxdWVyeScpIHsNCiAgICAgICAgLy8g5p+l6K+i5p2h5Lu25Lit55qE5pyN5Yqh6aG555uu6YCJ5oupDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXRlbU5vID0gc2VydmljZUluZm8uc2VydmljZU5vOw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLml0ZW1OYW1lID0gc2VydmljZUluZm8uc2VydmljZU5hbWU7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5aSE55CG54mp6LWE5oiW5pyN5Yqh6YCJ5oup5Y+Y5YyWICovDQogICAgaGFuZGxlTWF0ZXJpYWxPclNlcnZpY2VDaGFuZ2UodmFsdWUpIHsNCiAgICAgIC8vIOa4heepuuebuOWFs+Wtl+autQ0KICAgICAgaWYgKHZhbHVlID09PSAnTScgfHwgdmFsdWUgPT09ICdTJyB8fCB2YWx1ZSA9PT0gJ1AnKSB7DQogICAgICAgIC8vIOmAieaLqeS7u+S9leexu+Wei+aXtu+8jOmDvea4heepuml0ZW1Ob+WSjGl0ZW1OYW1l77yM6K6p55So5oi36YeN5paw6YCJ5oupDQogICAgICAgIHRoaXMuZm9ybS5pdGVtTm8gPSBudWxsOw0KICAgICAgICB0aGlzLmZvcm0uaXRlbU5hbWUgPSBudWxsOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5pyq6YCJ5oup5pe277yM5riF56m65omA5pyJ55u45YWz5a2X5q61DQogICAgICAgIHRoaXMuZm9ybS5pdGVtTm8gPSBudWxsOw0KICAgICAgICB0aGlzLmZvcm0uaXRlbU5hbWUgPSBudWxsOw0KICAgICAgfQ0KDQogICAgICAvLyDliIfmjaLnsbvlnovlkI7vvIzmuIXpmaTkuYvliY3nmoTpqozor4HplJnor6/kv6Hmga8NCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMuZm9ybSkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS5jbGVhclZhbGlkYXRlKFsnaXRlbU5vJywgJ2l0ZW1OYW1lJ10pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlpITnkIbmn6Xor6LmnaHku7bkuK3nianotYTmiJbmnI3liqHpgInmi6nlj5jljJYgKi8NCiAgICBoYW5kbGVRdWVyeU1hdGVyaWFsT3JTZXJ2aWNlQ2hhbmdlKHZhbHVlKSB7DQogICAgICAvLyDmuIXnqbrmn6Xor6LmnaHku7bkuK3nmoTnm7jlhbPlrZfmrrUNCiAgICAgIGlmICh2YWx1ZSA9PT0gJ00nIHx8IHZhbHVlID09PSAnUycgfHwgdmFsdWUgPT09ICdQJykgew0KICAgICAgICAvLyDpgInmi6nku7vkvZXnsbvlnovml7bvvIzpg73muIXnqbppdGVtTm/lkoxpdGVtTmFtZe+8jOiuqeeUqOaIt+mHjeaWsOmAieaLqQ0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLml0ZW1ObyA9IG51bGw7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXRlbU5hbWUgPSBudWxsOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5pyq6YCJ5oup5pe277yM5riF56m65omA5pyJ55u45YWz5a2X5q61DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXRlbU5vID0gbnVsbDsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pdGVtTmFtZSA9IG51bGw7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5pi+56S65aSE572a5o6q5pa96YCJ5oup5by556qXICovDQogICAgc2hvd1B1bmlzaG1lbnRNZWFzdXJlRGlhbG9nKCkgew0KICAgICAgdGhpcy4kcmVmcy5wdW5pc2htZW50TWVhc3VyZURpYWxvZy5zaG93KHRoaXMuZm9ybS5wdW5pc2htZW50TWVhc3VyZSk7DQogICAgfSwNCiAgICAvKiog5aSE55CG5aSE572a5o6q5pa96YCJ5oupICovDQogICAgaGFuZGxlUHVuaXNobWVudE1lYXN1cmVTZWxlY3QobWVhc3VyZVRleHQpIHsNCiAgICAgIHRoaXMuZm9ybS5wdW5pc2htZW50TWVhc3VyZSA9IG1lYXN1cmVUZXh0Ow0KICAgICAgdGhpcy5wYXJzZU1lYXN1cmVUZXh0VG9UYWdzKG1lYXN1cmVUZXh0KTsNCiAgICB9LA0KDQogICAgLyoqIOaYvuekuuWkhOe9muS+neaNrumAieaLqeW8ueeqlyAqLw0KICAgIHNob3dQdW5pc2htZW50QmFzaXNEaWFsb2coKSB7DQogICAgICBjb25zb2xlLmxvZygn5pi+56S65aSE572a5L6d5o2u5by556qX77yM5b2T5YmN5YC877yaJywgdGhpcy5mb3JtLnB1bmlzaG1lbnRCYXNpcyk7DQogICAgICB0aGlzLiRyZWZzLnB1bmlzaG1lbnRCYXNpc0RpYWxvZy5zaG93KHRoaXMuZm9ybS5wdW5pc2htZW50QmFzaXMpOw0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG5aSE572a5L6d5o2u6YCJ5oupICovDQogICAgaGFuZGxlUHVuaXNobWVudEJhc2lzU2VsZWN0KGJhc2lzVGV4dCkgew0KICAgICAgdGhpcy5mb3JtLnB1bmlzaG1lbnRCYXNpcyA9IGJhc2lzVGV4dDsNCiAgICB9LA0KDQogICAgLyoqIOino+aekOWkhOe9muaOquaWveaWh+acrOS4uuagh+etviAqLw0KICAgIHBhcnNlTWVhc3VyZVRleHRUb1RhZ3MobWVhc3VyZVRleHQpIHsNCiAgICAgIHRoaXMucHVuaXNobWVudE1lYXN1cmVUYWdzID0gW107DQogICAgICBpZiAoIW1lYXN1cmVUZXh0KSByZXR1cm47DQoNCiAgICAgIGNvbnN0IG1lYXN1cmVzID0gbWVhc3VyZVRleHQuc3BsaXQoJ++8mycpLmZpbHRlcihpdGVtID0+IGl0ZW0udHJpbSgpKTsNCiAgICAgIG1lYXN1cmVzLmZvckVhY2gobWVhc3VyZSA9PiB7DQogICAgICAgIGNvbnN0IHRhZyA9IHRoaXMuY3JlYXRlTWVhc3VyZVRhZyhtZWFzdXJlLnRyaW0oKSk7DQogICAgICAgIGlmICh0YWcpIHsNCiAgICAgICAgICB0aGlzLnB1bmlzaG1lbnRNZWFzdXJlVGFncy5wdXNoKHRhZyk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5Yib5bu65aSE572a5o6q5pa95qCH562+ICovDQogICAgY3JlYXRlTWVhc3VyZVRhZyhtZWFzdXJlVGV4dCkgew0KICAgICAgaWYgKG1lYXN1cmVUZXh0LmluY2x1ZGVzKCflpITnvZonKSAmJiBtZWFzdXJlVGV4dC5pbmNsdWRlcygn5YWDJykpIHsNCiAgICAgICAgcmV0dXJuIHsgdGV4dDogbWVhc3VyZVRleHQsIHR5cGU6ICdwZW5hbHR5JyB9Ow0KICAgICAgfSBlbHNlIGlmIChtZWFzdXJlVGV4dCA9PT0gJ+mZjee6pycpIHsNCiAgICAgICAgcmV0dXJuIHsgdGV4dDogbWVhc3VyZVRleHQsIHR5cGU6ICdkb3duZ3JhZGUnIH07DQogICAgICB9IGVsc2UgaWYgKG1lYXN1cmVUZXh0ID09PSAn5reY5rGw77yI56aB55So77yJJykgew0KICAgICAgICByZXR1cm4geyB0ZXh0OiBtZWFzdXJlVGV4dCwgdHlwZTogJ2VsaW1pbmF0ZScgfTsNCiAgICAgIH0gZWxzZSBpZiAobWVhc3VyZVRleHQuaW5jbHVkZXMoJ+aague8kycpICYmIG1lYXN1cmVUZXh0LmluY2x1ZGVzKCfmnIgnKSkgew0KICAgICAgICByZXR1cm4geyB0ZXh0OiBtZWFzdXJlVGV4dCwgdHlwZTogJ3N1c3BlbmQnIH07DQogICAgICB9DQogICAgICByZXR1cm4gbnVsbDsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluagh+etvuexu+Wei+WvueW6lOeahOminOiJsiAqLw0KICAgIGdldFRhZ1R5cGUodHlwZSkgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgJ3BlbmFsdHknOiAnZGFuZ2VyJywNCiAgICAgICAgJ2Rvd25ncmFkZSc6ICdkYW5nZXInLA0KICAgICAgICAnZWxpbWluYXRlJzogJ2RhbmdlcicsDQogICAgICAgICdzdXNwZW5kJzogJ2RhbmdlcicNCiAgICAgIH07DQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAnJzsNCiAgICB9LA0KDQogICAgLyoqIOWIoOmZpOWkhOe9muaOquaWveagh+etviAqLw0KICAgIHJlbW92ZU1lYXN1cmVUYWcoaW5kZXgpIHsNCiAgICAgIHRoaXMucHVuaXNobWVudE1lYXN1cmVUYWdzLnNwbGljZShpbmRleCwgMSk7DQogICAgICB0aGlzLnVwZGF0ZU1lYXN1cmVUZXh0KCk7DQogICAgfSwNCg0KICAgIC8qKiDmm7TmlrDlpITnvZrmjqrmlr3mlofmnKwgKi8NCiAgICB1cGRhdGVNZWFzdXJlVGV4dCgpIHsNCiAgICAgIGNvbnN0IG1lYXN1cmVUZXh0cyA9IHRoaXMucHVuaXNobWVudE1lYXN1cmVUYWdzLm1hcCh0YWcgPT4gdGFnLnRleHQpOw0KICAgICAgdGhpcy5mb3JtLnB1bmlzaG1lbnRNZWFzdXJlID0gbWVhc3VyZVRleHRzLmpvaW4oJ++8mycpOw0KICAgIH0sDQoNCg0KICAgIC8qKiDmoLnmja7loavmiqXkurrlt6Xlj7fojrflj5blkI3lrZfjgIHljZXkvY3kv6Hmga8gKi8NCiAgICBnZXRVc2VyQ29tcGFueUluZm8odXNlck5hbWUpIHsNCiAgICAgIGdldFVzZXJDb21wYW55KHVzZXJOYW1lKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgLy8g5LuOU3lzVXNlcuWvueixoeS4reWPlnJzRGVwdE5hbWXkvZzkuLrnoa7orqTpg6jpl6jlkoznlLPor7fpg6jpl6gNCiAgICAgICAgICBjb25zdCBkZXB0TmFtZSA9IHJlc3BvbnNlLmRhdGEucnNEZXB0TmFtZSB8fCAnJzsNCiAgICAgICAgICB0aGlzLmZvcm0uY29tcGFueUNvZGUgPSBkZXB0TmFtZTsgIC8vIOehruiupOmDqOmXqA0KICAgICAgICAgIHRoaXMuZm9ybS5kZXB0Tm8gPSBkZXB0TmFtZTsgICAgICAgLy8g55Sz6K+36YOo6Zeo77yM6buY6K6k5LiO56Gu6K6k6YOo6Zeo5LiA6Ie0DQogICAgICAgICAgdGhpcy5mb3JtLnVzZXJOYW1lID0gcmVzcG9uc2UuZGF0YS5uaWNrTmFtZSB8fCAnJzsNCg0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUud2Fybign6I635Y+W5Y2V5L2N5L+h5oGv5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgLy8g5LiN5pi+56S66ZSZ6K+v5o+Q56S677yM6YG/5YWN5b2x5ZON55So5oi35L2T6aqMDQogICAgICB9KTsNCiAgICB9LA0KDQoNCg0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInkvpvlupTllYblpITnvZrorrDlvZXmlbDmja7pobk/JywgIuitpuWRiiIsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIC8vIOaJi+WKqOaehOW7uuaXtumXtOiMg+WbtOWPguaVsA0KICAgICAgICAgIGxldCBwYXJhbXMgPSB7IC4uLnRoaXMucXVlcnlQYXJhbXMgfTsNCiAgICAgICAgICBpZiAoIXBhcmFtcy5wYXJhbXMpIHsNCiAgICAgICAgICAgIHBhcmFtcy5wYXJhbXMgPSB7fTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlpITnkIbkuovku7blj5HnlJ/ml7bpl7TojIPlm7QNCiAgICAgICAgICBpZiAodGhpcy5oYXBwZW5lZFRpbWVSYW5nZSAmJiB0aGlzLmhhcHBlbmVkVGltZVJhbmdlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICAgICAgcGFyYW1zLnBhcmFtc1siaGFwcGVuZWRUaW1lQmVnaW5UaW1lIl0gPSB0aGlzLmhhcHBlbmVkVGltZVJhbmdlWzBdOw0KICAgICAgICAgICAgcGFyYW1zLnBhcmFtc1siaGFwcGVuZWRUaW1lRW5kVGltZSJdID0gdGhpcy5oYXBwZW5lZFRpbWVSYW5nZVsxXTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlpITnkIblpITnvZrmiafooYzml7bpl7TojIPlm7QNCiAgICAgICAgICBpZiAodGhpcy5wdW5pc2htZW50VGltZVJhbmdlICYmIHRoaXMucHVuaXNobWVudFRpbWVSYW5nZS5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgICAgIHBhcmFtcy5wYXJhbXNbInB1bmlzaG1lbnRUaW1lQmVnaW5UaW1lIl0gPSB0aGlzLnB1bmlzaG1lbnRUaW1lUmFuZ2VbMF07DQogICAgICAgICAgICBwYXJhbXMucGFyYW1zWyJwdW5pc2htZW50VGltZUVuZFRpbWUiXSA9IHRoaXMucHVuaXNobWVudFRpbWVSYW5nZVsxXTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICByZXR1cm4gZXhwb3J0UHVuaXNobWVudChwYXJhbXMpOw0KICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7DQogICAgICAgIH0pDQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6n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file": "index.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"100px\"\r\n    >\r\n    <el-form-item label=\"编号\" prop=\"serialNo\">\r\n        <el-input\r\n          v-model=\"queryParams.serialNo\"\r\n          placeholder=\"请输入编号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"申请部门\" prop=\"deptNo\">\r\n        <el-select\r\n          v-model=\"queryParams.deptNo\"\r\n          placeholder=\"请选择申请部门\"\r\n          clearable\r\n          size=\"small\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in getDepNameList\"\r\n            :key=\"item\"\r\n            :label=\"item\"\r\n            :value=\"item\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"填报人\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入填报人\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"确认人\" prop=\"confirmName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入确认人\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"确认部门\" prop=\"companyCode\">\r\n        <el-input\r\n          v-model=\"queryParams.companyCode\"\r\n          placeholder=\"请输入确认部门\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>     \r\n      <el-form-item label=\"供应商代码\" prop=\"suppId\">\r\n        <el-input\r\n          v-model=\"queryParams.suppId\"\r\n          placeholder=\"请输入供应商代码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        >\r\n          <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showSuppInfoDialogForQuery\"></i>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"供应商名称\" prop=\"suppName\">\r\n        <el-input\r\n          v-model=\"queryParams.suppName\"\r\n          placeholder=\"请输入供应商名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"处罚类型\" prop=\"punishmentType\">\r\n        <el-select v-model=\"queryParams.punishmentType\" placeholder=\"请选择处罚类型\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in punishmentTypeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"供应类型\" prop=\"suppType\">\r\n        <el-select v-model=\"queryParams.suppType\" placeholder=\"请选择物资、服务或工程\" clearable size=\"small\" @change=\"handleQueryMaterialOrServiceChange\">\r\n          <el-option\r\n            v-for=\"option in suppTypeOptions\"\r\n            :key=\"option.value\"\r\n            :label=\"option.label\"\r\n            :value=\"option.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"物料小类编码\" prop=\"itemNo\" v-if=\"queryParams.suppType === 'M'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemNo\"\r\n          placeholder=\"请输入物料小类编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        >\r\n          <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showMaterialInfoDialogForQuery\"></i>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"物料小类名称\" prop=\"itemName\" v-if=\"queryParams.suppType === 'M'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemName\"\r\n          placeholder=\"请输入物料小类名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"服务小类编码\" prop=\"itemNo\" v-if=\"queryParams.suppType === 'S'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemNo\"\r\n          placeholder=\"请输入服务小类编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        >\r\n          <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showServiceDialogForQuery\"></i>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"服务小类名称\" prop=\"itemName\" v-if=\"queryParams.suppType === 'S'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemName\"\r\n          placeholder=\"请输入服务小类名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"工程小类编码\" prop=\"itemNo\" v-if=\"queryParams.suppType === 'P'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemNo\"\r\n          placeholder=\"请输入工程小类编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        >\r\n          <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showProjectDialogForQuery\"></i>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"工程小类名称\" prop=\"itemName\" v-if=\"queryParams.suppType === 'P'\">\r\n        <el-input\r\n          v-model=\"queryParams.itemName\"\r\n          placeholder=\"请输入工程小类名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      \r\n      <el-form-item label=\"状态\" prop=\"stateId\">\r\n        <div style=\"display: flex; align-items: center;\">\r\n          <el-select\r\n            v-model=\"queryParams.stateId\"\r\n            placeholder=\"请选择状态\"\r\n            :clearable=\"userGroup !== 'query'\"\r\n            :disabled=\"userGroup === 'query'\"\r\n            size=\"small\"\r\n            style=\"width: 180px;\"\r\n          >\r\n            <el-option\r\n              v-for=\"dict in statusOptions\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item label=\"事件发生时间\">\r\n        <el-date-picker\r\n          v-model=\"happenedTimeRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          format=\"yyyy/MM/dd\"\r\n          value-format=\"yyyy/MM/dd\"\r\n          size=\"small\"\r\n          clearable>\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"处罚执行时间\">\r\n        <el-date-picker\r\n          v-model=\"punishmentTimeRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          format=\"yyyy/MM/dd\"\r\n          value-format=\"yyyy/MM/dd\"\r\n          size=\"small\"\r\n          clearable>\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      \r\n      <el-form-item>\r\n        <el-button\r\n          type=\"cyan\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n        >搜索</el-button>\r\n        <el-button\r\n          icon=\"el-icon-refresh\"\r\n          size=\"mini\"\r\n          @click=\"resetQuery\"\r\n        >重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canAdd\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canEdit\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canConfirm\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleConfirm\"\r\n        >确认</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canDelete\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canExport\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col>\r\n\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <div class=\"table-container\">\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"punishmentList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"状态\" width=\"70\" align=\"center\" prop=\"stateId\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.stateId == 1\" type=\"info\">草稿</el-tag>\r\n            <el-tag v-if=\"scope.row.stateId == 2\" type=\"success\">确认</el-tag>         \r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"编号\" width=\"140\" align=\"center\" prop=\"serialNo\" />\r\n        <el-table-column label=\"申请部门\" width=\"150\" align=\"center\" prop=\"deptNo\" />\r\n        <el-table-column label=\"填报人\" width=\"120\" align=\"center\" prop=\"userName\" />\r\n        <el-table-column label=\"确认人\" width=\"120\" align=\"center\" prop=\"confirmName\" />\r\n        <el-table-column label=\"确认部门\" width=\"150\" align=\"center\" prop=\"companyCode\" />\r\n        <el-table-column label=\"供应商代码\" width=\"120\" align=\"center\" prop=\"suppId\" />\r\n        <el-table-column label=\"供应商名称\" min-width=\"230\" align=\"center\" prop=\"suppName\" />\r\n        <el-table-column label=\"供应类型\" width=\"80\" align=\"center\" prop=\"suppType\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.suppType == 'M'\">物资</span>\r\n            <span v-if=\"scope.row.suppType == 'S'\">服务</span>\r\n            <span v-if=\"scope.row.suppType == 'P'\">工程</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"物资/服务/工程代码\" width=\"150\" align=\"center\" prop=\"itemNo\" />\r\n        <el-table-column label=\"物资/服务/工程小类名称\" width=\"200\" align=\"center\" prop=\"itemName\" />\r\n        <el-table-column label=\"处罚类型\" width=\"80\" align=\"center\" prop=\"punishmentType\" :formatter=\"punishmentTypeFormat\" />\r\n        <el-table-column label=\"处罚事由\" min-width=\"200\" align=\"center\" prop=\"punishmentReason\" />\r\n        <el-table-column label=\"处罚依据\" min-width=\"300\" align=\"center\" prop=\"punishmentBasis\" />\r\n        <el-table-column label=\"处罚措施\" min-width=\"150\" align=\"center\" prop=\"punishmentMeasure\" />\r\n        <el-table-column label=\"事件发生时间\" width=\"100\" align=\"center\" prop=\"happenedTime\" :formatter=\"dateFormat\" />\r\n        <el-table-column label=\"处罚执行时间\" width=\"100\" align=\"center\" prop=\"punishmentTime\" :formatter=\"dateFormat\" />\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n          class-name=\"small-padding fixed-width\"\r\n          fixed=\"right\"\r\n          width=\"180\"\r\n        >\r\n        <template slot-scope=\"scope\">\r\n          <!-- 修改按钮：只有草稿状态(1)可以修改，且有修改权限 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 1 && permissions.canEdit\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            >修改</el-button\r\n          >\r\n          <!-- 确认按钮：只有草稿状态(1)可以确认，且有确认权限 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 1 && permissions.canConfirm\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleConfirm(scope.row)\"\r\n            >确认</el-button\r\n          >\r\n          <!-- 删除按钮：只有草稿状态(1)可以删除，且有删除权限 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 1 && permissions.canDelete\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n          <!-- 查看按钮：确认状态(2)只能查看，或者没有修改权限时显示查看 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 2 || (scope.row.stateId == 1 && !permissions.canEdit)\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n          >查看</el-button>\r\n        </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改供应商处罚记录对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"填报人\" prop=\"userName\">\r\n              <el-input\r\n                v-model=\"form.userName\"\r\n                placeholder=\"请输入填报人\"\r\n                readonly\r\n                class=\"readonly-input\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"确认部门\" prop=\"companyCode\">\r\n              <el-input\r\n                v-model=\"form.companyCode\"\r\n                placeholder=\"请输入确认部门\"\r\n                readonly\r\n                class=\"readonly-input\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"申请部门\" prop=\"deptNo\">\r\n              <el-select\r\n                v-model=\"form.deptNo\"\r\n                clearable\r\n                placeholder=\"请选择申请部门\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in getDepNameList\"\r\n                  :key=\"item\"\r\n                  :label=\"item\"\r\n                  :value=\"item\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应商代码\" prop=\"suppId\">\r\n              <el-input\r\n                v-model=\"form.suppId\"\r\n                placeholder=\"请输入供应商代码\"\r\n              >\r\n                <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showSuppInfoDialog\"></i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应商名称\" prop=\"suppName\">\r\n              <el-input v-model=\"form.suppName\" placeholder=\"请输入供应商名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"处罚类型\" prop=\"punishmentType\">\r\n              <el-select v-model=\"form.punishmentType\" placeholder=\"请选择处罚类型\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in punishmentTypeOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应类型\" prop=\"suppType\">\r\n              <el-select v-model=\"form.suppType\" placeholder=\"请选择涉及物资、服务或工程\" style=\"width: 100%\" @change=\"handleMaterialOrServiceChange\">\r\n                <el-option\r\n                  v-for=\"option in suppTypeOptions\"\r\n                  :key=\"option.value\"\r\n                  :label=\"option.label\"\r\n                  :value=\"option.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          \r\n          \r\n        </el-row>\r\n        <el-row :gutter=\"20\" v-if=\"form.suppType === 'M'\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物料小类编码\" prop=\"itemNo\" :required=\"true\">\r\n              <el-input v-model=\"form.itemNo\" placeholder=\"请输入物料小类编码\">\r\n                <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showMaterialInfoDialogForForm\"></i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物料小类名称\" prop=\"itemName\" :required=\"true\">\r\n              <el-input v-model=\"form.itemName\" placeholder=\"请输入物料小类名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\" v-if=\"form.suppType === 'S'\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务小类编码\" prop=\"itemNo\">\r\n              <el-input v-model=\"form.itemNo\" placeholder=\"请输入服务小类编码\">\r\n                <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showServiceDialogForForm\"></i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务小类名称\" prop=\"itemName\">\r\n              <el-input v-model=\"form.itemName\" placeholder=\"请输入服务小类名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" v-if=\"form.suppType === 'P'\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程小类编码\" prop=\"itemNo\">\r\n              <el-input v-model=\"form.itemNo\" placeholder=\"请输入工程小类编码\">\r\n                <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showProjectDialogForForm\"></i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程小类名称\" prop=\"itemName\">\r\n              <el-input v-model=\"form.itemName\" placeholder=\"请输入工程小类名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        \r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"事件发生时间\" prop=\"happenedTime\">\r\n              <el-date-picker\r\n                v-model=\"form.happenedTime\"\r\n                type=\"date\"\r\n                placeholder=\"请选择事件发生时间\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"处罚执行时间\" prop=\"punishmentTime\">\r\n              <el-date-picker\r\n                v-model=\"form.punishmentTime\"\r\n                type=\"date\"\r\n                placeholder=\"请选择处罚执行时间\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"处罚事由\" prop=\"punishmentReason\">\r\n              <el-input v-model=\"form.punishmentReason\" type=\"textarea\" placeholder=\"请输入处罚事由\" :rows=\"3\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"处罚依据\" prop=\"punishmentBasis\">\r\n              <div class=\"basis-input-wrapper\">\r\n                <el-input\r\n                  v-model=\"form.punishmentBasis\"\r\n                  type=\"textarea\"\r\n                  placeholder=\"请选择处罚依据\"\r\n                  :rows=\"3\"\r\n                  class=\"basis-textarea\"\r\n                  readonly\r\n                />\r\n                <div class=\"basis-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    @click=\"showPunishmentBasisDialog\"\r\n                    class=\"basis-btn\"\r\n                    size=\"small\"\r\n                    title=\"选择处罚依据\"\r\n                  >\r\n                    选择\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"处罚措施\" prop=\"punishmentMeasure\">\r\n              <div class=\"measure-input-wrapper\">\r\n                <!-- 标签显示区域 -->\r\n                <div class=\"measure-tags-container\" v-if=\"punishmentMeasureTags.length > 0\">\r\n                  <el-tag\r\n                    v-for=\"(tag, index) in punishmentMeasureTags\"\r\n                    :key=\"index\"\r\n                    :type=\"getTagType(tag.type)\"\r\n                    closable\r\n                    @close=\"removeMeasureTag(index)\"\r\n                    class=\"measure-tag\"\r\n                  >\r\n                    {{ tag.text }}\r\n                  </el-tag>\r\n                </div>\r\n\r\n                <!-- 隐藏的输入框用于存储完整文本 -->\r\n                <el-input\r\n                  v-model=\"form.punishmentMeasure\"\r\n                  type=\"hidden\"\r\n                />\r\n\r\n                <!-- 占位符显示区域 -->\r\n                <div\r\n                  v-if=\"punishmentMeasureTags.length === 0\"\r\n                  class=\"measure-placeholder\"\r\n                  @click=\"showPunishmentMeasureDialog\"\r\n                >\r\n                  请选择处罚措施（至少选一个）\r\n                </div>\r\n\r\n                <div class=\"measure-buttons\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    @click=\"showPunishmentMeasureDialog\"\r\n                    class=\"measure-btn\"\r\n                    size=\"small\"\r\n                    title=\"选择处罚措施\"\r\n                  >\r\n                    选择\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"!isViewMode\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 供应商信息查询弹窗 -->\r\n    <supp-info-dialog ref=\"suppInfoDialog\" @select=\"handleSuppSelect\" />\r\n\r\n    <!-- 物料信息查询弹窗 -->\r\n    <material-info-dialog ref=\"materialInfoDialog\" @select=\"handleMaterialSelect\" />\r\n\r\n    <!-- 服务查询弹窗 -->\r\n    <service-project-dialog ref=\"serviceDialog\" @select=\"handleServiceSelect\" />\r\n    <!-- 项目查询弹窗 -->\r\n    <project-dialog ref=\"projectDialog\" @select=\"handleProjectSelect\" />\r\n\r\n    <!-- 处罚措施选择弹窗 -->\r\n    <punishment-measure-dialog ref=\"punishmentMeasureDialog\" @select=\"handlePunishmentMeasureSelect\" />\r\n\r\n    <!-- 处罚依据选择弹窗 -->\r\n    <punishment-basis-dialog ref=\"punishmentBasisDialog\" @select=\"handlePunishmentBasisSelect\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPunishment, getPunishment, delPunishment, addPunishment, updatePunishment, exportPunishment, confirmPunishment, getUserCompany, getUserGroup } from \"@/api/suppPunishment/punishment\";\r\nimport { getDepNameList } from \"@/api/purchase/purdchaseFactoryStock\";\r\nimport { addDateRange } from \"@/utils/ruoyi\";\r\nimport SuppInfoDialog from \"./suppInfo-module.vue\";\r\nimport MaterialInfoDialog from \"./materialInfo-module.vue\";\r\nimport ServiceProjectDialog from \"./service-module.vue\";\r\nimport ProjectDialog from \"./project-module.vue\";\r\nimport PunishmentMeasureDialog from \"./punishmentMeasure-module.vue\";\r\nimport PunishmentBasisDialog from \"./punishmentBasis-module.vue\";\r\n\r\n\r\nexport default {\r\n  name: \"Punishment\",\r\n  components: {\r\n    SuppInfoDialog,\r\n    MaterialInfoDialog,\r\n    ServiceProjectDialog,\r\n    ProjectDialog,\r\n    PunishmentMeasureDialog,\r\n    PunishmentBasisDialog\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 供应商处罚记录表格数据\r\n      punishmentList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否为查看模式\r\n      isViewMode: false,\r\n      // 当前选择类型：form-表单选择，query-查询条件选择\r\n      currentSelectType: 'form',\r\n      // 用户分组权限\r\n      userGroup: '',\r\n      // 权限控制\r\n      permissions: {\r\n        canAdd: true,      // 新增权限\r\n        canEdit: true,     // 修改权限\r\n        canDelete: true,   // 删除权限\r\n        canConfirm: true,  // 确认权限\r\n        canExport: true    // 导出权限\r\n      },\r\n      // 处罚类型数据字典\r\n      punishmentTypeOptions: [],\r\n      // 状态数据字典\r\n      statusOptions: [],\r\n      // 物资或服务选项\r\n      suppTypeOptions: [\r\n        { value: 'M', label: '物资' },\r\n        { value: 'S', label: '服务' },\r\n        { value: 'P', label: '工程' }\r\n      ],\r\n      // 服务部门列表\r\n      getDepNameList: [],\r\n      // 事件发生时间范围\r\n      happenedTimeRange: [],\r\n      // 处罚执行时间范围\r\n      punishmentTimeRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        recCreator: null,\r\n        recCreateTime: null,\r\n        recRevisor: null,\r\n        recReviseTime: null,\r\n        userName: null,\r\n        serialNo: null,\r\n        companyCode: null,\r\n        deptNo: null,\r\n        suppId: null,\r\n        suppName: null,\r\n        itemNo: null,\r\n        itemName: null,\r\n        suppType: null,\r\n        punishmentType: null,\r\n        stateId: null,\r\n        punishmentReason: null,\r\n        punishmentBasis: null,\r\n        punishmentMeasure: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 处罚措施标签数组\r\n      punishmentMeasureTags: [],\r\n      // 表单校验\r\n      rules: {\r\n        suppId: [\r\n          { required: true, trigger: \"blur\", message: \"请输入供应商代码\" }\r\n        ],\r\n        suppName: [\r\n          { required: true, trigger: \"blur\", message: \"请输入供应商名称\" }\r\n        ],\r\n        suppType: [\r\n          { required: true, trigger: \"change\", message: \"请选择物资或服务\" }\r\n        ],\r\n        punishmentType: [\r\n          { required: true, trigger: \"change\", message: \"请选择处罚类型\" }\r\n        ],\r\n        deptNo: [\r\n          { required: true, trigger: \"change\", message: \"请选择申请部门\" }\r\n        ],\r\n        itemNo: [\r\n          {\r\n            required: false,\r\n            trigger: \"blur\",\r\n            validator: (rule, value, callback) => {\r\n              // 只有物资类型才必填\r\n              if (this.form.suppType === 'M') {\r\n                if (!value || value.trim() === '') {\r\n                  callback(new Error('请输入物料小类编码'));\r\n                } else {\r\n                  callback();\r\n                }\r\n              } else {\r\n                // 服务和工程类型不校验必填\r\n                callback();\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        itemName: [\r\n          {\r\n            required: false,\r\n            trigger: \"blur\",\r\n            validator: (rule, value, callback) => {\r\n              // 只有物资类型才必填\r\n              if (this.form.suppType === 'M') {\r\n                if (!value || value.trim() === '') {\r\n                  callback(new Error('请输入物料小类名称'));\r\n                } else {\r\n                  callback();\r\n                }\r\n              } else {\r\n                // 服务和工程类型不校验必填\r\n                callback();\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        happenedTime: [\r\n          { required: true, trigger: \"change\", message: \"请选择事件发生时间\" }\r\n        ],\r\n        punishmentTime: [\r\n          { required: true, trigger: \"change\", message: \"请选择处罚执行时间\" }\r\n        ],\r\n        punishmentReason: [\r\n          { required: true, trigger: \"blur\", message: \"请输入处罚事由\" }\r\n        ],\r\n        punishmentBasis: [\r\n          { required: true, trigger: \"blur\", message: \"请选择处罚依据\" }\r\n        ],\r\n        punishmentMeasure: [\r\n          { required: true, trigger: \"blur\", message: \"请选择处罚措施\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictData();\r\n    this.getDeptList();\r\n    this.getUserGroupPermissions();\r\n    // 不在这里调用getList()，而是在设置默认值后调用\r\n  },\r\n  methods: {\r\n    // 添加日期范围\r\n    addDateRange,\r\n    // 获取字典数据\r\n    getDictData() {\r\n      // 获取处罚类型\r\n      this.getDicts(\"supp_punishment_type\").then((response) => {\r\n        this.punishmentTypeOptions = response.data;\r\n      });\r\n      // 获取状态 - 如果字典不存在，使用手动定义的状态\r\n      this.getDicts(\"supp_punishment_status\").then((response) => {\r\n        this.statusOptions = response.data;\r\n      });\r\n    },\r\n    /** 查询供应商处罚记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n\r\n      console.log('事件发生时间范围:', this.happenedTimeRange);\r\n      console.log('处罚执行时间范围:', this.punishmentTimeRange);\r\n\r\n      // 手动构建时间范围参数\r\n      let params = { ...this.queryParams };\r\n      if (!params.params) {\r\n        params.params = {};\r\n      }\r\n\r\n      // 处理事件发生时间范围\r\n      if (this.happenedTimeRange && this.happenedTimeRange.length === 2) {\r\n        params.params[\"happenedTimeBeginTime\"] = this.happenedTimeRange[0];\r\n        params.params[\"happenedTimeEndTime\"] = this.happenedTimeRange[1];\r\n      }\r\n\r\n      // 处理处罚执行时间范围\r\n      if (this.punishmentTimeRange && this.punishmentTimeRange.length === 2) {\r\n        params.params[\"punishmentTimeBeginTime\"] = this.punishmentTimeRange[0];\r\n        params.params[\"punishmentTimeEndTime\"] = this.punishmentTimeRange[1];\r\n      }\r\n\r\n      console.log('最终参数:', params);\r\n\r\n      listPunishment(params).then(response => {\r\n        this.punishmentList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 查询服务部门列表 */\r\n    getDeptList() {\r\n      getDepNameList().then(response => {\r\n        this.getDepNameList = response.data;\r\n        // 直接执行查询，不设置默认申请部门\r\n        this.getList();\r\n      });\r\n    },\r\n\r\n    /** 获取用户分组权限 */\r\n    getUserGroupPermissions() {\r\n      getUserGroup().then(response => {\r\n        if (response.code === 200 && response.data) {\r\n          this.userGroup = response.data.userGroup;\r\n          this.setPermissions();\r\n        } else {\r\n          // 默认设置为查阅组权限（最严格）\r\n          this.userGroup = 'query';\r\n          this.setPermissions();\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取用户分组失败:', error);\r\n        // 默认设置为查阅组权限（最严格）\r\n        this.userGroup = 'query';\r\n        this.setPermissions();\r\n      });\r\n    },\r\n\r\n    /** 根据用户分组设置权限 */\r\n    setPermissions() {\r\n      switch (this.userGroup) {\r\n        case 'input':\r\n          // 填报组：隐藏确认、导出按钮\r\n          this.permissions = {\r\n            canAdd: true,\r\n            canEdit: true,\r\n            canDelete: true,\r\n            canConfirm: false,\r\n            canExport: false\r\n          };\r\n          break;\r\n        case 'confirm':\r\n          // 确认组：隐藏导出按钮\r\n          this.permissions = {\r\n            canAdd: true,\r\n            canEdit: true,\r\n            canDelete: true,\r\n            canConfirm: true,\r\n            canExport: false\r\n          };\r\n          break;\r\n        case 'query':\r\n          // 查阅组：隐藏新增、修改、确认、删除按钮\r\n          this.permissions = {\r\n            canAdd: false,\r\n            canEdit: false,\r\n            canDelete: false,\r\n            canConfirm: false,\r\n            canExport: true\r\n          };\r\n          // 设置查阅组默认状态为\"确认\"\r\n          this.setQueryGroupDefaults();\r\n          break;\r\n        case 'manage':\r\n          // 管理组：具备所有功能\r\n          this.permissions = {\r\n            canAdd: true,\r\n            canEdit: true,\r\n            canDelete: true,\r\n            canConfirm: true,\r\n            canExport: true\r\n          };\r\n          break;\r\n        default:\r\n          // 默认为查阅组权限\r\n          this.permissions = {\r\n            canAdd: false,\r\n            canEdit: false,\r\n            canDelete: false,\r\n            canConfirm: false,\r\n            canExport: true\r\n          };\r\n          // 设置查阅组默认状态为\"确认\"\r\n          this.setQueryGroupDefaults();\r\n      }\r\n    },\r\n\r\n    /** 设置查阅组默认值 */\r\n    setQueryGroupDefaults() {\r\n      // 设置状态默认为\"确认\"（值为2）\r\n      this.queryParams.stateId = '2';\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        recCreator: null,\r\n        recCreateTime: null,\r\n        recRevisor: null,\r\n        recReviseTime: null,\r\n        userName: null,\r\n        serialNo: null,\r\n        companyCode: null,\r\n        deptNo: null,\r\n        suppId: null,\r\n        suppName: null,\r\n        itemNo: null,\r\n        itemName: null,\r\n        punishmentType: null,\r\n        suppType: null,\r\n        happenedTime: null,\r\n        punishmentTime: null,\r\n        punishmentReason: null,\r\n        punishmentBasis: null,\r\n        punishmentMeasure: null\r\n      };\r\n      // 重置处罚措施标签\r\n      this.punishmentMeasureTags = [];\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 处罚类型字典翻译\r\n    punishmentTypeFormat(row, column) {\r\n      return this.selectDictLabel(this.punishmentTypeOptions, row.punishmentType);\r\n    },\r\n    // 状态字典翻译\r\n    stateFormat(row, column) {\r\n      return this.selectDictLabel(this.statusOptions, row.stateId);\r\n    },\r\n    // 日期格式化：将20250803转换为2025-08-03\r\n    dateFormat(row, column, cellValue) {\r\n      if (!cellValue) return '';\r\n      // 如果已经是正确格式，直接返回\r\n      if (cellValue.includes('-')) return cellValue;\r\n      // 将20250803格式转换为2025-08-03\r\n      if (cellValue.length === 8) {\r\n        const year = cellValue.substring(0, 4);\r\n        const month = cellValue.substring(4, 6);\r\n        const day = cellValue.substring(6, 8);\r\n        return `${year}-${month}-${day}`;\r\n      }\r\n      return cellValue;\r\n    },\r\n    // 日期格式转换：将20250803转换为2025/08/03（用于表单回显）\r\n    convertDateFormat(dateValue) {\r\n      if (!dateValue) return '';\r\n      // 如果已经是正确格式，直接返回\r\n      if (dateValue.includes('/') || dateValue.includes('-')) return dateValue;\r\n      // 将20250803格式转换为2025/08/03\r\n      if (dateValue.length === 8) {\r\n        const year = dateValue.substring(0, 4);\r\n        const month = dateValue.substring(4, 6);\r\n        const day = dateValue.substring(6, 8);\r\n        return `${year}/${month}/${day}`;\r\n      }\r\n      return dateValue;\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.happenedTimeRange = [];\r\n      this.punishmentTimeRange = [];\r\n      this.resetForm(\"queryForm\");\r\n\r\n      // 如果是查阅组，重置后需要重新设置默认状态\r\n      if (this.userGroup === 'query') {\r\n        this.setQueryGroupDefaults();\r\n      }\r\n\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.isViewMode = false;\r\n      // 自动填入当前登录用户\r\n      if (this.$store.getters.name) {\r\n        // 立即获取登录人信息\r\n        this.getUserCompanyInfo(this.$store.getters.name);\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加供应商处罚记录\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      let id;\r\n\r\n      if (row && row.id) {\r\n        // 单行修改：从表格行操作按钮调用\r\n        if (row.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能修改\");\r\n          return;\r\n        }\r\n        id = row.id;\r\n      } else {\r\n        // 批量修改：从顶部按钮调用\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.msgError(\"请选择要修改的记录\");\r\n          return;\r\n        }\r\n        if (this.ids.length > 1) {\r\n          this.msgError(\"修改操作只能选择一条记录\");\r\n          return;\r\n        }\r\n\r\n        // 根据选中的ID查找对应的记录\r\n        const selectedRow = this.punishmentList.find(item => item.id === this.ids[0]);\r\n        if (!selectedRow) {\r\n          this.msgError(\"无法找到选中的记录\");\r\n          return;\r\n        }\r\n        if (selectedRow.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能修改\");\r\n          return;\r\n        }\r\n        id = selectedRow.id;\r\n      }\r\n      console.log('修改操作 - id:', id);\r\n      console.log('修改操作 - row:', row);\r\n\r\n      if (!id) {\r\n        this.msgError(\"无法获取记录标识，请重新选择\");\r\n        return;\r\n      }\r\n\r\n      getPunishment(id).then(response => {\r\n        this.form = response.data;\r\n        // 转换日期格式：将20250803转换为2025/08/03\r\n        this.form.happenedTime = this.convertDateFormat(this.form.happenedTime);\r\n        this.form.punishmentTime = this.convertDateFormat(this.form.punishmentTime);\r\n        // 解析处罚措施为标签\r\n        this.parseMeasureTextToTags(this.form.punishmentMeasure);\r\n        this.isViewMode = false;\r\n        this.open = true;\r\n        this.title = \"修改供应商处罚记录\";\r\n      }).catch(error => {\r\n        console.error('获取详情失败:', error);\r\n        this.msgError(\"获取记录详情失败\");\r\n      });\r\n    },\r\n\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      console.log('查看操作 - id:', id);\r\n\r\n      if (!id) {\r\n        this.msgError(\"无法获取记录标识\");\r\n        return;\r\n      }\r\n\r\n      getPunishment(id).then(response => {\r\n        this.form = response.data;\r\n        // 转换日期格式：将20250803转换为2025/08/03\r\n        this.form.happenedTime = this.convertDateFormat(this.form.happenedTime);\r\n        this.form.punishmentTime = this.convertDateFormat(this.form.punishmentTime);\r\n        // 解析处罚措施为标签\r\n        this.parseMeasureTextToTags(this.form.punishmentMeasure);\r\n        this.isViewMode = true;\r\n        this.open = true;\r\n        this.title = \"查看供应商处罚记录\";\r\n      }).catch(error => {\r\n        console.error('获取详情失败:', error);\r\n        this.msgError(\"获取记录详情失败\");\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updatePunishment(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addPunishment(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 确认按钮操作 */\r\n    handleConfirm(row) {\r\n      let ids;\r\n\r\n      if (row && row.id) {\r\n        // 单行确认：从表格行操作按钮调用\r\n        if (row.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能确认\");\r\n          return;\r\n        }\r\n        ids = row.id;\r\n      } else {\r\n        // 批量确认：从顶部按钮调用\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.msgError(\"请选择要确认的记录\");\r\n          return;\r\n        }\r\n\r\n        // 检查所有选中记录的状态，只有草稿状态才能确认\r\n        const selectedRows = this.punishmentList.filter(item => this.ids.includes(item.id));\r\n        const hasNonDraftRecord = selectedRows.some(item => item.stateId != 1);\r\n        if (hasNonDraftRecord) {\r\n          this.msgError(\"只有草稿状态的记录才能确认\");\r\n          return;\r\n        }\r\n\r\n        ids = this.ids;\r\n      }\r\n      if (!ids || (Array.isArray(ids) && ids.length === 0)) {\r\n        this.msgError(\"请选择要确认的记录\");\r\n        return;\r\n      }\r\n\r\n      const confirmIds = Array.isArray(ids) ? ids.join(',') : ids;\r\n      this.$confirm('是否确认选中的处罚记录?', \"提示\", {\r\n          cancelButtonText: \"取消\",\r\n          confirmButtonText: \"确定\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          return confirmPunishment(confirmIds);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"确认成功\");\r\n        })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let ids;\r\n\r\n      if (row && row.id) {\r\n        // 单行删除：从表格行操作按钮调用\r\n        if (row.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能删除\");\r\n          return;\r\n        }\r\n        ids = row.id;\r\n      } else {\r\n        // 批量删除：从顶部按钮调用\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.msgError(\"请选择要删除的记录\");\r\n          return;\r\n        }\r\n\r\n        // 检查所有选中记录的状态，只有草稿状态才能删除\r\n        const selectedRows = this.punishmentList.filter(item => this.ids.includes(item.id));\r\n        const hasNonDraftRecord = selectedRows.some(item => item.stateId != 1);\r\n        if (hasNonDraftRecord) {\r\n          this.msgError(\"只有草稿状态的记录才能删除\");\r\n          return;\r\n        }\r\n\r\n        ids = this.ids;\r\n      }\r\n      if (!ids) {\r\n        this.msgError(\"无法获取记录标识，请重新选择\");\r\n        return;\r\n      }\r\n\r\n      this.$confirm('是否确认删除?', \"警告\", {\r\n          cancelButtonText: \"取消\",\r\n          confirmButtonText: \"确定\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delPunishment(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 显示供应商信息查询弹窗（表单用） */\r\n    showSuppInfoDialog() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.suppInfoDialog.show();\r\n    },\r\n    /** 显示供应商信息查询弹窗（查询条件用） */\r\n    showSuppInfoDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.suppInfoDialog.show();\r\n    },\r\n    /** 处理供应商选择 */\r\n    handleSuppSelect(suppInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的供应商选择\r\n        this.form.suppId = suppInfo.suppId;\r\n        this.form.suppName = suppInfo.suppName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的供应商选择\r\n        this.queryParams.suppId = suppInfo.suppId;\r\n        this.queryParams.suppName = suppInfo.suppName;\r\n      }\r\n    },\r\n    /** 显示物料信息查询弹窗（表单用） */\r\n    showMaterialInfoDialogForForm() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.materialInfoDialog.show();\r\n    },\r\n    /** 显示物料信息查询弹窗（查询条件用） */\r\n    showMaterialInfoDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.materialInfoDialog.show();\r\n    },\r\n    /** 处理物料选择 */\r\n    handleMaterialSelect(materialInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的物料选择\r\n        this.form.itemNo = materialInfo.itemId;\r\n        this.form.itemName = materialInfo.itemName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的物料选择\r\n        this.queryParams.itemNo = materialInfo.itemId;\r\n        this.queryParams.itemName = materialInfo.itemName;\r\n      }\r\n    },\r\n    /** 显示服务查询弹窗（表单用） */\r\n    showServiceDialogForForm() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.serviceDialog.show();\r\n    },\r\n    /** 显示服务项目查询弹窗（查询条件用） */\r\n    showServiceDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.serviceDialog.show();\r\n    },\r\n    /** 处理服务选择 */\r\n    handleServiceSelect(serviceInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的服务选择\r\n        this.form.itemNo = serviceInfo.serviceNo;\r\n        this.form.itemName = serviceInfo.serviceName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的服务选择\r\n        this.queryParams.itemNo = serviceInfo.serviceNo;\r\n        this.queryParams.itemName = serviceInfo.serviceName;\r\n      }\r\n    },\r\n    /** 显示项目查询弹窗（表单用） */\r\n    showProjectDialogForForm() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.projectDialog.show();\r\n    },\r\n    /** 显示项目查询弹窗（查询条件用） */\r\n    showProjectDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.projectDialog.show();\r\n    },\r\n    /** 处理项目选择 */\r\n    handleProjectSelect(projectInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的项目选择\r\n        this.form.itemNo = projectInfo.projectNo;\r\n        this.form.itemName = projectInfo.projectName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的项目选择\r\n        this.queryParams.itemNo = projectInfo.projectNo;\r\n        this.queryParams.itemName = projectInfo.projectName;\r\n      }\r\n    },\r\n    /** 处理服务项目选择 */\r\n    handleServiceProjectSelect(serviceInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的服务项目选择\r\n        this.form.itemNo = serviceInfo.serviceNo;\r\n        this.form.itemName = serviceInfo.serviceName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的服务项目选择\r\n        this.queryParams.itemNo = serviceInfo.serviceNo;\r\n        this.queryParams.itemName = serviceInfo.serviceName;\r\n      }\r\n    },\r\n    /** 处理物资或服务选择变化 */\r\n    handleMaterialOrServiceChange(value) {\r\n      // 清空相关字段\r\n      if (value === 'M' || value === 'S' || value === 'P') {\r\n        // 选择任何类型时，都清空itemNo和itemName，让用户重新选择\r\n        this.form.itemNo = null;\r\n        this.form.itemName = null;\r\n      } else {\r\n        // 未选择时，清空所有相关字段\r\n        this.form.itemNo = null;\r\n        this.form.itemName = null;\r\n      }\r\n\r\n      // 切换类型后，清除之前的验证错误信息\r\n      this.$nextTick(() => {\r\n        if (this.$refs.form) {\r\n          this.$refs.form.clearValidate(['itemNo', 'itemName']);\r\n        }\r\n      });\r\n    },\r\n    /** 处理查询条件中物资或服务选择变化 */\r\n    handleQueryMaterialOrServiceChange(value) {\r\n      // 清空查询条件中的相关字段\r\n      if (value === 'M' || value === 'S' || value === 'P') {\r\n        // 选择任何类型时，都清空itemNo和itemName，让用户重新选择\r\n        this.queryParams.itemNo = null;\r\n        this.queryParams.itemName = null;\r\n      } else {\r\n        // 未选择时，清空所有相关字段\r\n        this.queryParams.itemNo = null;\r\n        this.queryParams.itemName = null;\r\n      }\r\n    },\r\n    /** 显示处罚措施选择弹窗 */\r\n    showPunishmentMeasureDialog() {\r\n      this.$refs.punishmentMeasureDialog.show(this.form.punishmentMeasure);\r\n    },\r\n    /** 处理处罚措施选择 */\r\n    handlePunishmentMeasureSelect(measureText) {\r\n      this.form.punishmentMeasure = measureText;\r\n      this.parseMeasureTextToTags(measureText);\r\n    },\r\n\r\n    /** 显示处罚依据选择弹窗 */\r\n    showPunishmentBasisDialog() {\r\n      console.log('显示处罚依据弹窗，当前值：', this.form.punishmentBasis);\r\n      this.$refs.punishmentBasisDialog.show(this.form.punishmentBasis);\r\n    },\r\n\r\n    /** 处理处罚依据选择 */\r\n    handlePunishmentBasisSelect(basisText) {\r\n      this.form.punishmentBasis = basisText;\r\n    },\r\n\r\n    /** 解析处罚措施文本为标签 */\r\n    parseMeasureTextToTags(measureText) {\r\n      this.punishmentMeasureTags = [];\r\n      if (!measureText) return;\r\n\r\n      const measures = measureText.split('；').filter(item => item.trim());\r\n      measures.forEach(measure => {\r\n        const tag = this.createMeasureTag(measure.trim());\r\n        if (tag) {\r\n          this.punishmentMeasureTags.push(tag);\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 创建处罚措施标签 */\r\n    createMeasureTag(measureText) {\r\n      if (measureText.includes('处罚') && measureText.includes('元')) {\r\n        return { text: measureText, type: 'penalty' };\r\n      } else if (measureText === '降级') {\r\n        return { text: measureText, type: 'downgrade' };\r\n      } else if (measureText === '淘汰（禁用）') {\r\n        return { text: measureText, type: 'eliminate' };\r\n      } else if (measureText.includes('暂缓') && measureText.includes('月')) {\r\n        return { text: measureText, type: 'suspend' };\r\n      }\r\n      return null;\r\n    },\r\n\r\n    /** 获取标签类型对应的颜色 */\r\n    getTagType(type) {\r\n      const typeMap = {\r\n        'penalty': 'danger',\r\n        'downgrade': 'danger',\r\n        'eliminate': 'danger',\r\n        'suspend': 'danger'\r\n      };\r\n      return typeMap[type] || '';\r\n    },\r\n\r\n    /** 删除处罚措施标签 */\r\n    removeMeasureTag(index) {\r\n      this.punishmentMeasureTags.splice(index, 1);\r\n      this.updateMeasureText();\r\n    },\r\n\r\n    /** 更新处罚措施文本 */\r\n    updateMeasureText() {\r\n      const measureTexts = this.punishmentMeasureTags.map(tag => tag.text);\r\n      this.form.punishmentMeasure = measureTexts.join('；');\r\n    },\r\n\r\n\r\n    /** 根据填报人工号获取名字、单位信息 */\r\n    getUserCompanyInfo(userName) {\r\n      getUserCompany(userName).then(response => {\r\n        if (response.code === 200 && response.data) {\r\n          // 从SysUser对象中取rsDeptName作为确认部门和申请部门\r\n          const deptName = response.data.rsDeptName || '';\r\n          this.form.companyCode = deptName;  // 确认部门\r\n          this.form.deptNo = deptName;       // 申请部门，默认与确认部门一致\r\n          this.form.userName = response.data.nickName || '';\r\n\r\n        }\r\n      }).catch(error => {\r\n        console.warn('获取单位信息失败:', error);\r\n        // 不显示错误提示，避免影响用户体验\r\n      });\r\n    },\r\n\r\n\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$confirm('是否确认导出所有供应商处罚记录数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          // 手动构建时间范围参数\r\n          let params = { ...this.queryParams };\r\n          if (!params.params) {\r\n            params.params = {};\r\n          }\r\n\r\n          // 处理事件发生时间范围\r\n          if (this.happenedTimeRange && this.happenedTimeRange.length === 2) {\r\n            params.params[\"happenedTimeBeginTime\"] = this.happenedTimeRange[0];\r\n            params.params[\"happenedTimeEndTime\"] = this.happenedTimeRange[1];\r\n          }\r\n\r\n          // 处理处罚执行时间范围\r\n          if (this.punishmentTimeRange && this.punishmentTimeRange.length === 2) {\r\n            params.params[\"punishmentTimeBeginTime\"] = this.punishmentTimeRange[0];\r\n            params.params[\"punishmentTimeEndTime\"] = this.punishmentTimeRange[1];\r\n          }\r\n\r\n          return exportPunishment(params);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 日期范围选择器宽度与其他输入框一致 */\r\n.el-date-editor.el-range-editor {\r\n  width: 205px !important;\r\n  font-size: 12px !important;\r\n}\r\n\r\n/* 调整日期范围选择器内部样式 */\r\n.el-date-editor.el-range-editor .el-range-input {\r\n  width: 32% !important;\r\n  font-size: 12px !important;\r\n  text-align: center;\r\n}\r\n\r\n.el-date-editor.el-range-editor .el-range-separator {\r\n  width: 20% !important;\r\n  text-align: center;\r\n  font-size: 12px !important;\r\n  color: #C0C4CC;\r\n}\r\n\r\n/* 确保日期范围选择器的高度与其他输入框一致 */\r\n.el-date-editor.el-range-editor.el-input__inner {\r\n  height: 28px !important;\r\n  line-height: 28px !important;\r\n}\r\n\r\n/* 缩短右侧留白 */\r\n.app-container {\r\n  padding-right: 5px !important;\r\n}\r\n\r\n/* 调整表单容器宽度 */\r\n.el-form--inline {\r\n  max-width: calc(100% - 10px) !important;\r\n}\r\n\r\n/* 调整输入框上下间距相等 */\r\n.el-form--inline .el-form-item {\r\n  margin-bottom: 18px !important;\r\n  margin-top: 0 !important;\r\n}\r\n\r\n/* 确保第一行没有额外的上边距 */\r\n.el-form--inline .el-form-item:first-child {\r\n  margin-top: 0 !important;\r\n}\r\n\r\n/* 缩小输入框标题字体 */\r\n.el-form--inline .el-form-item__label {\r\n  font-size: 10px !important;\r\n}\r\n\r\n/* 更强的选择器确保字体样式生效 */\r\n.app-container .el-form--inline .el-form-item .el-form-item__label {\r\n  font-size: 10px !important;\r\n  font-weight: normal !important;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n.el-dialog__header {\r\n  text-align: center !important;\r\n}\r\n\r\n.el-dialog__header .el-dialog__title {\r\n  text-align: center !important;\r\n  width: 100% !important;\r\n  display: block !important;\r\n}\r\n\r\n/* 更强的选择器确保弹窗标题居中 */\r\n.el-dialog .el-dialog__header .el-dialog__title {\r\n  text-align: center !important;\r\n  width: 100% !important;\r\n  display: block !important;\r\n  margin: 0 auto !important;\r\n}\r\n\r\n/* 使用深度选择器确保样式穿透 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center !important;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center !important;\r\n  width: 100% !important;\r\n  display: block !important;\r\n}\r\n\r\n/* 搜索图标样式 */\r\n.search-icon {\r\n  cursor: pointer;\r\n  color: #909399;\r\n  padding: 0 8px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.search-icon:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 操作列固定宽度 */\r\n.el-table .fixed-width {\r\n  width: 200px;\r\n  min-width: 200px;\r\n}\r\n\r\n/* 小间距 */\r\n.el-table .small-padding .cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n}\r\n\r\n/* 处罚措施输入框样式 */\r\n.measure-input-wrapper {\r\n  position: relative;\r\n  min-height: 78px;\r\n  height: 78px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  padding: 8px 12px;\r\n  background-color: #fff;\r\n}\r\n\r\n.measure-tags-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  min-height: 62px;\r\n  max-height: 62px;\r\n  overflow-y: auto;\r\n  align-items: flex-start;\r\n  align-content: flex-start;\r\n}\r\n\r\n.measure-tag {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  height: 28px;\r\n  line-height: 26px;\r\n  border-radius: 14px;\r\n  padding: 0 12px;\r\n  cursor: default;\r\n  font-weight: 500;\r\n}\r\n\r\n.measure-tag .el-icon-close {\r\n  margin-left: 6px;\r\n  font-size: 12px;\r\n}\r\n\r\n.measure-placeholder {\r\n  color: #c0c4cc;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  position: absolute;\r\n  top: 8px;\r\n  left: 12px;\r\n  cursor: pointer;\r\n  user-select: none;\r\n}\r\n\r\n.measure-placeholder:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n.measure-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.measure-buttons {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.measure-btn {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  min-width: 50px;\r\n  height: 28px;\r\n  line-height: 1;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.measure-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 选择按钮样式 */\r\n.measure-btn.el-button--primary {\r\n  background: rgba(64, 158, 255, 0.9);\r\n  border-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.measure-btn.el-button--primary:hover {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n}\r\n\r\n/* 清空按钮样式 */\r\n.measure-btn.el-button--danger {\r\n  background: rgba(245, 108, 108, 0.9);\r\n  border-color: #F56C6C;\r\n  color: white;\r\n}\r\n\r\n.measure-btn.el-button--danger:hover {\r\n  background: #F56C6C;\r\n  border-color: #F56C6C;\r\n}\r\n\r\n/* 只读处罚措施输入框样式 */\r\n.measure-textarea.el-textarea.is-disabled .el-textarea__inner,\r\n.measure-textarea .el-textarea__inner[readonly] {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #606266;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 查询表单样式优化 - 每行4个输入框 */\r\n.el-form--inline {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: flex-start;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  width: calc(25% - 15px);\r\n  margin-right: 20px;\r\n  margin-bottom: 15px;\r\n  flex: 0 0 auto;\r\n}\r\n\r\n/* 每行第4个元素不需要右边距 */\r\n.el-form--inline .el-form-item:nth-child(4n) {\r\n  margin-right: 0;\r\n}\r\n\r\n/* 搜索按钮区域单独处理 */\r\n.el-form--inline .el-form-item:last-child {\r\n  width: auto;\r\n  margin-left: auto;\r\n  margin-right: 0;\r\n}\r\n\r\n/* 统一输入框宽度 */\r\n.el-form--inline .el-form-item .el-input {\r\n  width: 100%;\r\n}\r\n\r\n/* 统一选择框宽度 */\r\n.el-form--inline .el-form-item .el-select {\r\n  width: 100%;\r\n}\r\n\r\n/* 统一日期选择器宽度 */\r\n.el-form--inline .el-form-item .el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1400px) {\r\n  .el-form--inline .el-form-item {\r\n    width: calc(33.33% - 13px);\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(4n) {\r\n    margin-right: 20px;\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(3n) {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 1000px) {\r\n  .el-form--inline .el-form-item {\r\n    width: calc(50% - 10px);\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(3n) {\r\n    margin-right: 20px;\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(2n) {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  width: 100%;\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n}\r\n\r\n/* 处罚依据输入框样式 */\r\n.basis-input-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.basis-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.basis-buttons {\r\n  position: absolute;\r\n  top: 5px;\r\n  right: 5px;\r\n  z-index: 10;\r\n}\r\n\r\n.basis-btn {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  min-width: 50px;\r\n  height: 28px;\r\n  line-height: 1;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.basis-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 选择按钮样式 */\r\n.basis-btn.el-button--primary {\r\n  background: rgba(64, 158, 255, 0.9);\r\n  border-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.basis-btn.el-button--primary:hover {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n}\r\n\r\n/* 滚动条样式优化 */\r\n.table-container::-webkit-scrollbar {\r\n  height: 8px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n\r\n\r\n\r\n/* Element UI 固定列样式优化 */\r\n::v-deep .el-table__fixed-right {\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 只读输入框样式 */\r\n.readonly-input ::v-deep .el-input__inner {\r\n  background-color: #f5f7fa !important;\r\n  border-color: #e4e7ed !important;\r\n  color: #909399 !important;\r\n  cursor: not-allowed !important;\r\n}\r\n\r\n.readonly-input ::v-deep .el-input__inner:hover {\r\n  border-color: #e4e7ed !important;\r\n}\r\n\r\n.readonly-input ::v-deep .el-input__inner:focus {\r\n  border-color: #e4e7ed !important;\r\n  box-shadow: none !important;\r\n}\r\n</style>\r\n"]}]}