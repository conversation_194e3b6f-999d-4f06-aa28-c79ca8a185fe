package com.ruoyi.app.saleScore.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.ruoyi.app.domain.FileApproval;
import com.ruoyi.app.saleScore.domain.TScoreRule;
import com.ruoyi.app.saleScore.service.ITScoreRuleService;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.saleScore.ScoreItemStatus;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.saleScore.mapper.TScoreItemMapper;
import com.ruoyi.app.saleScore.domain.TScoreItem;
import com.ruoyi.app.saleScore.service.ITScoreItemService;

/**
 * 评分记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */
@Service
public class TScoreItemServiceImpl implements ITScoreItemService 
{
    @Autowired
    private TScoreItemMapper tScoreItemMapper;

    @Autowired
    private ITScoreRuleService tScoreRuleService;

    /**
     * 查询评分记录
     * 
     * @param id 评分记录ID
     * @return 评分记录
     */
    @Override
    public TScoreItem selectTScoreItemById(Integer id)
    {
        TScoreItem tScoreItem = tScoreItemMapper.selectTScoreItemById(id);
        tScoreItem.setStatusDesc(ScoreItemStatus.getInfoByCode(tScoreItem.getStatus()));
        return tScoreItem;
    }

    /**
     * 查询评分记录列表
     * 
     * @param tScoreItem 评分记录
     * @return 评分记录
     */
    @Override
    public List<TScoreItem> selectTScoreItemList(TScoreItem tScoreItem)
    {
        List<TScoreItem> list = tScoreItemMapper.selectTScoreItemList(tScoreItem);
        for(TScoreItem item:list){
            item.setStatusDesc(ScoreItemStatus.getInfoByCode(item.getStatus()));
        }

        return list;
    }

    @Override
    public Map selectHQReport(String scoreTime) {
        Map res = new HashMap();
        //获取titleList
        TScoreRule tScoreRule = tScoreRuleService.selectTScoreRuleById(10);
        List<Map> pjList = JSONArray.parseArray(tScoreRule.getContent(), Map.class);


        //获取每个角色占比
        List<Map> roleRatioList = JSONArray.parseArray(tScoreRule.getRoleRatio(), Map.class);
        BigDecimal roleSum = roleRatioList.stream().map(x -> new BigDecimal(String.valueOf(x.get("rolePer")))).reduce(BigDecimal.ZERO,BigDecimal::add);



        List<Map> list = tScoreItemMapper.selectHQReport(scoreTime);


        for(Map item:list){
            List<Map> contentList = JSONArray.parseArray(item.get("scoreContent").toString(), Map.class);
            BigDecimal sum = contentList.stream().map(x -> new BigDecimal(String.valueOf(x.get("grade")))).reduce(BigDecimal.ZERO,BigDecimal::add);
            item.put("sum",sum);
            item.put("contentList",contentList);
        }

        List<String> titleList = ((List<Map>)list.get(0).get("contentList")).stream().map(x->x.get("itemName").toString()).collect(Collectors.toList());
        res.put("titleList",titleList);


        List<String> markNoList = list.stream().map(x->x.get("markNo").toString()).distinct().collect(Collectors.toList());
        List<Map> resList = new ArrayList<>();
        for(String markNo:markNoList){
            //取出该工号评分内容
            List<Map> pfList = list.stream().filter(x->{
                if(Objects.nonNull(x.get("markNo")) && x.get("markNo").toString() .equals(markNo)){
                    return true;
                }
                return false;
            }).collect(Collectors.toList());


            //按照角色进行求总分
            List<BigDecimal> roleFSList = new ArrayList<>();
            for(Map roleRatioItem : roleRatioList){
                List<Map> rolePJList = pfList.stream().filter(x -> {
                    if (Objects.nonNull(x.get("roleCode")) && x.get("roleCode").toString().equals(roleRatioItem.get("roleCode").toString())) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                if(rolePJList.size()>0){
                    BigDecimal averageBigDecimal = rolePJList.stream().map(x->new BigDecimal(String.valueOf(x.get("sum"))))
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(BigDecimal.valueOf(rolePJList.size()), 5, RoundingMode.HALF_UP);
                    roleFSList.add(averageBigDecimal.multiply(new BigDecimal(String.valueOf(roleRatioItem.get("rolePer"))).divide(roleSum,5,RoundingMode.HALF_UP)));
                }
            }
            BigDecimal endTotal = roleFSList.stream().reduce(BigDecimal.ZERO,BigDecimal::add);
            for(Map pfItem:pfList){
                pfItem.put("endTotal",endTotal.setScale(2, RoundingMode.HALF_UP));
                resList.add(pfItem);
            }

            //按照角色进行求平均分，涉及 titleList， pfList， roleRatioList， pfList.contentList
            List<Map> averageItems = new ArrayList<>();
            Map average = new HashMap();
            average.put("markNo",markNo);
            average.put("markName",pfList.get(0).get("markName"));
            average.put("saleName","平均");
            average.put("serviceName","--");
            average.put("roleName","--");
            average.put("workName","--");
            for(String titleItem:titleList){
                Map contentItem = new HashMap();
                contentItem.put("itemName",titleItem);
                List<Map> itemContentList = pfList.stream().map(x -> {
                    Map itemPF = new HashMap();
                    itemPF.put("roleCode", x.get("roleCode"));
                    Map pfContentItem = ((List<Map>) x.get("contentList")).stream().filter(r -> titleItem.equals(r.get("itemName"))).findFirst().orElse(null);
                    itemPF.put("itemName", pfContentItem.get("itemName"));
                    itemPF.put("grade", pfContentItem.get("grade"));
                    itemPF.put("itemCount", pfContentItem.get("itemCount"));
                    return itemPF;
                }).collect(Collectors.toList());
                if(itemContentList.size()>0){
                    List<BigDecimal> roleFSItemList = new ArrayList<>();
                    for(Map roleRatioItem : roleRatioList){
                        List<Map> rolePJItemList = itemContentList.stream().filter(x -> {
                            if (Objects.nonNull(x.get("roleCode")) && x.get("roleCode").toString().equals(roleRatioItem.get("roleCode").toString())) {
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
                        if(rolePJItemList.size()>0){
                            BigDecimal itemAverageBigDecimal = rolePJItemList.stream().map(x->new BigDecimal(String.valueOf(x.get("grade"))))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .divide(BigDecimal.valueOf(rolePJItemList.size()),  5, RoundingMode.HALF_UP);
                            roleFSItemList.add(itemAverageBigDecimal.multiply(new BigDecimal(String.valueOf(roleRatioItem.get("rolePer"))).divide(roleSum,3,RoundingMode.HALF_UP)));
                        }
                    }
                    BigDecimal avaItemGrade = roleFSItemList.stream().reduce(BigDecimal.ZERO,BigDecimal::add);
                    contentItem.put("grade",avaItemGrade.setScale(3,RoundingMode.HALF_UP));
                }
                averageItems.add(contentItem);

            }
            BigDecimal avaSum = averageItems.stream().map(x -> new BigDecimal(String.valueOf(x.get("grade")))).reduce(BigDecimal.ZERO,BigDecimal::add).setScale(2,RoundingMode.HALF_UP);
            average.put("contentList",averageItems);
            average.put("sum",avaSum);
            average.put("endTotal",avaSum);
            resList.add(average);

        }

        res.put("list",resList);


        return res;
    }

    /**
     * 新增评分记录
     * 
     * @param tScoreItem 评分记录
     * @return 结果
     */
    @Override
    public int insertTScoreItem(TScoreItem tScoreItem)
    {
        tScoreItem.setCreateTime(DateUtils.getNowDate());
        return tScoreItemMapper.insertTScoreItem(tScoreItem);
    }

    /**
     * 修改评分记录
     * 
     * @param tScoreItem 评分记录
     * @return 结果
     */
    @Override
    public int updateTScoreItem(TScoreItem tScoreItem)
    {
        tScoreItem.setUpdateTime(DateUtils.getNowDate());
//        tScoreItem.setStatus(ScoreItemStatus.Write.getCode());
        return tScoreItemMapper.updateTScoreItem(tScoreItem);
    }



    /**
     * 删除评分记录信息
     * 
     * @param tScoreItem 评分记录ID
     * @return 结果
     */
    @Override
    public int deleteTScoreItemById(TScoreItem tScoreItem)
    {
        tScoreItem.setDeleteTime(DateUtils.getNowDate());
        return tScoreItemMapper.deleteTScoreItemById(tScoreItem);
    }

    @Override
    public TableDataInfo selectSelfItemList(TScoreItem tScoreItem) {
        List<Map> selfItemList= tScoreItemMapper.selectSelfItemList(tScoreItem);
        for(Map item:selfItemList){
            item.put("statusDesc",ScoreItemStatus.getInfoByCode(Integer.valueOf(item.get("status").toString())));
        }

        long total = new PageInfo(selfItemList).getTotal();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(selfItemList);
        rspData.setTotal(total);
        return rspData;
    }
}
