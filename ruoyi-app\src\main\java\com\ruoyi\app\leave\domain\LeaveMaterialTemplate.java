package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**excel导入模板 */
public class LeaveMaterialTemplate extends BaseEntity {
    /** 物资名称 */
    @Excel(name = "物资名称")
    private String materialName;

    /** 物资型号规格 */
    @Excel(name = "物资型号规格")
    private String materialSpec;

    /** 计划数量 */
    @Excel(name = "计划数量")
    private BigDecimal planNum;

    /** 计量单位 */
    @Excel(name = "计量单位")
    private String measureUnit;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialSpec() {
        return materialSpec;
    }

    public void setMaterialSpec(String materialSpec) {
        this.materialSpec = materialSpec;
    }

    public BigDecimal getPlanNum() {
        return planNum;
    }

    public void setPlanNum(BigDecimal planNum) {
        this.planNum = planNum;
    }

    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
