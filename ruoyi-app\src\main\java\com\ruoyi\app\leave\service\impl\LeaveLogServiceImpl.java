package com.ruoyi.app.leave.service.impl;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import jakarta.json.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeaveLogMapper;
import com.ruoyi.app.leave.domain.LeaveLog;
import com.ruoyi.app.leave.service.ILeaveLogService;

/**
 * 出门证日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeaveLogServiceImpl implements ILeaveLogService 
{
    @Autowired
    private LeaveLogMapper leaveLogMapper;

    /**
     * 查询出门证日志
     * 
     * @param id 出门证日志ID
     * @return 出门证日志
     */
    @Override
    public LeaveLog selectLeaveLogById(Long id)
    {
        return leaveLogMapper.selectLeaveLogById(id);
    }

    /**
     * 查询出门证日志列表
     * 
     * @param leaveLog 出门证日志
     * @return 出门证日志
     */
    @Override
    public List<LeaveLog> selectLeaveLogList(LeaveLog leaveLog)
    {
        return leaveLogMapper.selectLeaveLogList(leaveLog);
    }

    /**
     * 新增出门证日志
     * 
     * @param leaveLog 出门证日志
     * @return 结果
     */
    @Override
    public int insertLeaveLog(LeaveLog leaveLog)
    {
        leaveLog.setCreateTime(DateUtils.getNowDate());
        return leaveLogMapper.insertLeaveLog(leaveLog);
    }

    /**
     * 修改出门证日志
     * 
     * @param leaveLog 出门证日志
     * @return 结果
     */
    @Override
    public int updateLeaveLog(LeaveLog leaveLog)
    {
        return leaveLogMapper.updateLeaveLog(leaveLog);
    }

    /**
     * 批量删除出门证日志
     * 
     * @param ids 需要删除的出门证日志ID
     * @return 结果
     */
    @Override
    public int deleteLeaveLogByIds(Long[] ids)
    {
        return leaveLogMapper.deleteLeaveLogByIds(ids);
    }

    /**
     * 删除出门证日志信息
     * 
     * @param id 出门证日志ID
     * @return 结果
     */
    @Override
    public int deleteLeaveLogById(Long id)
    {
        return leaveLogMapper.deleteLeaveLogById(id);
    }

    @Override
    public LeaveLog handleLeaveLog(LeaveLog leaveLog) {


        return null;
    }
}
