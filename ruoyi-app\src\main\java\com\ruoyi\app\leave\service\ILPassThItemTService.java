package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LPassThItemT;

/**
 * 退货申请物资明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface ILPassThItemTService 
{
    /**
     * 查询退货申请物资明细
     * 
     * @param id 退货申请物资明细ID
     * @return 退货申请物资明细
     */
    public LPassThItemT selectLPassThItemTById(Long id);

    /**
     * 查询退货申请物资明细列表
     * 
     * @param lPassThItemT 退货申请物资明细
     * @return 退货申请物资明细集合
     */
    public List<LPassThItemT> selectLPassThItemTList(LPassThItemT lPassThItemT);

    /**
     * 新增退货申请物资明细
     * 
     * @param lPassThItemT 退货申请物资明细
     * @return 结果
     */
    public int insertLPassThItemT(LPassThItemT lPassThItemT);

    /**
     * 修改退货申请物资明细
     * 
     * @param lPassThItemT 退货申请物资明细
     * @return 结果
     */
    public int updateLPassThItemT(LPassThItemT lPassThItemT);

    /**
     * 批量删除退货申请物资明细
     * 
     * @param ids 需要删除的退货申请物资明细ID
     * @return 结果
     */
    public int deleteLPassThItemTByIds(Long[] ids);

    /**
     * 删除退货申请物资明细信息
     * 
     * @param id 退货申请物资明细ID
     * @return 结果
     */
    public int deleteLPassThItemTById(Long id);
}
