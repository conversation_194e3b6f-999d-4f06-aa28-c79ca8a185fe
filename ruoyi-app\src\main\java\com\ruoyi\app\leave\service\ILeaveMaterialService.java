package com.ruoyi.app.leave.service;

import java.util.List;
import com.ruoyi.app.leave.domain.LeaveMaterial;

/**
 * 出门证物资Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ILeaveMaterialService 
{
    /**
     * 查询出门证物资
     * 
     * @param id 出门证物资ID
     * @return 出门证物资
     */
    public LeaveMaterial selectLeaveMaterialById(Long id);

    /**
     * 查询出门证物资列表
     * 
     * @param leaveMaterial 出门证物资
     * @return 出门证物资集合
     */
    public List<LeaveMaterial> selectLeaveMaterialList(LeaveMaterial leaveMaterial);

    /**
     * 新增出门证物资
     * 
     * @param leaveMaterial 出门证物资
     * @return 结果
     */
    public int insertLeaveMaterial(LeaveMaterial leaveMaterial,String workNo);

    /**
     * 修改出门证物资
     * 
     * @param leaveMaterial 出门证物资
     * @return 结果
     */
    public int updateLeaveMaterial(LeaveMaterial leaveMaterial);

    /**
     * 批量删除出门证物资
     * 
     * @param ids 需要删除的出门证物资ID
     * @return 结果
     */
    public int deleteLeaveMaterialByIds(Long[] ids);

    /**
     * 删除出门证物资信息
     * 
     * @param id 出门证物资ID
     * @return 结果
     */
    public int deleteLeaveMaterialById(Long id);
}
