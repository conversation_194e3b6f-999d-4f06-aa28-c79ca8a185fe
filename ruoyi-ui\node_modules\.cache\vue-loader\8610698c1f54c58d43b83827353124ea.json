{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\department\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\department\\index.vue", "mtime": 1756170476830}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGVwYXJ0bWVudCwgZ2V0RGVwYXJ0bWVudCwgZGVsRGVwYXJ0bWVudCwgYWRkRGVwYXJ0bWVudCwgdXBkYXRlRGVwYXJ0bWVudCxjYW5jZWxEZXBhcnRtZW50LCBleHBvcnREZXBhcnRtZW50IH0gZnJvbSAiQC9hcGkvbGVhdmUvZGVwYXJ0bWVudCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRlcGFydG1lbnQiLA0KICBjb21wb25lbnRzOiB7DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlh7rpl6jor4Hpg6jpl6jvvIjljoLlhoXljZXkvY3vvInooajmoLzmlbDmja4NCiAgICAgIGRlcGFydG1lbnRMaXN0OiBbXSwNCiAgICAgIC8vIOWNleS9jeWxgue6p+WIl+ihqA0KICAgICAgbGV2ZWxPcHRpb25zOlsNCiAgICAgICAgICB7a2V5OiIxIixsYWJlbDoi5LiA57qn5Y2V5L2NIix2YWx1ZToxfSwNCiAgICAgICAgICB7a2V5OiIyIixsYWJlbDoi5LqM57qn5Y2V5L2NIix2YWx1ZToyfSwNCiAgICAgICAgICB7a2V5OiIzIixsYWJlbDoi5LiJ57qn5Y2V5L2NIix2YWx1ZTozfSwNCiAgICAgICAgICB7a2V5OiI0IixsYWJlbDoi5Zub57qn5Y2V5L2NIix2YWx1ZTo0fSwNCiAgICAgIF0sDQogICAgICAvLyDljLrln5/liJfooagNCiAgICAgIGFyZWFPcHRpb25zOlsNCiAgICAgICAgICB7a2V5OiIxIixsYWJlbDoi5ruo5rGfIix2YWx1ZToxfSwNCiAgICAgICAgICB7a2V5OiIyIixsYWJlbDoi5p2o5biCIix2YWx1ZToyfSwNCiAgICAgIF0sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgdmFsaWRGbGFnOiBudWxsLA0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgc3RvcmVOYW1lOiBudWxsLA0KICAgICAgICBxdWVyeVdvcmQ6IG51bGwsDQogICAgICAgIHR5cGU6IG51bGwsDQogICAgICAgIHVuaXROYW1lOiBudWxsLA0KICAgICAgICBwb3NpdGlvbjogbnVsbCwNCiAgICAgICAgbG93ZXJMaW1pdDogbnVsbCwNCiAgICAgICAgdXBMaW1pdDogbnVsbCwNCiAgICAgICAgbWVtbzogbnVsbCwNCiAgICAgICAgZmlkOiBudWxsLA0KICAgICAgICBmU3RvcmVOYW1lOiBudWxsLA0KICAgICAgICBsZXZlbE5hbWU6IG51bGwsDQogICAgICAgIGFyZWE6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouWHuumXqOivgemDqOmXqO+8iOWOguWGheWNleS9je+8ieWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdERlcGFydG1lbnQodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZGVwYXJ0bWVudExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICB2YWxpZEZsYWc6IG51bGwsDQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBzdG9yZU5hbWU6IG51bGwsDQogICAgICAgIHF1ZXJ5V29yZDogbnVsbCwNCiAgICAgICAgdHlwZTogbnVsbCwNCiAgICAgICAgdW5pdE5hbWU6IG51bGwsDQogICAgICAgIHBvc2l0aW9uOiBudWxsLA0KICAgICAgICBsb3dlckxpbWl0OiBudWxsLA0KICAgICAgICB1cExpbWl0OiBudWxsLA0KICAgICAgICBtZW1vOiBudWxsLA0KICAgICAgICBmaWQ6IG51bGwsDQogICAgICAgIGZTdG9yZU5hbWU6IG51bGwsDQogICAgICAgIGxldmVsTmFtZTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICBhcmVhOm51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Ye66Zeo6K+B6YOo6Zeo77yI5Y6C5YaF5Y2V5L2N77yJIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIGdldERlcGFydG1lbnQoaWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWHuumXqOivgemDqOmXqO+8iOWOguWGheWNleS9je+8iSI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVEZXBhcnRtZW50KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGREZXBhcnRtZW50KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5L2c5bqf5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQ2FuY2VsKHJvdykgew0KICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTkvZzlup8/JywgIuitpuWRiiIsIHsNCiAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgdHlwZTogIndhcm5pbmciDQogICAgfSkNCiAgICAudGhlbigoKSA9PiB7DQogICAgICByZXR1cm4gZ2V0RGVwYXJ0bWVudChpZHMpOw0KICAgIH0pDQogICAgLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIHJldHVybiBjYW5jZWxEZXBhcnRtZW50KHRoaXMuZm9ybSk7DQogICAgfSkNCiAgICAudGhlbigoKSA9PiB7DQogICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuS9nOW6n+aIkOWKnyIpOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSkNCiAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6ZmkPycsICLorablkYoiLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICAgIHJldHVybiBkZWxEZXBhcnRtZW50KGlkcyk7DQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgY29uc3QgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5Ye66Zeo6K+B6YOo6Zeo77yI5Y6C5YaF5Y2V5L2N77yJ5pWw5o2u6aG5PycsICLorablkYoiLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICAgIHJldHVybiBleHBvcnREZXBhcnRtZW50KHF1ZXJ5UGFyYW1zKTsNCiAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOw0KICAgICAgICB9KQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAySA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/leave/department", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\r\n      <!-- <el-form-item label=\"状态\" prop=\"validFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.validFlag\"\r\n          placeholder=\"请输入状态\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"厂内单位名称\" prop=\"storeName\">\r\n        <el-input\r\n          v-model=\"queryParams.storeName\"\r\n          placeholder=\"请输入厂内单位名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"拼音头缩写\" prop=\"queryWord\">\r\n        <el-input\r\n          v-model=\"queryParams.queryWord\"\r\n          placeholder=\"请输入拼音头缩写\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" prop=\"type\">\r\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" clearable size=\"small\">\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"单位名称\" prop=\"unitName\">\r\n        <el-input\r\n          v-model=\"queryParams.unitName\"\r\n          placeholder=\"请输入单位名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"库房位置\" prop=\"position\">\r\n        <el-input\r\n          v-model=\"queryParams.position\"\r\n          placeholder=\"请输入库房位置\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"最小限制\" prop=\"lowerLimit\">\r\n        <el-input\r\n          v-model=\"queryParams.lowerLimit\"\r\n          placeholder=\"请输入最小限制\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"最大限制\" prop=\"upLimit\">\r\n        <el-input\r\n          v-model=\"queryParams.upLimit\"\r\n          placeholder=\"请输入最大限制\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"备注信息\" prop=\"memo\">\r\n        <el-input\r\n          v-model=\"queryParams.memo\"\r\n          placeholder=\"请输入备注信息\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"上级单位编码\" prop=\"fid\">\r\n        <el-input\r\n          v-model=\"queryParams.fid\"\r\n          placeholder=\"请输入上级单位编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"上级单位名称\" prop=\"fStoreName\">\r\n        <el-input\r\n          v-model=\"queryParams.fStoreName\"\r\n          placeholder=\"请输入上级单位名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"单位层级\" prop=\"levelName\">\r\n        <el-input\r\n          v-model=\"queryParams.levelName\"\r\n          placeholder=\"请输入单位层级\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleCancel\"\r\n        >作废</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['leave:department:remove']\"\r\n        >删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col>\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"departmentList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"validFlag\" /> -->\r\n      <!-- <el-table-column label=\"厂内单位编码\" align=\"center\" prop=\"id\" /> -->\r\n      <el-table-column label=\"厂内单位名称\" align=\"center\" prop=\"storeName\" />\r\n      <el-table-column label=\"拼音头缩写\" align=\"center\" prop=\"queryWord\" />\r\n      <!-- <el-table-column label=\"区域\" align=\"center\" prop=\"area\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.area == '1'\" type=\"info\">滨江</el-tag>\r\n          <el-tag v-if=\"scope.row.area == '2'\" type=\"info\">杨市</el-tag>\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column label=\"原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" align=\"center\" prop=\"type\" />\r\n      <el-table-column label=\"单位名称\" align=\"center\" prop=\"unitName\" />\r\n      <el-table-column label=\"库房位置\" align=\"center\" prop=\"position\" />\r\n      <el-table-column label=\"最小限制\" align=\"center\" prop=\"lowerLimit\" />\r\n      <el-table-column label=\"最大限制\" align=\"center\" prop=\"upLimit\" />\r\n      <el-table-column label=\"备注信息\" align=\"center\" prop=\"memo\" /> -->\r\n      <!-- <el-table-column label=\"上级单位编码\" align=\"center\" prop=\"fid\" />\r\n      <el-table-column label=\"上级单位名称\" align=\"center\" prop=\"fStoreName\" /> -->\r\n      <el-table-column label=\"单位层级\" align=\"center\" prop=\"levelName\" >\r\n        <template slot-scope=\"scope\">\r\n             {{levelOptions[parseInt(scope.row.levelName)-1]['label']}}\r\n            </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"添加人\" align=\"center\" prop=\"createBy\" />\r\n      <el-table-column label=\"添加时间\" align=\"center\" prop=\"createTime\" />\r\n      <el-table-column label=\"修改人\" align=\"center\" prop=\"updateBy\" />\r\n      <el-table-column label=\"修改时间\" align=\"center\" prop=\"updateTime\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-lock\"\r\n            @click=\"handleCancel(scope.row)\"\r\n          >作废</el-button>\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['leave:department:remove']\"\r\n          >删除</el-button> -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改出门证部门（厂内单位）对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <!-- <el-form-item label=\"状态\" prop=\"validFlag\">\r\n          <el-input v-model=\"form.validFlag\" placeholder=\"请输入状态\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"厂内单位编码\" prop=\"id\">\r\n          <el-input v-model=\"form.id\" placeholder=\"请输入厂内单位编码\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"厂内单位名称\" prop=\"storeName\">\r\n          <el-input v-model=\"form.storeName\" placeholder=\"请输入厂内单位名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"拼音头缩写\" prop=\"queryWord\">\r\n          <el-input v-model=\"form.queryWord\" placeholder=\"请输入拼音头缩写\" />\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"区域\" prop=\"area\">\r\n          <el-select v-model=\"form.area\" placeholder=\"请选择区域\">\r\n                                <el-option\r\n                                v-for=\"dict in areaOptions\"\r\n                                :key=\"dict.key\"\r\n                                :label=\"dict.label\"\r\n                                :value=\"dict.value\">\r\n                                <span style=\"float: left\">{{ dict.label }}</span>\r\n                                </el-option>\r\n            </el-select>\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" prop=\"type\">\r\n          <el-select v-model=\"form.type\" placeholder=\"请选择原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\">\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位名称\" prop=\"unitName\">\r\n          <el-input v-model=\"form.unitName\" placeholder=\"请输入单位名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"库房位置\" prop=\"position\">\r\n          <el-input v-model=\"form.position\" placeholder=\"请输入库房位置\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"最小限制\" prop=\"lowerLimit\">\r\n          <el-input v-model=\"form.lowerLimit\" placeholder=\"请输入最小限制\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"最大限制\" prop=\"upLimit\">\r\n          <el-input v-model=\"form.upLimit\" placeholder=\"请输入最大限制\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"备注信息\" prop=\"memo\">\r\n          <el-input v-model=\"form.memo\" placeholder=\"请输入备注信息\" />\r\n        </el-form-item>\r\n        <!-- <el-form-item label=\"上级单位编码\" prop=\"fid\">\r\n          <el-input v-model=\"form.fid\" placeholder=\"请输入上级单位编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"上级单位名称\" prop=\"fStoreName\">\r\n          <el-input v-model=\"form.fStoreName\" placeholder=\"请输入上级单位名称\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"单位层级\" prop=\"levelName\">\r\n           <el-select v-model=\"form.levelName\" placeholder=\"请选择单位层级\">\r\n                                <el-option\r\n                                v-for=\"dict in levelOptions\"\r\n                                :key=\"dict.key\"\r\n                                :label=\"dict.label\"\r\n                                :value=\"dict.value\">\r\n                                <span style=\"float: left\">{{ dict.label }}</span>\r\n                                </el-option>\r\n            </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDepartment, getDepartment, delDepartment, addDepartment, updateDepartment,cancelDepartment, exportDepartment } from \"@/api/leave/department\";\r\n\r\nexport default {\r\n  name: \"Department\",\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证部门（厂内单位）表格数据\r\n      departmentList: [],\r\n      // 单位层级列表\r\n      levelOptions:[\r\n          {key:\"1\",label:\"一级单位\",value:1},\r\n          {key:\"2\",label:\"二级单位\",value:2},\r\n          {key:\"3\",label:\"三级单位\",value:3},\r\n          {key:\"4\",label:\"四级单位\",value:4},\r\n      ],\r\n      // 区域列表\r\n      areaOptions:[\r\n          {key:\"1\",label:\"滨江\",value:1},\r\n          {key:\"2\",label:\"杨市\",value:2},\r\n      ],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        validFlag: null,\r\n        id: null,\r\n        storeName: null,\r\n        queryWord: null,\r\n        type: null,\r\n        unitName: null,\r\n        position: null,\r\n        lowerLimit: null,\r\n        upLimit: null,\r\n        memo: null,\r\n        fid: null,\r\n        fStoreName: null,\r\n        levelName: null,\r\n        area: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询出门证部门（厂内单位）列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDepartment(this.queryParams).then(response => {\r\n        this.departmentList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        validFlag: null,\r\n        id: null,\r\n        storeName: null,\r\n        queryWord: null,\r\n        type: null,\r\n        unitName: null,\r\n        position: null,\r\n        lowerLimit: null,\r\n        upLimit: null,\r\n        memo: null,\r\n        fid: null,\r\n        fStoreName: null,\r\n        levelName: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null,\r\n        area:null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加出门证部门（厂内单位）\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getDepartment(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改出门证部门（厂内单位）\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateDepartment(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDepartment(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 作废按钮操作 */\r\n    handleCancel(row) {\r\n    const ids = row.id || this.ids;\r\n    this.$confirm('是否确认作废?', \"警告\", {\r\n      confirmButtonText: \"确定\",\r\n      cancelButtonText: \"取消\",\r\n      type: \"warning\"\r\n    })\r\n    .then(() => {\r\n      return getDepartment(ids);\r\n    })\r\n    .then(response => {\r\n      this.form = response.data;\r\n      return cancelDepartment(this.form);\r\n    })\r\n    .then(() => {\r\n      this.msgSuccess(\"作废成功\");\r\n      this.getList();\r\n    })\r\n  },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delDepartment(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证部门（厂内单位）数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return exportDepartment(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}