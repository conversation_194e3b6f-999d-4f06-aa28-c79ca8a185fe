package com.ruoyi.app.qualityCost.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.io.File;
import java.io.FileOutputStream;
import java.text.DecimalFormat;

import com.google.common.collect.Lists;
import com.ruoyi.app.qualityCost.domain.CostCenter;
import com.ruoyi.app.qualityCost.domain.QualityCostMonthlyVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.qualityCost.mapper.QualityCostDetailMapper;
import com.ruoyi.app.qualityCost.domain.QualityCostDetail;
import com.ruoyi.app.qualityCost.domain.QualityCostSumVO;
import com.ruoyi.app.qualityCost.service.IQualityCostDetailService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.config.RuoYiConfig;

/**
 * 兴澄特钢质量成本总Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class QualityCostDetailServiceImpl implements IQualityCostDetailService
{
    private static final List<String> TYPE_CODES = Arrays.asList("A1","A11","A12","A13","A2","A3","A4","A","B1","B2","B3","B31","B32","B33","B4","B","C1","C11","C12","C13","C14","C2","C3","C4","C","D1","D11","D12","D13","D14","D2","D3","D4","D","Z");
    // private static final List<String> TYPE_CODES_SUM = Arrays.asList("A","B","C","D","Z");
    private static final String COMPANY_CODE = "JYXCTZG";
    private static final Map<String, String> CATEGORY_MAP = new HashMap<>();
    static {
        // 添加所有类目到 HashMap
        CATEGORY_MAP.put("A1", "一、质量管理费");
        CATEGORY_MAP.put("A11", "1.差旅费");
        CATEGORY_MAP.put("A12", "2.会议费");
        CATEGORY_MAP.put("A13", "3.其他费用");
        CATEGORY_MAP.put("A2", "二、质量培训费");
        CATEGORY_MAP.put("A3", "三、质量评审费");
        CATEGORY_MAP.put("A4", "四、质量管理人员工资及附加");
        CATEGORY_MAP.put("A", "小计");
        
        CATEGORY_MAP.put("B1", "一、试验检验费");
        CATEGORY_MAP.put("B2", "二、外部检测费");
        CATEGORY_MAP.put("B3", "三、质量检测设备费用");
        CATEGORY_MAP.put("B31", "1.购置费用");
        CATEGORY_MAP.put("B32", "2.维护费用");
        CATEGORY_MAP.put("B33", "3.折旧费用");
        CATEGORY_MAP.put("B4", "四、质量检测人员工资及附加");
        CATEGORY_MAP.put("B", "小计");

        CATEGORY_MAP.put("C1", "一、产品报废损失");
        CATEGORY_MAP.put("C11", "1.产品报废量（普通类）");
        CATEGORY_MAP.put("C12", "2.产品报废量（高钼钢）");
        CATEGORY_MAP.put("C13", "3.产品报废量（高Ni钢）");
        CATEGORY_MAP.put("C14", "4.产品报废量（高镍钼钢）");
        CATEGORY_MAP.put("C2", "二、产品改判损失");
        CATEGORY_MAP.put("C3", "三、产品脱合同损失");
        CATEGORY_MAP.put("C4", "四、产品挽救处理项");
        CATEGORY_MAP.put("C", "小计");
        
        CATEGORY_MAP.put("D1", "一、质量异议退货损失");
        CATEGORY_MAP.put("D11", "1.质量异议报废量（普通类）");
        CATEGORY_MAP.put("D12", "2.质量异议报废量（高钼钢）");
        CATEGORY_MAP.put("D13", "3.质量异议报废量（高Ni钢）");
        CATEGORY_MAP.put("D14", "4.质量异议报废量（高镍钼钢）");
        CATEGORY_MAP.put("D2", "二、客户索赔费");
        CATEGORY_MAP.put("D3", "三、质量异议运费");
        CATEGORY_MAP.put("D4", "四、质量异议差旅费");
        CATEGORY_MAP.put("D", "小计");
        
        CATEGORY_MAP.put("Z", "合计");
    }
    @Autowired
    private QualityCostDetailMapper qualityCostDetailMapper;

    /**
     * 查询兴澄特钢质量成本总
     * 
     * @param costCenterCname 兴澄特钢质量成本总ID
     * @return 兴澄特钢质量成本总
     */
    @Override
    public QualityCostDetail selectQualityCostDetailById(String costCenterCname)
    {
        return qualityCostDetailMapper.selectQualityCostDetailById(costCenterCname);
    }

    /**
     * 查询兴澄特钢质量成本总列表
     * 
     * @param qualityCostDetail 兴澄特钢质量成本总
     * @return 兴澄特钢质量成本总
     */
    @Override
    public List<QualityCostDetail> selectQualityCostDetailList(QualityCostDetail qualityCostDetail)
    {
        return qualityCostDetailMapper.selectQualityCostDetailList(qualityCostDetail);
    }

    @Override
    public List<QualityCostDetail> selectQualityCostDetailListSum(QualityCostDetail qualityCostDetail)
    {
        return qualityCostDetailMapper.selectQualityCostDetailListSum(qualityCostDetail);
    }

    @Override
    public QualityCostMonthlyVO selectMonthlyQualityCostDetailList(QualityCostDetail qualityCostDetail) {
        QualityCostMonthlyVO qualityCostMonthlyVO = new QualityCostMonthlyVO();
        List<QualityCostDetail> list = qualityCostDetailMapper.selectQualityCostDetailList(qualityCostDetail);
        //过滤掉精整工艺名相关科目
        list = this.handleList(list, qualityCostDetail);
        qualityCostMonthlyVO.setQualityCostDetailList( list);
//        qualityCostMonthlyVO.setQualityCostSumVOList(this.convertToSumVO(list));

        this.filterType(qualityCostMonthlyVO, qualityCostDetail.getTypeCodeList());

        return qualityCostMonthlyVO;
    }

    private void filterType(QualityCostMonthlyVO qualityCostMonthlyVO, List<String> typeCodeList){
        if(CollectionUtils.isEmpty(typeCodeList)){
            return;
        }
        List<QualityCostDetail> qualityCostDetailList = qualityCostMonthlyVO.getQualityCostDetailList();
        List<QualityCostSumVO> qualityCostSumVOList = qualityCostMonthlyVO.getQualityCostSumVOList();

        List<QualityCostDetail> result1 = Lists.newArrayList();
        for (QualityCostDetail qualityCostDetail : qualityCostDetailList) {
            //产量单独处理
            if("Z".equals(qualityCostDetail.getTypeCode())){
                result1.add(qualityCostDetail);
                continue;
            }
            for (String typeCode : typeCodeList) {
                if(qualityCostDetail.getTypeCode().startsWith(typeCode)){
                    result1.add(qualityCostDetail);
                    break;
                }
            }
        }

//        List<QualityCostSumVO> result2 = Lists.newArrayList();
//        for (QualityCostSumVO qualityCostSumVO : qualityCostSumVOList) {
//            if("ALL".equals(qualityCostSumVO.getTypeCode())){
//                result2.add(qualityCostSumVO);
//                continue;
//            }
//            for (String typeCode : typeCodeList) {
//                if(qualityCostSumVO.getTypeCode().startsWith(typeCode)){
//                    result2.add(qualityCostSumVO);
//                    break;
//                }
//            }
//
//        }

        qualityCostMonthlyVO.setQualityCostDetailList(result1);
//        qualityCostMonthlyVO.setQualityCostSumVOList(result2);
    }

    public List<QualityCostDetail> handleList(List<QualityCostDetail> list, QualityCostDetail param){
        List<String> typeCodes = TYPE_CODES;
        //仅保留typeCode为"A1","A11","A12","A13","A2","A3","A4","A","B1","B2","B3","B31","B32","B4","B","C4","C","D2","D3","D4","D","S"的记录
        list = list.stream().filter(x->typeCodes.contains(x.getTypeCode())).collect(Collectors.toList());


        //若缺失TYPE_CODES中的任意一个，则将缺失的typeCode对应的记录添加到list中，缺失的数据需要填充以下字段：COST_CENTER、COST_CENTER_CNAME、YEAR_MONTH、TYPE_NAME、TYPE_CODE
        for (String typeCode : typeCodes) {
            if(!list.stream().anyMatch(y->y.getTypeCode().equals(typeCode))){
                QualityCostDetail qualityCostDetail = new QualityCostDetail();
                qualityCostDetail.setCostCenter(param.getCostCenter());
//                qualityCostDetail.setCostCenterCname(list.get(0).getCostCenterCname());
                qualityCostDetail.setYearMonth(param.getYearMonth());
                qualityCostDetail.setTypeName(CATEGORY_MAP.get(typeCode));
                qualityCostDetail.setTypeCode(typeCode);
                list.add(qualityCostDetail);
            }
        }


        //遍历list，将typeName映射为category
        list.forEach(x->{
            x.setTypeName(CATEGORY_MAP.get(x.getTypeCode()));
        });
        //按TYPE_CODES数组顺序排序
        list.sort(Comparator.comparing(x->typeCodes.indexOf(x.getTypeCode())));

        return list;
    }

    public List<QualityCostSumVO> convertToSumVO(List<QualityCostDetail> list){
        //先将list分组，typeCode为A开头、B开头、C开头、D开头，分组好后，对每组的COST_EX、COST_PER_EX求和，然后转换为QualityCostSumVO
        Map<String, List<QualityCostDetail>> map = list.stream().collect(Collectors.groupingBy(x->x.getTypeCode().startsWith("A")?"A":x.getTypeCode().startsWith("B")?"B":x.getTypeCode().startsWith("C")?"C":x.getTypeCode().startsWith("D")?"D":"S"));
        List<QualityCostSumVO> qualityCostSumVOs = new ArrayList<>();
        map.forEach((key, value) -> {
            if(!"S".equals( key)){
                QualityCostSumVO vo = new QualityCostSumVO();
                vo.setTypeCode(key);
                vo.setCostEx(map.get(key).stream().filter(x->Objects.nonNull(x.getCostEx())).map(QualityCostDetail::getCostEx).reduce(BigDecimal.ZERO, BigDecimal::add));
                vo.setCostPerEx(map.get(key).stream().filter(x->Objects.nonNull(x.getCostPerEx())).map(QualityCostDetail::getCostPerEx).reduce(BigDecimal.ZERO, BigDecimal::add));
                qualityCostSumVOs.add(vo);
            }
        });
        //先计算qualityCostSumVOs中的costEx的和，然后计算percent
        BigDecimal totalCostEx = qualityCostSumVOs.stream().map(QualityCostSumVO::getCostEx).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(Objects.nonNull(totalCostEx) && totalCostEx.compareTo(BigDecimal.ZERO)>0){
            qualityCostSumVOs.forEach(x->{
                x.setPercent(x.getCostEx().divide(totalCostEx, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
            });
        }


        //质量成本合计
        QualityCostSumVO qualityCostSumVO = new QualityCostSumVO();
        qualityCostSumVO.setTypeCode("ALL");
        qualityCostSumVO.setCostEx(qualityCostSumVOs.stream().filter(x->Objects.nonNull(x.getCostEx())).map(QualityCostSumVO::getCostEx).reduce(BigDecimal.ZERO, BigDecimal::add));
        qualityCostSumVO.setCostPerEx(qualityCostSumVOs.stream().filter(x->Objects.nonNull(x.getCostPerEx())).map(QualityCostSumVO::getCostPerEx).reduce(BigDecimal.ZERO, BigDecimal::add));
        qualityCostSumVO.setPercent(new BigDecimal(100));
        qualityCostSumVOs.add(qualityCostSumVO);

        return qualityCostSumVOs;
    }
        

    @Override
    public List<CostCenter> selectCostCenterList(QualityCostDetail qualityCostDetail)
    {
        return qualityCostDetailMapper.selectCostCenterList(qualityCostDetail);
    }

    /**
     * 查询预防成本数据
     * 
     * @param qualityCostDetail 查询条件
     * @return 预防成本数据集合
     */
    @Override
    public List<QualityCostDetail> selectPreventionCostList(QualityCostDetail qualityCostDetail)
    {
        return qualityCostDetailMapper.selectPreventionCostList(qualityCostDetail);
    }

    /**
     * 新增兴澄特钢质量成本总
     * 
     * @param qualityCostDetail 兴澄特钢质量成本总
     * @return 结果
     */
    @Override
    public int insertQualityCostDetail(QualityCostDetail qualityCostDetail)
    {
        return qualityCostDetailMapper.insertQualityCostDetail(qualityCostDetail);
    }

    /**
     * 修改兴澄特钢质量成本总
     * 
     * @param qualityCostDetail 兴澄特钢质量成本总
     * @return 结果
     */
    @Override
    public int updateQualityCostDetail(QualityCostDetail qualityCostDetail)
    {
        return qualityCostDetailMapper.updateQualityCostDetail(qualityCostDetail);
    }

    /**
     * 批量删除兴澄特钢质量成本总
     * 
     * @param costCenterCnames 需要删除的兴澄特钢质量成本总ID
     * @return 结果
     */
    @Override
    public int deleteQualityCostDetailByIds(String[] costCenterCnames)
    {
        return qualityCostDetailMapper.deleteQualityCostDetailByIds(costCenterCnames);
    }

    /**
     * 删除兴澄特钢质量成本总信息
     * 
     * @param costCenterCname 兴澄特钢质量成本总ID
     * @return 结果
     */
    @Override
    public int deleteQualityCostDetailById(String costCenterCname)
    {
        return qualityCostDetailMapper.deleteQualityCostDetailById(costCenterCname);
    }

    @Override
    public List<QualityCostDetail> selectAppraisalCost(String costCenterCname, String yearMonth) {
        return qualityCostDetailMapper.selectAppraisalCost(costCenterCname, yearMonth);
    }

    @Override
    public List<QualityCostDetail> selectInternalFailureCost(String costCenterCname, String yearMonth) {
        return qualityCostDetailMapper.selectInternalFailureCost(costCenterCname, yearMonth);
    }

    /**
     * 查询产品挽救处理项列表
     * 
     * @param costCenterCname 成本中心名称
     * @param yearMonth 会计期间
     * @return 产品挽救处理项数据列表
     */
    @Override
    public List<QualityCostDetail> selectSalvageItemsList(String costCenterCname, String yearMonth) {
        return qualityCostDetailMapper.selectSalvageItemsList(costCenterCname, yearMonth);
    }

    /**
     * 查询外部损失成本数据
     *
     * @param costCenterCname 成本中心名称
     * @param yearMonth 会计期间
     * @return 外部损失成本数据列表
     */
    @Override
    @DataSource(DataSourceType.XCC1)
    public List<QualityCostDetail> selectExternalFailureCost(String costCenterCname, String yearMonth) {
        return qualityCostDetailMapper.selectExternalFailureCost(costCenterCname, yearMonth);
    }

    /**
     * 导出兴澄特钢质量成本表
     *
     * @param qualityCostDetail 查询条件
     * @return 导出结果
     */
    @Override
    public AjaxResult exportQualityCostPage(QualityCostDetail qualityCostDetail) {
        try {
            // 获取数据，使用与页面相同的方法
            QualityCostMonthlyVO monthlyVO = this.selectMonthlyQualityCostDetailList(qualityCostDetail);
            List<QualityCostDetail> list = monthlyVO != null ? monthlyVO.getQualityCostDetailList() : null;

            if (list == null || list.isEmpty()) {
                return AjaxResult.error("暂无数据可导出");
            }

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("兴澄特钢质量成本表");

            // 创建样式
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            CellStyle boldDataStyle = createBoldDataStyle(workbook);

            // 创建标题行
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("兴澄特钢质量成本表");
            titleCell.setCellStyle(titleStyle);

            // 合并标题行单元格（合并9列：A-I）
            CellRangeAddress titleRegion = new CellRangeAddress(0, 0, 0, 8);
            sheet.addMergedRegion(titleRegion);
            // 为标题合并区域设置边框
            setBorderForMergedRegion(sheet, titleRegion, workbook);

            // 添加销量信息行
            Row salesRow = sheet.createRow(1);
            Cell salesLabelCell = salesRow.createCell(7);
            if (qualityCostDetail.getCostCenter().equals("JYXCTZG")) {
                salesLabelCell.setCellValue("销量");
            } else {
                salesLabelCell.setCellValue("产量");
            }

            salesLabelCell.setCellStyle(dataStyle);

            //获取销量或产量
            QualityCostDetail salesData = list.stream().filter(x -> x.getTypeCode().equals("Z")).findFirst().orElse(null);
            Cell salesValueCell = salesRow.createCell(8);
            if (salesData.getCostTon() != null) {
                salesValueCell.setCellValue(salesData.getCostTon().toString() + "吨");
            } else {
                salesValueCell.setCellValue("无");
            }

            salesValueCell.setCellStyle(dataStyle);

            // 合并销量行前7个单元格
            CellRangeAddress salesMergeRegion = new CellRangeAddress(1, 1, 0, 6);
            sheet.addMergedRegion(salesMergeRegion);
            setBorderForMergedRegion(sheet, salesMergeRegion, workbook);


            // 创建多级表头（行号需要调整）
            createMultiLevelHeaders(sheet, headerStyle);

            // 填充数据并处理合并
            fillDataWithMerging(sheet, list, dataStyle, boldDataStyle, workbook);

            // 设置列宽
            setColumnWidths(sheet);

            //成本类别边框修复（调整行号：原来1-2，现在2-3）
            CellRangeAddress titleRegion2 = new CellRangeAddress(2, 3, 0, 0);
            // 为标题合并区域设置边框
            setBorderForMergedRegion(sheet, titleRegion2, workbook);
            //金额百分比边框修复（调整行号：原来1-2，现在2-3）
            CellRangeAddress titleRegion3 = new CellRangeAddress(2, 3, 8, 8);
            // 为标题合并区域设置边框
            setBorderForMergedRegion(sheet, titleRegion3, workbook);



            // 生成文件名
            String fileName = "兴澄特钢质量成本表_" +
                (qualityCostDetail.getYearMonth() != null ? qualityCostDetail.getYearMonth() : "全部") +
                "_" + System.currentTimeMillis() + ".xlsx";

            // 保存文件到下载目录
            String downloadDir = RuoYiConfig.getProfile() + "/download";
            File dir = new File(downloadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String filePath = downloadDir + "/" + fileName;

            FileOutputStream fileOut = new FileOutputStream(filePath);
            workbook.write(fileOut);
            fileOut.close();
            workbook.close();

            return AjaxResult.success(fileName);

        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("导出失败：" + e.getMessage());
        }
    }

    /**
     * 创建标题样式
     */
    private CellStyle createTitleStyle(Workbook workbook) {
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 添加边框
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        return titleStyle;
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        return headerStyle;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        // 设置边框颜色，确保边框可见
        dataStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        dataStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        dataStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        dataStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        return dataStyle;
    }

    /**
     * 创建加粗数据样式
     */
    private CellStyle createBoldDataStyle(Workbook workbook) {
        CellStyle boldDataStyle = workbook.createCellStyle();
        boldDataStyle.setAlignment(HorizontalAlignment.CENTER);
        boldDataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框
        boldDataStyle.setBorderTop(BorderStyle.THIN);
        boldDataStyle.setBorderBottom(BorderStyle.THIN);
        boldDataStyle.setBorderLeft(BorderStyle.THIN);
        boldDataStyle.setBorderRight(BorderStyle.THIN);
        // 设置边框颜色，确保边框可见
        boldDataStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        boldDataStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        boldDataStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        boldDataStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 设置加粗字体
        Font boldFont = workbook.createFont();
        boldFont.setBold(true);
        boldDataStyle.setFont(boldFont);
        return boldDataStyle;
    }

    /**
     * 创建多级表头
     */
    private void createMultiLevelHeaders(Sheet sheet, CellStyle headerStyle) {
        // 第一级表头（现在从第2行开始，因为第1行是销量信息）
        Row headerRow1 = sheet.createRow(2);

        // 成本类别
        Cell cell0 = headerRow1.createCell(0);
        cell0.setCellValue("成本类别");
        cell0.setCellStyle(headerStyle);
        CellRangeAddress region0 = new CellRangeAddress(2, 3, 0, 0);
        sheet.addMergedRegion(region0);

        // 科目
        Cell cell1 = headerRow1.createCell(1);
        cell1.setCellValue("科目");
        cell1.setCellStyle(headerStyle);
        CellRangeAddress region1 = new CellRangeAddress(2, 3, 1, 1);
        sheet.addMergedRegion(region1);

        // 质量成本（列入项）
        Cell cell2 = headerRow1.createCell(2);
        cell2.setCellValue("质量成本（列入项）");
        cell2.setCellStyle(headerStyle);
        CellRangeAddress region2 = new CellRangeAddress(2, 2, 2, 3);
        sheet.addMergedRegion(region2);

        // 质量成本（不列入项）
        Cell cell4 = headerRow1.createCell(4);
        cell4.setCellValue("质量成本（不列入项）");
        cell4.setCellStyle(headerStyle);
        CellRangeAddress region4 = new CellRangeAddress(2, 2, 4, 5);
        sheet.addMergedRegion(region4);

        // 质量成本（总）
        Cell cell6 = headerRow1.createCell(6);
        cell6.setCellValue("质量成本（总）");
        cell6.setCellStyle(headerStyle);
        CellRangeAddress region6 = new CellRangeAddress(2, 2, 6, 7);
        sheet.addMergedRegion(region6);

        // 金额百分比
        Cell cell8 = headerRow1.createCell(8);
        cell8.setCellValue("金额百分比");
        cell8.setCellStyle(headerStyle);
        CellRangeAddress region8 = new CellRangeAddress(2, 3, 8, 8);
        sheet.addMergedRegion(region8);

        // 第二级表头（现在从第3行开始）
        Row headerRow2 = sheet.createRow(3);

        // 质量成本（列入项）子列
        Cell subCell2 = headerRow2.createCell(2);
        subCell2.setCellValue("金额");
        subCell2.setCellStyle(headerStyle);

        Cell subCell3 = headerRow2.createCell(3);
        subCell3.setCellValue("吨钢金额");
        subCell3.setCellStyle(headerStyle);

        // 质量成本（不列入项）子列
        Cell subCell4 = headerRow2.createCell(4);
        subCell4.setCellValue("金额");
        subCell4.setCellStyle(headerStyle);

        Cell subCell5 = headerRow2.createCell(5);
        subCell5.setCellValue("吨钢金额");
        subCell5.setCellStyle(headerStyle);

        // 质量成本（总）子列
        Cell subCell6 = headerRow2.createCell(6);
        subCell6.setCellValue("金额");
        subCell6.setCellStyle(headerStyle);

        Cell subCell7 = headerRow2.createCell(7);
        subCell7.setCellValue("吨钢金额");
        subCell7.setCellStyle(headerStyle);
    }

    /**
     * 填充数据并处理合并
     */
    private void fillDataWithMerging(Sheet sheet, List<QualityCostDetail> list, CellStyle dataStyle, CellStyle boldDataStyle, Workbook workbook) {
        int rowIndex = 4; // 从第5行开始填充数据（因为增加了销量行）

        // 按成本类型分组，用于合并单元格
        Map<String, List<QualityCostDetail>> groupedData = new LinkedHashMap<>();
        for (QualityCostDetail item : list) {
            String costType = getCostTypeDisplay(item.getTypeCode());
            groupedData.computeIfAbsent(costType, k -> new ArrayList<>()).add(item);
        }

        // 记录合并区域
        List<CellRangeAddress> mergeRegions = new ArrayList<>();

        for (Map.Entry<String, List<QualityCostDetail>> entry : groupedData.entrySet()) {
            String costType = entry.getKey();
            List<QualityCostDetail> items = entry.getValue();

            int startRow = rowIndex;

            for (QualityCostDetail item : items) {
                Row dataRow = sheet.createRow(rowIndex);

                // 判断是否为合计行
                boolean isTotalRow = item.getTypeName() != null &&
                    (item.getTypeName().contains("合计") || item.getTypeName().contains("小计"));
                CellStyle currentStyle = isTotalRow ? boldDataStyle : dataStyle;

                // 成本类别（所有行都设置值和样式，确保合并后边框完整）
                Cell cell0 = dataRow.createCell(0);
                cell0.setCellValue(costType); // 所有行都设置值，不只是第一行
                cell0.setCellStyle(currentStyle);

                // 科目名称
                Cell cell1 = dataRow.createCell(1);
                cell1.setCellValue(item.getTypeName() != null ? item.getTypeName() : "-");
                cell1.setCellStyle(currentStyle);

                // 质量成本（列入项）- 金额
                Cell cell2 = dataRow.createCell(2);
                cell2.setCellValue(formatCurrency(item.getCostEx()));
                cell2.setCellStyle(currentStyle);

                // 质量成本（列入项）- 吨钢金额
                Cell cell3 = dataRow.createCell(3);
                cell3.setCellValue(formatCurrency(item.getCostPerEx()));
                cell3.setCellStyle(currentStyle);

                // 质量成本（不列入项）- 金额
                Cell cell4 = dataRow.createCell(4);
                cell4.setCellValue(formatCurrency(item.getNincEx()));
                cell4.setCellStyle(currentStyle);

                // 质量成本（不列入项）- 吨钢金额
                Cell cell5 = dataRow.createCell(5);
                cell5.setCellValue(formatCurrency(item.getNincPerEx()));
                cell5.setCellStyle(currentStyle);

                // 质量成本（总）- 金额
                Cell cell6 = dataRow.createCell(6);
                cell6.setCellValue(formatCurrency(item.getAllcEx()));
                cell6.setCellStyle(currentStyle);

                // 质量成本（总）- 吨钢金额
                Cell cell7 = dataRow.createCell(7);
                cell7.setCellValue(formatCurrency(item.getAllcPerEx()));
                cell7.setCellStyle(currentStyle);

                // 金额百分比
                Cell cell8 = dataRow.createCell(8);
                cell8.setCellValue(formatPercentage(item.getAmountPercent()));
                cell8.setCellStyle(currentStyle);

                rowIndex++;
            }

            // 添加成本类别列的合并区域
            if (items.size() > 1) {
                mergeRegions.add(new CellRangeAddress(startRow, rowIndex - 1, 0, 0));
            }
        }

        // 应用合并区域并设置边框
        for (CellRangeAddress region : mergeRegions) {
            // 先为合并区域设置边框，再进行合并
            setBorderForMergedRegion(sheet, region, workbook);
            sheet.addMergedRegion(region);
        }
    }

    /**
     * 设置列宽
     */
    private void setColumnWidths(Sheet sheet) {
        sheet.setColumnWidth(0, 4000);  // 成本类别
        sheet.setColumnWidth(1, 6000);  // 科目
        sheet.setColumnWidth(2, 4000);  // 质量成本-金额
        sheet.setColumnWidth(3, 4000);  // 质量成本-吨钢金额
        sheet.setColumnWidth(4, 4000);  // 不列入项-金额
        sheet.setColumnWidth(5, 4000);  // 不列入项-吨钢金额
        sheet.setColumnWidth(6, 4000);  // 总-金额
        sheet.setColumnWidth(7, 4000);  // 总-吨钢金额
        sheet.setColumnWidth(8, 3000);  // 金额百分比
    }

    /**
     * 为合并区域设置边框
     */
    private void setBorderForMergedRegion(Sheet sheet, CellRangeAddress region, Workbook workbook) {
        // 先使用RegionUtil为整个合并区域设置边框，这是最可靠的方法
        RegionUtil.setBorderTop(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);

        // 为合并区域的所有单元格确保有正确的样式和边框
        for (int row = region.getFirstRow(); row <= region.getLastRow(); row++) {
            Row currentRow = sheet.getRow(row);
            if (currentRow == null) {
                currentRow = sheet.createRow(row);
            }
            for (int col = region.getFirstColumn(); col <= region.getLastColumn(); col++) {
                Cell cell = currentRow.getCell(col);
                if (cell == null) {
                    cell = currentRow.createCell(col);
                }

                // 获取或创建单元格样式
                CellStyle cellStyle = cell.getCellStyle();
                CellStyle newStyle;

                if (cellStyle != null && cellStyle.getIndex() != 0) {
                    // 如果有原有样式，复制并确保边框设置
                    newStyle = workbook.createCellStyle();
                    newStyle.cloneStyleFrom(cellStyle);
                } else {
                    // 如果没有原有样式，创建新样式
                    newStyle = workbook.createCellStyle();
                    newStyle.setAlignment(HorizontalAlignment.CENTER);
                    newStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                }

                // 强制设置所有边框，确保边框完整
                newStyle.setBorderTop(BorderStyle.THIN);
                newStyle.setBorderBottom(BorderStyle.THIN);
                newStyle.setBorderLeft(BorderStyle.THIN);
                newStyle.setBorderRight(BorderStyle.THIN);

                // 设置边框颜色，确保可见性
                newStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
                newStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                newStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
                newStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

                // 应用样式
                cell.setCellStyle(newStyle);
            }
        }
    }

    /**
     * 获取成本类型显示名称
     */
    private String getCostTypeDisplay(String typeCode) {
        if (typeCode == null) return "-";

        // 根据typeCode的第一个字符确定成本类型
        if (typeCode.startsWith("A")) {
            return "预防成本";
        } else if (typeCode.startsWith("B")) {
            return "鉴定成本";
        } else if (typeCode.startsWith("C")) {
            return "内部损失成本";
        } else if (typeCode.startsWith("D")) {
            return "外部损失成本";
        } else if (typeCode.equals("Z")) {
            return "汇总";
        } else {
            return typeCode;
        }
    }

    /**
     * 格式化货币，与前端保持一致
     */
    private String formatCurrency(BigDecimal value) {
        if (value == null || value.compareTo(BigDecimal.ZERO) == 0) {
            return "-";
        }
        DecimalFormat df = new DecimalFormat("#,##0.00");
        return df.format(value);
    }

    /**
     * 格式化百分比，与前端保持一致
     */
    private String formatPercentage(BigDecimal value) {
        if (value == null || value.compareTo(BigDecimal.ZERO) == 0) {
            return "-";
        }
        // 根据用户要求，将数值乘以100，格式化为三位小数，并添加百分号
        DecimalFormat df = new DecimalFormat("#,##0.000");
        return df.format(value.multiply(new BigDecimal(100))) + "%";
    }

    /**
     * 导出工厂成本汇总表
     *
     * @param requestData 请求数据
     * @return 导出结果
     */
    @Override
    public AjaxResult exportFactoryCostSummary(Map<String, Object> requestData) {
        try {
            // 从请求数据中获取前端处理后的数据
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> manualTypeList = (List<Map<String, Object>>) requestData.get("manualTypeList");
            @SuppressWarnings("unchecked")
            List<String> manualArray = (List<String>) requestData.get("manualArray");
            String currentCostTypeTitle = (String) requestData.get("currentCostTypeTitle");
            String yearMonth = (String) requestData.get("yearMonth");

            if (manualTypeList == null || manualArray == null) {
                return AjaxResult.error("缺少必要的导出数据");
            }

            if (manualTypeList == null || manualTypeList.isEmpty()) {
                return AjaxResult.error("暂无数据可导出");
            }

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("工厂成本汇总表");

            // 创建样式
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            CellStyle boldDataStyle = createBoldDataStyle(workbook);

            // 生成标题
            String title = generateFactoryTitle(currentCostTypeTitle);

            // 创建标题行
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(title);
            titleCell.setCellStyle(titleStyle);

            // 计算需要合并的列数（成本类别 + 科目 + 成本中心数量）
            int totalColumns = 2 + manualArray.size();
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, totalColumns - 1));

            // 创建表头
            createFactoryHeaders(sheet, headerStyle, manualArray);

            // 填充数据
            fillFactoryData(sheet, manualTypeList, manualArray, dataStyle, boldDataStyle);

            // 设置列宽
            setFactoryColumnWidths(sheet, manualArray.size());

            // 生成文件名
            String fileName = "工厂成本汇总表_" +
                (yearMonth != null ? yearMonth : "全部") +
                "_" + (currentCostTypeTitle != null && !currentCostTypeTitle.isEmpty() ? currentCostTypeTitle : "全部") +
                "_" + System.currentTimeMillis() + ".xlsx";

            // 保存文件到下载目录
            String downloadDir = RuoYiConfig.getProfile() + "/download";
            File dir = new File(downloadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String filePath = downloadDir + "/" + fileName;

            FileOutputStream fileOut = new FileOutputStream(filePath);
            workbook.write(fileOut);
            fileOut.close();
            workbook.close();

            return AjaxResult.success(fileName);

        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("导出失败：" + e.getMessage());
        }
    }

    /**
     * 生成工厂成本汇总表标题
     */
    private String generateFactoryTitle(String currentCostTypeTitle) {
        if (currentCostTypeTitle == null || currentCostTypeTitle.isEmpty()) {
            return "各分厂金额汇总（元）";
        }
        return "各分厂" + currentCostTypeTitle + "金额汇总（元）";
    }

    /**
     * 创建工厂成本汇总表表头
     */
    private void createFactoryHeaders(Sheet sheet, CellStyle headerStyle, List<String> manualArray) {
        // 创建表头行
        Row headerRow = sheet.createRow(1);

        // 成本类别列
        Cell cell0 = headerRow.createCell(0);
        cell0.setCellValue("成本类别");
        cell0.setCellStyle(headerStyle);

        // 科目列
        Cell cell1 = headerRow.createCell(1);
        cell1.setCellValue("科目");
        cell1.setCellStyle(headerStyle);



        // 成本中心列
        int colIndex = 2;
        for (String centerName : manualArray) {
            if (!"typeName".equals(centerName) && !"costType".equals(centerName)) {
                Cell cell = headerRow.createCell(colIndex++);
                cell.setCellValue(centerName);
                cell.setCellStyle(headerStyle);
            }
        }
    }

    /**
     * 填充工厂成本汇总表数据
     */
    private void fillFactoryData(Sheet sheet, List<Map<String, Object>> manualTypeList, List<String> manualArray,
                                CellStyle dataStyle, CellStyle boldDataStyle) {
        int rowIndex = 2; // 从第3行开始（0-based）

        // 用于记录需要合并的单元格信息
        List<MergeInfo> mergeInfoList = new ArrayList<>();
        String currentCostType = null;
        int mergeStartRow = -1;

        for (int i = 0; i < manualTypeList.size(); i++) {
            Map<String, Object> typeItem = manualTypeList.get(i);
            Row dataRow = sheet.createRow(rowIndex);

            // 成本类别
            Cell cell0 = dataRow.createCell(0);
            Object costType = typeItem.get("costType");
            String costTypeStr = costType != null ? costType.toString() : "";

            // 检查是否需要开始新的合并区域
            if (!costTypeStr.equals(currentCostType)) {
                // 如果之前有合并区域，先记录下来
                if (currentCostType != null && mergeStartRow != -1 && rowIndex - 1 > mergeStartRow) {
                    mergeInfoList.add(new MergeInfo(mergeStartRow, rowIndex - 1, currentCostType));
                }
                // 开始新的合并区域
                currentCostType = costTypeStr;
                mergeStartRow = rowIndex;
            }

            cell0.setCellValue(costTypeStr);
            cell0.setCellStyle(dataStyle);

            // 科目
            Cell cell1 = dataRow.createCell(1);
            Object typeName = typeItem.get("typeName");
            cell1.setCellValue(typeName != null ? typeName.toString() : "");
            cell1.setCellStyle(dataStyle);

            // 各成本中心数据
            int colIndex = 2;
            for (String centerName : manualArray) {
                if (!"typeName".equals(centerName) && !"costType".equals(centerName)) {
                    Cell cell = dataRow.createCell(colIndex++);
                    Object centerData = typeItem.get(centerName);

                    if (centerData != null) {
                        if (centerData instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> dataMap = (Map<String, Object>) centerData;
                            Object costEx = dataMap.get("costEx");
                            if (costEx != null) {
                                try {
                                    BigDecimal value = new BigDecimal(costEx.toString());
                                    cell.setCellValue(formatCurrency(value));
                                } catch (NumberFormatException e) {
                                    cell.setCellValue("-");
                                }
                            } else {
                                cell.setCellValue("-");
                            }
                        } else {
                            try {
                                BigDecimal value = new BigDecimal(centerData.toString());
                                cell.setCellValue(formatCurrency(value));
                            } catch (NumberFormatException e) {
                                cell.setCellValue("-");
                            }
                        }
                    } else {
                        cell.setCellValue("-");
                    }
                    cell.setCellStyle(dataStyle);
                }
            }

            rowIndex++;
        }

        // 处理最后一个合并区域
        if (currentCostType != null && mergeStartRow != -1 && rowIndex - 1 > mergeStartRow) {
            mergeInfoList.add(new MergeInfo(mergeStartRow, rowIndex - 1, currentCostType));
        }

        // 应用单元格合并
        for (MergeInfo mergeInfo : mergeInfoList) {
            if (mergeInfo.endRow > mergeInfo.startRow) {
                // 合并第一列（成本类别列）
                sheet.addMergedRegion(new CellRangeAddress(mergeInfo.startRow, mergeInfo.endRow, 0, 0));

                // 清空合并区域中除第一行外的其他行的第一列内容
                for (int r = mergeInfo.startRow + 1; r <= mergeInfo.endRow; r++) {
                    Row row = sheet.getRow(r);
                    if (row != null) {
                        Cell cell = row.getCell(0);
                        if (cell != null) {
                            cell.setCellValue(""); // 清空内容，通过合并显示
                        }
                    }
                }
            }
        }
    }

    /**
     * 设置工厂成本汇总表列宽
     */
    private void setFactoryColumnWidths(Sheet sheet, int manualArraySize) {
        // 成本类别列
        sheet.setColumnWidth(0, 3000);
        // 科目列
        sheet.setColumnWidth(1, 8000);

        // 成本中心列（减去typeName和costType两个非数据列）
        int dataColumns = manualArraySize - 2;
        for (int i = 2; i < 2 + dataColumns; i++) {
            sheet.setColumnWidth(i, 4000);
        }
    }

    /**
     * 单元格合并信息内部类
     */
    private static class MergeInfo {
        int startRow;
        int endRow;
        String costType;

        public MergeInfo(int startRow, int endRow, String costType) {
            this.startRow = startRow;
            this.endRow = endRow;
            this.costType = costType;
        }
    }

    public void setBorderStyle(CellRangeAddress region, HSSFSheet sheet){
        BorderStyle bordertemp = BorderStyle.THIN;
        RegionUtil.setBorderBottom(bordertemp, region, sheet);
        RegionUtil.setBorderLeft(bordertemp, region, sheet);     //左边框
        RegionUtil.setBorderRight(bordertemp, region, sheet);    //右边框
        RegionUtil.setBorderTop(bordertemp, region, sheet);      //上边框
    }
}
