{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue", "mtime": 1756170476805}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBhbnN3ZXJzdGF0dXNsaXN0QWRtaW4gLGZvcm1GcmVxdWVuY3l9IGZyb20gIkAvYXBpL3RZankvZm9ybSI7DQppbXBvcnQgYW5zd2VySW5wdXQgZnJvbSAiLi9pbnB1dCI7DQppbXBvcnQgeyBuZXdBZGQsIGFkZEFsb25lIH0gZnJvbSAiQC9hcGkvdFlqeS9hbnN3ZXIiOw0KaW1wb3J0IHsgZ2V0QWxsUm9vdExpc3RGb3JBbnN3ZXIsZ2V0QWxsUm9vdExpc3QgfSBmcm9tICJAL2FwaS90WWp5L2RpbWVuc2lvbmFsaXR5IjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCmltcG9ydCBheGlvcyBmcm9tICJheGlvcyI7DQppbXBvcnQgKiBhcyB4bHN4IGZyb20gJ3hsc3gnOw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiQW5zd2VyIiwNCiAgY29tcG9uZW50czogeyBhbnN3ZXJJbnB1dCB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBwaWNrZXJPcHRpb25zOiB7DQogICAgICAgIGRpc2FibGVkRGF0ZSh0aW1lKSB7DQogICAgICAgICAgcmV0dXJuIHRpbWUuZ2V0VGltZSgpID4gRGF0ZS5ub3coKTsNCiAgICAgICAgfSwNCiAgICAgIH0sDQogICAgICBmcmVxdWVuY3lPcHRpb25zOiBbXSwNCg0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgZm9ybVF1ZXN0aW9uOiB1bmRlZmluZWQsDQogICAgICAgIGZjRGF0ZTogdW5kZWZpbmVkLA0KICAgICAgICBkaW1lbnNpb25hbGl0eUlkOiB1bmRlZmluZWQsDQogICAgICAgIGZvcm1RdWVzdGlvbjogdW5kZWZpbmVkLA0KICAgICAgfSwNCg0KICAgICAgZm9ybVR5cGU6IG51bGwsDQogICAgICBkaW1lbnNpb25hbGl0eU5hbWVzOiBudWxsLA0KICAgICAgZGltZW5zaW9uYWxpdHlOYW1lOiBudWxsLA0KICAgICAgc3BlY2lhbEZjRGF0ZTpudWxsLA0KICAgICAgZHJhd2VyU2hvdzogZmFsc2UsDQogICAgICBzdGlja3lUb3A6IDAsDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICBhbnN3ZXJMaXN0OiBbXSwNCiAgICAgIGZvcm1EYXRhOiB7fSwNCiAgICAgIHJvdzoge30sDQogICAgICByb290TGlzdDogW10sDQogICAgICB1c2VySW5mbzoge30sDQogICAgICBkYXRlc2F2ZTp7fSwNCiAgICAgIHBhdGhzYXZlOnt9LA0KICAgICAgZGVwdE5hbWU6bnVsbCwNCiAgICAgIGRhdGVWYWx1ZTogbnVsbCwNCiAgICAgIHF1ZXJ5SW1wb3J0OiB7DQogICAgICAgIHN0YXJ0RGF0ZTogbnVsbCwNCiAgICAgICAgZW5kRGF0ZTogbnVsbCwNCiAgICAgICAgcm9vdElkOiBudWxsLA0KICAgICAgfSwNCiAgICAgIGNvdW50OjEsDQogICAgICBpbXBvcnRPcGVuOmZhbHNlLA0KICAgICAgU3BlY2lhbEltcG9ydE9wZW46ZmFsc2UsDQogICAgICBjb25uZWN0T3BlbjpmYWxzZSwNCiAgICAgIHByZVZpZXdPcGVuOmZhbHNlLA0KICAgICAgLy8g5a+85YWl5Y+C5pWwDQogICAgICB1cGxvYWQ6IHsNCiAgICAgICAgLy8g5piv5ZCm56aB55So5LiK5LygDQogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoDQogICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSB9LA0KICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYANCiAgICAgICAgdXJsOg0KICAgICAgICAgIHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKw0KICAgICAgICAgICIvd2ViL1RZankvYW5zd2VyL2ltcG9ydERhdGEiLA0KDQogICAgICB9LA0KDQogICAgICB1cGxvYWRTcGVjaWFsOiB7DQogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oA0KICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsDQogICAgICAgIC8vIOiuvue9ruS4iuS8oOeahOivt+axguWktOmDqA0KICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkgfSwNCiAgICAgICAgLy8g5LiK5Lyg55qE5Zyw5Z2ADQogICAgICAgIHVybDoNCiAgICAgICAgICBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsNCiAgICAgICAgICAiL3dlYi9UWWp5L2Fuc3dlci9pbXBvcnREYXRhU3BlY2lhbCIsDQoNCiAgICAgIH0sDQogICAgICBleGNlbEh0bWw6IiIsDQogICAgICBzZWFyY2hvcGVuOmZhbHNlLA0KICAgICAgZXhjZWxEYXRhOiBbXSwgLy8g5a2Y5YKoIEV4Y2VsIOaVsOaNrg0KICAgICAgZXhjZWx0aXRsZTogW10sDQogICAgICBjdXN0b21CbG9iQ29udGVudDoiIg0KICAgIH07DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy51c2VySW5mbyA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy4kc3RvcmUuc3RhdGUudXNlcikpOw0KICB9LA0KDQogIGNyZWF0ZWQoKSB7DQogICAgY29uc3QgZGltZW5zaW9uYWxpdHlJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmRpbWVuc2lvbmFsaXR5SWQ7DQogICAgY29uc3QgZmNEYXRlID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmNEYXRlOw0KDQogICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWU9dGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZGltZW5zaW9uYWxpdHlOYW1lOw0KICAgIHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZD1kaW1lbnNpb25hbGl0eUlkDQogICAgdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGU9ZmNEYXRlDQogICAgdGhpcy5pbml0RGF0YSgpOw0KDQoNCiAgICAvLyBpZih0aGlzLiRyb3V0ZS5xdWVyeSkNCiAgICAvLyB7DQogICAgLy8gICBjb25zdCBkaW1lbnNpb25hbGl0eUlkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZGltZW5zaW9uYWxpdHlJZDsNCiAgICAvLyAgIC8vIGNvbnN0IGRpbWVuc2lvbmFsaXR5TmFtZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmRpbWVuc2lvbmFsaXR5TmFtZTsNCiAgICAvLyAgIGNvbnN0IGZjRGF0ZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5ICYmIHRoaXMuJHJvdXRlLnF1ZXJ5LmZjRGF0ZTsNCiAgICAvLyAgIHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZD1kaW1lbnNpb25hbGl0eUlkDQogICAgLy8gICAvLyB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5TmFtZT1kaW1lbnNpb25hbGl0eU5hbWUNCiAgICAvLyAgIHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlPWZjRGF0ZQ0KICAgIC8vICAgdGhpcy5pbml0RGF0YTEoKTsNCiAgICAvLyB9DQogICAgLy8gZWxzZQ0KICAgIC8vIHsNCiAgICAvLyAgIHRoaXMuaW5pdERhdGEoKTsNCiAgICAvLyB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBvbkRhdGVDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZyh0aGlzLmRhdGVWYWx1ZSk7DQogICAgICBpZiAodGhpcy5kYXRlVmFsdWUgIT0gbnVsbCAmJiB0aGlzLmRhdGVWYWx1ZSAhPSAiIikgew0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnN0YXJ0RGF0ZSA9IHRoaXMuZGF0ZVZhbHVlWzBdOw0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LmVuZERhdGUgPSB0aGlzLmRhdGVWYWx1ZVsxXTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuc3RhcnREYXRlID0gIiI7DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuZW5kRGF0ZSA9ICIiOw0KICAgICAgfQ0KICAgIH0sDQogICAgY2xpY2tOb2RlKCRldmVudCwgbm9kZSkgew0KICAgICAgJGV2ZW50LnRhcmdldC5wYXJlbnRFbGVtZW50LnBhcmVudEVsZW1lbnQuZmlyc3RFbGVtZW50Q2hpbGQuY2xpY2soKTsNCiAgICB9LA0KICAgIGNoYW5nZUV2ZW50KHBhcmFtcykgew0KICAgICAgY29uc3QgJGZvcm0gPSB0aGlzLiRyZWZzLmZvcm1SZWY7DQogICAgICBpZiAoJGZvcm0pIHsNCiAgICAgICAgJGZvcm0udXBkYXRlU3RhdHVzKHBhcmFtcyk7DQogICAgICB9DQogICAgfSwNCiAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgcmV0dXJuIHRpbWUuZ2V0VGltZSgpIDwgRGF0ZS5ub3coKSAtIDguNjRlNzsgLy8gOC42NGU3IOavq+enkuaVsOS7o+ihqOS4gOWkqQ0KICAgIH0sDQogICAgaW5wdXRDaGFuZ2UodmFsLCByb3cpIHsNCiAgICAgIHJvdy5mb3JtVmFsdWUgPSB2YWw7DQogICAgfSwNCiAgICBoYW5kbGVTY3JvbGwoKSB7DQogICAgICB0aGlzLmlzU3RpY2t5ID0gd2luZG93LnNjcm9sbFkgPj0gdGhpcy5zdGlja3lUb3A7DQogICAgfSwNCiAgICBpbml0RGF0YSgpIHsNCiAgICAgIC8vIGdldEFsbFJvb3RMaXN0Rm9yQW5zd2VyKCkudGhlbigocmVzKSA9PiB7DQogICAgICAvLyAgIHRoaXMucm9vdExpc3QgPSByZXMuZGF0YTsNCiAgICAgIC8vICAgY29uc29sZS5sb2coImFuZ3J5Iix0aGlzLnF1ZXJ5UGFyYW1zKQ0KICAgICAgICAvLyBpZih0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQ9PW51bGwpDQogICAgICAgIC8vIHsNCiAgICAgICAgLy8gICAvLyB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPSB0aGlzLnJvb3RMaXN0WzBdLnZhbHVlOw0KICAgICAgICAvLyAgIC8vIHRoaXMuZGVwdE5hbWU9IHRoaXMucm9vdExpc3RbMF0uZGVwdE5hbWU7DQogICAgICAgIC8vICAgLy8gdGhpcy5kZXB0Q29kZT0gdGhpcy5yb290TGlzdFswXS5kZXB0Q29kZTsNCiAgICAgICAgLy8gfQ0KICAgICAgICAvLyBlbHNlDQogICAgICAgIC8vIHsNCiAgICAgICAgLy8gICAvLyB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPSB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQ7DQogICAgICAgIC8vICAgZm9yKGxldCBpPTA7aTx0aGlzLnJvb3RMaXN0Lmxlbmd0aDtpKyspDQogICAgICAgIC8vICAgew0KICAgICAgICAvLyAgICAgaWYodGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID09IHRoaXMucm9vdExpc3RbaV0udmFsdWUpDQogICAgICAgIC8vICAgICB7DQogICAgICAgIC8vICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZCA9IHRoaXMucm9vdExpc3RbaV0udmFsdWU7DQogICAgICAgIC8vICAgICAgIHRoaXMuZGVwdE5hbWU9IHRoaXMucm9vdExpc3RbaV0uZGVwdE5hbWU7DQogICAgICAgIC8vICAgICAgIHRoaXMuZGVwdENvZGU9IHRoaXMucm9vdExpc3RbaV0uZGVwdENvZGU7DQogICAgICAgIC8vICAgICB9DQogICAgICAgIC8vICAgfQ0KICAgICAgICAvLyB9DQogICAgICAvLyAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgLy8gfSk7DQogICAgICBnZXRBbGxSb290TGlzdCgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnJvb3RMaXN0ID0gcmVzLmRhdGE7DQogICAgICAgIGlmKHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZD09bnVsbCkNCiAgICAgICAgew0KICAgICAgICAgIC8vIHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZCA9IHRoaXMucm9vdExpc3RbMF0udmFsdWU7DQogICAgICAgICAgLy8gdGhpcy5kZXB0TmFtZT0gdGhpcy5yb290TGlzdFswXS5kZXB0TmFtZTsNCiAgICAgICAgICAvLyB0aGlzLmRlcHRDb2RlPSB0aGlzLnJvb3RMaXN0WzBdLmRlcHRDb2RlOw0KICAgICAgICB9DQogICAgICAgIGVsc2UNCiAgICAgICAgew0KICAgICAgICAgIC8vIHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZCA9IHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZDsNCiAgICAgICAgICBmb3IobGV0IGk9MDtpPHRoaXMucm9vdExpc3QubGVuZ3RoO2krKykNCiAgICAgICAgICB7DQogICAgICAgICAgICBpZih0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPT0gdGhpcy5yb290TGlzdFtpXS52YWx1ZSkNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID0gdGhpcy5yb290TGlzdFtpXS52YWx1ZTsNCiAgICAgICAgICAgICAgdGhpcy5kZXB0TmFtZT0gdGhpcy5yb290TGlzdFtpXS5kZXB0TmFtZTsNCiAgICAgICAgICAgICAgdGhpcy5kZXB0Q29kZT0gdGhpcy5yb290TGlzdFtpXS5kZXB0Q29kZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGluaXREYXRhMSgpIHsNCiAgICAgIGdldEFsbFJvb3RMaXN0Rm9yQW5zd2VyKCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMucm9vdExpc3QgPSByZXMuZGF0YTsNCiAgICAgICAgZm9yKGxldCBpPTA7aTx0aGlzLnJvb3RMaXN0Lmxlbmd0aDtpKyspDQogICAgICAgIHsNCiAgICAgICAgICBpZih0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPT0gdGhpcy5yb290TGlzdFtpXS52YWx1ZSkNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aGlzLmRlcHROYW1lPSB0aGlzLnJvb3RMaXN0WzBdLmRlcHROYW1lOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5p+l6K+iVFlqeUFuc3dlcuWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICBmb3JtRnJlcXVlbmN5KHtkaW1lbnNpb25hbGl0eUlkOiB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWR9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgaWYodGhpcy5jb3VudCE9cmVzLmRhdGEpDQogICAgICAgICAgIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlPXVuZGVmaW5lZA0KICAgICAgICAgICB9DQogICAgICAgICAgIHRoaXMuY291bnQ9cmVzLmRhdGENCiAgICAgIH0pOw0KICAgICAgdGhpcy5hbnN3ZXJMaXN0ID0gW107DQogICAgICBhbnN3ZXJzdGF0dXNsaXN0QWRtaW4oew0KICAgICAgICBmY0RhdGU6IHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlLA0KICAgICAgICBkaW1lbnNpb25hbGl0eUlkOiB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQsDQogICAgICAgIGZvcm1RdWVzdGlvbjogdGhpcy5xdWVyeVBhcmFtcy5mb3JtUXVlc3Rpb24sDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgbGV0IGFuc3dlckxpc3QgPSBbXTsNCiAgICAgICAgbGV0IGxpc3QgPSByZXMuZGF0YTsNCiAgICAgICAgZm9yKGxldCBpPTA7aTxsaXN0Lmxlbmd0aDtpKyspDQogICAgICAgIHsNCiAgICAgICAgICAgIHRoaXMuZGF0ZXNhdmVbbGlzdFtpXS5mb3JtSWRdPWxpc3RbaV0uZm9ybVZhbHVlDQogICAgICAgICAgICB0aGlzLnBhdGhzYXZlW2xpc3RbaV0uZGltZW5zaW9uYWxpdHlOYW1lXT1saXN0W2ldLmRpbWVuc2lvbmFsaXR5UGF0aA0KICAgICAgICB9DQogICAgICAgIC8vIOS9v+eUqCBtYXAg5o+Q5Y+WIGRpbWVuc2lvbmFsaXR5TmFtZSDlsZ7mgKfliLDkuIDkuKrmlbDnu4QNCiAgICAgICAgbGV0IGRpbWVuc2lvbmFsaXR5UGF0aHMgPSBbXQ0KICAgICAgICBsZXQgUGF0aHNldD1bXQ0KICAgICAgICBsaXN0LmZvckVhY2goKHgpID0+IA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGlmKCFQYXRoc2V0LmluY2x1ZGVzKHguZGltZW5zaW9uYWxpdHlQYXRoKSkNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgUGF0aHNldC5wdXNoKHguZGltZW5zaW9uYWxpdHlQYXRoKQ0KICAgICAgICAgICAgICBkaW1lbnNpb25hbGl0eVBhdGhzLnB1c2goeyBvcmlnaW5hbE5hbWU6IHguZGltZW5zaW9uYWxpdHlOYW1lLCBzb3J0S2V5OiB4LmRpbWVuc2lvbmFsaXR5UGF0aH0pDQogICAgICAgICAgICB9ICAgDQogICAgICAgICAgfQ0KICAgICAgICApOw0KICAgICAgICBkaW1lbnNpb25hbGl0eVBhdGhzLmZvckVhY2goKHRpdGxlKSA9PiB7DQogICAgICAgICAgbGV0IGdyb3VwID0gew0KICAgICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAgICAgbGlzdDogW10sDQogICAgICAgICAgfTsNCiAgICAgICAgICBncm91cC50aXRsZSA9IHRpdGxlLm9yaWdpbmFsTmFtZTsNCiAgICAgICAgICBncm91cC5saXN0ID0gbGlzdC5maWx0ZXIoKGl0ZW0pID0+IGl0ZW0uZGltZW5zaW9uYWxpdHlQYXRoID09PSB0aXRsZS5zb3J0S2V5KTsNCiAgICAgICAgICAvLyDlgYforr7kvaDmnInkuIDkuKrmlbDnu4TmnaXlrZjlgqjmiYDmnInnmoTnu4QNCiAgICAgICAgICBhbnN3ZXJMaXN0LnB1c2goZ3JvdXApOyAvLyDlsIbnlJ/miJDnmoTnu4Tmt7vliqDliLBncm91cHPmlbDnu4TkuK0NCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMuYW5zd2VyTGlzdCA9IGFuc3dlckxpc3Q7DQogICAgICAgIGNvbnNvbGUubG9nKCJhbmdyeSIsdGhpcy5hbnN3ZXJMaXN0KTsNCg0KDQoNCiAgICAgICAgLy8gLy8g5L2/55SoIG1hcCDmj5Dlj5YgZGltZW5zaW9uYWxpdHlOYW1lIOWxnuaAp+WIsOS4gOS4quaVsOe7hA0KICAgICAgICAvLyBsZXQgZGltZW5zaW9uYWxpdHlOYW1lcyA9IGxpc3QubWFwKCh4KSA9PiB4LmRpbWVuc2lvbmFsaXR5TmFtZSk7DQoNCiAgICAgICAgLy8gLy8g5o+Q5Y+WIC8g5ZCO55qE5YmN5LiJ5L2N5a2X56ym77yM5bm25LiO5Y6f5a2X56ym5Liy6YWN5a+5DQogICAgICAgIC8vIGRpbWVuc2lvbmFsaXR5TmFtZXMgPSBkaW1lbnNpb25hbGl0eU5hbWVzLm1hcCgobmFtZSkgPT4gew0KICAgICAgICAvLyAgIC8vIGxldCBrZXkgPSBuYW1lLmluY2x1ZGVzKCIvIikgPyBuYW1lLnNwbGl0KCIvIilbMV0uc2xpY2UoMCwgMykgOiAiIjsNCiAgICAgICAgLy8gICBsZXQga2V5ID0gdGhpcy5wYXRoc2F2ZVtuYW1lXTsNCiAgICAgICAgLy8gICByZXR1cm4geyBvcmlnaW5hbE5hbWU6IG5hbWUsIHNvcnRLZXk6IGtleSB9Ow0KICAgICAgICAvLyB9KTsNCg0KICAgICAgICAvLyAvLyDmjInnhafmj5Dlj5blh7rnmoTliY3kuInlrZfnrKbmjpLluo8NCiAgICAgICAgLy8gZGltZW5zaW9uYWxpdHlOYW1lcy5zb3J0KChhLCBiKSA9PiBhLnNvcnRLZXkubG9jYWxlQ29tcGFyZShiLnNvcnRLZXkpKTsNCiAgICAgICAgLy8gLy8gY29uc29sZS5sb2coInRlc3QwIixkaW1lbnNpb25hbGl0eU5hbWVzKQ0KICAgICAgICAvLyAvLyDlpoLmnpzpnIDopoHvvIzlj6/ku6Xmj5Dlj5bmjpLluo/lkI7nmoTljp/lp4vlkI3lrZcNCiAgICAgICAgLy8gZGltZW5zaW9uYWxpdHlOYW1lcyA9IGRpbWVuc2lvbmFsaXR5TmFtZXMubWFwKA0KICAgICAgICAvLyAgIChpdGVtKSA9PiBpdGVtLm9yaWdpbmFsTmFtZQ0KICAgICAgICAvLyApOw0KDQogICAgICAgIC8vIC8vIOS9v+eUqCBTZXQg5Y676YeNDQogICAgICAgIC8vIGxldCB1bmlxdWVEaW1lbnNpb25hbGl0eU5hbWVzID0gWy4uLm5ldyBTZXQoZGltZW5zaW9uYWxpdHlOYW1lcyldOw0KDQogICAgICAgIC8vIHVuaXF1ZURpbWVuc2lvbmFsaXR5TmFtZXMuZm9yRWFjaCgodGl0bGUpID0+IHsNCiAgICAgICAgLy8gICBsZXQgZ3JvdXAgPSB7DQogICAgICAgIC8vICAgICB0aXRsZTogIiIsDQogICAgICAgIC8vICAgICBsaXN0OiBbXSwNCiAgICAgICAgLy8gICB9Ow0KICAgICAgICAvLyAgIGdyb3VwLnRpdGxlID0gdGl0bGU7DQogICAgICAgIC8vICAgZ3JvdXAubGlzdCA9IGxpc3QuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmRpbWVuc2lvbmFsaXR5TmFtZSA9PT0gdGl0bGUpOw0KICAgICAgICAvLyAgIC8vIOWBh+iuvuS9oOacieS4gOS4quaVsOe7hOadpeWtmOWCqOaJgOacieeahOe7hA0KICAgICAgICAvLyAgIGFuc3dlckxpc3QucHVzaChncm91cCk7IC8vIOWwhueUn+aIkOeahOe7hOa3u+WKoOWIsGdyb3Vwc+aVsOe7hOS4rQ0KICAgICAgICAvLyB9KTsNCiAgICAgICAgLy8gdGhpcy5hbnN3ZXJMaXN0ID0gYW5zd2VyTGlzdDsNCiAgICAgICAgLy8gY29uc29sZS5sb2coImFuZ3J5Iix0aGlzLmFuc3dlckxpc3QpOw0KICAgICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIGZvcihsZXQgaT0wO2k8dGhpcy5yb290TGlzdC5sZW5ndGg7aSsrKQ0KICAgICAgew0KICAgICAgICBpZih0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPT0gdGhpcy5yb290TGlzdFtpXS52YWx1ZSkNCiAgICAgICAgew0KICAgICAgICAgIHRoaXMuZGVwdE5hbWU9IHRoaXMucm9vdExpc3RbMF0uZGVwdE5hbWU7DQogICAgICAgICAgdGhpcy5kZXB0Q29kZT0gdGhpcy5yb290TGlzdFtpXS5kZXB0Q29kZTsNCiAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT10aGlzLnJvb3RMaXN0W2ldLmxhYmVsDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgaGFuZGxlRGF0ZUNoYW5nZSgpIHsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlID0gdW5kZWZpbmVkOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5mb3JtUXVlc3Rpb24gPSB1bmRlZmluZWQ7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPSB1bmRlZmluZWQ7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KDQogICAgaGFuZGxlUHJldmlldygpIHsNCiAgICAgIGxldCBxdWVyeUltcG9ydD17fQ0KICAgICAgcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICBxdWVyeUltcG9ydC5mY0RhdGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZQ0KICAgICAgcXVlcnlJbXBvcnQudHlwZT0iMSINCiAgICAgIGlmKHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lPT0n56CU56m26Zmi55uu5qCH5oyH5qCH5LiA6KeIJykNCiAgICAgIHsNCiAgICAgICAgICB0aGlzLmRvd25sb2FkWGxzeCgNCiAgICAgICAgICAiL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRXaXRoVGVtcGxhdGUiLA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIC4uLnF1ZXJ5SW1wb3J0LA0KICAgICAgICAgIH0sDQogICAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgICAiKSIgKw0KICAgICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgICApLnRoZW4oKGJsb2IpID0+IHsNCiAgICAgICAgICBsZXQgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTsNCiAgICAgICAgICByZWFkZXIucmVhZEFzQXJyYXlCdWZmZXIoYmxvYik7DQogICAgICAgICAgDQogICAgICAgICAgcmVhZGVyLm9ubG9hZCA9IChldnQpID0+IHsNCiAgICAgICAgICAgIHRoaXMuY3VzdG9tQmxvYkNvbnRlbnQ9cmVhZGVyLnJlc3VsdDsNCiAgICAgICAgICAgIGxldCBpbnRzID0gbmV3IFVpbnQ4QXJyYXkoZXZ0LnRhcmdldC5yZXN1bHQpOyAvL+imgeS9v+eUqOivu+WPlueahOWGheWuue+8jOaJgOS7peWwhuivu+WPluWGheWuuei9rOWMluaIkFVpbnQ4QXJyYXkNCiAgICAgICAgICAgIGludHMgPSBpbnRzLnNsaWNlKDAsIGJsb2Iuc2l6ZSk7DQogICAgICAgICAgICBsZXQgd29ya0Jvb2sgPSB4bHN4LnJlYWQoaW50cywgeyB0eXBlOiAiYXJyYXkiIH0pOw0KICAgICAgICAgICAgbGV0IHNoZWV0TmFtZXMgPSB3b3JrQm9vay5TaGVldE5hbWVzOw0KICAgICAgICAgICAgbGV0IHNoZWV0TmFtZSA9IHNoZWV0TmFtZXNbMF07DQogICAgICAgICAgICBsZXQgd29ya1NoZWV0ID0gd29ya0Jvb2suU2hlZXRzW3NoZWV0TmFtZV07DQogICAgICAgICAgICAvL+iOt+WPlkV4Y2xl5YaF5a6577yM5bm25bCG56m65YaF5a6555So56m65YC85L+d5a2YDQogICAgICAgICAgICBsZXQgZXhjZWxUYWJsZSA9IHhsc3gudXRpbHMuc2hlZXRfdG9fanNvbih3b3JrU2hlZXQpOw0KICAgICAgICAgICAgLy8g6I635Y+WRXhjZWzlpLTpg6gNCiAgICAgICAgICAgIGxldCB0YWJsZVRoZWFkID0gQXJyYXkuZnJvbShPYmplY3Qua2V5cyhleGNlbFRhYmxlWzBdKSkubWFwKA0KICAgICAgICAgICAgICAoaXRlbSkgPT4gew0KICAgICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICk7DQogICAgICAgICAgICB0aGlzLmV4Y2VsRGF0YSA9IGV4Y2VsVGFibGU7DQogICAgICAgICAgICB0aGlzLmV4Y2VsdGl0bGU9dGFibGVUaGVhZA0KICAgICAgICAgICAgdGhpcy5leGNlbEh0bWw9IGV4Y2VsVGFibGUNCiAgICAgICAgICAgIHRoaXMuc2VhcmNob3BlbiA9IHRydWU7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGVsc2UNCiAgICAgIHsNCiAgICAgICAgdGhpcy5kb3dubG9hZFhsc3goDQogICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFRlbXBsYXRlU3BlY2lhbCIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi5xdWVyeUltcG9ydCwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgIikiICsNCiAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICApLnRoZW4oKGJsb2IpID0+IHsNCiAgICAgICAgbGV0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7DQogICAgICAgIHJlYWRlci5yZWFkQXNBcnJheUJ1ZmZlcihibG9iKTsNCg0KICAgICAgICByZWFkZXIub25sb2FkID0gKGV2dCkgPT4gew0KICAgICAgICAgIHRoaXMuY3VzdG9tQmxvYkNvbnRlbnQ9cmVhZGVyLnJlc3VsdDsNCiAgICAgICAgICBsZXQgaW50cyA9IG5ldyBVaW50OEFycmF5KGV2dC50YXJnZXQucmVzdWx0KTsgLy/opoHkvb/nlKjor7vlj5bnmoTlhoXlrrnvvIzmiYDku6XlsIbor7vlj5blhoXlrrnovazljJbmiJBVaW50OEFycmF5DQogICAgICAgICAgaW50cyA9IGludHMuc2xpY2UoMCwgYmxvYi5zaXplKTsNCiAgICAgICAgICBsZXQgd29ya0Jvb2sgPSB4bHN4LnJlYWQoaW50cywgeyB0eXBlOiAiYXJyYXkiIH0pOw0KICAgICAgICAgIGxldCBzaGVldE5hbWVzID0gd29ya0Jvb2suU2hlZXROYW1lczsNCiAgICAgICAgICBsZXQgc2hlZXROYW1lID0gc2hlZXROYW1lc1swXTsNCiAgICAgICAgICBsZXQgd29ya1NoZWV0ID0gd29ya0Jvb2suU2hlZXRzW3NoZWV0TmFtZV07DQogICAgICAgICAgLy/ojrflj5ZFeGNsZeWGheWuue+8jOW5tuWwhuepuuWGheWuueeUqOepuuWAvOS/neWtmA0KICAgICAgICAgIGxldCBleGNlbFRhYmxlID0geGxzeC51dGlscy5zaGVldF90b19qc29uKHdvcmtTaGVldCk7DQogICAgICAgICAgLy8g6I635Y+WRXhjZWzlpLTpg6gNCiAgICAgICAgICBsZXQgdGFibGVUaGVhZCA9IEFycmF5LmZyb20oT2JqZWN0LmtleXMoZXhjZWxUYWJsZVswXSkpLm1hcCgNCiAgICAgICAgICAgIChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICB9DQogICAgICAgICAgKTsNCiAgICAgICAgICB0aGlzLmV4Y2VsRGF0YSA9IGV4Y2VsVGFibGU7DQogICAgICAgICAgdGhpcy5leGNlbHRpdGxlPXRhYmxlVGhlYWQNCiAgICAgICAgICB0aGlzLmV4Y2VsSHRtbD0gZXhjZWxUYWJsZQ0KICAgICAgICAgIHRoaXMuc2VhcmNob3BlbiA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVQcmV2aWV3MSgpIHsNCiAgICAgIC8vIGxldCBxdWVyeUltcG9ydD17fQ0KICAgICAgLy8gcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICAvLyBxdWVyeUltcG9ydC5mY0RhdGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZQ0KICAgICAgLy8gcXVlcnlJbXBvcnQudHlwZT0iMSINCiAgICAgICAgaWYgKA0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnN0YXJ0RGF0ZSA9PSBudWxsIHx8DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuc3RhcnREYXRlID09ICIifHwNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5lbmREYXRlID09IG51bGx8fA0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LmVuZERhdGUgPT0gIiINCiAgICAgICkgew0KICAgICAgICB0aGlzLiRub3RpZnkuZXJyb3Ioew0KICAgICAgICAgIHRpdGxlOiAi6ZSZ6K+vIiwNCiAgICAgICAgICBtZXNzYWdlOiAi5a+85Ye65YmN6K+35YWI6L6T5YWl5byA5aeL57uT5p2f5pe26Ze0IiwNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMucXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnR5cGU9IjEiDQogICAgICB0aGlzLmRvd25sb2FkWGxzeCgNCiAgICAgICAgIi93ZWIvVFlqeS9hbnN3ZXIvZXhwb3J0VGVtcGxhdGVOb21yYWwiLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5xdWVyeUltcG9ydCwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgIikiICsNCiAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICApLnRoZW4oKGJsb2IpID0+IHsNCiAgICAgICAgbGV0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7DQogICAgICAgIHJlYWRlci5yZWFkQXNBcnJheUJ1ZmZlcihibG9iKTsNCiAgICAgICAgDQogICAgICAgIHJlYWRlci5vbmxvYWQgPSAoZXZ0KSA9PiB7DQogICAgICAgICAgdGhpcy5jdXN0b21CbG9iQ29udGVudD1yZWFkZXIucmVzdWx0Ow0KICAgICAgICAgIGxldCBpbnRzID0gbmV3IFVpbnQ4QXJyYXkoZXZ0LnRhcmdldC5yZXN1bHQpOyAvL+imgeS9v+eUqOivu+WPlueahOWGheWuue+8jOaJgOS7peWwhuivu+WPluWGheWuuei9rOWMluaIkFVpbnQ4QXJyYXkNCiAgICAgICAgICBpbnRzID0gaW50cy5zbGljZSgwLCBibG9iLnNpemUpOw0KICAgICAgICAgIGxldCB3b3JrQm9vayA9IHhsc3gucmVhZChpbnRzLCB7IHR5cGU6ICJhcnJheSIgfSk7DQogICAgICAgICAgbGV0IHNoZWV0TmFtZXMgPSB3b3JrQm9vay5TaGVldE5hbWVzOw0KICAgICAgICAgIGxldCBzaGVldE5hbWUgPSBzaGVldE5hbWVzWzBdOw0KICAgICAgICAgIGxldCB3b3JrU2hlZXQgPSB3b3JrQm9vay5TaGVldHNbc2hlZXROYW1lXTsNCiAgICAgICAgICAvL+iOt+WPlkV4Y2xl5YaF5a6577yM5bm25bCG56m65YaF5a6555So56m65YC85L+d5a2YDQogICAgICAgICAgbGV0IGV4Y2VsVGFibGUgPSB4bHN4LnV0aWxzLnNoZWV0X3RvX2pzb24od29ya1NoZWV0KTsNCiAgICAgICAgICAvLyDojrflj5ZFeGNlbOWktOmDqA0KICAgICAgICAgIGxldCB0YWJsZVRoZWFkID0gQXJyYXkuZnJvbShPYmplY3Qua2V5cyhleGNlbFRhYmxlWzBdKSkubWFwKA0KICAgICAgICAgICAgKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICApOw0KICAgICAgICAgIHRoaXMuZXhjZWxEYXRhID0gZXhjZWxUYWJsZTsNCiAgICAgICAgICB0aGlzLmV4Y2VsdGl0bGU9dGFibGVUaGVhZA0KICAgICAgICAgIHRoaXMuZXhjZWxIdG1sPSBleGNlbFRhYmxlDQogICAgICAgICAgdGhpcy5zZWFyY2hvcGVuID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGhhbmRsZVVwbG9hZCh7IGZpbGUgfSkgew0KICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsNCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgiZmlsZSIsIGZpbGUpOw0KICAgICAgcmV0dXJuIGF4aW9zDQogICAgICAgIC5wb3N0KA0KICAgICAgICAgIHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2NvbW1vbi91cGxvYWRNaW5pb0RhdGFSZXBvcnQiLA0KICAgICAgICAgIGZvcm1EYXRhDQogICAgICAgICkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAuLi5yZXMuZGF0YSwNCiAgICAgICAgICB9Ow0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBoYW5kbGVTdWJtaXQoKSB7DQogICAgICAvLyDpppblhYjlr7kgYW5zd2VyTGlzdCDov5vooYzlpITnkIbvvJrlkIjlubbjgIHov4fmu6TlkozovazmjaINCiAgICAgIGxldCBwcm9jZXNzZWRMaXN0cyA9IHRoaXMuYW5zd2VyTGlzdA0KICAgICAgICAucmVkdWNlKChhY2MsIGN1cnJlbnQpID0+IHsNCiAgICAgICAgICByZXR1cm4gYWNjLmNvbmNhdChjdXJyZW50Lmxpc3QpOw0KICAgICAgICB9LCBbXSkNCiAgICAgICAgLmZpbHRlcigoeCkgPT4gew0KICAgICAgICAgIC8vIOi/h+a7pOadoeS7tg0KICAgICAgICAgIHJldHVybiAoDQogICAgICAgICAgICB4LmZvcm1WYWx1ZSAhPSBudWxsICYmDQogICAgICAgICAgICB4LmZvcm1WYWx1ZSAhPSAiIiAmJg0KICAgICAgICAgICAgKCghWyIwIiwgIjEiXS5pbmNsdWRlcyh4LnN0YXR1cykpJiYNCiAgICAgICAgICAgICgNCiAgICAgICAgICAgICAgKFsiMiIsICIzIl0uaW5jbHVkZXMoeC5zdGF0dXMpICYmIHRoaXMuZGF0ZXNhdmVbeC5mb3JtSWRdIT14LmZvcm1WYWx1ZSkpDQogICAgICAgICAgICAgIHx8KFsiNCJdLmluY2x1ZGVzKHguc3RhdHVzKSkNCiAgICAgICAgICAgICkNCiAgICAgICAgICApOw0KICAgICAgICB9KTsNCg0KICAgICAgLy8g5a+556ym5ZCI5p2h5Lu255qE5YWD57Sg6L+b6KGMIGZvcm1WYWx1ZSDnmoTovazmjaINCiAgICAgIHByb2Nlc3NlZExpc3RzLmZvckVhY2goKHgpID0+IHsNCiAgICAgICAgaWYgKFsiMCIsICIxIl0uaW5jbHVkZXMoeC5mb3JtVHlwZSkpIHsNCiAgICAgICAgICB4LmZvcm1WYWx1ZSA9IHBhcnNlRmxvYXQoeC5mb3JtVmFsdWUpOw0KICAgICAgICB9DQogICAgICAgIHguZmNEYXRlID0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGU7DQogICAgICB9KTsNCg0KICAgICAgLy8g5pyA5ZCO6L+b6KGM5rex5ou36LSdDQogICAgICBsZXQgYWxsTGlzdHMgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHByb2Nlc3NlZExpc3RzKSk7DQoNCiAgICAgIGNvbnNvbGUubG9nKCJhbGxMaXN0czoiLCBhbGxMaXN0cyk7DQogICAgICBuZXdBZGQoYWxsTGlzdHMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv53lrZjmiJDlip8iKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgDQogICAgaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzKCkgew0KICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlOw0KICAgIH0sDQogICAgaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UpIHsNCiAgICAgIGNvbnNvbGUubG9nKHJlc3BvbnNlKQ0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS4iuS8oOaIkOWKnyIpOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy5pbXBvcnRPcGVuID0gZmFsc2U7DQogICAgICAgIHRoaXMuU3BlY2lhbEltcG9ydE9wZW4gPSBmYWxzZTsNCiAgICAgIH0NCiAgICAgIGVsc2Ugew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5LiK5Lyg5aSx6LSlIikNCiAgICAgIH0NCiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7DQogICAgfSwNCiAgICAvLyDmqKHmnb/kuIvovb0NCiAgICBkb3dubG9hZFRlbXBsYXRlKCl7DQogICAgDQogICAgICBpZiAoDQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuc3RhcnREYXRlID09IG51bGwgfHwNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5zdGFydERhdGUgPT0gIiJ8fA0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LmVuZERhdGUgPT0gbnVsbHx8DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuZW5kRGF0ZSA9PSAiIg0KICAgICAgKSB7DQogICAgICAgIHRoaXMuJG5vdGlmeS5lcnJvcih7DQogICAgICAgICAgdGl0bGU6ICLplJnor68iLA0KICAgICAgICAgIG1lc3NhZ2U6ICLlr7zlh7rliY3or7flhYjovpPlhaXlvIDlp4vnu5PmnZ/ml7bpl7QiLA0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5xdWVyeUltcG9ydC5yb290SWQgPSB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQNCiAgICAgIHRoaXMuZG93bmxvYWRGaWxlKA0KICAgICAgICAiL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRUZW1wbGF0ZSIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5SW1wb3J0LA0KICAgICAgICB9LA0KICAgICAgICAiKCIgKw0KICAgICAgICAgIHRoaXMucXVlcnlJbXBvcnQuc3RhcnREYXRlICsNCiAgICAgICAgICAiLSIgKw0KICAgICAgICAgIHRoaXMucXVlcnlJbXBvcnQuZW5kRGF0ZSArDQogICAgICAgICAgIikiICsNCiAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICApOw0KICAgIA0KICAgIH0sDQogICAgZG93bmxvYWRUZW1wbGF0ZVNwZWNpYWwoKXsNCiAgICAgIGlmICh0aGlzLnNwZWNpYWxGY0RhdGUgPT0gbnVsbCApIHsNCiAgICAgICAgdGhpcy5zcGVjaWFsRmNEYXRlPSB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZQ0KICAgICAgfQ0KDQogICAgICAvLyBpZiAodGhpcy5zcGVjaWFsRmNEYXRlID09IG51bGwgKSB7DQogICAgICAvLyAgIHRoaXMuJG5vdGlmeS5lcnJvcih7DQogICAgICAvLyAgICAgdGl0bGU6ICLplJnor68iLA0KICAgICAgLy8gICAgIG1lc3NhZ2U6ICLmnKrpgInmi6nml7bpl7QiLA0KICAgICAgLy8gICB9KTsNCiAgICAgIC8vICAgcmV0dXJuOw0KICAgICAgLy8gfQ0KICAgICAgaWYodGhpcy5kaW1lbnNpb25hbGl0eU5hbWU9PSfnoJTnqbbpmaLnm67moIfmjIfmoIfkuIDop4gnKQ0KICAgICAgew0KICAgICAgICBsZXQgcXVlcnlJbXBvcnQ9e30NCiAgICAgICAgcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICAgIHF1ZXJ5SW1wb3J0LmZjRGF0ZSA9IHRoaXMuc3BlY2lhbEZjRGF0ZQ0KICAgICAgICBxdWVyeUltcG9ydC50eXBlPSIxIg0KICAgICAgICB0aGlzLmRvd25sb2FkRmlsZSgNCiAgICAgICAgICAiL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRXaXRoVGVtcGxhdGUiLA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIC4uLnF1ZXJ5SW1wb3J0LA0KICAgICAgICAgIH0sDQogICAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgICAiKSIgKw0KICAgICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgICApOw0KICAgICAgfQ0KICAgICAgZWxzZQ0KICAgICAgew0KICAgICAgICBsZXQgcXVlcnlJbXBvcnQ9e30NCiAgICAgICAgcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICAgIHF1ZXJ5SW1wb3J0LmZjRGF0ZSA9IHRoaXMuc3BlY2lhbEZjRGF0ZQ0KICAgICAgICBxdWVyeUltcG9ydC50eXBlPSIxIg0KICAgICAgICB0aGlzLmRvd25sb2FkRmlsZSgNCiAgICAgICAgICAiL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRUZW1wbGF0ZVNwZWNpYWwiLA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIC4uLnF1ZXJ5SW1wb3J0LA0KICAgICAgICAgIH0sDQogICAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgICAiKSIgKw0KICAgICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgICApOw0KICAgICAgfQ0KDQogICAgfSwNCiAgICAgICAgLy8g5qih5p2/5LiL6L29DQogICAgZG93bmxvYWRDb25uZWN0VGVtcGxhdGUoKXsNCiAgICBpZiAoDQogICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnN0YXJ0RGF0ZSA9PSBudWxsIHx8DQogICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnN0YXJ0RGF0ZSA9PSAiInx8DQogICAgICB0aGlzLnF1ZXJ5SW1wb3J0LmVuZERhdGUgPT0gbnVsbHx8DQogICAgICB0aGlzLnF1ZXJ5SW1wb3J0LmVuZERhdGUgPT0gIiINCiAgICApIHsNCiAgICAgIHRoaXMuJG5vdGlmeS5lcnJvcih7DQogICAgICAgIHRpdGxlOiAi6ZSZ6K+vIiwNCiAgICAgICAgbWVzc2FnZTogIuWvvOWHuuWJjeivt+WFiOi+k+WFpeW8gOWni+e7k+adn+aXtumXtCIsDQogICAgICB9KTsNCiAgICAgIHJldHVybjsNCiAgICB9DQogICAgdGhpcy5xdWVyeUltcG9ydC5yb290SWQgPSB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQNCiAgICB0aGlzLnF1ZXJ5SW1wb3J0LnR5cGU9IjEiDQogICAgdGhpcy5kb3dubG9hZEZpbGUoDQogICAgICAiL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRUZW1wbGF0ZU5vbXJhbCIsDQogICAgICB7DQogICAgICAgIC4uLnRoaXMucXVlcnlJbXBvcnQsDQogICAgICB9LA0KICAgICAgIigiICsNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5zdGFydERhdGUgKw0KICAgICAgICAiLSIgKw0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LmVuZERhdGUgKw0KICAgICAgICAiKSIgKw0KICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgKTsNCiAgIH0sDQogICBjb250YWluc1N1YnN0cmluZyhzdWJzdHJpbmcsIHN0cmluZykgew0KICAgICAgcmV0dXJuIHN0cmluZy5pbmNsdWRlcyhzdWJzdHJpbmcpOw0KICAgfSwNCiAgIGFsb25lTGlzdChzdHJpbmcpIHsNCiAgICAgIGlmKHN0cmluZz09ICfmsJTkvZPnu5PnrpfmnIjmiqUnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICfpq5jngonjgIHovazngonnhaTmsJTmnIjmiqXooagnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICflpKnnhLbmsJTmtojogJfmnIjmiqXooagnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICfokrjmsb3mtojogJfmnIjmiqXooagnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICfnlLXph4/mnIjmiqXooagnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICfnibnmnb/kuovkuJrpg6gyMDI15bm057uP5rWO6LSj5Lu75Yi25aWW572a5rGH5oC7JykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn56CU56m26Zmi55uu5qCH5oyH5qCH5LiA6KeIJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2U7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["answerShow.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgd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file": "answerShow.vue", "sourceRoot": "src/views/dataReport/answer", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n    >\r\n      <el-row :gutter=\"20\" style=\"margin: 20px\">\r\n        <el-form-item label=\"报表名称\" prop=\"dimensionalityId\">\r\n          <el-select\r\n            v-model=\"queryParams.dimensionalityId\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            @change=\"handleQuery\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in rootList\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '1'\"  >\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '0'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-01\"\r\n            type=\"month\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"填报问题\" prop=\"formQuestion\">\r\n          <el-input\r\n            v-model=\"queryParams.formQuestion\"\r\n            placeholder=\"请输入填报问题\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n          <el-form-item>\r\n\r\n\r\n\r\n          </el-form-item>\r\n          <!-- <el-button\r\n            type=\"warning\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"importOpen = true\"\r\n            >数据导入导出</el-button\r\n          > -->\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-button v-if=\"aloneList(dimensionalityName)  || containsSubstring('安全责任工资',dimensionalityName) \"\r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"SpecialImportOpen = true\"\r\n            >单周期报表导出</el-button\r\n          >\r\n\r\n          <el-button v-if=\"aloneList(dimensionalityName) || containsSubstring('安全责任工资',dimensionalityName) \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handlePreview\"\r\n            >数据预览</el-button\r\n          >\r\n          <el-button v-if=\"queryParams.dimensionalityId == 456 \"\r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"connectOpen = true\"\r\n            >格式化报表导出</el-button\r\n          >\r\n          <el-button v-if=\"queryParams.dimensionalityId == 456 \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"preViewOpen = true\"\r\n            >格式化数据预览</el-button\r\n          >\r\n          <!-- <el-button \r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"SpecialImportOpen = true\"\r\n            >报表导出测试</el-button\r\n          > -->\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <!-- <el-col :span=\"1.5\">管理部门：{{deptName}}\r\n        </el-col> -->\r\n        <right-toolbar\r\n          :showSearch.sync=\"showSearch\"\r\n          @queryTable=\"getList\"\r\n        ></right-toolbar>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <vxe-form\r\n      ref=\"formRef\"\r\n      :data=\"formData\"\r\n      @submit=\"handleSubmit\"\r\n      border\r\n      title-background\r\n      vertical-align=\"center\"\r\n      title-width=\"300\"\r\n      title-bold\r\n    >\r\n      <vxe-form-group\r\n        v-for=\"(group, index) in answerList\"\r\n        :key=\"index\"\r\n        span=\"24\"\r\n        :title=\"group.title\"\r\n        title-bold\r\n        vertical\r\n      >\r\n        <vxe-form-item\r\n          v-for=\"(question, qIndex) in group.list\"\r\n          :key=\"qIndex\"\r\n          :title=\"question.formQuestion\"\r\n          :field=\"answerList[index].list[qIndex].formValue\"\r\n          :span=\"question.formType == '3' ? 24 : 12\"\r\n          :item-render=\"{}\"\r\n        >\r\n          <template #default=\"params\">\r\n            <vxe-tag\r\n              v-if=\"question.status == '0'\"\r\n              status=\"primary\"\r\n              content=\"主要颜色\"\r\n              >待审核</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '1'\"\r\n              status=\"warning\"\r\n              content=\"信息颜色\"\r\n              >审核中</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '2'\"\r\n              status=\"success\"\r\n              content=\"信息颜色\"\r\n              >审核完成</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '3'\"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >驳回理由: {{ question.assessment }}</vxe-tag\r\n            >\r\n\r\n            <vxe-input\r\n              v-if=\"question.formType == '0'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"integer\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '1'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"'float'\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '2'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"text\"\r\n            ></vxe-input>\r\n            <vxe-textarea\r\n              v-if=\"question.formType == '3'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              :placeholder=\"question.formQuestion\"\r\n            ></vxe-textarea>\r\n            <vxe-text v-if=\"question.formNote != null\" status=\"warning\"\r\n              >问题指标:{{ question.formNote }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formNote1 != null\" status=\"warning\"\r\n              >问题备注:{{ question.formNote1 }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.maximum != null\" status=\"primary\"\r\n              >最大值:{{ question.maximum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.minimum != null\" status=\"primary\"\r\n              >最小值:{{ question.minimum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formUnit != null\" status=\"primary\"\r\n              >问题单位:{{ question.formUnit }}<br\r\n            /></vxe-text>\r\n            <vxe-tag\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >输入值超出预计范围，请输入原因和改进措施</vxe-tag\r\n            >\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].reason\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请填写原因\"\r\n            ></vxe-textarea>\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].measure\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请输入改进措施\"\r\n            ></vxe-textarea>\r\n          </template>\r\n        </vxe-form-item>\r\n      </vxe-form-group>\r\n\r\n      <!-- <vxe-form-item align=\"center\" span=\"24\" :item-render=\"{}\">\r\n        <template #default>\r\n          <vxe-button\r\n            type=\"submit\"\r\n            status=\"primary\"\r\n            content=\"提交\"\r\n          ></vxe-button>\r\n        </template>\r\n      </vxe-form-item> -->\r\n    </vxe-form>\r\n\r\n    <el-dialog\r\n      title=\"选择导出范围\"\r\n      :visible.sync=\"importOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplate\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <!-- <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"upload.headers\"\r\n            :disabled=\"upload.isUploading\"\r\n            :action=\"upload.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col> -->\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"单周期报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplateSpecial\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <!-- <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"uploadSpecial.headers\"\r\n            :disabled=\"uploadSpecial.isUploading\"\r\n            :action=\"uploadSpecial.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col> -->\r\n      </el-row>\r\n      <!-- <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"SpecialImportOpen = false\">取 消</el-button>\r\n      </div> -->\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"选择导出范围\"\r\n      :visible.sync=\"connectOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadConnectTemplate\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"选择预览时间范围\"\r\n      :visible.sync=\"preViewOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"handlePreview1\"\r\n            >数据预览</el-button\r\n          >\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog  title=\"数据预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport { answerstatuslistAdmin ,formFrequency} from \"@/api/tYjy/form\";\r\nimport answerInput from \"./input\";\r\nimport { newAdd, addAlone } from \"@/api/tYjy/answer\";\r\nimport { getAllRootListForAnswer,getAllRootList } from \"@/api/tYjy/dimensionality\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\nexport default {\r\n  name: \"Answer\",\r\n  components: { answerInput },\r\n  data() {\r\n    return {\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() > Date.now();\r\n        },\r\n      },\r\n      frequencyOptions: [],\r\n\r\n      queryParams: {\r\n        formQuestion: undefined,\r\n        fcDate: undefined,\r\n        dimensionalityId: undefined,\r\n        formQuestion: undefined,\r\n      },\r\n\r\n      formType: null,\r\n      dimensionalityNames: null,\r\n      dimensionalityName: null,\r\n      specialFcDate:null,\r\n      drawerShow: false,\r\n      stickyTop: 0,\r\n      loading: false,\r\n      showSearch: true,\r\n      answerList: [],\r\n      formData: {},\r\n      row: {},\r\n      rootList: [],\r\n      userInfo: {},\r\n      datesave:{},\r\n      pathsave:{},\r\n      deptName:null,\r\n      dateValue: null,\r\n      queryImport: {\r\n        startDate: null,\r\n        endDate: null,\r\n        rootId: null,\r\n      },\r\n      count:1,\r\n      importOpen:false,\r\n      SpecialImportOpen:false,\r\n      connectOpen:false,\r\n      preViewOpen:false,\r\n      // 导入参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importData\",\r\n\r\n      },\r\n\r\n      uploadSpecial: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importDataSpecial\",\r\n\r\n      },\r\n      excelHtml:\"\",\r\n      searchopen:false,\r\n      excelData: [], // 存储 Excel 数据\r\n      exceltitle: [],\r\n      customBlobContent:\"\"\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userInfo = JSON.parse(JSON.stringify(this.$store.state.user));\r\n  },\r\n\r\n  created() {\r\n    const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    const fcDate = this.$route.query && this.$route.query.fcDate;\r\n\r\n    this.dimensionalityName=this.$route.query && this.$route.query.dimensionalityName;\r\n    this.queryParams.dimensionalityId=dimensionalityId\r\n    this.queryParams.fcDate=fcDate\r\n    this.initData();\r\n\r\n\r\n    // if(this.$route.query)\r\n    // {\r\n    //   const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    //   // const dimensionalityName = this.$route.query && this.$route.query.dimensionalityName;\r\n    //   const fcDate = this.$route.query && this.$route.query.fcDate;\r\n    //   this.queryParams.dimensionalityId=dimensionalityId\r\n    //   // this.queryParams.dimensionalityName=dimensionalityName\r\n    //   this.queryParams.fcDate=fcDate\r\n    //   this.initData1();\r\n    // }\r\n    // else\r\n    // {\r\n    //   this.initData();\r\n    // }\r\n  },\r\n  methods: {\r\n    onDateChange() {\r\n      console.log(this.dateValue);\r\n      if (this.dateValue != null && this.dateValue != \"\") {\r\n        this.queryImport.startDate = this.dateValue[0];\r\n        this.queryImport.endDate = this.dateValue[1];\r\n      } else {\r\n        this.queryImport.startDate = \"\";\r\n        this.queryImport.endDate = \"\";\r\n      }\r\n    },\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    changeEvent(params) {\r\n      const $form = this.$refs.formRef;\r\n      if ($form) {\r\n        $form.updateStatus(params);\r\n      }\r\n    },\r\n    disabledDate(time) {\r\n      return time.getTime() < Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天\r\n    },\r\n    inputChange(val, row) {\r\n      row.formValue = val;\r\n    },\r\n    handleScroll() {\r\n      this.isSticky = window.scrollY >= this.stickyTop;\r\n    },\r\n    initData() {\r\n      // getAllRootListForAnswer().then((res) => {\r\n      //   this.rootList = res.data;\r\n      //   console.log(\"angry\",this.queryParams)\r\n        // if(this.queryParams.dimensionalityId==null)\r\n        // {\r\n        //   // this.queryParams.dimensionalityId = this.rootList[0].value;\r\n        //   // this.deptName= this.rootList[0].deptName;\r\n        //   // this.deptCode= this.rootList[0].deptCode;\r\n        // }\r\n        // else\r\n        // {\r\n        //   // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;\r\n        //   for(let i=0;i<this.rootList.length;i++)\r\n        //   {\r\n        //     if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n        //     {\r\n        //       this.queryParams.dimensionalityId = this.rootList[i].value;\r\n        //       this.deptName= this.rootList[i].deptName;\r\n        //       this.deptCode= this.rootList[i].deptCode;\r\n        //     }\r\n        //   }\r\n        // }\r\n      //   this.getList();\r\n      // });\r\n      getAllRootList().then((res) => {\r\n        this.rootList = res.data;\r\n        if(this.queryParams.dimensionalityId==null)\r\n        {\r\n          // this.queryParams.dimensionalityId = this.rootList[0].value;\r\n          // this.deptName= this.rootList[0].deptName;\r\n          // this.deptCode= this.rootList[0].deptCode;\r\n        }\r\n        else\r\n        {\r\n          // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;\r\n          for(let i=0;i<this.rootList.length;i++)\r\n          {\r\n            if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n            {\r\n              this.queryParams.dimensionalityId = this.rootList[i].value;\r\n              this.deptName= this.rootList[i].deptName;\r\n              this.deptCode= this.rootList[i].deptCode;\r\n            }\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n    initData1() {\r\n      getAllRootListForAnswer().then((res) => {\r\n        this.rootList = res.data;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n          {\r\n            this.deptName= this.rootList[0].deptName;\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n\r\n    /** 查询TYjyAnswer列表 */\r\n    getList() {\r\n      formFrequency({dimensionalityId: this.queryParams.dimensionalityId}).then((res) => {\r\n           if(this.count!=res.data)\r\n           {\r\n            this.queryParams.fcDate=undefined\r\n           }\r\n           this.count=res.data\r\n      });\r\n      this.answerList = [];\r\n      answerstatuslistAdmin({\r\n        fcDate: this.queryParams.fcDate,\r\n        dimensionalityId: this.queryParams.dimensionalityId,\r\n        formQuestion: this.queryParams.formQuestion,\r\n      }).then((res) => {\r\n        let answerList = [];\r\n        let list = res.data;\r\n        for(let i=0;i<list.length;i++)\r\n        {\r\n            this.datesave[list[i].formId]=list[i].formValue\r\n            this.pathsave[list[i].dimensionalityName]=list[i].dimensionalityPath\r\n        }\r\n        // 使用 map 提取 dimensionalityName 属性到一个数组\r\n        let dimensionalityPaths = []\r\n        let Pathset=[]\r\n        list.forEach((x) => \r\n          {\r\n            if(!Pathset.includes(x.dimensionalityPath))\r\n            {\r\n              Pathset.push(x.dimensionalityPath)\r\n              dimensionalityPaths.push({ originalName: x.dimensionalityName, sortKey: x.dimensionalityPath})\r\n            }   \r\n          }\r\n        );\r\n        dimensionalityPaths.forEach((title) => {\r\n          let group = {\r\n            title: \"\",\r\n            list: [],\r\n          };\r\n          group.title = title.originalName;\r\n          group.list = list.filter((item) => item.dimensionalityPath === title.sortKey);\r\n          // 假设你有一个数组来存储所有的组\r\n          answerList.push(group); // 将生成的组添加到groups数组中\r\n        });\r\n        this.answerList = answerList;\r\n        console.log(\"angry\",this.answerList);\r\n\r\n\r\n\r\n        // // 使用 map 提取 dimensionalityName 属性到一个数组\r\n        // let dimensionalityNames = list.map((x) => x.dimensionalityName);\r\n\r\n        // // 提取 / 后的前三位字符，并与原字符串配对\r\n        // dimensionalityNames = dimensionalityNames.map((name) => {\r\n        //   // let key = name.includes(\"/\") ? name.split(\"/\")[1].slice(0, 3) : \"\";\r\n        //   let key = this.pathsave[name];\r\n        //   return { originalName: name, sortKey: key };\r\n        // });\r\n\r\n        // // 按照提取出的前三字符排序\r\n        // dimensionalityNames.sort((a, b) => a.sortKey.localeCompare(b.sortKey));\r\n        // // console.log(\"test0\",dimensionalityNames)\r\n        // // 如果需要，可以提取排序后的原始名字\r\n        // dimensionalityNames = dimensionalityNames.map(\r\n        //   (item) => item.originalName\r\n        // );\r\n\r\n        // // 使用 Set 去重\r\n        // let uniqueDimensionalityNames = [...new Set(dimensionalityNames)];\r\n\r\n        // uniqueDimensionalityNames.forEach((title) => {\r\n        //   let group = {\r\n        //     title: \"\",\r\n        //     list: [],\r\n        //   };\r\n        //   group.title = title;\r\n        //   group.list = list.filter((item) => item.dimensionalityName === title);\r\n        //   // 假设你有一个数组来存储所有的组\r\n        //   answerList.push(group); // 将生成的组添加到groups数组中\r\n        // });\r\n        // this.answerList = answerList;\r\n        // console.log(\"angry\",this.answerList);\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleQuery() {\r\n      for(let i=0;i<this.rootList.length;i++)\r\n      {\r\n        if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n        {\r\n          this.deptName= this.rootList[0].deptName;\r\n          this.deptCode= this.rootList[i].deptCode;\r\n          this.dimensionalityName=this.rootList[i].label\r\n        }\r\n      }\r\n      this.getList();\r\n    },\r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.fcDate = undefined;\r\n      this.queryParams.formQuestion = undefined;\r\n      this.queryParams.dimensionalityId = undefined;\r\n      this.getList();\r\n    },\r\n\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"1\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n          this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n\r\n    handlePreview1() {\r\n      // let queryImport={}\r\n      // queryImport.rootId = this.queryParams.dimensionalityId\r\n      // queryImport.fcDate = this.queryParams.fcDate\r\n      // queryImport.type=\"1\"\r\n        if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.queryImport.type=\"1\"\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateNomral\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        \r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleUpload({ file }) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      return axios\r\n        .post(\r\n          process.env.VUE_APP_BASE_API + \"/common/uploadMinioDataReport\",\r\n          formData\r\n        )\r\n        .then((res) => {\r\n          return {\r\n            ...res.data,\r\n          };\r\n        });\r\n    },\r\n    /** 提交按钮 */\r\n    handleSubmit() {\r\n      // 首先对 answerList 进行处理：合并、过滤和转换\r\n      let processedLists = this.answerList\r\n        .reduce((acc, current) => {\r\n          return acc.concat(current.list);\r\n        }, [])\r\n        .filter((x) => {\r\n          // 过滤条件\r\n          return (\r\n            x.formValue != null &&\r\n            x.formValue != \"\" &&\r\n            ((![\"0\", \"1\"].includes(x.status))&&\r\n            (\r\n              ([\"2\", \"3\"].includes(x.status) && this.datesave[x.formId]!=x.formValue))\r\n              ||([\"4\"].includes(x.status))\r\n            )\r\n          );\r\n        });\r\n\r\n      // 对符合条件的元素进行 formValue 的转换\r\n      processedLists.forEach((x) => {\r\n        if ([\"0\", \"1\"].includes(x.formType)) {\r\n          x.formValue = parseFloat(x.formValue);\r\n        }\r\n        x.fcDate = this.queryParams.fcDate;\r\n      });\r\n\r\n      // 最后进行深拷贝\r\n      let allLists = JSON.parse(JSON.stringify(processedLists));\r\n\r\n      console.log(\"allLists:\", allLists);\r\n      newAdd(allLists).then((res) => {\r\n        this.getList();\r\n        this.msgSuccess(\"保存成功\");\r\n      });\r\n    },\r\n    \r\n    handleFileUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n    handleFileSuccess(response) {\r\n      console.log(response)\r\n      if (response.code == 200) {\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n        this.getList();\r\n        this.importOpen = false;\r\n        this.SpecialImportOpen = false;\r\n      }\r\n      else {\r\n        this.$modal.msgError(\"上传失败\")\r\n      }\r\n      this.upload.isUploading = false;\r\n    },\r\n    // 模板下载\r\n    downloadTemplate(){\r\n    \r\n      if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportTemplate\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        \"(\" +\r\n          this.queryImport.startDate +\r\n          \"-\" +\r\n          this.queryImport.endDate +\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    \r\n    },\r\n    downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        let queryImport={}\r\n        queryImport.rootId = this.queryParams.dimensionalityId\r\n        queryImport.fcDate = this.specialFcDate\r\n        queryImport.type=\"1\"\r\n        this.downloadFile(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        );\r\n      }\r\n      else\r\n      {\r\n        let queryImport={}\r\n        queryImport.rootId = this.queryParams.dimensionalityId\r\n        queryImport.fcDate = this.specialFcDate\r\n        queryImport.type=\"1\"\r\n        this.downloadFile(\r\n          \"/web/TYjy/answer/exportTemplateSpecial\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        );\r\n      }\r\n\r\n    },\r\n        // 模板下载\r\n    downloadConnectTemplate(){\r\n    if (\r\n      this.queryImport.startDate == null ||\r\n      this.queryImport.startDate == \"\"||\r\n      this.queryImport.endDate == null||\r\n      this.queryImport.endDate == \"\"\r\n    ) {\r\n      this.$notify.error({\r\n        title: \"错误\",\r\n        message: \"导出前请先输入开始结束时间\",\r\n      });\r\n      return;\r\n    }\r\n    this.queryImport.rootId = this.queryParams.dimensionalityId\r\n    this.queryImport.type=\"1\"\r\n    this.downloadFile(\r\n      \"/web/TYjy/answer/exportTemplateNomral\",\r\n      {\r\n        ...this.queryImport,\r\n      },\r\n      \"(\" +\r\n        this.queryImport.startDate +\r\n        \"-\" +\r\n        this.queryImport.endDate +\r\n        \")\" +\r\n        `数据.xlsx`\r\n    );\r\n   },\r\n   containsSubstring(substring, string) {\r\n      return string.includes(substring);\r\n   },\r\n   aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '特板事业部2025年经济责任制奖罚汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院目标指标一览')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n/* 设置滚动条的样式 */\r\n::-webkit-scrollbar {\r\n  width: 10px;\r\n  /* 竖向滚动条宽度 */\r\n  height: 15px;\r\n  /* 横向滚动条宽度 */\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* 滚动槽 */\r\n::-webkit-scrollbar-track {\r\n  /* 其实直接在  ::-webkit-scrollbar 中设置也能达到同样的视觉效果*/\r\n  /* -webkit-box-shadow: inset 0 0 6px rgba(177, 223, 117, 0.7); */\r\n  background-color: #e4e4e4;\r\n  border-radius: 10px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 5px;\r\n  -webkit-box-shadow: inset 0 0 6px rgba(158, 156, 156, 0.616);\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(139, 138, 138, 0.616);\r\n  -webkit-box-shadow: unset;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:window-inactive {\r\n  /* 容器不被激活时的样式 */\r\n  background: #bdbdbd66;\r\n}\r\n\r\n::-webkit-scrollbar-corner {\r\n  /* 两个滚动条交汇处边角的样式 */\r\n  background-color: #cacaca66;\r\n}\r\n\r\n.sticky {\r\n  padding: 20px;\r\n  position: -webkit-sticky;\r\n  position: sticky;\r\n  top: 0; /* 粘性定位的起始位置 */\r\n  z-index: 100; /* 确保按钮在卡片之上 */\r\n}\r\n.el-tabs--card {\r\n  height: calc(100vh - 110px);\r\n}\r\n.el-tab-pane {\r\n  height: calc(100vh - 110px);\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n  "]}]}