package com.ruoyi.web.controller.leave;

import com.ruoyi.app.leave.domain.LeaveDepartment;
import com.ruoyi.app.leave.service.ILeaveDepartmentService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 出门证部门（厂内单位）Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/web/leave/department")
public class WebLeaveDepartmentController extends BaseController
{
    @Autowired
    private ILeaveDepartmentService leaveDepartmentService;

    /**
     * 查询出门证部门（厂内单位）列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeaveDepartment leaveDepartment)
    {
        startPage();
        List<LeaveDepartment> list = leaveDepartmentService.selectLeaveDepartmentList(leaveDepartment);
        return getDataTable(list);
    }

    /**
     * 导出出门证部门（厂内单位）列表
     */
    @Log(title = "出门证部门（厂内单位）", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveDepartment leaveDepartment)
    {
        List<LeaveDepartment> list = leaveDepartmentService.selectLeaveDepartmentList(leaveDepartment);
        ExcelUtil<LeaveDepartment> util = new ExcelUtil<LeaveDepartment>(LeaveDepartment.class);
        return util.exportExcel(list, "department");
    }

    /**
     * 获取出门证部门（厂内单位）详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveDepartmentService.selectLeaveDepartmentById(id));
    }

    /**
     * 新增出门证部门（厂内单位）
     */
    @Log(title = "出门证部门（厂内单位）", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveDepartment leaveDepartment)
    {
        return toAjax(leaveDepartmentService.insertLeaveDepartment(leaveDepartment, SecurityUtils.getUsername()));
    }

    /**
     * 修改出门证部门（厂内单位）
     */
    @Log(title = "出门证部门（厂内单位）", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveDepartment leaveDepartment)
    {
        return toAjax(leaveDepartmentService.updateLeaveDepartment(leaveDepartment, SecurityUtils.getUsername()));
    }
    /**
     * 作废出门证部门（厂内单位）
     */
    @Log(title = "出门证部门（厂内单位）", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel")
    public AjaxResult cancel(@RequestBody LeaveDepartment leaveDepartment)
    {
        return toAjax(leaveDepartmentService.cancelLeaveDepartment(leaveDepartment,SecurityUtils.getUsername()));
    }
    /**
     * 删除出门证部门（厂内单位）
     */
    @Log(title = "出门证部门（厂内单位）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveDepartmentService.deleteLeaveDepartmentByIds(ids));
    }
}
