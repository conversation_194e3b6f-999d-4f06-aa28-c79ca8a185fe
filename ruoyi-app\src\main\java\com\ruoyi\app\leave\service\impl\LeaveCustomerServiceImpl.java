package com.ruoyi.app.leave.service.impl;

import java.util.Collection;
import java.util.List;

import com.ruoyi.app.leave.enums.LeaveCustomerEnum;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeaveCustomerMapper;
import com.ruoyi.app.leave.domain.LeaveCustomer;
import com.ruoyi.app.leave.service.ILeaveCustomerService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 出门证厂外客户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeaveCustomerServiceImpl implements ILeaveCustomerService 
{
    @Autowired
    private LeaveCustomerMapper leaveCustomerMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询出门证厂外客户
     * 
     * @param id 出门证厂外客户ID
     * @return 出门证厂外客户
     */
    @Override
    public LeaveCustomer selectLeaveCustomerById(Long id)
    {
        return leaveCustomerMapper.selectLeaveCustomerById(id);
    }

    /**
     * 查询出门证厂外客户列表
     * 
     * @param leaveCustomer 出门证厂外客户
     * @return 出门证厂外客户
     */
    @Override
    public List<LeaveCustomer> selectLeaveCustomerList(LeaveCustomer leaveCustomer)
    {
        return leaveCustomerMapper.selectLeaveCustomerList(leaveCustomer);
    }

    /**
     * 新增出门证厂外客户
     * 
     * @param leaveCustomer 出门证厂外客户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertLeaveCustomer(LeaveCustomer leaveCustomer,String workNo)
    {
        SysUser sysUser = sysUserMapper.selectUserByUserName(workNo);
        leaveCustomer.setCreateBy(workNo);
        leaveCustomer.setCreateTime(DateUtils.getNowDate());
        leaveCustomer.setValidFlag(LeaveCustomerEnum.NORMAL.getCode());
        leaveCustomer.setUpdateBy(workNo);
        leaveCustomer.setUpdateTime(DateUtils.getNowDate());
        return leaveCustomerMapper.insertLeaveCustomer(leaveCustomer);
    }


    /**
     * 修改出门证厂外客户
     * 
     * @param leaveCustomer 出门证厂外客户
     * @return 结果
     */
    @Override
    public int updateLeaveCustomer(LeaveCustomer leaveCustomer)
    {
        leaveCustomer.setUpdateTime(DateUtils.getNowDate());
        return leaveCustomerMapper.updateLeaveCustomer(leaveCustomer);
    }

    /**
     * 批量删除出门证厂外客户
     * 
     * @param ids 需要删除的出门证厂外客户ID
     * @return 结果
     */
    @Override
    public int deleteLeaveCustomerByIds(Long[] ids)
    {
        return leaveCustomerMapper.deleteLeaveCustomerByIds(ids);
    }

    /**
     * 删除出门证厂外客户信息
     * 
     * @param id 出门证厂外客户ID
     * @return 结果
     */
    @Override
    public int deleteLeaveCustomerById(Long id)
    {
        return leaveCustomerMapper.deleteLeaveCustomerById(id);
    }

    /**
     * 废弃客户
     * @param id
     * @return
     */
    @Override
    public int invalidLeaveCustomerByIds(Long id,String workNo) {
        LeaveCustomer customer = new LeaveCustomer();
        customer.setId(id);
        customer.setValidFlag(LeaveCustomerEnum.CANCEL.getCode());
        customer.setUpdateTime(DateUtils.getNowDate());
        customer.setUpdateBy(workNo);
        return leaveCustomerMapper.updateLeaveCustomerValidFlag(customer);
    }
}
