package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.StoreinWwjgMeasure;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 外委加工入库Mapper接口
 * 
 * <AUTHOR>
 */
public interface StoreinWwjgMapper 
{
    /**
     * 查询外委加工入库
     * 
     * @param id 外委加工入库主键
     * @return 外委加工入库
     */
    @DataSource(DataSourceType.XCC1)
    public StoreinWwjgMeasure selectStoreinWwjgById(Long id);

    /**
     * 查询外委加工入库列表
     * 
     * @param storeinWwjgMeasure 外委加工入库
     * @return 外委加工入库集合
     */
    @DataSource(DataSourceType.XCC1)
    public List<StoreinWwjgMeasure> selectStoreinWwjgList(StoreinWwjgMeasure storeinWwjgMeasure);

    /**
     * 新增外委加工入库
     * 
     * @param storeinWwjgMeasure 外委加工入库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int insertStoreinWwjg(StoreinWwjgMeasure storeinWwjgMeasure);

    /**
     * 修改外委加工入库
     * 
     * @param storeinWwjgMeasure 外委加工入库
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int updateStoreinWwjg(StoreinWwjgMeasure storeinWwjgMeasure);

    /**
     * 删除外委加工入库
     * 
     * @param id 外委加工入库主键
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreinWwjgById(Long id);

    /**
     * 批量删除外委加工入库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreinWwjgByIds(Long[] ids);

    /**
     * 根据匹配ID删除外委加工入库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteStoreinWwjgByMatchid(String matchid);
} 