package com.ruoyi.app.leave.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeaveTaskMaterialMapper;
import com.ruoyi.app.leave.domain.LeaveTaskMaterial;
import com.ruoyi.app.leave.service.ILeaveTaskMaterialService;

/**
 * 出门证任务物资Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeaveTaskMaterialServiceImpl implements ILeaveTaskMaterialService 
{
    @Autowired
    private LeaveTaskMaterialMapper leaveTaskMaterialMapper;

    /**
     * 查询出门证任务物资
     * 
     * @param id 出门证任务物资ID
     * @return 出门证任务物资
     */
    @Override
    public LeaveTaskMaterial selectLeaveTaskMaterialById(Long id)
    {
        return leaveTaskMaterialMapper.selectLeaveTaskMaterialById(id);
    }

    /**
     * 查询出门证任务物资列表
     * 
     * @param leaveTaskMaterial 出门证任务物资
     * @return 出门证任务物资
     */
    @Override
    public List<LeaveTaskMaterial> selectLeaveTaskMaterialList(LeaveTaskMaterial leaveTaskMaterial)
    {
        return leaveTaskMaterialMapper.selectLeaveTaskMaterialList(leaveTaskMaterial);
    }

    /**
     * 新增出门证任务物资
     * 
     * @param leaveTaskMaterial 出门证任务物资
     * @return 结果
     */
    @Override
    public int insertLeaveTaskMaterial(LeaveTaskMaterial leaveTaskMaterial)
    {
        leaveTaskMaterial.setCreateTime(DateUtils.getNowDate());
        leaveTaskMaterial.setCreateBy(SecurityUtils.getUsername());
        return leaveTaskMaterialMapper.insertLeaveTaskMaterial(leaveTaskMaterial);
    }

    /**
     * 修改出门证任务物资
     * 
     * @param leaveTaskMaterial 出门证任务物资
     * @return 结果
     */
    @Override
    public int updateLeaveTaskMaterial(LeaveTaskMaterial leaveTaskMaterial)
    {
        leaveTaskMaterial.setUpdateTime(DateUtils.getNowDate());
        return leaveTaskMaterialMapper.updateLeaveTaskMaterial(leaveTaskMaterial);
    }

    /**
     * 批量删除出门证任务物资
     * 
     * @param ids 需要删除的出门证任务物资ID
     * @return 结果
     */
    @Override
    public int deleteLeaveTaskMaterialByIds(Long[] ids)
    {
        return leaveTaskMaterialMapper.deleteLeaveTaskMaterialByIds(ids);
    }

    /**
     * 删除出门证任务物资信息
     * 
     * @param id 出门证任务物资ID
     * @return 结果
     */
    @Override
    public int deleteLeaveTaskMaterialById(Long id)
    {
        return leaveTaskMaterialMapper.deleteLeaveTaskMaterialById(id);
    }
}
