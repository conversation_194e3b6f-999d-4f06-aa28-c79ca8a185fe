<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeaveCustomerMapper">
    
    <resultMap type="LeaveCustomer" id="LeaveCustomerResult">
        <result property="id"    column="id"    />
        <result property="validFlag"    column="valid_flag"    />
        <result property="customerName"    column="customer_name"    />
        <result property="queryWord"    column="query_word"    />
        <result property="erpCode"    column="erp_code"    />
        <result property="tele"    column="tele"    />
        <result property="address"    column="address"    />
        <result property="memo"    column="memo"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectLeaveCustomerVo">
        select id, valid_flag, customer_name, query_word, erp_code, tele, address, memo, create_time, create_by, update_time, update_by from leave_customer
    </sql>

    <select id="selectLeaveCustomerList" parameterType="LeaveCustomer" resultMap="LeaveCustomerResult">
        <include refid="selectLeaveCustomerVo"/>
        <where>
            <choose>
                <when test="validFlag != null">
                    valid_flag = #{validFlag}
                </when>
                <otherwise>
                    valid_flag = 1
                </otherwise>
            </choose>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="queryWord != null  and queryWord != ''"> and query_word = #{queryWord}</if>
            <if test="erpCode != null  and erpCode != ''"> and erp_code = #{erpCode}</if>
            <if test="tele != null  and tele != ''"> and tele = #{tele}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectLeaveCustomerById" parameterType="Long" resultMap="LeaveCustomerResult">
        <include refid="selectLeaveCustomerVo"/>
        where id = #{id}
        order by create_time desc
    </select>
        
    <insert id="insertLeaveCustomer" parameterType="LeaveCustomer" useGeneratedKeys="true" keyProperty="id">
        insert into leave_customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="validFlag != null">valid_flag,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="queryWord != null">query_word,</if>
            <if test="erpCode != null">erp_code,</if>
            <if test="tele != null">tele,</if>
            <if test="address != null">address,</if>
            <if test="memo != null">memo,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="validFlag != null">#{validFlag},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="queryWord != null">#{queryWord},</if>
            <if test="erpCode != null">#{erpCode},</if>
            <if test="tele != null">#{tele},</if>
            <if test="address != null">#{address},</if>
            <if test="memo != null">#{memo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateLeaveCustomer" parameterType="LeaveCustomer">
        update leave_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="queryWord != null">query_word = #{queryWord},</if>
            <if test="erpCode != null">erp_code = #{erpCode},</if>
            <if test="tele != null">tele = #{tele},</if>
            <if test="address != null">address = #{address},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveCustomerById" parameterType="Long">
        delete from leave_customer where id = #{id}
    </delete>

    <delete id="deleteLeaveCustomerByIds" parameterType="String">
        delete from leave_customer where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateLeaveCustomerValidFlag"  >
        UPDATE leave_customer
        <trim prefix="SET" suffixOverrides=",">
        <if test="validFlag != null">valid_flag = #{validFlag},</if>
        </trim>
        WHERE id = #{id}
    </update>
    
</mapper>