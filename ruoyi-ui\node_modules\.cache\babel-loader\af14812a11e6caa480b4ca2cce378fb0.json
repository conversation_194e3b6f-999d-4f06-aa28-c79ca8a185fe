{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue", "mtime": 1756170476877}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuam9pbi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLm1hdGNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcudHJpbS5qcyIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogIlB1bmlzaG1lbnRCYXNpc0RpYWxvZyIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgLy8g6YCJ5Lit55qE5L6d5o2u57G75Z6LCiAgICAgIHNlbGVjdGVkQmFzaXNUeXBlOiAnJywKICAgICAgLy8g6LSo6YeP5byC6K6u5Y2V5Y+3CiAgICAgIHF1YWxpdHlOdW1iZXI6ICcnLAogICAgICAvLyDliLbluqblkI3np7AKICAgICAgc3lzdGVtTmFtZTogJycsCiAgICAgIC8vIOaKpeWRiuWQjeensAogICAgICByZXBvcnROYW1lOiAnJywKICAgICAgLy8g5L6d5o2u5YaF5a65CiAgICAgIGJhc2lzQ29udGVudDogJycsCiAgICAgIC8vIOmihOiniOaWh+acrAogICAgICBwcmV2aWV3VGV4dDogJycKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5pi+56S65by556qXICovc2hvdzogZnVuY3Rpb24gc2hvdygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdmFyIGN1cnJlbnRWYWx1ZSA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogJyc7CiAgICAgIHRoaXMudmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMucGFyc2VDdXJyZW50VmFsdWUoY3VycmVudFZhbHVlKTsKICAgICAgdGhpcy51cGRhdGVQcmV2aWV3KCk7CiAgICAgIC8vIOehruS/neW8ueeql+WujOWFqOaJk+W8gOWQjuWGjei/m+ihjOWFtuS7luaTjeS9nAogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+W8ueeql+W3suaYvuekuu+8jOW9k+WJjeaVsOaNru+8micsIHsKICAgICAgICAgIHNlbGVjdGVkQmFzaXNUeXBlOiBfdGhpcy5zZWxlY3RlZEJhc2lzVHlwZSwKICAgICAgICAgIHF1YWxpdHlOdW1iZXI6IF90aGlzLnF1YWxpdHlOdW1iZXIsCiAgICAgICAgICBzeXN0ZW1OYW1lOiBfdGhpcy5zeXN0ZW1OYW1lLAogICAgICAgICAgcmVwb3J0TmFtZTogX3RoaXMucmVwb3J0TmFtZSwKICAgICAgICAgIGJhc2lzQ29udGVudDogX3RoaXMuYmFzaXNDb250ZW50CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDpmpDol4/lvLnnqpcgKi9oaWRlOiBmdW5jdGlvbiBoaWRlKCkgewogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvKiog5YWz6Zet5by556qXICovaGFuZGxlQ2xvc2U6IGZ1bmN0aW9uIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8qKiDph43nva7mlbDmja4gKi9yZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRCYXNpc1R5cGUgPSAnJzsKICAgICAgdGhpcy5xdWFsaXR5TnVtYmVyID0gJyc7CiAgICAgIHRoaXMuc3lzdGVtTmFtZSA9ICcnOwogICAgICB0aGlzLnJlcG9ydE5hbWUgPSAnJzsKICAgICAgdGhpcy5iYXNpc0NvbnRlbnQgPSAnJzsKICAgICAgdGhpcy5wcmV2aWV3VGV4dCA9ICcnOwogICAgfSwKICAgIC8qKiDop6PmnpDlvZPliY3lgLwgKi9wYXJzZUN1cnJlbnRWYWx1ZTogZnVuY3Rpb24gcGFyc2VDdXJyZW50VmFsdWUodmFsdWUpIHsKICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOWwneivleino+aekOeOsOacieeahOS+neaNruWGheWuuQogICAgICBpZiAodmFsdWUuaW5jbHVkZXMoJ+i0qOmHj+W8guiuruWNleWPt++8micpKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9ICdxdWFsaXR5JzsKICAgICAgICB2YXIgbWF0Y2ggPSB2YWx1ZS5tYXRjaCgv6LSo6YeP5byC6K6u5Y2V5Y+377yaKFte77ybXG5dKikvKTsKICAgICAgICBpZiAobWF0Y2gpIHsKICAgICAgICAgIHRoaXMucXVhbGl0eU51bWJlciA9IG1hdGNoWzFdLnRyaW0oKTsKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAodmFsdWUuaW5jbHVkZXMoJ+WItuW6puWQjeensO+8micpKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEJhc2lzVHlwZSA9ICdzeXN0ZW0nOwogICAgICAgIHZhciBfbWF0Y2ggPSB2YWx1ZS5tYXRjaCgv5Yi25bqm5ZCN56ew77yaKFte77ybXG5dKikvKTsKICAgICAgICBpZiAoX21hdGNoKSB7CiAgICAgICAgICB0aGlzLnN5c3RlbU5hbWUgPSBfbWF0Y2hbMV0udHJpbSgpOwogICAgICAgIH0KICAgICAgfSBlbHNlIGlmICh2YWx1ZS5pbmNsdWRlcygn5oql5ZGK77yaJykpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlID0gJ3JlcG9ydCc7CiAgICAgICAgdmFyIF9tYXRjaDIgPSB2YWx1ZS5tYXRjaCgv5oql5ZGK77yaKFte77ybXG5dKikvKTsKICAgICAgICBpZiAoX21hdGNoMikgewogICAgICAgICAgdGhpcy5yZXBvcnROYW1lID0gX21hdGNoMlsxXS50cmltKCk7CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDop6PmnpDkvp3mja7lhoXlrrkKICAgICAgdmFyIGNvbnRlbnRNYXRjaCA9IHZhbHVlLm1hdGNoKC/kvp3mja7lhoXlrrnvvJooW15dKikvKTsKICAgICAgaWYgKGNvbnRlbnRNYXRjaCkgewogICAgICAgIHRoaXMuYmFzaXNDb250ZW50ID0gY29udGVudE1hdGNoWzFdLnRyaW0oKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlpoLmnpzmsqHmnInmib7liLDkvp3mja7lhoXlrrnmoIfor4bvvIzlsIbmlbTkuKrlhoXlrrnkvZzkuLrkvp3mja7lhoXlrrkKICAgICAgICB0aGlzLmJhc2lzQ29udGVudCA9IHZhbHVlOwogICAgICB9CiAgICB9LAogICAgLyoqIOS+nea<PERSON>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"}, {"version": 3, "names": ["name", "data", "visible", "selectedBasisType", "qualityNumber", "systemName", "reportName", "basisContent", "previewText", "methods", "show", "_this", "currentValue", "arguments", "length", "undefined", "parseCurrentValue", "updatePreview", "$nextTick", "console", "log", "hide", "handleClose", "reset", "value", "includes", "match", "trim", "contentMatch", "handleBasisTypeChange", "updateBasisText", "checkQuality", "checkSystem", "checkReport", "parts", "push", "concat", "join", "handleConfirm", "$message", "warning", "$emit"], "sources": ["src/views/suppPunishment/punishmentBasis-module.vue"], "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"处罚依据选择\"\r\n    :visible.sync=\"visible\"\r\n    width=\"500px\"\r\n    top=\"5vh\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n    :close-on-click-modal=\"false\"\r\n    custom-class=\"basis-dialog\"\r\n  >\r\n    <div class=\"basis-dialog-content\">\r\n      <!-- 处罚依据选项 -->\r\n      <div class=\"basis-options\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>选择依据类型：</h4>\r\n        <el-radio-group v-model=\"selectedBasisType\" @change=\"handleBasisTypeChange\">\r\n          <!-- 质量异议单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"quality\" @change=\"handleBasisTypeChange\">质量异议单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"qualityNumber\"\r\n                  placeholder=\"请输入质量异议单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkQuality\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 制度名称 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"system\" @change=\"handleBasisTypeChange\">制度名称</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"systemName\"\r\n                  placeholder=\"请输入制度名称\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkSystem\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 报告 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"report\" @change=\"handleBasisTypeChange\">报告</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"reportName\"\r\n                  placeholder=\"请输入文件报批单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkReport\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-radio-group>\r\n      </div>\r\n\r\n      <!-- 依据内容 -->\r\n      <div class=\"basis-content\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>依据内容：</h4>\r\n        <div class=\"content-wrapper\">\r\n          <el-input\r\n            v-model=\"basisContent\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入依据内容\"\r\n            @input=\"updateBasisText\"\r\n            class=\"content-textarea\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 预览区域 -->\r\n      <div class=\"preview-area\">\r\n        <h4 class=\"section-title\"> 预览结果：</h4>\r\n        <div class=\"preview-wrapper\">\r\n          <el-input\r\n            v-model=\"previewText\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            readonly\r\n            placeholder=\"选择处罚依据后将在此显示预览\"\r\n            class=\"preview-textarea\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <div class=\"footer-buttons\">\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确 定</el-button>\r\n        <el-button @click=\"handleClose\">取 消</el-button>\r\n      </div>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PunishmentBasisDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 选中的依据类型\r\n      selectedBasisType: '',\r\n      // 质量异议单号\r\n      qualityNumber: '',\r\n      // 制度名称\r\n      systemName: '',\r\n      // 报告名称\r\n      reportName: '',\r\n      // 依据内容\r\n      basisContent: '',\r\n      // 预览文本\r\n      previewText: ''\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show(currentValue = '') {\r\n      this.visible = true;\r\n      this.parseCurrentValue(currentValue);\r\n      this.updatePreview();\r\n      // 确保弹窗完全打开后再进行其他操作\r\n      this.$nextTick(() => {\r\n        console.log('弹窗已显示，当前数据：', {\r\n          selectedBasisType: this.selectedBasisType,\r\n          qualityNumber: this.qualityNumber,\r\n          systemName: this.systemName,\r\n          reportName: this.reportName,\r\n          basisContent: this.basisContent\r\n        });\r\n      });\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.selectedBasisType = '';\r\n      this.qualityNumber = '';\r\n      this.systemName = '';\r\n      this.reportName = '';\r\n      this.basisContent = '';\r\n      this.previewText = '';\r\n    },\r\n    \r\n    /** 解析当前值 */\r\n    parseCurrentValue(value) {\r\n      if (!value) {\r\n        this.reset();\r\n        return;\r\n      }\r\n      \r\n      // 尝试解析现有的依据内容\r\n      if (value.includes('质量异议单号：')) {\r\n        this.selectedBasisType = 'quality';\r\n        const match = value.match(/质量异议单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.qualityNumber = match[1].trim();\r\n        }\r\n      } else if (value.includes('制度名称：')) {\r\n        this.selectedBasisType = 'system';\r\n        const match = value.match(/制度名称：([^；\\n]*)/);\r\n        if (match) {\r\n          this.systemName = match[1].trim();\r\n        }\r\n      } else if (value.includes('报告：')) {\r\n        this.selectedBasisType = 'report';\r\n        const match = value.match(/报告：([^；\\n]*)/);\r\n        if (match) {\r\n          this.reportName = match[1].trim();\r\n        }\r\n      }\r\n      \r\n      // 解析依据内容\r\n      const contentMatch = value.match(/依据内容：([^]*)/);\r\n      if (contentMatch) {\r\n        this.basisContent = contentMatch[1].trim();\r\n      } else {\r\n        // 如果没有找到依据内容标识，将整个内容作为依据内容\r\n        this.basisContent = value;\r\n      }\r\n    },\r\n    \r\n    /** 依据类型变化 */\r\n    handleBasisTypeChange() {\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 更新依据文本 */\r\n    updateBasisText() {\r\n      this.updatePreview();\r\n    },\r\n\r\n    /** 点击质量异议单号输入框时自动选中 */\r\n    checkQuality() {\r\n      if (this.selectedBasisType !== 'quality') {\r\n        this.selectedBasisType = 'quality';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击制度名称输入框时自动选中 */\r\n    checkSystem() {\r\n      if (this.selectedBasisType !== 'system') {\r\n        this.selectedBasisType = 'system';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击报告输入框时自动选中 */\r\n    checkReport() {\r\n      if (this.selectedBasisType !== 'report') {\r\n        this.selectedBasisType = 'report';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n    \r\n    /** 更新预览 */\r\n    updatePreview() {\r\n      const parts = [];\r\n\r\n      // 添加选中的依据类型信息\r\n      if (this.selectedBasisType === 'quality' && this.qualityNumber) {\r\n        parts.push(`质量异议单号：${this.qualityNumber}`);\r\n      } else if (this.selectedBasisType === 'system' && this.systemName) {\r\n        parts.push(`制度名称：${this.systemName}`);\r\n      } else if (this.selectedBasisType === 'report' && this.reportName) {\r\n        parts.push(`报告：${this.reportName}`);\r\n      }\r\n\r\n      // 添加依据内容\r\n      if (this.basisContent) {\r\n        parts.push(`依据内容：${this.basisContent}`);\r\n      }\r\n\r\n      this.previewText = parts.join('；');\r\n\r\n      console.log('预览更新：', {\r\n        selectedBasisType: this.selectedBasisType,\r\n        qualityNumber: this.qualityNumber,\r\n        systemName: this.systemName,\r\n        reportName: this.reportName,\r\n        basisContent: this.basisContent,\r\n        previewText: this.previewText\r\n      });\r\n    },\r\n    \r\n    /** 确认选择 */\r\n    handleConfirm() {\r\n      this.updatePreview();\r\n\r\n      // 验证是否填写了必要信息\r\n      if (!this.selectedBasisType) {\r\n        this.$message.warning('请选择依据类型');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'quality' && !this.qualityNumber) {\r\n        this.$message.warning('请输入质量异议单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'system' && !this.systemName) {\r\n        this.$message.warning('请输入制度名称');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'report' && !this.reportName) {\r\n        this.$message.warning('请输入文件报批单号');\r\n        return;\r\n      }\r\n\r\n      if (!this.basisContent) {\r\n        this.$message.warning('请输入依据内容');\r\n        return;\r\n      }\r\n\r\n      this.$emit('select', this.previewText);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 弹窗内容容器 */\r\n.basis-dialog-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 章节标题样式 */\r\n.section-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 0 0 15px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 顶级标题样式（选择依据类型） */\r\n.basis-options .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 处罚依据选项样式 */\r\n.basis-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-item {\r\n  margin-bottom: 15px;\r\n  padding: 0;\r\n}\r\n\r\n/* 新的行布局 */\r\n.basis-row {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  min-height: 36px;\r\n}\r\n\r\n.radio-wrapper {\r\n  width: 120px;\r\n  flex-shrink: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 36px;\r\n}\r\n\r\n.input-wrapper {\r\n  width: calc(100% - 135px);\r\n  margin-left: 14px;\r\n}\r\n\r\n.aligned-input {\r\n  width: 100%;\r\n}\r\n\r\n.aligned-input ::v-deep .el-input__inner {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 单选框对齐样式 */\r\n.radio-wrapper ::v-deep .el-radio {\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 0;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__input {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 8px;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__inner {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__label {\r\n  font-size: 14px;\r\n  line-height: 36px;\r\n  padding-left: 8px;\r\n  color: #606266;\r\n}\r\n\r\n/* 依据内容区域样式 */\r\n.basis-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-content .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.content-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.content-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.content-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 预览区域样式 */\r\n.preview-area {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.preview-area .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.preview-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.preview-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.preview-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 专门的弹窗样式 */\r\n::v-deep .basis-dialog {\r\n  margin-top: 5vh !important;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__body {\r\n  padding: 25px;\r\n  max-height: 75vh;\r\n  overflow-y: auto;\r\n  background-color: #ffffff;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__header {\r\n  padding: 20px 25px 15px;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__footer {\r\n  padding: 15px 25px 20px;\r\n  text-align: right;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.footer-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n}\r\n\r\n/* 单选框组样式 */\r\n::v-deep .el-radio-group {\r\n  width: 100%;\r\n}\r\n\r\n::v-deep .el-radio {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n::v-deep .el-radio__label {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 必填标识符样式 */\r\n.required-mark {\r\n  color: #F56C6C;\r\n  margin-right: 4px;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAkHA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,iBAAA;MACA;MACAC,aAAA;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA;IACA,WACAC,IAAA,WAAAA,KAAA;MAAA,IAAAC,KAAA;MAAA,IAAAC,YAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,KAAAX,OAAA;MACA,KAAAc,iBAAA,CAAAJ,YAAA;MACA,KAAAK,aAAA;MACA;MACA,KAAAC,SAAA;QACAC,OAAA,CAAAC,GAAA;UACAjB,iBAAA,EAAAQ,KAAA,CAAAR,iBAAA;UACAC,aAAA,EAAAO,KAAA,CAAAP,aAAA;UACAC,UAAA,EAAAM,KAAA,CAAAN,UAAA;UACAC,UAAA,EAAAK,KAAA,CAAAL,UAAA;UACAC,YAAA,EAAAI,KAAA,CAAAJ;QACA;MACA;IACA;IAEA,WACAc,IAAA,WAAAA,KAAA;MACA,KAAAnB,OAAA;IACA;IAEA,WACAoB,WAAA,WAAAA,YAAA;MACA,KAAApB,OAAA;MACA,KAAAqB,KAAA;IACA;IAEA,WACAA,KAAA,WAAAA,MAAA;MACA,KAAApB,iBAAA;MACA,KAAAC,aAAA;MACA,KAAAC,UAAA;MACA,KAAAC,UAAA;MACA,KAAAC,YAAA;MACA,KAAAC,WAAA;IACA;IAEA,YACAQ,iBAAA,WAAAA,kBAAAQ,KAAA;MACA,KAAAA,KAAA;QACA,KAAAD,KAAA;QACA;MACA;;MAEA;MACA,IAAAC,KAAA,CAAAC,QAAA;QACA,KAAAtB,iBAAA;QACA,IAAAuB,KAAA,GAAAF,KAAA,CAAAE,KAAA;QACA,IAAAA,KAAA;UACA,KAAAtB,aAAA,GAAAsB,KAAA,IAAAC,IAAA;QACA;MACA,WAAAH,KAAA,CAAAC,QAAA;QACA,KAAAtB,iBAAA;QACA,IAAAuB,MAAA,GAAAF,KAAA,CAAAE,KAAA;QACA,IAAAA,MAAA;UACA,KAAArB,UAAA,GAAAqB,MAAA,IAAAC,IAAA;QACA;MACA,WAAAH,KAAA,CAAAC,QAAA;QACA,KAAAtB,iBAAA;QACA,IAAAuB,OAAA,GAAAF,KAAA,CAAAE,KAAA;QACA,IAAAA,OAAA;UACA,KAAApB,UAAA,GAAAoB,OAAA,IAAAC,IAAA;QACA;MACA;;MAEA;MACA,IAAAC,YAAA,GAAAJ,KAAA,CAAAE,KAAA;MACA,IAAAE,YAAA;QACA,KAAArB,YAAA,GAAAqB,YAAA,IAAAD,IAAA;MACA;QACA;QACA,KAAApB,YAAA,GAAAiB,KAAA;MACA;IACA;IAEA,aACAK,qBAAA,WAAAA,sBAAA;MACA,KAAAZ,aAAA;IACA;IAEA,aACAa,eAAA,WAAAA,gBAAA;MACA,KAAAb,aAAA;IACA;IAEA,uBACAc,YAAA,WAAAA,aAAA;MACA,SAAA5B,iBAAA;QACA,KAAAA,iBAAA;QACA,KAAAc,aAAA;MACA;IACA;IAEA,qBACAe,WAAA,WAAAA,YAAA;MACA,SAAA7B,iBAAA;QACA,KAAAA,iBAAA;QACA,KAAAc,aAAA;MACA;IACA;IAEA,mBACAgB,WAAA,WAAAA,YAAA;MACA,SAAA9B,iBAAA;QACA,KAAAA,iBAAA;QACA,KAAAc,aAAA;MACA;IACA;IAEA,WACAA,aAAA,WAAAA,cAAA;MACA,IAAAiB,KAAA;;MAEA;MACA,SAAA/B,iBAAA,uBAAAC,aAAA;QACA8B,KAAA,CAAAC,IAAA,8CAAAC,MAAA,MAAAhC,aAAA;MACA,gBAAAD,iBAAA,sBAAAE,UAAA;QACA6B,KAAA,CAAAC,IAAA,kCAAAC,MAAA,MAAA/B,UAAA;MACA,gBAAAF,iBAAA,sBAAAG,UAAA;QACA4B,KAAA,CAAAC,IAAA,sBAAAC,MAAA,MAAA9B,UAAA;MACA;;MAEA;MACA,SAAAC,YAAA;QACA2B,KAAA,CAAAC,IAAA,kCAAAC,MAAA,MAAA7B,YAAA;MACA;MAEA,KAAAC,WAAA,GAAA0B,KAAA,CAAAG,IAAA;MAEAlB,OAAA,CAAAC,GAAA;QACAjB,iBAAA,OAAAA,iBAAA;QACAC,aAAA,OAAAA,aAAA;QACAC,UAAA,OAAAA,UAAA;QACAC,UAAA,OAAAA,UAAA;QACAC,YAAA,OAAAA,YAAA;QACAC,WAAA,OAAAA;MACA;IACA;IAEA,WACA8B,aAAA,WAAAA,cAAA;MACA,KAAArB,aAAA;;MAEA;MACA,UAAAd,iBAAA;QACA,KAAAoC,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,SAAArC,iBAAA,wBAAAC,aAAA;QACA,KAAAmC,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,SAAArC,iBAAA,uBAAAE,UAAA;QACA,KAAAkC,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,SAAArC,iBAAA,uBAAAG,UAAA;QACA,KAAAiC,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,UAAAjC,YAAA;QACA,KAAAgC,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAC,KAAA,gBAAAjC,WAAA;MACA,KAAAc,WAAA;IACA;EACA;AACA", "ignoreList": []}]}