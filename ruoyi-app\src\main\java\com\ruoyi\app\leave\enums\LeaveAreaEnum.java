package com.ruoyi.app.leave.enums;

import java.util.Arrays;

public enum LeaveAreaEnum {
    BINJIANG(1, "滨江"),
    YANGSHI(2, "杨市");

    private Integer code;
    private String name;

    LeaveAreaEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static LeaveAreaEnum getByCode(Integer code) {
        return Arrays.stream(LeaveAreaEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static LeaveAreaEnum getByName(String name) {
        return Arrays.stream(LeaveAreaEnum.values())
                .filter(e -> e.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
    
}
