package com.ruoyi.app.leave.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LPassThItemTMapper;
import com.ruoyi.app.leave.domain.LPassThItemT;
import com.ruoyi.app.leave.service.ILPassThItemTService;

/**
 * 退货申请物资明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class LPassThItemTServiceImpl implements ILPassThItemTService 
{
    @Autowired
    private LPassThItemTMapper lPassThItemTMapper;

    /**
     * 查询退货申请物资明细
     * 
     * @param id 退货申请物资明细ID
     * @return 退货申请物资明细
     */
    @Override
    public LPassThItemT selectLPassThItemTById(Long id)
    {
        return lPassThItemTMapper.selectLPassThItemTById(id);
    }

    /**
     * 查询退货申请物资明细列表
     * 
     * @param lPassThItemT 退货申请物资明细
     * @return 退货申请物资明细
     */
    @Override
    public List<LPassThItemT> selectLPassThItemTList(LPassThItemT lPassThItemT)
    {
        return lPassThItemTMapper.selectLPassThItemTList(lPassThItemT);
    }

    /**
     * 新增退货申请物资明细
     * 
     * @param lPassThItemT 退货申请物资明细
     * @return 结果
     */
    @Override
    public int insertLPassThItemT(LPassThItemT lPassThItemT)
    {
        return lPassThItemTMapper.insertLPassThItemT(lPassThItemT);
    }

    /**
     * 修改退货申请物资明细
     * 
     * @param lPassThItemT 退货申请物资明细
     * @return 结果
     */
    @Override
    public int updateLPassThItemT(LPassThItemT lPassThItemT)
    {
        return lPassThItemTMapper.updateLPassThItemT(lPassThItemT);
    }

    /**
     * 批量删除退货申请物资明细
     * 
     * @param ids 需要删除的退货申请物资明细ID
     * @return 结果
     */
    @Override
    public int deleteLPassThItemTByIds(Long[] ids)
    {
        return lPassThItemTMapper.deleteLPassThItemTByIds(ids);
    }

    /**
     * 删除退货申请物资明细信息
     * 
     * @param id 退货申请物资明细ID
     * @return 结果
     */
    @Override
    public int deleteLPassThItemTById(Long id)
    {
        return lPassThItemTMapper.deleteLPassThItemTById(id);
    }
}
