package com.ruoyi.app.leave.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 计量Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface LeaveMeasureMapper {
    
    /**
     * 获取下一个序列值
     * 
     * @param paramMap 参数Map，包含name_var、bgtime、result
     */
    @DataSource(DataSourceType.MEASURE)
    public void nextSequence(Map<String, Object> paramMap);
}
