package com.ruoyi.app.leave.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.Key;


/**
 * Created by LiDuo on 2019-04-18.
 */
public class MeasureDesUtil {

    private static String encryptKey = "xctgyxgs";
    private final static byte[] keys = {0x12, 0x34, 0x56, 0x78, (byte) 0x90, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF};

    /**
     * 加密字符串
     *
     * @param encryptString
     * @return String
     */
    public static String encrypt(String encryptString) {
        Cipher cipher;
        try {
            byte[] rgbKey = encryptKey.substring(0, 8).getBytes("UTF-8");
            byte[] rgbIV = keys;
            byte[] inputByteArray = encryptString.getBytes("UTF-8");
            DESKeySpec spec = new DESKeySpec(rgbKey);
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            Key deskey = keyFactory.generateSecret(spec);
            IvParameterSpec ips = new IvParameterSpec(rgbIV);
            cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, deskey, ips);
            return  new String(Base64.encodeBase64(cipher.doFinal(inputByteArray, 0, inputByteArray.length)));
        } catch (Exception e) {
            return encryptString;
        }
    }

    public static String decrypt(String decryptString) {
        Cipher cipher;
        try {
            byte[] rgbKey = encryptKey.getBytes("UTF-8");
            byte[] rgbIV = keys;
            byte[] inputByteArray = Base64.decodeBase64(decryptString.getBytes("UTF-8"));
            DESKeySpec spec = new DESKeySpec(rgbKey);
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            Key deskey = keyFactory.generateSecret(spec);
            IvParameterSpec ips = new IvParameterSpec(rgbIV);

            cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, deskey, ips);
            byte[] pasByte = cipher.doFinal(inputByteArray, 0, inputByteArray.length);
            return new String(pasByte, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
            return decryptString;
        }
    }

    public static void main(String args[]){
            String str = "苏BP36Q1|40000002";
            str = encrypt(str);
            System.out.println("es6eliJA1WlnwEoDwjK3gHFd+Y//sF2m".equals(str));
           str = decrypt("es6eliJA1WlnwEoDwjK3gHFd+Y//sF2m");
        System.out.println(str);
        System.out.println(str.split("\\|")[0]);
         System.out.println(str.split("\\|")[1]);
    }

}
