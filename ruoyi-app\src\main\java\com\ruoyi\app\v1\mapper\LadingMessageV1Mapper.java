package com.ruoyi.app.v1.mapper;

import com.ruoyi.app.v1.domain.DriverLading;
import com.ruoyi.app.v1.domain.LadingList;
import com.ruoyi.app.v1.domain.LadingMessageV1;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface LadingMessageV1Mapper {

    /**
     * 销售员权限列表
     */
    @DataSource(value = DataSourceType.XCC1)
    public List<String> selectOperatorStrvalue(String operatorNo);

    /**
     * 查询提单信息
     *
     * @param ladingMessage 权限列表
     * @return 提单集合
     */
    @DataSource(value = DataSourceType.XCC1)
    public List<LadingMessageV1> selectLadingMessageResult(LadingMessageV1 ladingMessage);

    /**
     * 查询提单详细信息
     *
     * @param billOfLadingNo 提单
     * @return 提单
     */
    public List<Map<String, Object>> selectLadingList(LadingList billOfLadingNo);

    /**
     * 司机预约提货情况
     *
     * @param billOfLadingNo 提单
     * @return 提单
     */
    public List<Map<String, Object>> selectDriverLading(DriverLading billOfLadingNo);

    /**
     * 查询客商列表
     *
     * @param ladingMessage 权限列表
     * @return 客商列表
     */
    public List<Map<String, Object>> selectConsignUserNameResult(LadingMessageV1 ladingMessage);
}
