package com.ruoyi.web.controller.supply;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.supply.domain.SupplyUserInfo;
import com.ruoyi.app.supply.service.ISupplyUserInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 相关方人员Controller
 * 
 * <AUTHOR>
 * @date 2023-3-22
 */
@RestController
@RequestMapping("/web/supply/userinfo")
public class SupplyUserInfoController extends BaseController
{
    @Autowired
    private ISupplyUserInfoService supplyUserInfoService;

    /**
     * 查询相关方人员列表
     */
    @GetMapping("/get/list")
    public TableDataInfo getList(SupplyUserInfo supplyUserInfo)
    {
        startPage();
        List<SupplyUserInfo> list = supplyUserInfoService.selectSupplyUserInfoList(supplyUserInfo);
        return getDataTable(list);
    }

    /**
     * 导出相关方人员列表
     */
    @Log(title = "相关方人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(SupplyUserInfo supplyUserInfo)
    {
        List<SupplyUserInfo> list = supplyUserInfoService.selectSupplyUserInfoList(supplyUserInfo);
        ExcelUtil<SupplyUserInfo> util = new ExcelUtil<SupplyUserInfo>(SupplyUserInfo.class);
        return util.exportExcel(list, "相关方人员数据");
    }

    /**
     * 获取相关方人员详细信息
     */
    @GetMapping(value = "/get/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(supplyUserInfoService.selectSupplyUserInfoById(id));
    }

    /**
     * 新增相关方人员
     */
    @Log(title = "相关方人员", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody SupplyUserInfo supplyUserInfo)
    {
        return toAjax(supplyUserInfoService.insertSupplyUserInfo(supplyUserInfo));
    }

    /**
     * 修改相关方人员
     */
    @Log(title = "相关方人员", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody SupplyUserInfo supplyUserInfo)
    {
        return toAjax(supplyUserInfoService.updateSupplyUserInfo(supplyUserInfo));
    }

    /**
     * 删除相关方人员
     */
    @Log(title = "相关方人员", businessType = BusinessType.DELETE)
	@DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable Integer[] ids)
    {
        return toAjax(supplyUserInfoService.deleteSupplyUserInfoByIds(ids));
    }
} 