package com.ruoyi.app.leave.service;

import java.util.Date;
import java.util.List;
import com.ruoyi.app.leave.domain.StoreinWwjgMeasure;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.domain.LeavePlan;
import com.ruoyi.app.leave.domain.LeaveTaskMaterial;

/**
 * 外委加工入库Service接口
 * 
 * <AUTHOR>
 */

public interface IStoreinWwjgService 
{
    /**
     * 查询外委加工入库
     * 
     * @param id 外委加工入库主键
     * @return 外委加工入库
     */
    public StoreinWwjgMeasure selectStoreinWwjgById(Long id);

    /**
     * 查询外委加工入库列表
     * 
     * @param storeinWwjgMeasure 外委加工入库
     * @return 外委加工入库集合
     */
    public List<StoreinWwjgMeasure> selectStoreinWwjgList(StoreinWwjgMeasure storeinWwjgMeasure);

    /**
     * 新增外委加工入库
     * 
     * @param storeinWwjgMeasure 外委加工入库
     * @return 结果
     */
    public int insertStoreinWwjg(StoreinWwjgMeasure storeinWwjgMeasure);

    /**
     * 修改外委加工入库
     * 
     * @param storeinWwjgMeasure 外委加工入库
     * @return 结果
     */
    public int updateStoreinWwjg(StoreinWwjgMeasure storeinWwjgMeasure);

    /**
     * 批量删除外委加工入库
     * 
     * @param ids 需要删除的外委加工入库主键集合
     * @return 结果
     */
    public int deleteStoreinWwjgByIds(Long[] ids);

    /**
     * 删除外委加工入库信息
     * 
     * @param id 外委加工入库主键
     * @return 结果
     */
    public int deleteStoreinWwjgById(Long id);

    /**
     * 根据匹配ID删除外委加工入库
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    public int deleteStoreinWwjgByMatchid(String matchid);

    /**
     * 处理外委加工入库
     *  @param nowDate 当前时间
     * @param leaveTask 任务信息
     * @param leavePlan 计划信息
     * @param leaveTaskMaterial 物料信息
     * @param directSupplyPlanNo
     */
    void handleExternalProcessingStockIn(Date nowDate, LeaveTask leaveTask, LeavePlan leavePlan, LeaveTaskMaterial leaveTaskMaterial, String directSupplyPlanNo);
} 